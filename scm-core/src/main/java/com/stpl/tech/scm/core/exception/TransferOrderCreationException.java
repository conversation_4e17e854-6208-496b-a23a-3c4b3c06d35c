package com.stpl.tech.scm.core.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by chaayos on 17-05-2017.
 */
@ResponseStatus(value = HttpStatus.OK, reason = "Transfer Order creation failure.")
public class TransferOrderCreationException extends Exception{

    private static final long serialVersionUID = 616315625637405710L;

    private SCMError code;

    public TransferOrderCreationException() {
    }

    public TransferOrderCreationException(String message) {
        super(message);
        this.code = new SCMError("Transfer Order creation failure.", message, 701);
    }


    public TransferOrderCreationException(SCMError code) {
        super(code.getErrorMsg());
        this.code = code;
    }

    public SCMError getCode() {
        return code;
    }

    public TransferOrderCreationException(Throwable cause) {
        super(cause);
    }

    public TransferOrderCreationException(String message, Throwable cause) {
        super(message, cause);
    }

    public TransferOrderCreationException(String message, Throwable cause, boolean enableSuppression,
                                          boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

}

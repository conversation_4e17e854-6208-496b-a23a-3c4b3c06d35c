package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.data.model.SalesPerformaCorrectedInvoiceDetail;
import com.stpl.tech.scm.data.model.SalesPerformaDetailData;
import com.stpl.tech.scm.domain.model.CorrectedSalesInvoiceDetails;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

import java.util.HashMap;
import java.util.Map;

public class CorrectionDetailsTemplate extends AbstractVelocityTemplate {

    private EnvProperties props;
    private SalesPerformaDetailData invoice;
    private SalesPerformaCorrectedInvoiceDetail correctedSalesInvoiceDetails;

    public CorrectionDetailsTemplate(SalesPerformaDetailData salesPerformaDetailData, EnvProperties props , SalesPerformaCorrectedInvoiceDetail correctedSalesInvoiceDetails) {
        this.props = props;
        this.invoice = salesPerformaDetailData;
        this.correctedSalesInvoiceDetails = correctedSalesInvoiceDetails;
    }

    @Override
    public String getTemplatePath() {
        return "templates/CorrectionDetails.html";
    }

    @Override
    public String getFilepath() {

        return props.getBasePath() + "/CorrectionDetails/" + invoice.getInvoiceId() + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> data = new HashMap<>();
        data.put("props",props);
        data.put("invoice",invoice);
        data.put("correctedInvoice",correctedSalesInvoiceDetails);
        return data;
    }

}
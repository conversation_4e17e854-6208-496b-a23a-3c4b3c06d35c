package com.stpl.tech.scm.core.service;

import com.stpl.tech.scm.data.model.ProductionUnitData;
import com.stpl.tech.scm.domain.model.SkuDefinition;

import java.util.List;

public interface MappingCache {
	
	public Integer findProductionLine(int unitId, int skuId);

	public void removeProductionLine(int unitId, int skuId);

	public int findSKUID(int productId);

	public  ProductionUnitData findProductionLine(int productIonUnitId);

	public void removeAllProductionLine();

	public List<SkuDefinition> findAllSkuForProductId(int productId);

	public void removeSkuListForProduct(int productId);

	public void removeAllSkuDefinitionList();

}

package com.stpl.tech.scm.core.service;

import com.stpl.tech.scm.data.enums.SchedulerType;
import java.util.Date;

/**
 * Service interface for Kettle Stock Out management operations
 * Handles stock out percentage calculations and data processing
 */
public interface KettleStockOutService {

    /**
     * Creates stock out percentage data for the current business date
     * Processes morning stock out data and calculates stock out percentages
     * @param unitId
     */
    void createStockOutPercentageData(Integer unitId);

    /**
     * Processes stock out data and creates dump tables
     * Clears and recreates product and unit detail clone tables
     * @param unitId
     */
    void processStockOutDataAndCreateDump(Integer unitId);

    /**
     * Checks if percentage data has already been created for the current business date
     * @return true if data already exists, false otherwise
     * @param stockOutPercentage
     */
    boolean checkWhetherPercentageDataAlreadyCreated(SchedulerType stockOutPercentage);

    /**
     * Creates stock out percentage data for a specific date range
     * Loops through each business date and processes data for each date and unit
     * @param startDate Start date for processing (optional)
     * @param endDate End date for processing (optional)
     * @param unitId Unit ID to process (optional)
     */
    void createStockOutPercentageDataByBusinessDate(Date startDate, Date endDate, Integer unitId);

    /**
     * Creates stock out percentage data for a single specific date
     * Processes data for the given date and unit without date looping
     * @param businessDate The specific business date to process
     * @param unitId Unit ID to process (optional)
     */
    void createStockOutPercentageDataByBusinessDate(Date businessDate, Integer unitId);
}
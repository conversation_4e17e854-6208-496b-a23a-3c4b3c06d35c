/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import static javax.persistence.GenerationType.IDENTITY;


@SuppressWarnings("serial")
@Entity
@Table(name = "GATEPASS_ITEM_ASSET_MAPPING")
public class GatepassItemAssetMappingData implements java.io.Serializable {


    private Integer gatePassItemAssetId;

    private Integer gatePassId;

    private Integer gatePassItemId;

    private Integer assetId;

    private String assetTagValue;

    private String gatePassType;

    private String isReturned;

    private Integer returnGatePassItemId;

    public GatepassItemAssetMappingData() {
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "GATEPASS_ITEM_ASSET_MAPPING_ID", unique = true, nullable = false)
    public Integer getGatePassItemAssetId() {
        return gatePassItemAssetId;
    }

    public void setGatePassItemAssetId(Integer gatePassItemAssetId) {
        this.gatePassItemAssetId = gatePassItemAssetId;
    }

    @Column(name = "GATEPASS_ID", nullable = false)
    public Integer getGatePassId() {
        return gatePassId;
    }

    public void setGatePassId(Integer gatePassId) {
        this.gatePassId = gatePassId;
    }

    @Column(name = "GATEPASS_ITEM_ID", nullable = false)
    public Integer getGatePassItemId() {
        return gatePassItemId;
    }

    public void setGatePassItemId(Integer gatePassItemId) {
        this.gatePassItemId = gatePassItemId;
    }

    @Column(name = "ASSET_ID", nullable = false)
    public Integer getAssetId() {
        return assetId;
    }

    public void setAssetId(Integer assetId) {
        this.assetId = assetId;
    }

    @Column(name = "ASSET_TAG_VALUE", nullable = false)
    public String getAssetTagValue() {
        return assetTagValue;
    }

    public void setAssetTagValue(String assetTagValue) {
        this.assetTagValue = assetTagValue;
    }

    @Column(name = "GATEPASS_TYPE", nullable = false)
    public String getGatePassType() {
        return gatePassType;
    }

    public void setGatePassType(String gatePassType) {
        this.gatePassType = gatePassType;
    }

    @Column(name = "IS_RETURNED", nullable = true)
    public String getIsReturned() {return isReturned;}

    public void setIsReturned(String isReturned) {this.isReturned = isReturned;}

    @Column(name = "RETURN_GATEPASS_ITEM_ID")
    public Integer getReturnGatePassItemId() {
        return returnGatePassItemId;
    }

    public void setReturnGatePassItemId(Integer returnGatePassItemId) {
        this.returnGatePassItemId = returnGatePassItemId;
    }
}

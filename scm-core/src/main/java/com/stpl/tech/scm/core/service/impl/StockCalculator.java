package com.stpl.tech.scm.core.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.model.CostDetailData;
import com.stpl.tech.scm.data.model.DayCloseInventoryDrillDown;
import com.stpl.tech.scm.data.model.DayCloseTxnEventDrillDownData;
import com.stpl.tech.scm.domain.model.DayCloseEventLogType;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 16-06-2017.
 */
@Component
public class StockCalculator{

    @Autowired
    private SCMCache scmCache;

    public List<DayCloseInventoryDrillDown> calculate(Map<Integer, DayCloseInventoryDrillDown> consumptionRecords,
                                                      Map<Integer, CostDetailData> currentPriceMap,
                                                      Set<Integer> skusEntered,
                                                      Map<Integer, List<DayCloseTxnEventDrillDownData>> skuWiseDrillDown, Boolean isPreview) throws InventoryUpdateException, SumoException {


        for(Integer skuId : consumptionRecords.keySet()){
            CostDetailData currentPrice = currentPriceMap.get(skuId);
            SkuDefinition sku = scmCache.getSkuDefinition(skuId);
            List<DayCloseTxnEventDrillDownData> eventList = skuWiseDrillDown.get(skuId);
            DayCloseInventoryDrillDown stock = consumptionRecords.get(skuId);
            stock.setProductId(sku.getLinkedProduct().getId());
            if (isPreview.equals(Boolean.FALSE) && Objects.isNull(currentPrice)) {
                throw new SumoException("PRICE IS MISSING...!", "No Price Found For " + sku.getSkuName() + " [ " + sku.getSkuId() + " ]");
            }
            stock.setSkuPrice(isPreview.equals(Boolean.FALSE) ? currentPrice.getPrice() : getPrice(currentPrice,sku));
            stock.setUnitOfMeasure(sku.getUnitOfMeasure());

            BigDecimal transferCost=BigDecimal.ZERO,receiveCost=BigDecimal.ZERO,
                    wasteCost=BigDecimal.ZERO,consumptionCost=BigDecimal.ZERO, bookingCost=BigDecimal.ZERO, reverseBookingCost=BigDecimal.ZERO, reverseConsumptionCost = BigDecimal.ZERO;


            if(eventList!=null && !eventList.isEmpty()){
                for(DayCloseTxnEventDrillDownData txnDrillDown : eventList){
                    if(txnDrillDown.getEventType()!=null){
                        switch (DayCloseEventLogType.valueOf(txnDrillDown.getEventType())){
                            case BOOKINGS:stock.setBooked(add(stock.getBooked(), txnDrillDown.getSkuQuantity()));
                                bookingCost = add(bookingCost,txnDrillDown.getSkuCost());
                                break;
                            case REVERSE_BOOKING:
                                stock.setReverseBooked(add(stock.getReverseBooked(), txnDrillDown.getSkuQuantity()));
                                reverseBookingCost = add(reverseBookingCost,txnDrillDown.getSkuCost());
                                break;
                            case CONSUMPTION:
                                stock.setConsumed(add(stock.getConsumed(), txnDrillDown.getSkuQuantity()));
                                consumptionCost = add(consumptionCost,txnDrillDown.getSkuCost());
                                break;
                            case REVERSE_CONSUMPTION:
                                stock.setReverseConsumed(add(stock.getReverseConsumed(), txnDrillDown.getSkuQuantity()));
                                reverseConsumptionCost = add(reverseConsumptionCost,txnDrillDown.getSkuCost());
                                break;
                            case RECEIVINGS:
                            case GATEPASS_RETURN:
                                stock.setReceived(add(stock.getReceived(), txnDrillDown.getSkuQuantity()));
                                receiveCost = add(receiveCost,txnDrillDown.getSkuCost());
                                break;
                            case TRANSFERS:
                            case INVOICE:
                            case GATEPASS:
                                stock.setTransferred(add(stock.getTransferred(), txnDrillDown.getSkuQuantity()));
                                transferCost = add(transferCost,txnDrillDown.getSkuCost());
                                break;
                            case WASTAGES:stock.setWasted(add(stock.getWasted(),txnDrillDown.getSkuQuantity()));
                                wasteCost = add(wasteCost,txnDrillDown.getSkuCost());
                                break;
                            default:
                                break;
                        }
                    }
                }
                calculateVariance(stock);
            }else{
                //calculate variance if no transactions have happened
                BigDecimal expected = stock.getOpeningStock();
                BigDecimal actual = skusEntered.contains(stock.getSkuId())
                        ? stock.getActual() : stock.getOpeningStock();
                BigDecimal variance = SCMUtil.subtract(expected,actual);
                stock.setExpected(expected);
                stock.setActual(SCMUtil.convertToBigDecimal(actual));
                stock.setVariance(variance);    
            }
            stock.setVarianceCost(SCMUtil.multiplyWithScale10(stock.getVariance(),stock.getSkuPrice()));
            consumptionRecords.put(skuId,stock);
        }
        return new ArrayList<>(consumptionRecords.values());
    }

    private BigDecimal add(BigDecimal a, BigDecimal b) {
        return SCMUtil.convertToBigDecimal(a).add(SCMUtil.convertToBigDecimal(b));
    }

    public void calculateVariance(DayCloseInventoryDrillDown stock) {
        BigDecimal received = add(add(stock.getOpeningStock(),add(stock.getReceived(),stock.getBooked())), stock.getReverseConsumed());
        BigDecimal consumed = add(add(stock.getTransferred(),add(stock.getWasted(),stock.getConsumed())), stock.getReverseBooked());
        BigDecimal expected = received.subtract(consumed);
        BigDecimal actual = stock.getActual()!=null ? stock.getActual() : expected;
        BigDecimal variance = expected.subtract(actual); //expected - actual is variance
        stock.setExpected(expected);
        stock.setActual(SCMUtil.convertToBigDecimal(actual));
        stock.setVariance(variance);
    }

    private BigDecimal getPrice(CostDetailData currentPrice, SkuDefinition skuDefinition) {
        BigDecimal price;
        if(currentPrice==null){
            price = SCMUtil.convertToBigDecimal(skuDefinition.getUnitPrice());
        }else {
            price = currentPrice.getPrice();
        }
        return price;
    }
}

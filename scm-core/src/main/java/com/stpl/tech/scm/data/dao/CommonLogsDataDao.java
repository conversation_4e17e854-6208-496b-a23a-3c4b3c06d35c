package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.data.model.CommonLogsData;
import com.stpl.tech.scm.domain.model.LogType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CommonLogsDataDao extends JpaRepository<CommonLogsData, Integer> {

    CommonLogsData findTop1ByLogTypeIdAndLogTypeAndFromStateAndToStateOrderByLogIdDesc(Integer logTypeId, LogType logType, String fromState, String toState);


}

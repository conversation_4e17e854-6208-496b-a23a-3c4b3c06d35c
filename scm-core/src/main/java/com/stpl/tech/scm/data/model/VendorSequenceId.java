/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

// Generated 15 Aug, 2015 12:50:05 PM by Hibernate Tools 4.0.0

/**
 * UnitSequenceId generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "VENDOR_SEQUENCE_ID")
public class VendorSequenceId implements java.io.Serializable {

	private Integer sequenceId;
	private int vendorId;
	private String idType;
	private int nextValue;
	private Integer financialYear;

	public VendorSequenceId() {
	}

	public VendorSequenceId(int vendorId, String idType, int nextValue) {
		this.vendorId = vendorId;
		this.idType = idType;
		this.nextValue = nextValue;
	}

	public VendorSequenceId(int vendorId, String idType, int nextValue, Integer financialYear) {
		this.vendorId = vendorId;
		this.idType = idType;
		this.nextValue = nextValue;
		this.financialYear = financialYear;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "SEQUENCE_ID", unique = true, nullable = false)
	public Integer getSequenceId() {
		return this.sequenceId;
	}

	public void setSequenceId(Integer sequenceId) {
		this.sequenceId = sequenceId;
	}

	@Column(name = "VENDOR_ID", nullable = false)
	public int getVendorId() {
		return this.vendorId;
	}

	public void setVendorId(int vendorId) {
		this.vendorId = vendorId;
	}


	/**
	 * @return the idType
	 */
	@Column(name = "ID_TYPE", nullable = false)
	public String getIdType() {
		return idType;
	}

	/**
	 * @param idType the idType to set
	 */
	public void setIdType(String idType) {
		this.idType = idType;
	}

	@Column(name = "NEXT_VALUE", nullable = false)
	public int getNextValue() {
		return this.nextValue;
	}

	public void setNextValue(int nextValue) {
		this.nextValue = nextValue;
	}

	@Column(name = "FINANCIAL_YEAR", nullable = false)
	public Integer getFinancialYear() {
		return financialYear;
	}

	public void setFinancialYear(Integer financialYear) {
		this.financialYear = financialYear;
	}

}

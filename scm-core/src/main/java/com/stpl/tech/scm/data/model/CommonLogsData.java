package com.stpl.tech.scm.data.model;

import com.stpl.tech.scm.domain.model.LogType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "COMMON_LOGS_DATA")
@EntityListeners(AuditingEntityListener.class)
public class CommonLogsData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "LOG_ID",unique = true)
    private Integer logId;

    @Enumerated(EnumType.STRING)
    @Column(name = "LOG_TYPE")
    private LogType logType;

    @Column(name = "LOG_TYPE_ID")
    private Integer logTypeId;

    @Column(name = "FROM_STATE")
    private String fromState;

    @Column(name = "TO_STATE")
    private String toState;

    @Column(name = "LOG_MESSAGE")
    private String logMessage;

    @Column(name = "UPDATED_BY", nullable = false)
    private Integer updatedBy;

    @CreatedDate
    @Column(name = "UPDATED_TIME", nullable = false)
    private Date updatedTime;

}

package com.stpl.tech.scm.core.exception;

import com.stpl.tech.scm.domain.model.ProductStockForUnit;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.List;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 26-10-2016.
 */

@ResponseStatus(value = HttpStatus.NOT_IMPLEMENTED, reason = "Negative Stock Entered")
public class NegativeStockException extends Exception {

    private static final long serialVersionUID = 616315625637405710L;

    private String title;

    private String errorMessage;

    private List<ProductStockForUnit> negatives;

    public NegativeStockException(String title, String message) {
        super(title);
        this.title = title;
        this.errorMessage = message;
    }

    public NegativeStockException(String title, String errorMessage, List<ProductStockForUnit> negatives) {
        this.title = title;
        this.errorMessage = errorMessage;
        this.negatives = negatives;
    }

    public List<ProductStockForUnit> getNegatives() {
        return negatives;
    }

    public void setNegatives(List<ProductStockForUnit> negatives) {
        this.negatives = negatives;
    }

    public String getTitle() {
        return title;
    }

    public String getErrorMessage() {
        return errorMessage;
    }
}
/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Version;

@Entity
@Table(name = "WASTAGE_EVENT")
public class SCMWastageEventData {
	private Integer wastageId;
	private Date businessDate;
	private Date generationTime;
	private String status;
	private String type;
	private int unitId;
	private Integer generatedBy;
	private Integer linkedGRId;
	private String grReason;
	private Integer linkedRefId;
	private String linkedRefIdType;
	private String kettleReason;
	private Integer closureEventId;
	private List<SCMWastageData> items = new ArrayList<>();
	private Integer rowVersion;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "WASTAGE_ID", unique = true, nullable = false)
	public Integer getWastageId() {
		return wastageId;
	}

	public void setWastageId(Integer wastageId) {
		this.wastageId = wastageId;
	}

	@Column(name = "BUSINESS_DATE", nullable = false)
	public Date getBusinessDate() {
		return businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	@Column(name = "GENERATION_TIME", nullable = false)
	public Date getGenerationTime() {
		return generationTime;
	}

	public void setGenerationTime(Date generationTime) {
		this.generationTime = generationTime;
	}

	@Column(name = "STATUS", nullable = false)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}
	
	@Column(name = "EVENT_TYPE", nullable = true)
	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Column(name = "GENERATED_BY", nullable = false)
	public Integer getGeneratedBy() {
		return generatedBy;
	}

	public void setGeneratedBy(Integer generatedBy) {
		this.generatedBy = generatedBy;
	}

	public void setLinkedGRId(Integer linkedGRId) {
		this.linkedGRId = linkedGRId;
	}

	@Column(name = "LINKED_GR_ID", nullable = true)
	public Integer getLinkedGRId() {
		return linkedGRId;
	}

	@Column(name = "GR_REASON", nullable = true)
	public String getGrReason() {
		return grReason;
	}

	public void setGrReason(String grReason) {
		this.grReason = grReason;
	}
	
	@Column(name = "LINKED_REF_ID", nullable = true)
	public Integer getLinkedRefId() {
		return linkedRefId;
	}

	public void setLinkedRefId(Integer linkedRefId) {
		this.linkedRefId = linkedRefId;
	}

	@Column(name = "LINKED_REF_ID_TYPE", nullable = true)
	public String getLinkedRefIdType() {
		return linkedRefIdType;
	}

	public void setLinkedRefIdType(String linkedRefIdType) {
		this.linkedRefIdType = linkedRefIdType;
	}
	
	@Column(name = "KETTLE_REASON", nullable = true)
	public String getKettleReason() {
		return kettleReason;
	}

	public void setKettleReason(String kettleReason) {
		this.kettleReason = kettleReason;
	}

	@Column(name = "CLOSURE_EVENT_ID", nullable = true)
	public Integer getClosureEventId() {
		return closureEventId;
	}
	
	

	public void setClosureEventId(Integer closureEventId) {
		this.closureEventId = closureEventId;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "wastage")
	public List<SCMWastageData> getItems() {
		return items;
	}

	public void setItems(List<SCMWastageData> items) {
		this.items = items;
	}

	@Version
	@Column(name="ROW_VERSION")
	public Integer getRowVersion() {
		return rowVersion;
	}

	public void setRowVersion(Integer rowVersion) {
		this.rowVersion = rowVersion;
	}
}

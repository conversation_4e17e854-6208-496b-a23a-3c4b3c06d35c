package com.stpl.tech.scm.core.util.model;

import java.util.List;

import com.stpl.tech.scm.domain.model.POCreationType;
import com.stpl.tech.scm.domain.model.PurchaseOrderItem;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 17-05-2017.
 */
public class PurchaseOrderCreateVO {

    private int userId;
    private int deliveryUnitId;
    private int dispatchId;
    private int vendorId;
    private String comment;
    private String fulfilmentDate;
    private POCreationType creationType;
    private List<PurchaseOrderItem> items;
    private String orderType;
    private Boolean stopNotify;
    private int leadTime;
    private String type;


    public int getDeliveryUnitId() {
        return deliveryUnitId;
    }

    public void setDeliveryUnitId(int deliveryUnitId) {
        this.deliveryUnitId = deliveryUnitId;
    }

    public int getDispatchId() {
        return dispatchId;
    }

    public void setDispatchId(int dispatchId) {
        this.dispatchId = dispatchId;
    }

    public int getVendorId() {
        return vendorId;
    }

    public void setVendorId(int vendorId) {
        this.vendorId = vendorId;
    }

    public List<PurchaseOrderItem> getItems() {
        return items;
    }

    public void setItems(List<PurchaseOrderItem> items) {
        this.items = items;
    }

    public String getFulfilmentDate() {
        return fulfilmentDate;
    }

    public void setFulfilmentDate(String fulfilmentDate) {
        this.fulfilmentDate = fulfilmentDate;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public POCreationType getCreationType() {
        return creationType;
    }

    public void setCreationType(POCreationType creationType) {
        this.creationType = creationType;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

	public Boolean getStopNotify() {
		return stopNotify;
	}

	public void setStopNotify(Boolean stopNotify) {
		this.stopNotify = stopNotify;
	}

    public int getLeadTime() {
        return leadTime;
    }

    public void setLeadTime(int leadTime) {
        this.leadTime = leadTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}

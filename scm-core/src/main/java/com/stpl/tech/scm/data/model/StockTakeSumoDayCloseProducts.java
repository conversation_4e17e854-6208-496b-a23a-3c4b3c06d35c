package com.stpl.tech.scm.data.model;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "STOCK_TAKE_SUMO_DAY_CLOSE_PRODUCTS")
public class StockTakeSumoDayCloseProducts {

    private Integer sumoDayCloseProductItemId;

    private  StockTakeSumoDayCloseEvent stockTakeSumoDayCloseEventId;

    private Integer productId;

    private Date updatedTime;

    private List<DayCloseProductPackagingMappings> dayCloseProductPackagingMappings = new ArrayList<>(0);

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "DAY_CLOSE_PRODUCT_ITEM_ID", unique = true, nullable = false)
    public Integer getSumoDayCloseProductItemId() {
        return sumoDayCloseProductItemId;
    }

    public void setSumoDayCloseProductItemId(Integer sumoDayCloseProductItemId) {
        this.sumoDayCloseProductItemId = sumoDayCloseProductItemId;
    }

    @ManyToOne(fetch = FetchType.LAZY, targetEntity = StockTakeSumoDayCloseEvent.class)
    @JoinColumn(name = "STOCK_TAKE_SUMO_DAY_CLOSE_EVENT_ID", nullable = false)
    public StockTakeSumoDayCloseEvent getStockTakeSumoDayCloseEventId() {
        return stockTakeSumoDayCloseEventId;
    }

    public void setStockTakeSumoDayCloseEventId(StockTakeSumoDayCloseEvent stockTakeSumoDayCloseEventId) {
        this.stockTakeSumoDayCloseEventId = stockTakeSumoDayCloseEventId;
    }

    @Column(name = "PRODUCT_ID")
    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    @Column(name = "UPDATED_TIME")
    public Date getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "sumoDayCloseProductItemId")
    public List<DayCloseProductPackagingMappings> getDayCloseProductPackagingMappings() {
        return dayCloseProductPackagingMappings;
    }

    public void setDayCloseProductPackagingMappings(List<DayCloseProductPackagingMappings> dayCloseProductPackagingMappings) {
        this.dayCloseProductPackagingMappings = dayCloseProductPackagingMappings;
    }
}

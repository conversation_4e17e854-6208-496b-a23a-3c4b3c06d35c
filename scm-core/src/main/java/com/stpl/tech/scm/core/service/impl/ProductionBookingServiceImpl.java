package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.TaxDataCache;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.recipe.model.UnitOfMeasure;
import com.stpl.tech.master.tax.model.TaxData;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.GoodsReceiveManagementService;
import com.stpl.tech.scm.core.service.ProductionBookingService;
import com.stpl.tech.scm.core.service.PurchaseOrderManagementService;
import com.stpl.tech.scm.core.service.SCMAssetManagementService;
import com.stpl.tech.scm.core.service.SCMProductPriceManagementService;
import com.stpl.tech.scm.core.service.StockManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.model.BookingDetail;
import com.stpl.tech.scm.core.util.model.CreateVendorGrVO;
import com.stpl.tech.scm.core.util.model.PurchaseOrderCreateVO;
import com.stpl.tech.scm.core.util.model.ReverseBookingDetail;
import com.stpl.tech.scm.core.util.model.UsedPOModel;
import com.stpl.tech.scm.core.util.model.UsedSKUModel;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.PriceManagementDao;
import com.stpl.tech.scm.data.dao.ProductionBookingDao;
import com.stpl.tech.scm.data.mapper.ReverseProductionBookingMapper;
import com.stpl.tech.scm.data.model.BookingConsumptionData;
import com.stpl.tech.scm.data.model.BookingConsumptionItemDrilldown;
import com.stpl.tech.scm.data.model.ItemTaxDetailData;
import com.stpl.tech.scm.data.model.ProductionBookingData;
import com.stpl.tech.scm.data.model.ProductionBookingMappingData;
import com.stpl.tech.scm.data.model.PurchaseOrderData;
import com.stpl.tech.scm.data.model.PurchaseOrderItemData;
import com.stpl.tech.scm.data.model.ReverseBookingConsumptionData;
import com.stpl.tech.scm.data.model.ReverseBookingConsumptionItemDrilldown;
import com.stpl.tech.scm.data.model.ReverseProductionBookingData;
import com.stpl.tech.scm.data.model.ReverseProductionBookingMappingData;
import com.stpl.tech.scm.data.model.TransferOrderItemData;
import com.stpl.tech.scm.data.model.VendorGoodsReceivedData;
import com.stpl.tech.scm.data.model.VendorGoodsReceivedItemData;
import com.stpl.tech.scm.domain.model.AssetDefinition;
import com.stpl.tech.scm.domain.model.AssetStatusType;
import com.stpl.tech.scm.domain.model.Booking;
import com.stpl.tech.scm.domain.model.BookingConsumption;
import com.stpl.tech.scm.domain.model.BookingStatus;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.InventoryItemDrilldown;
import com.stpl.tech.scm.domain.model.InvoiceDocType;
import com.stpl.tech.scm.domain.model.OtherTaxDetail;
import com.stpl.tech.scm.domain.model.POCreationType;
import com.stpl.tech.scm.domain.model.PercentageDetail;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductionBooking;
import com.stpl.tech.scm.domain.model.PurchaseOrderItem;
import com.stpl.tech.scm.domain.model.ReverseBookingConsumption;
import com.stpl.tech.scm.domain.model.ReverseProductionBooking;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.StockEventStatus;
import com.stpl.tech.scm.domain.model.TagType;
import com.stpl.tech.scm.domain.model.TaxCategoryType;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorDispatchLocation;
import com.stpl.tech.scm.domain.model.VendorGRItem;
import com.stpl.tech.scm.domain.model.VendorGrType;
import com.stpl.tech.scm.domain.model.WastageData;
import com.stpl.tech.scm.domain.model.WastageEvent;
import com.stpl.tech.scm.domain.vo.ConsumptionVO;
import com.stpl.tech.scm.domain.vo.InventoryItemVO;
import com.stpl.tech.scm.domain.model.SCMProductItem;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.Objects;

@Service
@Log4j2
public class ProductionBookingServiceImpl implements ProductionBookingService {

	@Autowired
	private ProductionBookingDao bookingDao;

	@Autowired
	private PurchaseOrderManagementService purchaseOrderManagementService;

	@Autowired
	private GoodsReceiveManagementService goodsReceiveManagementService;

	@Autowired
	private SCMAssetManagementService scmAssetManagementService;

	@Autowired
	private TaxDataCache taxDataCache;

	@Autowired
	private PriceManagementDao priceDao;

	@Autowired
	private SCMProductPriceManagementService productPriceManagementService;

	@Autowired
	private SCMCache scmCache;

	@Autowired
	private MasterDataCache masterDataCache;
	@Autowired
	private StockManagementService stockManagementService;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public ProductionBooking calculateConsumption(int unitId, int productId, BigDecimal quantity) throws SumoException, DataNotFoundException {
		return bookingDao.calculateConsumption(unitId, productId, quantity);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean addBooking(ProductionBooking booking) throws InventoryUpdateException, SumoException, DataNotFoundException, DataUpdationException {
		// All prices should be null here
		// prices will be calculated while creating receiving and consumption

		ProductionBooking fixedAssetBookingdetail = booking;
		BookingDetail detail = bookingDao.addBooking(booking);

		// Consumption and receiving calculations here
		booking = detail.getBooking();
		try {

			// calculate consumption
			ConsumptionVO consumption = priceDao.reduceConsumable(booking, false);

			// update booking drill down prices
			saveBookingConsumptionDrillDowns(booking, detail.getList());

			// calculate and set receiving price
			BigDecimal price = BigDecimal.ZERO;

			// set booking price here using consumption
			for (InventoryItemVO item : consumption.getConsumption().values()) {
				price = SCMUtil.add(price, SCMUtil.multiplyWithScale10(item.getQuantity(), (item.getPrice())));
			}
			booking.setUnitPrice(SCMUtil.divideWithScale10(price, booking.getQuantity()));
			fixedAssetBookingdetail.setUnitPrice(booking.getUnitPrice());
			fixedAssetBookingdetail.setBookingId(booking.getBookingId());
			// book receiving
			priceDao.addReceiving(new Booking(booking), false);
			// update prices back to booking
			bookingDao.updateBookingPrice(booking);

            SkuDefinition skuDefinition = scmCache.getSkuDefinition(booking.getSkuId());
            ProductDefinition productDefinition = scmCache.getProductDefinition(booking.getProductId());
            if(productDefinition.getCategoryDefinition().getId() == SCMServiceConstants.CATEGORY_FIXED_ASSETS) {

                Integer purchaseorderId = purchaseOrderManagementService.createPurchaseOrder(createPORequest(fixedAssetBookingdetail, skuDefinition));
                PurchaseOrderData purchaseOrderData = bookingDao.find(PurchaseOrderData.class, purchaseorderId);
                PurchaseOrderData purchaseOrderData1 = bookingDao.update(purchaseOrderData, false);

                Integer vendorGoodsReceivedId = goodsReceiveManagementService.createVendorGR(createGrRequest(purchaseOrderData1, fixedAssetBookingdetail, skuDefinition));
                VendorGoodsReceivedData vendorGoodsReceivedData = bookingDao.find(VendorGoodsReceivedData.class, vendorGoodsReceivedId);
                VendorGoodsReceivedData vendorGoodsReceivedData1 = bookingDao.update(vendorGoodsReceivedData, false);

                List<AssetDefinition> assetDefinitions = scmAssetManagementService.addNewAssets(createAssetDefination(vendorGoodsReceivedData1));
				deleteCostDetailEntryForFixedAsset(booking.getBookingId(),booking.getUnitId());
            }
		} catch (Exception e) {
			log.error("Error", e);
			throw new InventoryUpdateException(e);
		}
		productPriceManagementService.saveRecipeCost(booking);
		return true;
	}



	private Boolean deleteCostDetailEntryForFixedAsset(Integer bookingId , Integer unitId){
		return bookingDao.deleteCostDetailEntryForFixedAsset(bookingId,unitId);
	}



	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean addReverseBooking(ProductionBooking booking) throws InventoryUpdateException, SumoException, DataNotFoundException, DataUpdationException {
		ReverseProductionBooking reverseProductionBooking = ReverseProductionBookingMapper.INSTANCE.toDomain(booking);
		ReverseBookingDetail detail = bookingDao.addReverseBooking(reverseProductionBooking);
		reverseProductionBooking = detail.getBooking();
		try {
			ProductionBooking productionBooking = ReverseProductionBookingMapper.INSTANCE.toDomain(reverseProductionBooking);
			priceDao.checkConsumableData(productionBooking);
			reverseProductionBooking = ReverseProductionBookingMapper.INSTANCE.toDomain(productionBooking);
			ConsumptionVO consumption = priceDao.addReceiving(reverseProductionBooking, false);
			BigDecimal price = BigDecimal.ZERO;
			for (InventoryItemVO item : consumption.getConsumption().values()) {
				price = SCMUtil.add(price, SCMUtil.multiplyWithScale10(item.getQuantity(), (item.getPrice())));
			}
			reverseProductionBooking.setUnitPrice(SCMUtil.divideWithScale10(price, booking.getQuantity()));
			ReverseProductionBooking reverseProduction = reverseProductionBooking;
			ProductionBooking bookingDetail = ReverseProductionBookingMapper.INSTANCE.toDomain(reverseProduction);
			Booking booking1 = new Booking(bookingDetail);
			booking1.setGetItemDrillDown(true);
			priceDao.reduceConsumable(booking1, false);
			saveReverseBookingConsumptionDrillDowns(booking1.getBooking(), detail.getList());
			bookingDao.updateReverseBookingPrice(reverseProductionBooking, booking1.getBooking());
			ReverseProductionBookingData reverseProductionBookingData = bookingDao.find(ReverseProductionBookingData.class, detail.getBooking().getBookingId());
			List<WastageEvent> wastageEvents = stockManagementService.addReverseWastageEvent(booking, reverseProductionBooking, reverseProductionBookingData);
			if (!wastageEvents.isEmpty()) {
				reverseProductionBookingData.setWastageId(wastageEvents.get(0).getWastageId());
				bookingDao.update(reverseProductionBookingData, true);
			}
		} catch (Exception e) {
			log.error("Error", e);
			throw new InventoryUpdateException(e);
		}
		return true;
	}

	private  List<AssetDefinition> createAssetDefination(VendorGoodsReceivedData vendorGr){
		List<AssetDefinition> assetDefinitions = new ArrayList<>();
		VendorDetail vendorDetail = scmCache.getVendorDetail(vendorGr.getGeneratedForVendor());
		VendorGoodsReceivedItemData itemData = vendorGr.getGrItemList().get(0);
		for(Integer i=0 ; i<itemData.getReceivedQty().intValue(); i++){
			AssetDefinition assetDefinition = new AssetDefinition();
			assetDefinition.setAssetName(itemData.getSkuName());
			assetDefinition.setUnitId(vendorGr.getDeliveryUnitId());
			assetDefinition.setUnitType(UnitCategory.WAREHOUSE.value());
			assetDefinition.setAssetStatus(AssetStatusType.INITIATED);
			assetDefinition.setSkuId(itemData.getSkuId());
			assetDefinition.setGrItemId(itemData.getItemId());
			assetDefinition.setGrId(vendorGr.getGoodsReceivedId());
			assetDefinition.setVendorId(vendorGr.getGeneratedForVendor());
			assetDefinition.setVendorName(vendorDetail.getEntityName());
			assetDefinition.setOwnerType(UnitCategory.WAREHOUSE.value());
			assetDefinition.setOwnerId(vendorGr.getDeliveryUnitId());
			assetDefinition.setFirstOwnerType(UnitCategory.WAREHOUSE.value());
			assetDefinition.setFirstOwnerId(vendorGr.getDeliveryUnitId());
			assetDefinition.setTagType(TagType.QR_CODE);
			assetDefinition.setTagValue(null);
			assetDefinition.setTagPrintCount(0);
			assetDefinition.setLastTagPrintDate(null);
			assetDefinition.setLastTagPrintedBy(null);
			assetDefinition.setPrice(itemData.getUnitPrice().floatValue());
			assetDefinition.setTax(SCMUtil.divideWithScale10(itemData.getTotalTax(),itemData.getReceivedQty()).floatValue());
			assetDefinition.setTaxPercentage((SCMUtil.divideWithScale10(SCMUtil.multiplyWithScale10(itemData.getTotalTax(),new BigDecimal(100)), itemData.getTotalPrice())).floatValue());
			assetDefinition.setQuantity(1);
			IdCodeName idCodeName =  SCMUtil.getSystemUser();
			assetDefinition.setCreatedBy(idCodeName);
			assetDefinitions.add(assetDefinition);
			}
			return assetDefinitions;
	}
	private PurchaseOrderCreateVO createPORequest(ProductionBooking productionBooking, SkuDefinition skuDefinition) throws SumoException {
		VendorDetail vendorDetail = scmCache.getVendorDetail(productionBooking.getVendorId());
		ProductDefinition productDefinition = scmCache.getProductDefinition(productionBooking.getProductId());
        Optional<VendorDispatchLocation> dispatchLocation = SCMUtil.getDispatchLocation(vendorDetail, productionBooking.getDispatchId());
        Unit unit = masterDataCache.getUnit(productionBooking.getUnitId());
		TaxData bookingTax = taxDataCache.getTaxData(unit.getLocation().getState().getId(), productDefinition.getTaxCode());

		PurchaseOrderCreateVO po = new PurchaseOrderCreateVO();
		po.setComment("AUTO_GENERATED FOR PO");
		po.setCreationType(POCreationType.SYSTEM);
		po.setDeliveryUnitId(productionBooking.getUnitId());
		po.setDispatchId(productionBooking.getDispatchId());
		Date date = SCMUtil.getCurrentBusinessDate();
		po.setFulfilmentDate(AppUtils.getSQLFormattedDate(date));
		po.setOrderType(VendorGrType.FIXED_ASSET_ORDER.name());

		List<PurchaseOrderItem> purchaseOrderItems = new ArrayList<>();
		PurchaseOrderItem purchaseOrderItem = new PurchaseOrderItem();
		purchaseOrderItem.setSkuId(skuDefinition.getSkuId());
		purchaseOrderItem.setSkuName(skuDefinition.getSkuName());
		purchaseOrderItem.setRequestedQuantity(productionBooking.getQuantity());
		purchaseOrderItem.setUnitOfMeasure(productionBooking.getUnitOfMeasure());
		purchaseOrderItem.setPackagingId(scmCache.getLoosePackagingDefinition(UnitOfMeasure.valueOf(productionBooking.getUnitOfMeasure())).getPackagingId());
		purchaseOrderItem.setConversionRatio(new BigDecimal(1));
		purchaseOrderItem.setPackagingName(scmCache.getLoosePackagingDefinition(UnitOfMeasure.valueOf(productionBooking.getUnitOfMeasure())).getPackagingName());

		purchaseOrderItem.setUnitPrice(productionBooking.getUnitPrice());
		purchaseOrderItem.setHsn(productDefinition.getTaxCode());
		purchaseOrderItem.setTotalCost(SCMUtil.multiplyWithScale10(productionBooking.getQuantity(),productionBooking.getUnitPrice()));
		purchaseOrderItem.setPackagingQty(SCMUtil.multiply(productionBooking.getQuantity(), purchaseOrderItem.getConversionRatio()));

        purchaseOrderItem.setIgst(null);
        purchaseOrderItem.setCgst(null);
        purchaseOrderItem.setSgst(null);
        purchaseOrderItem.setOtherTaxes(null);
		if(!unit.getLocation().getState().getCode().equals(dispatchLocation.get().getAddress().getStateCode())){
            PercentageDetail igstPercentageDetail = new PercentageDetail();
            igstPercentageDetail.setValue((SCMUtil.multiplyWithScale10(SCMUtil.divideWithScale10(purchaseOrderItem.getTotalCost(),new BigDecimal(100)), bookingTax.getState().getIgst())));
            igstPercentageDetail.setPercentage(bookingTax.getState().getIgst());
            purchaseOrderItem.setIgst(igstPercentageDetail);
        }else {
            PercentageDetail cgstPercentageDetail = new PercentageDetail();
            cgstPercentageDetail.setValue((SCMUtil.multiplyWithScale10(SCMUtil.divideWithScale10(purchaseOrderItem.getTotalCost(),new BigDecimal(100)), bookingTax.getState().getCgst())));
            cgstPercentageDetail.setPercentage(bookingTax.getState().getCgst());
            purchaseOrderItem.setCgst(cgstPercentageDetail);

            PercentageDetail sgstPercentageDetail = new PercentageDetail();
            sgstPercentageDetail.setValue((SCMUtil.multiplyWithScale10(SCMUtil.divideWithScale10(purchaseOrderItem.getTotalCost(),new BigDecimal(100)), bookingTax.getState().getSgst())));
            sgstPercentageDetail.setPercentage(bookingTax.getState().getSgst());
            purchaseOrderItem.setSgst(cgstPercentageDetail);

        }
//        if(bookingTax.getTaxData().getOthers() != null && !bookingTax.getTaxData().getOthers().isEmpty()){
//            List<OtherTaxDetail> otherTaxDetails = new ArrayList<>();
//		    for(AdditionalTax additionalTax : bookingTax.getTaxData().getOthers()){
//		        OtherTaxDetail otherTaxDetail = new OtherTaxDetail();
//                if(additionalTax.getApplicability() == TaxApplicability.ON_TAX){
//                    otherTaxDetail.setValue(SCMUtil.add(SCMUtil.add(purchaseOrderItem.getIgst().getValue(), purchaseOrderItem.getSgst().getValue()), purchaseOrderItem.getCgst().getValue()));
//                }else{
//                    otherTaxDetail.setValue((SCMUtil.multiplyWithScale10(SCMUtil.divideWithScale10(purchaseOrderItem.getTotalCost(),new BigDecimal(100)), additionalTax.getTax())));
//                }
//                otherTaxDetail.setPercentage(additionalTax.getTax());
//                otherTaxDetail.setTaxName(additionalTax.getType());
//                otherTaxDetail.setTaxCategory(additionalTax.getType());
//                otherTaxDetails.add(otherTaxDetail);
//            }
//            purchaseOrderItem.setOtherTaxes(otherTaxDetails);
//        }
		purchaseOrderItems.add(purchaseOrderItem);
		po.setItems(purchaseOrderItems);
		po.setVendorId(vendorDetail.getVendorId());
		po.setUserId(AppConstants.SYSTEM_EMPLOYEE_ID);
		po.setLeadTime(vendorDetail.getLeadTime());
		return po;
	}

	private CreateVendorGrVO createGrRequest(PurchaseOrderData purchaseOrderData, ProductionBooking productionBooking, SkuDefinition skuDefinition) {
		CreateVendorGrVO grVO = new CreateVendorGrVO();
		grVO.setAmountMatched(true);
		grVO.setCreationType(POCreationType.SYSTEM);
		grVO.setDeliveryUnitId(purchaseOrderData.getDeliveryLocationId());
		grVO.setDispatchId(purchaseOrderData.getDispatchLocationId());
		Date date = SCMUtil.getCurrentBusinessDate();
		grVO.setDocDate(AppUtils.getSQLFormattedDate(date));
		String docNumber = "PROD_ID_"+productionBooking.getBookingId();
		grVO.setDocNumber(docNumber);
		grVO.setDocType(InvoiceDocType.SYSTEM_INVOICE);
		grVO.setExtraGrItems(null);
		grVO.setForceSummit(false);

		List<VendorGRItem> vendorGRItems = new ArrayList<>();
		VendorGRItem grItem = new VendorGRItem();
		for(PurchaseOrderItemData poItem : purchaseOrderData.getPurchaseOrderItemDatas()){
			if(poItem.getSkuId() == skuDefinition.getSkuId()){
				grItem.setSkuId(poItem.getSkuId());
				grItem.setSkuName(poItem.getSkuName());
				grItem.setHsn(poItem.getHsnCode());
				grItem.setReceivedQuantity(poItem.getRequestedAbsoluteQuantity());
				grItem.setUnitOfMeasure(poItem.getUnitOfMeasure());
				grItem.setUnitPrice(poItem.getUnitPrice());
				grItem.setTotalCost(poItem.getTotalCost());
				grItem.setAmountPaid(SCMUtil.add(poItem.getTotalCost(),poItem.getTotalTax()));
				grItem.setPackagingId(poItem.getPackagingId());
				grItem.setPackagingName(poItem.getPackagingName());
				grItem.setConversionRatio(poItem.getConversionRatio());
				grItem.setPackagingQty(poItem.getPackagingQuantity());
				grItem.setTotalTax(poItem.getTotalTax());

				List<OtherTaxDetail> taxes = new ArrayList<>();
				if(poItem.getIgstPercentage() != null){
                    OtherTaxDetail otherTaxDetail = new OtherTaxDetail();
                    otherTaxDetail.setPercentage(poItem.getIgstPercentage());
                    otherTaxDetail.setValue(poItem.getIgstValue());
                    otherTaxDetail.setTaxCategory(TaxCategoryType.IGST.value());
                    otherTaxDetail.setTaxName(TaxCategoryType.IGST.value());
                    taxes.add(otherTaxDetail);
                }
                if(poItem.getSgstPercentage() != null){
                    OtherTaxDetail otherTaxDetail = new OtherTaxDetail();
                    otherTaxDetail.setPercentage(poItem.getSgstPercentage());
                    otherTaxDetail.setValue(poItem.getSgstValue());
                    otherTaxDetail.setTaxCategory(TaxCategoryType.SGST.value());
                    otherTaxDetail.setTaxName(TaxCategoryType.SGST.value());
                    taxes.add(otherTaxDetail);
                }
                if(poItem.getCgstPercentage() != null){
                    OtherTaxDetail otherTaxDetail = new OtherTaxDetail();
                    otherTaxDetail.setPercentage(poItem.getCgstPercentage());
                    otherTaxDetail.setValue(poItem.getCgstValue());
                    otherTaxDetail.setTaxCategory(TaxCategoryType.CGST.value());
                    otherTaxDetail.setTaxName(TaxCategoryType.CGST.value());
                    taxes.add(otherTaxDetail);
                }
                if(poItem.getOtherTaxesApplied() != null && !poItem.getOtherTaxesApplied().isEmpty()){
                    for(ItemTaxDetailData itemTaxDetailData : poItem.getOtherTaxesApplied()){
                        OtherTaxDetail otherTaxDetail = new OtherTaxDetail();
                        otherTaxDetail = SCMDataConverter.convert(itemTaxDetailData);
                        taxes.add(otherTaxDetail);
                    }
                }
				grItem.setTaxes(taxes);

				Map<Integer , BigDecimal> map = new HashMap<>();
				map.put(poItem.getId(), poItem.getRequestedAbsoluteQuantity());
				grItem.setUsedPOItems(map);
				vendorGRItems.add(grItem);
			}
			grVO.setItems(vendorGRItems);
			List<UsedPOModel> usedPOModelList = new ArrayList<>();
			UsedPOModel usedPOModel = new UsedPOModel();
			usedPOModel.setId(purchaseOrderData.getId());
			List<UsedSKUModel> usedSKUModelList = new ArrayList<>();
			UsedSKUModel usedSKUModel = new UsedSKUModel();
			usedSKUModel.setId(poItem.getSkuId());
			usedSKUModel.setPkg(poItem.getPackagingId());
			usedSKUModel.setQty(productionBooking.getQuantity());
			usedSKUModelList.add(usedSKUModel);
			usedPOModel.setSkus(usedSKUModelList);

			usedPOModelList.add(usedPOModel);
			grVO.setUsedPOList(usedPOModelList);
			grVO.setUserId(AppConstants.SYSTEM_EMPLOYEE_ID);
			grVO.setVendorId(purchaseOrderData.getGeneratedForVendor());
			grVO.setVendorGrType(VendorGrType.FIXED_ASSET_ORDER);
		}
		return grVO;
	}

	private void saveBookingConsumptionDrillDowns(ProductionBooking booking, List<BookingConsumptionData> list) throws SumoException {
		HashMap<Integer, BookingConsumptionData> itemMap = new HashMap<>();
		for (BookingConsumptionData item : list) {
			itemMap.put(item.getId(), item);
		}
		for (BookingConsumption item : booking.getBookingConsumption()) {
			for (InventoryItemDrilldown drilldown : item.getDrillDowns()) {

				BookingConsumptionData consumptionData = itemMap.get(item.getItemKeyId());
				if (consumptionData.getTotalCost() == null) {
					consumptionData.setTotalCost(BigDecimal.ZERO);
				}
				consumptionData.setTotalCost(
						consumptionData.getTotalCost().add(drilldown.getPrice().multiply(drilldown.getQuantity())));
				consumptionData.setUnitPrice(AppUtils.divideWithScale10(consumptionData.getTotalCost(), consumptionData.getCalculatedQuantity()));

				BookingConsumptionItemDrilldown toDrilldown = new BookingConsumptionItemDrilldown();
				toDrilldown.setConsumptionData(consumptionData);
				toDrilldown.setQuantity(drilldown.getQuantity());
				toDrilldown.setPrice(drilldown.getPrice());
				toDrilldown.setAddTime(AppUtils.getCurrentTimestamp());
				toDrilldown.setExpiryDate(drilldown.getExpiryDate());

				bookingDao.add(toDrilldown, false);
			}
		}
		bookingDao.flush();
	}

	private void saveReverseBookingConsumptionDrillDowns(ProductionBooking booking, List<ReverseBookingConsumptionData> list) throws SumoException {
		HashMap<Integer, ReverseBookingConsumptionData> itemMap = new HashMap<>();
		for (ReverseBookingConsumptionData item : list) {
			itemMap.put(item.getId(), item);
		}
		List<ReverseBookingConsumption> reverseBookingConsumptions = ReverseProductionBookingMapper.INSTANCE.toDomainList(booking.getBookingConsumption());
		for (ReverseBookingConsumption item : reverseBookingConsumptions) {
			for (InventoryItemDrilldown drilldown : item.getDrillDowns()) {
				ReverseBookingConsumptionData consumptionData = itemMap.get(item.getItemKeyId());
				if (consumptionData.getTotalCost() == null) {
					consumptionData.setTotalCost(BigDecimal.ZERO);
				}
				consumptionData.setTotalCost(
						consumptionData.getTotalCost().add(drilldown.getPrice().multiply(drilldown.getQuantity())));
				consumptionData.setUnitPrice(AppUtils.divideWithScale10(consumptionData.getTotalCost(), consumptionData.getCalculatedQuantity()));
				ReverseBookingConsumptionItemDrilldown toDrilldown = new ReverseBookingConsumptionItemDrilldown();
				toDrilldown.setConsumptionData(consumptionData);
				toDrilldown.setQuantity(drilldown.getQuantity());
				toDrilldown.setPrice(drilldown.getPrice());
				toDrilldown.setAddTime(AppUtils.getCurrentTimestamp());
				toDrilldown.setExpiryDate(drilldown.getExpiryDate());

				bookingDao.add(toDrilldown, false);
			}
		}
		bookingDao.flush();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ProductionBooking> getBookings(Integer unitId, Date startDate, Date endDate, boolean isReverse) {
		return bookingDao.getBookings(unitId, startDate, endDate,isReverse);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean cancelBookings(Integer bookingId, Integer empId) throws InventoryUpdateException, DataNotFoundException {
		ProductionBookingData bookingData = bookingDao.cancelBooking(bookingId, empId);
		ProductionBooking booking = SCMDataConverter.convert(bookingData, scmCache, masterDataCache);
		if (booking != null) {
			boolean hasSavedDrilldown = updateBookingConsumptionItemDrillDowns(bookingData, booking);
			priceDao.addReceiving(booking, true);
			Booking booking1 = new Booking(booking);
			booking1.setGetItemDrillDown(true);
			Booking b = priceDao.reduceConsumable(booking1, true);
			priceDao.updateLatestFlag(b);
			return true;
		}
		return false;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean cancelReverseBookings(Integer bookingId, Integer empId) throws InventoryUpdateException, DataNotFoundException {
		ReverseProductionBookingData bookingData = bookingDao.cancelReverseBooking(bookingId, empId);
		ReverseProductionBooking booking = SCMDataConverter.convert(bookingData, scmCache, masterDataCache);
		if (booking != null) {
			boolean hasSavedDrilldown = updateReverseBookingConsumptionItemDrillDowns(bookingData, booking);
			ProductionBooking booking1 = ReverseProductionBookingMapper.INSTANCE.toDomain(booking);
			priceDao.reduceConsumable(booking1, true);
			Booking booking2 = new Booking(ReverseProductionBookingMapper.INSTANCE.toDomain(booking));
			Booking b = priceDao.addReceiving(booking2, true);
			priceDao.updateLatestFlag(b);
			if (Objects.nonNull(bookingData.getWastageId())) {
				stockManagementService.cancelWastageEvent(bookingData.getWastageId());
			}
			return true;
		}
		return false;
	}


	private  ProductionBooking updateQuantities(ProductionBooking productionBooking , BigDecimal newQuantity){
		BigDecimal oldBookingQty = productionBooking.getQuantity();
		//updates production booking entry
		productionBooking.setQuantity(newQuantity);
		productionBooking.setTotalCost(productionBooking.getQuantity().multiply(productionBooking.getUnitPrice()));

		//updation of booking consumption and booking consumption item drilldown entry
		for(BookingConsumption bookingConsumption : productionBooking.getBookingConsumption()){
			BigDecimal singleQuantity =	SCMUtil.divideWithScale10(bookingConsumption.getCalculatedQuantity(),oldBookingQty);
			bookingConsumption.setCalculatedQuantity(SCMUtil.multiplyWithScale10(singleQuantity,newQuantity));
			bookingConsumption.setTotalCost(bookingConsumption.getCalculatedQuantity().multiply(bookingConsumption.getUnitPrice()));

			for(InventoryItemDrilldown itemDrilldown : bookingConsumption.getDrillDowns()){
				singleQuantity = SCMUtil.divideWithScale10(itemDrilldown.getQuantity(),oldBookingQty);
				itemDrilldown.setQuantity(SCMUtil.multiplyWithScale10(singleQuantity,newQuantity));
			}

		}
		return productionBooking;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updateBookings(ProductionBookingData productionBooking , TransferOrderItemData transferOrderItem, Integer empId  , SCMCache scmCache) throws InventoryUpdateException, DataNotFoundException, SumoException {
		if (BookingStatus.CANCELLED.name().equals(productionBooking.getBookingStatus())){
			throw new SumoException("Booking is already in Cancelled State");
		}
		BigDecimal oldBookingQty = productionBooking.getQuantity();
		BigDecimal cancelledBookingQty = transferOrderItem.getTransferredQuantity();
		BigDecimal newBookingQty = oldBookingQty.subtract(cancelledBookingQty);
		//updates production booking entry
		productionBooking.setQuantity(newBookingQty);
		productionBooking.setTotalCost(newBookingQty.multiply(productionBooking.getUnitPrice()));
		bookingDao.update(productionBooking,true);

		//updation of booking consumption and booking consumption item drilldown entry
		List<BookingConsumptionData> bookingConsumptionDataList = new ArrayList<>();
		List<BookingConsumptionItemDrilldown> bookingConsumptionItemDrilldownList = new ArrayList<>();
        for(BookingConsumptionData bookingConsumptionData : productionBooking.getConsumption()){
			BigDecimal singleQuantity =	SCMUtil.divideWithScale10(bookingConsumptionData.getCalculatedQuantity(),oldBookingQty);
			bookingConsumptionData.setCalculatedQuantity(SCMUtil.multiplyWithScale10(singleQuantity,newBookingQty));
			bookingConsumptionData.setTotalCost(bookingConsumptionData.getCalculatedQuantity().multiply(bookingConsumptionData.getUnitPrice()));
			for(BookingConsumptionItemDrilldown bookingConsumptionItemDrilldown : bookingConsumptionData.getConsumption()){
				singleQuantity = SCMUtil.divideWithScale10(bookingConsumptionItemDrilldown.getQuantity(),oldBookingQty);
				bookingConsumptionItemDrilldown.setQuantity(SCMUtil.multiplyWithScale10(singleQuantity,newBookingQty));
				bookingConsumptionItemDrilldownList.add(bookingConsumptionItemDrilldown);
			}
			bookingConsumptionDataList.add(bookingConsumptionData);
		}
		bookingDao.update(bookingConsumptionDataList,true);
		bookingDao.update(bookingConsumptionItemDrilldownList,true);

		ProductionBooking booking = SCMDataConverter.convert(productionBooking, scmCache, masterDataCache);
		if (booking != null) {
			boolean hasSavedDrilldown = updateBookingConsumptionItemDrillDowns(productionBooking, booking);
			booking = updateQuantities(booking,cancelledBookingQty);
			priceDao.addReceiving(booking, true);
			Booking b = priceDao.reduceConsumable(new Booking(booking), true);
			priceDao.updateLatestFlag(b);
			return true;
		}
		return false;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public ProductionBooking getLastBooking(Integer unitId, boolean isReverse) throws InventoryUpdateException, DataNotFoundException {
		ProductionBooking bookingData = bookingDao.getLastBooking(unitId,isReverse);
		if(bookingData != null) {
			return bookingData;
		}else {
			log.error("cannot find booking");
			throw new DataNotFoundException("Cannot find booking!");
		}
	}

	private boolean updateBookingConsumptionItemDrillDowns(ProductionBookingData bookingData,
			ProductionBooking booking) {
		log.info("updateBookingConsumptionItemDrillDowns : booking id : " + bookingData.getBookingId());
		HashMap<Integer, BookingConsumption> itemMap = new HashMap<>();
		for (BookingConsumption item : booking.getBookingConsumption()) {
			itemMap.put(item.getId(), item);
		}
		for (BookingConsumptionData consumptionData : bookingData.getConsumption()) {
			List<InventoryItemDrilldown> ddList = new ArrayList<>();
			for (BookingConsumptionItemDrilldown dd : consumptionData.getConsumption()) {
				ddList.add(SCMDataConverter.convert(dd));
			}
			if (ddList.size() == 0) {
				return false;
			}
			itemMap.get(consumptionData.getId()).getDrillDowns().addAll(ddList);
		}
		return true;
	}
	private boolean updateReverseBookingConsumptionItemDrillDowns(ReverseProductionBookingData bookingData,
																  ReverseProductionBooking booking) {
		log.info("updateBookingConsumptionItemDrillDowns : booking id : " + bookingData.getBookingId());
		HashMap<Integer, ReverseBookingConsumption> itemMap = new HashMap<>();
		for (ReverseBookingConsumption item : booking.getBookingConsumption()) {
			itemMap.put(item.getId(), item);
		}
		for (ReverseBookingConsumptionData consumptionData : bookingData.getConsumption()) {
			List<InventoryItemDrilldown> ddList = new ArrayList<>();
			for (ReverseBookingConsumptionItemDrilldown dd : consumptionData.getConsumption()) {
				ddList.add(SCMDataConverter.convert(dd));
			}
			if (ddList.isEmpty()) {
				return false;
			}
			itemMap.get(consumptionData.getId()).getDrillDowns().addAll(ddList);
		}
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updateMapping(ProductionBooking booking, Boolean updateMapping) throws SumoException {
		List<BookingConsumption> bookingConsumption = booking.getBookingConsumption();
		if (booking.getBookingConsumption() != null) {
			//case1 -- update
			List<ProductionBookingMappingData> bookingMappingData=bookingDao.findMapping(booking.getProductId(), booking.getUnitId());
			if (bookingMappingData.size()>0) {
				//update to inactive
				if(!bookingDao.inactiveMapping(booking.getProductId(), booking.getUnitId())){
					return false;
				}else {
					bookingMappingData=bookingDao.findMapping(booking.getProductId(), booking.getUnitId());
				}
				//find mapping
				log.info("updating the present mapping with product_ID{}",booking.getProductId());
				return productionMappingUpdate(bookingMappingData, bookingConsumption,booking);
			} else {
				//case2 -- add only unique
				Set<Integer> uniqueSkuId= new HashSet<>();
				List<ProductionBookingMappingData> allProductionBooking=new ArrayList<>();
				if(addMappingForAllLevels(bookingConsumption, booking,allProductionBooking,uniqueSkuId)) {
					log.info("ADD all distinct SKU_Id Mapping for product_Id{}",booking.getProductId());
					 bookingDao.addAll(allProductionBooking);
					 return true;
				}else {
					log.info("error while adding mapping  product_Id{}",booking.getProductId());
					return false;
				}
			}
		} else {
			log.info("Empty List");
			return false;
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updateReverseMapping(ProductionBooking booking, Boolean updateMapping) throws SumoException {
		ReverseProductionBooking reverseProductionBooking = ReverseProductionBookingMapper.INSTANCE.toDomain(booking);
		List<ReverseBookingConsumption> bookingConsumption = reverseProductionBooking.getBookingConsumption();
		if (booking.getBookingConsumption() != null) {
			//case1 -- update
			List<ReverseProductionBookingMappingData> bookingMappingData = bookingDao.findReverseMapping(booking.getProductId(), booking.getUnitId());
			if (!bookingMappingData.isEmpty()) {
				//update to inactive
				if (!bookingDao.inactiveMapping(booking.getProductId(), booking.getUnitId())) {
					return false;
				} else {
					bookingMappingData = bookingDao.findReverseMapping(booking.getProductId(), booking.getUnitId());
				}
				//find mapping
				log.info("updating the present mapping with product_ID{}", booking.getProductId());
				return productionReverseMappingUpdate(bookingMappingData, bookingConsumption, reverseProductionBooking);
			} else {
				//case2 -- add only unique
				Set<Integer> uniqueSkuId = new HashSet<>();
				List<ReverseProductionBookingMappingData> allProductionBooking = new ArrayList<>();
				if (addReverseMappingForAllLevels(bookingConsumption, reverseProductionBooking, allProductionBooking, uniqueSkuId)) {
					log.info("ADD all distinct SKU_Id Mapping for product_Id{}", booking.getProductId());
					bookingDao.addAll(allProductionBooking);
					return true;
				} else {
					log.info("error while adding mapping  product_Id{}", booking.getProductId());
					return false;
				}
			}
		} else {
			log.info("Empty List");
			return false;
		}
	}

	private boolean productionMappingUpdate(List<ProductionBookingMappingData> bookingMappingData, List<BookingConsumption> bookingConsumption,ProductionBooking booking) throws SumoException {
		for (BookingConsumption data : bookingConsumption) {
			if (data.getSkuId() == 0) { // if from frontend skuId is zero then set from avl sku list
				List<IdCodeName> avlSkuList = data.getAvailableSkuList();
				data.setSkuId(avlSkuList.get(0).getId());
				data.setSkuName(avlSkuList.get(0).getName());
			}
			ProductionBookingMappingData productMapping= findBookingData(bookingMappingData,data,booking);
			if(productMapping!=null) {
				productMapping.setProfile(booking.getProfile());
				productMapping.setGenerationTime(SCMUtil.getCurrentTimestamp());
				productMapping.setLinkedSkuId(data.getSkuId());
				productMapping.setLinkedSkuName(data.getSkuName());
				productMapping.setLinkedUnitOfMeasure(data.getUnitOfMeasure());
				productMapping.setCalculatedQuantity(data.getCalculatedQuantity());
				productMapping.setMappingStatus(AppConstants.ACTIVE);
				if(data.isAutoProduction() || data.isMappedAutoProduction()) {
					productMapping.setAutoProduction(SCMServiceConstants.SCM_CONSTANT_YES);
				}else {
					productMapping.setAutoProduction(SCMServiceConstants.SCM_CONSTANT_NO);
				}
				bookingDao.update(productMapping, true);
				if (data.isAutoProduction() || data.isMappedAutoProduction()) {
					if (!productionMappingUpdate(bookingMappingData,data.getBookingConsumption(), booking)) {
						return false;
					}
				}
			}else {
				 productMapping = new ProductionBookingMappingData(booking.getProductId(),
					booking.getProductName(), booking.getSkuId(), booking.getUnitOfMeasure(), booking.getQuantity(),
					booking.getUnitId(), booking.getGeneratedBy().getId(), booking.getProfile());
				productMapping.setGenerationTime(SCMUtil.getCurrentTimestamp());
				productMapping.setLinkedSkuId(data.getSkuId());
				productMapping.setLinkedSkuName(data.getSkuName());
				productMapping.setLinkedUnitOfMeasure(data.getUnitOfMeasure());
				productMapping.setCalculatedQuantity(data.getCalculatedQuantity());
				productMapping.setMappingStatus(AppConstants.ACTIVE);
				if(data.isAutoProduction() || data.isMappedAutoProduction()) {
					productMapping.setAutoProduction(SCMServiceConstants.SCM_CONSTANT_YES);
				}else{
					productMapping.setAutoProduction(SCMServiceConstants.SCM_CONSTANT_NO);
				}
				bookingDao.add(productMapping, true);
				if (data.isAutoProduction() || data.isMappedAutoProduction()) {
					if (!productionMappingUpdate(bookingMappingData,data.getBookingConsumption(), booking)) {
						return false;
					}
				}

			}
		}
		return true;
	}

	private boolean productionReverseMappingUpdate(List<ReverseProductionBookingMappingData> bookingMappingData,
												   List<ReverseBookingConsumption> bookingConsumption,
												   ReverseProductionBooking booking) throws SumoException {
		for (ReverseBookingConsumption data : bookingConsumption) {
			if (data.getSkuId() == 0) { // if from frontend skuId is zero then set from avl sku list
				List<IdCodeName> avlSkuList = data.getAvailableSkuList();
				data.setSkuId(avlSkuList.get(0).getId());
				data.setSkuName(avlSkuList.get(0).getName());
			}
			ReverseProductionBookingMappingData productMapping = findReverseBookingData(bookingMappingData, data, booking);
			if (productMapping != null) {
				productMapping.setProfile(booking.getProfile());
				productMapping.setGenerationTime(SCMUtil.getCurrentTimestamp());
				productMapping.setLinkedSkuId(data.getSkuId());
				productMapping.setLinkedSkuName(data.getSkuName());
				productMapping.setLinkedUnitOfMeasure(data.getUnitOfMeasure());
				productMapping.setCalculatedQuantity(data.getCalculatedQuantity());
				productMapping.setMappingStatus(AppConstants.ACTIVE);
				if (data.isAutoProduction() || data.isMappedAutoProduction()) {
					productMapping.setAutoProduction(SCMServiceConstants.SCM_CONSTANT_YES);
				} else {
					productMapping.setAutoProduction(SCMServiceConstants.SCM_CONSTANT_NO);
				}
				bookingDao.update(productMapping, true);
				if (data.isAutoProduction() || data.isMappedAutoProduction()) {
					if (!productionReverseMappingUpdate(bookingMappingData, data.getBookingConsumption(), booking)) {
						return false;
					}
				}
			} else {
				productMapping = new ReverseProductionBookingMappingData(booking.getProductId(),
						booking.getProductName(), booking.getSkuId(), booking.getUnitOfMeasure(), booking.getQuantity(),
						booking.getUnitId(), booking.getGeneratedBy().getId(), booking.getProfile());
				productMapping.setGenerationTime(SCMUtil.getCurrentTimestamp());
				productMapping.setLinkedSkuId(data.getSkuId());
				productMapping.setLinkedSkuName(data.getSkuName());
				productMapping.setLinkedUnitOfMeasure(data.getUnitOfMeasure());
				productMapping.setCalculatedQuantity(data.getCalculatedQuantity());
				productMapping.setMappingStatus(AppConstants.ACTIVE);
				if (data.isAutoProduction() || data.isMappedAutoProduction()) {
					productMapping.setAutoProduction(SCMServiceConstants.SCM_CONSTANT_YES);
				} else {
					productMapping.setAutoProduction(SCMServiceConstants.SCM_CONSTANT_NO);
				}
				bookingDao.add(productMapping, true);
				if (data.isAutoProduction() || data.isMappedAutoProduction()) {
					if (!productionReverseMappingUpdate(bookingMappingData, data.getBookingConsumption(), booking)) {
						return false;
					}
				}

			}
		}
		return true;
	}

	private ProductionBookingMappingData findBookingData(List<ProductionBookingMappingData> bookingMappingData, BookingConsumption bookingConsumption, ProductionBooking booking) {
		for(ProductionBookingMappingData data:bookingMappingData){
			if(data.getProductId().equals(booking.getProductId()) && data.getUnitId().equals(booking.getUnitId())  && data.getLinkedSkuId().equals(bookingConsumption.getSkuId())){
				return data;
			}
		}
		return null;
	}

	private ReverseProductionBookingMappingData findReverseBookingData(List<ReverseProductionBookingMappingData> bookingMappingData,
																ReverseBookingConsumption bookingConsumption,
																ReverseProductionBooking booking) {
		for(ReverseProductionBookingMappingData data:bookingMappingData){
			if(data.getProductId().equals(booking.getProductId()) && data.getUnitId().equals(booking.getUnitId())
					&& data.getLinkedSkuId().equals(bookingConsumption.getSkuId())){
				return data;
			}
		}
		return null;
	}


	private boolean addMappingForAllLevels(List<BookingConsumption> bookingConsumption, ProductionBooking booking,
										   List<ProductionBookingMappingData> allProductionBooking,Set<Integer> uniqueSkuId) throws SumoException {
		for (BookingConsumption data : bookingConsumption) {// if from frontend skuId is zero then set from avl sku list
			if(data.getSkuId()==0){
				List<IdCodeName> avlSkuList= data.getAvailableSkuList();
				data.setSkuId(avlSkuList.get(0).getId());
				data.setSkuName(avlSkuList.get(0).getName());
			}
			ProductionBookingMappingData productMapping = new ProductionBookingMappingData(booking.getProductId(),
				booking.getProductName(), booking.getSkuId(), booking.getUnitOfMeasure(), booking.getQuantity(),
				booking.getUnitId(), booking.getGeneratedBy().getId(), booking.getProfile());
			productMapping.setGenerationTime(SCMUtil.getCurrentTimestamp());
			productMapping.setLinkedSkuId(data.getSkuId());
			productMapping.setLinkedSkuName(data.getSkuName());
			productMapping.setLinkedUnitOfMeasure(data.getUnitOfMeasure());
			productMapping.setCalculatedQuantity(data.getCalculatedQuantity());
			productMapping.setMappingStatus(AppConstants.ACTIVE);
			productMapping.setAutoProduction(SCMUtil.setStatus(data.isAutoProduction()));
			if(!uniqueSkuId.contains(data.getSkuId())) {
				uniqueSkuId.add(data.getSkuId());
				allProductionBooking.add(productMapping);
				//bookingDao.add(productMapping, true);
			}
			if (data.isAutoProduction()) {
				if (!addMappingForAllLevels(data.getBookingConsumption(), booking,allProductionBooking,uniqueSkuId)) {
					return false;
				}
			}
		}
		return true;
	}

	private boolean addReverseMappingForAllLevels(List<ReverseBookingConsumption> bookingConsumption, ReverseProductionBooking booking,
										   List<ReverseProductionBookingMappingData> allProductionBooking,Set<Integer> uniqueSkuId) throws SumoException {
		for (ReverseBookingConsumption data : bookingConsumption) {// if from frontend skuId is zero then set from avl sku list
			if(data.getSkuId()==0){
				List<IdCodeName> avlSkuList= data.getAvailableSkuList();
				data.setSkuId(avlSkuList.get(0).getId());
				data.setSkuName(avlSkuList.get(0).getName());
			}
			ReverseProductionBookingMappingData productMapping = new ReverseProductionBookingMappingData(booking.getProductId(),
				booking.getProductName(), booking.getSkuId(), booking.getUnitOfMeasure(), booking.getQuantity(),
				booking.getUnitId(), booking.getGeneratedBy().getId(), booking.getProfile());
			productMapping.setGenerationTime(SCMUtil.getCurrentTimestamp());
			productMapping.setLinkedSkuId(data.getSkuId());
			productMapping.setLinkedSkuName(data.getSkuName());
			productMapping.setLinkedUnitOfMeasure(data.getUnitOfMeasure());
			productMapping.setCalculatedQuantity(data.getCalculatedQuantity());
			productMapping.setMappingStatus(AppConstants.ACTIVE);
			productMapping.setAutoProduction(SCMUtil.setStatus(data.isAutoProduction()));
			if(!uniqueSkuId.contains(data.getSkuId())) {
				uniqueSkuId.add(data.getSkuId());
				allProductionBooking.add(productMapping);
				//bookingDao.add(productMapping, true);
			}
			if (data.isAutoProduction()) {
				if (!addReverseMappingForAllLevels(data.getBookingConsumption(), booking,allProductionBooking,uniqueSkuId)) {
					return false;
				}
			}
		}
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean inactiveProductFromProductionBooking(Integer productId, String profile) {
		return bookingDao.inactiveProductFromProductionBooking(productId,profile);

	}


	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public ProductionBookingMappingData productionUnitMappingItems(int productId, int unitId, int skuId, String profile) throws SumoException {
		return bookingDao.productionUnitMappingItems(productId, unitId, skuId, profile);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<SCMProductItem> readUploadFile(MultipartFile file)  throws IOException,SumoException {

		try {
			InputStream excelFile = file.getInputStream();
			XSSFWorkbook workbook = new XSSFWorkbook(excelFile);
			XSSFSheet sheet1 = workbook.getSheetAt(0);
			int lastRowNumber = sheet1.getLastRowNum();
			List<SCMProductItem> productList = new ArrayList<>();
			for (int i = 1; i <= lastRowNumber; i++) {
				XSSFRow currentRow = sheet1.getRow(i);
				if (Objects.nonNull(currentRow.getCell(0)) && Objects.nonNull(currentRow.getCell(1))) {
					SCMProductItem product = new SCMProductItem();
					product.setProductId((int) currentRow.getCell(0).getNumericCellValue());
					BigDecimal quantity = BigDecimal.valueOf(currentRow.getCell(1).getNumericCellValue());
					product.setQuantity(quantity);
					productList.add(product);
				}
			}
			return productList;
		} catch (Exception e) {
			throw new SumoException("Error reading excel file :: {}",e.getMessage());
		}
	}


}

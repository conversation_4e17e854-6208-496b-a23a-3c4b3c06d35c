package com.stpl.tech.scm.core.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(value = HttpStatus.FORBIDDEN, reason = "Gatepass Exception")
public class GatepassException extends Exception {

	private static final long serialVersionUID = 7822878299799032213L;

	private SCMError code;

	public GatepassException() {
	}

	public GatepassException(String message) {
		super(message);
		this.code = new SCMError("Error in Gatepass Creation", message, 702);
	}

	public GatepassException(String title, String message) {
		super(message);
		this.code = new SCMError(title, message, 702);
	}

	public GatepassException(SCMError code) {
		super(code.getErrorMsg());
		this.code = code;
	}

	public SCMError getCode() {
		return code;
	}

	public GatepassException(Throwable cause) {
		super(cause);
	}

	public GatepassException(String message, Throwable cause) {
		super(message, cause);
	}

	public GatepassException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
		super(message, cause, enableSuppression, writableStackTrace);
	}

}

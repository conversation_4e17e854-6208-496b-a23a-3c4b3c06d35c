package com.stpl.tech.scm.notification.email;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.notification.email.template.AssetOrderNotificationTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

/**
 * Created by Chaayos on 05-09-2016.
 */
public class AssetOrderNotification extends EmailNotification {

	private AssetOrderNotificationTemplate template;
	private EnvType envType;
	private String sendingUnit;

	public AssetOrderNotification() {

	}

	public AssetOrderNotification(AssetOrderNotificationTemplate template, EnvType envType, String sendingUnit) {
		this.template = template;
		this.envType = envType;
		this.sendingUnit = sendingUnit;
	}

	@Override
	public String[] getToEmails() {

		return SCMUtil.isDev(envType) ? new String[] { "<EMAIL>" } : new String[] { "<EMAIL>" };
	}

	@Override
	public String getFromEmail() {
		return "<EMAIL>";
	}

	@Override
	public String subject() {
		String subject = "Asset Order Created By " + sendingUnit + " on "
				+ SCMUtil.getFormattedTime(SCMUtil.getCurrentDateIST(), "EEE dd MMM yyyy");
		;
		if (SCMUtil.isDev(envType)) {
			subject = "Dev " + subject;
		}
		return subject;
	}

	public String body() throws EmailGenerationException {
		try {
			return template.getContent();
		} catch (TemplateRenderingException e) {
			throw new EmailGenerationException("Failed to render the template", e);
		}
	}

	@Override
	public EnvType getEnvironmentType() {
		return envType;
	}
}

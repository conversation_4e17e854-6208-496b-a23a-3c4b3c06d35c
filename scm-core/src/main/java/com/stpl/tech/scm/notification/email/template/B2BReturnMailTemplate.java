package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.data.model.DocumentDetailData;
import com.stpl.tech.scm.domain.model.SalesPerformaInvoice;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import org.apache.velocity.tools.generic.MathTool;

import java.util.HashMap;
import java.util.Map;

public class B2BReturnMailTemplate extends AbstractVelocityTemplate {

    private EnvProperties props;
    private SalesPerformaInvoice invoice;

    public B2BReturnMailTemplate(SalesPerformaInvoice invoice, EnvProperties props) {
        this.props = props;
        this.invoice = invoice;
    }

    @Override
    public String getTemplatePath() {
        return "templates/B2BReturnMailTemplate.html";
    }

    @Override
    public String getFilepath() {
        return props.getBasePath() + "/B2BReturn/" + invoice.getId() + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> data = new HashMap<>();
        data.put("props",props);
        data.put("invoice",invoice);
        data.put("mathTool", new MathTool());
        return data;
    }
}
package com.stpl.tech.scm.notification.email;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.notification.email.template.VendorEmailNotificationFailureTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

/**
 * Created by Chaayos on 05-09-2016.
 */
public class VendorEmailFailureNotification extends EmailNotification {

	private VendorEmailNotificationFailureTemplate template;
	private EnvType envType;

	public VendorEmailFailureNotification() {

	}

	public VendorEmailFailureNotification(VendorEmailNotificationFailureTemplate template, EnvType envType) {
		this.template = template;
		this.envType = envType;
	}

	@Override
	public String[] getToEmails() {
		String[] mails = { "<EMAIL>" };
		return mails;
	}

	@Override
	public String getFromEmail() {
		return "<EMAIL>";
	}

	@Override
	public String subject() {
		String subject;
		if (template.isReceiving()) {
			subject = "Vendor Receiving Email Failure for " + SCMUtil.getDate(SCMUtil.getPreviousDateIST());
		} else {
			subject = "Vendor Request Email Failure for " + SCMUtil.getDate(SCMUtil.getCurrentDateIST());
		}
		if (SCMUtil.isDev(envType)) {
			subject = "Dev " + subject;
		}
		return subject;
	}

	@Override
	public String body() throws EmailGenerationException {
		try {
			return template.getContent();
		} catch (TemplateRenderingException e) {
			throw new EmailGenerationException("Failed to render the template", e);
		}
	}

	@Override
	public EnvType getEnvironmentType() {
		return envType;
	}
}

package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

@Entity
@Table(name = "REGULAR_ORDERING_EVENTS")
public class RegularOrderingEvent {

    private Integer eventId;
    private SCMDayCloseEventData dayCloseEventData;
    private Integer unitId;
    private String brand;
    private Date fulfilmentDate;
    private Integer orderingDays;
    private String status;
    private Date createdAt;
    private Date updatedAt;
    private ReferenceOrderData referenceOrderData;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "EVENT_ID", nullable = false, unique = true)
    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    @ManyToOne(fetch = FetchType.LAZY, targetEntity = SCMDayCloseEventData.class)
    @JoinColumn(name = "DAY_CLOSE_ID", nullable = false)
    public SCMDayCloseEventData getDayCloseEventData() {
        return dayCloseEventData;
    }

    public void setDayCloseEventData(SCMDayCloseEventData dayCloseEventData) {
        this.dayCloseEventData = dayCloseEventData;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "BRAND", nullable = false)
    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "FULFILMENT_DATE", nullable = true)
    public Date getFulfilmentDate() {
        return fulfilmentDate;
    }

    public void setFulfilmentDate(Date fulfilmentDate) {
        this.fulfilmentDate = fulfilmentDate;
    }

    @Column(name = "ORDERING_DAYS", nullable = false)
    public Integer getOrderingDays() {
        return orderingDays;
    }

    public void setOrderingDays(Integer orderingDays) {
        this.orderingDays = orderingDays;
    }

    @Column(name = "STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATED_AT", nullable = true)
    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_UPDATED_TIME", nullable = true)
    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @OneToOne(fetch = FetchType.LAZY, targetEntity = ReferenceOrderData.class)
    @JoinColumn(name = "REFERENCE_ORDER_ID")
    public ReferenceOrderData getReferenceOrderData() {
        return referenceOrderData;
    }

    public void setReferenceOrderData(ReferenceOrderData referenceOrderData) {
        this.referenceOrderData = referenceOrderData;
    }
}

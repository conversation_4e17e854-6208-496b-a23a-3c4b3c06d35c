/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;
// Generated 4 May, 2016 12:06:56 AM by Hibernate Tools 3.2.2.GA

import com.stpl.tech.scm.core.service.eventListeners.SkuEventListener;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 * SkuPackagingMappingData generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "SKU_PACKAGING_MAPPING", uniqueConstraints = @UniqueConstraint(columnNames = {"PACKAGING_ID",
    "SKU_ID"}))
@EntityListeners(SkuEventListener.class)
public class SkuPackagingMappingData implements java.io.Serializable {

    private Integer skuPackagingMappingId;
    private int packagingId;
    private int skuId;
    private String isDefault = "N";
    private String mappingStatus;

    public SkuPackagingMappingData() {
    }

    public SkuPackagingMappingData(int packagingId, int skuId, String mappingStatus) {
        this.packagingId = packagingId;
        this.skuId = skuId;
        this.mappingStatus = mappingStatus;
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)

    @Column(name = "SKU_PACKAGING_MAPPING_ID", unique = true, nullable = false)
    public Integer getSkuPackagingMappingId() {
        return this.skuPackagingMappingId;
    }

    public void setSkuPackagingMappingId(Integer skuPackagingMappingId) {
        this.skuPackagingMappingId = skuPackagingMappingId;
    }

    @Column(name = "PACKAGING_ID", nullable = false)
    public int getPackagingId() {
        return this.packagingId;
    }

    public void setPackagingId(int packagingId) {
        this.packagingId = packagingId;
    }

    @Column(name = "SKU_ID", nullable = false)
    public int getSkuId() {
        return this.skuId;
    }

    public void setSkuId(int skuId) {
        this.skuId = skuId;
    }

    @Column(name = "MAPPING_STATUS", nullable = false, length = 15)
    public String getMappingStatus() {
        return this.mappingStatus;
    }

    public void setMappingStatus(String mappingStatus) {
        this.mappingStatus = mappingStatus;
    }

    @Column(name = "IS_DEFAULT", nullable = false)
    public String getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(String isDefault) {
        this.isDefault = isDefault;
    }

}

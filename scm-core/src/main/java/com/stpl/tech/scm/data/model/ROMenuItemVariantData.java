package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * Created by <PERSON><PERSON> on 17-06-2016.
 */
@Entity
@Table(name = "RO_MENU_ITEM_VARIANT")
public class ROMenuItemVariantData {

    private Integer id;
    private String name;
    private BigDecimal conversionQuantity;
    private BigDecimal orderedQuantity;
    private ReferenceOrderMenuItemData referenceOrderMenuItemData;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "RO_MENU_VARIANT_ID", nullable = false, unique = true)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "VARIANT_NAME", nullable = false)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Column(name = "VARIANT_CONVERSION_QUANTITY", nullable = false)
    public BigDecimal getConversionQuantity() {
        return conversionQuantity;
    }

    public void setConversionQuantity(BigDecimal conversionQuantity) {
        this.conversionQuantity = conversionQuantity;
    }

    @Column(name = "VARIANT_ORDERED_QUANTITY", nullable = false)
    public BigDecimal getOrderedQuantity() {
        return orderedQuantity;
    }

    public void setOrderedQuantity(BigDecimal orderedQuantity) {
        this.orderedQuantity = orderedQuantity;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "MENU_ITEM_ID", nullable = false)
    public ReferenceOrderMenuItemData getReferenceOrderMenuItemData() {
        return referenceOrderMenuItemData;
    }

    public void setReferenceOrderMenuItemData(ReferenceOrderMenuItemData referenceOrderMenuItemData) {
        this.referenceOrderMenuItemData = referenceOrderMenuItemData;
    }
}

package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * Entity for KETTLE_STOCK_OUT_TIMINGS_DATA table
 */

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "KETTLE_STOCK_OUT_TIMINGS_DATA")
public class KettleStockOutTimingsData  {

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "KETTLE_STOCK_OUT_TIMINGS_DATA_ID", unique = true, nullable = false)
    private Integer kettleStockOutTimingsDataId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "KETTLE_STOCK_OUT_DATE_WISE_DATA_ID")
    private KettleStockOutDateWiseData kettleStockOutDateWiseDataId;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "STOCK_OUT_TIME")
    private Date stockOutTime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "STOCK_IN_TIME")
    private Date stockInTime;

}

package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.scm.data.model.ProductProjectionsDetailsData;
import com.stpl.tech.scm.domain.model.ProductProjectionsUnitDetail;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class ProductProjectionsEmailNotificationTemplate extends AbstractVelocityTemplate {
    private String basePath;
    private EnvType envType;
    private ProductProjectionsDetailsData productProjectionsDetailsData;
    private Set<ProductProjectionsUnitDetail> unitDetails;

    public ProductProjectionsEmailNotificationTemplate() {

    }

    public ProductProjectionsEmailNotificationTemplate(String basePath, EnvType envType, ProductProjectionsDetailsData productProjectionsDetailsData, Set<ProductProjectionsUnitDetail> unitDetails) {
        this.basePath = basePath;
        this.envType = envType;
        this.productProjectionsDetailsData = productProjectionsDetailsData;
        this.unitDetails = unitDetails;
    }

    @Override
    public String getTemplatePath() {
        return "templates/ProductProjectionsEmailTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/" + "scm-projections" + "/" +
                + productProjectionsDetailsData.getId() + ".xlsx";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("productProjectionsDetailsData", this.productProjectionsDetailsData);
        stringObjectMap.put("unitDetails", this.unitDetails);
        return stringObjectMap;
    }

}

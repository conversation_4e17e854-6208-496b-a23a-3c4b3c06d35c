package com.stpl.tech.scm.core.util;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class SCMObjectUtils {

    @SafeVarargs
    public static <T> T[] getArray(T... elements) {
        return elements;
    }

    @SafeVarargs
    public static <T> T[] getNoNullArray(T... elements) {
        return Arrays.stream(elements)
                .filter(Objects::nonNull)
                .toArray(size -> Arrays.copyOf(elements, size));
    }

    @SafeVarargs
    public static <T> List<T> getList(T... elements) {
        return new ArrayList<>(Arrays.asList(elements));
    }

    @SafeVarargs
    public static <T> T firstNonNull(T... elements) {
        for (T element : elements) {
            if (element != null) {
                return element;
            }
        }
        return null;
    }

    public static int getSafeSize(Collection<?> collection) {
        return collection == null ? 0 : collection.size();
    }

    // all elements in A except the ones that are also in B
    public static <T> List<T> subtract(List<T> a, List<T> b) {
        if (a == null) {
            return Collections.emptyList();
        }
        if (b == null) {
            return new ArrayList<>(a);
        }

        Set<T> setB = new HashSet<>(b);
        return a.stream()
                .filter(e -> !setB.contains(e))
                .collect(Collectors.toList());
    }

}

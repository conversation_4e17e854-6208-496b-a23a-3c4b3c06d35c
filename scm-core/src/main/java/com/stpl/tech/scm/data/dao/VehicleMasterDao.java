package com.stpl.tech.scm.data.dao;

import java.util.List;

import com.stpl.tech.scm.data.model.VehicleData;
import com.stpl.tech.scm.domain.model.Vehicle;

public interface VehicleMasterDao extends SCMAbstractDao {

	List<Vehicle> getVehicleData();

	void addVehicleData(VehicleData vehicleData);

	VehicleData getSingleVehicleData(Integer vehicleId);

	void updateVehicleData(VehicleData vehicleData);

}

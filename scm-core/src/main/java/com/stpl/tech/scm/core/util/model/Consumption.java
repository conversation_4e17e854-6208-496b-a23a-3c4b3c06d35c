/**
 * 
 */
package com.stpl.tech.scm.core.util.model;

import com.stpl.tech.scm.data.model.CostDetailData;
import com.stpl.tech.scm.domain.model.StockEventType;
import com.stpl.tech.util.AppConstants;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
public class Consumption {

	private int txnItemId;
	private StockEventType txnType;
	private CostDetailData detail;
	private BigDecimal currentQuantity;
	private boolean variance;
	private boolean consumed;
	private BigDecimal itemQuantity;

	private Map<Integer, List<ConsumptionDrillDown>> drillDowns = new HashMap<>();

	public Consumption(CostDetailData detail) {
		super();
		this.detail = detail;
		this.currentQuantity = new BigDecimal(String.valueOf(detail.getQuantity()));
	}

	public int getTxnItemId() {
		return txnItemId;
	}

	public void setTxnItemId(int txnItemId) {
		this.txnItemId = txnItemId;
	}

	public StockEventType getTxnType() {
		return txnType;
	}

	public void setTxnType(StockEventType txnType) {
		this.txnType = txnType;
	}

	public CostDetailData getDetail() {
		return detail;
	}

	public void setDetail(CostDetailData detail) {
		this.detail = detail;
	}

	public boolean isVariance() {
		return variance;
	}

	public void setVariance(boolean variance) {
		this.variance = variance;
	}

	public boolean isConsumed() {
		return consumed;
	}

	public boolean isLatest() {
		return AppConstants.getValue(detail.getLatest());
	}

	public void setConsumed(boolean consumed) {
		this.consumed = consumed;
	}

	public BigDecimal getCurrentPrice() {
		return detail.getPrice();
	}

	public BigDecimal getCurrentQuantity() {
		return currentQuantity;
	}

	public void setCurrentQuantity(BigDecimal currentQuantity) {
		this.currentQuantity = currentQuantity;
	}

	public Map<Integer, List<ConsumptionDrillDown>> getDrillDowns() {
		return drillDowns;
	}

	public void setItemQuantity(BigDecimal quantity) {
		this.itemQuantity = quantity;
	}

	public BigDecimal getItemQuantity() {
		return itemQuantity;
	}

	@Override
	public String toString() {
		String message = "Consumption [originalQuantity=" + detail.getQuantity() + ", price=" + detail.getPrice()
				+ ", currentQuantity=" + currentQuantity + ", variance=" + variance + ", consumed=" + consumed + "]";
		for (Integer key : getDrillDowns().keySet()) {
			List<ConsumptionDrillDown> data = drillDowns.get(key);
			for (ConsumptionDrillDown drillDown : data) {
				message = message + "\n\t" + drillDown;
			}
		}
		return message;
	}
}

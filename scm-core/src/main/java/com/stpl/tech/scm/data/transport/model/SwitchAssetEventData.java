package com.stpl.tech.scm.data.transport.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SwitchAssetEventData {
 String switchId;
    String newAssetIdAndName;
    String newAssetProcurementCost;
    String newAssetWarranty;
    String newAssetWarrantyLastDate;
    String newAssetInsurance;
    String newAssetInsuranceLastDate;
    String newAssetComment;
    String oldAssetIdAndName;
    String oldAssetProcurementCost;
    String oldAssetWarranty;
    String oldAssetWarrantyLastDate;
    String oldAssetInsurance;
    String oldAssetInsuranceLastDate;
    String oldAssetComment;

    String requestedByUser;
    String createdByUser;
    String fromUnit;
    String toUnit;
    String url;
    String reason;

}

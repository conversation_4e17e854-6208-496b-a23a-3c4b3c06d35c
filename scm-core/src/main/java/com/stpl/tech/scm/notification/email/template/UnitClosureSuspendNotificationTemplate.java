package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.data.model.UnitClosureEvent;
import com.stpl.tech.scm.data.model.VendorContractSoInfo;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
public class UnitClosureSuspendNotificationTemplate extends AbstractVelocityTemplate {

    private UnitClosureEvent unitClosureEvent;
    private String unitName;

    private String basePath;

    @Override
    public String getTemplatePath() {
        return  "templates/UnitClosureSuspendEmailNotification.html";
    }

    @Override
    public String getFilepath() {
        return  basePath + "/unit-closure/UnitClosureSuspend_" + AppUtils.getCurrentTimeISTStringWithNoColons() + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
            stringObjectMap.put("unitClosureEvent",unitClosureEvent);
        stringObjectMap.put("unitName",unitName);
        return stringObjectMap;
    }
}

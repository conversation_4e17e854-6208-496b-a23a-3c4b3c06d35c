package com.stpl.tech.scm.core.cache.service;

import com.stpl.tech.scm.core.cache.base.BaseCacheManager;
import com.stpl.tech.scm.data.redis.service.CacheRefreshService;
import com.stpl.tech.scm.data.redis.service.RedisHashManager;
import com.stpl.tech.scm.data.redis.service.RedisHashMap;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class ProductCacheManager extends BaseCacheManager<Integer, ProductDefinition> {

    public ProductCacheManager(RedisHashManager redisHashManager,
                               CacheRefreshService cacheRefreshService,
                               @Qualifier("taskExecutor") ThreadPoolTaskExecutor taskExecutor) {
        super(redisHashManager, cacheRefreshService, taskExecutor);
    }

    @Override
    protected RedisHashMap<Integer, ProductDefinition> getRedisHash() {
        return redisHashManager.productDefinition();
    }

    @Override
    protected Map<Integer, ProductDefinition> loadFromSource() {
        return cacheRefreshService.reloadProductCache();
    }

    @Override
    protected Integer extractKey(ProductDefinition product) {
        return product.getProductId();
    }
}

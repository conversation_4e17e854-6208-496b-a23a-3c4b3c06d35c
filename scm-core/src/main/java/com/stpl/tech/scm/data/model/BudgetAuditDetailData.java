package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "BUDGET_AUDIT_DETAIL")
public class BudgetAuditDetailData {
	
	private Integer id;
	private Integer capexAuditId;
	private Integer capexBudgetDetailId;
	private String amountType;
	private BigDecimal previousValue;
	private BigDecimal finalValue;
	private String actionType;
	private String action;
	private String keyType;
	private Integer keyValue;
	private String comment;
	private Integer actionBy;
	private Date actionTime;
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ID")
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	
	@Column(name = "CAPEX_AUDIT_ID")
	public Integer getCapexAuditId() {
		return capexAuditId;
	}
	public void setCapexAuditId(Integer capexAuditId) {
		this.capexAuditId = capexAuditId;
	}
	
	@Column(name = "CAPEX_BUDGET_DETAIL_ID")
	public Integer getCapexBudgetDetailId() {
		return capexBudgetDetailId;
	}
	public void setCapexBudgetDetailId(Integer capexBudgetDetailId) {
		this.capexBudgetDetailId = capexBudgetDetailId;
	}
	
	@Column(name = "AMOUNT_TYPE")
	public String getAmountType() {
		return amountType;
	}
	public void setAmountType(String amountType) {
		this.amountType = amountType;
	}
	
	@Column(name = "PREVIOUS_VALUE")
	public BigDecimal getPreviousValue() {
		return previousValue;
	}
	public void setPreviousValue(BigDecimal previousValue) {
		this.previousValue = previousValue;
	}
	
	@Column(name = "FINAL_VALUE")
	public BigDecimal getFinalValue() {
		return finalValue;
	}
	public void setFinalValue(BigDecimal finalValue) {
		this.finalValue = finalValue;
	}
	
	@Column(name = "ACTION_TYPE")
	public String getActionType() {
		return actionType;
	}
	public void setActionType(String actionType) {
		this.actionType = actionType;
	}
	
	@Column(name = "ACTION")
	public String getAction() {
		return action;
	}
	public void setAction(String action) {
		this.action = action;
	}
	
	@Column(name = "KEY_TYPE")
	public String getKeyType() {
		return keyType;
	}
	public void setKeyType(String keyType) {
		this.keyType = keyType;
	}
	
	@Column(name = "KEY_VALUE")
	public Integer getKeyValue() {
		return keyValue;
	}
	public void setKeyValue(Integer keyValue) {
		this.keyValue = keyValue;
	}
	
	@Column(name = "COMMENT")
	public String getComment() {
		return comment;
	}
	public void setComment(String comment) {
		this.comment = comment;
	}
	
	@Column(name = "ACTION_BY")
	public Integer getActionBy() {
		return actionBy;
	}
	public void setActionBy(Integer actionBy) {
		this.actionBy = actionBy;
	}
	
	@Column(name = "ACTION_TIME")
	public Date getActionTime() {
		return actionTime;
	}
	public void setActionTime(Date actionTime) {
		this.actionTime = actionTime;
	}
	
	

}

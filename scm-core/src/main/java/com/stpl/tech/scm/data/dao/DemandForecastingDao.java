/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.data.model.DayWiseSlotWiseSalesData;
import com.stpl.tech.scm.data.model.HolidaysListData;
import com.stpl.tech.scm.data.model.UnitWiseOrderingStrategyData;
import com.stpl.tech.scm.domain.model.UnitMappedMenuMappedProductDto;
import com.stpl.tech.util.EnvType;

import java.util.List;
import java.util.Date;

public interface DemandForecastingDao {

    UnitWiseOrderingStrategyData getUnitWiseOrderingStrategyDataByUnitId(Integer unitId);

    List<DayWiseSlotWiseSalesData> getHistoricalSalesData(Integer unitId, Integer brandId, Date startDate, Date endDate);

    List<HolidaysListData> getHolidayListOfType(Date startDate, Date endDate, List<String> holidayTypes);

    List<UnitMappedMenuMappedProductDto> getUnitMappedMenuMappedProducts(Integer unitId, EnvType envType);

}

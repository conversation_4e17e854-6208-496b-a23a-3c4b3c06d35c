package com.stpl.tech.scm.data.converter;

import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.model.RegionProductPackagingMappingData;
import com.stpl.tech.scm.data.model.SkuDefinitionData;
import com.stpl.tech.scm.data.model.UserSkuCreationRequestData;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.ObjectFactory;
import com.stpl.tech.scm.domain.model.RegionProductPackagingMapping;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.SwitchStatus;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class EntityToDtoConverter {

    private static final ObjectFactory factory = new ObjectFactory();

    public static Map<String, RegionProductPackagingMapping> convertToDto(List<RegionProductPackagingMappingData> regionProductPackagingMappingDataList) {

        Map<String, RegionProductPackagingMapping> resultMap = new HashMap<>();
        for(RegionProductPackagingMappingData data : regionProductPackagingMappingDataList) {
            RegionProductPackagingMapping mapping = new RegionProductPackagingMapping();
            mapping.setId(data.getMappingId());
            mapping.setRegionCode(data.getRegionCode());
            mapping.setProductId(data.getProductId());
            mapping.setPackagingId(data.getPackagingId());
            mapping.setStatus(data.getStatus());
            mapping.setCreatedBy(data.getCreatedBy());

            String KEY = SCMUtil.generateUniqueKey(data.getRegionCode(), data.getProductId().toString());
            resultMap.put(KEY, mapping);
        }

        return resultMap;
    }

    public static SkuDefinition cacheConverter(SkuDefinitionData skuDefinitionData, IdCodeName createdBy, IdCodeName product) {
        SkuDefinition skuDefinition = factory.createSkuDefinition();
        skuDefinition.setCreatedBy(createdBy);
        skuDefinition.setCreationDate(skuDefinitionData.getCreationDate());
        skuDefinition.setHasCase(SCMUtil.getStatus(skuDefinitionData.getHasCase()));
        skuDefinition.setHasInner(SCMUtil.getStatus(skuDefinitionData.getHasInner()));
        skuDefinition.setLinkedProduct(product);
        skuDefinition.setShelfLifeInDays(skuDefinitionData.getShelfLifeInDays());
        skuDefinition.setSkuDescription(skuDefinitionData.getSkuDescription());
        skuDefinition.setSkuId(skuDefinitionData.getSkuId());
        skuDefinition.setSkuName(skuDefinitionData.getSkuName());
        skuDefinition.setSkuStatus(SwitchStatus.fromValue(skuDefinitionData.getSkuStatus()));
        skuDefinition.setSupportsLooseOrdering(SCMUtil.getStatus(skuDefinitionData.getSupportsLooseOrdering()));
        skuDefinition.setUnitOfMeasure(skuDefinitionData.getUnitOfMeasure());
        skuDefinition.setPriceLastUpdated(skuDefinitionData.getPriceLastUpdated());
        skuDefinition.setTorqusSkuName(skuDefinitionData.getTorqusSKU());
        skuDefinition.setIsDefault(SCMUtil.getStatus(skuDefinitionData.getIsDefault()));
        skuDefinition.setInventoryList(skuDefinitionData.getInventoryList());
        skuDefinition.setUnitPrice(SCMUtil.convertToBigDecimal(skuDefinitionData.getUnitPrice()).floatValue());
        skuDefinition.setNegotiatedUnitPrice(
                SCMUtil.convertToBigDecimal(skuDefinitionData.getNegotiatedUnitPrice()).floatValue());
        skuDefinition.setUnitPrice(SCMUtil.convertToBigDecimal(skuDefinitionData.getUnitPrice()).floatValue());
        skuDefinition.setSkuCode(skuDefinitionData.getSkuCode());
        if (skuDefinitionData.getSkuImage() != null) {
            skuDefinition.setSkuImage(skuDefinitionData.getSkuImage());
        }
        skuDefinition.setTaxCode(skuDefinitionData.getTaxCategoryCode());
        if (Objects.nonNull(skuDefinitionData.getVoDisContinuedFrom())) {
            skuDefinition.setVoDisContinuedFrom(skuDefinitionData.getVoDisContinuedFrom());
        }
        if (Objects.nonNull(skuDefinitionData.getRoDisContinuedFrom())) {
            skuDefinition.setRoDisContinuedFrom(skuDefinitionData.getRoDisContinuedFrom());
        }
        if (Objects.nonNull(skuDefinitionData.getIsBranded())) {
            skuDefinition.setIsBranded(SCMUtil.getStatus(skuDefinitionData.getIsBranded()));
        }
        skuDefinition.setBrand(skuDefinitionData.getBrand());
        return skuDefinition;
    }

}

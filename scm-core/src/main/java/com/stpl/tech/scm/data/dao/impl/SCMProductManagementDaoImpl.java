package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.dao.SCMProductManagementDao;
import com.stpl.tech.scm.data.model.DerivedMappingData;
import com.stpl.tech.scm.data.model.PackagingDefinitionData;
import com.stpl.tech.scm.data.model.ProductDefinitionData;
import com.stpl.tech.scm.data.model.ProductFulfillmentTypeData;
import com.stpl.tech.scm.data.model.ProductPackagingMappingData;
import com.stpl.tech.scm.data.model.RegionProductPackagingMappingData;
import com.stpl.tech.scm.data.model.SkuAttributeValueData;
import com.stpl.tech.scm.data.model.SkuDefinitionData;
import com.stpl.tech.scm.data.model.SkuPackagingMappingData;
import com.stpl.tech.scm.data.model.UnitSkuVendorMapping;
import com.stpl.tech.scm.data.model.UserProductCreationRequestData;
import com.stpl.tech.scm.domain.model.FulfillmentType;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.PackagingType;
import com.stpl.tech.scm.domain.model.ProductStatus;
import com.stpl.tech.scm.domain.model.RPPMappingRequest;
import com.stpl.tech.scm.domain.model.RequestedSkuDetails;
import com.stpl.tech.scm.domain.model.SwitchStatus;
import com.stpl.tech.scm.domain.model.UnitProductPackagingMapping;
import com.stpl.tech.scm.domain.model.UserRequestDto;
import com.stpl.tech.util.AppUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by Rahul Singh on 07-05-2016.
 */
@Repository
public class SCMProductManagementDaoImpl extends SCMAbstractDaoImpl implements SCMProductManagementDao {

    @Autowired
    private MasterDataCache masterDataCache;

    private static final Logger LOG = LoggerFactory.getLogger(SCMProductManagementDaoImpl.class);

    @Override
    public List<ProductFulfillmentTypeData> getProductFulfillmentTypes(int productId) {
        Query query = manager
                .createQuery("FROM ProductFulfillmentTypeData p WHERE p.productDefinitionData.productId = :productId");
        query.setParameter("productId", productId);
        return query.getResultList();
    }

    @Override
    public List<ProductPackagingMappingData> getPackagingMappingsForProduct(int productId) {
        Query query = manager.createQuery("FROM ProductPackagingMappingData p WHERE p.productId = :productId");
        query.setParameter("productId", productId);
        return query.getResultList();
    }

    @Override
    public List<SkuDefinitionData> getSkuAgainstProduct(int productId) {
        Query query = manager.createQuery("FROM SkuDefinitionData p WHERE p.linkedProduct.productId = :productId AND p.skuStatus NOT IN (:statusList)" );
        query.setParameter("statusList", List.of(SwitchStatus.INITIATED.name(), SwitchStatus.CANCELLED.name(), SwitchStatus.REJECTED.name()));
        query.setParameter("productId", productId);
        return query.getResultList();
    }

    @Override
    public List<SkuPackagingMappingData> getPackagingMappingsForSku(int skuId) {
        Query query = manager.createQuery("FROM SkuPackagingMappingData p WHERE p.skuId = :skuId");
        query.setParameter("skuId", skuId);
        return query.getResultList();
    }

    @Override
    public SkuAttributeValueData fetchSkuAttributeByType(int skuId, int attributeId) {
        Query query = manager
                .createQuery("FROM SkuAttributeValueData p WHERE p.skuId = :skuId AND p.attributeId = :attributeId");
        query.setParameter("skuId", skuId);
        query.setParameter("attributeId", attributeId);
        List<SkuAttributeValueData> list = query.getResultList();
        if (list != null & !list.isEmpty()) {
            return list.get(0);
        } else {
            return null;
        }
    }

    @Override
    public List<UnitSkuVendorMapping> getUnitSkuVendorMappingsByUnitId(int unitId) {
        Query query = manager
                .createQuery("FROM UnitSkuVendorMapping u WHERE u.unitSkuMapping.unitId = :unitId" +
                        " AND u.unitSkuMapping.mappingStatus = :mappingStatus AND u.mappingStatus = :mappingStatus");
        query.setParameter("unitId", unitId);
        query.setParameter("mappingStatus", SwitchStatus.ACTIVE.value());
        return query.getResultList();
    }

    @Override
    public void updateIsDefaultFlag(Integer skuId, Integer productId) {
        Query query = manager.createQuery("UPDATE SkuDefinitionData E SET E.isDefault = :notDefault" +
                " where E.linkedProduct.productId = :productId and E.skuId != :skuId");
        query.setParameter("productId", productId);
        query.setParameter("notDefault", SCMServiceConstants.SCM_CONSTANT_NO);
        query.setParameter("skuId", skuId);
        query.executeUpdate();
        manager.flush();
    }

    @Override
    public PackagingDefinitionData findByPackagingTypeAndPackagingCode(PackagingType packagingType, String packagingCode) {
        Query query = manager
                .createQuery("FROM PackagingDefinitionData u WHERE u.packagingType = :packagingType" +
                        " AND u.packagingCode = :packagingCode");
        query.setParameter("packagingCode", packagingCode);
        query.setParameter("packagingType", packagingType.name());
        try {
            return (PackagingDefinitionData) query.getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    @Override
    public List<UnitProductPackagingMapping> findPackagingByUnitId(int unitId) {
        List<UnitProductPackagingMapping> unitProductPackagingMappingList = new ArrayList<>();
        Query query = manager
            .createNativeQuery("select pd.PRODUCT_ID, fum.FULFILLING_UNIT_ID, pft.FULFILLMENT_TYPE," +
                "max(usm.PACKAGING_ID)" +
                "from UNIT_SKU_MAPPING usm, SKU_DEFINITION sku, PRODUCT_DEFINITION pd, PRODUCT_FULFILLMENT_TYPE_DATA pft, FULFILLMENT_UNIT_MAPPING fum" +
                " where fum.REQUESTING_UNIT_ID = :unitId AND fum.MAPPING_STATUS = 'ACTIVE'" +
                " and fum.FULFILLMENT_TYPE =pft.FULFILLMENT_TYPE" +
                " and pft.PRODUCT_DEFINITION_ID = pd.PRODUCT_ID and pd.PRODUCT_ID = sku.LINKED_PRODUCT_ID" +
                " and sku.SKU_ID = usm.SKU_ID" +
                " and usm.MAPPING_STATUS = 'ACTIVE'" +
                "and sku.SKU_STATUS = 'ACTIVE'" +
                "and pd.PRODUCT_STATUS = 'ACTIVE'" +
                "and usm.PACKAGING_ID IS NOT NULL" +
                " and pft.PRODUCT_FULFILLMENT_TYPE_STATUS = 'ACTIVE'" +
                "and usm.UNIT_ID = fum.FULFILLING_UNIT_ID" +
                " group by pd.PRODUCT_ID");
        query.setParameter("unitId", unitId);
        try {
            List<Object[]> list = query.getResultList();
            if (list != null) {
                for (Object[] obj : list) {
                    UnitProductPackagingMapping unitProductPackagingMapping = new UnitProductPackagingMapping();
                    unitProductPackagingMapping.setProductId((Integer) obj[0]);
                    unitProductPackagingMapping.setFulfillmentUnitId((Integer) obj[1]);
                    unitProductPackagingMapping.setFulfillmentType(FulfillmentType.fromValue((String) obj[2]));
                    unitProductPackagingMapping.setPackagingId((Integer) obj[3]);
                    unitProductPackagingMappingList.add(unitProductPackagingMapping);
                }
            }
            return unitProductPackagingMappingList;
        }catch (Exception e){
            return null;
        }
    }

    @Override
    public List<Object[]> getUnitSkuPackagingMappings(int unitId) {
        try{
            Query query = manager.createNativeQuery("SELECT pd.PRODUCT_ID, max(usm.PACKAGING_ID) FROM UNIT_SKU_MAPPING usm,SKU_DEFINITION sku," +
                    "PRODUCT_DEFINITION pd " +
                    "WHERE usm.UNIT_ID = :unitId AND usm.MAPPING_STATUS = 'ACTIVE'" +
                    "AND pd.PRODUCT_ID = sku.LINKED_PRODUCT_ID " +
                    "AND sku.SKU_ID = usm.SKU_ID " +
                    "AND sku.SKU_STATUS = 'ACTIVE' " +
                    "AND pd.PRODUCT_STATUS = 'ACTIVE' " +
                    "AND usm.PACKAGING_ID IS NOT NULL" +
                    " GROUP BY pd.PRODUCT_ID");
            query.setParameter("unitId", unitId);
            List<Object[]> list = query.getResultList();
            return list;
        }
        catch (Exception e){
            LOG.error("Exception occurred while fetching data from database :: ",e);
            return null;
        }
    }

    @Override
    public List<UserProductCreationRequestData> findAllProductsInStatus(UserRequestDto productRequest) {
        try{
            if(CollectionUtils.isEmpty(productRequest.getStatus()) && Objects.isNull(productRequest.getStartDate()) && Objects.isNull(productRequest.getEndDate())) {
                return new ArrayList<>();
            }
            StringBuilder qString = new StringBuilder("FROM UserProductCreationRequestData ");
            if(Objects.nonNull(productRequest.getStartDate())) {
                qString.append("WHERE creationDate >=:startDate ");
            }
            if(Objects.nonNull(productRequest.getEndDate())) {
                qString.append("AND creationDate <=:endDate ");
            }
            if(!CollectionUtils.isEmpty(productRequest.getStatus())) {
                qString.append("AND productStatus IN (:status) ");
            }
            qString.append("ORDER BY productId DESC");
            Query query = manager.createQuery(qString.toString());

            if(!CollectionUtils.isEmpty(productRequest.getStatus())) {
                query.setParameter("status", productRequest.getStatus());
            }
            if(Objects.nonNull(productRequest.getStartDate())) {
                query.setParameter("startDate", AppUtils.getDate(productRequest.getStartDate(),AppUtils.DATE_FORMAT_STRING));
            }
            if(Objects.nonNull(productRequest.getEndDate())) {
                query.setParameter("endDate", AppUtils.getEndOfDay(AppUtils.getDate(productRequest.getEndDate(),AppUtils.DATE_FORMAT_STRING)));
            }
            return query.getResultList();
        } catch (Exception exp) {
            LOG.error("Exception in findAllProductsInStatus ", exp);
        }
        return new ArrayList<>();
    }

    @Override
    public void isDuplicateProduct(String productName, Integer productId) throws SumoException {
        try {
            // Check for Duplicate entry of product name in PRODUCT_DEFINITION.
            StringBuilder pddString = new StringBuilder("FROM ProductDefinitionData WHERE UPPER(productName) = :productName");
            if(Objects.nonNull(productId)) {
                pddString.append(" AND productId <> :productId");
            }
            Query query = manager.createQuery(pddString.toString());
            query.setParameter("productName", productName);
            if(Objects.nonNull(productId)) {
                query.setParameter("productId", productId);
            }
            List<ProductDefinitionData> pdd = query.getResultList();
            if(!CollectionUtils.isEmpty(pdd)) {
                throw new SumoException("Duplicate Entry of Product Name : " + productName + ". Product ID : " + pdd.get(0).getProductId());
            }

            CheckInUserCreation(productName, productId);
        } catch (SumoException exp) {
            LOG.error("Error : ", exp);
            throw exp;
        }
    }

    @Override
    public void isDuplicateProduct2(String productName, Integer productId) throws SumoException {
        try {
            StringBuilder pddString = new StringBuilder("FROM ProductDefinitionData WHERE UPPER(productName) = :productName");
            if(Objects.nonNull(productId)) {
                pddString.append(" AND productId <> :productId");
            }
            Query query = manager.createQuery(pddString.toString());
            query.setParameter("productName", productName);
            if(Objects.nonNull(productId)) {
                query.setParameter("productId", productId);
            }
            List<ProductDefinitionData> pdd = query.getResultList();
            if(!CollectionUtils.isEmpty(pdd)) {
                throw new SumoException("Duplicate Entry of Product Name : " + productName + ". Product ID : " + pdd.get(0).getProductId());
            }
            ProductDefinitionData definitionData = find(ProductDefinitionData.class, productId);
            if(Objects.isNull(definitionData) || Objects.isNull(definitionData.getProductCreationId())) {
                CheckInUserCreation(productName, null);
            } else {
                CheckInUserCreation(productName, definitionData.getProductCreationId());
            }
        } catch (SumoException exp) {
            LOG.error("Error : ", exp);
            throw exp;
        }
    }

    @Override
    public List<RequestedSkuDetails> findAllSkusInStatus(UserRequestDto skuRequest) throws Exception {
        try {
            if (CollectionUtils.isEmpty(skuRequest.getStatus()) &&
                    Objects.isNull(skuRequest.getStartDate()) &&
                    Objects.isNull(skuRequest.getEndDate())) {
                return new ArrayList<>();
            }

            StringBuilder qString = new StringBuilder(
                    "SELECT usc.userSkuCreationId, usc.skuName, usc.creationDate, usc.createdBy, usc.skuStatus " +
                            "FROM UserSkuCreationRequestData usc WHERE 1=1 "
            );

            if (Objects.nonNull(skuRequest.getStartDate())) {
                qString.append("AND usc.creationDate >= :startDate ");
            }
            if (Objects.nonNull(skuRequest.getEndDate())) {
                qString.append("AND usc.creationDate <= :endDate ");
            }
            if (!CollectionUtils.isEmpty(skuRequest.getStatus())) {
                qString.append("AND usc.skuStatus IN (:status) ");
            }
            qString.append("ORDER BY usc.userSkuCreationId DESC");

            Query query = manager.createQuery(qString.toString());

            // Set parameters
            if (!CollectionUtils.isEmpty(skuRequest.getStatus())) {
                List<SwitchStatus> filterStatus = skuRequest.getStatus().stream().map(SwitchStatus::valueOf).toList();
                query.setParameter("status", filterStatus);
            }
            if (Objects.nonNull(skuRequest.getStartDate())) {
                query.setParameter("startDate", AppUtils.getDate(skuRequest.getStartDate(), AppUtils.DATE_FORMAT_STRING));
            }
            if (Objects.nonNull(skuRequest.getEndDate())) {
                query.setParameter("endDate", AppUtils.getEndOfDay(AppUtils.getDate(skuRequest.getEndDate(), AppUtils.DATE_FORMAT_STRING)));
            }

            List<RequestedSkuDetails> details = new ArrayList<>();
            List<Object[]> resultList = query.getResultList();
            for(Object[] res : resultList) {
                RequestedSkuDetails detail = new RequestedSkuDetails();
                detail.setSkuId((Integer) res[0]);
                detail.setSkuName((String) res[1]);
                detail.setCreationDate((Date) res[2]);
                IdCodeName createdBy = SCMUtil.generateIdCodeName((Integer) res[3], "",
                        masterDataCache.getEmployees().get((Integer) res[3]));
                detail.setCreatedBy(createdBy);
                SwitchStatus status = (SwitchStatus) res[4];
                detail.setSkuStatus(status.name());
                details.add(detail);
            }
            return details;
        } catch (Exception exp) {
            LOG.error("Exception in findAllSkusInStatus ", exp);
            throw new SumoException("Error while getting requested SKU's", exp.getMessage());
        }
    }

    public void CheckInUserCreation(String productName, Integer productId) throws SumoException {
        try {
            StringBuilder requestString = new StringBuilder("FROM UserProductCreationRequestData WHERE UPPER(productName) = :productName AND productStatus NOT IN (:productStatus)");
            if(Objects.nonNull(productId)) {
                requestString.append(" AND productId <> :productId");
            }
            Query query1 = manager.createQuery(requestString.toString());
            query1.setParameter("productName", productName);
            query1.setParameter("productStatus", List.of(ProductStatus.CANCELLED.name(), ProductStatus.APPROVER_REJECTED.name(), ProductStatus.FINANCE_REJECTED.name()));
            if(Objects.nonNull(productId)) {
                query1.setParameter("productId", productId);
            }
            List<UserProductCreationRequestData> resultList = query1.getResultList();
            if(!CollectionUtils.isEmpty(resultList)) {
                throw new SumoException("Duplicate Entry of Product Name : " + productName + ". Product ID : " + productId);
            }
        } catch (Exception exp) {
            LOG.error("Error : ", exp);
            throw exp;
        }
    }

    @Override
    public void isSkuNameAlreadyExistsInUserSkuCreation(String skuName, Integer userSkuCreationId) throws SumoException {
        isSkuNameNull(skuName);
        StringBuilder queryString = new StringBuilder("SELECT userSkuCreationId FROM UserSkuCreationRequestData WHERE UPPER(skuName) = :skuName AND skuStatus NOT IN (:status) ");
        if(Objects.nonNull(userSkuCreationId)) {
            queryString.append("AND userSkuCreationId <>:userSkuCreationId");
        }

        Query query = manager.createQuery(queryString.toString());
        query.setParameter("skuName", skuName);
        if(Objects.nonNull(userSkuCreationId)) {
            query.setParameter("userSkuCreationId", userSkuCreationId);
        }
        query.setParameter("status", List.of(SwitchStatus.CANCELLED, SwitchStatus.REJECTED)).setMaxResults(1);
        List<Integer> data = query.getResultList();
        if(!CollectionUtils.isEmpty(data)) {
            throw new SumoException("Sku id : " + data.get(0) + " also contains same sku name: "+ skuName + " So please change the Sku Name");
        }
    }

    private void isSkuNameNull(String skuName) throws SumoException {
        if(StringUtils.isBlank(skuName)) {
            throw new SumoException("SKU name should be not be blank or null");
        }
    }

    @Override
    public void isSkuNameAlreadyExistsInSkuDefinition(String skuName, Integer skuId) throws SumoException {
        isSkuNameNull(skuName);
        StringBuilder queryString = new StringBuilder("SELECT skuId FROM SkuDefinitionData WHERE UPPER(skuName) = :skuName ");
        if(Objects.nonNull(skuId)) {
            queryString.append("AND skuId <>:skuId");
        }

        Query query = manager.createQuery(queryString.toString());
        query.setParameter("skuName", skuName);
        if(Objects.nonNull(skuId)) {
            query.setParameter("skuId", skuId);
        }
        List<Integer> data = query.getResultList();
        if(!CollectionUtils.isEmpty(data)) {
            throw new SumoException("Sku id : " + data.get(0) + " also contains same sku name: "+ skuName + " So please change the Sku Name");
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<Integer, DerivedMappingData> getDerivedProductsMapping(Integer id, boolean isUnitId) {
        StringBuilder queryString = new StringBuilder(" FROM DerivedMappingData WHERE ");
        if (isUnitId) {
            queryString.append(" unitId = :id ");
        } else {
            queryString.append(" product.productId = :id ");
        }
        Query query = manager.createQuery(queryString.toString());
        query.setParameter("id", id);
        List<DerivedMappingData> result = query.getResultList();
        if (CollectionUtils.isEmpty(result)) {
            return new HashMap<>();
        }

        return result.stream().collect(
                Collectors.toMap(
                        !isUnitId ? DerivedMappingData::getUnitId : d -> d.getProduct().getProductId(),
                        d -> d
                )
        );

    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<Integer, DerivedMappingData> getDerivedMappingsByIds(List<Integer> derivedMappingIds) {

        if (CollectionUtils.isEmpty(derivedMappingIds)) {
            return new HashMap<>();
        }
        Query query = manager.createQuery("FROM DerivedMappingData WHERE mappingId IN :derivedMappingIds");
        query.setParameter("derivedMappingIds", derivedMappingIds);
        List<DerivedMappingData> result = query.getResultList();
        return result.stream().collect(
                Collectors.toMap(
                        DerivedMappingData::getMappingId,
                        d -> d
                ));

    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<Integer, ProductDefinitionData> getProductsByIds(List<Integer> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Map.of();
        }
        Query query = manager.createQuery("FROM ProductDefinitionData WHERE productId IN :productIds");
        query.setParameter("productIds", productIds);
        List<ProductDefinitionData> result = query.getResultList();
        if (CollectionUtils.isEmpty(result)) {
            return Map.of();
        }
        return result.stream().collect(
                Collectors.toMap(
                        ProductDefinitionData::getProductId,
                        d -> d
                )
        );
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<RegionProductPackagingMappingData> getRegionProductPackagings(RPPMappingRequest request) throws SumoException {

        StringBuilder queryString = new StringBuilder("FROM RegionProductPackagingMappingData r WHERE 1=1 ");
        if (!CollectionUtils.isEmpty(request.getRegionCodes())) {
            queryString.append("AND r.regionCode IN :regionCodes ");
        }
        if (!CollectionUtils.isEmpty(request.getProductIds())) {
            queryString.append("AND r.productId IN :productIds ");
        }
        Query query = manager.createQuery(queryString.toString());
        if (!CollectionUtils.isEmpty(request.getRegionCodes())) {
            query.setParameter("regionCodes", request.getRegionCodes());
        }
        if (!CollectionUtils.isEmpty(request.getProductIds())) {
            query.setParameter("productIds", request.getProductIds());
        }
        try {
            return query.getResultList();
        } catch (NoResultException e) {
            LOG.warn("No RegionProductPackagingMapping found for the given criteria: {}", request);
        } catch (Exception e) {
            LOG.error("Error while fetching RegionProductPackagingMapping: ", e);
            throw new SumoException("Error while fetching RegionProductPackagingMapping", e.getMessage());
        }
        return null;
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<RegionProductPackagingMappingData> findByMappingIds(List<Integer> mappingIds) {
        if (!CollectionUtils.isEmpty(mappingIds)) {
            Query query = manager.createQuery("FROM RegionProductPackagingMappingData r WHERE r.id IN :mappingIds");
            query.setParameter("mappingIds", mappingIds);
            return query.getResultList();
        }
        return List.of();
    }

}

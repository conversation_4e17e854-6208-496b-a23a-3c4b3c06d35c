/**
 * 
 */
package com.stpl.tech.scm.core.util;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.ProductClassification;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.recipe.model.IngredientDefinition;
import com.stpl.tech.master.recipe.model.IngredientProduct;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariant;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import com.stpl.tech.master.recipe.model.RecipeCategoryCost;
import com.stpl.tech.master.recipe.model.RecipeCost;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.master.recipe.model.RecipeIngredientCost;
import com.stpl.tech.scm.domain.model.ProductPriceData;
import com.stpl.tech.scm.domain.model.ProductStatus;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

/**
 * A helper class to calculate the recipe cost based on the product prices.
 * 
 * <AUTHOR>
 *
 */
public class RecipeCostCalculator {

	private RecipeDetail recipe;
	private Map<Integer, ProductPriceData> productPrices;
	private Map<Integer, Pair<List<SkuDefinition> , BigDecimal>> productSkuMapWithPrice;

	/**
	 * 
	 * @param recipe
	 *            recipe detail whose cost needs to be calculated
	 * @param productPrices
	 *            product prices of all products in SCM
	 */
	public RecipeCostCalculator(RecipeDetail recipe, Map<Integer, ProductPriceData> productPrices) {
		super();
		this.recipe = recipe;
		this.productPrices = productPrices;
	}

	/**
	 * This method calculates the dine in, delivery and takeaway cost of a
	 * recipe. Logic 1 : For all variants in ingredients, we pick up the default
	 * variant and its quantity and multiply that by the price of the product to
	 * get the cost of the variant Logic 2 : For all products in ingredients, we
	 * pick up the default product and its quantity and multiply that by the
	 * price of the product to get the cost of the product Logic 1 : For all
	 * components in ingredients, we add cost of all components by multiplying
	 * individual products with their prices
	 * 
	 * @return the object holding the calculated recipe cost for dine in,
	 *         delivery and takeaway
	 */
	public RecipeCost calculate() {
		RecipeCost cost = new RecipeCost(recipe.getRecipeId(), recipe.getName(), recipe.getProduct().getProductId(),
				recipe.getProduct().getName(), recipe.getDimension().getName());
		BigDecimal v = BigDecimal.ZERO;
		if (recipe.getIngredient() != null) {
			if (recipe.getIngredient().getVariants() != null) {
				for (IngredientVariant variant : recipe.getIngredient().getVariants()) {
					for (IngredientVariantDetail detail : variant.getDetails()) {
						if (detail.isDefaultSetting()) {
							RecipeIngredientCost ingredient = addPrice(cost.getErrorCodes(),
									IngredientDefinition.IngredientVariant, variant.getProduct().getProductId(),
									variant.getProduct().getName(), detail.getQuantity(), detail.getYield(),
									detail.getUom().name());
							v = AppUtils.add(v, ingredient.getCost());
							cost.getCommonIngredient().add(ingredient);
						}
					}

				}
			}

			if (recipe.getIngredient().getProducts() != null) {
				for (IngredientProduct variant : recipe.getIngredient().getProducts()) {
					for (IngredientProductDetail detail : variant.getDetails()) {
						if (detail.isDefaultSetting()) {
							RecipeIngredientCost ingredient = addPrice(cost.getErrorCodes(),
									IngredientDefinition.IngredientProducts, detail.getProduct().getProductId(),
									detail.getProduct().getName(), detail.getQuantity(), detail.getYield(),
									detail.getUom().name());
							v = AppUtils.add(v, ingredient.getCost());
							cost.getCommonIngredient().add(ingredient);
						}
					}

				}
			}

			if (recipe.getIngredient().getComponents() != null) {
				for (IngredientProductDetail variant : recipe.getIngredient().getComponents()) {
					RecipeIngredientCost ingredient = addPrice(cost.getErrorCodes(),
							IngredientDefinition.IngredientComponents, variant.getProduct().getProductId(),
							variant.getProduct().getName(), variant.getQuantity(), variant.getYield(),
							variant.getUom().name());
					v = AppUtils.add(v, ingredient.getCost());
					cost.getCommonIngredient().add(ingredient);

				}
			}

		}

		if (ProductClassification.SCM_PRODUCT.equals(recipe.getProduct().getClassification())) {
			cost.getCategoryCost().add(getConsumablesCost(recipe.getDineInConsumables(), v, cost,
					IngredientDefinition.DineInConsumables, UnitCategory.CAFE));
		} else {
			cost.getCategoryCost().add(getConsumablesCost(recipe.getDineInConsumables(), v, cost,
					IngredientDefinition.DineInConsumables, UnitCategory.CAFE));
			cost.getCategoryCost().add(getConsumablesCost(recipe.getDeliveryConsumables(), v, cost,
					IngredientDefinition.DeliveryConsumables, UnitCategory.DELIVERY));
			cost.getCategoryCost().add(getConsumablesCost(recipe.getTakeawayConsumables(), v, cost,
					IngredientDefinition.TakeAwayConsumables, UnitCategory.TAKE_AWAY));
		}
		return cost;
	}

	private RecipeIngredientCost addPrice(List<String> errorCodes, IngredientDefinition type, int productId,
			String name, BigDecimal quantity, BigDecimal yield, String uom) {
		RecipeIngredientCost data = new RecipeIngredientCost(type, productId, name, uom, quantity, yield);
		if (productPrices.containsKey(productId) && productPrices.get(productId) != null) {
			ProductPriceData priceData = productPrices.get(productId);
			BigDecimal price = priceData.getPrice();
			data.setCalculatedFromNegotiatedPrice(AppConstants.YES);
			if (Objects.nonNull(productSkuMapWithPrice) && productSkuMapWithPrice.containsKey(productId)) {
				Pair<List<SkuDefinition>, BigDecimal> pair = productSkuMapWithPrice.get(productId);
				if (Objects.nonNull(pair) && Objects.nonNull(pair.getValue())) {
					price = pair.getValue();
					data.setCalculatedFromNegotiatedPrice(AppConstants.NO);
				}
			}
			data.setPrice(price);
			if (ProductStatus.IN_ACTIVE.equals(priceData.getStatus())) {
				errorCodes.add(String.format("%s : %s [%d] - %s Inactive Product", type, name, productId, price));
			}
			if (price.compareTo(BigDecimal.ZERO) <= 0) {
				errorCodes
						.add(String.format("%s : %s [%d] - %s Incorrect Product Price", type, name, productId, price));
				data.setCost(BigDecimal.ZERO);
			} else {

			}
			BigDecimal amount = getCost(price, quantity, yield);
			data.setCost(amount);
		} else {
			errorCodes.add(String.format("%s : %s [%d] No Product Price", type, name, productId));
			data.setCost(BigDecimal.ZERO);
		}
		return data;
	}

	private BigDecimal getCost(BigDecimal price, BigDecimal quantity, BigDecimal yield) {
		return AppUtils.multiplyWithScale10(price, AppUtils.multiplyWithScale10(quantity,
				AppUtils.divideWithScale10(new BigDecimal(100d), yield == null ? new BigDecimal(100d) : yield)));

	}

	private RecipeCategoryCost getConsumablesCost(List<IngredientProductDetail> details, BigDecimal curretCost,
			RecipeCost cost, IngredientDefinition type, UnitCategory costType) {
		RecipeCategoryCost category = new RecipeCategoryCost(costType);
		BigDecimal v = AppUtils.add(BigDecimal.ZERO, curretCost);
		if (details != null && details.size() > 0) {
			for (IngredientProductDetail variant : details) {
				RecipeIngredientCost ingredient = addPrice(category.getErrorCodes(), type,
						variant.getProduct().getProductId(), variant.getProduct().getName(), variant.getQuantity(),
						variant.getYield(), variant.getUom().name());
				v = AppUtils.add(v, ingredient.getCost());
				category.getIngredients().add(ingredient);
			}
		}
		category.setCost(v);
		return category;
	}

	public Map<Integer, Pair<List<SkuDefinition>, BigDecimal>> getProductSkuMapWithPrice() {
		return productSkuMapWithPrice;
	}

	public void setProductSkuMapWithPrice(Map<Integer, Pair<List<SkuDefinition>, BigDecimal>> productSkuMapWithPrice) {
		this.productSkuMapWithPrice = productSkuMapWithPrice;
	}
}

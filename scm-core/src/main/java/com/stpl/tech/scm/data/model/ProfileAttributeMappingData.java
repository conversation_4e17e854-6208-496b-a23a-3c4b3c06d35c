/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;
// Generated 4 May, 2016 12:06:56 AM by Hibernate Tools 3.2.2.GA

import javax.persistence.*;

import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * ProfileAttributeMappingData generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "PROFILE_ATTRIBUTE_MAPPING", uniqueConstraints = @UniqueConstraint(columnNames = {"PROFILE_ID",
    "ATTRIBUTE_ID"}))
public class ProfileAttributeMappingData implements java.io.Serializable {

    private Integer profileAttributeMappingId;
    private ProfileDefinitionData profileDefinitionData;
    private AttributeDefinitionData attributeDefinition;
    private Date creationDate;
    private int createdBy;
    private String isDefinedAtProduct;
    private String isDefinedAtSKU;
    private String isDefinedAtAsset;
    private String isMandatoryAtProduct;
    private String isMandatoryAtSKU;
    private String isMandatoryAtAsset;
    private String isOverridableAtProduct;
    private String isOverridableAtSKU;
    private String participateInName;
    private Date updationDate;
    private int updatedBy;
    private String status;
    private String associatedImage;
    private String isStandAlone;

    public ProfileAttributeMappingData() {
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "PROFILE_ATTRIBUTE_MAPPING_ID", unique = true, nullable = false)
    public Integer getProfileAttributeMappingId() {
        return profileAttributeMappingId;
    }

    public void setProfileAttributeMappingId(Integer profileAttributeMappingId) {
        this.profileAttributeMappingId = profileAttributeMappingId;
    }

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "PROFILE_ID", nullable = false)
    public ProfileDefinitionData getProfileDefinitionData() {
        return profileDefinitionData;
    }

    public void setProfileDefinitionData(ProfileDefinitionData profileDefinitionData) {
        this.profileDefinitionData = profileDefinitionData;
    }

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "ATTRIBUTE_ID", nullable = false)
    public AttributeDefinitionData getAttributeDefinition() {
        return this.attributeDefinition;
    }

    public void setAttributeDefinition(AttributeDefinitionData attributeDefinition) {
        this.attributeDefinition = attributeDefinition;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_DATE", length = 19)
    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    @Column(name = "CREATED_BY", nullable = false)
    public int getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(int createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "IS_DEFINED_AT_PRODUCT", nullable = false, length = 10)
    public String getIsDefinedAtProduct() {
        return isDefinedAtProduct;
    }

    public void setIsDefinedAtProduct(String isDefinedAtProduct) {
        this.isDefinedAtProduct = isDefinedAtProduct;
    }

    @Column(name = "IS_DEFINED_AT_SKU", nullable = false, length = 10)
    public String getIsDefinedAtSKU() {
        return isDefinedAtSKU;
    }

    public void setIsDefinedAtSKU(String isDefinedAtSKU) {
        this.isDefinedAtSKU = isDefinedAtSKU;
    }

    @Column(name = "IS_DEFINED_AT_ASSET", nullable = false, length = 1)
    public String getIsDefinedAtAsset() {
        return isDefinedAtAsset;
    }

    public void setIsDefinedAtAsset(String isDefinedAtAsset) {
        this.isDefinedAtAsset = isDefinedAtAsset;
    }

    @Column(name = "IS_MANDATORY_AT_PRODUCT", nullable = false, length = 1)
    public String getIsMandatoryAtProduct() {
        return isMandatoryAtProduct;
    }

    public void setIsMandatoryAtProduct(String isMandatoryAtProduct) {
        this.isMandatoryAtProduct = isMandatoryAtProduct;
    }

    @Column(name = "IS_MANDATORY_AT_SKU", nullable = false, length = 1)
    public String getIsMandatoryAtSKU() {
        return isMandatoryAtSKU;
    }

    public void setIsMandatoryAtSKU(String isMandatoryAtSKU) {
        this.isMandatoryAtSKU = isMandatoryAtSKU;
    }

    @Column(name = "IS_MANDATORY_AT_ASSET", nullable = false, length = 1)
    public String getIsMandatoryAtAsset() {
        return isMandatoryAtAsset;
    }

    public void setIsMandatoryAtAsset(String isMandatoryAtAsset) {
        this.isMandatoryAtAsset = isMandatoryAtAsset;
    }

    @Column(name = "IS_OVERRIDABLE_AT_PRODUCT", nullable = false, length = 1)
    public String getIsOverridableAtProduct() {
        return isOverridableAtProduct;
    }

    public void setIsOverridableAtProduct(String isOverridableAtProduct) {
        this.isOverridableAtProduct = isOverridableAtProduct;
    }

    @Column(name = "IS_OVERRIDABLE_AT_ASSET", nullable = false, length = 1)
    public String getIsOverridableAtSKU() {
        return isOverridableAtSKU;
    }

    public void setIsOverridableAtSKU(String isOverridableAtSKU) {
        this.isOverridableAtSKU = isOverridableAtSKU;
    }

    @Column(name = "PARTICIPATE_IN_NAME", nullable = false, length = 1)
    public String getParticipateInName() {
        return participateInName;
    }

    public void setParticipateInName(String participateInName) {
        this.participateInName = participateInName;
    }

    @Column(name = "LAST_UPDATED_BY", nullable = false)
    public int getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(int updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_UPDATED_DATE", length = 19)
    public Date getUpdationDate() {
        return updationDate;
    }

    public void setUpdationDate(Date updationDate) {
        this.updationDate = updationDate;
    }

    @Column(name = "STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "PROFILE_ATTRIBUTE_MAPPING_IMG", length=255)
    public String getAssociatedImage() {
        return associatedImage;
    }

    public void setAssociatedImage(String associatedImage) {
        this.associatedImage = associatedImage;
    }

    @Column(name = "IS_STANDALONE", nullable = false, length = 1)
    public String getIsStandAlone() {
        return isStandAlone;
    }

    public void setIsStandAlone(String isStandAlone) {
        this.isStandAlone = isStandAlone;
    }
}

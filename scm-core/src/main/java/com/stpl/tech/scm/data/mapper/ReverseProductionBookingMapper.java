package com.stpl.tech.scm.data.mapper;

import com.stpl.tech.scm.domain.model.BookingConsumption;
import com.stpl.tech.scm.domain.model.ProductionBooking;
import com.stpl.tech.scm.domain.model.ReverseBookingConsumption;
import com.stpl.tech.scm.domain.model.ReverseProductionBooking;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ReverseProductionBookingMapper {
    ReverseProductionBookingMapper INSTANCE = Mappers.getMapper(ReverseProductionBookingMapper.class);
    ReverseProductionBooking toDomain(ProductionBooking productionBooking);
    ProductionBooking toDomain(ReverseProductionBooking productionBooking);
    List<ProductionBooking> toDomainRPBList(List<ReverseProductionBooking> productionBooking);

    List<ReverseBookingConsumption> toDomainList(List<BookingConsumption> bookingConsumptions);
    List<BookingConsumption> toDomainBCList(List<ReverseBookingConsumption> reverseBookingConsumptions);

}

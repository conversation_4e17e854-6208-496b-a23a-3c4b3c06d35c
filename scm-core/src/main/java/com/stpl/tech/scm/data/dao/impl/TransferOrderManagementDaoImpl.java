package com.stpl.tech.scm.data.dao.impl;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.persistence.NoResultException;
import javax.persistence.Query;

import com.stpl.tech.scm.data.model.*;
import com.stpl.tech.scm.domain.model.*;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import com.stpl.tech.master.tax.model.TaxationDetailDao;
import com.stpl.tech.scm.data.dao.TransferOrderManagementDao;

/**
 * Created by <PERSON><PERSON> on 22-06-2016.
 */
@Repository
public class TransferOrderManagementDaoImpl extends SCMAbstractDaoImpl implements TransferOrderManagementDao {

    private static final Logger LOG = LoggerFactory.getLogger(TransferOrderManagementDaoImpl.class);

    @Override
    public List<TransferOrderData> findTransferOrders(Integer generationUnitId, Integer generatedForUnitId,
                                                      Date startDate, Date endDate, SCMOrderStatus status, Integer transferOrderId) {
        String queryString = "FROM TransferOrderData t WHERE ";
        if (transferOrderId != null) {
            queryString += "t.id = :transferOrderId ";
        } else {
            queryString += "t.generationTime >= :startDate and t.generationTime < :endDate ";
        }
        if (generationUnitId != null) {
            queryString += "and t.generationUnitId = :generationUnitId ";
        }
        if (generatedForUnitId != null) {
            queryString += "and t.generatedForUnitId = :generatedForUnitId ";
        }
        if (status != null) {
            queryString += "and t.status = :status ";
        }
        queryString += "order by t.generationTime";
        Query query = manager.createQuery(queryString);
        if (transferOrderId != null) {
            query.setParameter("transferOrderId", transferOrderId);
        } else {
            query.setParameter("startDate", startDate);
            query.setParameter("endDate", endDate);
        }
        if (generationUnitId != null) {
            query.setParameter("generationUnitId", generationUnitId);
        }
        if (generatedForUnitId != null) {
            query.setParameter("generatedForUnitId", generatedForUnitId);
        }
        if (status != null) {
            query.setParameter("status", status.value());
        }
        return query.getResultList();
    }

    @Override
    public BigInteger getTransferOrdersCount(){
        String queryString = "SELECT COUNT(*) FROM TRANSFER_ORDER WHERE " +
                "TRANSFER_TYPE= 'INVOICE' and " +
                "TRANSFER_ORDER_STATUS != 'CANCELLED' and " +
                "(E_INVOICE_GENERATED != 'Y'  or E_INVOICE_GENERATED is null) and " +
                "GENERATION_TIME >= '2020-10-01'";
        Query query = manager.createNativeQuery(queryString);
        return (BigInteger) query.getSingleResult();
    }

    @Override
    public List<PendingTransferOrder> findPendingTransferOrders(Integer selectedStateCode, Integer generationUnitId, Integer generatedForUnitId, Date startDate, Date endDate, EnvType env) {
        List<PendingTransferOrder> pendingTransferOrders = new ArrayList<>();
        String queryString = "SELECT t.TRANSFER_ORDER_ID, t.GENERATION_UNIT_ID, t.GENERATED_FOR_UNIT_ID, t.REQUEST_ORDER_ID, t.GENERATED_BY, t.GENERATION_TIME, t.TRANSFER_ORDER_STATUS, t.PARTIAL_INVOICE_IRN" +
        " FROM TRANSFER_ORDER t ";
        if(env.equals(EnvType.DEV) || env.equals(EnvType.LOCAL)){
            queryString += " LEFT JOIN KETTLE_MASTER_DEV";
        }else if(env.equals(EnvType.STAGE)){
            queryString += " LEFT JOIN KETTLE_MASTER_STAGE";
        }else if(AppUtils.isProd(env)){
            queryString += " LEFT JOIN KETTLE_MASTER";
        }
        queryString += ".UNIT_DETAIL u ON t.GENERATION_UNIT_ID = u.UNIT_ID " ;
        if(env.equals(EnvType.DEV) || env.equals(EnvType.LOCAL)){
            queryString += " LEFT JOIN KETTLE_MASTER_DEV";
        }else if(env.equals(EnvType.STAGE)){
            queryString += " LEFT JOIN KETTLE_MASTER_STAGE";
        }else if(AppUtils.isProd(env)){
            queryString += " LEFT JOIN KETTLE_MASTER";
        }
        queryString += ".LOCATION_DETAIL l ON u.LOCATION_DETAIL_ID = l.LOCATION_ID " ;
        if(env.equals(EnvType.DEV) || env.equals(EnvType.LOCAL)){
            queryString += " LEFT JOIN KETTLE_MASTER_DEV";
        }else if(env.equals(EnvType.STAGE)){
            queryString += " LEFT JOIN KETTLE_MASTER_STAGE";
        }else if(AppUtils.isProd(env)){
            queryString += " LEFT JOIN KETTLE_MASTER";
        }
        queryString += ".STATE_DETAIL s ON l.STATE_DETAIL_ID = s.STATE_DETAIL_ID WHERE ";
        boolean unitSelected = false;
        queryString += "t.GENERATION_TIME >= :startDate and t.GENERATION_TIME < :endDate ";
        queryString += "and t.TRANSFER_TYPE = :transferType ";
        queryString += "and t.TRANSFER_ORDER_STATUS != :status ";
        if(selectedStateCode != null){
            if(generationUnitId != null){
                queryString += "and t.GENERATION_UNIT_ID = :generationUnitId ";
                unitSelected = true;
            }else{
                queryString += "and s.STATE_CODE = :stateCode ";
            }
        }
        if( !unitSelected && generationUnitId != null){
            queryString += "and t.GENERATION_UNIT_ID = :generationUnitId ";
        }
        queryString += "and (t.E_INVOICE_GENERATED != :eInvoiceGenerated  or t.E_INVOICE_GENERATED is null) ";
        queryString += "and t.PARTIAL_INVOICE_IRN is null ";
        queryString += " order by t.GENERATION_TIME";
        Query query = manager.createNativeQuery(queryString);
            query.setParameter("startDate", startDate);
            query.setParameter("endDate", endDate);

            query.setParameter("transferType", "INVOICE");
            query.setParameter("status", "CANCELLED");
            if(selectedStateCode != null && !unitSelected){
                query.setParameter("stateCode", selectedStateCode);
            }
            if(generationUnitId != null){
                query.setParameter("generationUnitId", generationUnitId);
            }
            query.setParameter("eInvoiceGenerated", 'Y');
        List<Object[]> result = query.getResultList();
        for(Object[] o:result){
            pendingTransferOrders.add(new PendingTransferOrder((Integer) o[0],(Integer) o[1],(Integer) o[2], (Integer) o[3],
                    (Integer) o[4],(Date) o[5], (String) o[6],(String) o[7]));
        }
        return pendingTransferOrders;
    }

    @Override
    public List<PendingTransferOrder> findPartiallyCompleteTransferOrders(Integer selectedStateCode, Integer generationUnitId, Integer generatedForUnitId, Date startDate, Date endDate, EnvType env) {
        List<PendingTransferOrder> pendingTransferOrders = new ArrayList<>();
        String queryString = "SELECT t.TRANSFER_ORDER_ID, t.GENERATION_UNIT_ID, t.GENERATED_FOR_UNIT_ID, t.REQUEST_ORDER_ID, t.GENERATED_BY, t.GENERATION_TIME, t.TRANSFER_ORDER_STATUS, t.PARTIAL_INVOICE_IRN" +
        " FROM TRANSFER_ORDER t INNER JOIN TRANSFER_ORDER_E_INVOICE e ON e.TRANSFER_ORDER_ID = t.TRANSFER_ORDER_ID";
        if(env.equals(EnvType.DEV) || env.equals(EnvType.LOCAL)){
            queryString += " LEFT JOIN KETTLE_MASTER_DEV";
        }else if(env.equals(EnvType.STAGE)){
            queryString += " LEFT JOIN KETTLE_MASTER_STAGE";
        }else if(AppUtils.isProd(env)){
            queryString += " LEFT JOIN KETTLE_MASTER";
        }
        queryString += ".UNIT_DETAIL u ON t.GENERATION_UNIT_ID = u.UNIT_ID " ;
        if(env.equals(EnvType.DEV) || env.equals(EnvType.LOCAL)){
            queryString += " LEFT JOIN KETTLE_MASTER_DEV";
        }else if(env.equals(EnvType.STAGE)){
            queryString += " LEFT JOIN KETTLE_MASTER_STAGE";
        }else if(AppUtils.isProd(env)){
            queryString += " LEFT JOIN KETTLE_MASTER";
        }
        queryString += ".LOCATION_DETAIL l ON u.LOCATION_DETAIL_ID = l.LOCATION_ID " ;
        if(env.equals(EnvType.DEV) || env.equals(EnvType.LOCAL)){
            queryString += " LEFT JOIN KETTLE_MASTER_DEV";
        }else if(env.equals(EnvType.STAGE)){
            queryString += " LEFT JOIN KETTLE_MASTER_STAGE";
        }else if(AppUtils.isProd(env)){
            queryString += " LEFT JOIN KETTLE_MASTER";
        }
        queryString += ".STATE_DETAIL s ON l.STATE_DETAIL_ID = s.STATE_DETAIL_ID WHERE ";
        boolean unitSelected = false;
        queryString += "t.GENERATION_TIME >= :startDate and t.GENERATION_TIME < :endDate ";
        queryString += "and t.TRANSFER_TYPE = :transferType ";
        queryString += "and t.TRANSFER_ORDER_STATUS != :status ";
        if(selectedStateCode != null){
            if(generationUnitId != null){
                queryString += "and t.GENERATION_UNIT_ID = :generationUnitId ";
                unitSelected = true;
            }else{
                queryString += "and s.STATE_CODE = :stateCode ";
            }
        }
        if( !unitSelected && generationUnitId != null){
            queryString += "and t.GENERATION_UNIT_ID = :generationUnitId ";
        }
        queryString += "and t.E_INVOICE_GENERATED = :eInvoiceGenerated ";
        queryString += "and e.STATUS = :eInvoiceStatus ";
        queryString += " order by t.GENERATION_TIME";
        Query query = manager.createNativeQuery(queryString);
            query.setParameter("startDate", startDate);
            query.setParameter("endDate", endDate);

            query.setParameter("transferType", "INVOICE");
            query.setParameter("status", "CANCELLED");
            if(selectedStateCode != null && !unitSelected){
                query.setParameter("stateCode", selectedStateCode);
            }
            if(generationUnitId != null){
                query.setParameter("generationUnitId", generationUnitId);
            }
            query.setParameter("eInvoiceGenerated", 'Y');
            query.setParameter("eInvoiceStatus", "PARTIALLY_COMPLETED");
        List<Object[]> result = query.getResultList();
        for(Object[] o:result){
            pendingTransferOrders.add(new PendingTransferOrder((Integer) o[0],(Integer) o[1],(Integer) o[2], (Integer) o[3],
                    (Integer) o[4],(Date) o[5], (String) o[6],(String) o[7]));
        }
        return pendingTransferOrders;
    }

    @Override
    public List<PendingTransferOrder> findLostExcelCompletedTO(Integer selectedStateCode, Integer generationUnitId, Integer generatedForUnitId, Date startDate, Date endDate, EnvType env) {
        List<PendingTransferOrder> pendingTransferOrders = new ArrayList<>();
        String queryString = "SELECT t.TRANSFER_ORDER_ID, t.GENERATION_UNIT_ID, t.GENERATED_FOR_UNIT_ID, t.REQUEST_ORDER_ID, t.GENERATED_BY, t.GENERATION_TIME, t.TRANSFER_ORDER_STATUS, t.PARTIAL_INVOICE_IRN" +
                " FROM TRANSFER_ORDER t ";
        if(env.equals(EnvType.DEV) || env.equals(EnvType.LOCAL)){
            queryString += " LEFT JOIN KETTLE_MASTER_DEV";
        }else if(env.equals(EnvType.STAGE)){
            queryString += " LEFT JOIN KETTLE_MASTER_STAGE";
        }else if(AppUtils.isProd(env)){
            queryString += " LEFT JOIN KETTLE_MASTER";
        }
        queryString += ".UNIT_DETAIL u ON t.GENERATION_UNIT_ID = u.UNIT_ID " ;
        if(env.equals(EnvType.DEV) || env.equals(EnvType.LOCAL)){
            queryString += " LEFT JOIN KETTLE_MASTER_DEV";
        }else if(env.equals(EnvType.STAGE)){
            queryString += " LEFT JOIN KETTLE_MASTER_STAGE";
        }else if(AppUtils.isProd(env)){
            queryString += " LEFT JOIN KETTLE_MASTER";
        }
        queryString += ".LOCATION_DETAIL l ON u.LOCATION_DETAIL_ID = l.LOCATION_ID " ;
        if(env.equals(EnvType.DEV) || env.equals(EnvType.LOCAL)){
            queryString += " LEFT JOIN KETTLE_MASTER_DEV";
        }else if(env.equals(EnvType.STAGE)){
            queryString += " LEFT JOIN KETTLE_MASTER_STAGE";
        }else if(AppUtils.isProd(env)){
            queryString += " LEFT JOIN KETTLE_MASTER";
        }
        queryString += ".STATE_DETAIL s ON l.STATE_DETAIL_ID = s.STATE_DETAIL_ID WHERE ";
        boolean unitSelected = false;
        queryString += "t.GENERATION_TIME >= :startDate and t.GENERATION_TIME < :endDate ";
        queryString += "and t.TRANSFER_TYPE = :transferType ";
        queryString += "and t.TRANSFER_ORDER_STATUS != :status ";
        if(selectedStateCode != null){
            if(generationUnitId != null){
                queryString += "and t.GENERATION_UNIT_ID = :generationUnitId ";
                unitSelected = true;
            }else{
                queryString += "and s.STATE_CODE = :stateCode ";
            }
        }
        if( !unitSelected && generationUnitId != null){
            queryString += "and t.GENERATION_UNIT_ID = :generationUnitId ";
        }
        queryString += "and (t.E_INVOICE_GENERATED != :eInvoiceGenerated  or t.E_INVOICE_GENERATED is null) ";
        queryString += "and t.PARTIAL_INVOICE_IRN is not null ";
        queryString += " order by t.GENERATION_TIME";
        Query query = manager.createNativeQuery(queryString);
        query.setParameter("startDate", startDate);
        query.setParameter("endDate", endDate);
        query.setParameter("transferType", "INVOICE");
        query.setParameter("status", "CANCELLED");
        if(selectedStateCode != null && !unitSelected){
            query.setParameter("stateCode", selectedStateCode);
        }
        if(generationUnitId != null){
            query.setParameter("generationUnitId", generationUnitId);
        }
        query.setParameter("eInvoiceGenerated", 'Y');
        List<Object[]> result = query.getResultList();
        for(Object[] o:result){
            pendingTransferOrders.add(new PendingTransferOrder((Integer) o[0],(Integer) o[1],(Integer) o[2], (Integer) o[3],
                    (Integer) o[4],(Date) o[5], (String) o[6],(String) o[7]));
        }
        return pendingTransferOrders;
    }


    @Override
    public List<TransferOrderData> incrementalTransferOrders(Integer generationUnitId, Integer generatedForUnitId, Date startDate, Date endDate, SCMOrderStatus status, Integer transferOrderId, String ids) {
        String queryString = "FROM TransferOrderData t WHERE ";
        if (transferOrderId != null) {
            queryString += "t.id = :transferOrderId ";
        } else {
            queryString += "t.generationTime >= :startDate and t.generationTime < :endDate ";
        }
        if (generationUnitId != null) {
            queryString += "and t.generationUnitId = :generationUnitId ";
        }
        if (generatedForUnitId != null) {
            queryString += "and t.generatedForUnitId = :generatedForUnitId ";
        }
        if (status != null) {
            queryString += "and t.status = :status ";
        }
        if (ids != null) {
            queryString += "and t.id not in (" + ids + ") ";
        }
        queryString += "order by t.generationTime";
        Query query = manager.createQuery(queryString);
        if (transferOrderId != null) {
            query.setParameter("transferOrderId", transferOrderId);
        } else {
            query.setParameter("startDate", startDate);
            query.setParameter("endDate", endDate);
        }
        if (generationUnitId != null) {
            query.setParameter("generationUnitId", generationUnitId);
        }
        if (generatedForUnitId != null) {
            query.setParameter("generatedForUnitId", generatedForUnitId);
        }
        if (status != null) {
            query.setParameter("status", status.value());
        }
        return query.getResultList();
    }

    @Override
    public GoodsReceivedData getGRFromTransferOrder(int transferOrderId) {
        try {
            Query query = manager
                    .createQuery("FROM GoodsReceivedData g WHERE g.transferOrderData.id = :transferOrderId");
            query.setParameter("transferOrderId", transferOrderId);
            return (GoodsReceivedData) query.getSingleResult();
        } catch (NoResultException e) {
            LOG.info("GR for TO id {} not found!", transferOrderId);
            return null;
        }
    }

    @Override
    public void setTaxDetail(boolean applyTaxes, TransferOrderData detail, List<TaxDetail> taxes) {

        if (!applyTaxes || taxes == null || taxes.isEmpty()) {
            return;
        }
        for (TaxDetail tax : taxes) {
            TransferOrderTaxDetail taxDetail = new TransferOrderTaxDetail();
            setTaxInfo(taxDetail, tax);
            taxDetail.setOrderDetail(detail);
            manager.persist(taxDetail);
        }
        manager.flush();

    }

    private void setTaxInfo(TaxationDetailDao taxDetail, TaxDetail tax) {
        taxDetail.setTaxCode(tax.getCode());
        taxDetail.setTaxType(tax.getType());
        taxDetail.setTotalTax(tax.getValue());
        taxDetail.setTaxPercentage(tax.getPercentage());
        taxDetail.setTotalAmount(tax.getTotal());
        taxDetail.setTaxableAmount(tax.getTaxable());
    }


    @Override
    public int getNextStateInvoiceId(int stateId, String type) {

        Query query = manager.createQuery("FROM StateSequenceId E where E.stateId = :stateId and E.idType = :idType and E.financialYear IS NULL");
        query.setParameter("idType", type);
        query.setParameter("stateId", stateId);
        StateSequenceId sequence = null;
        try {
            sequence = (StateSequenceId) query.getSingleResult();
        } catch (NoResultException e) {
            sequence = addStateSequenceId(stateId, type);
        }
        int currentValue = sequence.getNextValue();
        sequence.setNextValue(currentValue + 1);
        return currentValue;
    }

    @Override
    public int getCurrentStateInvoiceId(int stateId, String type) {

        Query query = manager.createQuery("FROM StateSequenceId E where E.stateId = :stateId and E.idType = :idType and E.financialYear IS NULL");
        query.setParameter("idType", type);
        query.setParameter("stateId", stateId);
        StateSequenceId sequence = (StateSequenceId) query.getSingleResult();
        int currentValue = sequence.getNextValue();
        return currentValue-1;
    }

    @Override
    public int getNextStateInvoiceId(int stateId, String type, Integer financialYear) {
        Query query = manager.createQuery("FROM StateSequenceId E where E.stateId = :stateId and E.idType = :idType and E.financialYear =:financialYear");
        query.setParameter("idType", type);
        query.setParameter("stateId", stateId);
        query.setParameter("financialYear", financialYear);
        StateSequenceId sequence = null;
        try {
            sequence = (StateSequenceId) query.getSingleResult();
        } catch (NoResultException e) {
            sequence = addStateSequenceId(stateId, type, financialYear);
        }
        int currentValue = sequence.getNextValue();
        sequence.setNextValue(currentValue + 1);
        return currentValue;
    }

    private StateSequenceId addStateSequenceId(int stateId, String type, Integer financialYear) {
        StateSequenceId info = new StateSequenceId(stateId, type, 1, financialYear);
        manager.persist(info);
        return info;
    }

    @Override
    public TransferOrderData findByRequestOrderId(Integer requestOrderId) {
        TransferOrderData transferOrderData = null;
        Query query = manager.createQuery("FROM TransferOrderData E where E.requestOrderData.id = :id and E.status <> :cancelled");
        query.setParameter("id", requestOrderId)
                .setParameter("cancelled", SCMOrderStatus.CANCELLED.toString()).setMaxResults(1);
        List<TransferOrderData> transferOrderDataList = query.getResultList();
        if (transferOrderDataList != null && !transferOrderDataList.isEmpty()) {
            transferOrderData = transferOrderDataList.get(0);
        }
        return transferOrderData;
    }

    private StateSequenceId addStateSequenceId(int stateId, String type) {
        StateSequenceId info = new StateSequenceId(stateId, type, 1);
        manager.persist(info);
        return info;

    }

    @Override
    public String getEwayBill(int transferOrderId) {
        Query query = manager.createQuery("FROM EwayBillData E where E.transferOrder.id = :id");
        query.setParameter("id", transferOrderId);
        EwayBillData ewayBill = (EwayBillData) query.getSingleResult();
        if (ewayBill != null) {
            return ewayBill.getEwayBillNumber();
        }
        return null;
    }

    @Override
    public boolean getAlreadyTransferredRO(List<Integer> roIds) {
        boolean flag = false;
        Query query = manager.createQuery("FROM RequestOrderData E where E.id IN (:ids) and E.childRO.id IS NOT NULL");
        query.setParameter("ids", roIds);
        try {
            List<RequestOrderData> requestOrderData = query.getResultList();
            if(requestOrderData.size() > 0){
                return true;
            }
        }catch (Exception e){
            return false;
        }
        return flag;
    }

	@Override
	public List<TransferOrderItemData> findByTransferId(Integer transferOrderId) {
		Query query = manager
				.createQuery("FROM TransferOrderItemData E where E.transferOrderData.id = :transferOrderId");
		query.setParameter("transferOrderId", transferOrderId);
		try {
			return query.getResultList();
		} catch (Exception e) {
			return new ArrayList<>();
		}
	}

    @Override
    public TransferOrderData findByRequestOrderIds(List<Integer> requestOrderIds) {
        TransferOrderData transferOrderData = null;
        Query query = manager.createQuery("FROM TransferOrderData E where E.requestOrderData.id in :ids and E.status <> :cancelled");
        query.setParameter("ids", requestOrderIds)
                .setParameter("cancelled", SCMOrderStatus.CANCELLED.toString()).setMaxResults(1);
        List<TransferOrderData> transferOrderDataList = query.getResultList();
        if (transferOrderDataList != null && !transferOrderDataList.isEmpty()) {
            transferOrderData = transferOrderDataList.get(0);
        }
        return transferOrderData;
    }

    @Override
    public List<BulkTransferEventData> findBulkEventByDates(Integer unitId, Date startDate , Date endDate , Integer bulkEventId , Boolean isShort) {
        List<BulkTransferEventData> bulkTransferEventDataList = new ArrayList<>();
        StringBuilder queryString  =  new StringBuilder("Select Distinct E FROM BulkTransferEventData E ");
        if(!isShort){
            queryString.append("left join fetch E.transferOrderDataList ");
        }

        queryString.append("where E.generationUnitId = :unitId ");

        boolean isRangeGiven = Objects.nonNull(startDate) && Objects.nonNull(endDate);
        if(isRangeGiven){
            queryString.append(" and E.initiationTime >= :startDate and E.initiationTime <= :endDate ");
        }

        if(Objects.nonNull(bulkEventId)){
            queryString.append("and E.bulkTransferEventId = :bulkEventId");
        }

        queryString.append(" and E.status = :status");

        Query query = manager.createQuery(queryString.toString());

        query.setParameter("unitId",unitId);
        if(isRangeGiven){
            query.setParameter("startDate",startDate)
                    .setParameter("endDate",endDate);
        }
        if(Objects.nonNull(bulkEventId)){
            query.setParameter("bulkEventId",bulkEventId);
        }
        query.setParameter("status",SCMOrderStatus.TRANSFERRED.value());
        bulkTransferEventDataList = query.getResultList();
        if(bulkTransferEventDataList.isEmpty()){
            if(isRangeGiven){
                LOG.info("No Bulk Transfer Event Found Between {} and {} dates",startDate,endDate);
            }else {
                LOG.info("No Bulk Transfer Event Found With ID : {}",bulkEventId);
            }

        }
        return  bulkTransferEventDataList;
    }

    @Override
    public List<TransferOrderData> findByTransferIds(List<Integer> toIds ){
        List<TransferOrderData> transferOrderDataList = new ArrayList<>();
        if(toIds.isEmpty()){
            return transferOrderDataList;
        }
        StringBuilder queryString  =  new StringBuilder("Select Distinct t FROM TransferOrderData t left join fetch t.transferOrderItemDatas ");
        queryString.append("where t.id in :toIds ");
        Query query = manager.createQuery(queryString.toString());
        query.setParameter("toIds",toIds);
        transferOrderDataList = query.getResultList();
        if(transferOrderDataList.isEmpty()){
            LOG.info("No TO Found for {} ids",toIds);
        }
        return  transferOrderDataList;


    }

    @Override
    public TransferOrderData findTransfersByGeneratedId(String invoiceId){
        TransferOrderData transferOrderData = null;
        try{
            StringBuilder queryString  =  new StringBuilder("FROM TransferOrderData t ");
            queryString.append("where t.generatedInvoiceId = :invoiceId ");
            Query query = manager.createQuery(queryString.toString());
            query.setParameter("invoiceId",invoiceId);
            transferOrderData = (TransferOrderData) query.getSingleResult();
        }catch (Exception e ){
            LOG.error("Error While finding Transfer Order For Invoice Id : {} ::::", invoiceId,e);
        }
        return transferOrderData;


    }



    @Override
    public Map<String, List<TransferredAsset>> findAssetTransfersByUnitId(Integer unitId) {
        Map<String,List<TransferredAsset>> resultMap = new HashMap<>();
        List<TransferredAsset> resultOut = new ArrayList<>();
        List<TransferredAsset> resultSent = new ArrayList<>();
        try {
            Query query = manager.createNativeQuery("SELECT t.TRANSFER_ORDER_ID,toi.TRANSFER_ORDER_ITEM_ID,toi.SKU_ID,toi.SKU_NAME,toi.ASSOCIATED_ASSET_ID," +
                    "toi.ASSOCIATED_ASSET_TAG_VALUE,t.GENERATION_UNIT_ID,t.GENERATED_FOR_UNIT_ID FROM TRANSFER_ORDER t INNER JOIN TRANSFER_ORDER_ITEM toi ON t.TRANSFER_ORDER_ID = toi.TRANSFER_ORDER_ID " +
                    "WHERE t.GENERATION_UNIT_ID =:unitId AND t.TO_TYPE <>:toType AND t.TRANSFER_ORDER_STATUS <>:toStatus");
            query.setParameter("unitId",unitId);
            query.setParameter("toType", TransferOrderType.REGULAR_TRANSFER.value());
            query.setParameter("toStatus", SCMOrderStatus.CANCELLED.value());
            List<Object[]> queryResult = (List<Object[]>)query.getResultList();
            LOG.info("Size of Query 1 Result  is : {}",queryResult.size());
            convertTransferredAssets(resultOut, queryResult);
            List<Integer> unitIds = resultOut.stream().map(TransferredAsset::getGeneratedForUnitId).collect(Collectors.toList());
            List<String> assetTags = resultOut.stream().map(TransferredAsset::getAssetTag).collect(Collectors.toList());
            if (unitIds.size() > 0 && assetTags.size() > 0) {
                try {
                    Query query2 = manager.createNativeQuery("SELECT t.TRANSFER_ORDER_ID,toi.TRANSFER_ORDER_ITEM_ID,toi.SKU_ID,toi.SKU_NAME,toi.ASSOCIATED_ASSET_ID," +
                            "toi.ASSOCIATED_ASSET_TAG_VALUE,t.GENERATION_UNIT_ID,t.GENERATED_FOR_UNIT_ID FROM TRANSFER_ORDER t INNER JOIN TRANSFER_ORDER_ITEM toi ON t.TRANSFER_ORDER_ID = toi.TRANSFER_ORDER_ID " +
                            "AND t.TO_TYPE <>:toType AND t.TRANSFER_ORDER_STATUS <>:toStatus AND t.GENERATION_UNIT_ID IN(:unitIds)" +
                            " AND toi.ASSOCIATED_ASSET_TAG_VALUE IN(:assetTags)");
                    query2.setParameter("toType", TransferOrderType.REGULAR_TRANSFER.value());
                    query2.setParameter("toStatus", SCMOrderStatus.CANCELLED.value());
                    query2.setParameter("unitIds", unitIds);
                    query2.setParameter("assetTags", assetTags);
                    List<Object[]> query2ResultList = (List<Object[]>) query2.getResultList();
                    LOG.info("Size of Query 2 Result  is : {}", query2ResultList.size());
                    convertTransferredAssets(resultSent, query2ResultList);
                } catch (Exception e) {
                    LOG.error("Error Occurred While fetching the Transferred Out assets at 2nd Level ..! :: ", e);
                }
            }
        }
        catch (Exception e) {
            LOG.error("Exception Occurred While Fetching Asset Transfers :::  ",e);
        }
        resultMap.put("L1",resultOut);
        resultMap.put("L2",resultSent);
        return resultMap;
    }

    private void convertTransferredAssets(List<TransferredAsset> resultOut, List<Object[]> queryResult) {
        if (queryResult.size() > 0) {
            for (Object[] data : queryResult) {
                TransferredAsset entry = new TransferredAsset();
                entry.setToId((Integer) data[0]);
                entry.setToItemId((Integer) data[1]);
                entry.setSkuId((Integer) data[2]);
                entry.setSkuName((String) data[3]);
                entry.setAssetId((Integer) data[4]);
                entry.setAssetTag((String) data[5]);
                entry.setGeneratedUnitId((Integer) data[6]);
                entry.setGeneratedForUnitId((Integer) data[7]);
                resultOut.add(entry);
            }
        }
    }

    @Override
    public List<TransferOrderAssetCorrectionData> findAssetCorrectionDataByIds(List<Integer> toItemIds) {
        List<TransferOrderAssetCorrectionData> result = new ArrayList<>();
        try {
            Query query = manager.createQuery("FROM TransferOrderAssetCorrectionData t WHERE t.transferOrderItemId IN(:toItemIds) ORDER BY t.assetCorrectionId DESC");
            query.setParameter("toItemIds",toItemIds);
            result = (List<TransferOrderAssetCorrectionData>)query.getResultList();
        }
        catch (Exception e){
            LOG.error("Exception occurred while fetching asset correction data.. :: ",e);
        }
        return result;
    }

    @Override
    public List<GoodsReceivedData> findGRDataByToIds(List<Integer> toIds) {
        List<GoodsReceivedData> result = new ArrayList<>();
        try {
            Query query = manager.createQuery("FROM GoodsReceivedData gr WHERE gr.transferOrderData.id IN(:toIds)");
            query.setParameter("toIds",toIds);
            result = (List<GoodsReceivedData>)query.getResultList();
        }
        catch (Exception e){
            LOG.error("Exception occurred while fetching GR Data with toId's .. :: ",e);
        }
        return result;
    }

    @Override
    public List<Integer> filterGrData(List<Integer> grIds) {
        List<Integer> result = new ArrayList<>();
        try {
            Query query = manager.createQuery("SELECT gr.parentGR.id FROM GoodsReceivedData gr WHERE gr.parentGR.id IN(:grIds)");
            query.setParameter("grIds",grIds);
            result = (List<Integer>)query.getResultList();
        }
        catch (Exception e){
            LOG.error("Exception occurred while fetching GR Data with toId's .. :: ",e);
        }
        return result;
    }


    @Override
    public Optional<List<MultiPackagingAdjustmentsData>> getAdjustedPackaging(List<Integer> roIds) {
        List<Integer> result = new ArrayList<>();
        try {
            Query query = manager.createQuery(" FROM MultiPackagingAdjustmentsData pkg WHERE pkg.requestOrderItemData.id in :roIds ");
            query.setParameter("roIds",roIds);
            return Optional.ofNullable(query.getResultList());
        }
        catch (Exception e){
            LOG.error("Error Occured While Getting Adjusted Quantities ::::: ",e);
        }
        return null;
    }

    @Override
    public Boolean checkIfUnitIsExcludedForDayCloseCheck(Integer unitId) {
        try {
            Query query = manager.createQuery(" FROM ScmDayCloseCheckExclusionList s  WHERE s.unitId = :unitId and s.status = :status ");
            query.setParameter("unitId",unitId).setParameter("status", AppConstants.ACTIVE);
            ScmDayCloseCheckExclusionList s = (ScmDayCloseCheckExclusionList) query.getSingleResult();
            if(Objects.nonNull(s)){
                return true;
            }
            return false;
        } catch (Exception e){
            LOG.error("Error Occured While Checking If Unit is Excluded For Day Close Check ::::: ");
            return false;
        }
    }

    @Override
    public TransferOrderEInvoice findEInvoiceByTOID(Integer toId){
        try{
            Query query = manager.createQuery("from TransferOrderEInvoice where transferOrderId = :toId ");
            query.setParameter("toId",toId);
            return (TransferOrderEInvoice) query.getSingleResult();
        }catch (Exception e){
            return null;
        }
    }

}

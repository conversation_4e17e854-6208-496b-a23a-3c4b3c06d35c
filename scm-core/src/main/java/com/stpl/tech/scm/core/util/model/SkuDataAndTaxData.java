package com.stpl.tech.scm.core.util.model;

import com.stpl.tech.master.tax.model.TaxData;
import com.stpl.tech.scm.domain.model.SkuData;

import java.util.Map;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 15-05-2017.
 */
public class SkuDataAndTaxData {

    private Integer id;
    private SkuData skuData;
    private TaxData taxData;
    private Map<Integer,TaxData> packagingTaxMap;

    public SkuDataAndTaxData(){}

    public SkuDataAndTaxData(TaxData taxData, SkuData skuData , Map<Integer,TaxData> packagingTaxMap) {
        this.id = skuData.getId();
        this.skuData = skuData;
        this.taxData = taxData;
        this.packagingTaxMap = packagingTaxMap;
    }

    public SkuData getSkuData() {
        return skuData;
    }

    public void setSkuData(SkuData skuData) {
        this.skuData = skuData;
    }

    public TaxData getTaxData() {
        return taxData;
    }

    public void setTaxData(TaxData taxData) {
        this.taxData = taxData;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Map<Integer, TaxData> getPackagingTaxMap() {
        return packagingTaxMap;
    }

    public void setPackagingTaxMap(Map<Integer, TaxData> packagingTaxMap) {
        this.packagingTaxMap = packagingTaxMap;
    }
}

package com.stpl.tech.scm.data.model;

import com.stpl.tech.scm.data.enums.SchedulerStatus;
import com.stpl.tech.scm.data.enums.SchedulerType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

@Entity
@Table(name = "SCHEDULER_STATUS_DATA")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class SchedulerStatusData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    Long id;

    @Column(name = "SCHEDULER_KEY")
    String schedulerKey;

    @Column(name = "STATUS")
    @Enumerated(EnumType.STRING)
    SchedulerStatus status;

    @Column(name = "SCHEDULER_TYPE")
    @Enumerated(EnumType.STRING)
    SchedulerType schedulerType;

    @Column(name = "MESSAGE", columnDefinition = "TEXT")
    String message;

    @CreatedDate
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "TIMESTAMP")
    Date timeStamp;

    public SchedulerStatusData(String schedulerKey, SchedulerStatus schedulerStatus, SchedulerType schedulerType) {
        this.schedulerKey = schedulerKey;
        this.status = schedulerStatus;
        this.schedulerType = schedulerType;
    }

}

package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.data.model.LdcVendorData;
import com.stpl.tech.scm.data.model.VendorDetailData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LdcVendorDao extends JpaRepository<LdcVendorData,Long> {
     List<LdcVendorData> findByVendorDetailDataAndStatus(VendorDetailData vendorDetailData,String status);

}

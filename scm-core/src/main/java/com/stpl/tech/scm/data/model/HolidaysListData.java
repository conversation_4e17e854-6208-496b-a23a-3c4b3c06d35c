package com.stpl.tech.scm.data.model;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "HOLIDAYS_LIST_DATA")
public class HolidaysListData {

    private Integer holidayListId;
    private String holidayType;
    private Date holidayDate;
    private Integer holidayYear;
    private Integer holidayMonth;
    private String status;
    private String createdBy;
    private Date createdTime;
    private String updatedBy;
    private Date updatedAt;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "HOLIDAY_LIST_ID", nullable = false, unique = true)
    public Integer getHolidayListId() {
        return holidayListId;
    }

    public void setHolidayListId(Integer holidayListId) {
        this.holidayListId = holidayListId;
    }

    @Column(name = "HOLIDAY_TYPE", nullable = false, length = 20)
    public String getHolidayType() {
        return holidayType;
    }

    public void setHolidayType(String holidayType) {
        this.holidayType = holidayType;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "HOLIDAY_DATE", nullable = false)
    public Date getHolidayDate() {
        return holidayDate;
    }

    public void setHolidayDate(Date holidayDate) {
        this.holidayDate = holidayDate;
    }

    @Column(name = "HOLIDAY_YEAR", nullable = false)
    public Integer getHolidayYear() {
        return holidayYear;
    }

    public void setHolidayYear(Integer holidayYear) {
        this.holidayYear = holidayYear;
    }

    @Column(name = "HOLIDAY_MONTH", nullable = false)
    public Integer getHolidayMonth() {
        return holidayMonth;
    }

    public void setHolidayMonth(Integer holidayMonth) {
        this.holidayMonth = holidayMonth;
    }

    @Column(name = "STATUS", nullable = false, length = 15)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "CREATED_BY", nullable = false, length = 50)
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATED_TIME", nullable = true)
    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    @Column(name = "UPDATED_BY", length = 50)
    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "UPDATED_AT", nullable = true, length = 19)
    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
}

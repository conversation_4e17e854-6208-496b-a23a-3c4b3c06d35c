package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import java.util.Date;

@Entity
@Table(name = "OBSOLETE_COST_DETAIL_DATA_CAFE", uniqueConstraints = @UniqueConstraint(columnNames = { "KEY_ID", "KEY_TYPE",
        "UNIT_ID" }))
public class ObsoleteCostDetailDataCafe extends CostDetailData{

    private Date deletedTime;
    private Integer dayCloseEventId;

    @Column(name = "DELETED_TIME", nullable = true)
    public Date getDeletedTime() {
        return deletedTime;
    }

    public void setDeletedTime(Date deletedTime) {
        this.deletedTime = deletedTime;
    }

    @Column(name = "DAY_CLOSE_EVENT_ID", nullable = true)
    public Integer getDayCloseEventId() {
        return dayCloseEventId;
    }

    public void setDayCloseEventId(Integer dayCloseEventId) {
        this.dayCloseEventId = dayCloseEventId;
    }
}

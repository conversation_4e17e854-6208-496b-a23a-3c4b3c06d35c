package com.stpl.tech.scm.core.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.scm.data.model.AdditionalDocumentsMaster;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.scm.core.service.ServiceMappingManagementService;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.ServiceMappingManagementDao;
import com.stpl.tech.scm.data.model.CostElementData;
import com.stpl.tech.scm.domain.model.CostElement;
import com.stpl.tech.scm.domain.model.CostElementPriceUpdate;
import com.stpl.tech.scm.domain.model.IdCodeNameStatus;

@Service
public class ServiceMappingManagementServiceImpl implements ServiceMappingManagementService {
	
	private static final Logger LOG = LoggerFactory.getLogger(SCMVendorManagementServiceImpl.class);
	
	@Autowired
	ServiceMappingManagementDao dao;
	@Autowired
	private MasterDataCache masterCache;
	
	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<CostElement> getCostElementData() {
		List<CostElementData> costElementData = dao.getCostElement();
		List<CostElement> costElementNew = new ArrayList<CostElement>();
		for(CostElementData costElementDataObj : costElementData) {
			CostElement costElement = SCMDataConverter.convert(costElementDataObj);
			costElementNew.add(costElement);
		}
		return costElementNew;
	}

	@Override
	public List<IdCodeNameStatus> searchCostELementVendorMappings(Integer costElementId) {
		return dao.getCostELementVendorMappings(costElementId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean addCostELementVendorMapping(int employeeId, String employeeName, int costElementId, List<Integer> vendorIds) {
		return dao.addCostElementVendorMappings(employeeId, employeeName, costElementId, vendorIds);
	}

	@Override
	public List<IdCodeNameStatus> allVendors() {
		return dao.getAllVendorList();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updateCostElementVendorMapping(int employeeId, String employeeName, int vendorId, int costElementId,
			String status) {
		return dao.updateCostElementVendorMapping(employeeId, employeeName, vendorId, costElementId, status);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean addVendorCostElementMapping(int employeeId, String employeeName, int vendorId, List<Integer> costElementIds) {
		return dao.addVendorCostElementMappings(employeeId, employeeName, vendorId, costElementIds);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<IdCodeNameStatus> searchVendorToCostElementMappings(Integer vendorId) {
		return dao.getVendorCostElementMappings(vendorId);
	}

	@Override
	public List<IdCodeNameStatus> allCostCentre() {
		return dao.getAllCostCentres();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean addCostElementCostCenterMapping(int employeeId, String employeeName, int costElementId,
			List<Integer> costCentersId) {
		return dao.addCostElementCostCenterMappings(employeeId, employeeName, costElementId, costCentersId);
	}

	@Override
	public List<IdCodeNameStatus> searchCostELementCostCenterMappings(Integer costElementId) {
		return dao.searchCostELementCostCenterMapping(costElementId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updateCostElementCostCenterMapping(int employeeId, String employeeName, int costElementId,
			int costCenterId, String status) {
		return dao.updateCostElementCostCenterMappings(employeeId, employeeName, costElementId,
				costCenterId, status);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean addCostCenterToCostElementMapping(int employeeId, String employeeName, int costCenterId,
			List<Integer> costElementIds) {
		return dao.addCostCenterToCostElementMappings(employeeId, employeeName, costCenterId, costElementIds);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<IdCodeNameStatus> searchCostCenterToCostElementMappings(Integer costCenterId) {
		return dao.searchCostCenterToCostElementMappings(costCenterId);
	}

	@Override
	public List<CostElementPriceUpdate> getPricedCostElements(Integer vendorId, Integer costCenterId) {
		return dao.getPricedCostElements(vendorId, costCenterId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updateStatusCostElementPriceMapping(int employeeId, String employeeName, int costElementId,
			String status) {
		return dao.updateStatusCostElementPriceMappings(employeeId, employeeName, costElementId, status);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean addCostElementPriceMapping(Integer vendorId, Integer costCenterId,
			Integer costElementId, BigDecimal price, Integer employeeId, String employeeName) {
		return dao.addCostElementPriceMapping(vendorId, costCenterId,costElementId,price,employeeId,employeeName);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updateCostElementPriceMapping(Integer costElementMappingId, BigDecimal price, Integer employeeId,
			String employeeName) {
		return dao.updateCostElementPriceMappings(costElementMappingId,price,employeeId,employeeName);
	}

    @Override
    public List<CostElementPriceUpdate> getClonePriceForCostElementId(String costElementIds, Integer vendorId, Integer costCenterId) {
        String[] list = costElementIds.split(",");
		List<Integer> costElementIdList = new ArrayList<>();
		for(String id : list){
			costElementIdList.add(Integer.parseInt(id));
		}
		List<CostElementPriceUpdate> costElementPriceUpdatesAll = dao.getPricedCostElements(vendorId,costCenterId);
		List<CostElementPriceUpdate> costElementPriceUpdatesForCostElementId = new ArrayList<>();
		for(CostElementPriceUpdate costElementPriceUpdate : costElementPriceUpdatesAll){
			if(AppConstants.ACTIVE.equals(costElementPriceUpdate.getStatus()) && costElementIdList.contains(costElementPriceUpdate.getId())){
				costElementPriceUpdatesForCostElementId.add(costElementPriceUpdate);
			}
		}
		return costElementPriceUpdatesForCostElementId;
    }

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean addMasterDocument(String documentName,Integer createdBy) throws DataUpdationException {
		AdditionalDocumentsMaster additionalDocumentsMaster = AdditionalDocumentsMaster.builder()
				.documentName(documentName).documentCode(getDocumentCode(documentName)).createdDate(AppUtils.getCurrentDate())
				.status(AppConstants.ACTIVE).lastUpdatedBy(createdBy).lastUpdatedDate(AppUtils.getCurrentDate())
				.createdBy(masterCache.getEmployeeBasicDetail(createdBy).getName()+"["+createdBy +"]")
				.build();
        AdditionalDocumentsMaster addedDocument = dao.addMasterDocument(additionalDocumentsMaster);
		if(Objects.nonNull(addedDocument)){
			return true;
		}
		return false;
	}



	private String getDocumentCode(String documentName){
		String documentCode = documentName.replaceAll(" ","_");
		return documentCode;
	}


	public List<IdCodeName> searchCostElementDocumentMappings(Integer costElementId) {
		return dao.getCostElementDocumentMappings(costElementId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean addCostElementDocumentMapping(int employeeId, String employeeName, int costElementId, List<Integer> documentIds) {
		return dao.addCostElementDocumentMappings(employeeId, employeeName, costElementId, documentIds);
	}

	public List<AdditionalDocumentsMaster> getDocumentList(){
		return dao.getDocumentList();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updateCostElementDocumentMapping(int documentId, int costElementId) {
		return dao.updateCostElementDocumentMapping(documentId, costElementId);
	}

	public List<AdditionalDocumentsMaster> getAdditionalDocs(){
		return dao.getAdditionalDocs();
	}
}

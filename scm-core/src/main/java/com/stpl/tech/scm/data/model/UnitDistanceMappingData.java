package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "UNIT_DISTANCE_MAPPING")
public class UnitDistanceMappingData {

	private Integer mappingId;
	private int sourceUnitId;
	private int destinationUnitId;
	private BigDecimal distance;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "MAPPING_ID", unique = true, nullable = false)
	public Integer getMappingId() {
		return mappingId;
	}

	public void setMappingId(Integer mappingId) {
		this.mappingId = mappingId;
	}

	@Column(name = "SOURCE_ID", nullable = false)
	public int getSourceUnitId() {
		return sourceUnitId;
	}

	public void setSourceUnitId(int sourceUnitId) {
		this.sourceUnitId = sourceUnitId;
	}

	@Column(name = "DESTINATION_ID", nullable = false)
	public int getDestinationUnitId() {
		return destinationUnitId;
	}

	public void setDestinationUnitId(int destinationUnitId) {
		this.destinationUnitId = destinationUnitId;
	}

	@Column(name = "DISTANCE", nullable = false)
	public BigDecimal getDistance() {
		return distance;
	}

	public void setDistance(BigDecimal distance) {
		this.distance = distance;
	}

}

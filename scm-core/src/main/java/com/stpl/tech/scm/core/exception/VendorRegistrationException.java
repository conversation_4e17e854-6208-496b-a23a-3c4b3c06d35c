package com.stpl.tech.scm.core.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 24-04-2017.
 */
@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR, reason = "Exception in Vendor Request")
public class VendorRegistrationException extends Exception {
    private static final long serialVersionUID = 616315625637405710L;

    public VendorRegistrationException() {
    }

    public VendorRegistrationException(String message) {
        super(message);
    }

    public VendorRegistrationException(Throwable cause) {
        super(cause);
    }

    public VendorRegistrationException(String message, Throwable cause) {
        super(message, cause);
    }

    public VendorRegistrationException(String message, Throwable cause, boolean enableSuppression,
                                  boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}

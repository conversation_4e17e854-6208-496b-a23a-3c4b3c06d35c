package com.stpl.tech.scm.data.converter;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.model.CommonLogsData;
import com.stpl.tech.scm.data.model.GatepassData;
import com.stpl.tech.scm.data.model.RegionProductPackagingMappingData;
import com.stpl.tech.scm.domain.model.Gatepass;
import com.stpl.tech.scm.domain.model.GatepassStatus;
import com.stpl.tech.scm.domain.model.LogType;
import com.stpl.tech.scm.domain.model.RegionProductPackagingMapping;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.domain.RequestContext;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Objects;

@Slf4j
public class DtoToEntityConverter {

    public static RegionProductPackagingMappingData convertToEntity(RegionProductPackagingMapping mapping) {
        RegionProductPackagingMappingData regionProductPackagingMappingData = new RegionProductPackagingMappingData();
        regionProductPackagingMappingData.setRegionCode( mapping.getRegionCode() );
        regionProductPackagingMappingData.setProductId( mapping.getProductId() );
        regionProductPackagingMappingData.setPackagingId( mapping.getPackagingId() );
        regionProductPackagingMappingData.setStatus( AppConstants.ACTIVE );
        regionProductPackagingMappingData.setCreatedBy( RequestContext.getContext().getLoggedInUserId() );
        return regionProductPackagingMappingData;
    }

    public static CommonLogsData createEntity(String fromState, String toState, Integer logTypeId, LogType logType, String logMsg) {
        CommonLogsData contractStatusLog = new CommonLogsData();
        contractStatusLog.setFromState(fromState);
        contractStatusLog.setToState(toState);
        Integer updatedBy = RequestContext.getContext().getLoggedInUserId();
        if(Objects.isNull(updatedBy) || updatedBy <= 0) {
            updatedBy = AppConstants.SYSTEM_EMPLOYEE_ID;
        }
        contractStatusLog.setUpdatedBy(updatedBy);
        contractStatusLog.setLogType(logType);
        contractStatusLog.setLogTypeId(logTypeId);
        contractStatusLog.setLogMessage(logMsg);
        return contractStatusLog;
    }

    public static GatepassData convertToEntity(Gatepass gatepass) {
        GatepassData gatepassData = new GatepassData();
        gatepassData.setVendorId(gatepass.getVendor().getId());
        gatepassData.setOperationType(gatepass.getOperationType().name());
        gatepassData.setReturnable(SCMUtil.setStatus(gatepass.getReturnable()));
        gatepassData.setComment(gatepass.getComment());
        gatepassData.setReason(gatepass.getReason());
        gatepassData.setCreatedBy(gatepass.getCreatedBy().getId());
        gatepassData.setCreatedAt(AppUtils.getCurrentTimestamp());
        gatepassData.setSendingUnit(gatepass.getSendingUnit().getId());
        gatepassData.setNeedsApproval(AppUtils.NO);
        gatepassData.setIssueDate(gatepass.getIssueDate());
        gatepassData.setDispatchLocationId(gatepass.getDispatchLocation().getId());
        gatepassData.setAssetGatePass(SCMUtil.setStatus(gatepass.getAssetGatePass()));
        gatepassData.setStatus(GatepassStatus.INITIATED.name());
        gatepassData.setTotalCost(BigDecimal.ZERO);
        gatepassData.setTotalTax(BigDecimal.ZERO);
        gatepassData.setApprovalRequestedTo(gatepass.getApprovalRequestedTo().getId());

        if (gatepass.getExpectedReturn() != null) {
            gatepassData.setExpectedReturn(gatepass.getExpectedReturn());
        }
        if (gatepass.getAdditionalCharges() != null) {
            gatepassData.setAdditionalCharges(gatepass.getAdditionalCharges());
        }

        return gatepassData;
    }


}

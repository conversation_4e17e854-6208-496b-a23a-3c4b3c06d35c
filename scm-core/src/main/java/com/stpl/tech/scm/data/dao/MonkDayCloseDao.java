package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.data.model.MonkDayCloseEventStatusData;
import com.stpl.tech.scm.data.model.MonkStatusDayCloseData;
import com.stpl.tech.scm.data.model.MonkStatusDayCloseHistoryData;

import java.util.Date;
import java.util.List;

public interface MonkDayCloseDao extends SCMAbstractDao {
    
    MonkDayCloseEventStatusData createMonkDayCloseEvent(Integer unitId, Date businessDate, String eventType, String eventStatus);
    
    MonkDayCloseEventStatusData getMonkDayCloseEvent(Integer unitId, Date businessDate);
    
    List<MonkStatusDayCloseData> getMonkStatusesByEventId(Long eventStatusId);
    
    MonkStatusDayCloseData createMonkStatus(Long eventStatusId, String monkName, String monkStatus, Integer updatedBy);
    
    MonkStatusDayCloseData updateMonkStatus(Long monkStatusDayCloseId, String monkStatus, Integer updatedBy);
    
    MonkDayCloseEventStatusData updateEventStatus(Long monkDayCloseEventStatusId, String eventStatus);
    
    List<MonkDayCloseEventStatusData> getPendingMonkDayCloseEvents();
    
    MonkDayCloseEventStatusData getLatestMonkDayCloseEventByUnit(Integer unitId);
    
    List<MonkStatusDayCloseData> getLatestMonkStatusesByUnit(Integer unitId, Integer limit);
    
    Integer getRequiredMonksCount(Integer unitId);
    
    void linkKettleDayClose(Long eventId, Integer kettleDayCloseId);
    
    void linkSumoDayClose(Long eventId, Integer sumoDayCloseId);
    
    // History operations
    MonkStatusDayCloseHistoryData createMonkStatusHistory(Long monkStatusDayCloseId, String monkStatus, String comment,Integer updatedBy);
    
    List<MonkStatusDayCloseHistoryData> getMonkStatusHistory(Long monkStatusDayCloseId);
    
    MonkStatusDayCloseData updateMonkStatusWithHistory(Long monkStatusDayCloseId, String monkStatus, String comment,Integer updatedBy);
}

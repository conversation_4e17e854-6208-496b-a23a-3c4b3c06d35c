package com.stpl.tech.scm.data.model;

import com.stpl.tech.master.domain.model.SwitchStatus;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 19-04-2017.
 */
@Entity
@Table(name = "VENDOR_DISPATCH_LOCATIONS")
public class VendorDispatchLocationDetailData {

    private Integer dispatchLocationId;
    private VendorDetailData vendorDetail;
    private String applyTax;
    private String locationName;
    private String notificationType;
    private String emailId;
    private AddressDetailData locationAddress;
    private Integer updatedBy;
    private Date updatedAt;
    private String TIN;
    private String GSTIN;
    private String gstStatus;
    private DocumentDetailData gstinDocument;
    private String status;
    private String locationType;


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "DISPATCH_LOCATION_ID")
    public Integer getDispatchLocationId() {
        return dispatchLocationId;
    }

    public void setDispatchLocationId(Integer dispatchLocationId) {
        this.dispatchLocationId = dispatchLocationId;
    }

    @ManyToOne
    @JoinColumn(name = "VENDOR_ID", nullable = false)
    public VendorDetailData getVendorDetail() {
        return vendorDetail;
    }

    public void setVendorDetail(VendorDetailData vendorDetails) {
        this.vendorDetail = vendorDetails;
    }

    @Column(name = "APPLY_TAX", nullable = false)
    public String getApplyTax() {
        return applyTax;
    }

    public void setApplyTax(String applyTax) {
        this.applyTax = applyTax;
    }

    @Column(name = "LOCATION_NAME", nullable = false)
    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    @Column(name = "NOTIFICATION_TYPE", nullable = false)
    public String getNotificationType() {
        return notificationType;
    }

    public void setNotificationType(String notificationType) {
        this.notificationType = notificationType;
    }

    @Column(name = "UPDATED_BY", nullable = false)
    public Integer getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Column(name = "UPDATED_AT", nullable = false)
    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Column(name = "TIN", nullable = true, unique = true)
    public String getTIN() {
        return TIN;
    }

    public void setTIN(String TIN) {
        this.TIN = TIN;
    }

    @Column(name = "GSTIN", nullable = true, unique = true)
    public String getGSTIN() {
        return GSTIN;
    }

    public void setGSTIN(String GSTIN) {
        this.GSTIN = GSTIN;
    }

    @Column(name = "GST_STATUS", nullable = false)
    public String getGstStatus() {
        return gstStatus;
    }

    public void setGstStatus(String gstStatus) {
        this.gstStatus = gstStatus;
    }

    @Column(name = "CONTACT_EMAIL", nullable = false)
    public String getEmailId() {
        return emailId;
    }

    public void setEmailId(String emailId) {
        this.emailId = emailId;
    }

    @Column(name = "LOCATION_STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @OneToOne
    @JoinColumn(name = "LOCATION_ADDRESS_ID", nullable = true)
    public AddressDetailData getLocationAddress() {
        return locationAddress;
    }

    public void setLocationAddress(AddressDetailData locationAddress) {
        this.locationAddress = locationAddress;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "GSTIN_DOCUMENT", nullable = true)
	public DocumentDetailData getGstinDocument() {
		return gstinDocument;
	}

	public void setGstinDocument(DocumentDetailData gstinDocument) {
		this.gstinDocument = gstinDocument;
	}


    @Column(name = "LOCATION_TYPE", nullable = false)
    public String getLocationType() {
        return locationType;
    }

    public void setLocationType(String locationType) {
        this.locationType = locationType;
    }
}

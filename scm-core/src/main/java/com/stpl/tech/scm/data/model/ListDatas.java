package com.stpl.tech.scm.data.model;

import java.util.Collection;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;

/**
 * Copyright (C) ,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by Vishal on 23-01-2020.
 */

@Entity
@Table(name = "LIST_DATA")
public class ListDatas {
	
	private Integer listDataId;
	private String code;
	private String name;
	private String description;
	private String status;
	private String alias;
	private ListTypeData listType;
	private Collection<CostElementData> costElementDataSubSubCategory;
	
	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "LIST_DATA_ID", unique = true, nullable = false)
	public Integer getId() {
		return listDataId;
	}
	public void setId(Integer listDataId) {
		this.listDataId = listDataId;
	}
	
	@Column(name = "CODE", nullable = false)
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	
	@Column(name = "NAME", nullable = false)
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	
	@Column(name = "DESCRIPTION", nullable = false)
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	
	@Column(name = "STATUS", nullable = false)
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	
	@Column(name = "ALIAS", nullable = false)
	public String getAlias() {
		return alias;
	}
	public void setAlias(String alias) {
		this.alias = alias;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "LIST_TYPE_ID", nullable = true)
	public ListTypeData getListType() {
		return listType;
	}
	public void setListType(ListTypeData listType) {
		this.listType = listType;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "subSubCategory")
	public Collection<CostElementData> getCostElementDataSubSubCategory() {
		return costElementDataSubSubCategory;
	}

	public void setCostElementDataSubSubCategory(Collection<CostElementData> costElementDataSubSubCategory) {
		this.costElementDataSubSubCategory = costElementDataSubSubCategory;
	}
	
}

package com.stpl.tech.scm.core.service;

import com.stpl.tech.scm.data.model.MonkWastageDetailData;
import com.stpl.tech.scm.domain.model.MonkWastageDetailDto;

import java.util.List;
import java.util.Map;

public interface MonkWastageManagementService {
    
    /**
     * Save monk wastage detail data from DTO
     * @param wastageDetailDto the wastage detail DTO
     * @param unitId
     * @return saved wastage detail DTO
     */
    void saveMonkWastageDetail(MonkWastageDetailDto wastageDetailDto, Integer unitId);
    
    /**
     * Process unprocessed monk wastage details for a specific unit with optional threshold bypass
     * @param unitId the unit ID to process, or null to process all units
     * @param bypassThreshold if true, bypasses the quantity threshold check
     */
    void processUnprocessedWastageDetailsForUnit(Integer unitId, boolean bypassThreshold);
    
    /**
     * Get unprocessed monk wastage details for a specific unit
     * @param unitId the unit ID
     * @return list of unprocessed wastage details
     */
    List<MonkWastageDetailData> getUnprocessedWastageDetails(Integer unitId);
    
    /**
     * Get all unprocessed monk wastage details grouped by unit
     * @return map of unit ID to list of unprocessed wastage details
     */
    Map<Integer, List<MonkWastageDetailData>> getAllUnprocessedWastageDetailsGroupedByUnit();
    
    /**
     * Get unprocessed monk wastage details grouped by unit with optional unit filter
     * @param unitId the unit ID to filter by, or null to get all units
     * @return map of unit ID to list of unprocessed wastage details
     */
    Map<Integer, List<MonkWastageDetailData>> getUnprocessedWastageDetailsGroupedByUnit(Integer unitId);
} 
package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;
import java.util.Set;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * Entity for KETTLE_STOCK_OUT_DATE_WISE_DATA table
 */
@Setter
@Getter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "KETTLE_STOCK_OUT_DATE_WISE_DATA")
public class KettleStockOutDateWiseData {

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "KETTLE_STOCK_OUT_DATE_WISE_DATA_ID", unique = true, nullable = false)
    private Integer kettleStockOutDateWiseDataId;

    @Column(name = "UNIT_ID", nullable = false)
    private Integer unitId;

    @Temporal(TemporalType.DATE)
    @Column(name = "BUSINESS_DATE", nullable = false)
    private Date businessDate;

    @Column(name = "KETTLE_PRODUCT_ID", nullable = false)
    private Integer kettleProductId;

    @Column(name = "DIMENSION", length = 255)
    private String dimension;

    @Column(name = "BRAND_ID")
    private Integer brandId;

    @Column(name = "INVENTORY_TRACK_LEVEL")
    private String inventoryTrackLevel;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATED_AT", nullable = false)
    private Date createdAt;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_UPDATED_AT", nullable = false)
    private Date lastUpdatedAt;

    @Column(name = "BUSINESS_DATE_WISE_STOCK_OUT_TIME_IN_MIN")
    private Long businessDateWiseStockOutTimeInMin;

    @OneToMany(mappedBy = "kettleStockOutDateWiseDataId", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<KettleStockOutTimingsData> kettleStockOutTimingsDataSet;

}

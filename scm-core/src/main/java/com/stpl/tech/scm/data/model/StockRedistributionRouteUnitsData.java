package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@Table(name = "STOCK_REDISTRIBUTION_ROUTE_UNITS_DATA")
@AllArgsConstructor
@NoArgsConstructor
public class StockRedistributionRouteUnitsData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "STOCK_REDISTRIBUTION_ROUTE_UNIT_DATA_ID")
    private Integer stockRedistributionRouteUnitDataId;

    @ManyToOne(targetEntity = StockRedistributionRouteInfoData.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "STOCK_REDISTRIBUTION_ROUTE_INFO_DATA_ID", nullable = false)
    private StockRedistributionRouteInfoData stockRedistributionRouteInfoData;

    @Column(name = "UNIT_ID", nullable = false)
    private Integer unitId;

    @Column(name = "UNIT_STATUS", nullable = false)
    private String unitStatus;
}

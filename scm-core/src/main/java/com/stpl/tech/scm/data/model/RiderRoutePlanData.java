package com.stpl.tech.scm.data.model;

import com.stpl.tech.scm.domain.model.RiderRoutePlanStatusEnum;
import com.stpl.tech.scm.domain.model.RiderRouteTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;
import java.util.Set;

@Entity
@Getter
@Setter
@Table(name = "RIDER_ROUTE_PLAN_DATA")
@AllArgsConstructor
@NoArgsConstructor
public class RiderRoutePlanData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "RIDER_ROUTE_PLAN_DATA_ID")
    private Integer riderRoutePlanDataId;

    @OneToOne(targetEntity = RiderInfoData.class)
    @JoinColumn(name = "RIDER_ID", nullable = false)
    private RiderInfoData riderInfoData;

    @OneToOne(targetEntity = StockRedistributionRouteInfoData.class)
    @JoinColumn(name = "ROUTE_ID", nullable = false)
    private StockRedistributionRouteInfoData stockRedistributionRouteInfoData;

    @Enumerated(EnumType.STRING)
    @Column(name = "RIDER_ROUTE_PLAN_STATUS", nullable = false)
    private RiderRoutePlanStatusEnum riderRoutePlanStatus;

    @Enumerated(EnumType.STRING)
    @Column(name = "RIDER_ROUTE_TYPE", nullable = false)
    private RiderRouteTypeEnum riderRouteType;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_TIME")
    private Date creationTime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "RIDE_START_TIME")
    private Date rideStartTime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "RIDE_END_TIME")
    private Date rideEndTime;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "riderRoutePlanData")
    private Set<RiderRoutePlanStepData> riderRoutePlanStepDataSet;

}

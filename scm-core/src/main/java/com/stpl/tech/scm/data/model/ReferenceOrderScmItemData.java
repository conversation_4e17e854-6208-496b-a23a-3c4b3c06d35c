package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * Created by <PERSON><PERSON> on 10-06-2016.
 */
@Entity
@Table(name = "REFERENCE_ORDER_SCM_ITEM")
public class ReferenceOrderScmItemData {

    private Integer id;
    private int productId;
    private String productName;
    private BigDecimal requestedQuantity;
    private BigDecimal requestedAbsoluteQuantity;
    private BigDecimal transferredQuantity;
    private BigDecimal receivedQuantity;
    private String fulfillmentType;
    private String unitOfMeasure;
    private ReferenceOrderData referenceOrderData;
    private BigDecimal suggestedQuantity;
    private BigDecimal predictedQuantity;
    private BigDecimal suggestedQuantityBeforeMoq;
    private List<RoScmItemExpiryData> roScmItemExpiryData;
    private String reason;


    protected BigDecimal consumption;
    protected BigDecimal stockOutPercentage;
    protected  BigDecimal stockOutRaw;
    protected BigDecimal wastagePercentage;

    protected BigDecimal wastageRaw;

    protected BigDecimal adjustedQuantity;

    protected BigDecimal totalCafeHours;


    protected String comments;
    private String expiryUsageLogs;
    private Integer packagingId;


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "SCM_ITEM_ID", nullable = false, unique = true)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "PRODUCT_ID", nullable = false)
    public int getProductId() {
        return productId;
    }

    public void setProductId(int productId) {
        this.productId = productId;
    }

    @Column(name = "PRODUCT_NAME", nullable = false)
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @Column(name = "REQUESTED_QUANTITY", nullable = false)
    public BigDecimal getRequestedQuantity() {
        return requestedQuantity;
    }

    public void setRequestedQuantity(BigDecimal requestedQuantity) {
        this.requestedQuantity = requestedQuantity;
    }

    @Column(name = "REQUESTED_ABSOLUTE_QUANTITY", nullable = false)
    public BigDecimal getRequestedAbsoluteQuantity() {
        return requestedAbsoluteQuantity;
    }

    public void setRequestedAbsoluteQuantity(BigDecimal requestedAbsoluteQuantity) {
        this.requestedAbsoluteQuantity = requestedAbsoluteQuantity;
    }

    @Column(name = "TRANSFERRED_QUANTITY", nullable = true)
    public BigDecimal getTransferredQuantity() {
        return transferredQuantity;
    }

    public void setTransferredQuantity(BigDecimal transferredQuantity) {
        this.transferredQuantity = transferredQuantity;
    }

    @Column(name = "FULFILLMENT_TYPE", nullable = true)
    public String getFulfillmentType() {
        return fulfillmentType;
    }

    public void setFulfillmentType(String fulfillmentType) {
        this.fulfillmentType = fulfillmentType;
    }

    @Column(name = "RECEIVED_QUANTITY", nullable = true)
    public BigDecimal getReceivedQuantity() {
        return receivedQuantity;
    }

    public void setReceivedQuantity(BigDecimal receivedQuantity) {
        this.receivedQuantity = receivedQuantity;
    }

    @Column(name = "UNIT_OF_MEASURE", nullable = false, length = 10)
    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "REFERENCE_ORDER_ID", nullable = false)
    public ReferenceOrderData getReferenceOrderData() {
        return referenceOrderData;
    }

    public void setReferenceOrderData(ReferenceOrderData referenceOrderData) {
        this.referenceOrderData = referenceOrderData;
    }

    @Column(name = "SUGGESTED_QUANTITY", nullable = false)
    public BigDecimal getSuggestedQuantity() {
        return suggestedQuantity;
    }

    public void setSuggestedQuantity(BigDecimal suggestedQuantity) {
        this.suggestedQuantity = suggestedQuantity;
    }

    @Column(name = "PREDICTED_QUANTITY", nullable = false)
    public BigDecimal getPredictedQuantity() {
        return predictedQuantity;
    }

    public void setPredictedQuantity(BigDecimal predictedQuantity) {
        this.predictedQuantity = predictedQuantity;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "scmItemId")
    public List<RoScmItemExpiryData> getRoScmItemExpiryData() {
        return roScmItemExpiryData;
    }

    public void setRoScmItemExpiryData(List<RoScmItemExpiryData> roScmItemExpiryData) {
        this.roScmItemExpiryData = roScmItemExpiryData;
    }

    @Column(name = "REASON", nullable = true)
    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }


    @Column(name = "STOCK_OUT_PERCENTAGE", nullable = true)
    public BigDecimal getStockOutPercentage() {
        return stockOutPercentage;
    }



    public void setStockOutPercentage(BigDecimal stockOutPercentage) {
        this.stockOutPercentage = stockOutPercentage;
    }

    @Column(name = "STOCK_OUT_RAW", nullable = true)
    public BigDecimal getStockOutRaw() {
        return stockOutRaw;
    }

    public void setStockOutRaw(BigDecimal stockOutRaw) {
        this.stockOutRaw = stockOutRaw;
    }


    @Column(name = "WASTAGE_PERCENTAGE", nullable = true)
    public BigDecimal getWastagePercentage() {
        return wastagePercentage;
    }

    public void setWastagePercentage(BigDecimal wastagePercentage) {
        this.wastagePercentage = wastagePercentage;
    }

    @Column(name = "WASTAGE_RAW", nullable = true)
    public BigDecimal getWastageRaw() {
        return wastageRaw;
    }

    public void setWastageRaw(BigDecimal wastageRaw) {
        this.wastageRaw = wastageRaw;
    }


    @Column(name = "ADJUSTED_QUANTITY", nullable = true)
    public BigDecimal getAdjustedQuantity() {
        return adjustedQuantity;
    }

    public void setAdjustedQuantity(BigDecimal adjustedQuantity) {
        this.adjustedQuantity = adjustedQuantity;
    }


    @Column(name = "COMMENTS", nullable = true)
    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }


    @Column(name = "CONSUMPTION")
    public BigDecimal getConsumption() {
        return consumption;
    }

    public void setConsumption(BigDecimal consumption) {
        this.consumption = consumption;
    }


    @Column(name = "TOTAL_CAFE_HOURS")
    public BigDecimal getTotalCafeHours() {
        return totalCafeHours;
    }

    public void setTotalCafeHours(BigDecimal totalCafeHours) {
        this.totalCafeHours = totalCafeHours;
    }

    @Column(name = "SUGGESTED_QUANTITY_BEFORE_MOQ", nullable = true)
    public BigDecimal getSuggestedQuantityBeforeMoq() {
        return suggestedQuantityBeforeMoq;
    }

    public void setSuggestedQuantityBeforeMoq(BigDecimal suggestedQuantityBeforeMoq) {
        this.suggestedQuantityBeforeMoq = suggestedQuantityBeforeMoq;
    }

    @Column(name = "EXPIRY_USAGE_LOGS", columnDefinition = "TEXT")
    public String getExpiryUsageLogs() {
        return expiryUsageLogs;
    }

    public void setExpiryUsageLogs(String expiryUsageLogs) {
        this.expiryUsageLogs = expiryUsageLogs;
    }

    @Transient
    public Integer getPackagingId() {
        return packagingId;
    }

    public void setPackagingId(Integer packagingId) {
        this.packagingId = packagingId;
    }
}
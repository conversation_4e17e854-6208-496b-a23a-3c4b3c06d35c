/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@SuppressWarnings("serial")
@Entity
@Table(name = "COSTELEMENT_COSTCENTER_MAPPING")
public class CostElementCostCenterMapping implements java.io.Serializable {

	private Integer costElementCostCenterMappingId;
	private int costElementId;
	private int costCenterId;
	private String mappingStatus;
	private String createdBy;
	private Date createdAt;
	private String updatedBy;
	private Date updatedAt;

	public CostElementCostCenterMapping() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "COSTELEMENT_COSTCENTER_MAPPING_ID", unique = true, nullable = false)
	public Integer getCostElementCostCenterMappingId() {
		return costElementCostCenterMappingId;
	}

	public void setCostElementCostCenterMappingId(Integer costElementCostCenterMappingId) {
		this.costElementCostCenterMappingId = costElementCostCenterMappingId;
	}
	
	@Column(name = "COSTELEMENT_ID", nullable = false)
	public int getCostElementId() {
		return costElementId;
	}

	public void setCostElementId(int costElementId) {
		this.costElementId = costElementId;
	}
	
	@Column(name = "COSTCENTER_ID", nullable = false)
	public int getCostCenterId() {
		return costCenterId;
	}

	public void setCostCenterId(int costCenterId) {
		this.costCenterId = costCenterId;
	}
	
	@Column(name = "MAPPING_STATUS", nullable = false, length = 15)
	public String getMappingStatus() {
		return this.mappingStatus;
	}

	public void setMappingStatus(String mappingStatus) {
		this.mappingStatus = mappingStatus;
	}

	@Column(name = "CREATED_BY", length = 50)
	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATED_AT", nullable = true, length = 19)
	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	@Column(name = "UPDATED_BY", length = 50)
	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "UPDATED_AT", nullable = true, length = 19)
	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

}

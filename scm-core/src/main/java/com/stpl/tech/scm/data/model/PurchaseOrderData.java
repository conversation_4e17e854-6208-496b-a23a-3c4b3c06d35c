package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;

/**
 * Created by <PERSON><PERSON> on 11-06-2016.
 */
@Entity
@Table(name = "PURCHASE_ORDER")
public class PurchaseOrderData {

    private Integer id;
    private Date generationTime;
    private Date initiationTime;
    private Date lastUpdateTime;
    private Integer generatedForVendor;
    private Integer generatedBy;
    private Integer lastUpdatedBy;
    private Date fulfillmentDate;
    private BigDecimal billAmount;
    private BigDecimal paidAmount;
    private String receiptNumber;
    private String status;
    private String comment;
    private BigDecimal totalTaxes;
    private Integer approvedBy;
    private String vendorNotified = "N";
    private String forceClosed;
    private String creationType; //MANUAL,SYSTEM_CREATED, etc.
    private Integer dispatchLocationId;
    private Integer deliveryLocationId;
    private DocumentDetailData poInvoiceDocument;
    private Integer companyId;
    private String orderType;
    private Date expiryDate;
    private String expiryStatus;
    private Integer leadTime;
    private String type = "OPEX";
    private List<AdvancePaymentData> advancePaymentDatas = new ArrayList<>(0);


    private List<PurchaseOrderItemData> purchaseOrderItemDatas = new ArrayList<>(0);
    private List<PurchaseOrderNotificationData> notificationList = new ArrayList<>(0);
    private List<PurchaseOrderVendorGRMappingData> grMappingList = new ArrayList<>(0);


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PURCHASE_ORDER_ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "GENERATION_TIME", nullable = false)
    public Date getGenerationTime() {
        return generationTime;
    }

    public void setGenerationTime(Date generationTime) {
        this.generationTime = generationTime;
    }

    @Column(name = "GENERATED_FOR_VENDOR_ID", nullable = false)
    public int getGeneratedForVendor() {
        return generatedForVendor;
    }

    public void setGeneratedForVendor(int generatedForVendor) {
        this.generatedForVendor = generatedForVendor;
    }

    @Column(name = "GENERATED_BY", nullable = false)
    public Integer getGeneratedBy() {
        return generatedBy;
    }

    public void setGeneratedBy(int generatedBy) {
        this.generatedBy = generatedBy;
    }

    @Column(name = "FULFILLMENT_DATE", nullable = false)
    public Date getFulfillmentDate() {
        return fulfillmentDate;
    }

    public void setFulfillmentDate(Date fulfillmentDate) {
        this.fulfillmentDate = fulfillmentDate;
    }

    @Column(name = "BILL_AMOUNT", nullable = true)
    public BigDecimal getBillAmount() {
        return billAmount;
    }

    public void setBillAmount(BigDecimal billAmount) {
        this.billAmount = billAmount;
    }

    @Column(name = "PAID_AMOUNT", nullable = true)
    public BigDecimal getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(BigDecimal paidAmount) {
        this.paidAmount = paidAmount;
    }

    @Column(name = "ORDER_RECEIPT_NUMBER", nullable = true)
    public String getReceiptNumber() {
        return receiptNumber;
    }

    public void setReceiptNumber(String receiptNumber) {
        this.receiptNumber = receiptNumber;
    }

    @Column(name = "PURCHASE_ORDER_STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "COMMENT", nullable = true, length = 1000)
    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    @Column(name = "INITIATION_TIME", nullable = true)
    public Date getInitiationTime() {
        return initiationTime;
    }

    public void setInitiationTime(Date initiationTime) {
        this.initiationTime = initiationTime;
    }

    @Column(name = "LAST_UPDATE_TIME", nullable = true)
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "purchaseOrderData")
    public List<PurchaseOrderItemData> getPurchaseOrderItemDatas() {
        return purchaseOrderItemDatas;
    }

    public void setPurchaseOrderItemDatas(List<PurchaseOrderItemData> purchaseOrderItemDatas) {
        this.purchaseOrderItemDatas = purchaseOrderItemDatas;
    }

    @Column(name = "LAST_UPDATED_BY", nullable = true)
    public Integer getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(Integer lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    @Column(name = "TOTAL_TAXES", nullable = true)
    public BigDecimal getTotalTaxes() {
        return totalTaxes;
    }

    public void setTotalTaxes(BigDecimal totalTaxes) {
        this.totalTaxes = totalTaxes;
    }

    @Column(name = "APPROVED_BY", nullable = true)
    public Integer getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(Integer approvedBy) {
        this.approvedBy = approvedBy;
    }

    @Column(name = "VENDOR_NOTIFIED", nullable = true)
    public String getVendorNotified() {
        return vendorNotified;
    }

    public void setVendorNotified(String vendorNotified) {
        this.vendorNotified = vendorNotified;
    }

    @Column(name = "FORCE_CLOSED", nullable = true)
    public String getForceClosed() {
        return forceClosed;
    }

    public void setForceClosed(String forceClosed) {
        this.forceClosed = forceClosed;
    }

    @Column(name = "CREATION_TYPE", nullable = true)
    public String getCreationType() {
        return creationType;
    }

    public void setCreationType(String creationType) {
        this.creationType = creationType;
    }

    @Column(name = "DISPATCH_LOCATION_ID", nullable = true)
    public Integer getDispatchLocationId() {
        return dispatchLocationId;
    }

    public void setDispatchLocationId(Integer dispatchLocationId) {
        this.dispatchLocationId = dispatchLocationId;
    }

    @Column(name = "DELIVERY_LOCATION_ID", nullable = true)
    public Integer getDeliveryLocationId() {
        return deliveryLocationId;
    }

    public void setDeliveryLocationId(Integer deliveryLocationId) {
        this.deliveryLocationId = deliveryLocationId;
    }

    @Column(name = "ORDER_TYPE", nullable = true)
    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "purchaseOrderId")
    public List<PurchaseOrderNotificationData> getNotificationList() {
        return notificationList;
    }

    public void setNotificationList(List<PurchaseOrderNotificationData> notificationList) {
        this.notificationList = notificationList;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "purchaseOrderData")
    public List<PurchaseOrderVendorGRMappingData> getGrMappingList() {
        return grMappingList;
    }

    public void setGrMappingList(List<PurchaseOrderVendorGRMappingData> mappingList) {
        this.grMappingList = mappingList;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PO_INVOICE_ID")
    public DocumentDetailData getPoInvoiceDocument() {
        return poInvoiceDocument;
    }

    public void setPoInvoiceDocument(DocumentDetailData poInvoiceDocument) {
        this.poInvoiceDocument = poInvoiceDocument;
    }

    @Column(name = "COMPANY_ID", nullable = false)
    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    @Column(name = "EXPIRY_DATE")
    public Date getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    @Column(name = "EXPIRY_STATUS")
    public String getExpiryStatus() {
        return expiryStatus;
    }

    public void setExpiryStatus(String expiryStatus) {
        this.expiryStatus = expiryStatus;
    }

    @Column(name = "LEAD_TIME", nullable = false)
    public Integer getLeadTime() {
        return leadTime;
    }

    public void setLeadTime(Integer leadTime) {
        this.leadTime = leadTime;
    }

    @Column(name = "TYPE", nullable = true)
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "purchaseOrderData")
    public List<AdvancePaymentData> getAdvancePaymentDatas() {
        return advancePaymentDatas;
    }

    public void setAdvancePaymentDatas(List<AdvancePaymentData> advancePaymentDatas) {
        this.advancePaymentDatas = advancePaymentDatas;
    }


}

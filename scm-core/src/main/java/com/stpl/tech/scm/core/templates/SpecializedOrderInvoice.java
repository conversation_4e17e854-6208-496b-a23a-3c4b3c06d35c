package com.stpl.tech.scm.core.templates;

import com.stpl.tech.master.domain.model.Company;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.scm.domain.model.GoodsReceived;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorDispatchLocation;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import org.apache.velocity.tools.generic.DateTool;
import org.apache.velocity.tools.generic.MathTool;

import java.util.HashMap;
import java.util.Map;

public class SpecializedOrderInvoice extends AbstractVelocityTemplate {


    private GoodsReceived gr;
    private String basePath;
    private Company company;
    private Unit unitData;
    private String totalAmountInWords;
    private VendorDetail vendorDetail;
    private Float totalTax;
    private Float totalCgst;
    private Float totalSgst;
    private Float roundOffValue;
    private  Integer finalAmount;
    private String invoiceId;
    private VendorDispatchLocation vendorDispatchLocation;


    public SpecializedOrderInvoice(GoodsReceived gr, String basePath, Company company, Unit unitData, String totalAmountInWords , VendorDetail vendorDetail,
                                   Float totalTax , Float totalCgst , Float totalSgst , Float roundOffValue , Integer finalAmount, String invoiceId,
                                   VendorDispatchLocation vendorDispatchLocation) {
        this.gr = gr;
        this.basePath = basePath;
        this.company = company;
        this.unitData = unitData;
        this.totalAmountInWords = totalAmountInWords;
        this.vendorDetail = vendorDetail;
        this.totalTax = totalTax;
        this.totalCgst = totalCgst;
        this.totalSgst = totalSgst;
        this.roundOffValue = roundOffValue;
        this.finalAmount = finalAmount;
        this.invoiceId = invoiceId;
        this.vendorDispatchLocation = vendorDispatchLocation;

    }

    @Override
    public String getTemplatePath() {
        return "templates/specializedOrderInvoice.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/" + gr.getId() + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("gr", gr);
        stringObjectMap.put("totalAmountInWords", totalAmountInWords);
        stringObjectMap.put("unitData",unitData);
        stringObjectMap.put("company",company);
        stringObjectMap.put("dateTool", new DateTool());
        stringObjectMap.put("mathTool", new MathTool());
        stringObjectMap.put("vendorDetail",vendorDetail);
        stringObjectMap.put("totalTax",totalTax);
        stringObjectMap.put("totalCGST",totalCgst);
        stringObjectMap.put("totalSGST",totalSgst);
        stringObjectMap.put("roundOffValue",roundOffValue);
        stringObjectMap.put("finalAmount",finalAmount);
        stringObjectMap.put("invoiceId",invoiceId);
        stringObjectMap.put("vendorDispatchLocation",vendorDispatchLocation);
        return stringObjectMap;
    }
}

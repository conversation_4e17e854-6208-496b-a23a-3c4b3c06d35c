/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;

@Entity
@Table(name = "LINKED_ADVANCE_FOR_PAYMENT")
public class LinkedPaymentsForAdvance {

    private Integer linkedParentAdvanceId;

    private AdvancePaymentData advancePaymentData;

    private PaymentRequestData paymentRequestData;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "LINKED_PARENT_ADVANCE_ID", nullable = false, unique = true)
    public Integer getLinkedParentAdvanceId() {
        return this.linkedParentAdvanceId;
    }

    public void setLinkedParentAdvanceId(Integer linkedParentAdvanceId) {
        this.linkedParentAdvanceId = linkedParentAdvanceId;
    }

    @ManyToOne(fetch = FetchType.LAZY, targetEntity = AdvancePaymentData.class)
    @JoinColumn(name = "ADVANCE_PAYMENT_ID")
    public AdvancePaymentData getAdvancePaymentData() {
        return advancePaymentData;
    }

    public void setAdvancePaymentData(AdvancePaymentData advancePaymentData) {
        this.advancePaymentData = advancePaymentData;
    }

    @ManyToOne(fetch = FetchType.LAZY, targetEntity = PaymentRequestData.class)
    @JoinColumn(name = "PAYMENT_REQUEST_ID")
    public PaymentRequestData getPaymentRequestData() {
        return paymentRequestData;
    }

    public void setPaymentRequestData(PaymentRequestData paymentRequestData) {
        this.paymentRequestData = paymentRequestData;
    }
}

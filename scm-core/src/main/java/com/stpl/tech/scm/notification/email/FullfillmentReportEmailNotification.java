package com.stpl.tech.scm.notification.email;


import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.util.SCMObjectUtils;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.notification.email.template.FullfillmentReportEmailNotificationTemplate;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;


public class FullfillmentReportEmailNotification extends EmailNotification{

    FullfillmentReportEmailNotificationTemplate fullfillmentTemplate;

    EnvType envType;

    FullfillmentReportEmailNotification(){

    }
    public FullfillmentReportEmailNotification(FullfillmentReportEmailNotificationTemplate fullfillmentTemplate, EnvType envType){
            this.fullfillmentTemplate = fullfillmentTemplate;
            this.envType = envType;
    }

    @Override
    public String[] getToEmails() {
        if(SCMUtil.isProd(envType)){
            return SCMObjectUtils.getArray(SCMServiceConstants.TECHNOLOGY_EMAIL, SCMServiceConstants.SUPPLY_CHAIN_EMAIL);
        }
        return SCMObjectUtils.getArray(SCMServiceConstants.TECHNOLOGY_EMAIL);
    }

    @Override
    public String getFromEmail() {
        return SCMServiceConstants.REPORTING_EMAIL;
    }

    @Override
    public String subject() {
        return (SCMUtil.isDev(envType) ? SCMUtil.wrapInBrackets(envType.name()) : "") +
                "SCM Report: Fullfilment Percentage (NEW) - Kitchen and Warehouse " + AppUtils.getCurrentDateISTFormatted();
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return fullfillmentTemplate.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}

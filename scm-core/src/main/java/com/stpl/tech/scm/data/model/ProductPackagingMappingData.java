/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;
// Generated 4 May, 2016 12:06:56 AM by Hibernate Tools 3.2.2.GA

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 * ProductPackagingMappingData generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "PRODUCT_PACKAGING_MAPPING", uniqueConstraints = @UniqueConstraint(columnNames = {"PACKAGING_ID",
    "PRODUCT_ID"}))
public class ProductPackagingMappingData implements java.io.Serializable {

    private Integer productPackagingMappingId;
    private int packagingId;
    private int productId;
    private String isDefault = "N";
    private String mappingStatus;

    public ProductPackagingMappingData() {
    }

    public ProductPackagingMappingData(int packagingId, int productId, String mappingStatus) {
        this.packagingId = packagingId;
        this.productId = productId;
        this.mappingStatus = mappingStatus;
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)

    @Column(name = "PRODUCT_PACKAGING_MAPPING_ID", unique = true, nullable = false)
    public Integer getProductPackagingMappingId() {
        return this.productPackagingMappingId;
    }

    public void setProductPackagingMappingId(Integer productPackagingMappingId) {
        this.productPackagingMappingId = productPackagingMappingId;
    }

    @Column(name = "PACKAGING_ID", nullable = false)
    public int getPackagingId() {
        return this.packagingId;
    }

    public void setPackagingId(int packagingId) {
        this.packagingId = packagingId;
    }

    @Column(name = "PRODUCT_ID", nullable = false)
    public int getProductId() {
        return this.productId;
    }

    public void setProductId(int productId) {
        this.productId = productId;
    }

    @Column(name = "MAPPING_STATUS", nullable = false, length = 15)
    public String getMappingStatus() {
        return this.mappingStatus;
    }

    public void setMappingStatus(String mappingStatus) {
        this.mappingStatus = mappingStatus;
    }

    @Column(name = "IS_DEFAULT", nullable = false)
    public String getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(String isDefault) {
        this.isDefault = isDefault;
    }
}

package com.stpl.tech.scm.data.transport.model;

import java.math.BigDecimal;
import java.util.List;

public class EWayBillWrapper {

	private String userGstin;
	private String supplyType;
	private int subSupplyType;
	private String docType;
	private String docNo;
	private String docDate; // format string
	private Integer transType = 1; // default to REGULAR type
	private String fromGstin;
	private String fromTrdName;
	private String fromAddr1;
	private String fromAddr2;
	private String fromPlace;
	private Integer fromPincode;
	private Integer fromStateCode;
	private Integer actualFromStateCode;
	private String toGstin;
	private String toTrdName;
	private String toAddr1;
	private String toAddr2;
	private String toPlace;
	private Integer toPincode;
	private Integer toStateCode;
	private Integer actualToStateCode;
	private BigDecimal totalValue;
	private BigDecimal cgstValue = BigDecimal.ZERO;
	private BigDecimal sgstValue = BigDecimal.ZERO;
	private BigDecimal igstValue = BigDecimal.ZERO;
	private BigDecimal cessValue = BigDecimal.ZERO;
	private BigDecimal TotNonAdvolVal = BigDecimal.ZERO;
	private BigDecimal OthValue = BigDecimal.ZERO;
	private BigDecimal totInvValue = BigDecimal.ZERO;
	private Integer transMode;
	private Integer transDistance;
	private String transporterName;
	private String transporterId;
	private String transDocNo;
	private String transDocDate; // format string
	private String vehicleNo;
	private String vehicleType = "R";
	private String mainHsnCode;
	private List<EWayItemWrapper> itemList;

	public String getUserGstin() {
		return userGstin;
	}

	public void setUserGstin(String userGstin) {
		this.userGstin = userGstin;
	}

	public String getSupplyType() {
		return supplyType;
	}

	public void setSupplyType(String supplyType) {
		this.supplyType = supplyType;
	}

	public int getSubSupplyType() {
		return subSupplyType;
	}

	public void setSubSupplyType(int subSupplyType) {
		this.subSupplyType = subSupplyType;
	}

	public String getDocType() {
		return docType;
	}

	public void setDocType(String docType) {
		this.docType = docType;
	}

	public String getDocNo() {
		return docNo;
	}

	public void setDocNo(String docNo) {
		this.docNo = docNo;
	}

	public String getDocDate() {
		return docDate;
	}

	public void setDocDate(String docDate) {
		this.docDate = docDate;
	}

	public String getFromGstin() {
		return fromGstin;
	}

	public void setFromGstin(String fromGstin) {
		this.fromGstin = fromGstin;
	}

	public String getFromTrdName() {
		return fromTrdName;
	}

	public void setFromTrdName(String fromTrdName) {
		this.fromTrdName = fromTrdName;
	}

	public String getFromAddr1() {
		return fromAddr1;
	}

	public void setFromAddr1(String fromAddr1) {
		this.fromAddr1 = fromAddr1;
	}

	public String getFromAddr2() {
		return fromAddr2;
	}

	public void setFromAddr2(String fromAddr2) {
		this.fromAddr2 = fromAddr2;
	}

	public String getFromPlace() {
		return fromPlace;
	}

	public void setFromPlace(String fromPlace) {
		this.fromPlace = fromPlace;
	}

	public Integer getFromPincode() {
		return fromPincode;
	}

	public void setFromPincode(Integer fromPincode) {
		this.fromPincode = fromPincode;
	}

	public Integer getFromStateCode() {
		return fromStateCode;
	}

	public void setFromStateCode(Integer fromStateCode) {
		this.fromStateCode = fromStateCode;
	}

	public Integer getActualFromStateCode() {
		return actualFromStateCode;
	}

	public void setActualFromStateCode(Integer actualFromStateCode) {
		this.actualFromStateCode = actualFromStateCode;
	}

	public String getToGstin() {
		return toGstin;
	}

	public void setToGstin(String toGstin) {
		this.toGstin = toGstin;
	}

	public String getToTrdName() {
		return toTrdName;
	}

	public void setToTrdName(String toTrdName) {
		this.toTrdName = toTrdName;
	}

	public String getToAddr1() {
		return toAddr1;
	}

	public void setToAddr1(String toAddr1) {
		this.toAddr1 = toAddr1;
	}

	public String getToAddr2() {
		return toAddr2;
	}

	public void setToAddr2(String toAddr2) {
		this.toAddr2 = toAddr2;
	}

	public String getToPlace() {
		return toPlace;
	}

	public void setToPlace(String toPlace) {
		this.toPlace = toPlace;
	}

	public Integer getToPincode() {
		return toPincode;
	}

	public void setToPincode(Integer toPincode) {
		this.toPincode = toPincode;
	}

	public Integer getToStateCode() {
		return toStateCode;
	}

	public void setToStateCode(Integer toStateCode) {
		this.toStateCode = toStateCode;
	}

	public Integer getActualToStateCode() {
		return actualToStateCode;
	}

	public void setActualToStateCode(Integer actualToStateCode) {
		this.actualToStateCode = actualToStateCode;
	}

	public BigDecimal getTotalValue() {
		return totalValue;
	}

	public void setTotalValue(BigDecimal totalValue) {
		this.totalValue = totalValue;
	}

	public BigDecimal getCgstValue() {
		return cgstValue;
	}

	public void setCgstValue(BigDecimal cgstValue) {
		this.cgstValue = cgstValue;
	}

	public BigDecimal getSgstValue() {
		return sgstValue;
	}

	public void setSgstValue(BigDecimal sgstValue) {
		this.sgstValue = sgstValue;
	}

	public BigDecimal getIgstValue() {
		return igstValue;
	}

	public void setIgstValue(BigDecimal igstValue) {
		this.igstValue = igstValue;
	}

	public BigDecimal getCessValue() {
		return cessValue;
	}

	public void setCessValue(BigDecimal cessValue) {
		this.cessValue = cessValue;
	}

	public BigDecimal getTotNonAdvolVal() {
		return TotNonAdvolVal;
	}

	public void setTotNonAdvolVal(BigDecimal totNonAdvolVal) {
		TotNonAdvolVal = totNonAdvolVal;
	}

	public BigDecimal getOthValue() {
		return OthValue;
	}

	public void setOthValue(BigDecimal othValue) {
		OthValue = othValue;
	}

	public Integer getTransMode() {
		return transMode;
	}

	public void setTransMode(Integer transMode) {
		this.transMode = transMode;
	}

	public Integer getTransDistance() {
		return transDistance;
	}

	public void setTransDistance(Integer transDistance) {
		this.transDistance = transDistance;
	}

	public String getTransporterName() {
		return transporterName;
	}

	public void setTransporterName(String transporterName) {
		this.transporterName = transporterName;
	}

	public String getTransporterId() {
		return transporterId;
	}

	public void setTransporterId(String transporterId) {
		this.transporterId = transporterId;
	}

	public String getTransDocNo() {
		return transDocNo;
	}

	public void setTransDocNo(String transDocNo) {
		this.transDocNo = transDocNo;
	}

	public String getTransDocDate() {
		return transDocDate;
	}

	public void setTransDocDate(String transDocDate) {
		this.transDocDate = transDocDate;
	}

	public String getVehicleNo() {
		return vehicleNo;
	}

	public void setVehicleNo(String vehicleNo) {
		this.vehicleNo = vehicleNo;
	}

	public List<EWayItemWrapper> getItemList() {
		return itemList;
	}

	public void setItemList(List<EWayItemWrapper> itemList) {
		this.itemList = itemList;
	}

	public String getVehicleType() {
		return vehicleType;
	}

	public void setVehicleType(String vehicleType) {
		this.vehicleType = vehicleType;
	}

	public BigDecimal getTotInvValue() {
		return totInvValue;
	}

	public void setTotInvValue(BigDecimal totInvValue) {
		this.totInvValue = totInvValue;
	}

	public String getMainHsnCode() {
		return mainHsnCode;
	}

	public void setMainHsnCode(String mainHsnCode) {
		this.mainHsnCode = mainHsnCode;
	}

	public Integer getTransType() {
		return transType;
	}

	public void setTransType(Integer transType) {
		this.transType = transType;
	}
}

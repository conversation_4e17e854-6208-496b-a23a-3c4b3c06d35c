package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.model.ReferenceOrderData;
import com.stpl.tech.scm.data.model.RequestOrderData;
import com.stpl.tech.scm.domain.model.RegularOrderEvent;
import com.stpl.tech.scm.domain.model.RequestOrder;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import org.apache.velocity.tools.generic.MathTool;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CafeRoEmailNotificationTemplate extends AbstractVelocityTemplate {
    private List<RequestOrder> requestOrder;
    private Unit unit;
    private String basePath;
    private ReferenceOrderData referenceOrderData;
    private RegularOrderEvent orderEvent;
    private String generatedBy;
    private Map<Integer, BigDecimal> lastRoMap;
    private RequestOrderData lastRoData;

    public CafeRoEmailNotificationTemplate() {
    }

    public  CafeRoEmailNotificationTemplate(List<RequestOrder> requestOrder, Unit unit, String basePath, ReferenceOrderData referenceOrderData,
                                           RegularOrderEvent orderEvent, String generatedBy, Map<Integer, BigDecimal> lastRoMap, RequestOrderData lastRoData) {
        this.requestOrder = requestOrder;
        this.unit = unit;
        this.basePath = basePath;
        this.referenceOrderData = referenceOrderData;
        this.orderEvent = orderEvent;
        this.generatedBy = generatedBy;
        this.lastRoMap = lastRoMap;
        this.lastRoData = lastRoData;
    }

    @Override
    public String getTemplatePath() {
        return "templates/CafeROEmailTemplate.html";
    }


    @Override
    public String getFilepath() {
        return basePath + "/cafe/requestOrders/" + unit.getName() + "/" + SCMUtil.getDateString(SCMUtil.getCurrentDateIST()) + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("requestOrderMap", requestOrder);
        stringObjectMap.put("unitMap", unit);
        stringObjectMap.put("referenceOrderData", referenceOrderData);
        stringObjectMap.put("orderEvent", orderEvent);
        stringObjectMap.put("generatedBy", generatedBy);
        stringObjectMap.put("lastRoMap", lastRoMap);
        stringObjectMap.put("lastRoData", lastRoData);
        stringObjectMap.put("mathTool", new MathTool());
        return stringObjectMap;
    }

    public Unit getUnit() {
        return unit;
    }

    public List<RequestOrder> getRequestOrder() {
        return requestOrder;
    }

}


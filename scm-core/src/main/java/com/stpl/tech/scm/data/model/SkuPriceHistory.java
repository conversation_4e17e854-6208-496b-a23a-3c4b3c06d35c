/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;
// Generated 4 May, 2016 12:06:56 AM by Hibernate Tools 3.2.2.GA

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * SkuPriceKey generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "SKU_PRICE_HISTORY")
public class SkuPriceHistory implements java.io.Serializable {

	private Integer skuPriceHistoryId;
	private int skuPriceDataId;
	private BigDecimal currentPrice;
	private BigDecimal negotiatedPrice;
	private String changeType;
	private String recordStatus;
	private Date startDate;
	private Date endDate;
	private String createdBy;
	private Date createdAt;
	private String updatedBy;
	private Date updatedAt;
	private Integer contractId;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "SKU_PRICE_HISTORY_ID", unique = true, nullable = false)
	public Integer getSkuPriceHistoryId() {
		return this.skuPriceHistoryId;
	}

	public void setSkuPriceHistoryId(Integer unitProductMappingId) {
		this.skuPriceHistoryId = unitProductMappingId;
	}

	@Column(name = "SKU_PRICE_DATA_ID", nullable = false)
	public int getSkuPriceDataId() {
		return this.skuPriceDataId;
	}

	public void setSkuPriceDataId(int unitId) {
		this.skuPriceDataId = unitId;
	}

	@Column(name = "CURRENT_PRICE", precision = 10)
	public BigDecimal getCurrentPrice() {
		return currentPrice;
	}

	public void setCurrentPrice(BigDecimal price) {
		this.currentPrice = price;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "START_DATE", length = 10)
	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "END_DATE", length = 10)
	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	@Column(name = "NEGOTIATED_PRICE", precision = 10)
	public BigDecimal getNegotiatedPrice() {
		return negotiatedPrice;
	}

	public void setNegotiatedPrice(BigDecimal negotiatedPrice) {
		this.negotiatedPrice = negotiatedPrice;
	}

	@Column(name = "RECORD_STATUS", length = 50)
	public String getRecordStatus() {
		return recordStatus;
	}

	public void setRecordStatus(String recordStatus) {
		this.recordStatus = recordStatus;
	}

	@Column(name = "CREATED_BY", length = 50)
	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATED_AT", nullable = true, length = 19)
	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	@Column(name = "UPDATED_BY", length = 50)
	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "UPDATED_AT", nullable = true, length = 19)
	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}
	
	@Column(name = "CHANGE_TYPE", nullable = true, length = 15)
	public String getChangeType() {
		return changeType;
	}

	public void setChangeType(String changeType) {
		this.changeType = changeType;
	}

	@Column(name = "CONTRACT_ID", nullable = true, length = 15)
	public Integer getContractId() {
		return contractId;
	}

	public void setContractId(Integer contractId) {
		this.contractId = contractId;
	}
}

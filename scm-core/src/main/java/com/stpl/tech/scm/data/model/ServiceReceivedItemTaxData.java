package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * Copyright (C) $today.year, Sunshine Teahouse Private Limited - All Rights
 * Reserved Unauthorized copying of this file, via any medium is strictly
 * prohibited Proprietary and confidential
 */

@Entity
@Table(name = "SERVICE_RECEIVED_ITEM_TAX")
public class ServiceReceivedItemTaxData {

	private Integer taxDataId;
	private Integer serviceReceivedItemId;
	private String taxType;
	private BigDecimal taxPercentage;
	private BigDecimal taxValue;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ITEM_TAX_DATA_ID", unique = true, nullable = false)
	public Integer getTaxDataId() {
		return taxDataId;
	}

	public void setTaxDataId(Integer taxDataId) {
		this.taxDataId = taxDataId;
	}

	@Column(name = "SERVICE_RECEIVED_ITEM_ID", nullable = false)
	public Integer getServiceReceivedItemId() {
		return serviceReceivedItemId;
	}

	public void setServiceReceivedItemId(Integer serviceReceivedItemId) {
		this.serviceReceivedItemId = serviceReceivedItemId;
	}

	@Column(name = "TAX_TYPE", nullable = false)
	public String getTaxType() {
		return taxType;
	}

	public void setTaxType(String taxType) {
		this.taxType = taxType;
	}

	@Column(name = "TAX_PERCENTAGE", nullable = false)
	public BigDecimal getTaxPercentage() {
		return taxPercentage;
	}

	public void setTaxPercentage(BigDecimal taxPercentage) {
		this.taxPercentage = taxPercentage;
	}

	@Column(name = "TAX_VALUE", nullable = false)
	public BigDecimal getTaxValue() {
		return taxValue;
	}

	public void setTaxValue(BigDecimal taxValue) {
		this.taxValue = taxValue;
	}

}

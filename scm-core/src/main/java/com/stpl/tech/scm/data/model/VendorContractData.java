package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;
import java.util.Set;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "VENDOR_CONTRACT_DATA")
public class VendorContractData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CONTRACT_ID",unique = true)
    private Integer contractId;

    @Column(name = "VENDOR_ID")
    private Integer vendorId;

    @Column(name = "STATUS")
    private String status;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATED_AT")
    private Date createdAt;

    @Column(name = "CREATED_BY")
    private Integer createdBy;

    @Column(name = "DOCUMENT_ID")
    private Integer documentId;

    @Temporal(TemporalType.DATE)
    @Column(name = "START_DATE")
    private Date startDate;

    @Temporal(TemporalType.DATE)
    @Column(name = "END_DATE")
    private Date endDate;

    @Column(name = "APPROVAL_REQUEST_FROM")
    private String approvalRequestFrom;

    @Column(name = "VENDOR_USER_NAME")
    private String vendorUserName;

    @Column(name = "VENDOR_USER_DESIGNATION")
    private String vendorUserDesignation;

    @Column(name = "VENDOR_IP_ADDRESS")
    private String ipAddress;

    @Column(name = "AUTH_IP_ADDRESS")
    private String authIpAddress;

    @Column(name = "UNSIGNED_DOCUMENT_ID")
    private Integer unsignedDocumentId;

    @Column(name = "VENDOR_SIGNED_DOCUMENT_ID")
    private Integer signedDocumentId;

    @Column(name = "AUTH_SIGNED_DOCUMENT_ID")
    private Integer authSignedDocumentId;

    @Column(name = "VENDOR_DIGITAL_SIGN_ID")
    private Integer digitalSignID;

    @Column(name = "AUTH_DIGITAL_SIGN_ID")
    private Integer authDigitalSignID;

    @Column(name = "OTP_VERIFIED")
    private String isOtpVerified;

    @Column(name = "IS_BYPASSED")
    private String isByPassed;

    @Column(name = "IS_MAIL_TRIGGERED")
    private String isMailTriggered;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MAIL_TIME")
    private Date mailTime;

    @Column(name = "TEMPLATE_ID")
    private Integer templateId;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "contractData")
    private Set<VendorContractItemData> vendorContractItemDataList;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "contractData")
    private Set<WorkOrderData> workOrderDataSet;

}

package com.stpl.tech.scm.data.model;

import com.stpl.tech.scm.domain.model.VendorContractStatus;
import com.stpl.tech.scm.domain.model.WorkOrderType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import java.util.Date;
import java.util.Set;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "WORK_ORDER_DATA")
public class WorkOrderData {

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "WORK_ORDER_ID", unique = true, nullable = false)
    private Integer workOrderId;

    @JoinColumn(name = "CONTRACT_ID", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private VendorContractData contractData;

    @Column(name = "GENERATED_WORK_ORDER_NUMBER")
    private String generatedWorkOrderNumber;

    @Enumerated(EnumType.STRING)
    @Column(name = "WORK_ORDER_TYPE")
    private WorkOrderType workOrderType;

    @Temporal(TemporalType.DATE)
    @Column(name = "START_DATE")
    private Date startDate;

    @Temporal(TemporalType.DATE)
    @Column(name = "END_DATE")
    private Date endDate;

    @Enumerated(EnumType.STRING)
    @Column(name = "WORK_ORDER_STATUS")
    private VendorContractStatus workOrderStatus;

    @Column(name = "APPROVAL_REQUEST_FROM")
    private Integer approvalRequestId;

    @Column(name = "IS_BYPASSED")
    private String isByPassed;

    @Column(name = "CREATED_BY")
    private Integer createdBy;

    @Column(name = "CREATED_AT")
    private Date createdAt;

    @OneToOne(mappedBy = "workOrderData", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private WorkOrderApprovalMetaData woApprovalMetaData;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "workOrderData")
    private Set<VendorContractItemData> vendorContractItemDataList;


}

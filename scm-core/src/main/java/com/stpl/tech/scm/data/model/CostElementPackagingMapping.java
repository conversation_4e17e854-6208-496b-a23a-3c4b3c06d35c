package com.stpl.tech.scm.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "COST_ELEMENT_PACKAGING_MAP")
public class CostElementPackagingMapping implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2565093697355543375L;

	private Integer mappingId;
	private Integer costElementDataId;
	private PackagingDefinitionData packaging;
	private String mappingStatus;
	

	public CostElementPackagingMapping() {
		// TODO Auto-generated constructor stub
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "PACKAGING_MAPPING_ID")
	public Integer getMappingId() {
		return mappingId;
	}

	public void setMappingId(Integer mappingId) {
		this.mappingId = mappingId;
	}

	@Column(name = "COST_ELEMENT_ID", nullable = false)
	public Integer getCostElementDataId() {
		return costElementDataId;
	}

	public void setCostElementDataId(Integer costElementDataId) {
		this.costElementDataId = costElementDataId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PACKAGING_ID", nullable = false)
	public PackagingDefinitionData getPackaging() {
		return packaging;
	}

	public void setPackaging(PackagingDefinitionData packaging) {
		this.packaging = packaging;
	}

	@Column(name = "MAPPING_STATUS", nullable = false)
	public String getMappingStatus() {
		return mappingStatus;
	}

	public void setMappingStatus(String mappingStatus) {
		this.mappingStatus = mappingStatus;
	}

	
}

package com.stpl.tech.scm.data.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import java.math.BigDecimal;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "UOM_CONVERSION_MAPPING_DATA")
@Getter
@Setter
public class UOMConversionMappingData {
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "UOM_CONVERSION_MAPPING_ID")
    private Integer uomConversionMappingId;

    @Column(name = "FROM_UOM")
    private String fromUom;

    @Column(name = "TO_UOM")
    private String toUom;

    @Column(name = "CONVERSION_RATIO")
    private BigDecimal conversionRatio;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PRODUCT_ID", nullable = false)
    private ProductDefinitionData productDefinition;

}

package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import java.util.List;

@Entity
@Table(name = "SALES_PERFORMA_INVOICE_CORRECTED")
public class SalesPerformaInvoiceCorrected {

    private Integer id;
    private Integer invoiceId;
    private String invoiceStatus;
    private String type;
    private String docId;
    private Integer creditNoteDocId;
    private String creditNoteDocUrl;
    private Integer debitNoteDocId;
    private String debitNoteDocUrl;
    private String generatedCreditNoteId;
    private String generatedDebitNoteId;
    private String irnNo;
    private String uploadedAckNo;
    private String uploadedEwayNo;
    private String signedQrCode;
    private Integer barcodeId;
    private List<SalesPerformaInvoiceItemCorrected> salesPerformaCorrectedItems;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", unique = true)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name="INVOICE_ID",nullable = true)
    public Integer getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Integer invoiceId) {
        this.invoiceId = invoiceId;
    }
    @Column(name="INVOICE_STATUS",nullable = true)
    public String getInvoiceStatus() {
        return invoiceStatus;
    }

    public void setInvoiceStatus(String invoiceStatus) {
        this.invoiceStatus = invoiceStatus;
    }

    @Column(name="TYPE",nullable = true)
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Column(name = "DOC_ID")
    public String getDocId() {
        return docId;
    }

    public void setDocId(String docId) {
        this.docId = docId;
    }

    @Column(name="CREDIT_NOTE_DOC_ID",nullable = true)
    public Integer getCreditNoteDocId() {
        return creditNoteDocId;
    }

    public void setCreditNoteDocId(Integer creditNoteDocId) {
        this.creditNoteDocId = creditNoteDocId;
    }

    @Column(name="CREDIT_NOTE_DOC_URL",nullable = true)
    public String getCreditNoteDocUrl() {
        return creditNoteDocUrl;
    }

    public void setCreditNoteDocUrl(String creditNoteDocUrl) {
        this.creditNoteDocUrl = creditNoteDocUrl;
    }

    @Column(name="DEBIT_NOTE_DOC_ID",nullable = true)
    public Integer getDebitNoteDocId() {
        return debitNoteDocId;
    }

    public void setDebitNoteDocId(Integer debitNoteDocId) {
        this.debitNoteDocId = debitNoteDocId;
    }

    @Column(name="DEBIT_NOTE_DOC_URL",nullable = true)
    public String getDebitNoteDocUrl() {
        return debitNoteDocUrl;
    }

    public void setDebitNoteDocUrl(String debitNoteDocUrl) {
        this.debitNoteDocUrl = debitNoteDocUrl;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "salesPerformaInvoiceCorrected")
    public List<SalesPerformaInvoiceItemCorrected> getSalesPerformaCorrectedItems() {
        return salesPerformaCorrectedItems;
    }

    public void setSalesPerformaCorrectedItems(List<SalesPerformaInvoiceItemCorrected> salesPerformaCorrectedItems) {
        this.salesPerformaCorrectedItems = salesPerformaCorrectedItems;
    }

    @Column(name="GENERATED_CREDIT_NOTE_ID",nullable = true)
    public String getGeneratedCreditNoteId() {
        return generatedCreditNoteId;
    }

    public void setGeneratedCreditNoteId(String generatedCreditNoteId) {
        this.generatedCreditNoteId = generatedCreditNoteId;
    }

    @Column(name="GENERATED_DEBIT_NOTE_ID",nullable = true)
    public String getGeneratedDebitNoteId() {
        return generatedDebitNoteId;
    }

    public void setGeneratedDebitNoteId(String generatedDebitNoteId) {
        this.generatedDebitNoteId = generatedDebitNoteId;
    }

    @Column(name="IRN_NO",nullable = true)
    public String getIrnNo() {
        return irnNo;
    }

    public void setIrnNo(String irnNo) {
        this.irnNo = irnNo;
    }

    @Column(name="UPLOADED_ACK_NO",nullable = true)
    public String getUploadedAckNo() {
        return uploadedAckNo;
    }

    public void setUploadedAckNo(String uploadedAckNo) {
        this.uploadedAckNo = uploadedAckNo;
    }

    @Column(name="UPLOADED_EWAY_NO",nullable = true)
    public String getUploadedEwayNo() {
        return uploadedEwayNo;
    }

    public void setUploadedEwayNo(String uploadedEwayNo) {
        this.uploadedEwayNo = uploadedEwayNo;
    }

    @Column(name="SIGNED_QR_CODE",nullable = true)
    public String getSignedQrCode() {
        return signedQrCode;
    }

    public void setSignedQrCode(String signedQrCode) {
        this.signedQrCode = signedQrCode;
    }

    @Column(name="BARCODE_ID",nullable = true)
    public Integer getBarcodeId() {
        return barcodeId;
    }

    public void setBarcodeId(Integer barcodeId) {
        this.barcodeId = barcodeId;
    }
}

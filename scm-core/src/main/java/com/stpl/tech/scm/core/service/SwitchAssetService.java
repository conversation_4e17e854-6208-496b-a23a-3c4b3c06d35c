package com.stpl.tech.scm.core.service;


import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.data.transport.model.SwitchAssetBody;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.SwitchAsset;
import org.springframework.web.multipart.MultipartFile;

public interface SwitchAssetService {
    Integer uploadAssetImage(FileType type, MimeType mimeType, DocUploadType docType, MultipartFile file, Integer userId,Boolean isNewAsset) throws SumoException;
    SwitchAsset getValidAssetFromUnitAndTag(Integer unitId, String tagValue) throws SumoException;
     Boolean sendOtp(Integer userId) throws SumoException;

     Boolean startProcess(SwitchAssetBody switchAssetBody) throws SumoException;

    void deleteTempFiles();
}

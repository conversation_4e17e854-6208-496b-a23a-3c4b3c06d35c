package com.stpl.tech.scm.core.service;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.util.model.ServiceReceiveVO;
import com.stpl.tech.scm.data.model.BusinessCostCenterData;
import com.stpl.tech.scm.data.model.ServiceReceivedData;
import com.stpl.tech.scm.domain.model.BulkRequestVO;
import com.stpl.tech.scm.domain.model.BusinessCostCenter;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.ServiceReceive;
import com.stpl.tech.scm.domain.model.ServiceReceiveShort;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface ServiceReceiveManagementService {

	public Integer createServiceReceive(ServiceReceiveVO srVO) throws SumoException;

	public boolean cancelServiceReceive(int srId, int userId) throws SumoException;

	public DocumentDetail uploadDocument(FileType type, MimeType mimeType, DocUploadType docType, Integer userId,
			MultipartFile file);

	public List<ServiceReceivedData> findServiceReceivedDataList(Integer bccId,Integer vendorId, Integer locationId, Integer userId, Date startDate,
																 Date endDate, Integer serviceOrderId , boolean isShort);

	public List<ServiceReceive> findServiceReceive(Integer vendorId, Integer locationId, Integer userId, Date startDate, Date endDate, Integer serviceOrderId);

	public List<ServiceReceiveShort> findServiceReceiveShort(Integer bccId,Integer vendorId, Integer locationId, Integer userId, Date startDate,
															 Date endDate, Integer serviceOrderId);

	public List<ServiceReceive> getLinkedSrForSo(Integer vendorId, Integer locationId, Integer userId, Date startDate, Date endDate, Integer paymentRequestId,Integer capexId) throws SumoException;

	public List<ServiceReceive> findServiceReceiveForPayment(Integer vendorId, Integer companyId, Integer userId, Integer locationId, Date startDate, Date endDate);

	public Boolean setServiceReceiveForNoPayment(BulkRequestVO request);

	public List<BusinessCostCenter> getBusinessCostCentersData();

	public List<Integer> createServiceReceive(List<ServiceReceiveVO> srVOs) throws SumoException;

	public boolean createBusinessCostCentersData(BusinessCostCenter businessCostCenter) throws SumoException;

	public List<ServiceReceive> searchServiceReceivingForPaymentRequest(Integer paymentRequestId);

	public Integer updateProvisionalServiceReceive(ServiceReceiveVO srVO, Integer srId, List<Integer> removedDrilldownIds) throws SumoException;
	public Boolean approveSR(Integer srId) throws SumoException;

	void getBusinessCostCentres(List<BusinessCostCenterData> businessCostCenterDataList, List<BusinessCostCenter> businessCostCenters);

    Map<String, Date> getMinMaxDateForSrs(List<Integer> srIds);

	public List<Integer> reCheckAllSrsForAdvance(Integer srId);

	
	View generateSRExcelSheet(List<Integer> srIds) throws SumoException;

	String getPrBudgetType(Integer prId) throws SumoException;
}

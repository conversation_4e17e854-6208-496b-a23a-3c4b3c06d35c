package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

/**
 * Created by <PERSON><PERSON> on 11-06-2016.
 */
@Entity
@Table(name = "PURCHASE_ORDER_ITEM_DETAIL")
public class PurchaseOrderItemData {

    private Integer id;
    private int skuId;
    private String skuName;
    private String hsnCode;
    private BigDecimal requestedQuantity;
    private BigDecimal requestedAbsoluteQuantity;
    private BigDecimal transferredQuantity;
    private BigDecimal receivedQuantity;
    private String unitOfMeasure;
    private BigDecimal unitPrice;
    private BigDecimal negotiatedUnitPrice;
    private BigDecimal totalCost;
    private BigDecimal amountPaid; //totalCost + totalTax
    private PurchaseOrderData purchaseOrderData;
    private int packagingId;
    private String packagingName;
    private BigDecimal packagingQuantity;
    private BigDecimal conversionRatio;
    private String exemptItem = "N";
    private BigDecimal igstPercentage;
    private BigDecimal igstValue;
    private BigDecimal cgstPercentage;
    private BigDecimal cgstValue;
    private BigDecimal sgstPercentage;
    private BigDecimal sgstValue;
    private BigDecimal otherTaxes;
    private BigDecimal totalTax;
    private String type;
    private List<ItemTaxDetailData> otherTaxesApplied;
    private Integer departmentId;



    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PURCHASE_ORDER_ITEM_ID", nullable = false, unique = true)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "SKU_ID", nullable = false)
    public int getSkuId() {
        return skuId;
    }

    public void setSkuId(int skuId) {
        this.skuId = skuId;
    }

    @Column(name = "HSN_CODE", nullable = false)
    public String getHsnCode() {
        return hsnCode;
    }

    public void setHsnCode(String hsnCode) {
        this.hsnCode = hsnCode;
    }

    @Column(name = "SKU_NAME", nullable = false)
    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    @Column(name = "REQUESTED_QUANTITY", nullable = false)
    public BigDecimal getRequestedQuantity() {
        return requestedQuantity;
    }

    public void setRequestedQuantity(BigDecimal requestedQuantity) {
        this.requestedQuantity = requestedQuantity;
    }

    @Column(name = "REQUESTED_ABSOLUTE_QUANTITY", nullable = false)
    public BigDecimal getRequestedAbsoluteQuantity() {
        return requestedAbsoluteQuantity;
    }

    public void setRequestedAbsoluteQuantity(BigDecimal requestedAbsoluteQuantity) {
        this.requestedAbsoluteQuantity = requestedAbsoluteQuantity;
    }

    @Column(name = "TRANSFERRED_QUANTITY", nullable = true)
    public BigDecimal getTransferredQuantity() {
        return transferredQuantity;
    }

    public void setTransferredQuantity(BigDecimal transferredQuantity) {
        this.transferredQuantity = transferredQuantity;
    }

    @Column(name = "RECEIVED_QUANTITY", nullable = true)
    public BigDecimal getReceivedQuantity() {
        return receivedQuantity;
    }

    public void setReceivedQuantity(BigDecimal receivedQuantity) {
        this.receivedQuantity = receivedQuantity;
    }

    @Column(name = "UNIT_OF_MEASURE", nullable = false)
    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }

    @Column(name = "UNIT_PRICE", nullable = true)
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    @Column(name = "NEGOTIATED_UNIT_PRICE", nullable = true)
    public BigDecimal getNegotiatedUnitPrice() {
        return negotiatedUnitPrice;
    }

    public void setNegotiatedUnitPrice(BigDecimal negotiatedUnitPrice) {
        this.negotiatedUnitPrice = negotiatedUnitPrice;
    }

    @Column(name = "TOTAL_COST", nullable = true)
    public BigDecimal getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(BigDecimal totalCost) {
        this.totalCost = totalCost;
    }

    @Column(name = "AMOUNT_PAID", nullable = true)
    public BigDecimal getAmountPaid() {
        return amountPaid;
    }

    public void setAmountPaid(BigDecimal amountPaid) {
        this.amountPaid = amountPaid;
    }

    @ManyToOne(fetch = FetchType.LAZY, targetEntity = PurchaseOrderData.class)
    @JoinColumn(name = "PURCHASE_ORDER_ID", nullable = false)
    public PurchaseOrderData getPurchaseOrderData() {
        return purchaseOrderData;
    }

    public void setPurchaseOrderData(PurchaseOrderData purchaseOrderData) {
        this.purchaseOrderData = purchaseOrderData;
    }

    @Column(name = "PACKAGING_ID", nullable = false)
    public int getPackagingId() {
        return packagingId;
    }

    public void setPackagingId(int packagingId) {
        this.packagingId = packagingId;
    }

    @Column(name = "PACKAGING_NAME", nullable = false)
    public String getPackagingName() {
        return packagingName;
    }

    public void setPackagingName(String packagingName) {
        this.packagingName = packagingName;
    }

    @Column(name = "PACKAGING_QUANTITY", nullable = false)
    public BigDecimal getPackagingQuantity() {
        return packagingQuantity;
    }

    public void setPackagingQuantity(BigDecimal packagingQuantity) {
        this.packagingQuantity = packagingQuantity;
    }

    @Column(name = "PACKAGING_CONVERSION_RATIO", nullable = false)
    public BigDecimal getConversionRatio() {
        return conversionRatio;
    }

    public void setConversionRatio(BigDecimal conversionRatio) {
        this.conversionRatio = conversionRatio;
    }

    @Column(name = "EXEMPT_ITEM", nullable = false)
    public String getExemptItem() {
        return exemptItem;
    }

    public void setExemptItem(String exemptItem) {
        this.exemptItem = exemptItem;
    }

    @Column(name = "IGST", nullable = true)
    public BigDecimal getIgstPercentage() {
        return igstPercentage;
    }

    public void setIgstPercentage(BigDecimal igstPercentage) {
        this.igstPercentage = igstPercentage;
    }

    @Column(name = "IGST_VALUE", nullable = true)
    public BigDecimal getIgstValue() {
        return igstValue;
    }

    public void setIgstValue(BigDecimal igstValue) {
        this.igstValue = igstValue;
    }

    @Column(name = "CGST", nullable = true)
    public BigDecimal getCgstPercentage() {
        return cgstPercentage;
    }

    public void setCgstPercentage(BigDecimal cgstPercentage) {
        this.cgstPercentage = cgstPercentage;
    }

    @Column(name = "CGST_VALUE", nullable = true)
    public BigDecimal getCgstValue() {
        return cgstValue;
    }

    public void setCgstValue(BigDecimal cgstValue) {
        this.cgstValue = cgstValue;
    }

    @Column(name = "SGST", nullable = true)
    public BigDecimal getSgstPercentage() {
        return sgstPercentage;
    }

    public void setSgstPercentage(BigDecimal sgstPercentage) {
        this.sgstPercentage = sgstPercentage;
    }

    @Column(name = "SGST_VALUE", nullable = true)
    public BigDecimal getSgstValue() {
        return sgstValue;
    }

    public void setSgstValue(BigDecimal sgstValue) {
        this.sgstValue = sgstValue;
    }

    @Column(name = "OTHER_TAXES", nullable = true)
    public BigDecimal getOtherTaxes() {
        return otherTaxes;
    }

    public void setOtherTaxes(BigDecimal otherTaxes) {
        this.otherTaxes = otherTaxes;
    }

    @Column(name = "TOTAL_TAX_VALUE", nullable = true)
    public BigDecimal getTotalTax() {
        return totalTax;
    }

    public void setTotalTax(BigDecimal totalTax) {
        this.totalTax = totalTax;
    }

    @Column(name = "TYPE", nullable = true)
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "purchaseItem")
    public List<ItemTaxDetailData> getOtherTaxesApplied() {
        return otherTaxesApplied;
    }

    public void setOtherTaxesApplied(List<ItemTaxDetailData> otherTaxesApplied) {
        this.otherTaxesApplied = otherTaxesApplied;
    }

    @Column(name = "DEPARTMENT_ID", nullable = true)
    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }


}

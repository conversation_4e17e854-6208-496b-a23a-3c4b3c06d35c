package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "TDS_LEDGER_RATE")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TdsLedgerRate {

    @Id
     @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    Long id;
    @Column(name = "LEDGER_NAME")
    String ledgerName;

    @Column(name = "SECTION")
    String section;
    @Column(name ="STATUS")
    String Status;

}

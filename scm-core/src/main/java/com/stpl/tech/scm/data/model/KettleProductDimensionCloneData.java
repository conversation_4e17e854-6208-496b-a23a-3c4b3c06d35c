package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;


@Setter
@Getter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "KETTLE_PRODUCT_DIMENSION_DATA_CLONE")
public class KettleProductDimensionCloneData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "PRODUCT_ID", nullable = false)
    private Integer productId;

    @Column(name = "DIMENSION", length = 255)
    private String dimension;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "KETTLE_UNIT_CLONE_ID", nullable = false)
    private KettleUnitDetailDataClone unitDetailDataClone;

    public KettleProductDimensionCloneData(int productId, String dimension) {
        this.productId = productId;
        this.dimension = dimension;
    }
}

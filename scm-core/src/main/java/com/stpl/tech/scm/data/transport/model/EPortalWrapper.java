package com.stpl.tech.scm.data.transport.model;

import java.util.List;

public class EPortalWrapper {

    private String version;
    private TransferDetails TranDtls;
    private DocumentDetails DocDtls;
    private SellerDetails SellerDtls;
    private BuyerDetails BuyerDtls;
    private ValueDetails ValDtls;
    private EwayBillDetails EwbDtls;
    private ReferenceDetails RefDtls;
    private List<EPortalitemDetails> ItemList;

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public TransferDetails getTranDtls() {
        return TranDtls;
    }

    public void setTranDtls(TransferDetails tranDtls) {
        TranDtls = tranDtls;
    }

    public DocumentDetails getDocDtls() {
        return DocDtls;
    }

    public void setDocDtls(DocumentDetails docDtls) {
        DocDtls = docDtls;
    }

    public SellerDetails getSellerDtls() {
        return SellerDtls;
    }

    public void setSellerDtls(SellerDetails sellerDtls) {
        SellerDtls = sellerDtls;
    }

    public BuyerDetails getBuyerDtls() {
        return BuyerDtls;
    }

    public void setBuyerDtls(BuyerDetails buyerDtls) {
        BuyerDtls = buyerDtls;
    }

    public ValueDetails getValDtls() {
        return ValDtls;
    }

    public void setValDtls(ValueDetails valDtls) {
        ValDtls = valDtls;
    }

    public EwayBillDetails getEwbDtls() {
        return EwbDtls;
    }

    public void setEwbDtls(EwayBillDetails ewbDtls) {
        EwbDtls = ewbDtls;
    }

    public ReferenceDetails getRefDtls() {
        return RefDtls;
    }

    public void setRefDtls(ReferenceDetails refDtls) {
        RefDtls = refDtls;
    }

    public List<EPortalitemDetails> getItemList() {
        return ItemList;
    }

    public void setItemList(List<EPortalitemDetails> itemList) {
        ItemList = itemList;
    }
}

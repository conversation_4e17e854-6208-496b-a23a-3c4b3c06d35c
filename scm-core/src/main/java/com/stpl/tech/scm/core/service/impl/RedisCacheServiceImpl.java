package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.scm.core.service.RedisCacheService;
import com.stpl.tech.scm.core.service.SCMMetadataService;
//import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.redis.dao.AssetDefinitionDao;
import com.stpl.tech.scm.data.dao.SCMAssetManagementDao;
import com.stpl.tech.scm.domain.model.AssetDefinition;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.stpl.tech.scm.data.converter.SCMDataConverter.convert;

@Service
@Slf4j
public class RedisCacheServiceImpl implements RedisCacheService {

    private static final Logger LOG = LoggerFactory.getLogger(RedisCacheServiceImpl.class);

    @Autowired
    private AssetDefinitionDao assetDefinitionDao;

    @Autowired
    private SCMAssetManagementDao scmAssetManagementDao;

    @Autowired
    private SCMMetadataService scmMetadataService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public List<AssetDefinition> getAssetsByUnitId(Integer unitId) {
        return assetDefinitionDao.findByUnitId(unitId);
   }

    @Override
    public List<AssetDefinition> getAllAssets() {
        List<AssetDefinition> assetDefinitionList = new ArrayList<>();
        for(AssetDefinition assetDefinition : assetDefinitionDao.findAll()){
            assetDefinitionList.add(assetDefinition);
        }
        return assetDefinitionList;
    }

    @Override
    public AssetDefinition getAssetByAssetId(Integer assetId) {
        return assetDefinitionDao.findByAssetId(assetId);
    }

    @Override
    public void updateAssetToCache(AssetDefinition assetDefinition) {
        assetDefinitionDao.save(assetDefinition);
    }

    @Override
    public void reloadAssetCache(){
        log.info("Refreshing ############# Asset Definition cache");
        assetDefinitionDao.deleteAll();
        scmMetadataService.saveAssetDefinitionToRedis(null);
    }

    @Override
    public void deleteAssetCache(){
        assetDefinitionDao.deleteAll();
    }

    @Override
    public void initialAssetCacheLoad(){
        long count = assetDefinitionDao.count();
        if( count == 0 ){
            reloadAssetCache();
        }
    }

}

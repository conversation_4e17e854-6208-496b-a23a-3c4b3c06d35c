package com.stpl.tech.scm.core.service.impl;


import com.hazelcast.map.IMap;
import com.hazelcast.multimap.MultiMap;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.readonly.domain.model.ProductVO;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.KettleStockOutAsyncService;
import com.stpl.tech.scm.core.service.KettleStockOutService;
import com.stpl.tech.scm.core.util.SCMObjectUtils;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.ValidationUtil;
import com.stpl.tech.scm.data.dao.KettleStockOutDao;
import com.stpl.tech.scm.data.dao.SchedulerStatusDao;
import com.stpl.tech.scm.data.enums.SchedulerStatus;
import com.stpl.tech.scm.data.enums.SchedulerType;
import com.stpl.tech.scm.data.model.KettleProductDataClone;
import com.stpl.tech.scm.data.model.KettleUnitDetailDataClone;
import com.stpl.tech.scm.data.model.SchedulerStatusData;
import com.stpl.tech.scm.notification.email.GenericEmailTemplate;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class KettleStockOutServiceImpl implements KettleStockOutService {

    @Autowired
    private KettleStockOutDao kettleStockOutDao;

    @Autowired
    private SchedulerStatusDao schedulerStatusDao;

    @Autowired
    private EnvProperties envProperties;

    @Autowired
    private KettleStockOutAsyncService kettleStockOutAsyncService;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    @Qualifier("taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM")
    public boolean checkWhetherPercentageDataAlreadyCreated(SchedulerType schedulerType) {
        return schedulerStatusDao.existsByTypeStatusAndExactDate(
                schedulerType,
                SchedulerStatus.SUCCESS, 
                AppUtils.getBusinessDate()
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM")
    public void createStockOutPercentageData(Integer unitId) {
        SchedulerStatusData schedulerStatusData = new SchedulerStatusData(
                "createStockOutPercentageData", 
                SchedulerStatus.INITIATED, 
                SchedulerType.STOCK_OUT_PERCENTAGE
        );
        try {
//            processStockOutPercentageData();
//            processStockOutPercentageDataV2(unitId, AppUtils.getBusinessDate());
            createStockOutPercentageDataForDate(unitId, AppUtils.getBusinessDate());
            schedulerStatusData.setStatus(SchedulerStatus.SUCCESS);
            schedulerStatusData.setMessage("createStockOutPercentageData executed successfully");
        } catch (Exception e) {
            log.error("Error in createStockOutPercentageData :: ", e);
            schedulerStatusData.setStatus(SchedulerStatus.FAILED);
            schedulerStatusData.setMessage("Error in createStockOutPercentageData :: " + e.getMessage());
            sendMailOnFailureCase(e.getMessage());
            throw e;
        } finally {
            schedulerStatusDao.save(schedulerStatusData);
        }
    }

//    private void processStockOutPercentageDataV2(Integer unitIdParam, Date businessDate) {
        // first getting all the cafe data which we stored yesterday and make a map
        // after that we will loop on each cafe and run for that cafe
        // what we do is while running for that cafe , based on the Timings data which we got for opening and closing we make two dates , openingdate and closing date
        // now we check if the opeing date is before 5:00 AM then we need to run our algo on the before date data also to caluclate the stock out
        // if the closing date is after 5:00 AM then we need to run our algo on the after date data also to calculate the stock out percentage

        // Step 1: load unit (cafe) details snapshot from KETTLE_UNIT_DETAIL_DATA_CLONE and build a map
        // Key format: unitId-brandId-inventoryLevel (consistent with V1 processing)
//        Map<Integer, List<KettleUnitDetailDataClone>> unitDetailDataCloneMap = getKettleUnitDetailDataCloneV2();
//        Map<Integer, KettleProductDataClone> productDataCloneMap = getKettleProductDataClone();
//
//        List<CompletableFuture<Void>> asyncTasks = new ArrayList<>();
//
//        unitDetailDataCloneMap.forEach((unitId, kettleUnitDetailDataClones) -> {
//            if (Objects.nonNull(unitIdParam) && !unitId.equals(unitIdParam)) {
//                return;
//            }
//
//            // Submit task directly to threadpool
//            CompletableFuture<Void> asyncTask = CompletableFuture.runAsync(() -> {
//                try {
//                    kettleStockOutAsyncService.processUnitStockOutPercentageAsyncByDate(unitId, kettleUnitDetailDataClones, productDataCloneMap, businessDate).get();
//                } catch (Exception e) {
//                    log.error("Error in threadpool task for unit stock out percentage processing for unitId: {}", unitId, e);
//                    // Don't rethrow - let this task fail silently and continue with others
//                }
//            }, taskExecutor);
//            asyncTasks.add(asyncTask);
//        });
//
//        // Wait for all async tasks to complete
//        if (!asyncTasks.isEmpty()) {
//            CompletableFuture<Void> allTasks = CompletableFuture.allOf(asyncTasks.toArray(new CompletableFuture[0]));
//            allTasks.join(); // This will block until all tasks are completed
//        }
//
//        log.info("Completed processing {} async tasks for stock out percentage data", asyncTasks.size());
//    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM")
    public void createStockOutPercentageDataByBusinessDate(Date startDate, Date endDate, Integer unitId) {
        try {
            // Set default dates if not provided
            if (startDate == null) {
                startDate = AppUtils.getBusinessDate(); // Current business date
            }
            if (endDate == null) {
                endDate = startDate; // Same as start date if not provided
            }

            log.info("Starting stock out percentage data creation for date range: {} to {}, unitId: {}", startDate, endDate, unitId);

            // Convert dates to LocalDate for easier iteration
            LocalDate start = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate end = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

            // Loop through each business date
            LocalDate currentDate = start;
            while (!currentDate.isAfter(end)) {
                Date businessDate = Date.from(currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                
                log.info("Processing business date: {} for unitId: {}", businessDate, unitId);
                
                // Call single-date method for each business date
                createStockOutPercentageDataByBusinessDate(businessDate, unitId);
                
                currentDate = currentDate.plusDays(1);
            }

            log.info("Completed stock out percentage data creation for date range: {} to {}", startDate, endDate);
        } catch (Exception e) {
            log.error("Error in createStockOutPercentageDataByBusinessDate", e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM")
    public void createStockOutPercentageDataByBusinessDate(Date businessDate, Integer unitId) {
        try {
            // Set default date if not provided
            if (businessDate == null) {
                businessDate = AppUtils.getBusinessDate(); // Current business date
            }

            log.info("Processing business date: {} for unitId: {}", businessDate, unitId);
            
            // 1st call clearAndCreateDump with the date
            clearAndCreateDumpByDate(unitId, businessDate);
            
            // Create stock out percentage data for this specific date
            createStockOutPercentageDataForDate(unitId, businessDate);
            
            log.info("Completed stock out percentage data creation for business date: {} and unitId: {}", businessDate, unitId);
        } catch (Exception e) {
            log.error("Error in createStockOutPercentageDataByBusinessDate for date: {} and unitId: {}", businessDate, unitId, e);
            throw e;
        }
    }

    private void createStockOutPercentageDataForDate(Integer unitIdParam, Date businessDate) {
        // Similar to processStockOutPercentageDataV2 but for specific date
        Map<Integer, List<KettleUnitDetailDataClone>> unitDetailDataCloneMap = getKettleUnitDetailDataCloneV2();
        Map<Integer, KettleProductDataClone> productDataCloneMap = getKettleProductDataClone();

        List<CompletableFuture<Void>> asyncTasks = new ArrayList<>();

        unitDetailDataCloneMap.forEach((unitId, kettleUnitDetailDataClones) -> {
            if (Objects.nonNull(unitIdParam) && !unitId.equals(unitIdParam)) {
                return;
            }
            
            // Submit task directly to threadpool with business date
            CompletableFuture<Void> asyncTask = CompletableFuture.runAsync(() -> {
                try {
                    kettleStockOutAsyncService.processUnitStockOutPercentageAsyncByDate(unitId, kettleUnitDetailDataClones, productDataCloneMap, businessDate).get();
                } catch (Exception e) {
                    log.error("Error in threadpool task for unit stock out percentage processing for unitId: {} on date: {}", unitId, businessDate, e);
                    // Don't rethrow - let this task fail silently and continue with others
                }
            }, taskExecutor);
            asyncTasks.add(asyncTask);
        });

        // Wait for all async tasks to complete
        if (!asyncTasks.isEmpty()) {
            CompletableFuture<Void> allTasks = CompletableFuture.allOf(asyncTasks.toArray(new CompletableFuture[0]));
            allTasks.join(); // This will block until all tasks are completed
        }

        log.info("Completed processing {} async tasks for stock out percentage data for date: {}", asyncTasks.size(), businessDate);
    }

    private void clearAndCreateDumpByDate(Integer unitId, Date businessDate) {
        kettleStockOutDao.clearAndCreateProductDataCloneTable();
        kettleStockOutDao.clearAndCreateUnitDetailDataCloneTable();
        createUnitDetailDataCloneByDate(unitId, businessDate);
    }

    private void createUnitDetailDataCloneByDate(Integer unitId, Date businessDate) {
        try {
            Map<Integer, Unit> units = masterDataCache.getUnits();
            MultiMap<Integer, ProductVO> allUnitsProducts = masterDataCache.getUnitProductTrimmedDetails();
            IMap<Integer, Brand> brandMetaData = masterDataCache.getBrandMetaData();
            Map<Integer, Product> productDetails = masterDataCache.getProductDetails();

            List<CompletableFuture<Void>> asyncTasks = new ArrayList<>();

            for (Unit unit : units.values()) {
                if (UnitCategory.CAFE.equals(unit.getFamily())) {
                    if (Objects.nonNull(unitId) && !(unit.getId() == unitId)) {
                        continue;
                    }
                    Collection<ProductVO> productVOS = allUnitsProducts.get(unit.getId());
                    if (CollectionUtils.isEmpty(productVOS)) {
                        continue;
                    }
                    Set<Integer> mappedBrands = productVOS.stream().
                            filter(e -> !CollectionUtils.isEmpty(e.getPrices())).map(ProductVO::getBrandId).collect(Collectors.toSet());
                    log.info("Found mapped brands for unitId: {} on date: {} are : {} and brands are : {}", unit.getId(), businessDate, mappedBrands.size(),
                            Arrays.toString(mappedBrands.toArray()));
                    List<Brand> brands = brandMetaData.values().stream().filter(e -> mappedBrands.contains(e.getBrandId()) && AppConstants.ACTIVE.equalsIgnoreCase(e.getStatus())).collect(Collectors.toList());
                    log.info("Found active brands for unitId: {} on date: {} are : {} and brands are : {}", unit.getId(), businessDate, brands.size(),
                            Arrays.toString(brands.stream().map(Brand::getBrandId).toList().toArray()));
                    if (ValidationUtil.checkIsEmptyCollection(brands)) {
                        log.warn("No active brands found for unitId: {} on date: {}", unit.getId(), businessDate);
                        continue;
                    }
                    for (Brand brand : brands) {
                        // Submit task directly to threadpool with business date
                        CompletableFuture<Void> asyncTask = CompletableFuture.runAsync(() -> {
                            try {
                                kettleStockOutAsyncService.createKettleUnitDetailDataCloneAsyncByDate(unit, brand, productDetails, productVOS, businessDate).get();
                            } catch (Exception e) {
                                log.error("Error in threadpool task for unit {} and brand {} on date {}", unit.getId(), brand.getBrandId(), businessDate, e);
                                // Don't rethrow - let this task fail silently and continue with others
                            }
                        }, taskExecutor);
                        asyncTasks.add(asyncTask);
                    }
                }
            }

            // Wait for all async tasks to complete
            if (!asyncTasks.isEmpty()) {
                CompletableFuture<Void> allTasks = CompletableFuture.allOf(asyncTasks.toArray(new CompletableFuture[0]));
                allTasks.join(); // This will block until all tasks are completed
            }

            log.info("Completed processing {} async tasks for unit detail data clone for date: {}", asyncTasks.size(), businessDate);
        } catch (Exception e) {
            log.error("Error while creating unit detail data clone for date: {}", businessDate, e);
        }
    }



    private Map<Integer, List<KettleUnitDetailDataClone>> getKettleUnitDetailDataCloneV2() {
        List<KettleUnitDetailDataClone> unitDetailDataCloneList = kettleStockOutDao.getKettleUnitDetailDataClone();
        ValidationUtil.requireNonEmptyCollection(unitDetailDataCloneList, "No Unit clone data found");
        return unitDetailDataCloneList.stream()
                .collect(Collectors.groupingBy(KettleUnitDetailDataClone::getUnitId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM")
    public void processStockOutDataAndCreateDump(Integer unitId) {
        SchedulerStatusData schedulerStatusData = new SchedulerStatusData(
                "processStockOutDataAndCreateDump", 
                SchedulerStatus.INITIATED, 
                SchedulerType.STOCK_OUT_DUMP_CREATION
        );
        try {
//            clearAndCreateDump(unitId);
            clearAndCreateDumpByDate(unitId, AppUtils.getBusinessDate());
            schedulerStatusData.setStatus(SchedulerStatus.SUCCESS);
            schedulerStatusData.setMessage("processStockOutDataAndCreateDump executed successfully");
        } catch (Exception e) {
            schedulerStatusData.setStatus(SchedulerStatus.FAILED);
            schedulerStatusData.setMessage("Error in processStockOutDataAndCreateDump :: " + e.getMessage());
            log.error("Error in processStockOutDataAndCreateDump :: ", e);
            throw e;
        } finally {
            schedulerStatusDao.save(schedulerStatusData);
        }
    }

//    private void processStockOutPercentageData() {
//        Map<String, Long> productDownTimeMap = new HashMap<>();
//        Map<Integer, KettleStockOutDateWiseData> stockOutDayWiseMap = new HashMap<>();
//        Map<String, KettleUnitDetailDataClone> unitDetailDataCloneMap = getKettleUnitDetailDataClone();
//        Map<Integer, KettleProductDataClone> productDataCloneMap = getKettleProductDataClone();
//        getStockOutDayWiseMap(stockOutDayWiseMap, productDataCloneMap);
//
//        getStockOutTimingData(stockOutDayWiseMap, productDownTimeMap, unitDetailDataCloneMap);
//
//        calculateStockOutPercentage(productDownTimeMap, unitDetailDataCloneMap);
//        kettleStockOutDao.updateList(new ArrayList<>(stockOutDayWiseMap.values()));
//    }

    private void sendMailOnFailureCase(String errorMsg) {
        try {
            log.info("Sending Mail On Failure Case");

            String subject = "Stock Out Percentage Data Creation Failed";
            String body = getEmailErrorBody().formatted(errorMsg, envProperties.getEnvType());
            new GenericEmailTemplate(
                    subject,
                    body,
                    SCMObjectUtils.getArray(SCMServiceConstants.TECHNOLOGY_EMAIL),
                    envProperties.getEnvType(),
                    SCMServiceConstants.REPORTING_EMAIL
            ).sendEmail();
        } catch (Exception e) {
            log.error("Error in sendMailOnFailureCase :: ", e);
        }
    }

//    private void getStockOutDayWiseMap(Map<Integer, KettleStockOutDateWiseData> stockOutDayWiseMap,
//                                       Map<Integer, KettleProductDataClone> productDataCloneMap) {
//        List<KettleStockOutDateWiseData> stockOutDateWiseData = kettleStockOutDao.getKettleStockOutDayWiseData();
//
//        for (KettleStockOutDateWiseData data : stockOutDateWiseData) {
//            KettleProductDataClone productDataClone = productDataCloneMap.get(data.getKettleProductId());
//            data.setInventoryTrackLevel(productDataClone.getInventoryTrackLevel());
//            data.setBrandId(productDataClone.getBrandId());
//            stockOutDayWiseMap.put(data.getKettleStockOutDateWiseDataId(), data);
//        }
//    }

//    private void getStockOutTimingData(Map<Integer, KettleStockOutDateWiseData> stockOutDayWiseMap,
//                                       Map<String, Long> productDownTimeMap,
//                                       Map<String, KettleUnitDetailDataClone> unitDetailDataCloneMap) {
//        List<KettleStockOutTimingsData> stockOutTimingsData = kettleStockOutDao.getKettleStockOutTimingsData(stockOutDayWiseMap.keySet());
//
//        if (ValidationUtil.checkNonEmptyCollection(stockOutTimingsData, "Stock out timing data is empty/null")) {
//            return;
//        }
//
//        Map<Integer, List<KettleStockOutTimingsData>> stockOutTimingsMap = stockOutTimingsData.stream()
//                .collect(Collectors.groupingBy(timingData ->
//                    timingData.getKettleStockOutDateWiseDataId().getKettleStockOutDateWiseDataId()));
//
//        stockOutTimingsMap.forEach((kettleStockOutDateWiseDataId, timingsData) -> {
//            KettleStockOutDateWiseData data = stockOutDayWiseMap.get(kettleStockOutDateWiseDataId);
//            String KEY = generateKey(data);
//            KettleUnitDetailDataClone ud = unitDetailDataCloneMap.get(KEY);
//            Long totalDownTime = getTotalDownTime(timingsData, ud);
//            productDownTimeMap.merge(KEY, totalDownTime, Long::sum);
//        });
//    }

//    private Long getTotalDownTime(List<KettleStockOutTimingsData> timingsData, KettleUnitDetailDataClone ud) {
//        Date cafeClosingTime = ud.getCafeClosing();
//        return timingsData.stream()
//                .mapToLong(timing -> {
//                    Date stockOut = timing.getStockOutTime();
//                    Date stockIn = timing.getStockInTime();
//
//                    // If stockIn is null, assume 5 AM next day
//                    if (stockIn == null && stockOut != null) {
//                        // Get calendar instance at stockOut time
//                        Calendar calendar = Calendar.getInstance();
//                        calendar.setTime(cafeClosingTime);
//
//                        stockIn = calendar.getTime();
//                    }
//
//                    // If stockOut is still null, treat as 0 duration
//                    if (stockOut == null) {
//                        return 0L;
//                    }
//                    long diffMillis = stockIn.getTime() - stockOut.getTime();
//                    return diffMillis / (60 * 1000); // convert to minutes
//                })
//                .sum();
//    }
//
//    private void calculateStockOutPercentage(Map<String, Long> productDownTimeMap,
//                                             Map<String, KettleUnitDetailDataClone> unitDetailDataCloneMap) {
//        List<KettleStockOutPercentageData> stockOutPercentageDataList = new ArrayList<>();
//
//        Date businessDate = SCMUtil.getPreviousBusinessDate(AppUtils.getBusinessDate());
//        for (KettleUnitDetailDataClone ud : unitDetailDataCloneMap.values()) {
//            Integer unitId = ud.getUnitId();
//            String KEY = SCMUtil.generateUniqueKey(unitId.toString(), ud.getBrandId().toString(), ud.getInventoryLevel());
//            Long productDownTime = productDownTimeMap.get(KEY);
//
//            KettleStockOutPercentageData stockOutPercentageData = new KettleStockOutPercentageData();
//            Integer totalProductsCount = ud.getTotalProducts();
//            stockOutPercentageData.setUnitId(unitId);
//            stockOutPercentageData.setBrandId(ud.getBrandId());
//            stockOutPercentageData.setInventoryTrackLevel(ud.getInventoryLevel());
//            stockOutPercentageData.setProductCount(totalProductsCount);
//            stockOutPercentageData.setBusinessDate(businessDate);
//            stockOutPercentageData.setCafeOpening(ud.getCafeOpening());
//            stockOutPercentageData.setCafeClosing(ud.getCafeClosing());
//            stockOutPercentageData.setCafeOperational(ud.getCafeOperational());
//            Integer productWiseTotalOperationTimeInMin = getProductOperationalTimeInMin(ud);
//            stockOutPercentageData.setProductOperationalTimeInMin(productWiseTotalOperationTimeInMin);
//            Integer totalOperationTimeInMin = SCMUtil.multiply(productWiseTotalOperationTimeInMin, totalProductsCount);
//            stockOutPercentageData.setTotalOperationTimeInMin(totalOperationTimeInMin);
//            if (ValidationUtil.checkNonNull(productDownTime, "No Product Down time Found")) {
//                Integer totalDownTimeInMin = productDownTime.intValue();
//                stockOutPercentageData.setTotalDownTimeInMin(totalDownTimeInMin);
//                stockOutPercentageData.setStockOutPercentage(getStockOutPercentage(totalDownTimeInMin, totalOperationTimeInMin));
//            }
//            stockOutPercentageDataList.add(stockOutPercentageData);
//        }
//        kettleStockOutDao.insertList(stockOutPercentageDataList);
//    }

//    private Integer getProductOperationalTimeInMin(KettleUnitDetailDataClone unitDetailData) {
//        Date cafeOpening = unitDetailData.getCafeOpening();
//        Date cafeClosing = unitDetailData.getCafeClosing();
//
//        if (cafeOpening == null || cafeClosing == null) {
//            return 0;
//        }
//
//        long openingMillis = cafeOpening.getTime();
//        long closingMillis = cafeClosing.getTime();
//
//        // Handle "open 24 hours" case
//        if (openingMillis == closingMillis) {
//            return 24 * 60; // 1440 minutes
//        }
//
//        // Handle next-day closing
//        if (closingMillis < openingMillis) {
//            closingMillis += 24 * 60 * 60 * 1000; // add 1 day
//        }
//
//        long diffMillis = closingMillis - openingMillis;
//        return (int) (diffMillis / (60 * 1000)); // Convert to minutes
//    }
//
//    private BigDecimal getStockOutPercentage(Integer totalDownTimeInMin, Integer totalOperationTimeInMin) {
//        return SCMUtil.multiplyWithScale(
//                SCMUtil.divideWithScale(
//                        BigDecimal.valueOf(totalDownTimeInMin),
//                        BigDecimal.valueOf(totalOperationTimeInMin),
//                        6
//                ),
//                BigDecimal.valueOf(100),
//                6
//        );
//    }

//    private Map<String, KettleUnitDetailDataClone> getKettleUnitDetailDataClone() {
//        List<KettleUnitDetailDataClone> ud = kettleStockOutDao.getKettleUnitDetailDataClone();
//        ValidationUtil.requireNonEmptyCollection(ud, "No Unit clone data found");
//        return ud.stream()
//                .collect(Collectors.toMap(
//                        this::generateKey,
//                        Function.identity()
//                ));
//    }

    private Map<Integer, KettleProductDataClone> getKettleProductDataClone() {
        List<KettleProductDataClone> kettleProductDataClones = kettleStockOutDao.getKettleProductDataClone();
        ValidationUtil.requireNonEmptyCollection(kettleProductDataClones, "No Product clone data found");
        return kettleProductDataClones.stream()
                .collect(Collectors.toMap(KettleProductDataClone::getProductId, Function.identity()));
    }

//    /**
//     * clear and create a dump of products, units and unit product mapping
//     * @param unitId
//     */
//    private void clearAndCreateDump(Integer unitId) {
//        kettleStockOutDao.clearAndCreateProductDataCloneTable();
//        kettleStockOutDao.clearAndCreateUnitDetailDataCloneTable();
//        createUnitDetailDataClone(unitId);
//    }

//    private void createUnitDetailDataClone(Integer unitId) {
//        try {
//            Map<Integer, Unit> units = masterDataCache.getUnits();
//            MultiMap<Integer, ProductVO> allUnitsProducts = masterDataCache.getUnitProductTrimmedDetails();
//            IMap<Integer, Brand> brandMetaData = masterDataCache.getBrandMetaData();
//            Map<Integer, Product> productDetails = masterDataCache.getProductDetails();
//
//            List<CompletableFuture<Void>> asyncTasks = new ArrayList<>();
//
//            for (Unit unit : units.values()) {
//                if (UnitCategory.CAFE.equals(unit.getFamily())) {
//                    if (Objects.nonNull(unitId) && !(unit.getId() == unitId)) {
//                        continue;
//                    }
//                    Collection<ProductVO> productVOS = allUnitsProducts.get(unit.getId());
//                    if (CollectionUtils.isEmpty(productVOS)) {
//                        continue;
//                    }
//                    Set<Integer> mappedBrands = productVOS.stream().
//                            filter(e -> !CollectionUtils.isEmpty(e.getPrices())).map(ProductVO::getBrandId).collect(Collectors.toSet());
//                    List<Brand> brands = brandMetaData.values().stream().filter(e -> mappedBrands.contains(e.getBrandId())).collect(Collectors.toList());
//                    if (ValidationUtil.checkIsEmptyCollection(brands)) {
//                        continue;
//                    }
//                    for (Brand brand : brands) {
//                        // Submit task directly to threadpool
//                        CompletableFuture<Void> asyncTask = CompletableFuture.runAsync(() -> {
//                            try {
//                                kettleStockOutAsyncService.createKettleUnitDetailDataCloneAsyncByDate(unit, brand, productDetails, productVOS, SCMUtil.getCurrentBusinessDate()).get();
//                            } catch (Exception e) {
//                                log.error("Error in threadpool task for unit {} and brand {}", unit.getId(), brand.getBrandId(), e);
//                                // Don't rethrow - let this task fail silently and continue with others
//                            }
//                        }, taskExecutor);
//                        asyncTasks.add(asyncTask);
//                    }
//                }
//            }
//
//            // Wait for all async tasks to complete
//            if (!asyncTasks.isEmpty()) {
//                CompletableFuture<Void> allTasks = CompletableFuture.allOf(asyncTasks.toArray(new CompletableFuture[0]));
//                allTasks.join(); // This will block until all tasks are completed
//            }
//
//            log.info("Completed processing {} async tasks for unit detail data clone", asyncTasks.size());
//        } catch (Exception e) {
//            log.error("Error while creating unit detail data clone", e);
//        }
//    }

    // for product count and down time count map key
//    private String generateKey(KettleStockOutDateWiseData data) {
//        return SCMUtil.generateUniqueKey(
//                data.getUnitId().toString(),
//                data.getBrandId().toString(),
//                data.getInventoryTrackLevel()
//        );
//    }
//
//    private String generateKey(KettleUnitDetailDataClone data) {
//        return SCMUtil.generateUniqueKey(
//                data.getUnitId().toString(),
//                data.getBrandId().toString(),
//                data.getInventoryLevel()
//        );
//    }

    private String getEmailErrorBody() {
        return """
        <html>
        <body style="font-family: Arial, sans-serif; background-color: #f9f9f9; padding: 20px;">
            <div style="max-width: 600px; margin: auto; background-color: #ffffff; border-radius: 8px; padding: 20px; box-shadow: 0 0 10px rgba(0,0,0,0.1);">
                <h2 style="color: #d9534f;">🚨 Stock Out Percentage Data Creation Failed</h2>
                <p style="font-size: 14px; color: #333333;">
                    Dear Team,
                </p>
                <p style="font-size: 14px; color: #333333;">
                    The scheduled job to compute <strong>Stock Out Percentage</strong> has failed with the following error:
                </p>
                <pre style="background-color: #f5f5f5; color: #c7254e; padding: 10px; border-radius: 4px; font-size: 13px; overflow-x: auto;">
%s
                </pre>
                <p style="font-size: 14px; color: #333333;">
                    Please investigate the issue at the earliest.
                </p>
                <p style="font-size: 14px; color: #666666;">
                    Regards,<br/>
                    Reporting System (%s)
                </p>
            </div>
        </body>
        </html>
        """;
    }
}
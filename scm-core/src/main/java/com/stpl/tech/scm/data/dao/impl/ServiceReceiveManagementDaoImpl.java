package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.kettle.core.data.budget.vo.ExpenseField;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.data.dao.ServiceReceiveManagementDao;
import com.stpl.tech.scm.data.model.CostElementData;
import com.stpl.tech.scm.data.model.ServiceOrderData;
import com.stpl.tech.scm.data.model.ServiceOrderServiceReceiveMappingData;
import com.stpl.tech.scm.data.model.ServiceReceivedData;
import com.stpl.tech.scm.data.model.ServiceReceivedItemData;
import com.stpl.tech.scm.domain.model.PaymentRequest;
import com.stpl.tech.scm.domain.model.ServiceOrderStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Repository
public class ServiceReceiveManagementDaoImpl extends SCMAbstractDaoImpl implements ServiceReceiveManagementDao {

	private static final Logger LOG = LoggerFactory.getLogger(ServiceReceiveManagementDaoImpl.class);

	@Override
	public List<ServiceReceivedData> findServiceReceives(Integer bccId,Integer vendorId, Integer dispatchLcoationId, Date startDate,
			Date endDate, List<Integer> costElements, Integer serviceOrderId , boolean isShort) {
		if(serviceOrderId!=null){
			StringBuilder queryString = new StringBuilder("SELECT g FROM ServiceReceivedData g  ");
			queryString.append(" WHERE g.serviceReceivedId = :serviceReceivedId ");
			Query query = manager.createQuery(queryString.toString())
					.setParameter("serviceReceivedId", serviceOrderId);
			return query.getResultList();
		}
		else {
			StringBuilder queryString = new StringBuilder(
					"SELECT DISTINCT g FROM ServiceReceivedData g, ServiceReceivedItemData h  ");
			if(isShort){
				queryString.append(" left join fetch g.serviceOrderMappingList left join fetch g.paymentRequestData ");
			}
			queryString.append(
					" WHERE g.createdAt>=:startDate AND g.createdAt<=:endDate AND g.serviceReceivedId = h.serviceReceivedDataId");

			if (vendorId != null) {
				queryString.append(" AND g.vendorId = :vendorId ");
			}

			if (dispatchLcoationId != null) {
				queryString.append(" AND g.dispatchLocationId = :locationId ");
			}

			if(bccId != null){
				queryString.append(" AND h.businessCostCenterId =:bccIds");
			}
			/*
			 * if (costElements != null && !costElements.isEmpty()) {
			 * queryString.append(" AND h.costElementId IN :costElements"); }
			 */

			queryString.append(" ORDER BY g.createdAt DESC");

			Query query = manager.createQuery(queryString.toString()).setParameter("startDate", startDate)
					.setParameter("endDate", endDate);

			if (vendorId != null) {
				query.setParameter("vendorId", vendorId);
			}

			if (dispatchLcoationId != null) {
				query.setParameter("locationId", dispatchLcoationId);
			}

			if (bccId !=null) {
				query.setParameter("bccIds", bccId);
			}

			/*
			 * if (costElements != null && !costElements.isEmpty()) {
			 * query.setParameter("costElements", costElements); }
			 */

			return query.getResultList();
		}
	}

	@Override
	public List<ServiceReceivedData> getLinkedSrForSo(Integer vendorId, Integer dispatchLcoationId, Date startDate,
														 Date endDate, List<Integer> costElements, Integer paymentRequestId) {
		StringBuilder queryString = new StringBuilder(
				"SELECT DISTINCT g FROM ServiceReceivedData g, ServiceReceivedItemData h ");
		if (paymentRequestId != null) {
			queryString.append(" WHERE g.paymentRequestData.id = :paymentRequestId");
			Query query = manager.createQuery(queryString.toString()).setParameter("paymentRequestId",paymentRequestId);
			return query.getResultList();
		}
		else {
			queryString.append(
					" WHERE g.createdAt>=:startDate AND g.createdAt<=:endDate AND g.serviceReceivedId = h.serviceReceivedDataId");

			if (vendorId != null) {
				queryString.append(" AND g.vendorId = :vendorId ");
			}

			if (dispatchLcoationId != null) {
				queryString.append(" AND g.dispatchLocationId = :locationId ");
			}

			/*
			 * if (costElements != null && !costElements.isEmpty()) {
			 * queryString.append(" AND h.costElementId IN :costElements"); }
			 */

			queryString.append(" ORDER BY g.createdAt DESC");

			Query query = manager.createQuery(queryString.toString()).setParameter("startDate", startDate)
					.setParameter("endDate", endDate);

			if (vendorId != null) {
				query.setParameter("vendorId", vendorId);
			}

			if (dispatchLcoationId != null) {
				query.setParameter("locationId", dispatchLcoationId);
			}

			/*
			 * if (costElements != null && !costElements.isEmpty()) {
			 * query.setParameter("costElements", costElements); }
			 */

			return query.getResultList();
		}
	}

	@Override
	public List<ServiceReceivedData> findServiceReceivesForPayment(Integer vendorId, Integer companyId,
																   Integer deliveryStateId, Date startDate, Date endDate, List<Integer> costElements) {
		StringBuilder queryString = new StringBuilder(
				"SELECT DISTINCT g FROM ServiceReceivedData g, ServiceReceivedItemData h");
		queryString
				.append(" WHERE g.serviceReceiveStatus = :srStatus AND g.serviceReceivedId = h.serviceReceivedDataId");

		if (vendorId != null) {
			queryString.append(" AND g.vendorId = :vendorId ");
		}

		if (companyId != null) {
			queryString.append(" AND g.companyId = :companyId ");
		}

		if (deliveryStateId != null) {
			queryString.append(" AND g.deliveryStateId = :deliveryStateId ");
		}

		/*
		 * if (costElements != null && !costElements.isEmpty()) {
		 * queryString.append(" AND h.costElementId IN :costElements"); }
		 */

		queryString.append(" AND g.paymentRequestData IS NULL")
				.append(" AND g.createdAt>=:startDate AND g.createdAt<=:endDate");

		Query query = manager.createQuery(queryString.toString()).setParameter("startDate", startDate)
				.setParameter("endDate", endDate).setParameter("srStatus", ServiceOrderStatus.CREATED.name());

		if (vendorId != null) {
			query.setParameter("vendorId", vendorId);
		}
		if (companyId != null) {
			query.setParameter("companyId", companyId);
		}
		if (deliveryStateId != null) {
			query.setParameter("deliveryStateId", deliveryStateId);
		}
		/*
		 * if (costElements != null && !costElements.isEmpty()) {
		 * query.setParameter("costElements", costElements); }
		 */
		return query.getResultList();
	}

	@Override
	public List<ServiceReceivedData> findServiceReceivingForPaymentRequest(Integer paymentRequestId) {
		Query query = manager
				.createQuery("From ServiceReceivedData S WHERE S.paymentRequestData.id = :paymentRequestId");
		query.setParameter("paymentRequestId", paymentRequestId);
		return query.getResultList();
	}

	@Override
	public List<ServiceOrderData> findSoForPr(Integer paymentRequestId) {
		Query query = manager.createQuery(
				"select m.serviceOrderData from ServiceReceivedData srd, ServiceOrderServiceReceiveMappingData m where m.serviceReceivedData.serviceReceivedId = srd.serviceReceivedId " +
						"and srd.paymentRequestData.id = :paymentRequestId ");
		query.setParameter("paymentRequestId", paymentRequestId);
		return query.getResultList();
	}

	@Override
	public String getBudgetCategory(int costElementId) {
		CostElementData costElement = manager.find(CostElementData.class, costElementId);
		if (costElement.getSubCategory() != null) {
			return costElement.getSubCategory().getBudgetCategory();
		} else {
			return ExpenseField.ServiceRecordCategory.NONE.name();
		}
	}

	@Override
	public void removeServiceReceiveDrillDowns(Integer id) {
		Query query = manager.createQuery(
				"delete from ServiceReceivedItemDrilldownData srDt " +
						"where srDt.serviceReceivedItemDrilldownId = :id ");
		query.setParameter("id", id).executeUpdate();
	}

	public List<ServiceReceivedItemData> findSrItemsBySoId(Integer soId){
		StringBuilder queryString = new StringBuilder("SELECT  g FROM ServiceReceivedItemData g  ");
		queryString.append(" WHERE g.serviceOrderId = :serviceOrderId ");
		Query query = manager.createQuery(queryString.toString())
				.setParameter("serviceOrderId", soId);
		return query.getResultList();
	}

	@Override
	public List<ServiceReceivedItemData> findSrItemsBySoIds(Set<Integer> soIds) {
		try {
			Query query = manager.createQuery("FROM ServiceReceivedItemData s WHERE s.serviceOrderId IN(:soIds) ORDER BY s.itemId DESC");
			query.setParameter("soIds",soIds);
			return query.getResultList();
		}
		catch (Exception e) {
			LOG.error("Error Occurred while find SR Item data with SO Id's ...! :: ",e);
			return new ArrayList<>();
		}
	}

	@Override
	public List<Integer> getSrIds(Set<Integer> srIds) {
		try {
			Query query = manager.createQuery("SELECT s.serviceReceivedId FROM ServiceReceivedData s WHERE s.serviceReceivedId IN(:srIds) AND s.serviceReceiveStatus <>:status ORDER BY s.serviceReceivedId DESC");
			query.setParameter("srIds",srIds);
			query.setParameter("status",ServiceOrderStatus.CANCELLED.name());
			return query.getResultList();
		}
		catch (Exception e) {
			LOG.error("Error Occurred while find SR Item data with SO Id's ...! :: ",e);
			return new ArrayList<>();
		}
	}

	@Override
	public List<ServiceOrderData> getSoData(Set<Integer> soIds) {
		try {
			Query query = manager.createQuery("FROM ServiceOrderData s WHERE s.id IN(:soIds)");
			query.setParameter("soIds",soIds);
			return query.getResultList();
		}
		catch (Exception e) {
			LOG.error("Error while getting SO Data :: ",e);
			return new ArrayList<>();
		}
	}

	@Override
	public List<String> findSrByPrIds(PaymentRequest paymentRequest){
		try {
			Query query = manager.createNativeQuery("SELECT CONCAT(r.BUSINESS_COST_CENTER_NAME ,' (', b.BCC_CODE ,')' ) from SERVICE_RECEIVED_DATA s INNER JOIN SERVICE_RECEIVED_ITEM r \n" +
					"ON s.SERVICE_RECEIVED_ID = r.SERVICE_RECEIVED_ID INNER JOIN BUSINESS_COST_CENTER_DATA b ON b.BCC_ID = r.BUSINESS_COST_CENTER_ID WHERE s.PAYMENT_REQUEST_ID = :serviceReceivedDataId");
			query.setParameter("serviceReceivedDataId",paymentRequest.getPaymentRequestId());
			return query.getResultList();

		}
		catch (Exception e) {
			LOG.error("Error while getting SO Data :: ",e);
			return new ArrayList<>();
		}
	}

	@Override
	public List<ServiceOrderServiceReceiveMappingData> findSrsWithSoId(Integer soId) {
		Query query = manager.createQuery("FROM ServiceOrderServiceReceiveMappingData s WHERE s.serviceOrderData.id =:soId");
		query.setParameter("soId",soId);
		return query.getResultList();
	}

	@Override
	public List<ServiceReceivedData> findAllSrs(List<Integer> srIds) {
		Query query = manager.createQuery("FROM ServiceReceivedData s WHERE s.id IN(:srIds)");
		query.setParameter("srIds",srIds);
		return query.getResultList();
	}

	@Override
	public List<ServiceReceivedData> getSrFromSoIds(List<Integer> soIds) throws SumoException {
		try {
			Query query = manager.createQuery("FROM ServiceReceivedData sr where sr.serviceReceivedId in (select sri.serviceReceivedDataId from  ServiceReceivedItemData  sri where sri.serviceOrderId in (:soIds)) order by 1 desc ");
			query.setParameter("soIds", soIds);
			return query.getResultList();
		}catch (Exception e){
			LOG.info("############ Error in getSrFromSoIds , error : {}  ########################",e.getMessage());
			throw new SumoException(e);
		}

	}

	@Override
	public List<ServiceReceivedItemData> getSrItemFromSr(List<Integer> srIds) throws SumoException {
		try {
			Query query = manager.createQuery("FROM   ServiceReceivedItemData  sri where sri.serviceReceivedDataId in (:srIds)");
			query.setParameter("srIds", srIds);
			return query.getResultList();
		}catch (Exception e){
			LOG.info("############ Error in getSrFromSoIds , error : {}  ########################",e.getMessage());
			throw new SumoException(e);
		}
	}

	@Override
	public String getPrBudgetType(Integer prId) throws SumoException {
		try{
				 Query query = manager.createNativeQuery("select TYPE from  SERVICE_ORDER where SERVICE_ORDER_ID =\n" +
					 "(select SERVICE_ORDER_ID from SERVICE_RECEIVED_ITEM where SERVICE_RECEIVED_ID = (\n" +
					 "SELECT SERVICE_RECEIVED_ID FROM SERVICE_RECEIVED_DATA where PAYMENT_REQUEST_ID = :prId limit 1 \n" +
					 ") limit 1);");
			query.setParameter("prId",prId);
		    String type = (String) query.getSingleResult();
			return type;
		}catch(NoResultException ex){
			return null;
		}catch (Exception e){
			LOG.info("############ Error in get pr budget type , error : {}  ########################",e.getMessage());
			throw new SumoException(e);
		}

	}

	@Override
	public ServiceOrderData getSoFromPR(Integer prId) throws SumoException {
		try{
			Query query = manager.createNativeQuery("select SERVICE_ORDER_ID from  SERVICE_ORDER where SERVICE_ORDER_ID =\n" +
					"(select SERVICE_ORDER_ID from SERVICE_RECEIVED_ITEM where SERVICE_RECEIVED_ID = (\n" +
					"SELECT SERVICE_RECEIVED_ID FROM SERVICE_RECEIVED_DATA where PAYMENT_REQUEST_ID = :prId limit 1 \n" +
					") limit 1);");
			query.setParameter("prId",prId);
			Integer soId = (Integer) query.getSingleResult();
		    ServiceOrderData so = find(ServiceOrderData.class,soId);
		    return so;
		}catch(NoResultException ex){
			return null;
		}catch (Exception e){
			LOG.info("############ Error in get So From PR , error : {}  ########################",e.getMessage());
			throw new SumoException(e);
		}

	}

}

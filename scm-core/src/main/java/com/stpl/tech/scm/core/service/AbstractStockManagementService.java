package com.stpl.tech.scm.core.service;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.inventory.service.InventoryService;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.inventory.model.InventoryAction;
import com.stpl.tech.master.inventory.model.InventorySource;
import com.stpl.tech.master.inventory.model.ProductQuantityData;
import com.stpl.tech.master.inventory.model.QuantityResponseData;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.PriceManagementDao;
import com.stpl.tech.scm.data.dao.StockManagementDao;
import com.stpl.tech.scm.data.model.ProductionBookingMappingData;
import com.stpl.tech.scm.data.model.SCMWastageData;
import com.stpl.tech.scm.data.model.SCMWastageEventData;
import com.stpl.tech.scm.data.model.WastageDataDrilldown;
import com.stpl.tech.scm.data.model.WastageLimitLookup;
import com.stpl.tech.scm.domain.model.BookingConsumption;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.InventoryItemDrilldown;
import com.stpl.tech.scm.domain.model.PriceUpdateEntryType;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductionBooking;
import com.stpl.tech.scm.domain.model.WastageData;
import com.stpl.tech.scm.domain.model.WastageEvent;
import com.stpl.tech.scm.domain.model.WastageEventType;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Copyright (C) $today.year, Sunshine Teahouse Private Limited - All Rights
 * Reserved Unauthorized copying of this file, via any medium is strictly
 * prohibited Proprietary and confidential Created by shikhar on 09-08-2017.
 */
public abstract class AbstractStockManagementService extends AbstractResources {

	@Autowired
	private ProductionBookingService productionBookingService;

	@Autowired
	private MasterDataCache masterDataCache;

	@Autowired
	private SCMCache scmCache;

	@Autowired
	private EnvProperties props;

	@Autowired
	private WarehouseStockManagementService warehouseStockManagementService;

	private static final Logger LOG = LoggerFactory.getLogger(AbstractStockManagementService.class);

	public List<WastageEvent> addWastage(List<WastageEvent> wastageEventList) throws InventoryUpdateException, DataNotFoundException {
		List<WastageEvent> updatedWastageEventList = new ArrayList<>();
		for (WastageEvent wastage : wastageEventList) {
			WastageEventType type = wastage.getItems().get(0).getSkuId() == null ? WastageEventType.PRODUCT
					: WastageEventType.SKU;
			wastage.setType(type);
			SCMWastageEventData eventData = getDao().addWastageEvent(wastage);
			String empName = getMasterDataCache().getEmployee(eventData.getGeneratedBy());
			wastage = SCMDataConverter.convert(eventData, getScmCache(), empName);
			updatedWastageEventList.add(wastage);
			getPriceDao().reduceConsumable(wastage, false);
			updatePricesAndCost(wastage);
			saveWastageDataDrillDowns(wastage, eventData.getItems());
		}
		publishWastage(InventoryAction.REMOVE, updatedWastageEventList.get(0).getUnitId(), updatedWastageEventList);
		return updatedWastageEventList;
	}

	public List<WastageEvent> addManualWastage(List<WastageEvent> wastageEventList, boolean verifyInventory, Integer loggedInUser) throws InventoryUpdateException, DataNotFoundException, SumoException {
		List<WastageEvent> updatedWastageEventList = new ArrayList<>();
		List<Integer> limitByPassEmpIds = new ArrayList<>(Arrays.asList(125200, 140199));
		for (WastageEvent wastage : wastageEventList) {
			WastageEventType type = wastage.getItems().get(0).getSkuId() == null ? WastageEventType.PRODUCT
					: WastageEventType.SKU;
			wastage.setType(type);
			if (wastage.getGeneratedBy() <= 0 && Objects.nonNull(loggedInUser)) {
				wastage.setGeneratedBy(loggedInUser);
			}
			SCMWastageEventData eventData = getDao().addWastageEvent(wastage);
			String empName = wastage.getGeneratedBy() <= 0 ? null : getMasterDataCache().getEmployee(eventData.getGeneratedBy());
			wastage = SCMDataConverter.convert(eventData, getScmCache(), empName);
			List<String> autoBookedProducts = createBookingForWastage(eventData.getItems(), wastage);
			LOG.info("size of total products are : {}",eventData.getItems().size());
			LOG.info("size of auto booked  products are : {}",autoBookedProducts.size());
			wastage = SCMDataConverter.convert(eventData, getScmCache(), empName);
			wastage.setAutoBookedProducts(autoBookedProducts);
			getPriceDao().reduceConsumable(wastage, false);
			updatedWastageEventList.add(wastage);
			BigDecimal totalCost = updatePricesAndCostManual(wastage);
			if(!limitByPassEmpIds.contains(eventData.getGeneratedBy()) && verifyInventory) {
				validateWastageLimit(wastage,totalCost);
			}
			saveWastageDataDrillDowns(wastage, eventData.getItems());
		}
		publishWastage(InventoryAction.REMOVE, updatedWastageEventList.get(0).getUnitId(), updatedWastageEventList);
		return updatedWastageEventList;
	}

	private void validateWastageLimit(WastageEvent wastage, BigDecimal totalCost) throws SumoException {
		LOG.info("designation id is {}",wastage.getGeneratedBy());
		EmployeeBasicDetail employeeBasicDetail = masterDataCache.getEmployeeBasicDetail(wastage.getGeneratedBy());
		LOG.info("designation id is {}",employeeBasicDetail.getDesignationId());
		WastageLimitLookup limitLookup = getDao().findWastageLimitById(employeeBasicDetail.getDesignationId());
		if (limitLookup == null) {
//			throw new SumoException("No wastage Limit Found..!","No wastage limit found for designation Id : "+employeeBasicDetail.getDesignationId());
			LOG.info("No Limit Look up entry found..! Validating with maximum Wastage Limit ");
			LOG.info("Maximum Limit is {} and total cost is {}",SCMServiceConstants.WASTAGE_LIMIT,totalCost);
			if (totalCost.compareTo(SCMServiceConstants.WASTAGE_LIMIT) > 0) {
				throw new SumoException("Exceeded the Allowed Wastage Cost","Please Contact Your Superior..!");
			}
		}
		else {
			if (limitLookup.getDesignationId() != AppConstants.DESIGNATION_ADMIN_ID ) {
				LOG.info("Found Designation Id who is not an Admin..Validating with maximum Limit ");
				BigDecimal maximumLimit = new BigDecimal(limitLookup.getMaximumLimit());
				LOG.info("Maximum Limit is {} and total cost is {}",maximumLimit,totalCost);
				if (totalCost.compareTo(maximumLimit) > 0) {
					throw new SumoException("Exceeded the Allowed Wastage Cost","Please Contact Your Superior..!");
				}
			}
			else {
				LOG.info("Found Designation Id of Admin.");
			}
		}
	}

	private List<String> createBookingForWastage(List<SCMWastageData> wastageList, WastageEvent wastage) throws SumoException, DataNotFoundException, InventoryUpdateException {
		List<String > result = new ArrayList<>();
		List<ProductionBooking> productionBookingList = new ArrayList<>();
		List<String> errors = new ArrayList<>();
		Unit unit = masterDataCache.getUnit(wastage.getUnitId());
		LOG.info("Checking for the family type {}", unit.getFamily());
		if (SCMUtil.isKitchen(unit.getFamily())) {
			LOG.info("START CALCULATING CONSUMPTION FOR REQUESTED RECIPE ITEMS DURING ADD WASTAGE");
			for (SCMWastageData wastageEntry : wastageList) {
				HashMap<Integer, IdCodeName> map = new HashMap<>();
				ProductDefinition pd = scmCache.getProductDefinition(wastageEntry.getProduct().getProductId());
				if (pd != null) {
					if (pd.isRecipeRequired()) {
						LOG.info("Doing Auto Production Booking for Product : {}",pd.getProductName());
						productionBookingList = calculateConsumptions(wastage.getUnitId(), pd, wastageEntry.getQuantity().floatValue()
								, productionBookingList, map, errors,wastage);
						result.add(pd.getProductName());
					}
				}
			}
				LOG.info("CALCULATION OF CONSUMPTION FOR REQUESTED RECIPE ITEMS IS DONE DURING ADD WASTAGE");
				if (errors != null && errors.size() > 0) {
					String message = "Error while Updating Wastage Please Update Mapping For the Requested Products For Production Booking" + "\n"
							+ Arrays.toString(errors.toArray());
					SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "SUMO",
							SlackNotification.SUPPLY_CHAIN, message);
					throw new SumoException(message);
				}

				if (productionBookingList.size() > 0) {
					List<String> productionBookingErrors = new ArrayList<>();
					for (ProductionBooking productionBooking : productionBookingList) {
						try {
							productionBookingService.addBooking(productionBooking);
							LOG.info("BOOKING SUCCESSFULLY DONE FOR PRODUCTS DURING ADD WASTAGE!");
							for (SCMWastageData wastageEntry : wastageList) {
								if (wastageEntry.getProduct().getProductId().equals(productionBooking.getProductId())) {
									wastageEntry.setProductionId(productionBooking.getBookingId());
								}
							}
						} catch (Exception e) {
							LOG.info("PRODUCTION BOOKING CANNOT BE DONE DURING ADD WASTAGE!",e);
							productionBookingErrors.add(e.getMessage());
						}
					}
					if (productionBookingErrors != null && productionBookingErrors.size() > 0) {
						String message = "Error while Adding Wastage Please Update Price For the Requested Products For Production Booking" + "\n"
								+ Arrays.toString(productionBookingErrors.toArray());
						throw new SumoException(message);
					}
				}
				LOG.info("PRODUCTION BOOKING FOR REQUESTED RECIPE ITEMS IS DONE DURING ADD WASTAGE!");
			}
			return result;
		}

		private List<ProductionBooking>  calculateConsumptions(Integer generationUnitId, ProductDefinition pd, Float quantity,
															   List<ProductionBooking> productionBookingList, HashMap<Integer, IdCodeName> map,
															   List<String> errors, WastageEvent wastage) throws DataNotFoundException, SumoException {
			long startCalculateConsumptionItem = System.currentTimeMillis();
			LOG.info("CALCULATING CONSUMPTION FOR REQUESTED RECIPE {}", pd.getProductName());
			ProductionBooking productionBooking = productionBookingService.calculateConsumption(generationUnitId, pd.getProductId(), SCMUtil.convertToBigDecimal(quantity));
			List<BookingConsumption> bookingConsumptions = productionBooking.getBookingConsumption();
			setProductMap(bookingConsumptions, map, productionBooking, generationUnitId, errors);
			LOG.info("CONSUMPTION FOR REQUESTED RECIPE {} IS CALCULATED SUCCESSFULLY", pd.getProductName());
			if (errors.size() == 0) {
				setProductionBooking(bookingConsumptions, map);
				IdCodeName idCodeName = new IdCodeName();
				idCodeName.setName(wastage.getEmpName());
				idCodeName.setId(wastage.getGeneratedBy());
				productionBooking.setGeneratedBy(idCodeName);
				productionBooking.setExpiryDate(AppUtils.createExpiryDate(AppUtils.getCurrentTimestamp(), pd.getShelfLifeInDays()));
				productionBooking.setAutoBooking(SCMServiceConstants.SCM_CONSTANT_YES);
				productionBookingList.add(productionBooking);
				long endCalculateConsumptionItem = System.currentTimeMillis();
				LOG.info("Calculating Consumption Time for Product during wastage is : {}  is {} ms",pd.getProductName()  , endCalculateConsumptionItem - startCalculateConsumptionItem);
			}
			return productionBookingList;
		}


	public void setProductMap(List<BookingConsumption> consumptions, HashMap<Integer, IdCodeName> map, ProductionBooking booking, int unitId, List<String> errors) throws SumoException {
		boolean flag;
		for (BookingConsumption bookingConsumption : consumptions) {
			if (!map.containsKey(bookingConsumption.getProductId())) {
				flag = false;
				for (IdCodeName skuList : bookingConsumption.getAvailableSkuList()) {
					ProductionBookingMappingData mappingData = productionBookingService.productionUnitMappingItems(booking.getProductId(), unitId, skuList.getId(), booking.getProfile());
					if (mappingData != null) {
						flag = true;
						map.put(bookingConsumption.getProductId(), SCMUtil.generateIdCodeName(mappingData.getLinkedSkuId(), mappingData.getUnitOfMeasure(), mappingData.getLinkedSkuName()));
						break;
					}
				}
			} else {
				flag = true;
			}
			if (!flag) {
				String message = "Could Not Find Mapping For Product " + booking.getProductName()+ "\n";
				errors.add(message);
				break;
			}
			if (bookingConsumption.getBookingConsumption().size() > 0 && (bookingConsumption.isAutoProduction() || bookingConsumption.isMappedAutoProduction())) {
				setProductMap(bookingConsumption.getBookingConsumption(), map, booking, unitId, errors);
			}
		}
	}

	public void setProductionBooking(List<BookingConsumption> booking, Map<Integer, IdCodeName> map) {
		for (BookingConsumption bookingConsumption : booking) {
			IdCodeName skuData = map.get(bookingConsumption.getProductId());
			if(skuData != null) {
				bookingConsumption.setSkuId(skuData.getId());
				bookingConsumption.setSkuName(skuData.getName());
				bookingConsumption.setUnitOfMeasure(skuData.getCode());
			}
			if (bookingConsumption.getBookingConsumption().size() > 0 && (bookingConsumption.isAutoProduction() || bookingConsumption.isMappedAutoProduction())) {
				setProductionBooking(bookingConsumption.getBookingConsumption(), map);
			}
		}
	}

	private void saveWastageDataDrillDowns(WastageEvent wastage, List<SCMWastageData> list)
			throws InventoryUpdateException {
		HashMap<Integer, SCMWastageData> itemMap = new HashMap<>();

		for (SCMWastageData item : list) {
			itemMap.put(item.getWastageItemId(), item);
		}
		try {
			for (WastageData item : wastage.getItems()) {
				for (InventoryItemDrilldown drilldown : item.getDrillDowns()) {
					WastageDataDrilldown toDrilldown = new WastageDataDrilldown();
					toDrilldown.setWastageData(itemMap.get(item.getId()));
					toDrilldown.setQuantity(drilldown.getQuantity());
					toDrilldown.setPrice(drilldown.getPrice());
					toDrilldown.setAddTime(AppUtils.getCurrentTimestamp());
					toDrilldown.setExpiryDate(drilldown.getExpiryDate());
					getDao().add(toDrilldown, false);
				}
			}
		} catch (SumoException se) {
			LOG.error("Error While adding wastage drill down", se);
			throw new InventoryUpdateException(se);
		}
		getDao().flush();
	}

	public List<WastageEvent> verifyPrices(List<WastageEvent> wastageEventList) throws InventoryUpdateException {
		List<WastageEvent> updatedWastageEventList = new ArrayList<>();
		for (WastageEvent wastage : wastageEventList) {
			WastageEventType type = wastage.getItems().get(0).getSkuId() == null ? WastageEventType.PRODUCT
					: WastageEventType.SKU;
			wastage.setType(type);
			wastage.setInventoryType(
					WastageEventType.PRODUCT.equals(type) ? PriceUpdateEntryType.PRODUCT : PriceUpdateEntryType.SKU);
			updatedWastageEventList.add(getPriceDao().verifyPriceData(wastage));
		}
		return updatedWastageEventList;
	}

	/**
	 * @param finalEvent
	 */
	private void updatePricesAndCost(WastageEvent finalEvent) {
		for (WastageData data : finalEvent.getItems()) {
			SCMWastageData wastage = getDao().find(SCMWastageData.class, data.getId());
			wastage.setPrice(data.getPrice());
			wastage.setCost(AppUtils.multiplyWithScale10(wastage.getQuantity(), data.getPrice()));
			if (wastage.getTaxPercentage() != null) {
				wastage.setTax(AppUtils.percentOfWithScale10(wastage.getCost(), wastage.getTaxPercentage()));
			}
		}
	}

	private BigDecimal updatePricesAndCostManual(WastageEvent finalEvent) {
		BigDecimal totalCost = BigDecimal.ZERO;
		for (WastageData data : finalEvent.getItems()) {
			SCMWastageData wastage = getDao().find(SCMWastageData.class, data.getId());
			wastage.setPrice(data.getPrice());
			wastage.setCost(AppUtils.multiplyWithScale10(wastage.getQuantity(), data.getPrice()));
			totalCost = totalCost.add(wastage.getCost());
			if (wastage.getTaxPercentage() != null) {
				wastage.setTax(AppUtils.percentOfWithScale10(wastage.getCost(), wastage.getTaxPercentage()));
				totalCost = totalCost.add(wastage.getTax());
			}
		}
		return totalCost;
	}

	private void publishWastage(InventoryAction action, int unitId, List<WastageEvent> events) {
		try {
			List<ProductQuantityData> details = new ArrayList<>();
			for (WastageEvent data : events) {
				if (data.getLinkedKettleId() != null && data.getLinkedKettleId() > 0) {
					// Waste due to kettle orders is not published as orders are already punched
					continue;
				}
				for (WastageData wastage : data.getItems()) {
					Integer productId = wastage.getProduct().getProductId();
					details.add(new ProductQuantityData(productId, wastage.getQuantity(),
							wastage.getProduct().getUnitOfMeasure()));
				}
			}
			if (details.size() > 0) {
				QuantityResponseData response = new QuantityResponseData(unitId, details, action,
						InventorySource.WASTAGE, null, SCMUtil.getCurrentTimestamp());
				getInventoryService().publishInventorySQSFifo(getProperties().getInventoryQueuePrefix(), response);
			}
		} catch (Exception e) {
			LOG.error("Error while publishing inventory for wastage for unit " + unitId);
		}
	}

	protected void publishSCMWastage(InventoryAction action, int unitId, SCMWastageEventData event) {
		try {
			List<ProductQuantityData> details = new ArrayList<>();
			for (SCMWastageData wastage : event.getItems()) {
				Integer productId = wastage.getProduct().getProductId();
				details.add(new ProductQuantityData(productId, wastage.getQuantity(),
						wastage.getProduct().getUnitOfMeasure()));
			}
			if (details.size() > 0) {
				QuantityResponseData response = new QuantityResponseData(unitId, details, action,
						InventorySource.WASTAGE, event.getWastageId(), SCMUtil.getCurrentTimestamp());
				getInventoryService().publishInventorySQSFifo(getProperties().getInventoryQueuePrefix(), response);
			}
		} catch (Exception e) {
			LOG.error("Error while publishing inventory for wastage for unit " + unitId);
		}
	}

	public abstract StockManagementDao getDao();

	public abstract PriceManagementDao getPriceDao();

	public abstract SCMCache getScmCache();

	public abstract InventoryService getInventoryService();

	public abstract MasterDataCache getMasterDataCache();

	public abstract EnvProperties getProperties();
}

package com.stpl.tech.scm.data.model;

import com.stpl.tech.scm.domain.model.RiderActionEnum;
import com.stpl.tech.scm.domain.model.StockType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.math.BigDecimal;

@Entity
@Getter
@Setter
@Table(name = "RIDER_ROUTE_PLAN_ITEM_DATA")
@AllArgsConstructor
@NoArgsConstructor
public class RiderRoutePlanItemData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "RIDER_ROUTE_PLAN_ITEM_DATA_ID")
    private Integer riderRoutePlanItemDataId;

    @ManyToOne(targetEntity = RiderRoutePlanStepData.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "RIDER_ROUTE_PLAN_STEP_DATA_ID", nullable = false)
    private RiderRoutePlanStepData riderRoutePlanStepData;

    @Enumerated(EnumType.STRING)
    @Column(name = "RIDER_ACTION", nullable = false)
    private RiderActionEnum riderAction;

    @Column(name = "PRODUCT_ID", nullable = false)
    private Integer productId;

    @Column(name = "PACKAGING_ID", nullable = false)
    private Integer packagingId;

    @Column(name = "PROPOSED_QUANTITY", nullable = false)
    private BigDecimal proposedQuantity;

    @Column(name = "FINAL_QUANTITY")
    private BigDecimal finalQuantity;

    @Enumerated(EnumType.STRING)
    @Column(name = "STOCK_TYPE", nullable = false)
    private StockType stockType;

    @Column(name = "COMMENT", columnDefinition = "TEXT")
    private String comment;
}

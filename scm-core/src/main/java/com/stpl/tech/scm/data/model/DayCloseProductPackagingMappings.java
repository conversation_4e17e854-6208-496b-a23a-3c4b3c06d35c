package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.math.BigDecimal;

@Entity
@Table(name = "DAY_CLOSE_PRODUCT_PACKAGING_MAPPINGS")
public class DayCloseProductPackagingMappings {

    private Integer productPackagingMappingId;

    private StockTakeSumoDayCloseProducts sumoDayCloseProductItemId;

    private Integer packagingId;

    private BigDecimal quantity;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PRODUCT_PACKAGING_MAPPING_ID", unique = true, nullable = false)
    public Integer getProductPackagingMappingId() {
        return productPackagingMappingId;
    }

    public void setProductPackagingMappingId(Integer productPackagingMappingId) {
        this.productPackagingMappingId = productPackagingMappingId;
    }

    @ManyToOne(fetch = FetchType.LAZY, targetEntity = StockTakeSumoDayCloseProducts.class)
    @JoinColumn(name = "DAY_CLOSE_PRODUCT_ITEM_ID", nullable = false)
    public StockTakeSumoDayCloseProducts getSumoDayCloseProductItemId() {
        return sumoDayCloseProductItemId;
    }

    public void setSumoDayCloseProductItemId(StockTakeSumoDayCloseProducts sumoDayCloseProductItemId) {
        this.sumoDayCloseProductItemId = sumoDayCloseProductItemId;
    }

    @Column(name = "PACKAGING_ID")
    public Integer getPackagingId() {
        return packagingId;
    }

    public void setPackagingId(Integer packagingId) {
        this.packagingId = packagingId;
    }

    @Column(name = "QUANTITY")
    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }
}

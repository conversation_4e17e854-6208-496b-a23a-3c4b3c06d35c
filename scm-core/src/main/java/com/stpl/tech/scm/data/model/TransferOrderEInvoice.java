package com.stpl.tech.scm.data.model;

import javax.persistence.*;

@Entity
@Table(name = "TRANSFER_ORDER_E_INVOICE")
public class TransferOrderEInvoice {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "TRANSFER_ORDER_E_INVOICE_ID")
    private Integer transferOrderEInvoiceId;

    @Column(name = "TRANSFER_ORDER_ID")
    private Integer transferOrderId;

    @Column(name = "IRN_NO")
    private String irnNo;

    @Column(name = "ACK_NO")
    private String ackNo;

    @Column(name = "BAR_CODE_ID")
    private Integer barCodeId;

    @Column(name = "SIGNED_QR_CODE")
    private String signedQrCode;

    @Column(name = "INVOICE_URL")
    private String invoiceUrl;

    @Column(name = "STATUS")
    private String status;


    public Integer getTransferOrderEInvoiceId() {
        return this.transferOrderEInvoiceId;
    }

    public void setTransferOrderEInvoiceId(Integer transferOrderEInvoiceId) {
        this.transferOrderEInvoiceId = transferOrderEInvoiceId;
    }

    public Integer getTransferOrderId() {
        return this.transferOrderId;
    }

    public void setTransferOrderId(Integer transferOrderId) {
        this.transferOrderId = transferOrderId;
    }

    public String getIrnNo() {
        return this.irnNo;
    }

    public void setIrnNo(String irnNo) {
        this.irnNo = irnNo;
    }

    public String getAckNo() {
        return this.ackNo;
    }

    public void setAckNo(String ackNo) {
        this.ackNo = ackNo;
    }

    public Integer getBarCodeId() {
        return this.barCodeId;
    }

    public void setBarCodeId(Integer barCodeId) {
        this.barCodeId = barCodeId;
    }

    public String getSignedQrCode() {
        return this.signedQrCode;
    }

    public void setSignedQrCode(String signedQrCode) {
        this.signedQrCode = signedQrCode;
    }

    public String getInvoiceUrl() {
        return this.invoiceUrl;
    }

    public void setInvoiceUrl(String invoiceUrl) {
        this.invoiceUrl = invoiceUrl;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}

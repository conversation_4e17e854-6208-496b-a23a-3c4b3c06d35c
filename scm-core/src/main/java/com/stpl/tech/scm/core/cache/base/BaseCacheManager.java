package com.stpl.tech.scm.core.cache.base;

import com.stpl.tech.scm.data.redis.service.CacheRefreshService;
import com.stpl.tech.scm.data.redis.service.RedisHashManager;
import com.stpl.tech.scm.data.redis.service.RedisHashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Slf4j
public abstract class BaseCacheManager<K, V> implements CacheManager<K, V> {

    protected final RedisHashManager redisHashManager;
    protected final CacheRefreshService cacheRefreshService;
    protected final ThreadPoolTaskExecutor taskExecutor;

    protected BaseCacheManager(RedisHashManager redisHashManager,
                               CacheRefreshService cacheRefreshService,
                               ThreadPoolTaskExecutor taskExecutor) {
        this.redisHashManager = redisHashManager;
        this.cacheRefreshService = cacheRefreshService;
        this.taskExecutor = taskExecutor;
    }

    // -------------------------------
    // Abstract methods (customizable)
    // -------------------------------
    protected abstract RedisHashMap<K, V> getRedisHash();
    protected abstract Map<K, V> loadFromSource();


    // -------------------------------
    // Shared Implementations
    // -------------------------------
    @Override
    public void forceRefresh() {
        log.info("Refreshing {} cache", this.getClass().getSimpleName());
        Map<K, V> data = loadFromSource();
        getRedisHash().clear();
        getRedisHash().putAll(data);
    }

    @Override
    public CompletableFuture<Void> refresh(boolean forceRefresh) {
        return CompletableFuture.runAsync(() -> {
            long size = getRedisHash().size();
            if (forceRefresh || size == 0) {
                forceRefresh();
            } else {
                log.info("{} cache already exists with size {}", this.getClass().getSimpleName(), size);
            }
        }, taskExecutor);
    }

    @Override
    public Map<K, V> getAll() {
        return getRedisHash();
    }

    @Override
    public V get(K key) {
        return getRedisHash().get(key);
    }

    @Override
    public void save(V value) {
        getRedisHash().put(extractKey(value), value);
    }

    // Domain-specific key extractor
    protected abstract K extractKey(V value);
}

/**
 *
 */
package com.stpl.tech.scm.core.service;

import com.stpl.tech.master.domain.model.ApiResponse;
import com.stpl.tech.master.domain.model.IdIndex;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.UnitBusinessType;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.VendorRegistrationException;
import com.stpl.tech.scm.core.util.model.SkuDataAndTaxData;
import com.stpl.tech.scm.data.model.SkuPackagingTaxMapping;
import com.stpl.tech.scm.data.model.VendorContractSoInfo;
import com.stpl.tech.scm.data.model.WorkOrderData;
import com.stpl.tech.scm.data.transport.model.VendorOTPValidationDomain;
import com.stpl.tech.scm.domain.model.VendorContractStatus;
import com.stpl.tech.scm.domain.model.VendorContractV2Dto;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.IdCodeNameStatus;
import com.stpl.tech.scm.domain.model.PreviousPricingDataVO;
import com.stpl.tech.scm.domain.model.SkuData;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.SkuPriceDetail;
import com.stpl.tech.scm.domain.model.SkuPriceUpdate;
import com.stpl.tech.scm.domain.model.SkuPriceUpdateDetail;
import com.stpl.tech.scm.domain.model.UnitVendorSkuMapping;
import com.stpl.tech.scm.domain.model.UpdateUnitVendorSkuMapping;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorType;
import com.stpl.tech.scm.domain.model.WorkOrder;
import com.stpl.tech.scm.domain.model.unitSkuMappingDetail;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.TemplateRenderingException;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 *
 */
public interface SkuMappingService {

	public boolean updatePrices(SkuPriceUpdate data) throws SumoException;
	public boolean updatePricesV2(SkuPriceUpdate data) throws SumoException;

	public List<IdCodeNameStatus> searchSkuMappingsForUnit(int unitId);

	public List<IdCodeNameStatus> searchUnitMappingsForSku(int skuId);

	public boolean updateSkuMappingsForUnit(int employeeId, String name, int unitId, List<Integer> skuIds);

	public boolean updateUnitMappingsForSku(int employeeId, String name, int skuId, List<Integer> unitIds);

    public boolean updateUnitMappingsForSku(int employeeId, String name, int skuId, List<Integer> unitIds, Map<Integer, String> inventoryListwithMappedSkus,
											Map<Integer,String> productionUnitMapped, Map<Integer, String> mappedPackagingIds , Map<Integer,String> mappedTaxCodes,
											Map<Integer,Date> mappedVoDiscontinuedFrom, Map<Integer,Date> mappedRoDiscontinuedFrom);

    public boolean updateSkuMappingsForUnit(int employeeId, String name, int unitId, List<Integer> skuIds, Map<Integer, String> mappedInventoryWithSkuId,Map<Integer,String> productionUnitMapped,
											Map<Integer, String> mappedPackagingIds , Map<Integer,String> mappedTaxCodes,
											Map<Integer,Date> mappedVoDiscontinuedFrom, Map<Integer,Date> mappedRoDiscontinuedFrom);

    public boolean updateSkuProfiles(Map<Integer, String> skuListWithInventoryListId, int unitId, String profiles);

    public List<IdCodeNameStatus> searchSkuMappingsForVendor(int vendorId);

	public List<IdCodeNameStatus> searchVendorMappingsForSku(int skuId);

	public List<IdCodeNameStatus> searchVendorMappingsForBusiness(int businessId);

	public boolean updateVendorSkuMapping(int employeeId, String name, int vendorId, int skuId, String status);

	public List<IdCodeNameStatus> allUnits();

	public List<IdCodeNameStatus> allSKU();

	public List<IdCodeNameStatus> allVendors();

	public List<IdCodeNameStatus> allVendorsWithBusinessType(List<VendorType> vendorTypes);

	public List<IdCodeName> getBusinessTypes();

	public List<unitSkuMappingDetail> getSkusProfileForUnit(int unit, List<Integer> sku);

	/**
	 * @param employeeId
	 * @param employeeName
	 * @param id
	 * @param skuIds
	 * @return
	 */
	public boolean addSkuMappingsForVendor(int employeeId, String employeeName, int id, List<IdCodeName> skuIds);

	//@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean addSkuMappingsForVendorAlias(IdCodeName request) throws SumoException;

	/**
	 * @param employeeId
	 * @param employeeName
	 * @param id
	 * @param mappingIds
	 * @return
	 */
	public boolean addVendorMappingsForSku(int employeeId, String employeeName, int id, List<Integer> mappingIds);


	public boolean addVendorMappingsForBusiness(int employeeId, String employeeName, int skuId, List<Integer> vendorIds);

	/**
	 * @param skuId
	 * @param deliveryLocationId
	 * @return
	 */
	public List<SkuPriceDetail> searchPricesBySku(int skuId, Integer deliveryLocationId);

	public List<SkuPriceDetail> searchPricesByVendor(int vendorId, Integer locationId);

	/**
	 * @param data
	 * @return
	 */
	public boolean cancelPriceUpdate(SkuPriceUpdate data);

	/**
	 * @param data
	 * @return
	 */
	public boolean updatePriceStatus(SkuPriceUpdate data);

	public List<UnitVendorSkuMapping> searchSkuMappingsForVendorAndUnit(int unitId, int vendorId);

	public List<UnitVendorSkuMapping> updateSkuMappingsForVendorAndUnit(UpdateUnitVendorSkuMapping data);

	public List<SkuDataAndTaxData> getSkuAndTaxData(int dispatchId,int unitId,int vendorId , Boolean toPickFromUnitMapping);

	public List<SkuPriceDetail> getSkuPackagingPriceForVendorsForUnit(int unitId, Set<Integer> vendorIds, Integer deliveryLocationId);

	public boolean addPrice(SkuPriceUpdate data);

	public boolean addPriceV2(SkuPriceUpdate data) throws SumoException;

	public Collection<VendorDetail> searchVendorsForUnit(int unitId);

	public Collection<IdCodeName> searchVendorsForUnitTrimmed(int unitId);

    public Collection<SkuData> getSkusMappedToVendor(int unitId, int vendorId);

	public List<SkuPriceDetail> getSkuPackagingPriceForUnit(int unitId, Integer deliveryLocationId);

    public List<IdCodeNameStatus> unitsByBusinessType(UnitBusinessType businessType);

	public List<Integer> getSubCategories(List<Integer> profiles);

	public List<String> getUnitDistanceMapping(int firstUnitId,int secondUnitId);

	public boolean updateMappingsForUnit(int firstUnitId, Integer firstMappingId, BigDecimal firstDistance, int secondUnitId,
			Integer secondMappingId, BigDecimal secondDistance) throws SumoException;

	public boolean updateLeadTime(IdIndex data);

	public boolean updateSkuLeadTime(int vendorId,int leadTime);

	public Map<Integer,Integer> getSibLingSkusInventoryListId(List<Integer> unitIds , Integer skuId);

	public Map<Integer, SkuPackagingTaxMapping> getAllUnitSkuPackagingTaxMappingByStatus(Integer skuId , Integer packagingId , List<String> statuses);

	public Boolean updateUnitSkuPackagingTaxMapping(Integer skuId, Integer packagingId, Map<Integer, String> unitToTaxMap, Integer employeeId);

	public String getHsnCodeFromUnitSkuMapping(SkuDefinition sku , Integer unitId);


	Boolean converUnitDistanceToZipCodeDistance() throws SumoException;

    boolean generateSkuPriceUpdateRequest(List<SkuPriceUpdate> data) throws SumoException;

//	List<SkuPriceDetail> getVendorPriceChange(int vendorId, String location);

	boolean processPriceRequestForVendor(SkuPriceUpdateDetail data);

    List<SkuPriceDetail> previewVendorPriceChange(Integer vendorId);

	List<VendorContractV2Dto> getVendorContractV2(Integer vendorId, String status, Date startDate, Date endDate, Integer vendorContractId);

	ApiResponse getWorkOrdersByContractId(Integer contractId) throws SumoException;

	ApiResponse getItemsByWoId(Integer workOrderId) throws SumoException;

	boolean cancelVendorContractV2(Integer workOrderId, Integer loggedInUser) throws SumoException;

	DocumentDetail generateContractUsingTemplateV2(Integer workOrderId, Integer templateId) throws SumoException, TemplateRenderingException;

	String getContractDocument(Integer documentId) throws SumoException;

	boolean triggerVendorContractMailV2(Integer workOrderId, WorkOrderData woData) throws SumoException, EmailGenerationException;

	boolean vendorAcceptanceV2(WorkOrder workOrder, Integer loggedInUser, HttpServletRequest request) throws SumoException, TemplateRenderingException, EmailGenerationException, VendorRegistrationException;

	void applyContract();

    boolean triggerEmailOtpForVendor(Integer vendorId) throws SumoException;
    boolean triggerEmailOtpForEmployee(Integer employeeId) throws SumoException;

	boolean validateVendorOtp(VendorOTPValidationDomain otp) throws SumoException;

	WorkOrder validateRequestV2(String token) throws UnsupportedEncodingException, VendorRegistrationException, SumoException;

	VendorContractSoInfo validateSoContractRequest(String token) throws UnsupportedEncodingException, VendorRegistrationException;

	Integer saveDigitalSignature(MultipartFile file, Integer woId) throws SumoException;

	void expiryContract();

    Map<Integer,List<SkuDefinition>> getSkuWrtCategory();

	WorkOrder getSkuPrices(Integer vendorId, Integer userId) throws SumoException;

    boolean saveVendorContract(WorkOrder workOrder, Integer loggedInUser) throws SumoException;

	public List<WorkOrder> getWorkOrders(Integer userId);

    WorkOrder findSkuPriceMapping(Integer woId, String status) throws SumoException;

    boolean submitPriceApprovals(WorkOrder workOrder, Integer loggedInUser, HttpServletRequest request) throws SumoException;

    Map<String, String> getContractStatus(Integer vendorId);

	Integer uploadDocument(String mimeType, Integer userId, MultipartFile file);

    List<PreviousPricingDataVO> getPreviousPricesOfSkuByLocation(Integer skuId, Integer deliveryLocationId, Integer packagingId);
    List<PreviousPricingDataVO> getPreviousPricesOfSkuForCustomer(Integer skuId, Integer packagingId);

	Map<Integer, List<Pair<Integer, String>>> getUnitsByDeliveryLocationIds(List<Integer> locationIds);

	Boolean vendorActionOnWOMail(String token, HttpServletRequest request, VendorContractStatus action) throws Exception;

}


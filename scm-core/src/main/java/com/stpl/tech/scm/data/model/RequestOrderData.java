package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

/**
 * Created by <PERSON><PERSON> on 08-06-2016.
 */
@Entity
@Table(name = "REQUEST_ORDER")
public class RequestOrderData implements Cloneable {

    private Integer id;
    private Date generationTime;
    private Date lastUpdateTime;
    private int requestUnitId;
    private int fulfillmentUnitId;
    private int requestCompanyId;
    private int fulfillmentCompanyId;
    private int generatedBy;
    private Integer lastUpdatedBy;
    private String status;
    private Date fulfillmentDate;
    private String isSpecialOrder = "N";
    private String assetOrder = "N";
    private String alternateF9Order = "N";
    private String comment;
    private BigDecimal totalAmount;
    private ReferenceOrderData referenceOrderData;
    private RequestOrderData childRO;
    private String budgetApplied;
    private String budgetReason;

    private List<RequestOrderItemData> requestOrderItemDatas = new ArrayList<RequestOrderItemData>(0);
    private Set<RequestOrderData> parentRequestOrderDatas = new HashSet<RequestOrderData>(0);
    private String transferType;

    private String tag;

    private Integer vendorId;
    private String vendorName;
    private Date notificationTime;
    private String isNotified;
    private String notificationTypes;
    private Integer numberOfDays;
    private String raiseBy;
    private String type;
    private String bulkOrder = "N";

    private String specializedUrgentOrder ="N";

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "REQUEST_ORDER_ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "GENERATION_TIME", nullable = false)
    public Date getGenerationTime() {
        return generationTime;
    }

    public void setGenerationTime(Date generationTime) {
        this.generationTime = generationTime;
    }

    @Column(name = "REQUEST_UNIT_ID", nullable = false)
    public int getRequestUnitId() {
        return requestUnitId;
    }

    public void setRequestUnitId(int requestUnitId) {
        this.requestUnitId = requestUnitId;
    }

    @Column(name = "GENERATED_BY", nullable = false)
    public int getGeneratedBy() {
        return generatedBy;
    }

    public void setGeneratedBy(int generatedBy) {
        this.generatedBy = generatedBy;
    }

    @Column(name = "REQUEST_ORDER_STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "FULFILLMENT_UNIT_ID", nullable = false)
    public int getFulfillmentUnitId() {
        return fulfillmentUnitId;
    }

    public void setFulfillmentUnitId(int fulfillmentUnitId) {
        this.fulfillmentUnitId = fulfillmentUnitId;
    }

    @Column(name = "FULFILLMENT_DATE", nullable = false)
    public Date getFulfillmentDate() {
        return fulfillmentDate;
    }

    public void setFulfillmentDate(Date fulfillmentDate) {
        this.fulfillmentDate = fulfillmentDate;
    }

    @Column(name = "COMMENT", nullable = true, length = 1000)
    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    @Column(name = "LAST_UPDATE_TIME", nullable = false)
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "REFERENCE_ORDER_ID", nullable = true)
    public ReferenceOrderData getReferenceOrderData() {
        return referenceOrderData;
    }

    public void setReferenceOrderData(ReferenceOrderData referenceOrderData) {
        this.referenceOrderData = referenceOrderData;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "requestOrderData")
    public List<RequestOrderItemData> getRequestOrderItemDatas() {
        return requestOrderItemDatas;
    }

    public void setRequestOrderItemDatas(List<RequestOrderItemData> requestOrderItemDatas) {
        this.requestOrderItemDatas = requestOrderItemDatas;
    }

    @Column(name = "TOTAL_AMOUNT", nullable = true)
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    @Column(name = "LAST_UPDATED_BY", nullable = true)
    public Integer getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(Integer lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    @Column(name = "IS_SPECIAL_ORDER", nullable = false)
    public String getIsSpecialOrder() {
        return isSpecialOrder;
    }

    public void setIsSpecialOrder(String isSpecialOrder) {
        this.isSpecialOrder = isSpecialOrder;
    }


    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL, optional = true)
    @JoinColumn(name="CHILD_REQUEST_ORDER_ID")
    public RequestOrderData getChildRO() {
        return childRO;
    }

    public void setChildRO(RequestOrderData childRO) {
        this.childRO = childRO;
    }


    @OneToMany(fetch = FetchType.LAZY, mappedBy = "childRO", orphanRemoval = true)
    public Set<RequestOrderData> getParentRequestOrderDatas() {
        return parentRequestOrderDatas;
    }

    public void setParentRequestOrderDatas(Set<RequestOrderData> parentRequestOrderDatas) {
        this.parentRequestOrderDatas = parentRequestOrderDatas;
    }

    @Column(name = "ASSET_ORDER", nullable = true)
	public String getAssetOrder() {
		return assetOrder;
	}

	public void setAssetOrder(String assetOrder) {
		this.assetOrder = assetOrder;
	}

    @Column(name = "ALTERNATE_F9_ORDER", nullable = true)
    public String getAlternateF9Order() {
        return alternateF9Order;
    }

    public void setAlternateF9Order(String alternateF9Order) {
        this.alternateF9Order = alternateF9Order;
    }

    @Column(name = "REQUEST_COMPANY_ID", nullable = true)
	public int getRequestCompanyId() {
		return requestCompanyId;
	}

	public void setRequestCompanyId(int requestCompanyId) {
		this.requestCompanyId = requestCompanyId;
	}

	@Column(name = "FULFILLMENT_COMPANY_ID", nullable = true)
	public int getFulfillmentCompanyId() {
		return fulfillmentCompanyId;
	}

	public void setFulfillmentCompanyId(int fulfillmentCompanyId) {
		this.fulfillmentCompanyId = fulfillmentCompanyId;
	}

	@Column(name = "BUDGET_APPLIED", nullable = true)
	public String getBudgetApplied() {
		return budgetApplied;
	}

	public void setBudgetApplied(String budgetApplied) {
		this.budgetApplied = budgetApplied;
	}

	@Column(name = "BUDGET_REASON", nullable = true)
	public String getBudgetReason() {
		return budgetReason;
	}

	public void setBudgetReason(String budgetReason) {
		this.budgetReason = budgetReason;
	}


    public void setTransferType(String transferType) {
        this.transferType = transferType;
    }

    @Column(name = "TRANSFER_TYPE", nullable = false)
    public String getTransferType() {
        return transferType;
    }

    @Column(name = "SEARCH_TAG", nullable = true)
    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    @Column(name = "VENDOR_ID", nullable = true)
	public Integer getVendorId() {
		return vendorId;
	}

	public void setVendorId(Integer vendorId) {
		this.vendorId = vendorId;
	}

    @Column(name = "NOTIFICATION_TIME", nullable = true)
	public Date getNotificationTime() {
		return notificationTime;
	}

	public void setNotificationTime(Date notificationTime) {
		this.notificationTime = notificationTime;
	}

    @Column(name = "IS_NOTIFIED", nullable = true)
	public String getIsNotified() {
		return isNotified;
	}

	public void setIsNotified(String isNotified) {
		this.isNotified = isNotified;
	}

    @Column(name = "NOTIFICATION_TYPES", nullable = true)
	public String getNotificationTypes() {
		return notificationTypes;
	}

	public void setNotificationTypes(String notificationTypes) {
		this.notificationTypes = notificationTypes;
	}

    @Column(name = "VENDOR_NAME", nullable = true)
	public String getVendorName() {
		return vendorName;
	}

	public void setVendorName(String vendorName) {
		this.vendorName = vendorName;
	}

    @Column(name ="NUMBER_OF_DAYS",nullable = true )//nullable = false
    public Integer getNumberOfDays() {
        return numberOfDays;
    }

    public void setNumberOfDays(Integer numberOfDays) {
        this.numberOfDays = numberOfDays;
    }

    @Column(name="RAISE_BY",nullable = false)
    public String getRaiseBy() {
        return raiseBy;
    }

    public void setRaiseBy(String raiseBy) {
        this.raiseBy = raiseBy;
    }

    @Column(name="TYPE")
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Column(name="BULK_ORDER")
    public String getBulkOrder() {
        return bulkOrder;
    }

    public void setBulkOrder(String bulkOrder) {
        this.bulkOrder = bulkOrder;
    }

    @Column(name="SPECIALIZED_URGENT_ORDER")
    public String getSpecializedUrgentOrder() {
        return specializedUrgentOrder;
    }

    public void setSpecializedUrgentOrder(String specializedUrgentOrder) {
        this.specializedUrgentOrder = specializedUrgentOrder;
    }
}

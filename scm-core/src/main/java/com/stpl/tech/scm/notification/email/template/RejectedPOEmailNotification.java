package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

public class RejectedPOEmailNotification extends EmailNotification {
    private Date deliveryDate;
    private VendorPOEmailNotificationTemplate vendorPOEmailNotificationTemplate;
    private EnvType envType;
    private String[] emails;
    private String subjectOfEmail;

    public RejectedPOEmailNotification() {
    }

    public RejectedPOEmailNotification(VendorPOEmailNotificationTemplate vendorPOEmailNotificationTemplate,
                                              EnvType envType, String[] emails, Date deliveryDate) {
        this.vendorPOEmailNotificationTemplate = vendorPOEmailNotificationTemplate;
        this.envType = envType;
        this.emails = emails;
        this.deliveryDate = deliveryDate;
    }

    @Override
    public String[] getToEmails() {
        if (SCMUtil.isDev(envType)) {
            return new String[] { "<EMAIL>" };
        } else {
            Set<String> mails = new HashSet<>();
            Arrays.asList(emails).forEach(email -> mails.add(email)); // adding vendor CC emails
            String unitEmail = vendorPOEmailNotificationTemplate.getDeliveryUnit().getUnitEmail();
            if(unitEmail!=null){
                mails.add(unitEmail);
            }
            String[] simpleArray = new String[mails.size()];
            return mails.toArray(simpleArray);
        }
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        subjectOfEmail = String.format("Rejected Purchase Order From Chaayos(%s) to %s for %s",
                vendorPOEmailNotificationTemplate.getDeliveryUnit().getName(),
                vendorPOEmailNotificationTemplate.getVendorDetail().getEntityName(),
                SCMUtil.getFormattedTime(this.deliveryDate, "EEE dd MMM yyyy"));
        if (SCMUtil.isDev(envType)) {
            subjectOfEmail = " [DEV] : " + subjectOfEmail;
        }
        return subjectOfEmail;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return vendorPOEmailNotificationTemplate.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }


    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}

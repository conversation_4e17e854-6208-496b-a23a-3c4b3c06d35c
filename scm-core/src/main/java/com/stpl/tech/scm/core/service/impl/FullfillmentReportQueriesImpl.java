package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.scm.core.service.FullfillmentReportQueries;
import com.stpl.tech.util.EnvType;
import org.springframework.stereotype.Service;

import java.sql.Time;

@Service
public class FullfillmentReportQueriesImpl implements FullfillmentReportQueries {
    @Override
    public String getFullfillmentQueryLastThirtyDay() {
        return "SELECT \n" +
                "    T.*, \n" +
                "    (CASE WHEN T.FINAL_RECEIVED_QUANTITY > T.REQUESTED_ABSOLUTE_QUANTITY THEN T.REQUESTED_ABSOLUTE_QUANTITY ELSE T.FINAL_RECEIVED_QUANTITY END) AS FINAL_RECEIVED_QUANTITY, \n" +
                "    (CASE WHEN T.FINAL_RECEIVED_QUANTITY > T.REQUESTED_ABSOLUTE_QUANTITY THEN T.REQUESTED_ABSOLUTE_QUANTITY ELSE T.FINAL_RECEIVED_QUANTITY END / T.REQUESTED_ABSOLUTE_QUANTITY) * 100 AS FULLFILLMENT_PERCENTAGE,\n" +
                "   (LEAST(T.ON_TIME_FULLFILLMENT_QUANTITY, T.REQUESTED_ABSOLUTE_QUANTITY) / T.REQUESTED_ABSOLUTE_QUANTITY) * 100 AS ON_TIME_FULLFILLMENT_PERCENTAGE,\n" +
                "   (LEAST(T.ON_DATE_FULLFILLMENT_QUANTITY, T.REQUESTED_ABSOLUTE_QUANTITY) / T.REQUESTED_ABSOLUTE_QUANTITY) * 100 AS ON_DATE_FULLFILLMENT_PERCENTAGE,\n" +
                "    (RAISED_ON_TIME_QUANTITY / TOTAL_ROS) * 100 AS ON_TIME_RO_RAISED_PERCENTAGE,\n" +
                "    (RAISED_ON_DATE_QUANTITY / TOTAL_ROS) * 100 AS ON_DATE_RO_RAISED_PERCENTAGE,\n" +
                "    (DELAY_RAISED_QUANTITY / TOTAL_ROS) * 100 AS DELAY_RO_RAISED_PERCENTAGE," +
                "    (CASE\n" +
                "        WHEN\n" +
                "            T.PRODUCT_ID IN (SELECT \n" +
                "                    umsm.SCM_PRODUCT_ID\n" +
                "                FROM\n" +
                "                    KETTLE_MASTER_DUMP.UNIT_PRODUCT_MAPPING upm,\n" +
                "                    KETTLE_MASTER_DUMP.UNIT_PRODUCT_PRICING upp,\n" +
                "                    KETTLE_MASTER_DUMP.REF_LOOKUP rl,\n" +
                "                    KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd,\n" +
                "                    KETTLE_MASTER_DUMP.MENU_TO_SCM_PRODUCT_MAP umsm\n" +
                "                WHERE\n" +
                "                    upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID\n" +
                "                        AND umsm.INGREDIENT_TYPE = 'INGREDIENT'\n" +
                "                        AND umsm.PRODUCT_CLASSIFICATION = 'MENU'\n" +
                "                        AND umsm.MENU_PRODUCT_ID = pd.PRODUCT_ID\n" +
                "                        AND umsm.RECIPE_PROFILE = upp.RECIPE_PROFILE\n" +
                "                        AND umsm.MENU_PRODUCT_DIMENSION = rl.RL_NAME\n" +
                "                        AND pd.PRODUCT_ID = upm.PRODUCT_ID\n" +
                "                        AND pd.PRODUCT_STATUS = 'ACTIVE'\n" +
                "                        AND pd.IS_INVENTORY_TRACKED = 'Y'\n" +
                "                        AND rl.RL_ID = upp.DIMENSION_CODE\n" +
                "                        AND upp.PRICING_STATUS = 'ACTIVE'\n" +
                "                        AND upm.PRODUCT_STATUS = 'ACTIVE'\n" +
                "                        AND upm.UNIT_ID = T.REQUESTING_UNIT_ID)\n" +
                "        THEN\n" +
                "            'Y'\n" +
                "        ELSE 'N'\n" +
                "    END) AS IS_CRITICAL,\n" +
                "    PD.CATEGORY_LEVEL AS PRODUCT_LEVEL\n" +
                "FROM\n" +
                "    (SELECT \n" +
                "        FULFILLMENT_DATE LAST_UPDATE_TIME,\n" +
                "            RO_ITEM_DETAILS.TRANSFR_UNIT AS TRANSFERRING_UNIT,\n" +
                "            RO_ITEM_DETAILS.TRANSFR_UNIT_ID AS TRANSFERRING_UNIT_ID,\n" +
                "            RO_ITEM_DETAILS.REQ_UNIT AS REQUESTING_UNIT,\n" +
                "            RO_ITEM_DETAILS.REQ_UNIT_ID AS REQUESTING_UNIT_ID,\n" +
                "            RO_ITEM_DETAILS.RO_ID REQUEST_ORDER_ID,\n" +
                "            RO_ITEM_DETAILS.TO_ID,\n" +
                "            RO_ITEM_DETAILS.GR_ID,\n" +
                "            RO_ITEM_DETAILS.RO_ITEM_ID AS REQUEST_ORDER_ITEM_ID,\n" +
                "            RO_ITEM_DETAILS.GOODS_RECEIVED_ITEM_ID,\n" +
                "            RO_ITEM_DETAILS.RO_STATUS,\n" +
                "            CASE\n" +
                "                WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.TO_STATUS)\n" +
                "                ELSE RO_ITEM_DETAILS.TO_STATUS\n" +
                "            END AS TO_STATUS,\n" +
                "            CASE\n" +
                "                WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.GR_STATUS)\n" +
                "                ELSE RO_ITEM_DETAILS.TO_STATUS\n" +
                "            END AS GR_STATUS,\n" +
                "            RO_ITEM_DETAILS.PROD_ID AS PRODUCT_ID,\n" +
                "            RO_ITEM_DETAILS.SKU_ID,\n" +
                "            RO_ITEM_DETAILS.PROD_NAME AS PRODUCT_NAME,\n" +
                "            RO_ITEM_DETAILS.UOM AS UNIT_OF_MEASURE,\n" +
                "            CASE\n" +
                "\t\t\t\tWHEN RO_ITEM_DETAILS.TO_STATUS = \"CANCELLED\" THEN 0\n" +
                "                WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.REQ_ABS_QTY)\n" +
                "                ELSE RO_ITEM_DETAILS.REQ_ABS_QTY\n" +
                "            END AS REQUESTED_ABSOLUTE_QUANTITY,\n" +
                "            CASE\n" +
                "\t\t\t\tWHEN RO_ITEM_DETAILS.TO_STATUS = \"CANCELLED\" THEN 0\n" +
                "                WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.TRANSFRD_QTY)\n" +
                "                ELSE RO_ITEM_DETAILS.TRANSFRD_QTY\n" +
                "            END AS TRANSFERRED_QUANTITY,\n" +
                "            CASE\n" +
                "                WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.RECVD_QTY)\n" +
                "                ELSE RO_ITEM_DETAILS.RECVD_QTY\n" +
                "            END AS RECVD_QTY,\n" +
                "            GR3_SETTLED.GR3_STATUS,\n" +
                "            GR3_SETTLED.GR3_QTY,\n" +
                "            GR2_DETAILS.GR2_STATUS,\n" +
                "            GR2_DETAILS.GR2_QTY,\n" +
                "            CASE\n" +
                "                WHEN\n" +
                "                    (RO_ITEM_DETAILS.RO_STATUS = 'SETTLED'\n" +
                "                        AND GR3_STATUS IS NULL\n" +
                "                        AND GR2_STATUS IS NULL\n" +
                "                        AND GOODS_RECEIVED_ITEM_ID IS NOT NULL)\n" +
                "                THEN\n" +
                "                    IF(COALESCE(MAX(RO_ITEM_DETAILS.RECVD_QTY), 0) / MAX(RO_ITEM_DETAILS.REQ_ABS_QTY) > 1, MAX(RO_ITEM_DETAILS.REQ_ABS_QTY), COALESCE(MAX(RO_ITEM_DETAILS.RECVD_QTY), 0))\n" +
                "                WHEN\n" +
                "                    (RO_ITEM_DETAILS.RO_STATUS = 'SETTLED'\n" +
                "                        AND GR3_STATUS IS NULL\n" +
                "                        AND GR2_STATUS IS NULL\n" +
                "                        AND GOODS_RECEIVED_ITEM_ID IS NULL)\n" +
                "                THEN\n" +
                "                    0\n" +
                "                WHEN RO_ITEM_DETAILS.RO_STATUS = 'ACKNOWLEDGED' THEN 0\n" +
                "                WHEN RO_ITEM_DETAILS.RO_STATUS = 'TRANSFERRED' THEN - 1\n" +
                "                WHEN\n" +
                "                    (GR3_SETTLED.GR3_STATUS = 'SETTLED'\n" +
                "                        OR GR3_SETTLED.GR3_STATUS = 'CREATED')\n" +
                "                THEN\n" +
                "                    IF((RO_ITEM_DETAILS.RECVD_QTY + GR3_SETTLED.GR3_QTY) / RO_ITEM_DETAILS.REQ_ABS_QTY > 1, RO_ITEM_DETAILS.REQ_ABS_QTY, RO_ITEM_DETAILS.RECVD_QTY + GR3_SETTLED.GR3_QTY)\n" +
                "                WHEN (GR2_DETAILS.GR2_STATUS = 'SETTLED') THEN RO_ITEM_DETAILS.RECVD_QTY\n" +
                "                WHEN (GR2_DETAILS.GR2_STATUS = 'INITIATED') THEN - 1\n" +
                "                ELSE 'NULL'\n" +
                "            END AS FINAL_RECEIVED_QUANTITY,\n" +
                "            CASE\n" +
                "               WHEN RO_ITEM_DETAILS.GR_LAST_UPDATED_TIME <= TIMESTAMP(DATE(FULFILLMENT_DATE), ?) \n" +
                "                   THEN COALESCE(RO_ITEM_DETAILS.RECVD_QTY, 0)\n" +
                "                   ELSE 0\n" +
                "               END AS ON_TIME_FULLFILLMENT_QUANTITY,\n" +
                "           \n" +
                "           CASE\n" +
                "               WHEN RO_ITEM_DETAILS.GR_LAST_UPDATED_TIME <= TIMESTAMP(DATE(FULFILLMENT_DATE), '23:59:59')\n" +
                "                   THEN COALESCE(RO_ITEM_DETAILS.RECVD_QTY, 0)\n" +
                "                   ELSE 0\n" +
                "               END AS ON_DATE_FULLFILLMENT_QUANTITY," +
                "           \n" +
                "           COUNT(DISTINCT CASE \n" +
                "               WHEN RO_ITEM_DETAILS.RO_GENERATION_TIME <= TIMESTAMP(DATE_SUB(DATE(FULFILLMENT_DATE), INTERVAL 2 DAY), ?) \n" +
                "                   THEN RO_ITEM_DETAILS.RO_ID \n" +
                "               END) AS RAISED_ON_TIME_QUANTITY,\n" +
                "\n" +
                "           COUNT(DISTINCT CASE \n" +
                "               WHEN RO_ITEM_DETAILS.RO_GENERATION_TIME <= TIMESTAMP(DATE_SUB(DATE(FULFILLMENT_DATE), INTERVAL 2 DAY), '23:59:59') \n" +
                "                   THEN RO_ITEM_DETAILS.RO_ID \n" +
                "               END) AS RAISED_ON_DATE_QUANTITY,\n" +
                "\n" +
                "           COUNT(DISTINCT CASE \n" +
                "               WHEN RO_ITEM_DETAILS.RO_GENERATION_TIME > TIMESTAMP(DATE_SUB(DATE(FULFILLMENT_DATE), INTERVAL 2 DAY), '23:59:59') \n" +
                "                   THEN RO_ITEM_DETAILS.RO_ID \n" +
                "               END) AS DELAY_RAISED_QUANTITY,\n" +
                "\n" +
                "           COUNT(DISTINCT RO_ITEM_DETAILS.RO_ID) AS TOTAL_ROS" +
                "    FROM\n" +
                "        (SELECT \n" +
                "        ro.FULFILLMENT_DATE AS FULFILLMENT_DATE,\n" +
                "            ud1.UNIT_NAME AS TRANSFR_UNIT,\n" +
                "            ro.FULFILLMENT_UNIT_ID AS TRANSFR_UNIT_ID,\n" +
                "            ud2.UNIT_NAME AS REQ_UNIT,\n" +
                "            ro.REQUEST_UNIT_ID AS REQ_UNIT_ID,\n" +
                "            ro.REQUEST_ORDER_ID AS RO_ID,\n" +
                "            gr.TRANSFER_ORDER_ID AS TO_ID,\n" +
                "            gr.GOODS_RECEIVED_ID AS GR_ID,\n" +
                "            roi.REQUEST_ORDER_ITEM_ID AS RO_ITEM_ID,\n" +
                "            gri.GOODS_RECEIVED_ITEM_ID,\n" +
                "            ro.REQUEST_ORDER_STATUS AS RO_STATUS,\n" +
                "            tr.TRANSFER_ORDER_STATUS AS TO_STATUS,\n" +
                "            gr.GOODS_RECEIVED_STATUS AS GR_STATUS,\n" +
                "            roi.PRODUCT_ID AS PROD_ID,\n" +
                "            gri.SKU_ID,\n" +
                "            roi.PRODUCT_NAME AS PROD_NAME,\n" +
                "            roi.UNIT_OF_MEASURE AS UOM,\n" +
                "            roi.REQUESTED_ABSOLUTE_QUANTITY AS REQ_ABS_QTY,\n" +
                "            gri.TRANSFERRED_QUANTITY AS TRANSFRD_QTY,\n" +
                "            gri.RECEIVED_QUANTITY AS RECVD_QTY,\n" +
                "            gr.LAST_UPDATE_TIME AS GR_LAST_UPDATED_TIME,\n" +
                "            ro.GENERATION_TIME AS RO_GENERATION_TIME" +
                "    FROM\n" +
                "        KETTLE_SCM_DUMP.REQUEST_ORDER ro\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.REQUEST_ORDER_ITEM roi ON ro.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID\n" +
                "    LEFT JOIN KETTLE_SCM_DUMP.TRANSFER_ORDER tr ON ro.REQUEST_ORDER_ID = tr.REQUEST_ORDER_ID\n" +
                "    LEFT JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED gr ON tr.TRANSFER_ORDER_ID = gr.TRANSFER_ORDER_ID\n" +
                "    LEFT JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED_ITEM gri ON gr.GOODS_RECEIVED_ID = gri.GOODS_RECEIVED_ID\n" +
                "        AND roi.REQUEST_ORDER_ITEM_ID = gri.REQUEST_ORDER_ITEM_ID\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.UNIT_DETAIL ud1 ON ro.FULFILLMENT_UNIT_ID = ud1.UNIT_ID\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.UNIT_DETAIL ud2 ON ro.REQUEST_UNIT_ID = ud2.UNIT_ID\n" +
                "    WHERE\n" +
                "        ro.REQUEST_ORDER_STATUS IN ('ACKNOWLEDGED' , 'SETTLED', 'TRANSFERRED')\n" +
                "            AND roi.REQUESTED_ABSOLUTE_QUANTITY <> 0\n" +
                "            AND ro.FULFILLMENT_UNIT_ID IN (26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429)\n" +
                "            AND ro.REQUEST_UNIT_ID NOT IN (26072 , 26423, 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429)\n" +
                "            AND ro.IS_SPECIAL_ORDER = 'N'\n" +
                "            AND ro.ASSET_ORDER = 'N'\n" +
                "            AND ro.TRANSFER_TYPE = 'TRANSFER'\n" +
                "            AND DATE(ro.FULFILLMENT_DATE) between DATE_SUB(LAST_DAY(SUBDATE(NOW(),1)),INTERVAL DAY(LAST_DAY(SUBDATE(NOW(),1))) - 1 DAY) and SUBDATE(CURRENT_DATE,1)\n" +
                "            ) AS RO_ITEM_DETAILS\n" +
                "    LEFT JOIN (SELECT \n" +
                "        ro.REQUEST_ORDER_ID AS RO_ID,\n" +
                "            gr1.GOODS_RECEIVED_ID AS GR_ID,\n" +
                "            gr3.GOODS_RECEIVED_STATUS AS GR3_STATUS,\n" +
                "            gri3.SKU_ID,\n" +
                "            gri3.TRANSFERRED_QUANTITY AS GR3_QTY\n" +
                "    FROM\n" +
                "        KETTLE_SCM_DUMP.GOODS_RECEIVED gr3\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED_ITEM gri3 ON gr3.GOODS_RECEIVED_ID = gri3.GOODS_RECEIVED_ID\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED gr2 ON gr2.GOODS_RECEIVED_ID = gr3.PARENT_GR\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED gr1 ON gr1.GOODS_RECEIVED_ID = gr2.PARENT_GR\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.REQUEST_ORDER ro ON gr1.REQUEST_ORDER_ID = ro.REQUEST_ORDER_ID\n" +
                "    WHERE\n" +
                "        gr3.PARENT_GR IS NOT NULL\n" +
                "            AND gr2.PARENT_GR IS NOT NULL\n" +
                "            AND gr1.REQUEST_ORDER_ID IS NOT NULL\n" +
                "            AND gr3.GENERATION_UNIT_ID IN (26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429)\n" +
                "            AND gr3.GENERATED_FOR_UNIT_ID NOT IN (26072 , 26423, 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429)\n" +
                "            AND gr3.GENERATION_TIME BETWEEN DATE_SUB(LAST_DAY(SUBDATE(NOW(),1)),INTERVAL DAY(LAST_DAY(SUBDATE(NOW(),1))) - 1 DAY) and SUBDATE(CURRENT_DATE,1)\n" +
                "            AND ro.IS_SPECIAL_ORDER = 'N'\n" +
                "            AND ro.ASSET_ORDER = 'N'\n" +
                "            AND ro.TRANSFER_TYPE = 'TRANSFER'\n" +
                "            AND DATE(ro.FULFILLMENT_DATE) between DATE_SUB(LAST_DAY(SUBDATE(NOW(),1)),INTERVAL DAY(LAST_DAY(SUBDATE(NOW(),1))) - 1 DAY) and SUBDATE(CURRENT_DATE,1) " +
                "            ) AS GR3_SETTLED ON GR3_SETTLED.RO_ID = RO_ITEM_DETAILS.RO_ID\n" +
                "        AND GR3_SETTLED.GR_ID = RO_ITEM_DETAILS.GR_ID\n" +
                "        AND GR3_SETTLED.SKU_ID = RO_ITEM_DETAILS.SKU_ID\n" +
                "    LEFT JOIN (SELECT \n" +
                "        ro.REQUEST_ORDER_ID AS RO_ID,\n" +
                "            gr1.GOODS_RECEIVED_ID AS GR_ID,\n" +
                "            gr2.GOODS_RECEIVED_STATUS AS GR2_STATUS,\n" +
                "            gri2.SKU_ID,\n" +
                "            gri2.TRANSFERRED_QUANTITY AS GR2_QTY\n" +
                "    FROM\n" +
                "        KETTLE_SCM_DUMP.GOODS_RECEIVED gr2\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED_ITEM gri2 ON gr2.GOODS_RECEIVED_ID = gri2.GOODS_RECEIVED_ID\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED gr1 ON gr1.GOODS_RECEIVED_ID = gr2.PARENT_GR\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.REQUEST_ORDER ro ON gr1.REQUEST_ORDER_ID = ro.REQUEST_ORDER_ID\n" +
                "    WHERE\n" +
                "        gr2.PARENT_GR IS NOT NULL\n" +
                "            AND gr1.PARENT_GR IS NULL\n" +
                "            AND gr2.GOODS_RECEIVED_STATUS IN ('SETTLED' , 'INITIATED')\n" +
                "            AND gr1.REQUEST_ORDER_ID IS NOT NULL\n" +
                "            AND gr2.GENERATED_FOR_UNIT_ID IN (26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429)\n" +
                "            AND gr2.GENERATION_UNIT_ID NOT IN (26072 , 26423, 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429)\n" +
                "            AND ro.IS_SPECIAL_ORDER = 'N'\n" +
                "            AND ro.ASSET_ORDER = 'N'\n" +
                "            AND ro.TRANSFER_TYPE = 'TRANSFER'\n" +
                "            AND DATE(ro.FULFILLMENT_DATE) between DATE_SUB(LAST_DAY(SUBDATE(NOW(),1)),INTERVAL DAY(LAST_DAY(SUBDATE(NOW(),1))) - 1 DAY) and SUBDATE(CURRENT_DATE,1)\n" +
                "            ) AS GR2_DETAILS ON GR2_DETAILS.RO_ID = RO_ITEM_DETAILS.RO_ID\n" +
                "        AND GR2_DETAILS.GR_ID = RO_ITEM_DETAILS.GR_ID\n" +
                "        AND GR2_DETAILS.SKU_ID = RO_ITEM_DETAILS.SKU_ID\n" +
                "    GROUP BY RO_ITEM_DETAILS.RO_ITEM_ID) AS T\n" +
                "        INNER JOIN\n" +
                "    KETTLE_SCM_DUMP.PRODUCT_DEFINITION PD ON T.PRODUCT_ID = PD.PRODUCT_ID;";

    }

    @Override
    public String getFullfillmentQueryLastDay() {
        return "SELECT \n" +
                "    T.*, \n" +
                "    (CASE WHEN T.FINAL_RECEIVED_QUANTITY > T.REQUESTED_ABSOLUTE_QUANTITY THEN T.REQUESTED_ABSOLUTE_QUANTITY ELSE T.FINAL_RECEIVED_QUANTITY END) AS FINAL_RECEIVED_QUANTITY, \n" +
                "    (CASE WHEN T.FINAL_RECEIVED_QUANTITY > T.REQUESTED_ABSOLUTE_QUANTITY THEN T.REQUESTED_ABSOLUTE_QUANTITY ELSE T.FINAL_RECEIVED_QUANTITY END / T.REQUESTED_ABSOLUTE_QUANTITY) * 100 AS FULLFILLMENT_PERCENTAGE,\n" +
                "   (LEAST(T.ON_TIME_FULLFILLMENT_QUANTITY, T.REQUESTED_ABSOLUTE_QUANTITY) / T.REQUESTED_ABSOLUTE_QUANTITY) * 100 AS ON_TIME_FULLFILLMENT_PERCENTAGE,\n" +
                "   (LEAST(T.ON_DATE_FULLFILLMENT_QUANTITY, T.REQUESTED_ABSOLUTE_QUANTITY) / T.REQUESTED_ABSOLUTE_QUANTITY) * 100 AS ON_DATE_FULLFILLMENT_PERCENTAGE,\n" +
                "    (RAISED_ON_TIME_QUANTITY / TOTAL_ROS) * 100 AS ON_TIME_RO_RAISED_PERCENTAGE,\n" +
                "    (RAISED_ON_DATE_QUANTITY / TOTAL_ROS) * 100 AS ON_DATE_RO_RAISED_PERCENTAGE,\n" +
                "    (DELAY_RAISED_QUANTITY / TOTAL_ROS) * 100 AS DELAY_RO_RAISED_PERCENTAGE," +
                "    (CASE\n" +
                "        WHEN\n" +
                "            T.PRODUCT_ID IN (SELECT \n" +
                "                    umsm.SCM_PRODUCT_ID\n" +
                "                FROM\n" +
                "                    KETTLE_MASTER_DUMP.UNIT_PRODUCT_MAPPING upm,\n" +
                "                    KETTLE_MASTER_DUMP.UNIT_PRODUCT_PRICING upp,\n" +
                "                    KETTLE_MASTER_DUMP.REF_LOOKUP rl,\n" +
                "                    KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd,\n" +
                "                    KETTLE_MASTER_DUMP.MENU_TO_SCM_PRODUCT_MAP umsm\n" +
                "                WHERE\n" +
                "                    upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID\n" +
                "                        AND umsm.INGREDIENT_TYPE = 'INGREDIENT'\n" +
                "                        AND umsm.PRODUCT_CLASSIFICATION = 'MENU'\n" +
                "                        AND umsm.MENU_PRODUCT_ID = pd.PRODUCT_ID\n" +
                "                        AND umsm.RECIPE_PROFILE = upp.RECIPE_PROFILE\n" +
                "                        AND umsm.MENU_PRODUCT_DIMENSION = rl.RL_NAME\n" +
                "                        AND pd.PRODUCT_ID = upm.PRODUCT_ID\n" +
                "                        AND pd.PRODUCT_STATUS = 'ACTIVE'\n" +
                "                        AND pd.IS_INVENTORY_TRACKED = 'Y'\n" +
                "                        AND rl.RL_ID = upp.DIMENSION_CODE\n" +
                "                        AND upp.PRICING_STATUS = 'ACTIVE'\n" +
                "                        AND upm.PRODUCT_STATUS = 'ACTIVE'\n" +
                "                        AND upm.UNIT_ID = T.REQUESTING_UNIT_ID)\n" +
                "        THEN\n" +
                "            'Y'\n" +
                "        ELSE 'N'\n" +
                "    END) AS IS_CRITICAL,\n" +
                "    PD.CATEGORY_LEVEL AS PRODUCT_LEVEL\n" +
                "FROM\n" +
                "    (SELECT \n" +
                "        FULFILLMENT_DATE LAST_UPDATE_TIME,\n" +
                "            RO_ITEM_DETAILS.TRANSFR_UNIT AS TRANSFERRING_UNIT,\n" +
                "            RO_ITEM_DETAILS.TRANSFR_UNIT_ID AS TRANSFERRING_UNIT_ID,\n" +
                "            RO_ITEM_DETAILS.REQ_UNIT AS REQUESTING_UNIT,\n" +
                "            RO_ITEM_DETAILS.REQ_UNIT_ID AS REQUESTING_UNIT_ID,\n" +
                "            RO_ITEM_DETAILS.RO_ID REQUEST_ORDER_ID,\n" +
                "            RO_ITEM_DETAILS.TO_ID,\n" +
                "            RO_ITEM_DETAILS.GR_ID,\n" +
                "            RO_ITEM_DETAILS.RO_ITEM_ID AS REQUEST_ORDER_ITEM_ID,\n" +
                "            RO_ITEM_DETAILS.GOODS_RECEIVED_ITEM_ID,\n" +
                "            RO_ITEM_DETAILS.RO_STATUS,\n" +
                "            CASE\n" +
                "                WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.TO_STATUS)\n" +
                "                ELSE RO_ITEM_DETAILS.TO_STATUS\n" +
                "            END AS TO_STATUS,\n" +
                "            CASE\n" +
                "                WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.GR_STATUS)\n" +
                "                ELSE RO_ITEM_DETAILS.TO_STATUS\n" +
                "            END AS GR_STATUS,\n" +
                "            RO_ITEM_DETAILS.PROD_ID AS PRODUCT_ID,\n" +
                "            RO_ITEM_DETAILS.SKU_ID,\n" +
                "            RO_ITEM_DETAILS.PROD_NAME AS PRODUCT_NAME,\n" +
                "            RO_ITEM_DETAILS.UOM AS UNIT_OF_MEASURE,\n" +
                "            CASE\n" +
                "\t\t\t\tWHEN RO_ITEM_DETAILS.TO_STATUS = \"CANCELLED\" THEN 0\n" +
                "                WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.REQ_ABS_QTY)\n" +
                "                ELSE RO_ITEM_DETAILS.REQ_ABS_QTY\n" +
                "            END AS REQUESTED_ABSOLUTE_QUANTITY,\n" +
                "            CASE\n" +
                "\t\t\t\tWHEN RO_ITEM_DETAILS.TO_STATUS = \"CANCELLED\" THEN 0\n" +
                "                WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.TRANSFRD_QTY)\n" +
                "                ELSE RO_ITEM_DETAILS.TRANSFRD_QTY\n" +
                "            END AS TRANSFERRED_QUANTITY,\n" +
                "            CASE\n" +
                "                WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.RECVD_QTY)\n" +
                "                ELSE RO_ITEM_DETAILS.RECVD_QTY\n" +
                "            END AS RECVD_QTY,\n" +
                "            GR3_SETTLED.GR3_STATUS,\n" +
                "            GR3_SETTLED.GR3_QTY,\n" +
                "            GR2_DETAILS.GR2_STATUS,\n" +
                "            GR2_DETAILS.GR2_QTY,\n" +
                "            CASE\n" +
                "                WHEN\n" +
                "                    (RO_ITEM_DETAILS.RO_STATUS = 'SETTLED'\n" +
                "                        AND GR3_STATUS IS NULL\n" +
                "                        AND GR2_STATUS IS NULL\n" +
                "                        AND GOODS_RECEIVED_ITEM_ID IS NOT NULL)\n" +
                "                THEN\n" +
                "                    IF(COALESCE(MAX(RO_ITEM_DETAILS.RECVD_QTY), 0) / MAX(RO_ITEM_DETAILS.REQ_ABS_QTY) > 1, MAX(RO_ITEM_DETAILS.REQ_ABS_QTY), COALESCE(MAX(RO_ITEM_DETAILS.RECVD_QTY), 0))\n" +
                "                WHEN\n" +
                "                    (RO_ITEM_DETAILS.RO_STATUS = 'SETTLED'\n" +
                "                        AND GR3_STATUS IS NULL\n" +
                "                        AND GR2_STATUS IS NULL\n" +
                "                        AND GOODS_RECEIVED_ITEM_ID IS NULL)\n" +
                "                THEN\n" +
                "                    0\n" +
                "                WHEN RO_ITEM_DETAILS.RO_STATUS = 'ACKNOWLEDGED' THEN 0\n" +
                "                WHEN RO_ITEM_DETAILS.RO_STATUS = 'TRANSFERRED' THEN - 1\n" +
                "                WHEN\n" +
                "                    (GR3_SETTLED.GR3_STATUS = 'SETTLED'\n" +
                "                        OR GR3_SETTLED.GR3_STATUS = 'CREATED')\n" +
                "                THEN\n" +
                "                    IF((RO_ITEM_DETAILS.RECVD_QTY + GR3_SETTLED.GR3_QTY) / RO_ITEM_DETAILS.REQ_ABS_QTY > 1, RO_ITEM_DETAILS.REQ_ABS_QTY, RO_ITEM_DETAILS.RECVD_QTY + GR3_SETTLED.GR3_QTY)\n" +
                "                WHEN (GR2_DETAILS.GR2_STATUS = 'SETTLED') THEN RO_ITEM_DETAILS.RECVD_QTY\n" +
                "                WHEN (GR2_DETAILS.GR2_STATUS = 'INITIATED') THEN - 1\n" +
                "                ELSE 'NULL'\n" +
                "            END AS FINAL_RECEIVED_QUANTITY,\n" +
                "           CASE\n" +
                "               WHEN RO_ITEM_DETAILS.GR_LAST_UPDATED_TIME <= TIMESTAMP(DATE(FULFILLMENT_DATE), ?) \n" +
                "                   THEN COALESCE(RO_ITEM_DETAILS.RECVD_QTY, 0)\n" +
                "                   ELSE 0\n" +
                "               END AS ON_TIME_FULLFILLMENT_QUANTITY,\n" +
                "           CASE\n" +
                "               WHEN RO_ITEM_DETAILS.GR_LAST_UPDATED_TIME <= TIMESTAMP(DATE(FULFILLMENT_DATE), \"23:59:59\")\n" +
                "                   THEN COALESCE(RO_ITEM_DETAILS.RECVD_QTY, 0)\n" +
                "                   ELSE 0\n" +
                "               END AS ON_DATE_FULLFILLMENT_QUANTITY,\n" +
                "           COUNT(DISTINCT CASE \n" +
                "               WHEN RO_ITEM_DETAILS.RO_GENERATION_TIME <= TIMESTAMP(DATE_SUB(DATE(FULFILLMENT_DATE), INTERVAL 2 DAY), ?) \n" +
                "                   THEN RO_ITEM_DETAILS.RO_ID \n" +
                "               END) AS RAISED_ON_TIME_QUANTITY,\n" +
                "\n" +
                "           COUNT(DISTINCT CASE \n" +
                "               WHEN RO_ITEM_DETAILS.RO_GENERATION_TIME <= TIMESTAMP(DATE_SUB(DATE(FULFILLMENT_DATE), INTERVAL 2 DAY), '23:59:59') \n" +
                "                   THEN RO_ITEM_DETAILS.RO_ID \n" +
                "               END) AS RAISED_ON_DATE_QUANTITY,\n" +
                "\n" +
                "           COUNT(DISTINCT CASE \n" +
                "               WHEN RO_ITEM_DETAILS.RO_GENERATION_TIME > TIMESTAMP(DATE_SUB(DATE(FULFILLMENT_DATE), INTERVAL 2 DAY), '23:59:59') \n" +
                "                   THEN RO_ITEM_DETAILS.RO_ID \n" +
                "               END) AS DELAY_RAISED_QUANTITY,\n" +
                "\n" +
                "           COUNT(DISTINCT RO_ITEM_DETAILS.RO_ID) AS TOTAL_ROS" +
                "    FROM\n" +
                "        (SELECT \n" +
                "        ro.FULFILLMENT_DATE AS FULFILLMENT_DATE,\n" +
                "            ud1.UNIT_NAME AS TRANSFR_UNIT,\n" +
                "            ro.FULFILLMENT_UNIT_ID AS TRANSFR_UNIT_ID,\n" +
                "            ud2.UNIT_NAME AS REQ_UNIT,\n" +
                "            ro.REQUEST_UNIT_ID AS REQ_UNIT_ID,\n" +
                "            ro.REQUEST_ORDER_ID AS RO_ID,\n" +
                "            gr.TRANSFER_ORDER_ID AS TO_ID,\n" +
                "            gr.GOODS_RECEIVED_ID AS GR_ID,\n" +
                "            roi.REQUEST_ORDER_ITEM_ID AS RO_ITEM_ID,\n" +
                "            gri.GOODS_RECEIVED_ITEM_ID,\n" +
                "            ro.REQUEST_ORDER_STATUS AS RO_STATUS,\n" +
                "            tr.TRANSFER_ORDER_STATUS AS TO_STATUS,\n" +
                "            gr.GOODS_RECEIVED_STATUS AS GR_STATUS,\n" +
                "            roi.PRODUCT_ID AS PROD_ID,\n" +
                "            gri.SKU_ID,\n" +
                "            roi.PRODUCT_NAME AS PROD_NAME,\n" +
                "            roi.UNIT_OF_MEASURE AS UOM,\n" +
                "            roi.REQUESTED_ABSOLUTE_QUANTITY AS REQ_ABS_QTY,\n" +
                "            gri.TRANSFERRED_QUANTITY AS TRANSFRD_QTY,\n" +
                "            gri.RECEIVED_QUANTITY AS RECVD_QTY,\n" +
                "            gr.LAST_UPDATE_TIME AS GR_LAST_UPDATED_TIME,\n" +
                "            ro.GENERATION_TIME AS RO_GENERATION_TIME" +
                "    FROM\n" +
                "        KETTLE_SCM_DUMP.REQUEST_ORDER ro\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.REQUEST_ORDER_ITEM roi ON ro.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID\n" +
                "    LEFT JOIN KETTLE_SCM_DUMP.TRANSFER_ORDER tr ON ro.REQUEST_ORDER_ID = tr.REQUEST_ORDER_ID\n" +
                "    LEFT JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED gr ON tr.TRANSFER_ORDER_ID = gr.TRANSFER_ORDER_ID\n" +
                "    LEFT JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED_ITEM gri ON gr.GOODS_RECEIVED_ID = gri.GOODS_RECEIVED_ID\n" +
                "        AND roi.REQUEST_ORDER_ITEM_ID = gri.REQUEST_ORDER_ITEM_ID\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.UNIT_DETAIL ud1 ON ro.FULFILLMENT_UNIT_ID = ud1.UNIT_ID\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.UNIT_DETAIL ud2 ON ro.REQUEST_UNIT_ID = ud2.UNIT_ID\n" +
                "    WHERE\n" +
                "        ro.REQUEST_ORDER_STATUS IN ('ACKNOWLEDGED' , 'SETTLED', 'TRANSFERRED')\n" +
                "            AND roi.REQUESTED_ABSOLUTE_QUANTITY <> 0\n" +
                "            AND ro.FULFILLMENT_UNIT_ID IN (26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429)\n" +
                "            AND ro.REQUEST_UNIT_ID NOT IN (26072 , 26423, 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429)\n" +
                "            AND ro.IS_SPECIAL_ORDER = 'N'\n" +
                "            AND ro.ASSET_ORDER = 'N'\n" +
                "            AND ro.TRANSFER_TYPE = 'TRANSFER'\n" +
                "            AND ro.FULFILLMENT_DATE = SUBDATE(CURRENT_DATE, 1)) AS RO_ITEM_DETAILS\n" +
                "    LEFT JOIN (SELECT \n" +
                "        ro.REQUEST_ORDER_ID AS RO_ID,\n" +
                "            gr1.GOODS_RECEIVED_ID AS GR_ID,\n" +
                "            gr3.GOODS_RECEIVED_STATUS AS GR3_STATUS,\n" +
                "            gri3.SKU_ID,\n" +
                "            gri3.TRANSFERRED_QUANTITY AS GR3_QTY\n" +
                "    FROM\n" +
                "        KETTLE_SCM_DUMP.GOODS_RECEIVED gr3\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED_ITEM gri3 ON gr3.GOODS_RECEIVED_ID = gri3.GOODS_RECEIVED_ID\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED gr2 ON gr2.GOODS_RECEIVED_ID = gr3.PARENT_GR\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED gr1 ON gr1.GOODS_RECEIVED_ID = gr2.PARENT_GR\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.REQUEST_ORDER ro ON gr1.REQUEST_ORDER_ID = ro.REQUEST_ORDER_ID\n" +
                "    WHERE\n" +
                "        gr3.PARENT_GR IS NOT NULL\n" +
                "            AND gr2.PARENT_GR IS NOT NULL\n" +
                "            AND gr1.REQUEST_ORDER_ID IS NOT NULL\n" +
                "            AND gr3.GENERATION_UNIT_ID IN (26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429)\n" +
                "            AND gr3.GENERATED_FOR_UNIT_ID NOT IN (26072 , 26423, 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429)\n" +
                "            AND gr3.GENERATION_TIME BETWEEN DATE_SUB(LAST_DAY(SUBDATE(NOW(),1)),INTERVAL DAY(LAST_DAY(SUBDATE(NOW(),1))) - 1 DAY) and SUBDATE(CURRENT_DATE,1)\n" +
                "            AND ro.IS_SPECIAL_ORDER = 'N'\n" +
                "            AND ro.ASSET_ORDER = 'N'\n" +
                "            AND ro.TRANSFER_TYPE = 'TRANSFER'\n" +
                "            AND ro.FULFILLMENT_DATE = SUBDATE(CURRENT_DATE, 1)) AS GR3_SETTLED ON GR3_SETTLED.RO_ID = RO_ITEM_DETAILS.RO_ID\n" +
                "        AND GR3_SETTLED.GR_ID = RO_ITEM_DETAILS.GR_ID\n" +
                "        AND GR3_SETTLED.SKU_ID = RO_ITEM_DETAILS.SKU_ID\n" +
                "    LEFT JOIN (SELECT \n" +
                "        ro.REQUEST_ORDER_ID AS RO_ID,\n" +
                "            gr1.GOODS_RECEIVED_ID AS GR_ID,\n" +
                "            gr2.GOODS_RECEIVED_STATUS AS GR2_STATUS,\n" +
                "            gri2.SKU_ID,\n" +
                "            gri2.TRANSFERRED_QUANTITY AS GR2_QTY\n" +
                "    FROM\n" +
                "        KETTLE_SCM_DUMP.GOODS_RECEIVED gr2\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED_ITEM gri2 ON gr2.GOODS_RECEIVED_ID = gri2.GOODS_RECEIVED_ID\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED gr1 ON gr1.GOODS_RECEIVED_ID = gr2.PARENT_GR\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.REQUEST_ORDER ro ON gr1.REQUEST_ORDER_ID = ro.REQUEST_ORDER_ID\n" +
                "    WHERE\n" +
                "        gr2.PARENT_GR IS NOT NULL\n" +
                "            AND gr1.PARENT_GR IS NULL\n" +
                "            AND gr2.GOODS_RECEIVED_STATUS IN ('SETTLED' , 'INITIATED')\n" +
                "            AND gr1.REQUEST_ORDER_ID IS NOT NULL\n" +
                "            AND gr2.GENERATED_FOR_UNIT_ID IN (26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429)\n" +
                "            AND gr2.GENERATION_UNIT_ID NOT IN (26072 , 26423, 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429)\n" +
                "            AND ro.IS_SPECIAL_ORDER = 'N'\n" +
                "            AND ro.ASSET_ORDER = 'N'\n" +
                "            AND ro.TRANSFER_TYPE = 'TRANSFER'\n" +
                "            AND ro.FULFILLMENT_DATE = SUBDATE(CURRENT_DATE, 1)) AS GR2_DETAILS ON GR2_DETAILS.RO_ID = RO_ITEM_DETAILS.RO_ID\n" +
                "        AND GR2_DETAILS.GR_ID = RO_ITEM_DETAILS.GR_ID\n" +
                "        AND GR2_DETAILS.SKU_ID = RO_ITEM_DETAILS.SKU_ID\n" +
                "    GROUP BY RO_ITEM_DETAILS.RO_ITEM_ID) AS T\n" +
                "        INNER JOIN\n" +
                "    KETTLE_SCM_DUMP.PRODUCT_DEFINITION PD ON T.PRODUCT_ID = PD.PRODUCT_ID;";
    }

    @Override
    public String getMenuToScmQuery() {
        return "SELECT \n" +
                "    upm.PRODUCT_ID,\n" +
                "    rl.RL_NAME,\n" +
                "    upp.RECIPE_PROFILE,\n" +
                "    umsm.SCM_PRODUCT_ID,\n" +
                "    umsm.SCM_PRODUCT_QUANTITY,\n" +
                "     (CASE WHEN umsm.INGREDIENT_TYPE= \"INGREDIENT\" and  umsm.PRODUCT_CLASSIFICATION = \"MENU\" THEN \"Y\" ELSE \"N\" END) as IS_CRITICAL \n" +
                "FROM\n" +
                "    KETTLE_MASTER_DUMP.UNIT_PRODUCT_MAPPING upm,\n" +
                "    KETTLE_MASTER_DUMP.UNIT_PRODUCT_PRICING upp,\n" +
                "    KETTLE_MASTER_DUMP.REF_LOOKUP rl,\n" +
                "    KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd,\n" +
                "    KETTLE_MASTER_DUMP.MENU_TO_SCM_PRODUCT_MAP umsm\n" +
                "WHERE\n" +
                "    upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID\n" +
                "        AND umsm.MENU_PRODUCT_ID = pd.PRODUCT_ID\n" +
                "        AND umsm.RECIPE_PROFILE = upp.RECIPE_PROFILE\n" +
                "        AND umsm.MENU_PRODUCT_DIMENSION = rl.RL_NAME\n" +
                "        AND pd.PRODUCT_ID = upm.PRODUCT_ID\n" +
                "        AND pd.PRODUCT_STATUS = 'ACTIVE'\n" +
                "        AND pd.IS_INVENTORY_TRACKED = 'Y'\n" +
                "        AND rl.RL_ID = upp.DIMENSION_CODE\n" +
                "        AND upp.PRICING_STATUS = 'ACTIVE'\n" +
                "        AND upm.PRODUCT_STATUS = 'ACTIVE'\n" +
                "        AND upm.UNIT_ID = ? ;";


    }

    @Override
    public String getFullfillmentQueryLastSevenDay() {
        return "SELECT \n" +
                "    T.*, \n" +
                "    (CASE WHEN T.FINAL_RECEIVED_QUANTITY > T.REQUESTED_ABSOLUTE_QUANTITY THEN T.REQUESTED_ABSOLUTE_QUANTITY ELSE T.FINAL_RECEIVED_QUANTITY END) AS FINAL_RECEIVED_QUANTITY, \n" +
                "    (CASE WHEN T.FINAL_RECEIVED_QUANTITY > T.REQUESTED_ABSOLUTE_QUANTITY THEN T.REQUESTED_ABSOLUTE_QUANTITY ELSE T.FINAL_RECEIVED_QUANTITY END / T.REQUESTED_ABSOLUTE_QUANTITY) * 100 AS FULLFILLMENT_PERCENTAGE,\n" +
                "   (LEAST(T.ON_TIME_FULLFILLMENT_QUANTITY, T.REQUESTED_ABSOLUTE_QUANTITY) / T.REQUESTED_ABSOLUTE_QUANTITY) * 100 AS ON_TIME_FULLFILLMENT_PERCENTAGE,\n" +
                "   (LEAST(T.ON_DATE_FULLFILLMENT_QUANTITY, T.REQUESTED_ABSOLUTE_QUANTITY) / T.REQUESTED_ABSOLUTE_QUANTITY) * 100 AS ON_DATE_FULLFILLMENT_PERCENTAGE,\n" +
                "    (RAISED_ON_TIME_QUANTITY / TOTAL_ROS) * 100 AS ON_TIME_RO_RAISED_PERCENTAGE,\n" +
                "    (RAISED_ON_DATE_QUANTITY / TOTAL_ROS) * 100 AS ON_DATE_RO_RAISED_PERCENTAGE,\n" +
                "    (DELAY_RAISED_QUANTITY / TOTAL_ROS) * 100 AS DELAY_RO_RAISED_PERCENTAGE," +
                "    (CASE\n" +
                "        WHEN\n" +
                "            T.PRODUCT_ID IN (SELECT \n" +
                "                    umsm.SCM_PRODUCT_ID\n" +
                "                FROM\n" +
                "                    KETTLE_MASTER_DUMP.UNIT_PRODUCT_MAPPING upm,\n" +
                "                    KETTLE_MASTER_DUMP.UNIT_PRODUCT_PRICING upp,\n" +
                "                    KETTLE_MASTER_DUMP.REF_LOOKUP rl,\n" +
                "                    KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd,\n" +
                "                    KETTLE_MASTER_DUMP.MENU_TO_SCM_PRODUCT_MAP umsm\n" +
                "                WHERE\n" +
                "                    upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID\n" +
                "                        AND umsm.INGREDIENT_TYPE = 'INGREDIENT'\n" +
                "                        AND umsm.PRODUCT_CLASSIFICATION = 'MENU'\n" +
                "                        AND umsm.MENU_PRODUCT_ID = pd.PRODUCT_ID\n" +
                "                        AND umsm.RECIPE_PROFILE = upp.RECIPE_PROFILE\n" +
                "                        AND umsm.MENU_PRODUCT_DIMENSION = rl.RL_NAME\n" +
                "                        AND pd.PRODUCT_ID = upm.PRODUCT_ID\n" +
                "                        AND pd.PRODUCT_STATUS = 'ACTIVE'\n" +
                "                        AND pd.IS_INVENTORY_TRACKED = 'Y'\n" +
                "                        AND rl.RL_ID = upp.DIMENSION_CODE\n" +
                "                        AND upp.PRICING_STATUS = 'ACTIVE'\n" +
                "                        AND upm.PRODUCT_STATUS = 'ACTIVE'\n" +
                "                        AND upm.UNIT_ID = T.REQUESTING_UNIT_ID)\n" +
                "        THEN\n" +
                "            'Y'\n" +
                "        ELSE 'N'\n" +
                "    END) AS IS_CRITICAL,\n" +
                "    PD.CATEGORY_LEVEL AS PRODUCT_LEVEL\n" +
                "FROM\n" +
                "    (SELECT \n" +
                "        FULFILLMENT_DATE LAST_UPDATE_TIME,\n" +
                "            RO_ITEM_DETAILS.TRANSFR_UNIT AS TRANSFERRING_UNIT,\n" +
                "            RO_ITEM_DETAILS.TRANSFR_UNIT_ID AS TRANSFERRING_UNIT_ID,\n" +
                "            RO_ITEM_DETAILS.REQ_UNIT AS REQUESTING_UNIT,\n" +
                "            RO_ITEM_DETAILS.REQ_UNIT_ID AS REQUESTING_UNIT_ID,\n" +
                "            RO_ITEM_DETAILS.RO_ID REQUEST_ORDER_ID,\n" +
                "            RO_ITEM_DETAILS.TO_ID,\n" +
                "            RO_ITEM_DETAILS.GR_ID,\n" +
                "            RO_ITEM_DETAILS.RO_ITEM_ID AS REQUEST_ORDER_ITEM_ID,\n" +
                "            RO_ITEM_DETAILS.GOODS_RECEIVED_ITEM_ID,\n" +
                "            RO_ITEM_DETAILS.RO_STATUS,\n" +
                "            CASE\n" +
                "                WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.TO_STATUS)\n" +
                "                ELSE RO_ITEM_DETAILS.TO_STATUS\n" +
                "            END AS TO_STATUS,\n" +
                "            CASE\n" +
                "                WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.GR_STATUS)\n" +
                "                ELSE RO_ITEM_DETAILS.TO_STATUS\n" +
                "            END AS GR_STATUS,\n" +
                "            RO_ITEM_DETAILS.PROD_ID AS PRODUCT_ID,\n" +
                "            RO_ITEM_DETAILS.SKU_ID,\n" +
                "            RO_ITEM_DETAILS.PROD_NAME AS PRODUCT_NAME,\n" +
                "            RO_ITEM_DETAILS.UOM AS UNIT_OF_MEASURE,\n" +
                "            CASE\n" +
                "\t\t\t\tWHEN RO_ITEM_DETAILS.TO_STATUS = \"CANCELLED\" THEN 0\n" +
                "                WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.REQ_ABS_QTY)\n" +
                "                ELSE RO_ITEM_DETAILS.REQ_ABS_QTY\n" +
                "            END AS REQUESTED_ABSOLUTE_QUANTITY,\n" +
                "            CASE\n" +
                "\t\t\t\tWHEN RO_ITEM_DETAILS.TO_STATUS = \"CANCELLED\" THEN 0\n" +
                "                WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.TRANSFRD_QTY)\n" +
                "                ELSE RO_ITEM_DETAILS.TRANSFRD_QTY\n" +
                "            END AS TRANSFERRED_QUANTITY,\n" +
                "            CASE\n" +
                "                WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.RECVD_QTY)\n" +
                "                ELSE RO_ITEM_DETAILS.RECVD_QTY\n" +
                "            END AS RECVD_QTY,\n" +
                "            GR3_SETTLED.GR3_STATUS,\n" +
                "            GR3_SETTLED.GR3_QTY,\n" +
                "            GR2_DETAILS.GR2_STATUS,\n" +
                "            GR2_DETAILS.GR2_QTY,\n" +
                "            CASE\n" +
                "                WHEN\n" +
                "                    (RO_ITEM_DETAILS.RO_STATUS = 'SETTLED'\n" +
                "                        AND GR3_STATUS IS NULL\n" +
                "                        AND GR2_STATUS IS NULL\n" +
                "                        AND GOODS_RECEIVED_ITEM_ID IS NOT NULL)\n" +
                "                THEN\n" +
                "                    IF(COALESCE(MAX(RO_ITEM_DETAILS.RECVD_QTY), 0) / MAX(RO_ITEM_DETAILS.REQ_ABS_QTY) > 1, MAX(RO_ITEM_DETAILS.REQ_ABS_QTY), COALESCE(MAX(RO_ITEM_DETAILS.RECVD_QTY), 0))\n" +
                "                WHEN\n" +
                "                    (RO_ITEM_DETAILS.RO_STATUS = 'SETTLED'\n" +
                "                        AND GR3_STATUS IS NULL\n" +
                "                        AND GR2_STATUS IS NULL\n" +
                "                        AND GOODS_RECEIVED_ITEM_ID IS NULL)\n" +
                "                THEN\n" +
                "                    0\n" +
                "                WHEN RO_ITEM_DETAILS.RO_STATUS = 'ACKNOWLEDGED' THEN 0\n" +
                "                WHEN RO_ITEM_DETAILS.RO_STATUS = 'TRANSFERRED' THEN - 1\n" +
                "                WHEN\n" +
                "                    (GR3_SETTLED.GR3_STATUS = 'SETTLED'\n" +
                "                        OR GR3_SETTLED.GR3_STATUS = 'CREATED')\n" +
                "                THEN\n" +
                "                    IF((RO_ITEM_DETAILS.RECVD_QTY + GR3_SETTLED.GR3_QTY) / RO_ITEM_DETAILS.REQ_ABS_QTY > 1, RO_ITEM_DETAILS.REQ_ABS_QTY, RO_ITEM_DETAILS.RECVD_QTY + GR3_SETTLED.GR3_QTY)\n" +
                "                WHEN (GR2_DETAILS.GR2_STATUS = 'SETTLED') THEN RO_ITEM_DETAILS.RECVD_QTY\n" +
                "                WHEN (GR2_DETAILS.GR2_STATUS = 'INITIATED') THEN - 1\n" +
                "                ELSE 'NULL'\n" +
                "            END AS FINAL_RECEIVED_QUANTITY,\n" +
                "            CASE\n" +
                "                WHEN RO_ITEM_DETAILS.GR_LAST_UPDATED_TIME <= TIMESTAMP(DATE(FULFILLMENT_DATE), ?) \n" +
                "                    THEN COALESCE(RO_ITEM_DETAILS.RECVD_QTY, 0)\n" +
                "                    ELSE 0\n" +
                "                END AS ON_TIME_FULLFILLMENT_QUANTITY,\n" +
                "            CASE\n" +
                "                WHEN RO_ITEM_DETAILS.GR_LAST_UPDATED_TIME <= TIMESTAMP(DATE(FULFILLMENT_DATE), \"23:59:59\")\n" +
                "                    THEN COALESCE(RO_ITEM_DETAILS.RECVD_QTY, 0)\n" +
                "                    ELSE 0\n" +
                "                END AS ON_DATE_FULLFILLMENT_QUANTITY,\n" +
                "           COUNT(DISTINCT CASE \n" +
                "               WHEN RO_ITEM_DETAILS.RO_GENERATION_TIME <= TIMESTAMP(DATE_SUB(DATE(FULFILLMENT_DATE), INTERVAL 2 DAY), ?) \n" +
                "                   THEN RO_ITEM_DETAILS.RO_ID \n" +
                "               END) AS RAISED_ON_TIME_QUANTITY,\n" +
                "\n" +
                "           COUNT(DISTINCT CASE \n" +
                "               WHEN RO_ITEM_DETAILS.RO_GENERATION_TIME <= TIMESTAMP(DATE_SUB(DATE(FULFILLMENT_DATE), INTERVAL 2 DAY), '23:59:59') \n" +
                "                   THEN RO_ITEM_DETAILS.RO_ID \n" +
                "               END) AS RAISED_ON_DATE_QUANTITY,\n" +
                "\n" +
                "           COUNT(DISTINCT CASE \n" +
                "               WHEN RO_ITEM_DETAILS.RO_GENERATION_TIME > TIMESTAMP(DATE_SUB(DATE(FULFILLMENT_DATE), INTERVAL 2 DAY), '23:59:59') \n" +
                "                   THEN RO_ITEM_DETAILS.RO_ID \n" +
                "               END) AS DELAY_RAISED_QUANTITY,\n" +
                "\n" +
                "           COUNT(DISTINCT RO_ITEM_DETAILS.RO_ID) AS TOTAL_ROS" +
                "    FROM\n" +
                "        (SELECT \n" +
                "        ro.FULFILLMENT_DATE AS FULFILLMENT_DATE,\n" +
                "            ud1.UNIT_NAME AS TRANSFR_UNIT,\n" +
                "            ro.FULFILLMENT_UNIT_ID AS TRANSFR_UNIT_ID,\n" +
                "            ud2.UNIT_NAME AS REQ_UNIT,\n" +
                "            ro.REQUEST_UNIT_ID AS REQ_UNIT_ID,\n" +
                "            ro.REQUEST_ORDER_ID AS RO_ID,\n" +
                "            gr.TRANSFER_ORDER_ID AS TO_ID,\n" +
                "            gr.GOODS_RECEIVED_ID AS GR_ID,\n" +
                "            roi.REQUEST_ORDER_ITEM_ID AS RO_ITEM_ID,\n" +
                "            gri.GOODS_RECEIVED_ITEM_ID,\n" +
                "            ro.REQUEST_ORDER_STATUS AS RO_STATUS,\n" +
                "            tr.TRANSFER_ORDER_STATUS AS TO_STATUS,\n" +
                "            gr.GOODS_RECEIVED_STATUS AS GR_STATUS,\n" +
                "            roi.PRODUCT_ID AS PROD_ID,\n" +
                "            gri.SKU_ID,\n" +
                "            roi.PRODUCT_NAME AS PROD_NAME,\n" +
                "            roi.UNIT_OF_MEASURE AS UOM,\n" +
                "            roi.REQUESTED_ABSOLUTE_QUANTITY AS REQ_ABS_QTY,\n" +
                "            gri.TRANSFERRED_QUANTITY AS TRANSFRD_QTY,\n" +
                "            gri.RECEIVED_QUANTITY AS RECVD_QTY,\n" +
                "            gr.LAST_UPDATE_TIME AS GR_LAST_UPDATED_TIME,\n" +
                "            ro.GENERATION_TIME AS RO_GENERATION_TIME" +
                "    FROM\n" +
                "        KETTLE_SCM_DUMP.REQUEST_ORDER ro\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.REQUEST_ORDER_ITEM roi ON ro.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID\n" +
                "    LEFT JOIN KETTLE_SCM_DUMP.TRANSFER_ORDER tr ON ro.REQUEST_ORDER_ID = tr.REQUEST_ORDER_ID\n" +
                "    LEFT JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED gr ON tr.TRANSFER_ORDER_ID = gr.TRANSFER_ORDER_ID\n" +
                "    LEFT JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED_ITEM gri ON gr.GOODS_RECEIVED_ID = gri.GOODS_RECEIVED_ID\n" +
                "        AND roi.REQUEST_ORDER_ITEM_ID = gri.REQUEST_ORDER_ITEM_ID\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.UNIT_DETAIL ud1 ON ro.FULFILLMENT_UNIT_ID = ud1.UNIT_ID\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.UNIT_DETAIL ud2 ON ro.REQUEST_UNIT_ID = ud2.UNIT_ID\n" +
                "    WHERE\n" +
                "        ro.REQUEST_ORDER_STATUS IN ('ACKNOWLEDGED' , 'SETTLED', 'TRANSFERRED')\n" +
                "            AND roi.REQUESTED_ABSOLUTE_QUANTITY <> 0\n" +
                "            AND ro.FULFILLMENT_UNIT_ID IN (26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429)\n" +
                "            AND ro.REQUEST_UNIT_ID NOT IN (26072 , 26423, 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429)\n" +
                "            AND ro.IS_SPECIAL_ORDER = 'N'\n" +
                "            AND ro.ASSET_ORDER = 'N'\n" +
                "            AND ro.TRANSFER_TYPE = 'TRANSFER'\n" +
                "            AND DATE(ro.FULFILLMENT_DATE) between SUBDATE(CURRENT_DATE,7)  and SUBDATE(CURRENT_DATE,1) " +
                "            ) AS RO_ITEM_DETAILS\n" +
                "    LEFT JOIN (SELECT \n" +
                "        ro.REQUEST_ORDER_ID AS RO_ID,\n" +
                "            gr1.GOODS_RECEIVED_ID AS GR_ID,\n" +
                "            gr3.GOODS_RECEIVED_STATUS AS GR3_STATUS,\n" +
                "            gri3.SKU_ID,\n" +
                "            gri3.TRANSFERRED_QUANTITY AS GR3_QTY\n" +
                "    FROM\n" +
                "        KETTLE_SCM_DUMP.GOODS_RECEIVED gr3\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED_ITEM gri3 ON gr3.GOODS_RECEIVED_ID = gri3.GOODS_RECEIVED_ID\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED gr2 ON gr2.GOODS_RECEIVED_ID = gr3.PARENT_GR\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED gr1 ON gr1.GOODS_RECEIVED_ID = gr2.PARENT_GR\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.REQUEST_ORDER ro ON gr1.REQUEST_ORDER_ID = ro.REQUEST_ORDER_ID\n" +
                "    WHERE\n" +
                "        gr3.PARENT_GR IS NOT NULL\n" +
                "            AND gr2.PARENT_GR IS NOT NULL\n" +
                "            AND gr1.REQUEST_ORDER_ID IS NOT NULL\n" +
                "            AND gr3.GENERATION_UNIT_ID IN (26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429)\n" +
                "            AND gr3.GENERATED_FOR_UNIT_ID NOT IN (26072 , 26423, 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429)\n" +
                "            AND gr3.GENERATION_TIME BETWEEN DATE_SUB(LAST_DAY(SUBDATE(NOW(),1)),INTERVAL DAY(LAST_DAY(SUBDATE(NOW(),1))) - 1 DAY) and SUBDATE(CURRENT_DATE,1)\n" +
                "            AND ro.IS_SPECIAL_ORDER = 'N'\n" +
                "            AND ro.ASSET_ORDER = 'N'\n" +
                "            AND ro.TRANSFER_TYPE = 'TRANSFER'\n" +
                "            AND DATE(ro.FULFILLMENT_DATE) between SUBDATE(CURRENT_DATE,7)  and SUBDATE(CURRENT_DATE,1) " +
                "           ) AS GR3_SETTLED ON GR3_SETTLED.RO_ID = RO_ITEM_DETAILS.RO_ID\n" +
                "        AND GR3_SETTLED.GR_ID = RO_ITEM_DETAILS.GR_ID\n" +
                "        AND GR3_SETTLED.SKU_ID = RO_ITEM_DETAILS.SKU_ID\n" +
                "    LEFT JOIN (SELECT \n" +
                "        ro.REQUEST_ORDER_ID AS RO_ID,\n" +
                "            gr1.GOODS_RECEIVED_ID AS GR_ID,\n" +
                "            gr2.GOODS_RECEIVED_STATUS AS GR2_STATUS,\n" +
                "            gri2.SKU_ID,\n" +
                "            gri2.TRANSFERRED_QUANTITY AS GR2_QTY\n" +
                "    FROM\n" +
                "        KETTLE_SCM_DUMP.GOODS_RECEIVED gr2\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED_ITEM gri2 ON gr2.GOODS_RECEIVED_ID = gri2.GOODS_RECEIVED_ID\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED gr1 ON gr1.GOODS_RECEIVED_ID = gr2.PARENT_GR\n" +
                "    INNER JOIN KETTLE_SCM_DUMP.REQUEST_ORDER ro ON gr1.REQUEST_ORDER_ID = ro.REQUEST_ORDER_ID\n" +
                "    WHERE\n" +
                "        gr2.PARENT_GR IS NOT NULL\n" +
                "            AND gr1.PARENT_GR IS NULL\n" +
                "            AND gr2.GOODS_RECEIVED_STATUS IN ('SETTLED' , 'INITIATED')\n" +
                "            AND gr1.REQUEST_ORDER_ID IS NOT NULL\n" +
                "            AND gr2.GENERATED_FOR_UNIT_ID IN (26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429)\n" +
                "            AND gr2.GENERATION_UNIT_ID NOT IN (26072 , 26423, 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429)\n" +
                "            AND ro.IS_SPECIAL_ORDER = 'N'\n" +
                "            AND ro.ASSET_ORDER = 'N'\n" +
                "            AND ro.TRANSFER_TYPE = 'TRANSFER'\n" +
                "            AND DATE(ro.FULFILLMENT_DATE) between SUBDATE(CURRENT_DATE,7)  and SUBDATE(CURRENT_DATE,1)\n" +
                "            ) AS GR2_DETAILS ON GR2_DETAILS.RO_ID = RO_ITEM_DETAILS.RO_ID\n" +
                "        AND GR2_DETAILS.GR_ID = RO_ITEM_DETAILS.GR_ID\n" +
                "        AND GR2_DETAILS.SKU_ID = RO_ITEM_DETAILS.SKU_ID\n" +
                "    GROUP BY RO_ITEM_DETAILS.RO_ITEM_ID) AS T\n" +
                "        INNER JOIN\n" +
                "    KETTLE_SCM_DUMP.PRODUCT_DEFINITION PD ON T.PRODUCT_ID = PD.PRODUCT_ID;";
    }
}

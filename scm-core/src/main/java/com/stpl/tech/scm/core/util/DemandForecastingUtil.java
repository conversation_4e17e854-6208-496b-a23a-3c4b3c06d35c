/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.core.util;

import com.stpl.tech.scm.data.model.DayWiseSlotWiseSalesData;
import com.stpl.tech.scm.domain.model.MeanResult;
import com.stpl.tech.scm.domain.model.MeanType;
import com.stpl.tech.util.AppConstants;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Calendar;

@Component
@Log4j2
public class DemandForecastingUtil {
    
    // Default values for demand forecasting calculations
    public static final int DEFAULT_STOCK_OUT_THRESHOLD_IN_MINUTES = 5;
    public static final double DEFAULT_SALES_VALUE = 0.0;
    public static final BigDecimal DEFAULT_HIGH_QUANTILE = BigDecimal.valueOf(0.8);
    public static final BigDecimal DEFAULT_LOW_QUANTILE = BigDecimal.valueOf(0.2);
    public static final int DEFAULT_HISTORICAL_DAYS = 60;

    /**
     * Generate product key by combining productId and dimension
     * Creates a unique identifier for products by concatenating productId with dimension
     * Handles null dimensions by using empty string as fallback
     */
    public static String generateProductKey(Integer productId, String dimension) {
        return productId + "_" + (dimension != null ? dimension : "");
    }

    /**
     * Get current business date minus 1 day for historical data analysis
     * Returns yesterday's date as the starting point for historical data collection
     * Used to determine the historical start date for demand forecasting
     */
    public static LocalDate getHistoricalStartDate() {
        LocalDate currentBusinessDate = LocalDate.now();
        return currentBusinessDate.minusDays(1);
    }


    /**
     * Forecast demand using statistical methods with comprehensive error handling
     * Calculates multiple statistical measures (arithmetic mean, median, trimmed mean, MAD)
     * Applies intelligent method selection based on data characteristics and outlier detection
     * Returns MeanResult with chosen method, mean, standard deviation, and confidence bounds
     * Handles edge cases like null data, infinite values, and calculation errors gracefully
     */
    public static MeanResult forecastDemand(List<Double> dataPoints) {
        try {
            // Corner case: null or empty data
            if (dataPoints == null || dataPoints.isEmpty()) {
                log.error("Data points are null or empty, returning zero result");
                return new MeanResult(0.0, 0.0, MeanType.ARITHMETIC_MEAN, 0.0, 0.0);
            }

            // Corner case: filter out null values and check if any valid data remains
            List<Double> validData = dataPoints.stream()
                    .filter(Objects::nonNull)
                    .filter(d -> !Double.isNaN(d) && !Double.isInfinite(d))
                    .collect(Collectors.toList());

            if (validData.isEmpty()) {
                log.error("No valid data points found after filtering, returning zero result");
                return new MeanResult(0.0, 0.0, MeanType.ARITHMETIC_MEAN, 0.0, 0.0);
            }

            // Sort data with error handling
            List<Double> sorted;
            try {
                sorted = new ArrayList<>(validData);
                Collections.sort(sorted);
            } catch (Exception e) {
                log.error("Error sorting data points: {}", e.getMessage());
                return new MeanResult(0.0, 0.0, MeanType.ARITHMETIC_MEAN, 0.0, 0.0);
            }

            int n = sorted.size();
            if (n == 0) {
                log.error("No data points after processing, returning zero result");
                return new MeanResult(0.0, 0.0, MeanType.ARITHMETIC_MEAN, 0.0, 0.0);
            }

            // Calculate arithmetic mean with error handling
            double arithmeticMean;
            try {
                double sum = 0;
                for (double d : sorted) {
                    if (Double.isFinite(d)) {
                        sum += d;
                    }
                }
                arithmeticMean = sum / n;
                if (Double.isNaN(arithmeticMean) || Double.isInfinite(arithmeticMean)) {
                    arithmeticMean = 0.0;
                }
            } catch (Exception e) {
                log.error("Error calculating arithmetic mean: {}", e.getMessage());
                arithmeticMean = 0.0;
            }

            // Calculate standard deviation with error handling
            double sd;
            try {
                double variance = 0;
                for (double d : sorted) {
                    if (Double.isFinite(d)) {
                        variance += Math.pow(d - arithmeticMean, 2);
                    }
                }
                variance /= n;
                sd = Math.sqrt(variance);
                if (Double.isNaN(sd) || Double.isInfinite(sd)) {
                    sd = 0.0;
                }
            } catch (Exception e) {
                log.error("Error calculating standard deviation: {}", e.getMessage());
                sd = 0.0;
            }

            // Calculate median with error handling
            double median;
            try {
                if (n % 2 == 0) {
                    median = (sorted.get(n/2 - 1) + sorted.get(n/2)) / 2.0;
                } else {
                    median = sorted.get(n/2);
                }
                if (Double.isNaN(median) || Double.isInfinite(median)) {
                    median = 0.0;
                }
            } catch (Exception e) {
                log.error("Error calculating median: {}", e.getMessage());
                median = 0.0;
            }

            // Calculate MAD with error handling
            double mad;
            try {
                List<Double> deviations = new ArrayList<>();
                for (double d : sorted) {
                    if (Double.isFinite(d)) {
                        deviations.add(Math.abs(d - median));
                    }
                }
                Collections.sort(deviations);
                if (deviations.size() % 2 == 0) {
                    mad = (deviations.get(deviations.size()/2 - 1) + deviations.get(deviations.size()/2)) / 2.0;
                } else {
                    mad = deviations.get(deviations.size()/2);
                }
                if (Double.isNaN(mad) || Double.isInfinite(mad)) {
                    mad = 0.0;
                }
            } catch (Exception e) {
                log.error("Error calculating MAD: {}", e.getMessage());
                mad = 0.0;
            }

            // Calculate trimmed mean with error handling
            double trimmedMean;
            double trimmedSd;
            try {
                int trim = Math.max(1, (int)(n * 0.1));
                if (trim >= n || n - trim <= 0) {
                    trimmedMean = median;
                    trimmedSd = sd;
                } else {
                    List<Double> trimmed = sorted.subList(trim, n - trim);
                    if (trimmed.isEmpty()) {
                        trimmedMean = median;
                        trimmedSd = sd;
                    } else {
                        trimmedMean = trimmed.stream()
                                .filter(Double::isFinite)
                                .mapToDouble(Double::doubleValue)
                                .average()
                                .orElse(median);
                        
                        double trimmedVariance = 0;
                        for (double d : trimmed) {
                            if (Double.isFinite(d)) {
                                trimmedVariance += Math.pow(d - trimmedMean, 2);
                            }
                        }
                        trimmedVariance /= trimmed.size();
                        trimmedSd = Math.sqrt(trimmedVariance);
                        
                        if (Double.isNaN(trimmedMean) || Double.isInfinite(trimmedMean)) {
                            trimmedMean = median;
                        }
                        if (Double.isNaN(trimmedSd) || Double.isInfinite(trimmedSd)) {
                            trimmedSd = sd;
                        }
                    }
                }
            } catch (Exception e) {
                log.error("Error calculating trimmed mean: {}", e.getMessage());
                trimmedMean = median;
                trimmedSd = sd;
            }

            // Decision logic with error handling
            MeanType method;
            double finalMean, finalSd;
            try {
                double maxVal = Collections.max(sorted);
                if (maxVal < arithmeticMean * 2) { // no extreme outliers
                    method = MeanType.ARITHMETIC_MEAN;
                    finalMean = arithmeticMean;
                    finalSd = sd;
                } else if (mad == 0) { // rare spike, mostly zeros
                    method = MeanType.MEDIAN;
                    finalMean = median;
                    finalSd = mad;
                } else {
                    method = MeanType.TRIMMED_MEAN;
                    finalMean = trimmedMean;
                    finalSd = trimmedSd;
                }
                
                // Final validation
                if (Double.isNaN(finalMean) || Double.isInfinite(finalMean)) {
                    finalMean = 0.0;
                }
                if (Double.isNaN(finalSd) || Double.isInfinite(finalSd)) {
                    finalSd = 0.0;
                }
            } catch (Exception e) {
                log.error("Error in decision logic: {}", e.getMessage());
                method = MeanType.ARITHMETIC_MEAN;
                finalMean = 0.0;
                finalSd = 0.0;
            }

            // Calculate bounds with error handling
            double lower, upper;
            try {
                lower = Math.max(0, finalMean - finalSd);
                upper = finalMean + finalSd;
                
                if (Double.isNaN(lower) || Double.isInfinite(lower)) {
                    lower = 0.0;
                }
                if (Double.isNaN(upper) || Double.isInfinite(upper)) {
                    upper = finalMean;
                }
            } catch (Exception e) {
                log.error("Error calculating bounds: {}", e.getMessage());
                lower = 0.0;
                upper = finalMean;
            }

            return new MeanResult(finalMean, finalSd, method, lower, upper);
            
        } catch (Exception e) {
            log.error("Unexpected error in forecastDemand: {}", e.getMessage(), e);
            return new MeanResult(0.0, 0.0, MeanType.ARITHMETIC_MEAN, 0.0, 0.0);
        }
    }

    /**
     * Check if date is weekend (Saturday or Sunday)
     * Uses Calendar to determine day of week and returns true for weekends
     * Returns false for null dates or weekdays (Monday-Friday)
     */
    public static boolean isWeekend(Date date) {
        if (date == null) {
            return false;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);
        return dayOfWeek == Calendar.SATURDAY || dayOfWeek == Calendar.SUNDAY;
    }

    /**
     * Check if sales data is operational for demand forecasting analysis
     * Validates that the data record exists and has operational flag set to "YES"
     * Returns true only if data is marked as operational, false otherwise
     */
    public static boolean isOperationalData(DayWiseSlotWiseSalesData data) {
        if (data == null) {
            return false;
        }
        return data.getIsOperational() != null && 
               AppConstants.YES.equals(data.getIsOperational());
    }
}

package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.KWhAutoDayClose;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class KWhAutoDayCloseNotificationTemplate extends AbstractVelocityTemplate {

    private List<KWhAutoDayClose> successfulDayCloses;
    private List<KWhAutoDayClose> failedDayCloses;
    private String basePath;

    public KWhAutoDayCloseNotificationTemplate(List<KWhAutoDayClose> successfulDayCloses, List<KWhAutoDayClose> failedDayCloses, String basePath) {
        this.successfulDayCloses = successfulDayCloses;
        this.failedDayCloses = failedDayCloses;
        this.basePath = basePath;
    }

    @Override
    public String getTemplatePath() {
        return "templates/KWhAutoDayCloseTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/KwhAutoDayClose/" + SCMUtil.getDateString(SCMUtil.getCurrentDateIST()) + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("SUCCESSFUL_AUTO_DAYCLOSES", successfulDayCloses);
        stringObjectMap.put("FAILED_AUTO_DAYCLOSES", failedDayCloses);
        return stringObjectMap;
    }
}

package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "SWITCH_ASSET_DATA")
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class SwitchAssetData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    Long id;

    @Column(name = "NEW_ASSET_ID",nullable = false)
    Integer newAssetId;
    @Column(name = "NEW_ASSET_COMMENTS")
    String newAssetComments;
    @Column(name = "NEW_ASSET_TO_ID",nullable = false)
    Integer newAssetToId;
    @Column(name = "NEW_ASSET_GR_ID",nullable = false)
    Integer newAssetGrId;
    @Column(name = "OLD_ASSET_ID",nullable = false)
    Integer oldAssetId;
    @Column(name = "OLD_ASSET_COMMENTS")
    String oldAssetComments;
    @Column(name = "OLD_ASSET_TO_ID",nullable = false)
    Integer oldAssetToId;
    @Column(name = "OLD_ASSET_GR_ID",nullable = false)
    Integer oldAssetGrId;
    @Column(name = "SWITCH_REASON")
    String switchReason;
    @Column(name="CREATED_BY",nullable = false)
    Integer createdBy;
    @Column(name="REQUESTED_BY",nullable = false)
    Integer requestedBy;
    @Column(name="CREATED_BY_UNIT",nullable = false)
    Integer createdUnit;
    @Column(name="REQUESTING_UNIT",nullable = false)
    Integer requestingUnit;
    @Column(name="TICKET_ID",nullable = false)
    String ticketId;
    @Column(name="AWS_BUCKET")
    String awsBucket;
    @Column(name="AWS_KEY")
    String awsKey;
    @Column(name="STATUS", nullable = false)
    String status;
    @Column(name="IS_EMAIL_SENT", nullable = false)
    String isEmailSent;

}

/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;


import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;


@SuppressWarnings("serial")
@Entity
@Table(name = "STOCK_EVENT_DEFINITION")
public class StockEventDefinitionData implements java.io.Serializable {


    private Integer eventId;

    private Integer parentId;

    /*
       UnitId signifies Location of Asset
    */
    private Integer unitId;

    private String unitType;

    private String eventType;

    private String eventStatus;

    private Integer initiatedBy;

    private Date eventCreationDate;


    private Integer auditedBy;

    private String subType;

    private String subCategory;

    public  String budgetType ;

    public  Integer receivingUnitId ;
    private Date lastUpdationTime ;

    private Integer roId ;
    private  Integer toId ;
    private String isSplit;
    private String deviceInfo;
    private Integer grId;

    public StockEventDefinitionData() {
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "EVENT_ID", unique = true, nullable = false)
    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "UNIT_TYPE", nullable = false)
    public String getUnitType() {
        return unitType;
    }

    public void setUnitType(String unitType) {
        this.unitType = unitType;
    }

    @Column(name = "EVENT_TYPE", nullable = false)
    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    @Column(name = "EVENT_STATUS", nullable = false)
    public String getEventStatus() {
        return eventStatus;
    }

    public void setEventStatus(String eventStatus) {
        this.eventStatus = eventStatus;
    }

    @Column(name = "EVENT_INITIATOR_ID", nullable = false)
    public Integer getInitiatedBy() {
        return initiatedBy;
    }

    public void setInitiatedBy(Integer initiatedBy) {
        this.initiatedBy = initiatedBy;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "EVENT_CREATION_DATE", length = 19)
    public Date getEventCreationDate() {
        return eventCreationDate;
    }

    public void setEventCreationDate(Date eventCreationDate) {
        this.eventCreationDate = eventCreationDate;
    }

    @Column(name = "AUDITOR_ID", nullable = true)
    public Integer getAuditedBy() {
        return auditedBy;
    }


    @Column(name="RECEIVING_UNIT_ID")
    public Integer getReceivingUnitId() {
        return receivingUnitId;
    }

    public void setReceivingUnitId(Integer receivingUnitId) {
        this.receivingUnitId = receivingUnitId;
    }

    @Column(name="BUDGET_TYPE")
    public String getBudgetType() {
        return budgetType;
    }

    public void setBudgetType(String budgetType) {
        this.budgetType = budgetType;
    }





    public void setAuditedBy(Integer auditedBy) {
        this.auditedBy = auditedBy;
    }

    @Column(name ="SUB_TYPE" , nullable = true)
    public String getSubType() {
        return subType;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_UPDATION_TIME")
    public Date getLastUpdationTime() {
        return lastUpdationTime;
    }

    public void setLastUpdationTime(Date lastUpdationTime) {
        this.lastUpdationTime = lastUpdationTime;
    }
    @Column(name = "RO_ID")
    public Integer getRoId() {
        return roId;
    }

    public void setRoId(Integer roId) {
        this.roId = roId;
    }
    @Column(name = "TO_ID")
    public Integer getToId() {
        return toId;
    }

    public void setToId(Integer toId) {
        this.toId = toId;
    }

    @Column(name = "PARENT_ID")
    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    @Column(name = "SUB_CATEGORY")
    public String getSubCategory() {
        return subCategory;
    }

    public void setSubCategory(String subCategory) {
        this.subCategory = subCategory;
    }

    @Column(name = "SPLIT")
    public String getSplit() {
        return isSplit;
    }

    public void setSplit(String split) {
        isSplit = split;
    }

    @Column(name = "DEVICE_INFO")
    public String getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(String deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    @Column(name="GR_ID")
    public Integer getGrId() {
        return grId;
    }
    public void setGrId(Integer grId) {
        this.grId = grId;
    }
}
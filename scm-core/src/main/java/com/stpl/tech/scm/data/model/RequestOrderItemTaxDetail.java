/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import com.stpl.tech.master.tax.model.TaxationDetailDao;

import javax.persistence.*;
import java.math.BigDecimal;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * OrderInfo generated by hbm2java
 */
@Entity
@Table(name = "REQUEST_ORDER_ITEM_TAX_DETAIL")
public class RequestOrderItemTaxDetail implements java.io.Serializable, TaxationDetailDao {

	/**
	 *
	 */
	private static final long serialVersionUID = 6909018841091451616L;
	private Integer orderItemTaxDetailId;
	private RequestOrderItemData orderItem;
	private String taxType;
	private String taxCode;
	private BigDecimal taxPercentage;
	private BigDecimal totalTax;
	private BigDecimal totalAmount;
	private BigDecimal taxableAmount;

	public RequestOrderItemTaxDetail() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ORDER_ITEM_TAX_DETAIL_ID", unique = true, nullable = false)
	public Integer getOrderItemTaxDetailId() {
		return this.orderItemTaxDetailId;
	}

	public void setOrderItemTaxDetailId(Integer orderItemTaxDetailId) {
		this.orderItemTaxDetailId = orderItemTaxDetailId;
	}

	@Column(name = "TAX_CODE", nullable = false, length = 20)
	public String getTaxCode() {
		return taxCode;
	}

	public void setTaxCode(String taxCode) {
		this.taxCode = taxCode;
	}

	@Column(name = "TAX_TYPE", nullable = false, length = 20)
	public String getTaxType() {
		return taxType;
	}

	public void setTaxType(String taxType) {
		this.taxType = taxType;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ORDER_ITEM_ID", nullable = true)
	public RequestOrderItemData getOrderItem() {
		return orderItem;
	}

	public void setOrderItem(RequestOrderItemData orderItem) {
		this.orderItem = orderItem;
	}

	@Column(name = "TAX_PERCENTAGE", precision = 10)
	public BigDecimal getTaxPercentage() {
		return taxPercentage;
	}

	public void setTaxPercentage(BigDecimal taxPercentage) {
		this.taxPercentage = taxPercentage;
	}

	@Column(name = "TOTAL_TAX", precision = 10)
	public BigDecimal getTotalTax() {
		return totalTax;
	}

	public void setTotalTax(BigDecimal totalTax) {
		this.totalTax = totalTax;
	}

	@Column(name = "TOTAL_AMOUNT", precision = 10)
	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	@Column(name = "TAXABLE_AMOUNT", precision = 10)
	public BigDecimal getTaxableAmount() {
		return taxableAmount;
	}

	public void setTaxableAmount(BigDecimal taxableAmount) {
		this.taxableAmount = taxableAmount;
	}

}
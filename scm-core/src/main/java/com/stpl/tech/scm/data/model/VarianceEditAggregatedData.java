/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

@Entity
@Table(name = "VARIANCE_EDIT_AGGREGATED_DATA")
public class VarianceEditAggregatedData {
    @Id
    @Column(name = "VARIANCE_EDIT_AGGREGATED_DATA_ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer varianceEditAggregatedDataId;

    @Column(name = "DAY_CLOSE_EVENT_ID")
    private Integer dayCloseEventId;

    @Column(name = "AGGREGATE_COST")
    private BigDecimal aggregateCost;

    @Column(name = "AGGREGATE_TAX")
    private BigDecimal aggregateTax;

    public Integer getVarianceEditAggregatedDataId() {
        return this.varianceEditAggregatedDataId;
    }

    public void setVarianceEditAggregatedDataId(Integer varianceEditAggregatedDataId) {
        this.varianceEditAggregatedDataId = varianceEditAggregatedDataId;
    }

    public Integer getDayCloseEventId() {
        return this.dayCloseEventId;
    }

    public void setDayCloseEventId(Integer dayCloseEventId) {
        this.dayCloseEventId = dayCloseEventId;
    }

    public BigDecimal getAggregateCost() {
        return this.aggregateCost;
    }

    public void setAggregateCost(BigDecimal aggregateCost) {
        this.aggregateCost = aggregateCost;
    }

    public BigDecimal getAggregateTax() {
        return aggregateTax;
    }

    public void setAggregateTax(BigDecimal aggregateTax) {
        this.aggregateTax = aggregateTax;
    }
}

package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 11-05-2017.
 */

@Entity
@Table(name = "PAYMENT_INVOICE_ITEM_TAX")
public class PaymentInvoiceItemTaxData {

    private Integer taxDetailId;
    private PaymentInvoiceItemData paymentInvoiceItemData;
    private String taxType;
    private BigDecimal taxPercentage;
    private BigDecimal taxValue;


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PAYMENT_INVOICE_ITEM_TAX_ID", unique = true, nullable = false)
    public Integer getTaxDetailId() {
        return taxDetailId;
    }

    public void setTaxDetailId(Integer taxDetailId) {
        this.taxDetailId = taxDetailId;
    }


    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PR_INVOICE_ITEM_ID", nullable = true)
    public PaymentInvoiceItemData getPaymentInvoiceItemData() {
        return paymentInvoiceItemData;
    }

    public void setPaymentInvoiceItemData(PaymentInvoiceItemData paymentInvoiceItemData) {
        this.paymentInvoiceItemData = paymentInvoiceItemData;
    }


    @Column(name = "TAX_TYPE", nullable = false)
    public String getTaxType() {
        return taxType;
    }

    public void setTaxType(String taxType) {
        this.taxType = taxType;
    }

    @Column(name = "TAX_PERCENTAGE", nullable = false)
    public BigDecimal getTaxPercentage() {
        return taxPercentage;
    }

    public void setTaxPercentage(BigDecimal taxPercentage) {
        this.taxPercentage = taxPercentage;
    }

    @Column(name = "TAX_VALUE", nullable = false)
    public BigDecimal getTaxValue() {
        return taxValue;
    }

    public void setTaxValue(BigDecimal taxValue) {
        this.taxValue = taxValue;
    }

}

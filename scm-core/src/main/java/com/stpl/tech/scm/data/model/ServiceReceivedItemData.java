package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "SERVICE_RECEIVED_ITEM")
public class ServiceReceivedItemData {

	private Integer itemId;
	private Integer serviceReceivedDataId;
	private Integer costElementId;
	private String costElementName;
	private String serviceDescription;
	private String ascCode;
	private BigDecimal receivedQuantity;
	private String unitOfMeasure;
	private BigDecimal unitPrice;
	private BigDecimal totalPrice;
	private BigDecimal totalTax;
	private BigDecimal totalAmount;
	private BigDecimal taxRate;
	private BigDecimal tdsRate;
	private Integer businessCostCenterId;
	private String businessCostCenterName;
	private Integer serviceOrderId;
	private Integer serviceOrderItemId;
	private BigDecimal pendingInvoiceQuantity;
	private BigDecimal InvoiceQuantity;
	private Date RecievedcostElementDate;
	private Date RecievedcostElementToDate;



	private List<ServiceReceivedItemTaxData> taxes;
	private List<ServiceOrderToReceiveItemMappingData> mappingList;
	protected String budgetCategory;
	private List<ServiceReceivedItemDrilldownData> serviceReceivedItemDrilldownData;

	public ServiceReceivedItemData() {
	}

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "SERVICE_RECEIVED_ITEM_ID", unique = true, nullable = false)
	public Integer getItemId() {
		return itemId;
	}

	public void setItemId(Integer itemId) {
		this.itemId = itemId;
	}

	@Column(name = "BUSINESS_COST_CENTER_ID", nullable = false)
	public Integer getBusinessCostCenterId() {
		return businessCostCenterId;
	}

	public void setBusinessCostCenterId(Integer allocationCenterId) {
		this.businessCostCenterId = allocationCenterId;
	}

	@Column(name = "BUSINESS_COST_CENTER_NAME", nullable = false)
	public String getBusinessCostCenterName() {
		return businessCostCenterName;
	}

	public void setBusinessCostCenterName(String allocationCenterName) {
		this.businessCostCenterName = allocationCenterName;
	}

	@Column(name = "COST_ELEMENT_ID", nullable = false)
	public Integer getCostElementId() {
		return costElementId;
	}

	public void setCostElementId(Integer costElementId) {
		this.costElementId = costElementId;
	}

	@Column(name = "COST_ELEMENT_NAME", nullable = false)
	public String getCostElementName() {
		return costElementName;
	}

	public void setCostElementName(String costElementName) {
		this.costElementName = costElementName;
	}

	@Column(name = "SERVICE_DESCRIPTION", nullable = false)
	public String getServiceDescription() {
		return serviceDescription;
	}

	public void setServiceDescription(String serviceDescription) {
		this.serviceDescription = serviceDescription;
	}

	@Column(name = "ASC_CODE", nullable = false)
	public String getAscCode() {
		return ascCode;
	}

	public void setAscCode(String hsnCode) {
		this.ascCode = hsnCode;
	}

	@Column(name = "RECEIVED_QUANTITY", nullable = false)
	public BigDecimal getReceivedQuantity() {
		return receivedQuantity;
	}

	public void setReceivedQuantity(BigDecimal receivedQuantity) {
		this.receivedQuantity = receivedQuantity;
	}

	@Column(name = "SERVICE_RECEIVED_ID", nullable = false)
	public Integer getServiceReceivedDataId() {
		return serviceReceivedDataId;
	}

	public void setServiceReceivedDataId(Integer serviceReceivedDataId) {
		this.serviceReceivedDataId = serviceReceivedDataId;
	}

	@Column(name = "TOTAL_AMOUNT", nullable = false)
	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	@Column(name = "TOTAL_TAX", nullable = false)
	public BigDecimal getTotalTax() {
		return totalTax;
	}

	public void setTotalTax(BigDecimal totalTax) {
		this.totalTax = totalTax;
	}

	@Column(name = "TAX_RATE", nullable = false)
	public BigDecimal getTaxRate() {
		return taxRate;
	}

	public void setTaxRate(BigDecimal taxRate) {
		this.taxRate = taxRate;
	}

	@Column(name = "TDS_RATE", nullable = false)
	public BigDecimal getTdsRate() {
		return tdsRate;
	}

	public void setTdsRate(BigDecimal tdsRate) {
		this.tdsRate = tdsRate;
	}

	@Column(name = "TOTAL_PRICE", nullable = false)
	public BigDecimal getTotalPrice() {
		return totalPrice;
	}

	public void setTotalPrice(BigDecimal totalPrice) {
		this.totalPrice = totalPrice;
	}

	@Column(name = "UNIT_PRICE", nullable = false)
	public BigDecimal getUnitPrice() {
		return unitPrice;
	}

	public void setUnitPrice(BigDecimal unitPrice) {
		this.unitPrice = unitPrice;
	}

	@Column(name = "UNIT_OF_MEASURE", nullable = false)
	public String getUnitOfMeasure() {
		return unitOfMeasure;
	}

	public void setUnitOfMeasure(String unitOfMeasure) {
		this.unitOfMeasure = unitOfMeasure;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "serviceReceivedItemId")
	public List<ServiceReceivedItemTaxData> getTaxes() {
		return taxes;
	}

	public void setTaxes(List<ServiceReceivedItemTaxData> taxes) {
		this.taxes = taxes;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "serviceReceivedItemDataId")
	public List<ServiceOrderToReceiveItemMappingData> getMappingList() {
		return mappingList;
	}

	public void setMappingList(List<ServiceOrderToReceiveItemMappingData> mappingList) {
		this.mappingList = mappingList;
	}

	@Column(name = "SERVICE_ORDER_ID", nullable = false)
	public Integer getServiceOrderId() {
		return serviceOrderId;
	}

	public void setServiceOrderId(Integer serviceOrderId) {
		this.serviceOrderId = serviceOrderId;
	}

	@Column(name = "SERVICE_ORDER_ITEM_ID", nullable = false)
	public Integer getServiceOrderItemId() {
		return serviceOrderItemId;
	}

	public void setServiceOrderItemId(Integer serviceOrderItemId) {
		this.serviceOrderItemId = serviceOrderItemId;
	}

	@Column(name = "PENDING_INVOICE_QUANTITY", nullable = true)
	public BigDecimal getPendingInvoiceQuantity() {
		return pendingInvoiceQuantity;
	}

	public void setPendingInvoiceQuantity(BigDecimal pendingInvoiceQuantity) {
		this.pendingInvoiceQuantity = pendingInvoiceQuantity;
	}

	@Column(name = "INVOICE_QUANTITY", nullable = true)
	public BigDecimal getInvoiceQuantity() {
		return InvoiceQuantity;
	}

	public void setInvoiceQuantity(BigDecimal invoiceQuantity) {
		InvoiceQuantity = invoiceQuantity;
	}


	@Column(name = "BUDGET_CATEGORY", nullable = false)
	public String getBudgetCategory() {
		return budgetCategory;
	}

	public void setBudgetCategory(String budgetCategory) {
		this.budgetCategory = budgetCategory;
	}

	@Column(name = "RECEIVED_COST_ELEMENT_DATE")
	public Date getRecievedcostElementDate() {
		return RecievedcostElementDate;
	}

	public void setRecievedcostElementDate(Date recievedcostElementDate) {
		RecievedcostElementDate = recievedcostElementDate;
	}

	@Column(name = "RECEIVED_COST_ELEMENT_TO_DATE")
	public Date getRecievedcostElementToDate() {
		return RecievedcostElementToDate;
	}

	public void setRecievedcostElementToDate(Date recievedcostElementToDate) {
		RecievedcostElementToDate = recievedcostElementToDate;
	}

	@OneToMany(fetch = FetchType.LAZY , mappedBy = "serviceReceivedItemId")
	public List<ServiceReceivedItemDrilldownData> getServiceReceivedItemDrilldownData() {
		return serviceReceivedItemDrilldownData;
	}

	public void setServiceReceivedItemDrilldownData(List<ServiceReceivedItemDrilldownData> serviceReceivedItemDrilldownData) {
		this.serviceReceivedItemDrilldownData = serviceReceivedItemDrilldownData;
	}
}

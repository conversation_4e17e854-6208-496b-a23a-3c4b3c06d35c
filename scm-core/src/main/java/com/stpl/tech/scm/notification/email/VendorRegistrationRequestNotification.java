package com.stpl.tech.scm.notification.email;

import java.util.List;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.notification.email.template.VendorRegistrationRequestNotificationTemplate;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

public class VendorRegistrationRequestNotification extends EmailNotification {

	private VendorRegistrationRequestNotificationTemplate template;
	private EnvType envType;
	private List<String> emails;
	private boolean isNew;
	private String brandName;

	public VendorRegistrationRequestNotification() {
	}

	public VendorRegistrationRequestNotification(VendorRegistrationRequestNotificationTemplate template,
			EnvType envType, List<String> emails, boolean isNew, String brandName) {
		this.template = template;
		this.envType = envType;
		this.emails = emails;
		this.isNew = isNew;
		this.brandName = brandName;
	}

	@Override
	public String[] getToEmails() {
		return emails.toArray(new String[emails.size()]);
	}

	@Override
	public String getFromEmail() {
		return "<EMAIL>";
	}

	@Override
	public String subject() {
		String subject = brandName + (isNew ? " Registration" : " Edit") + " Request - "
				+ SCMUtil.getDate(SCMUtil.getPreviousDateIST());
		if (AppUtils.isDev(envType)) {
			subject = "[Dev] " + subject;
		}
		return subject;
	}

	@Override
	public String body() throws EmailGenerationException {
		try {
			return template.getContent();
		} catch (TemplateRenderingException e) {
			throw new EmailGenerationException("Failed to render the template", e);
		}
	}

	@Override
	public EnvType getEnvironmentType() {
		return envType;
	}

}

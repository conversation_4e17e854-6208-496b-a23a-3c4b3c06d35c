package com.stpl.tech.scm.core.mapper;

import com.stpl.tech.scm.data.model.DayWiseSlotWiseSalesData;
import com.stpl.tech.scm.data.model.DemandForecastingStrategyMetadata;
import com.stpl.tech.scm.data.model.OrderingMultiplierData;
import com.stpl.tech.scm.data.model.UnitWiseOrderingStrategyData;
import com.stpl.tech.scm.domain.model.DayWiseSlotWiseSalesDataDto;
import com.stpl.tech.scm.domain.model.DemandForecastingStrategyMetadataDto;
import com.stpl.tech.scm.domain.model.OrderingMultiplierDataDto;
import com.stpl.tech.scm.domain.model.UnitWiseOrderingStrategyDataDto;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring")
public interface DemandForecastingMapper {

    DemandForecastingMapper INSTANCE = Mappers.getMapper(DemandForecastingMapper.class);

    DayWiseSlotWiseSalesDataDto toDayWiseSlotWiseSalesDataDto(DayWiseSlotWiseSalesData entity);

    DayWiseSlotWiseSalesData toDayWiseSlotWiseSalesData(DayWiseSlotWiseSalesDataDto dto);

    DemandForecastingStrategyMetadataDto toDemandForecastingStrategyMetadataDto(DemandForecastingStrategyMetadata entity);

    DemandForecastingStrategyMetadata toDemandForecastingStrategyMetadata(DemandForecastingStrategyMetadataDto dto);

    UnitWiseOrderingStrategyDataDto toUnitWiseOrderingStrategyDataDto(UnitWiseOrderingStrategyData entity);

    UnitWiseOrderingStrategyData toUnitWiseOrderingStrategyData(UnitWiseOrderingStrategyDataDto dto);

    OrderingMultiplierDataDto toOrderingMultiplierDataDto(OrderingMultiplierData entity);

    OrderingMultiplierData toOrderingMultiplierData(OrderingMultiplierDataDto dto);
}
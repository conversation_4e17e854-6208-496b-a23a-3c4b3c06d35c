package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.HashSet;
import java.util.Set;


@Setter
@Getter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "KETTLE_STOCK_OUT_PRODUCT_WISE_DATA")
public class KettleStockOutProductWiseData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "PRODUCT_ID", nullable = false)
    private Integer productId;

    @Column(name = "DIMENSION", length = 255)
    private String dimension;

    @Column(name = "TOTAL_DOWN_TIME")
    private Integer totalDownTime;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "KETTLE_STOCK_OUT_PERCENTAGE_DATA_ID", nullable = false)
    private KettleStockOutPercentageData kettleStockOutPercentageData;

    @OneToMany(mappedBy = "kettleStockOutProductWiseData", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<KettleStockOutProductWiseTimingsData> kettleStockOutProductWiseTimingsDataSet;

    public KettleStockOutProductWiseData(int productId, String dimension) {
        this.productId = productId;
        this.dimension = dimension;
    }

    public void addTimingsData(KettleStockOutProductWiseTimingsData timingsData) {
        if (kettleStockOutProductWiseTimingsDataSet == null) {
            kettleStockOutProductWiseTimingsDataSet = new HashSet<>();
        }
        kettleStockOutProductWiseTimingsDataSet.add(timingsData);
        timingsData.setKettleStockOutProductWiseData(this);
    }
}

package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.VendorRegistrationException;
import com.stpl.tech.scm.data.model.VendorContractData;
import com.stpl.tech.scm.data.model.PageRequestDetail;
import com.stpl.tech.scm.data.model.ProductionUnitData;
import com.stpl.tech.scm.data.model.PurchaseProfile;
import com.stpl.tech.scm.data.model.SkuDefinitionData;
import com.stpl.tech.scm.data.model.SkuPackagingTaxMapping;
import com.stpl.tech.scm.data.model.SkuPriceData;
import com.stpl.tech.scm.data.model.UnitSkuMapping;
import com.stpl.tech.scm.data.model.UnitSkuVendorMapping;
import com.stpl.tech.scm.data.model.WorkOrderData;
import com.stpl.tech.scm.domain.model.LogType;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.IdCodeNameStatus;
import com.stpl.tech.scm.domain.model.PreviousPricingDataVO;
import com.stpl.tech.scm.domain.model.PriceStatus;
import com.stpl.tech.scm.domain.model.SkuPriceDetail;
import com.stpl.tech.scm.domain.model.SkuPriceUpdate;
import com.stpl.tech.scm.domain.model.UnitVendorSkuMapping;
import com.stpl.tech.scm.domain.model.UpdateUnitVendorSkuMapping;
import com.stpl.tech.scm.domain.model.WorkOrder;
import com.stpl.tech.scm.domain.model.unitSkuMappingDetail;
import com.stpl.tech.util.EmailGenerationException;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by Mohit
 */
public interface SkuMappingDao extends SCMAbstractDao {

	public boolean updatePrices(SkuPriceUpdate data) throws SumoException;

	public List<SkuPriceDetail> searchPricesBySku(int skuId, Integer deliveryLocationId);

	public List<IdCodeNameStatus> searchSkuMappingsForUnit(int unitId);
	public List<UnitSkuVendorMapping> searchActiveVendorMappingsForUnit(int unitId);

	public List<IdCodeNameStatus> searchUnitMappingsForSku(int skuId);

	public boolean updateSkuMappingsForUnit(int employeeId, String name, int unitId, List<Integer> skuIds);

    public List<UnitSkuMapping> getUnitSkuMappingForUnitId(int unitId,List<Integer> skuIds);
	public List<UnitSkuMapping> getUnitSkuMappingForSKUS(List<Integer> skuId);
	public List<UnitSkuMapping> getUnitSkuMappingForSKUS(int skuId,List<Integer> unitId);

	public boolean updateUnitMappingsForSku(int employeeId, String name, int skuId, List<Integer> unitIds);

	Set<Integer> getAllUnitsForSku(int skuId);

	public List<IdCodeNameStatus> searchSkuMappingsForVendor(int vendorId);

	public List<UnitVendorSkuMapping> searchSkuMappingsForVendorAndUnit(int unitId, int vendorId);

	public List<Integer> searchSkuMappingIdsForVendorAndUnit(int unitId, int vendorId);

	public List<UnitVendorSkuMapping> updateSkuMappingsForVendorAndUnit(UpdateUnitVendorSkuMapping data);

	public List<unitSkuMappingDetail> getSkusProfileForUnit(int unit, List<Integer> sku);

	public List<IdCodeNameStatus> searchVendorMappingsForSku(int skuId);

	public List<IdCodeNameStatus> searchVendorMappingsForBusiness(int businessId);

	public boolean updateVendorSkuMapping(int employeeId, String name, int vendorId, int skuId, String status);

	public List<IdCodeNameStatus> allActiveUnits(Integer companyId);

	public List<IdCodeNameStatus> allActiveSKU(Integer companyId);

	public List<IdCodeNameStatus> allActiveVendors();

	public boolean updateSkuProfiles(Map<Integer,String> skuListWithInventoryListId, int unitId, String profile,Map<String,Integer> inventoryListName);

	String getName(String name, int id);

	/**
	 * @param employeeId
	 * @param employeeName
	 * @param vendorId
	 * @param skuIds
	 * @return
	 */
	public boolean addSkuMappingsForVendor(int employeeId, String employeeName, int vendorId, List<IdCodeName> skuIds);

	/**
	 * @param employeeId
	 * @param employeeName
	 * @param skuId
	 * @param vendorIds
	 * @return
	 */
	public boolean addVendorMappingsForSku(int employeeId, String employeeName, int skuId, List<Integer> vendorIds);


	public boolean addVendorMappingsForBusiness(int employeeId, String employeeName, int businessId, List<Integer> vendorIds);

	/**
	 * @param data
	 * @return
	 */
	public boolean cancelPriceUpdate(SkuPriceUpdate data);

	/**
	 * @param data
	 * @return
	 */
	public boolean updatePriceStatus(SkuPriceUpdate data);

	public boolean addPrice(SkuPriceUpdate data);

	public void updateSkuPricesFromCurrentDay(SkuPriceUpdate data);

	public List<SkuPriceDetail> searchSkuPricesForVendorAndUnit(int unitId, int vendorId, Integer dispatchLocationId, Integer deliveryLocationId);

	public List<SkuPriceDetail> getSkuPackagingPriceForVendorsForUnit(int unitId, Set<Integer> vendorIds, Integer deliveryLocationId);

	public List<SkuPriceDetail> searchPricesByVendorDeliveryLocation(int vendorId, Integer locationId, boolean isUpdated, boolean setUpdated);

	List<SkuPriceDetail> searchPricesByVendorAndStatus(int vendorId, String status);

    public List<UnitSkuMapping> searchSkuMappingsForUnitAndVendor(int unitId, int vendorId);

	public List<SkuPriceDetail> getSkuPackagingPriceForUnit(int unitId, Integer deliveryLocationId);

    public List<PurchaseProfile> getPurchaseMappings(List<Integer> profiles);

	public List<String> getDistanceOfUnits(int firstUnitId, int secondUnitId);

	public boolean updateUnitDistanceMappingData(int firstUnitId, Integer firstMappingId, BigDecimal firstDistance,
			int secondUnitId, Integer secondMappingId, BigDecimal secondDistance,boolean saveZipDis) throws SumoException;


	void saveZipCodeDistance(int firstUnitId, int secondUnitId, BigDecimal firstDistance, BigDecimal secondDistance) throws SumoException;

	public int getVendorSkuMappingId(int skuId,int vendorId);

	public boolean updateSkuLeadTime(int vendorId,int leadTime);

    public Integer findProductionLine(int unitId, int skuId);

	public int findSKUID(int productId);

	public ProductionUnitData findProductionLine(int productIonUnitId);

    public List<SkuDefinitionData> findAllSkuDefinition(int productId);

	public List<UnitSkuMapping> getActiveUnitSkuMappings(List<Integer> skuIds ,List<Integer> unitIds);

	public UnitSkuMapping findSkuMappingBySkuAndUnit(Integer skuId,Integer unitId );

	public List<SkuPackagingTaxMapping> findAllUnitSkuPackagingTaxMappingByStatus(Integer skuId, Integer packagingId , List<String> statuses);

	public List<SkuPackagingTaxMapping> findAllUnitSkuPackagingTaxMapping(List<Integer> skuIds);

	public Boolean updateUnitSkuPackagingTaxMapping(Integer employeeId, Integer skuId,Integer packagingId  , Map<Integer,String> unitTaxMap);

	public List<SkuPackagingTaxMapping> findAllUnitSkuPackagingTaxMappingByUnit(List<Integer> skuIds,Integer unitId);


	void processPriceRequestForVendor(SkuPriceUpdate val, PriceStatus key, PriceStatus currentStatus, Set<Integer> processedId) throws SumoException;

    List<SkuPriceDetail> vendorPriceChangeAsPerStatus(Integer vendorId,List<String> status);

	List<VendorContractData> getVendorContractV2(Integer vendorId, String status, Date startDate, Date endDate, Integer vendorContractId);

	void updateCommonStatesLogsData(String fromState, String toState, Integer updatedBy, Integer contractId, LogType logType, String logMsg);

	boolean cancelVendorContractV2(WorkOrder workOrderId, Integer loggedInUser) throws SumoException, EmailGenerationException;

	void applyContractV2();

	PageRequestDetail findByToken(String token) throws VendorRegistrationException;

	PageRequestDetail findPRDByToken(String token);

	void expiryContractV2();

	PageRequestDetail findPageRequestByEventTypeAndRecordStatus(String vendorContractEmployee, Integer vendorContractId);

    void cancelAllContractMailRequest(Integer vendorContractId, String eventType);

	String getPageRequestLink(Integer contractId, String eventType);

	SkuPriceData getSkuPriceData(int skuPriceDataId);

	List<PreviousPricingDataVO> findPreviousPricesOfSkuByLocation(Integer skuId, List<Integer> listOfUnitIds, Integer packagingId);
	List<Object[]> getPreviousPricesOfSkuForCustomer(Integer skuId, Integer packagingId);

	void applyWorkOrder(WorkOrderData woData, Integer loggedInUser);
}

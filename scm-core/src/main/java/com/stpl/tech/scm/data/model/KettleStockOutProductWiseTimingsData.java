package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * Entity for KETTLE_STOCK_OUT_PRODUCT_WISE_TIMINGS_DATA table
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "KETTLE_STOCK_OUT_PRODUCT_WISE_TIMINGS_DATA")
public class KettleStockOutProductWiseTimingsData {

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    private Integer id;

    @Temporal(value = TemporalType.TIMESTAMP)
    @Column(name = "STOCK_OUT_TIME")
    private Date stockOutTime;

    @Temporal(value = TemporalType.TIMESTAMP)
    @Column(name = "STOCK_IN_TIME")
    private Date stockInTime;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "KETTLE_STOCK_OUT_PRODUCT_WISE_DATA_ID", nullable = false)
    private KettleStockOutProductWiseData kettleStockOutProductWiseData;

    public KettleStockOutProductWiseTimingsData(Date stockOutTime, Date stockInTime) {
        this.stockOutTime = stockOutTime;
        this.stockInTime = stockInTime;
    }
}

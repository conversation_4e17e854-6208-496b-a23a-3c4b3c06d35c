package com.stpl.tech.scm.notification.email;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.notification.email.template.UnitClosureSuspendNotificationTemplate;
import com.stpl.tech.scm.notification.email.template.VendorSOEmailNotificationTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
public class UnitClosureSuspendNotification extends EmailNotification {
    private UnitClosureSuspendNotificationTemplate unitClosureSuspendNotificationTemplate;
    private EnvType envType;
    private String unitName;


    @Override
    public String[] getToEmails() {
            return new String[] { "<EMAIL>" };
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        String subjectOfEmail = String.format("Unit Closure Suspend for unit : "+unitName);
        if (SCMUtil.isDev(envType)) {
            subjectOfEmail = " [DEV] : " + subjectOfEmail;
        }
        return subjectOfEmail;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return unitClosureSuspendNotificationTemplate.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}

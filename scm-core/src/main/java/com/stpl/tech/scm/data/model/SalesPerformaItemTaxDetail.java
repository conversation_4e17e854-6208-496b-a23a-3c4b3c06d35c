package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 02-07-2018.
 */
@Entity
@Table(name = "SALES_PERFORMA_ITEM_TAX_DETAIL")
public class SalesPerformaItemTaxDetail {

    private Integer taxDetailId;
    private String taxCode;
    private String taxType;
    private BigDecimal percentage;
    private BigDecimal value;
    private SalesPerformaInvoiceItemData invoiceItem;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "TAX_DETAIL_ID", unique = true, nullable = false)
    public Integer getTaxDetailId() {
        return taxDetailId;
    }

    public void setTaxDetailId(Integer taxDetailId) {
        this.taxDetailId = taxDetailId;
    }

    @Column(name = "TAX_TYPE", nullable = false)
    public String getTaxType() {
        return taxType;
    }

    public void setTaxType(String taxType) {
        this.taxType = taxType;
    }

    @Column(name = "TAX_CODE", nullable = false)
    public String getTaxCode() {
        return taxCode;
    }

    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode;
    }

    @Column(name = "PERCENTAGE", nullable = false)
    public BigDecimal getPercentage() {
        return percentage;
    }

    public void setPercentage(BigDecimal percentage) {
        this.percentage = percentage;
    }

    @Column(name = "VALUE", nullable = false)
    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "INVOICE_ITEM_ID", nullable = false)
    public SalesPerformaInvoiceItemData getInvoiceItem() {
        return invoiceItem;
    }

    public void setInvoiceItem(SalesPerformaInvoiceItemData invoiceItem) {
        this.invoiceItem = invoiceItem;
    }
}

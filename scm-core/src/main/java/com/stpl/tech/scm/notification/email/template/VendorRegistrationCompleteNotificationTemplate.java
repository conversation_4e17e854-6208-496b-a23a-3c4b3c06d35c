package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import org.apache.velocity.tools.generic.DateTool;

import java.util.HashMap;
import java.util.Map;

public class VendorRegistrationCompleteNotificationTemplate extends AbstractVelocityTemplate {

    private String basePath;
    private VendorDetail vendorDetail;
    private Map<String, Pair<Object,Object>> vendorDifference;


    public VendorRegistrationCompleteNotificationTemplate() {

    }

    public VendorRegistrationCompleteNotificationTemplate(
            String basePath,
            VendorDetail vendorDetail,
            Map<String, Pair<Object,Object>> vendorDifference
    ) {
        this.basePath = basePath;
        this.vendorDetail=vendorDetail;
        this.vendorDifference=vendorDifference;
    }

    public VendorRegistrationCompleteNotificationTemplate(
            String basePath,
           VendorDetail vendorDetail
    ) {
        this.basePath = basePath;
        this.vendorDetail=vendorDetail;
    }

    @Override
    public String getTemplatePath() {
        return "templates/VendorRegistrationComplete.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/vendor/registration/complete/" + vendorDetail.getVendorId() + '_'
                + SCMUtil.getDateString(SCMUtil.getCurrentDateIST()) + ".html";

    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("vendor", vendorDetail);
        stringObjectMap.put("dateTool", new DateTool());
        if(vendorDifference != null) {
            stringObjectMap.put("diff", vendorDifference);
            stringObjectMap.put("keys", vendorDifference.keySet());
        }
        return stringObjectMap;
    }
}

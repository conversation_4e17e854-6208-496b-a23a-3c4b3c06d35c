/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.core.service;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.util.EnvType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@RefreshScope
@Service
public class EnvProperties {

    @Autowired
    Environment env;

    public String getBasePath() {
        return env.getProperty("server.base.dir");
    }

    public String getScmBaseUrl() {
        return env.getProperty("scm.service.url");
    }

    public EnvType getEnvType() {
        return EnvType.valueOf(env.getProperty("environment.type"));
    }

    public boolean getRunAutoReports() {
        return Boolean.valueOf(env.getProperty("run.auto.report", "false"));
    }

    public boolean sendVendorEmails() {
        return Boolean.valueOf(env.getProperty("send.vendor.email", "false"));
    }

    public String getDayCloseURLinKettle() {
        return env.getProperty("kettle.day.close.url");
    }

    public boolean sendVendorSMS() {
        return Boolean.valueOf(env.getProperty("send.vendor.sms", "false"));
    }

    public String[] vendorEmailCC() {
        return env.getProperty("vendor.email.cc", "").split(",");
    }

    public String[] vendorPurchaseEmailCC() {
        return env.getProperty("vendor.po.email.cc", "").split(",");
    }

    public String getInventoryQueuePrefix() {
        return env.getProperty("sqs.message.queue.prefix", "DEV");
    }

    public String getAuthToken() {
        return env.getProperty("scm.client.token");
    }

    public String getKettleServiceBasePath() {
        return env.getProperty("base.path.kettle.service");
    }

    public String getMasterServiceBasePath() {
        return env.getProperty("base.path.master.service");
    }

    public String getInventoryServiceBasePath() {
        return env.getProperty("base.path.inventory.service");
    }

    public String getVendorRequestUrl() {
        return env.getProperty("vendor.request.url");
    }

    public String getVendorContractUrl() {
        return env.getProperty("vendor.contract.url");
    }

    public String getSystemContractUrl() {
        return env.getProperty("system.contract.url");
    }

    public String getVendorEditEmailNotification() {
        return env.getProperty("vendor.edit.email.notification");
    }

    public String getVendorClientToken() {
        return env.getProperty("vendor.registration.token");
    }

    public String getS3Bucket() {
        return env.getProperty("amazon.s3.bucket", "chaayosdevtest");
    }

    public String getProdDocumentS3Bucket() {
        return env.getProperty("chaayos.document.bucket", "dev.chaayos.document");
    }

    public String getAssetImageBucket() {
        return env.getProperty("asset.image.bucket", "dev.assets.image");
    }

    public String getAssetImageUrl() {
        return env.getProperty("asset.image.cloudfront", "https://d2riuj8k13vflp.cloudfront.net/");
    }

    public String getProdDocument() {
        return env.getProperty("chaayos.doc.cloudfront", "https://d3mma452ftbk51.cloudfront.net/");
    }

    public String getCapexClosureBucket() {
        return env.getProperty("amazon.s3.closure.bucket", "capex-closure");
    }

    public String getS3ProductBucket() {
        return env.getProperty("amazon.s3.product.bucket", "product.image.dev");
    }

    public BigDecimal getPOLimit() {
        return BigDecimal.valueOf(Double.parseDouble(env.getProperty("po.cost.limit", "10000")));
    }

    public boolean getDayCloseCheck() {
        return Boolean.parseBoolean(env.getProperty("day.close.check", "false"));
    }

    public Integer getScrapUnitId(String unitRegion) {
        return Integer.valueOf(env.getProperty("scrap." + unitRegion.toLowerCase() + ".unit"));
    }

    public Integer getVendorLedgerLimit() {
        return Integer.parseInt(env.getProperty("vendor.ledger.limit", "-50000"));
    }

    public Date getFinancialCalendarStart() {
        return SCMUtil.parseDate(env.getProperty("finance.calendar.start", "2017-06-01 00:00:00"));
    }

    public Date getPaymentRequestLaunchDate() {
        return SCMUtil.parseDate(env.getProperty("payment.request.launch", "2017-11-01 00:00:00"));
    }

    public long getGatepassLimit() {
        return Long.parseLong(env.getProperty("gatepass.limit", "50000"));
    }

    public boolean removeDumplicateKeyError() {
        return Boolean.valueOf(env.getProperty("remove.duplicate.key.error.job", "false"));
    }

    public Float getMaxApplicableDepreciationPercentage() {
        return new Float(env.getProperty("max.applicable.depreciation.percentage", "0.95"));
    }

    public boolean vendorGrInvoiceNumberValidation() {
        return Boolean.valueOf(env.getProperty("vendor.gr.invoice.number.validation", "false"));
    }

    public Integer getCalculationDate() {
        return Integer.parseInt(env.getProperty("ordering.calculationDates"));
    }

    public Integer getCategoryBuffer() {
        return Integer.parseInt(env.getProperty("ordering.categoryBuffer"));
    }

    public Integer getBuffer() {
        return Integer.parseInt(env.getProperty("ordering.buffer"));
    }

    public Integer getOrderDayDiff() {
        return Integer.parseInt(env.getProperty("ordering.dayDifference"));
    }

    public Integer getCloningDayDiff() {
        return Integer.parseInt(env.getProperty("cloning.dayDifference"));
    }

    public Integer getPaymentThresholdDays() {
        return Integer.parseInt(env.getProperty("payment.ThresholdDays"));
    }

    public Integer getUtrUploadThresholdDays() {
        return Integer.parseInt(env.getProperty("utrUpload.ThresholdDays"));
    }

    public String getFountain9UserName() {
        return env.getProperty("fountain9.username");
    }

    public String getFountain9Password() {
        return env.getProperty("fountain9.password");
    }

    public String getFountain9IsSuperUser() {return env.getProperty("fountain9.isSuperUser");}

    public String getFountain9AccessTokenUrl() {return env.getProperty("fountain9.accessTokenUrl");}

    public String getFountain9ForecastReportUrl() {return env.getProperty("fountain9.forecastReportUrl");}

    public String getFountain9Metric() {return env.getProperty("fountain9.metric");}

    public String getFountain9TimeGranularity() {return env.getProperty("fountain9.timeGranularity");}

    public String getGoogleApiKey() {return env.getProperty("google.apikey");}

    public String getFountain9Columns() {
        return env.getProperty("fountain9.columns");
    }

    public String getFountain9ScmColumns() {
        return env.getProperty("fountain9.scm.columns");
    }

    public String getDimensionsAggregatedOn() {
        return env.getProperty("dimensions.aggregated.on");
    }

    public String getPartnerServiceBaseUrl() {
        return env.getProperty("partner.service.base.url");
    }

    public String getChannelPartnerClientToken() {
        return env.getProperty("channelpartner.client.token");
    }

    public String getKnockBaseUrl() {
        return env.getProperty("knock.base.url");
    }

    public String getKnockMasterToken() {
        return env.getProperty("knock.master.token");
    }

    public Integer getSpecialOrderBufferTime() {
        return Integer.parseInt(env.getProperty("special.order.buffer.hours", "2"));
    }

    public String getSandBoxBaseUrl() {
        return env.getProperty("sandbox.base.url", "https://api.sandbox.co.in/");
    }

    public String getSandBoxXApiKey() {
        return env.getProperty("sandbox.x.api.key", "key_live_mu2bxji762fMnn956mUMvtsLUHjvIGfO");
    }

    public String getSandBoxXSecretKey() {
        return env.getProperty("sandbox.x.secret.key", "secret_live_ZXzRcQAlkGKjBZtoayXRbIiwr3071R6q");
    }

    public String getSandBoxXApiVersion() {
        return env.getProperty("sandbox.x.api.version", "1.0");
    }

    public BigDecimal getVarAckCafePercent() {
        return BigDecimal.valueOf(Double.parseDouble(env.getProperty("variance_ack_cafe_percent", "0")));
    }

    public String getMaintanenceUnitIds() {
        return env.getProperty("maintanence.monk.warehouse.units", "");
    }

    public String getMonkConsumableProductIds() {
        return env.getProperty("monk.consumable.product.ids", "");
    }

    public Boolean isMaintenanceUnit(Integer unitId) {
        List<Integer> maintenanceUnitIds = Arrays.stream(getMaintanenceUnitIds().split(",")).map(Integer::parseInt)
                .collect(Collectors.toList());
        return maintenanceUnitIds.contains(unitId);
    }

    public Boolean isMonkConsumableItem(Integer productId) {
        List<Integer> monkConsumableProductIds = Arrays.stream(getMonkConsumableProductIds().split(",")).map(Integer::parseInt)
                .collect(Collectors.toList());
        return monkConsumableProductIds.contains(productId);
    }

    public String getChaiMonkUnitId() {
        return env.getProperty("chai.monk.unit", "26033");
    }

    public String getUnitsForStockTakeThroughApp() {
        return env.getProperty("units.for.dayClose.through.app", "");
    }

    public List<Integer> getExcludedUnitsForStockTakeThroughApp() {
        return Arrays
                .stream(env.getProperty("exclude.units.for.dayClose.through.app", "").split(","))
                .map(Integer::parseInt)
                .collect(Collectors.toList());
    }

    public String getMonkCalibrationExcludedProductIdsList() {
        return env.getProperty("monk.calibration.excluded.productIds", "");
    }

    public String getVendorSoContractUrl() {
        return env.getProperty("vendor.contract.so.url");
    }

    public boolean checkUnitClosureRequired() {
        return Boolean.valueOf(env.getProperty("unit.closure.required", "true"));
    }

    public String getByPassUsersForMerchandise() {
        return env.getProperty("bypass.user.for.merchandise.transfer", "");
    }

    public boolean getByPassFlagForMerchandise() {
        return Boolean.parseBoolean(env.getProperty("bypass.flag.for.merchandise.transfer", "false"));
    }

    public String getBulkOrderMinimLimit() {
        return env.getProperty("bulk.order.minimum.limit", "2000");
    }

    public boolean isKWhAutoDayCloseEnabled() {
        return Boolean.valueOf(env.getProperty("enable.k.wh.auto.day.close", "false"));
    }

    public String getExcludedUnitsForAutoDayClose() {
        return env.getProperty("excluded.units.for.auto.day.close", "");
    }

    public String getGatePassBypassIds() {
        return env.getProperty("gate.pass.bypass.ids", "140199");
    }

    public String getAuditorsUserId() {
        return env.getProperty("auditors.user.id", "");
    }

    public Boolean isUserHasAuditorAccess(Integer userId) {
        List<Integer> authorizedAuditorIds = Arrays.stream(getAuditorsUserId().split(",")).map(Integer::parseInt)
                .collect(Collectors.toList());
        return authorizedAuditorIds.contains(userId);
    }

    public String getThresholdStockOutPercentage() {
        return env.getProperty("stock.out.percentage", "5");
    }

    public String getThresholdWastagePercentage() {
        return env.getProperty("wastage.out.percentage", "5");
    }

    public Integer dayCloseExcludeCompany() {
        return Integer.valueOf(env.getProperty("day.close.exclude.company.id", "1005"));
    }

    public String getListOfBrandIds() {
        return env.getProperty("brand.brand.ids", "1");
    }

    public List<String> getDohfulEwayEmails() {
        return Arrays.stream(env.getProperty("eway.dohful.email.ids", "").split(",")).collect(Collectors.toList());
    }

    public List<String> getCorporateAdminEmailIds() {
        return Arrays.stream(env.getProperty("corporate.operation.admin.email.ids", "").split(",")).collect(Collectors.toList());
    }

    public List<Integer> getChaayosCompanyIds() {
        return Arrays.stream(
                env.getProperty("chaayos.company.ids", "1000").split(",")
        ).map(Integer::parseInt).toList();
    }

    public String getCashPurchaseVendors() {
        return env.getProperty("cash.purchase.vendor.ids", "");
    }

    public Boolean allowManualFaStockTakeReport() {
        return Boolean.valueOf(env.getProperty("allow.manual.fa.stock.take.report", "false"));
    }

    public String getExcludedEmailsForDayClose() {
        return env.getProperty("excluded.emails.for.day.close", "");
    }

    public List<String> getExcludedEmailsForDayCloseList() {
        return Arrays.stream(getExcludedEmailsForDayClose().split(","))
                .map(String::trim)
                .filter(email -> !email.isEmpty())
                .collect(Collectors.toList());
    }

    public String getProductWiseBudgetDeductionDeploymentDate() {
        return env.getProperty("products.department.deployment.date", "03-09-2025");
    }

    public List<String> getDohfulWhVarianceEmails() {
        return Arrays.stream(env.getProperty("variance.dohful.email.ids", "").split(",")).collect(Collectors.toList());
    }

    public List<Integer> getVendorPriceMappingApproverIds() {
        return Arrays.stream(
                env.getProperty("vendor.price.mapping.admin.ids", "")
                        .split(",")
        ).map(Integer::parseInt).toList();
    }

    public String getThresholdTimeForFullFillmentReport() {
        return env.getProperty("threshold.time.fullfillment.report", "07:00:00");
    }

    public String getThresholdTimeForOnTimeRORaised() {
        return env.getProperty("threshold.time.ro.raise.fullfillment.report", "11:00:00");
    }
  
    public Integer getDefaultAuditorRequestTo() {
        return Integer.valueOf(env.getProperty("default.auditor.request.to.id", "121624"));
    }


    public List<Integer> getSkipFilteringGatePassApprovalsFor() {
        return Arrays.stream(
                env.getProperty("skip.filtering.gatepass.approver.flow.ids", "140199")
                        .split(",")
        ).map(Integer::parseInt).toList();
    }
}

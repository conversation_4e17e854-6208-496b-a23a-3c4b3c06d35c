package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;

@Entity
@Table(name = "UNIT_WISE_ORDERING_STRATEGY_DATA")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UnitWiseOrderingStrategyData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "UNIT_ID", nullable = false)
    private Integer unitId;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "STRATEGY_ID", nullable = false)
    private DemandForecastingStrategyMetadata demandForecastingStrategyMetadata;

    @Column(name = "STATUS", length = 10)
    private String status;

    @Column(name = "PROXY_UNIT_ID")
    private Integer proxyUnitId;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ORDERING_MULTIPLIER_DATA_ID")
    private OrderingMultiplierData orderingMultiplierData;
}

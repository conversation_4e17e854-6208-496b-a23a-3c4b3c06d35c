package com.stpl.tech.scm.core.service.sqs;

import com.amazon.sqs.javamessaging.message.SQSObjectMessage;
import com.amazon.sqs.javamessaging.message.SQSTextMessage;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.stpl.tech.scm.core.service.MonkWastageManagementService;
import com.stpl.tech.scm.domain.model.MonkWastageDetailDto;
import com.stpl.tech.util.domain.GenericLogData;
import com.stpl.tech.util.domain.adapter.FlexibleDateDeserializer;
import org.apache.commons.lang.StringEscapeUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageListener;
import java.util.Date;

public class SumoListener implements MessageListener {

    private static final String GENERIC_LOG_DATA = GenericLogData.class.getSimpleName();
    private static final String MONK_WASTAGE_DETAIL_DTO = MonkWastageDetailDto.class.getSimpleName();
    private static final Logger LOG = LoggerFactory.getLogger(SumoListener.class);

    private static final Gson gson = new GsonBuilder().registerTypeAdapter(Date.class, new FlexibleDateDeserializer()).create();

    private MonkWastageManagementService monkWastageManagementService;

    public SumoListener(MonkWastageManagementService monkWastageManagementService) {
        this.monkWastageManagementService = monkWastageManagementService;
    }

    @Override
    public void onMessage(Message message) {
        if (message != null) {
            try {
                if (message instanceof SQSObjectMessage) {
                    Object messageObject = ((SQSObjectMessage) message).getObject();
                    if(messageObject != null && GENERIC_LOG_DATA.equals(messageObject.getClass().getSimpleName())){
                        GenericLogData genericLogData = (GenericLogData) messageObject;
                        LOG.info("Processing Sumo Log - {}", genericLogData.getClassName());
                        if (MONK_WASTAGE_DETAIL_DTO.equals(genericLogData.getClassName())) {
                            MonkWastageDetailDto monkWastageDetailDto = gson.fromJson(genericLogData.getJsonData(), MonkWastageDetailDto.class);
                            monkWastageManagementService.saveMonkWastageDetail(monkWastageDetailDto, genericLogData.getUnitId());
                        }
                    }else{
                        processMessage(((SQSObjectMessage) message).getMessageBody());
                    }
                } else if (message instanceof SQSTextMessage) {
                    processMessage(((SQSTextMessage) message).getText());
                } else {
                    LOG.info("Message Not Acknowledged {}", message.getJMSMessageID());
                }
                message.acknowledge();
            } catch (JMSException e) {
                LOG.error("Error in acknowledging message for SumoListener", e);
            }
        }
    }

    private void processMessage(String data) {
        data = StringEscapeUtils.unescapeJava(data);
        if(data.startsWith("\"") && data.endsWith("\"")) {
            data = data.substring(1, data.length() - 1);
        }else{
            if(data.substring(0,1).equals('"')){
                data = data.substring(1, data.length() - 1);
            }
        }
        
        JSONObject obj = new JSONObject(data);
        try {
            String className = obj.getString("className");
            LOG.info("Processing Sumo Log - {}", className);
            if (GENERIC_LOG_DATA.equals(className)) {
                // Handle dynamic jsonData field - can be string or object
                if (obj.has("jsonData") && obj.has("jsonDataClassName")) {
                    String jsonDataClassName = obj.getString("jsonDataClassName");
                    if (MONK_WASTAGE_DETAIL_DTO.equalsIgnoreCase(jsonDataClassName)) {
                        Object jsonDataField = obj.get("jsonData");
                        MonkWastageDetailDto monkWastageDetailDto;
                        Integer unitId;
                        
                        if (jsonDataField instanceof JSONObject) {
                            // New format: jsonData is an object
                            JSONObject jsonDataObj = (JSONObject) jsonDataField;
                            monkWastageDetailDto = gson.fromJson(jsonDataObj.toString(), MonkWastageDetailDto.class);
                            unitId = jsonDataObj.optInt("unitId", 0);
                            if (unitId == 0) {
                                LOG.warn("UnitId not found in jsonData object, attempting to extract from root level");
                                unitId = obj.optInt("unitId", 0);
                            }
                        } else {
                            // Old format: jsonData is a string, fallback to original GenericLogData parsing
                            try {
                                GenericLogData genericLogData = gson.fromJson(data, GenericLogData.class);
                                monkWastageDetailDto = gson.fromJson(genericLogData.getJsonData(), MonkWastageDetailDto.class);
                                unitId = genericLogData.getUnitId();
                            } catch (Exception fallbackException) {
                                LOG.error("Failed to parse with GenericLogData fallback for data: {}", data, fallbackException);
                                return;
                            }
                        }
                        if (unitId != null && unitId > 0) {
                            monkWastageManagementService.saveMonkWastageDetail(monkWastageDetailDto, unitId);
                        } else {
                            LOG.error("UnitId is null or invalid for MonkWastageDetailDto processing");
                        }
                    }
                } else {
                    // Fallback to original GenericLogData parsing for backward compatibility
                    GenericLogData genericLogData = gson.fromJson(data, GenericLogData.class);
                    if (MONK_WASTAGE_DETAIL_DTO.equalsIgnoreCase(genericLogData.getJsonDataClassName())) {
                        MonkWastageDetailDto monkWastageDetailDto = gson.fromJson(genericLogData.getJsonData(), MonkWastageDetailDto.class);
                        monkWastageManagementService.saveMonkWastageDetail(monkWastageDetailDto, genericLogData.getUnitId());
                    }
                }
            } else {
                LOG.info("Unhandled message type: {}", className);
            }
        } catch (Exception e) {
            LOG.error("Exception occurred during processing sumo log for data : {} ", data, e);
        }
    }
} 
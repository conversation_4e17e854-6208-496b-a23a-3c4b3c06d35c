package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "PAYMENT_INVOICE_ITEM")
public class PaymentInvoiceItemData {

    private Integer id;
    private Integer skuId;
    private String skuName;
    private String hsn;
    private String uom;
    private Integer packagingId;
    private BigDecimal conversionRatio;
    private BigDecimal quantity;
    private BigDecimal totalAmount;
    private BigDecimal totalTax;
    private BigDecimal totalPrice;
    private BigDecimal unitPrice;
    private BigDecimal packagingPrice;
    private String packagingName;
    private BigDecimal tdsRate;
    private List<PaymentInvoiceItemTaxData> taxes;
    private PaymentInvoiceData paymentInvoiceData;
    private Integer serviceReceivedId;
    private Integer serviceReceivedItemId;
    private Date skuDate;
    private Date toSkuDate;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PAYMENT_INVOICE_ITEM_ID")
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "SKU_ID", nullable = false)
    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    @Column(name = "SKU_NAME", nullable = false)
    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    @Column(name = "UOM", nullable = false)
    public String getUom() {
        return uom;
    }

    public void setUom(String uom) {
        this.uom = uom;
    }

    @Column(name = "QUANTITY", nullable = false)
    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PAYMENT_INVOICE_ID", nullable = false)
    public PaymentInvoiceData getPaymentInvoiceData() {
        return paymentInvoiceData;
    }

    public void setPaymentInvoiceData(PaymentInvoiceData paymentInvoiceData) {
        this.paymentInvoiceData = paymentInvoiceData;
    }

    @Column(name = "HSN", nullable = false)
    public String getHsn() {
        return hsn;
    }

    public void setHsn(String hsn) {
        this.hsn = hsn;
    }

    @Column(name = "PACKAGING_ID", nullable = false)
    public Integer getPackagingId() {
        return packagingId;
    }

    public void setPackagingId(Integer packagingId) {
        this.packagingId = packagingId;
    }

    @Column(name = "CONVERSION_RATIO", nullable = false)
    public BigDecimal getConversionRatio() {
        return conversionRatio;
    }

    public void setConversionRatio(BigDecimal conversionRatio) {
        this.conversionRatio = conversionRatio;
    }

    @Column(name = "TOTAL_AMOUNT", nullable = false)
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    @Column(name = "TOTAL_TAX", nullable = false)
    public BigDecimal getTotalTax() {
        return totalTax;
    }

    public void setTotalTax(BigDecimal totalTax) {
        this.totalTax = totalTax;
    }

    @Column(name = "TOTAL_PRICE", nullable = false)
    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    @Column(name = "UNIT_PRICE", nullable = false)
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    @Column(name = "PACKAGING_NAME", nullable = false)
    public String getPackagingName() {
        return packagingName;
    }

    public void setPackagingName(String packagingName) {
        this.packagingName = packagingName;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "paymentInvoiceItemData")
    public List<PaymentInvoiceItemTaxData> getTaxes() {
        return taxes;
    }

    public void setTaxes(List<PaymentInvoiceItemTaxData> taxes) {
        this.taxes = taxes;
    }

    @Column(name = "PACKAGING_PRICE", nullable = false)
    public BigDecimal getPackagingPrice() {
        return packagingPrice;
    }

    public void setPackagingPrice(BigDecimal packagingPrice) {
        this.packagingPrice = packagingPrice;
    }

    @Column(name = "TDS_RATE", nullable = true)
    public BigDecimal getTdsRate() {
        return tdsRate;
    }

    public void setTdsRate(BigDecimal tdsRate) {
        this.tdsRate = tdsRate;
    }

    @Column(name = "SERVICE_RECEIVED_ID", nullable = true)
	public Integer getServiceReceivedId() {
		return serviceReceivedId;
	}

	public void setServiceReceivedId(Integer serviceReceivedId) {
		this.serviceReceivedId = serviceReceivedId;
	}

	 @Column(name = "SERVICE_RECEIVED_ITEM_ID", nullable = true)
	public Integer getServiceReceivedItemId() {
		return serviceReceivedItemId;
	}

	public void setServiceReceivedItemId(Integer serviceReceivedItemId) {
		this.serviceReceivedItemId = serviceReceivedItemId;
	}

    @Column(name = "SKU_DATE")
    public Date getSkuDate() {
        return skuDate;
    }

    public void setSkuDate(Date skuDate) {
        this.skuDate = skuDate;
    }

    @Column(name = "TO_SKU_DATE")
    public Date getToSkuDate() {
        return toSkuDate;
    }

    public void setToSkuDate(Date toSkuDate) {
        this.toSkuDate = toSkuDate;
    }
}

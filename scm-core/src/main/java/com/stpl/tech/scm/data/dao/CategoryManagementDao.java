package com.stpl.tech.scm.data.dao;

import java.util.List;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.data.model.CategoryAttributeMappingData;
import com.stpl.tech.scm.data.model.CategoryAttributeValueData;

/**
 * Created by Chaayos on 11-05-2016.
 */
public interface CategoryManagementDao extends SCMAbstractDao {

    public List<CategoryAttributeMappingData> addCategoryAttributeMapping(List<CategoryAttributeMappingData> categoryAttributeMappingDataList) throws SumoException;

    public List<CategoryAttributeValueData> addCategoryAttributeValue(List<CategoryAttributeValueData> categoryAttributeValueDataList) throws SumoException;
}

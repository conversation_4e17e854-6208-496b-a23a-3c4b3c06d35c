package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

@Entity
@Table(name = "LDC_VENDOR_DATA")
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class LdcVendorData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "Id")
    Long id;

    @Column(name = "LDC_LIMIT", nullable = false)
    Double ldcLimit;
    @Column(name = "LDC_TENURE_FROM",nullable = false)
    Date ldcTenureFrom;
    @Column(name = "LDC_TENURE_TO",nullable = false)
    Date ldcTenureTo;
    @Column(name = "LDC_TDS_RATE",nullable = false)
    Double ldcTdsRate;
    @Column(name = "LDC_TDS_SECTION",nullable = false)
    String ldcTdsSection;
    @Column(name = "LDC_CERTIFICATE_NO",nullable = false)
    String ldcCertificateNo;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "VENDOR_ID", nullable = false)
    VendorDetailData vendorDetailData;

    @Column(name = "REMAINING_LIMIT", nullable = false)
    Double remainingLimit;


    @Column(name = "STATUS",nullable = false)
    String status = "ACTIVE";

    @Column(name = "CREATED_BY",nullable = false)
    Integer createdBy;

    @Column(name = "CREATED_AT",nullable = false)
    Date createdAt;
    @Column(name = "UPDATED_BY")
    Integer updatedBy;
    @Column(name = "UPDATED_AT")
    Date updatedAt;

}

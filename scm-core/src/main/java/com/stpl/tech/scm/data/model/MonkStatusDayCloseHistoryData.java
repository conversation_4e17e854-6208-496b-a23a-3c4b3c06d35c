package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "MONK_STATUS_DAY_CLOSE_HISTORY")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MonkStatusDayCloseHistoryData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MONK_STATUS_DAY_CLOSE_HISTORY_ID")
    private Long monkStatusDayCloseHistoryId;

    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATED_AT", nullable = false, updatable = false)
    private Date createdAt;

    @Column(name = "MONK_STATUS_DAY_CLOSE_ID", nullable = false)
    private Long monkStatusDayCloseId;

    @Column(name = "MONK_STATUS", nullable = false, length = 50)
    private String monkStatus;

    @Column(name = "COMMENT", length = 500)
    private String comment;

    @Column(name = "UPDATED_BY")
    private Integer updatedBy;

    // Many-to-One relationship back to MonkStatusDayCloseData
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "MONK_STATUS_DAY_CLOSE_ID", referencedColumnName = "MONK_STATUS_DAY_CLOSE_ID", insertable = false, updatable = false)
    private MonkStatusDayCloseData monkStatusDayClose;

    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = new Date();
        }
    }
}

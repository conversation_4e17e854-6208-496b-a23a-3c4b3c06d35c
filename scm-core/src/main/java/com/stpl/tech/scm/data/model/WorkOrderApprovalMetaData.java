package com.stpl.tech.scm.data.model;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.MapsId;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;

/**
 * This class represents the approval details for a particular work order.
 * It contains information about the vendor approval details and System approver details.
 */

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "WORK_ORDER_APPROVAL_METADATA")
public class WorkOrderApprovalMetaData implements Serializable {

    @Id
    @MapsId
    @OneToOne
    @JoinColumn(name = "WORK_ORDER_ID", nullable = false, unique = true)
    private WorkOrderData workOrderData;

    @Column(name = "WORK_ORDER_DOC_ID")
    private Integer workOrderDocId;

    @Column(name = "VENDOR_USER_NAME")
    private String vendorUserName;

    @Column(name = "VENDOR_USER_DESIGNATION")
    private String VendorDesignation;

    @Column(name = "VENDOR_IP_ADDRESS")
    private String vendorIpAddress;

    @Column(name = "AUTH_IP_ADDRESS")
    private String authIpAddress;

    @Column(name = "VENDOR_SIGNED_DOCUMENT_ID")
    private Integer vendorSignedDocumentId;

    @Column(name = "AUTH_SIGNED_DOCUMENT_ID")
    private Integer authSignedDocumentId;

    @Column(name = "VENDOR_DIGITAL_SIGN_ID")
    private Integer vendorSignId;

    @Column(name = "AUTH_DIGITAL_SIGN_ID")
    private Integer authSignId;

    @Column(name = "UNSIGNED_DOCUMENT_ID")
    private Integer unsignedDocumentId;

    @Column(name = "TEMPLATE_ID")
    private Integer templateId;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "VENDOR_APPROVAL_MAIL_TRIGGERED_TIME")
    private Date mailTime;
//
//    @Column(name = "VENDOR_OTP_VERIFIED")
//    private String vendorOtpVerified;

}

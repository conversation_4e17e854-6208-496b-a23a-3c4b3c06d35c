package com.stpl.tech.scm.reports.modal;

import java.math.BigDecimal;
import java.util.Date;

public class CafeUnsettledTOModal {

	private int transferOrderNo;
	private int transferOrderItemNo;
	private Date transferDate;
	private String skuName;
	private String uom;
	private String category;
	private String subCategory;
	private int receivingUnitId;
	private String receivingUnitName;
	private BigDecimal transferred;
	private BigDecimal received;

	public int getTransferOrderNo() {
		return transferOrderNo;
	}

	public void setTransferOrderNo(int transferOrderNo) {
		this.transferOrderNo = transferOrderNo;
	}

	public int getTransferOrderItemNo() {
		return transferOrderItemNo;
	}

	public void setTransferOrderItemNo(int transferOrderItemNo) {
		this.transferOrderItemNo = transferOrderItemNo;
	}

	public Date getTransferDate() {
		return transferDate;
	}

	public void setTransferDate(Date transferDate) {
		this.transferDate = transferDate;
	}

	public String getSkuName() {
		return skuName;
	}

	public void setSkuName(String productName) {
		this.skuName = productName;
	}

	public String getUom() {
		return uom;
	}

	public void setUom(String uom) {
		this.uom = uom;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getSubCategory() {
		return subCategory;
	}

	public void setSubCategory(String subCategory) {
		this.subCategory = subCategory;
	}

	public int getReceivingUnitId() {
		return receivingUnitId;
	}

	public void setReceivingUnitId(int receivingUnitId) {
		this.receivingUnitId = receivingUnitId;
	}

	public String getReceivingUnitName() {
		return receivingUnitName;
	}

	public void setReceivingUnitName(String receivingUnitName) {
		this.receivingUnitName = receivingUnitName;
	}

	public BigDecimal getTransferred() {
		return transferred;
	}

	public void setTransferred(BigDecimal transferred) {
		this.transferred = transferred;
	}

	public BigDecimal getReceived() {
		return received;
	}

	public void setReceived(BigDecimal received) {
		this.received = received;
	}

}

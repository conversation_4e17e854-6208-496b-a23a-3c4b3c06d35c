package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.annotation.Generated;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "UNIT_DAY_CLOSE_DAY_MAPPING")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UnitDayCloseDayMappingData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "DAY")
    private String day;

    @Column(name = "DAY_ID")
    private Integer dayId;

    @Column(name = "TYPE")
    private String type;

    @Column(name = "UNIT_IDS")
    private String unitIds;
}

package com.stpl.tech.scm.notification.email;

import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.notification.email.template.KWhAutoDayCloseNotificationTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.List;
import java.util.Objects;

public class KWhAutoDayCloseEmailNotification extends EmailNotification {

    private KWhAutoDayCloseNotificationTemplate notificationTemplate;
    private EnvType envType;
    private UnitBasicDetail unitBasicDetail;
    private String user;
    private List<String> emailIds;

    public KWhAutoDayCloseEmailNotification(KWhAutoDayCloseNotificationTemplate notificationTemplate, EnvType envType, UnitBasicDetail unitBasicDetail, String user, List<String> emailIds) {
        this.notificationTemplate = notificationTemplate;
        this.envType = envType;
        this.unitBasicDetail = unitBasicDetail;
        this.user = user;
        this.emailIds = emailIds;
    }

    @Override
    public String[] getToEmails() {
        emailIds.add("<EMAIL>");
        emailIds.add("<EMAIL>");
        String[] simpleArray = new String[emailIds.size()];
        return SCMUtil.isDev(envType) ? new String[]{"<EMAIL>"} : emailIds.toArray(simpleArray);
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        String subject = "Kitchen Warehouse Auto Day Close " + SCMUtil.getFormattedDate(SCMUtil.getCurrentDateIST());
        if (Objects.nonNull(unitBasicDetail)) {
            subject = subject + " for " + unitBasicDetail.getName() + " [ " + unitBasicDetail.getId() + " ] By " + user;
        }
        if (SCMUtil.isDev(envType)) {
            subject = "[Dev] : " + subject;
        }
        return subject;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return notificationTemplate.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}

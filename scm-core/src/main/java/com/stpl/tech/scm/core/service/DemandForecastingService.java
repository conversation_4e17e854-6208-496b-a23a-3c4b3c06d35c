/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.core.service;

import com.stpl.tech.scm.domain.model.DemandForecastingRequestDto;
import com.stpl.tech.scm.domain.model.ProductDemandForecast;
import com.stpl.tech.scm.domain.model.UnitWiseOrderingStrategyDataDto;
import com.stpl.tech.scm.core.exception.SumoException;

import java.util.Map;

public interface DemandForecastingService {

    UnitWiseOrderingStrategyDataDto getUnitWiseOrderingStrategyData(Integer unitId);

    Map<String, ProductDemandForecast> getDemandForecastForDates(DemandForecastingRequestDto request) throws SumoException;

}

package com.stpl.tech.scm.data.dao;

import java.math.BigDecimal;
import java.util.List;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.util.model.CostDataEntries;
import com.stpl.tech.scm.data.model.CostDetailData;
import com.stpl.tech.scm.domain.model.CostDetail;
import com.stpl.tech.scm.domain.model.PriceUpdateEntryType;
import com.stpl.tech.scm.domain.model.VarianceVO;
import com.stpl.tech.scm.domain.vo.ConsumptionVO;
import com.stpl.tech.scm.domain.vo.ErrorsVO;
import com.stpl.tech.scm.domain.vo.InventoryItemVO;
import com.stpl.tech.scm.domain.vo.ReceivingVO;

public interface PriceManagementDao extends SCMAbstractDao {

	// While adding the receivings, we need to do the following
	// 1. create the entry in audit table as well as the drill down table.
	// 2. Incase prices differ from the latest then we need to add a new row and
	// mark it as latest.
	// 3. Incase the prices match then we need to topup the inventory.
	// 4. Incase the prices are not found then a new entry is made.

	public <T extends ReceivingVO> T addReceiving(T rec, boolean canellation) throws InventoryUpdateException;

	// While reducing the consumption we need to do the following
	// 1. get all the inventory data for all the listed products in the
	// consumption VO.
	// 2. Try to fulfill all the required inventory for consumable using the
	// fifo for quantity of a product or a cafe.
	// 3. Get the weighted mean of the product/sku and set against each item.
	// 4. Set the drilldowns for each entry from the inventory entries being
	// consumed at that point of time.
	public <T extends ConsumptionVO> T reduceConsumable(T rec, boolean cancellation) throws InventoryUpdateException, DataNotFoundException;

	public <T extends ConsumptionVO> T checkConsumableData(T rec) throws InventoryUpdateException, DataNotFoundException;

	public List<CostDetailData> getCurrentPrice(PriceUpdateEntryType keyType, int unitId, Integer keyId, boolean current);

	public List<CostDetailData> getCurrentPrices(PriceUpdateEntryType keyType, int unitId, List<Integer> keyIds, boolean current);

	public CostDetailData addNewCostDetailEntry(int unitId, InventoryItemVO item, BigDecimal oldPrice, boolean cancellation, boolean isLatest, boolean createdThroughAutoDayClose) throws InventoryUpdateException;

	public CostDetailData updateCostDetailEntry(int costDetailId, BigDecimal oldPrice, InventoryItemVO item, boolean cancellation);

	public List<CostDetailData> getPriceDetailsForUnit(int unitId);

	public void deleteObsoletePrices(Integer unit, PriceUpdateEntryType entryType, Integer dayCloseEventId) throws InventoryUpdateException;

	/**
	 * @param wastage
	 */
	public <T extends ErrorsVO> T verifyPriceData(T rec);

	/**
	 * @param all
	 */
	public void overrideInventory(VarianceVO all) throws DataNotFoundException, InventoryUpdateException;

	public List<CostDetailData> fixPricing(List<CostDetailData> currentPriceList);

	public <T extends ConsumptionVO> T updateLatestFlag(T rec);

	public CostDataEntries getOrCreateCurrentPrices(PriceUpdateEntryType keyType, int unitId, List<Integer> keyIds,
													boolean current, boolean createMissig, Boolean missingToPickFromProduct) throws DataNotFoundException;

	public List<CostDetailData> getAllCurrentPriceForAnyUnitInRegionByKeyId(PriceUpdateEntryType keyType, List<Integer> keyIds,
																			List<String> regions, Boolean isLatest) throws DataNotFoundException;


	List<Integer> getAllAvailableKeyIds(int unitId, PriceUpdateEntryType priceUpdateEntryType);

	CostDetailData getCurrentPriceForAnyUnitInRegion(PriceUpdateEntryType keyType, Integer keyId, String region) throws DataNotFoundException;

	void addNewCostDetailEntryInBulk(Integer unitId, List<CostDetail> costDetails) throws InventoryUpdateException;

	List<CostDetailData> resetExpiryDateOfExpiringStock(boolean isCafe);
}

package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

import static javax.persistence.GenerationType.IDENTITY;

@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "CATEGORY_CONVERSION_ITEM")
public class CategoryConversionItemData {

    private Integer conversionItemId;
    private Integer conversionId;
    private Integer productId;
    private Integer assetId;
    private BigDecimal amount;
    private Integer grId;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "CONVERSION_ITEM_ID", unique = true, nullable = false)
    public Integer getConversionItemId() {
        return conversionItemId;
    }

    public void setConversionItemId(Integer conversionItemId) {
        this.conversionItemId = conversionItemId;
    }

    @Column(name = "CONVERSION_ID", nullable = false)
    public Integer getConversionId() {
        return conversionId;
    }

    public void setConversionId(Integer conversionId) {
        this.conversionId = conversionId;
    }

    @Column(name = "PRODUCT_ID", nullable = false)
    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    @Column(name = "ASSET_ID")
    public Integer getAssetId() {
        return assetId;
    }

    public void setAssetId(Integer assetId) {
        this.assetId = assetId;
    }

    @Column(name = "AMOUNT", nullable = false)
    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    @Column(name = "GR_ID")
    public Integer getGrId() {
        return grId;
    }

    public void setGrId(Integer grId) {
        this.grId = grId;
    }
}

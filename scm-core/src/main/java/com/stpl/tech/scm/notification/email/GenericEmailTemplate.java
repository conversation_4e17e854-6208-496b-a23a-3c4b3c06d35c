package com.stpl.tech.scm.notification.email;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.EmailNotification;

public class GenericEmailTemplate  extends EmailNotification {
    private final String subject;
    private final String body;
    private final String[] mailIds;
    private final EnvType envType;
    private final String fromMail;

    public GenericEmailTemplate(String subject, String body, String[] mailIds, EnvType envType, String fromMail) {
        this.subject = subject;
        this.body = body;
        this.mailIds = mailIds;
        this.envType = envType;
        this.fromMail = fromMail;
    }

    public GenericEmailTemplate(String subject, String body, String[] mailIds, EnvType envType) {
        this.subject = subject;
        this.body = body;
        this.mailIds = mailIds;
        this.envType = envType;
        this.fromMail = SCMServiceConstants.REPORTING_EMAIL;
    }

    @Override
    public String[] getToEmails() {
        return (SCMUtil.isDev(getEnvironmentType()) ? new String[] {SCMServiceConstants.TECHNOLOGY_EMAIL} : mailIds);
    }

    @Override
    public String getFromEmail() {
        return fromMail;
    }

    @Override
    public String subject() {
        return (SCMUtil.isDev(getEnvironmentType()) ? "[DEV] " : "") + subject;
    }

    @Override
    public String body() throws EmailGenerationException {
        return body;
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}

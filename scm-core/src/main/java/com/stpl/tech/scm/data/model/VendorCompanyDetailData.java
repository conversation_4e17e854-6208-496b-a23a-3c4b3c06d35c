package com.stpl.tech.scm.data.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 19-04-2017.
 */


@Entity
@Table(name = "VENDOR_COMPANY_DETAIL")
public class VendorCompanyDetailData {

    private Integer companyDetailId;
    private VendorDetailData vendorDetail;
    private String registeredCompanyName;
    private String companyName;
    private String CIN;
    private AddressDetailData companyAddress;
    private String PAN;
    private String panStatus;
    private Date updatedAt;
    private int updatedBy;
    private Integer creditDays = 0;
    private String ARC;
    private String CST;
    private String companyType;
    private String businessType;

    private DocumentDetailData cinDocument;
    private DocumentDetailData arcDocument;
    private DocumentDetailData cstDocument;
    private DocumentDetailData panDocument;
    private DocumentDetailData vatDocument;
    private DocumentDetailData serviceTaxDocument;
    private String exemptSupplier;
    private String msmeRegistered;
    private Date msmeExpirationDate;
    private DocumentDetailData msmeDocument;

    private Date panStatusUpdatedAt;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "VENDOR_COMPANY_ID", nullable = false, unique = true)
    public Integer getCompanyDetailId() {
        return companyDetailId;
    }

    public void setCompanyDetailId(Integer companyDetailId) {
        this.companyDetailId = companyDetailId;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "VENDOR_ID", nullable = false)
    public VendorDetailData getVendorDetail() {
        return vendorDetail;
    }

    public void setVendorDetail(VendorDetailData vendorDetail) {
        this.vendorDetail = vendorDetail;
    }

    @Column(name = "REGISTERED_NAME", nullable = false)
    public String getRegisteredCompanyName() {
        return registeredCompanyName;
    }

    public void setRegisteredCompanyName(String registeredCompanyName) {
        this.registeredCompanyName = registeredCompanyName;
    }

    @Column(name = "NAME", nullable = false)
    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    @Column(name = "CIN", nullable = true, unique = true)
    public String getCIN() {
        return CIN;
    }

    public void setCIN(String CIN) {
        this.CIN = CIN;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "VENDOR_COMPANY_ADDRESS_ID", nullable = false)
    public AddressDetailData getCompanyAddress() {
        return companyAddress;
    }

    public void setCompanyAddress(AddressDetailData companyAddress) {
        this.companyAddress = companyAddress;
    }

    @Column(name = "PAN", nullable = false, unique = true)
    public String getPAN() {
        return PAN;
    }

    public void setPAN(String PAN) {
        this.PAN = PAN;
    }

    @Column(name = "PAN_STATUS")
    public String getPanStatus() {
        return panStatus;
    }

    public void setPanStatus(String panStatus) {
        this.panStatus = panStatus;
    }

    @Column(name = "CST", nullable = false, unique = true)
    public String getCST() {
        return CST;
    }

    public void setCST(String CST) {
        this.CST = CST;
    }

    @Column(name = "UPDATED_AT", nullable = false)
    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Column(name = "UPDATED_BY", nullable = false)
    public int getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(int updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Column(name = "CREDIT_CYCLE", nullable = false)
    public Integer getCreditDays() {
        return creditDays;
    }

    public void setCreditDays(Integer creditDays) {
        this.creditDays = creditDays;
    }

    @Column(name = "ARC",unique = true, nullable = true)
    public String getARC() {
        return ARC;
    }

    public void setARC(String ARC) {
        this.ARC = ARC;
    }

    @Column(name = "COMPANY_TYPE", nullable = false)
    public String getCompanyType() {
        return companyType;
    }

    public void setCompanyType(String companyType) {
        this.companyType = companyType;
    }

    @Column(name = "BUSINESS_TYPE", nullable = false)
    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CIN_DOCUMENT", nullable = true)
    public DocumentDetailData getCinDocument() {
        return cinDocument;
    }

    public void setCinDocument(DocumentDetailData cinDocument) {
        this.cinDocument = cinDocument;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ARC_DOCUMENT", nullable = true)
    public DocumentDetailData getArcDocument() {
        return arcDocument;
    }

    public void setArcDocument(DocumentDetailData arcDocument) {
        this.arcDocument = arcDocument;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CST_DOCUMENT", nullable = true)
    public DocumentDetailData getCstDocument() {
        return cstDocument;
    }

    public void setCstDocument(DocumentDetailData cstDocument) {
        this.cstDocument = cstDocument;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PAN_DOCUMENT", nullable = true)
    public DocumentDetailData getPanDocument() {
        return panDocument;
    }

    public void setPanDocument(DocumentDetailData panDocument) {
        this.panDocument = panDocument;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "VAT_DOCUMENT", nullable = true)
    public DocumentDetailData getVatDocument() {
        return vatDocument;
    }

    public void setVatDocument(DocumentDetailData vatDocument) {
        this.vatDocument = vatDocument;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SERVICE_TAX_DOCUMENT", nullable = true)
    public DocumentDetailData getServiceTaxDocument() {
        return serviceTaxDocument;
    }

    public void setServiceTaxDocument(DocumentDetailData serviceTaxDocument) {
        this.serviceTaxDocument = serviceTaxDocument;
    }

    public void setExemptSupplier(String exemptSupplier) {
        this.exemptSupplier = exemptSupplier;
    }

    @Column(name = "EXEMPT_SUPPLIER", nullable = false)
    public String getExemptSupplier() {
        return exemptSupplier;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "MSME_DOCUMENT", nullable = true)
    public DocumentDetailData getMsmeDocument() {
		return msmeDocument;
	}

    public void setMsmeDocument(DocumentDetailData msmeDocument) {
		this.msmeDocument = msmeDocument;
	}

    @Column(name = "IS_MSME_REGISTERED", nullable = true)
	public String getMsmeRegistered() {
		return msmeRegistered;
	}

	public void setMsmeRegistered(String msmeRegistered) {
		this.msmeRegistered = msmeRegistered;
	}

    @Column(name = "MSME_EXPIRE_DATE",nullable = true)
    public Date getMsmeExpirationDate() {
        return msmeExpirationDate;
    }

    public void setMsmeExpirationDate(Date msmeExpirationDate) {
        this.msmeExpirationDate = msmeExpirationDate;
    }

    @Column(name="PAN_STATUS_UPDATED_AT")
    public Date getPanStatusUpdatedAt() {
        return panStatusUpdatedAt;
    }

    public void setPanStatusUpdatedAt(Date panStatusUpdatedAt) {
        this.panStatusUpdatedAt = panStatusUpdatedAt;
    }
}

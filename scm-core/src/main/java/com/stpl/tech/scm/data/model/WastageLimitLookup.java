package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

@Entity
@Table(name = "WASTAGE_LIMIT_LOOKUP" , uniqueConstraints = @UniqueConstraint(columnNames = {"WASTAGE_LIMIT_ID", "DESIGNATION_ID"}))
public class WastageLimitLookup {
    private Integer wastageLimitId;
    private Integer designationId;
    private Integer maximumLimit;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "WASTAGE_LIMIT_ID",unique = true, nullable = false)
    public Integer getWastageLimitId() {
        return wastageLimitId;
    }

    public void setWastageLimitId(Integer wastageLimitId) {
        this.wastageLimitId = wastageLimitId;
    }

    @Column(name = "DESIGNATION_ID",unique = true, nullable = false)
    public Integer getDesignationId() {
        return designationId;
    }

    public void setDesignationId(Integer designationId) {
        this.designationId = designationId;
    }

    @Column(name = "MAXIMUM_LIMIT", nullable = true)
    public Integer getMaximumLimit() {
        return maximumLimit;
    }

    public void setMaximumLimit(Integer maximumLimit) {
        this.maximumLimit = maximumLimit;
    }
}

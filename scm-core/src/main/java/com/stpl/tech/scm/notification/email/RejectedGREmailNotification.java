package com.stpl.tech.scm.notification.email;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.notification.email.template.RejectedGREmailNotificationTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

public class RejectedGREmailNotification extends EmailNotification {

    private RejectedGREmailNotificationTemplate rejectedGREmailNotificationTemplate;
    private EnvType envType;
    private String subjectOfEmail;

    public RejectedGREmailNotification() {
    }

    public RejectedGREmailNotification(RejectedGREmailNotificationTemplate rejectedGREmailNotificationTemplate, EnvType envType) {
        this.rejectedGREmailNotificationTemplate = rejectedGREmailNotificationTemplate;
        this.envType = envType;
    }

    @Override
    public String[] getToEmails() {
        if (SCMUtil.isDev(envType)) {
            return new String[] { "<EMAIL>" };
        } else {
            String[] simpleArray = new String[] {"<EMAIL>"};
            return simpleArray;
        }
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        subjectOfEmail = String.format("GR Rejection By %s (%d)",
                rejectedGREmailNotificationTemplate.getReceivingUnit().getName(),rejectedGREmailNotificationTemplate.getReceivingUnit().getId());
        if (SCMUtil.isDev(envType)) {
            subjectOfEmail = " [DEV] : " + subjectOfEmail;
        }
        return subjectOfEmail;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return rejectedGREmailNotificationTemplate.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}

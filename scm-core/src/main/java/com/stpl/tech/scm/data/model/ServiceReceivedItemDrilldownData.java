package com.stpl.tech.scm.data.model;

import javax.persistence.*;
import java.math.BigDecimal;

@Entity
@Table(name = "SERVICE_RECEIVED_ITEM_DRILLDOWN")
public class ServiceReceivedItemDrilldownData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "SERVICE_RECEIVED_ITEM_DRILLDOWN_ID")
    private Integer serviceReceivedItemDrilldownId;

    @Column(name = "SERVICE_RECEIVED_ITEM_ID")
    private Integer serviceReceivedItemId;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "WIDTH")
    private BigDecimal width;

    @Column(name = "HEIGHT")
    private BigDecimal height;

    @Column(name = "LENGTH")
    private BigDecimal length;

    @Column(name = "NOS")
    private Integer nos;

    @Column(name = "MULTIPLIER")
    private BigDecimal multiplier;

    @Column(name = "RECEIVED_QUANTITY")
    private BigDecimal receivedQuantity;

    @Column(name = "SOURCE_UOM")
    private String sourceUom;

    @Column(name = "EXCLUSION_ENTRY", length = 1, nullable = false)
    private String isExclusionEntry;

    public String getIsExclusionEntry() {
        return isExclusionEntry;
    }

    public void setIsExclusionEntry(String isExclusionEntry) {
        this.isExclusionEntry = isExclusionEntry;
    }

    public Integer getServiceReceivedItemDrilldownId() {
        return serviceReceivedItemDrilldownId;
    }

    public void setServiceReceivedItemDrilldownId(Integer serviceReceivedItemDrilldownId) {
        this.serviceReceivedItemDrilldownId = serviceReceivedItemDrilldownId;
    }

    public Integer getServiceReceivedItemId() {
        return this.serviceReceivedItemId;
    }

    public void setServiceReceivedItemId(Integer serviceReceivedItemId) {
        this.serviceReceivedItemId = serviceReceivedItemId;
    }


    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BigDecimal getWidth() {
        return this.width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public BigDecimal getHeight() {
        return this.height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public BigDecimal getLength() {
        return this.length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public Integer getNos() {
        return this.nos;
    }

    public void setNos(Integer nos) {
        this.nos = nos;
    }

    public BigDecimal getMultiplier() {
        return multiplier;
    }

    public void setMultiplier(BigDecimal multiplier) {
        this.multiplier = multiplier;
    }

    public BigDecimal getReceivedQuantity() {
        return this.receivedQuantity;
    }

    public void setReceivedQuantity(BigDecimal receivedQuantity) {
        this.receivedQuantity = receivedQuantity;
    }

    public String getSourceUom() {
        return this.sourceUom;
    }

    public void setSourceUom(String sourceUom) {
        this.sourceUom = sourceUom;
    }
}

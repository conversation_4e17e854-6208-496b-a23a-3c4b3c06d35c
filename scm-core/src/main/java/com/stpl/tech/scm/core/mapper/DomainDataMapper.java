/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.core.mapper;

import com.stpl.tech.scm.data.model.DayCloseProductPackagingMappings;
import com.stpl.tech.scm.data.model.MonkWastageDetailData;
import com.stpl.tech.scm.data.model.DuplicateMonkWastageDetailData;
import com.stpl.tech.scm.data.model.PaymentRequestQueryData;
import com.stpl.tech.scm.data.model.RiderRoutePlanData;
import com.stpl.tech.scm.data.model.RiderRoutePlanItemData;
import com.stpl.tech.scm.data.model.RiderRoutePlanStepData;
import com.stpl.tech.scm.data.model.SCMProductInventoryData;
import com.stpl.tech.scm.data.model.StockRedistributionRouteInfoData;
import com.stpl.tech.scm.data.model.StockRedistributionRouteUnitsData;
import com.stpl.tech.scm.data.model.StockTakeSumoDayCloseEvent;
import com.stpl.tech.scm.data.model.StockTakeSumoDayCloseProducts;
import com.stpl.tech.scm.data.model.UOMConversionMappingData;
import com.stpl.tech.scm.domain.model.DayCloseProductPackagingMappingsDTO;
import com.stpl.tech.scm.domain.model.MonkWastageDetailDto;
import com.stpl.tech.scm.domain.model.PaymentRequestQuery;
import com.stpl.tech.scm.domain.model.RiderRoutePlanDataDto;
import com.stpl.tech.scm.domain.model.RiderRoutePlanItemDataDto;
import com.stpl.tech.scm.domain.model.RiderRoutePlanStepDataDto;
import com.stpl.tech.scm.domain.model.StockRedistributionRouteInfoDataDto;
import com.stpl.tech.scm.domain.model.StockRedistributionRouteUnitsDataDto;
import com.stpl.tech.scm.domain.model.StockTakeSumoDayCloseEventDTO;
import com.stpl.tech.scm.domain.model.StockTakeSumoDayCloseProductsDTO;
import com.stpl.tech.scm.domain.model.UomConversionMappingDto;
import com.stpl.tech.scm.domain.model.VarianceEditItem;
import org.mapstruct.AfterMapping;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring")
public interface DomainDataMapper {

    DomainDataMapper INSTANCE = Mappers.getMapper(DomainDataMapper.class);

    @Mapping(source = "prId.id", target = "prId")
    @Mapping(source = "paymentDeviationData.id", target = "paymentDeviationId")
    @Mapping(source = "paymentDeviationData.deviationDetail", target = "paymentDeviationDetail")
    PaymentRequestQuery toPaymentRequestQuery(PaymentRequestQueryData paymentRequestQueryData);

    VarianceEditItem toVarianceEditItem(SCMProductInventoryData scmProductInventoryData);

    @Mapping(source = "dayCloseEvent.eventId", target = "dayCloseEventId")
    @Mapping(source = "sumoDayCloseEvent.eventId", target = "sumoDayCloseEventId")
    StockTakeSumoDayCloseEventDTO toStockTakeSumoDayCloseEventDto(StockTakeSumoDayCloseEvent stockTakeSumoDayCloseEvent);

    @Mapping(source = "stockTakeSumoDayCloseEventId.stockTakeSumoDayCloseEventId", target = "stockTakeSumoDayCloseEventId")
    StockTakeSumoDayCloseProductsDTO toStockTakeSumoDayCloseProductsDto(StockTakeSumoDayCloseProducts stockTakeSumoDayCloseProduct);

    @Mapping(source = "sumoDayCloseProductItemId.sumoDayCloseProductItemId", target = "sumoDayCloseProductItemId")
    DayCloseProductPackagingMappingsDTO toDayCloseProductPackagingMappingsDto(DayCloseProductPackagingMappings dayCloseProductPackagingMappings);

    StockRedistributionRouteInfoData toStockRedistributionRouteInfoData(StockRedistributionRouteInfoDataDto stockRedistributionRouteInfoDataDto);

    StockRedistributionRouteInfoDataDto toStockRedistributionRouteInfoDataDto(StockRedistributionRouteInfoData stockRedistributionRouteInfoData);

    @Mapping(source = "riderRoutePlanDataRiderRoutePlanDataId", target = "riderRoutePlanData.riderRoutePlanDataId")
    RiderRoutePlanStepData riderRoutePlanStepDataDtoToRiderRoutePlanStepData(RiderRoutePlanStepDataDto riderRoutePlanStepDataDto);

    @Mapping(source = "riderRoutePlanData.riderRoutePlanDataId", target = "riderRoutePlanDataRiderRoutePlanDataId")
    RiderRoutePlanStepDataDto riderRoutePlanStepDataToRiderRoutePlanStepDataDto(RiderRoutePlanStepData riderRoutePlanStepData);

    @Mapping(source = "riderRoutePlanDataRiderRoutePlanDataId", target = "riderRoutePlanData.riderRoutePlanDataId")
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    RiderRoutePlanStepData updateRiderRoutePlanStepDataFromRiderRoutePlanStepDataDto(RiderRoutePlanStepDataDto riderRoutePlanStepDataDto, @MappingTarget RiderRoutePlanStepData riderRoutePlanStepData);

    @Mapping(source = "riderRoutePlanStepDataRiderRoutePlanStepDataId", target = "riderRoutePlanStepData.riderRoutePlanStepDataId")
    RiderRoutePlanItemData riderRoutePlanItemDataDtoToRiderRoutePlanItemData(RiderRoutePlanItemDataDto riderRoutePlanItemDataDto);

    @Mapping(source = "riderRoutePlanStepData.riderRoutePlanStepDataId", target = "riderRoutePlanStepDataRiderRoutePlanStepDataId")
    RiderRoutePlanItemDataDto riderRoutePlanItemDataToRiderRoutePlanItemDataDto(RiderRoutePlanItemData riderRoutePlanItemData);

    @Mapping(source = "riderRoutePlanStepDataRiderRoutePlanStepDataId", target = "riderRoutePlanStepData.riderRoutePlanStepDataId")
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    RiderRoutePlanItemData updateRiderRoutePlanItemDataFromRiderRoutePlanItemDataDto(RiderRoutePlanItemDataDto riderRoutePlanItemDataDto, @MappingTarget RiderRoutePlanItemData riderRoutePlanItemData);

    @Mapping(source = "stockRedistributionRouteInfoDataStockRedistributionRouteInfoDataId", target = "stockRedistributionRouteInfoData.stockRedistributionRouteInfoDataId")
    RiderRoutePlanData riderRoutePlanDataDtoToRiderRoutePlanData(RiderRoutePlanDataDto riderRoutePlanDataDto);

    @Mapping(source = "stockRedistributionRouteInfoData.stockRedistributionRouteInfoDataId", target = "stockRedistributionRouteInfoDataStockRedistributionRouteInfoDataId")
    RiderRoutePlanDataDto riderRoutePlanDataToRiderRoutePlanDataDto(RiderRoutePlanData riderRoutePlanData);

    @Mapping(source = "stockRedistributionRouteInfoDataStockRedistributionRouteInfoDataId", target = "stockRedistributionRouteInfoData.stockRedistributionRouteInfoDataId")
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    RiderRoutePlanData updateRiderRoutePlanDataFromRiderRoutePlanDataDto(RiderRoutePlanDataDto riderRoutePlanDataDto, @MappingTarget RiderRoutePlanData riderRoutePlanData);

    @AfterMapping
    default void linkRiderRoutePlanStepDataSet(@MappingTarget RiderRoutePlanData riderRoutePlanData) {
        riderRoutePlanData.getRiderRoutePlanStepDataSet().forEach(riderRoutePlanStepDataSet -> riderRoutePlanStepDataSet.setRiderRoutePlanData(riderRoutePlanData));
    }

    @Mapping(source = "stockRedistributionRouteInfoDataStockRedistributionRouteInfoDataId", target = "stockRedistributionRouteInfoData.stockRedistributionRouteInfoDataId")
    StockRedistributionRouteUnitsData stockRedistributionRouteUnitsDataDtoToStockRedistributionRouteUnitsData1(StockRedistributionRouteUnitsDataDto stockRedistributionRouteUnitsDataDto);

    @Mapping(source = "stockRedistributionRouteInfoData.stockRedistributionRouteInfoDataId", target = "stockRedistributionRouteInfoDataStockRedistributionRouteInfoDataId")
    StockRedistributionRouteUnitsDataDto stockRedistributionRouteUnitsDataToStockRedistributionRouteUnitsDataDto1(StockRedistributionRouteUnitsData stockRedistributionRouteUnitsData);

    @Mapping(source = "stockRedistributionRouteInfoDataStockRedistributionRouteInfoDataId", target = "stockRedistributionRouteInfoData.stockRedistributionRouteInfoDataId")
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    StockRedistributionRouteUnitsData updateStockRedistributionRouteUnitsDataFromStockRedistributionRouteUnitsDataDto1(StockRedistributionRouteUnitsDataDto stockRedistributionRouteUnitsDataDto, @MappingTarget StockRedistributionRouteUnitsData stockRedistributionRouteUnitsData);

    @Mapping(source="productDefinition.productId", target = "productId")
    UomConversionMappingDto toUomConversionMapping(UOMConversionMappingData uomConversionMappingData);

    MonkWastageDetailData toMonkWastageDetailData(MonkWastageDetailDto dto);
    MonkWastageDetailDto toMonkWastageDetailDto(MonkWastageDetailData entity);
    
    @Mapping(target = "id", ignore = true)
    DuplicateMonkWastageDetailData toDuplicateMonkWastageDetailData(MonkWastageDetailData entity);

}

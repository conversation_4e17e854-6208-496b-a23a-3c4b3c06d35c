package com.stpl.tech.scm.core.util;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.domain.model.UnitDetail;
import com.stpl.tech.util.AppConstants;

import java.util.List;

public class CompanyBrandUtil {

    private static final Integer DOHFUL_COMPANY_ID = 1005;
    private static final Integer DOHFUL_BRAND_ID = 6;

    public static boolean isDohfulCompany(Integer unitId, SCMCache scmCache) {
        UnitDetail unitDetail = scmCache.getUnitDetail(unitId);
        return isDohfulCompany(unitDetail.getCompanyId());
    }

    public static boolean isDohfulCompany(Integer companyId) {
        return DOHFUL_COMPANY_ID.equals(companyId);
    }

    public static boolean isDohfulBrand(Integer brandId) {
        return DOHFUL_BRAND_ID.equals(brandId);
    }

    public static List<Integer> getBrandsForCompany(Integer companyId, MasterDataCache masterDataCache) {
        List<Brand> brands = masterDataCache.getCompanyBrandsMap().getOrDefault(companyId, List.of());
        return brands.stream().map(Brand::getBrandId).toList();
    }


}

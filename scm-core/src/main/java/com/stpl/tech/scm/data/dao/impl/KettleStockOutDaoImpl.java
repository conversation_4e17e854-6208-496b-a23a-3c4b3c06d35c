package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitHours;
import com.stpl.tech.master.readonly.domain.model.ProductPriceVO;
import com.stpl.tech.master.readonly.domain.model.ProductVO;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.util.SCMConstant;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.ValidationUtil;
import com.stpl.tech.scm.data.dao.KettleStockOutDao;
import com.stpl.tech.scm.data.model.KettleProductDataClone;
import com.stpl.tech.scm.data.model.KettleProductDimensionCloneData;
import com.stpl.tech.scm.data.model.KettleStockOutDateWiseData;
import com.stpl.tech.scm.data.model.KettleStockOutTimingsData;
import com.stpl.tech.scm.data.model.KettleUnitDetailDataClone;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Slf4j
@Repository
@RequiredArgsConstructor
public class KettleStockOutDaoImpl extends SCMAbstractDaoImpl implements KettleStockOutDao {

    private final EnvProperties env;
    private final MasterDataCache masterDataCache;


    private void executeTruncateQuery(String tableName) {
        try {
            manager.createNativeQuery("TRUNCATE TABLE " + tableName).executeUpdate();
        } catch (Exception e) {
            log.error("Error while truncating table {}", tableName, e);
        }
    }

    @Override
    public <T> void insertList(List<T> entries) {
        StopWatch watch = new StopWatch();
        watch.start();
//        batchInsert(entries, 500);
        addAll(entries);
        watch.stop();
        log.info("Time taken to insert {} records is {} ms", entries.size(), watch.getTotalTimeMillis());
    }

    @Override
    public <T> void updateList(List<T> entries) {
        StopWatch watch = new StopWatch();
        watch.start();
        update(entries, false);
        watch.stop();
        log.info("Time taken to update {} records is {} ms", entries.size(), watch.getTotalTimeMillis());
    }

    @Override
    public void clearAndCreateProductDataCloneTable() {
        executeTruncateQuery("KETTLE_PRODUCT_DATA_CLONE");
        createProductDataClone();
    }

    private void createProductDataClone() {
        try {
            List<Product> products = masterDataCache.getAllProducts();
            List<KettleProductDataClone> kettleProductDataClones = new ArrayList<>();
            for (Product product : products) {
                if(!product.isInventoryTracked()) {
                    continue;
                }
                KettleProductDataClone kettleProductDataClone = new KettleProductDataClone();
                kettleProductDataClone.setProductId(product.getId());
                kettleProductDataClone.setBrandId(product.getBrandId());
                kettleProductDataClone.setInventoryTrackLevel(product.getInventoryTrackedLevel());
                kettleProductDataClones.add(kettleProductDataClone);
            }
            insertList(kettleProductDataClones);
        } catch (Exception e) {
            log.error("Error while creating product data clone", e);
        }
    }

    @Override
    public void clearAndCreateUnitDetailDataCloneTable() {
        executeTruncateQuery("KETTLE_UNIT_DETAIL_DATA_CLONE");
        executeTruncateQuery("KETTLE_PRODUCT_DIMENSION_DATA_CLONE");
    }

    private void createKettleUnitDetailDataClone(Unit unit, Brand brand, List<KettleUnitDetailDataClone> kettleUnitDetailDataClones,
                                                 Map<Integer, Product> productDetails, Collection<ProductVO> productVOS) {

        UnitHours unitHours = getCurrentDayBusinessHours(unit, brand.getBrandId());
        if(Objects.isNull(unitHours) || (brand.getBrandId() == AppConstants.GNT_BRAND_ID ?
                (Objects.isNull(unitHours.getDeliveryOpeningTime()) || Objects.isNull(unitHours.getDeliveryClosingTime())) : (Objects.isNull(unitHours.getDineInOpeningTime()) || Objects.isNull(unitHours.getDineInClosingTime())))) {
            log.warn("No business hours found for unit {} and brand {}", unit.getId(), brand.getBrandId());
            return;
        }

        Map<String, Integer> productCount = new HashMap<>();
        Map<String, Set<KettleProductDimensionCloneData>> productDimensionMap = new HashMap<>();
        productVOS.stream().filter( e-> Objects.equals(e.getBrandId(), brand.getBrandId()) && e.isInventoryTracked() && !CollectionUtils.isEmpty(e.getPrices())).forEach( productDetail -> {
            Product product = productDetails.get(productDetail.getId());
            String KEY = SCMUtil.generateUniqueKey(
                    productDetail.getBrandId().toString(),
                    product.getInventoryTrackedLevel()
            );
            productDimensionMap.computeIfAbsent(KEY, k -> new HashSet<>());
            int count = 0;
            if(ValidationUtil.checkNonEmptyCollection(productDetail.getPrices(), "Prices are empty for product " + productDetail.getId())) {
                for(ProductPriceVO price : productDetail.getPrices()) {
                    productDimensionMap.get(KEY).add(new KettleProductDimensionCloneData(productDetail.getId(), price.getDimension()));
                    count++;
                }
            }
            if (productCount.containsKey(KEY)) {
                productCount.put(KEY, productCount.get(KEY) + count);
            } else {
                productCount.put(KEY, count);
            }
        });

        if(ValidationUtil.checkIsEmptyCollection(productCount)) {
            return;
        }

        productCount.forEach((KEY, value) -> {
            KettleUnitDetailDataClone kettleUnitDetailDataClone = new KettleUnitDetailDataClone();
            kettleUnitDetailDataClone.setUnitId(unit.getId());
            kettleUnitDetailDataClone.setBrandId(brand.getBrandId());
            kettleUnitDetailDataClone.setCafeOpening(brand.getBrandId() == AppConstants.GNT_BRAND_ID ?
                    SCMUtil.getDate(unitHours.getDeliveryOpeningTime(),  unitHours.getDeliveryClosingTime(), false) : SCMUtil.getDate(unitHours.getDineInOpeningTime(), unitHours.getDineInClosingTime(), false));
            kettleUnitDetailDataClone.setCafeClosing(brand.getBrandId() == AppConstants.GNT_BRAND_ID ?
                    SCMUtil.getDate(unitHours.getDeliveryOpeningTime(),  unitHours.getDeliveryClosingTime(), true) : SCMUtil.getDate(unitHours.getDineInOpeningTime(), unitHours.getDineInClosingTime(), true));
            kettleUnitDetailDataClone.setCafeOperational(SCMUtil.getStringFromBoolean(unitHours.isIsOperational()));
            kettleUnitDetailDataClone.setUnitStatus(unit.getStatus().toString());
            kettleUnitDetailDataClone.setIsLive(AppUtils.setStatus(unit.isLive()));
            kettleUnitDetailDataClone.setIsLiveInventoryEnabled(AppUtils.setStatus(unit.isLiveInventoryEnabled()));
            kettleUnitDetailDataClone.setInventoryLevel(KEY.split(SCMConstant.HYPHEN_SEPARATOR)[1]);
            kettleUnitDetailDataClone.setTotalProducts(value);

            Set<KettleProductDimensionCloneData> productDimensions = productDimensionMap.get(KEY);
            if (productDimensions != null) {
                productDimensions.forEach(kettleUnitDetailDataClone::addProductDimension);
            }

            kettleUnitDetailDataClones.add(kettleUnitDetailDataClone);
        });

    }

    private UnitHours getCurrentDayBusinessHours(Unit unit, Integer brandId) {
        try {
            List<UnitHours> unitHours;
            if (Objects.equals(brandId, AppConstants.GNT_BRAND_ID)) {
                unitHours = unit.getGntOperationalHours();
            } else {
                unitHours = unit.getOperationalHours();
            }
            if(ValidationUtil.checkIsEmptyCollection(unitHours)) {
                return null;
            }

            int dayOfWeek = SCMUtil.getDayOfWeek(SCMUtil.getCurrentBusinessDate());
            for(UnitHours hours : unitHours) {
                if(Objects.equals(dayOfWeek, hours.getDayOfTheWeekNumber())) {
                    return hours;
                }
            }
            return null;

        } catch (Exception e) {
            log.error("Error while fetching business hours for unit {}", unit.getId(), e);
            return null;
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<KettleStockOutDateWiseData> getKettleStockOutDayWiseData() {
        Date yesterday = SCMUtil.getBusinessDateScm(SCMUtil.getDayBeforeOrAfterCurrentDay(-1));
        return manager
                .createNativeQuery("SELECT * FROM KETTLE_STOCK_OUT_DATE_WISE_DATA WHERE BUSINESS_DATE = :businessDate", KettleStockOutDateWiseData.class)
                .setParameter("businessDate", yesterday)
                .getResultList();
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<KettleStockOutTimingsData> getKettleStockOutTimingsData(Set<Integer> ids) {
        return manager
                .createNativeQuery("SELECT * FROM KETTLE_STOCK_OUT_TIMINGS_DATA where KETTLE_STOCK_OUT_DATE_WISE_DATA_ID IN (:ids)", KettleStockOutTimingsData.class)
                .setParameter("ids", ids)
                .getResultList();
    }

    @Override
    public List<KettleUnitDetailDataClone> getKettleUnitDetailDataClone() {
        try {
            List<KettleUnitDetailDataClone> resultList = (List<KettleUnitDetailDataClone>) manager
                    .createQuery("select DISTINCT s from KettleUnitDetailDataClone s " +
                            "LEFT JOIN FETCH s.kettleProductDimensionCloneDataSet")
                    .getResultList();
            return resultList;
        } catch (Exception e) {
            log.error("Error while fetching kettle unit detail data clone", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<KettleProductDataClone> getKettleProductDataClone() {
        return findAll(KettleProductDataClone.class);
    }

    @Override
    public List<KettleStockOutDateWiseData> findByUnitIdAndBusinessDates(Integer unitId, List<Date> businessDates) {
        try {
            List<KettleStockOutDateWiseData> resultList = (List<KettleStockOutDateWiseData>) manager
                    .createQuery("select DISTINCT s from KettleStockOutDateWiseData s " +
                            "LEFT JOIN FETCH s.kettleStockOutTimingsDataSet " +
                            "where s.unitId =:unitId and s.businessDate IN :businessDates")
                    .setParameter("unitId", unitId)
                    .setParameter("businessDates", businessDates)
                    .getResultList();
            return resultList;
        } catch (Exception e) {
            log.error("Error while fetching stock out data for unit {} and business dates {}", unitId, businessDates, e);
            return new ArrayList<>();
        }
    }
}

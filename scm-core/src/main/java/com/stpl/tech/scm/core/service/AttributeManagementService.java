package com.stpl.tech.scm.core.service;

import java.util.List;
import java.util.Map;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.data.model.AttributeValueData;
import com.stpl.tech.scm.domain.model.AttributeDefinition;
import com.stpl.tech.scm.domain.model.AttributeType;
import com.stpl.tech.scm.domain.model.AttributeValue;
import com.stpl.tech.scm.domain.model.ValidateStateOutput;

/**
 * Created by <PERSON><PERSON> on 05-05-2016.
 */
public interface AttributeManagementService {

    public Map<AttributeType, List<AttributeDefinition>> viewAllAttributeDefinitions();

    public Map<Integer, List<AttributeValue>> viewAllAttributeValues();

    public List<AttributeValueData> addNewAttributeValue(List<AttributeValue> attributeValue) throws SumoException;

    public boolean updateAttributeValue(AttributeValue attributeValue);

    public Map<Integer, List<AttributeValue>> getAllAttributeValuesByAttribute(int categoryId, boolean onlyActive);

    public AttributeDefinition addAttribute(AttributeDefinition attributeDefinition) throws SumoException;

    public AttributeDefinition updateAttribute(AttributeDefinition attributeDefinition) throws SumoException;

    List<ValidateStateOutput> validateState(Long stateId, Integer unitId);
}

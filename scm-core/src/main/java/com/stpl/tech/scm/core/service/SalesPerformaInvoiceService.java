package com.stpl.tech.scm.core.service;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SalesPerformaInvoiceException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.data.model.SpecializedOrderInvoiceData;
import com.stpl.tech.scm.domain.model.CorrectedSalesInvoiceDetails;
import com.stpl.tech.scm.domain.model.CreditDebitNoteDetail;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.GoodsReceived;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.OutwardRegister;
import com.stpl.tech.scm.domain.model.SalesPerformaInvoice;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.util.TemplateRenderingException;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 03-07-2018.
 */
public interface SalesPerformaInvoiceService {

    public SalesPerformaInvoice createInvoice(SalesPerformaInvoice invoice) throws SalesPerformaInvoiceException, DataNotFoundException, InventoryUpdateException, SumoException;

    public List<SalesPerformaInvoice> viewInvoices(Integer sendingUnit, Date startDate, Date endDate,
                                                   List<String > status, Integer vendorId, Integer dispatchId, Boolean fetchPending,String businessType, Boolean raiseCreditNote);

    public SalesPerformaInvoice approveInvoice(SalesPerformaInvoice invoice, Integer userId) throws SalesPerformaInvoiceException, SumoException, FileNotFoundException, FileNotFoundException;

    public SalesPerformaInvoice cancelInvoice(Integer invoiceId, Integer userId, Integer docId) throws SalesPerformaInvoiceException, SumoException, InventoryUpdateException;

    public Map<String, String> uploadInvoice(MimeType mimeType, Integer invoiceId, File file, Integer userId) throws SalesPerformaInvoiceException, SumoException, IOException, TemplateRenderingException;

    public Map<String, String> uploadEwayBill(MimeType mimeType, Integer invoiceId, MultipartFile file, Integer userId) throws SalesPerformaInvoiceException, SumoException;

    public Map<String, String> uploadDeliveredDocument(MimeType mimeType, Integer invoiceId, MultipartFile file, Integer userId) throws SalesPerformaInvoiceException, SumoException, IOException, TemplateRenderingException;

    public SalesPerformaInvoice dispatchInvoice(Integer invoiceId, Integer userId, Date dateOfDelivery) throws SalesPerformaInvoiceException, SumoException, ParseException;

    SalesPerformaInvoice rejectInvoice(Integer invoiceId, Integer userId) throws SalesPerformaInvoiceException, SumoException;

    SalesPerformaInvoice markInvoiceReadyToDispatch(Integer invoiceId, Integer userId) throws SalesPerformaInvoiceException, SumoException;

    public List<SalesPerformaInvoice> getClosedInvoicesForVendor(Integer vendorId, Integer dispatchId, Integer sendingUnit);

    public DocumentDetail uploadCancelInvoiceDocument(FileType type, MimeType mimeType, DocUploadType docType, Integer userId,
                                         MultipartFile file);

    public SalesPerformaInvoice getInvoice(Integer invoiceId);

    public SalesPerformaInvoice getCorrectedInvoice(Integer invoiceId);

    public URL downloadEway(Integer invoiceId) throws SalesPerformaInvoiceException, SumoException, MalformedURLException;

    public URL downloadInvoice(Integer invoiceId) throws SalesPerformaInvoiceException, SumoException, MalformedURLException;

    public URL downloadDeliverDoc(Integer invoiceId) throws SalesPerformaInvoiceException, SumoException, MalformedURLException;

    public URL downloadPoDoc(Integer invoiceId) throws SalesPerformaInvoiceException, SumoException, MalformedURLException;

    public URL downloadCancelDoc(Integer invoiceId) throws SalesPerformaInvoiceException, SumoException, MalformedURLException;

    public Integer updateGeneratedId();

    Map<String, String> generateB2BInvoice(Integer invoiceId, Integer userId) throws SalesPerformaInvoiceException, SumoException;

    public DocumentDetail uploadDocument(FileType type, MimeType mimeType, DocUploadType docType, String poId, Integer userId, MultipartFile file);

    public Boolean uploadInvoiceSheet(MultipartFile file, Integer invoiceId, String type) throws IOException, SumoException;

    public String generateDebitNoteDocId(Integer debitNoteId);

    public String generateCreditNoteDocId(Integer creditNoteId);


    public Boolean saveDownloadJsonDocIdNo(Integer invoiceId, String docId);

    public Map<String, String> uploadSpecializedOrderInvoice(MimeType mimeType, SpecializedOrderInvoiceData invoice, File file,
                                                             Integer userId) throws SalesPerformaInvoiceException, SumoException;

    public Pair<URL,GoodsReceived> generateSpecializedOrderInvoice(List<GoodsReceived> goodsReceivedList, GoodsReceived aggregatedGr, Integer userId , Integer vendorId) throws SalesPerformaInvoiceException, SumoException;

    public Boolean saveGrPrDeviations(GoodsReceived goodsReceived , Integer prId) throws SumoException;



    public Boolean saveEntry(OutwardRegister outWardRegisterList) throws IOException, SumoException;

     public List<OutwardRegister> getEntries(Integer sendingUnit, Date startDate, Date endDate , String businessType) throws IOException,SumoException;

    public SalesPerformaInvoice approveCancellation(Integer invoiceId, Integer userId, Integer docId,String salesPerformaStatus) throws SalesPerformaInvoiceException, SumoException, InventoryUpdateException;

    public void cancelOldInvoices(Date date) throws SalesPerformaInvoiceException, SumoException;

    public boolean saveCorrectedInvoiceDetails(SalesPerformaInvoice salesPerformaInvoice,Integer userId) throws Exception ;

    public List<CorrectedSalesInvoiceDetails> getCorrectedInvoiceDetails(Integer invoiceId , String type);

    public boolean saveCreditDebitNoteDetails(CreditDebitNoteDetail creditDebitNoteDetail) throws Exception;

    public List<CreditDebitNoteDetail> getCreditDebitNoteDetails(Date startDate, Date endDate, String status ,Integer vendorId) throws Exception;

    public boolean approveCreditNote(CreditDebitNoteDetail creditDebitNoteDetail ,Integer userId) throws Exception;

    public boolean rejectCreditNote(CreditDebitNoteDetail creditDebitNoteDetail, Integer userId) throws Exception;

    public DocumentDetail uploadVendorInvoice(FileType type, MimeType mimeType, DocUploadType docType, Integer userId, MultipartFile file);

    public CreditDebitNoteDetail getCreditDebitDetail(Integer id) throws Exception;

    public URL downloadCreditNoteDoc(Integer invoiceId) throws SalesPerformaInvoiceException, SumoException, MalformedURLException;

    public URL downloadDebitNoteDoc(Integer invoiceId) throws SalesPerformaInvoiceException, SumoException, MalformedURLException;

    public Map<String, String> generateCreditNote(Integer invoiceId, Integer userId) throws SalesPerformaInvoiceException, SumoException, IOException, TemplateRenderingException;

    public Map<String, String> generateCorrectionCreditNote(Integer invoiceId, Integer userId) throws SalesPerformaInvoiceException, SumoException, IOException, TemplateRenderingException;

    public Map<String, String> generateDebitNote(Integer invoiceId, Integer userId) throws SalesPerformaInvoiceException, SumoException, IOException, TemplateRenderingException;

    public Map<String, String> generateCorrectionDebitNote(Integer invoiceId, Integer userId) throws SalesPerformaInvoiceException, SumoException, IOException, TemplateRenderingException;

    public Boolean uploadCorrectionInvoiceSheet(MultipartFile file, Integer invoiceId, String type) throws IOException, SumoException;

    public URL downloadCorrectionCreditNoteDoc(Integer invoiceId) throws SalesPerformaInvoiceException, SumoException, MalformedURLException;

    public URL downloadCorrectionDebitNoteDoc(Integer invoiceId) throws SalesPerformaInvoiceException, SumoException, MalformedURLException;

    public Boolean validateAndClose(Integer invoiceId, Integer userId) throws SumoException, SalesPerformaInvoiceException;

    byte[] generateB2BInvoicePdf(Integer invoiceId, Boolean saveFile) throws IOException, SumoException, TemplateRenderingException;
}


package com.stpl.tech.scm.notification.email;

import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.List;

public class TransferBlockedNotification extends EmailNotification {

    private EnvType envType;
    private Unit unit;
    private Unit receivingUnit;
    private List<String> emails;

    public TransferBlockedNotification(Unit unit , List<String> toEmails , EnvType envType , Unit receivingUnit){
        this.unit = unit;
        this.emails = toEmails;
        this.envType = envType;
        this.receivingUnit = receivingUnit;
    }

    @Override
    public String[] getToEmails() {
        return SCMUtil.isDev(envType) ? new String[] { "<EMAIL>" } :
                (String[])this.emails.toArray();
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        String subject = "Transfers Blocked !! " + " on "
                + SCMUtil.getFormattedTime(SCMUtil.getCurrentDateIST(), "EEE dd MMM yyyy");
        ;
        if (SCMUtil.isDev(envType)) {
            subject = "[Dev] " + subject;
        }
        return subject;
    }

    @Override
    public String body() throws EmailGenerationException {
        return "<b>Transferring Unit :::::::: " + unit.getName() + "(" + unit.getId() + ")<br>"
                +"You have not done the day closure of "+ receivingUnit.getName() + "("+ receivingUnit.getId() + ")" + "in the permitted time, so this transfer is blocked.<br>"
                +"Please make the day close on time to avoid the blockage of future transfers";
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}

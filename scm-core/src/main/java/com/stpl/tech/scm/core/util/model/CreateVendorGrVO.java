package com.stpl.tech.scm.core.util.model;

import com.stpl.tech.scm.domain.model.InvoiceDocType;
import com.stpl.tech.scm.domain.model.POCreationType;
import com.stpl.tech.scm.domain.model.VendorGRItem;
import com.stpl.tech.scm.domain.model.VendorGrType;

import java.math.BigDecimal;
import java.util.List;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 05-06-2017.
 */
public class CreateVendorGrVO {
    private int userId;
    private int deliveryUnitId;
    private int dispatchId;
    private int vendorId;
    private POCreationType creationType;
    private BigDecimal extraCharges;
    private InvoiceDocType docType;
    private Integer uploadId;
    private String docNumber;
    private String docDate;
    private List<VendorGRItem> items;
    private List<UsedPOModel> usedPOList;
    private boolean amountMatched;
    protected List<VendorGRItem> extraGrItems;
    private VendorGrType vendorGrType;
    private Boolean forceSummit;
    private String type;
    private String comment;
    private String vendorInvoiceDocId;

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public int getDeliveryUnitId() {
        return deliveryUnitId;
    }

    public void setDeliveryUnitId(int deliveryUnitId) {
        this.deliveryUnitId = deliveryUnitId;
    }

    public int getDispatchId() {
        return dispatchId;
    }

    public void setDispatchId(int dispatchId) {
        this.dispatchId = dispatchId;
    }

    public int getVendorId() {
        return vendorId;
    }

    public void setVendorId(int vendorId) {
        this.vendorId = vendorId;
    }

    public POCreationType getCreationType() {
        return creationType;
    }

    public void setCreationType(POCreationType creationType) {
        this.creationType = creationType;
    }

    public List<VendorGRItem> getItems() {
        return items;
    }

    public void setItems(List<VendorGRItem> items) {
        this.items = items;
    }

    public List<UsedPOModel> getUsedPOList() {
        return usedPOList;
    }

    public void setUsedPOList(List<UsedPOModel> usedPOList) {
        this.usedPOList = usedPOList;
    }

    public BigDecimal getExtraCharges() {
        return extraCharges;
    }

    public void setExtraCharges(BigDecimal extraCharges) {
        this.extraCharges = extraCharges;
    }

    public InvoiceDocType getDocType() {
        return docType;
    }

    public void setDocType(InvoiceDocType docType) {
        this.docType = docType;
    }

    public String getDocNumber() {
        return docNumber;
    }

    public void setDocNumber(String docNumber) {
        this.docNumber = docNumber;
    }

    public Integer getUploadId() {
        return uploadId;
    }

    public void setUploadId(Integer uploadId) {
        this.uploadId = uploadId;
    }

    public String getDocDate() {
        return docDate;
    }

    public void setDocDate(String docDate) {
        this.docDate = docDate;
    }

    public boolean isAmountMatched() {
        return this.amountMatched;
    }

    public void setAmountMatched(boolean amountMatched) {
        this.amountMatched = amountMatched;
    }

    public List<VendorGRItem> getExtraGrItems() {
        return extraGrItems;
    }

    public void setExtraGrItems(List<VendorGRItem> extraGrItems) {
        this.extraGrItems = extraGrItems;
    }

    public VendorGrType getVendorGrType() {
        return vendorGrType;
    }

    public void setVendorGrType(VendorGrType vendorGrType) {
        this.vendorGrType = vendorGrType;
    }

	public Boolean getForceSummit() {
		return forceSummit;
	}

	public void setForceSummit(Boolean forceSummit) {
		this.forceSummit = forceSummit;
	}

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getVendorInvoiceDocId() {return vendorInvoiceDocId;}

    public void setVendorInvoiceDocId(String vendorInvoiceDocId) {
        this.vendorInvoiceDocId = vendorInvoiceDocId;
    }


}


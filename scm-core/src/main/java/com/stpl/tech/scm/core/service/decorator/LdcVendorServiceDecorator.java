package com.stpl.tech.scm.core.service.decorator;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.LdcVendorService;
import com.stpl.tech.scm.core.service.impl.LdcVendorServiceImpl;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.LdcVendorDomain;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.BiPredicate;
import java.util.function.Predicate;

@Log4j2
@Component
public class LdcVendorServiceDecorator implements LdcVendorService {

    @Autowired
    LdcVendorServiceImpl ldcVendorServiceImpl;

    BiPredicate<LdcVendorDomain,Date> checkLdcDateValidity = (l,d) -> (d.after(l.getLdcTenureFrom()) && d.before(l.getLdcTenureTo())) ||
            d.equals(l.getLdcTenureFrom()) || d.equals(l.getLdcTenureTo());


    @Override
    public LdcVendorDomain addLdcForVendor(LdcVendorDomain ldcVendorDomain,Integer userId) throws SumoException {
        try{
         log.info("Adding LDC for vendor, vendor id : {}",ldcVendorDomain.getVendorId());
         ldcVendorDomain.setLdcTenureFrom(SCMUtil.getDate(ldcVendorDomain.getLdcTenureFrom()));
         ldcVendorDomain.setLdcTenureTo(SCMUtil.getDate(ldcVendorDomain.getLdcTenureTo()));
        return ldcVendorServiceImpl.addLdcForVendor(ldcVendorDomain,userId);
        }catch(Exception e){
            log.error("########### Error while adding ldc for vendor, payload : {} ", ldcVendorDomain);
            log.error("########### Error while adding ldc for vendor, message : {} ", e.getMessage());
            throw new SumoException("LDC_VENDOR_ERROR", e.getMessage());
        }
    }

    @Override
    public LdcVendorDomain deactiveLdcForVedndor(Long ldcId,Integer userId) throws SumoException {
        try{
            log.info("Deactivating LDC for vendor, ldc id : {}",ldcId);
          return ldcVendorServiceImpl.deactiveLdcForVedndor(ldcId,userId);
        }catch(Exception e){
            log.error("########### Error while Deactivating LDC for vendor, ldc is : {} ", ldcId);
            log.error("########### Error while Deactivating LDC for vendor : {} ", e.getMessage());
            throw new SumoException("LDC_VENDOR_ERROR", e.getMessage());
        }
    }

    @Override
    public Map<Long,LdcVendorDomain> getLdcForVendorId(Integer vendorId) throws SumoException {
        try{
            log.info("get LDC for vendor, vendor id : {}",vendorId);
            return ldcVendorServiceImpl.getLdcForVendorId(vendorId);
        }catch(Exception e){
            log.error("########### Error while getting ldc for vendor, vendor id : {} ", vendorId);
            log.error("########### Error while getting ldc for vendor : {} ", e.getMessage());
            throw new SumoException("LDC_VENDOR_ERROR", e.getMessage());
        }
    }

    @Override
    public LdcVendorDomain updateLdcVendorData(LdcVendorDomain ldcVendorDomain) throws SumoException {
        try{
            log.info("updating LDC for vendor, ldc data : {}",ldcVendorDomain);
            return ldcVendorServiceImpl.updateLdcVendorData(ldcVendorDomain);
        }catch(Exception e){
            log.error("########### Error while updating ldc for vendor : {} ", e.getMessage());
            throw new SumoException("LDC_VENDOR_ERROR", e.getMessage());
        }
    }


    @Override
    public List<LdcVendorDomain> getValidLdcData(Double checkAmount, Date checkDate, Integer vendorId) throws SumoException {
        try{
            Map<Long, LdcVendorDomain> allLdcForVendor =  ldcVendorServiceImpl.getLdcForVendorId(vendorId);
            List<LdcVendorDomain> result = new ArrayList<>();
            allLdcForVendor.values().forEach((e)->{
                        if(checkLdcDateValidity.test(e,checkDate)){
                             log.info("######## ldc date validation passed for ldc id : {}",e.getLdcId());
                            if(e.getRemainingLimit() >= checkAmount){
                                log.info("######## valid ldc limit,  for ldc id : {}",e.getLdcId());
                                result.add(e);
                            }else{
                                log.info("######## ldc limit exhausted, for ldc id : {}",e.getLdcId());
                            }
                        }
                    }
            );
            return result;
        }catch (Exception e){
            log.error("########### Error while getting valid LDC data : {}",e.getMessage());
            throw new SumoException("LDC_VENDOR_DATA",e.getMessage());
        }
    }

    @Override
    public Boolean updateLdcRemainingLimit(Long ldcId, Double deductedAmount) throws Exception {
        try {
            if(deductedAmount<=0){
                log.error("########### deducted amount cannot be negative or zero : {}",deductedAmount);
                return false;
            }
             ldcVendorServiceImpl.updateLdcRemainingLimit(ldcId,deductedAmount);
        }catch (Exception e){
            log.error("########### Error while updating ldc remaining limit : {}",e.getMessage());
            throw new SumoException("LDC_VENDOR_DATA",e.getMessage());
        }
        return true;
    }
}

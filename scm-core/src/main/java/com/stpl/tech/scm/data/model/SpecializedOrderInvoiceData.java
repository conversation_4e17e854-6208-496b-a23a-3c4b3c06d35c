package com.stpl.tech.scm.data.model;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "SPECIALIZED_ORDER_INVOICE")
public class SpecializedOrderInvoiceData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "SPECIALIZED_ORDER_INVOICE_ID")
    private Integer specializedOrderInvoiceId;

    @Column(name = "VENDOR_ID")
    private Integer vendorId;

    @Column(name = "UNIT_ID")
    private Integer unitId;

    @Column(name = "GENERATION_TIME")
    private Date generationTime;

    @Column(name = "IS_PR_RAISED")
    private String isPrRaised;

    @Column(name = "INVOICE_URL")
    private String invoiceUrl;

    @Column(name = "INVOICE_ID")
    private String invoiceId;

    @Column(name = "DOCUMENT_ID")
    private Integer documentId;

    @Column(name = "PR_ID")
    private Integer prId;

    public Integer getSpecializedOrderInvoiceId() {
        return this.specializedOrderInvoiceId;
    }

    public void setSpecializedOrderInvoiceId(Integer specializedOrderInvoiceId) {
        this.specializedOrderInvoiceId = specializedOrderInvoiceId;
    }

    public Integer getVendorId() {
        return this.vendorId;
    }

    public void setVendorId(Integer vendorId) {
        this.vendorId = vendorId;
    }

    public Integer getUnitId() {
        return this.unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Date getGenerationTime() {
        return this.generationTime;
    }

    public void setGenerationTime(Date generationTime) {
        this.generationTime = generationTime;
    }

    public String getIsPrRaised() {
        return this.isPrRaised;
    }

    public void setIsPrRaised(String isPrRaised) {
        this.isPrRaised = isPrRaised;
    }

    public String getInvoiceUrl() {
        return this.invoiceUrl;
    }

    public void setInvoiceUrl(String invoiceUrl) {
        this.invoiceUrl = invoiceUrl;
    }

    public String getInvoiceId() {
        return this.invoiceId;
    }

    public void setInvoiceId(String invoiceId) {
        this.invoiceId = invoiceId;
    }

    public Integer getDocumentId() {
        return this.documentId;
    }


    public void setDocumentId(Integer documentId) {
        this.documentId = documentId;
    }

    public Integer getPrId() {
        return prId;
    }

    public void setPrId(Integer prId) {
        this.prId = prId;
    }
}

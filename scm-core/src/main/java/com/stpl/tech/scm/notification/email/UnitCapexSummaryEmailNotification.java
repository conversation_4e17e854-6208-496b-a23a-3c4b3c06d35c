package com.stpl.tech.scm.notification.email;


import com.stpl.tech.scm.notification.email.template.UnitCapexSummaryTemplate;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.ArrayList;

public class UnitCapexSummaryEmailNotification extends EmailNotification {

    UnitCapexSummaryTemplate unitCapexSummaryTemplate;

    EnvType envType;
    ArrayList<String> destinationMails;

    UnitCapexSummaryEmailNotification(){

    }
    public UnitCapexSummaryEmailNotification(UnitCapexSummaryTemplate unitCapexSummaryTemplate, EnvType envType, ArrayList<String> destinationMails) {
        this.unitCapexSummaryTemplate =unitCapexSummaryTemplate;
        this.envType = envType;
        this.destinationMails = destinationMails;
    }

    @Override
    public String[] getToEmails() {
        if(envType==EnvType.PROD || envType == EnvType.SPROD){
            return  destinationMails.toArray(new String[0]);
        }else{
            String[] emails = new String[1];
            emails[0]="<EMAIL>";
            return emails;
        }
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        if(envType==EnvType.PROD || envType==EnvType.SPROD) {
            return "CLOSE CAPEX BUDGET" + AppUtils.getCurrentDateISTFormatted();
        }else{
            return "[DEV] CLOSE CAPEX BUDGET" + AppUtils.getCurrentDateISTFormatted();
        }
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return unitCapexSummaryTemplate.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }

}

package com.stpl.tech.scm.notification.email;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.AssetRecoveryDefinition;
import com.stpl.tech.scm.notification.email.template.AssetLostNotificationTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 05-09-2016.
 */
public class AssetLostNotification extends EmailNotification {

	private AssetLostNotificationTemplate template;
	private EnvType envType;
	private List<AssetRecoveryDefinition> AssetRecoveryDefinitionList;
	private List<String> emails;

	public AssetLostNotification() {

	}

	public AssetLostNotification(AssetLostNotificationTemplate template,
								 EnvType envType,
								 List<AssetRecoveryDefinition> AssetRecoveryDefinitionList,
								 List<String> emails) {
		this.template = template;
		this.envType = envType;
		this.AssetRecoveryDefinitionList = AssetRecoveryDefinitionList;
		this.emails = emails;
	}

	@Override
	public String[] getToEmails() {

		return SCMUtil.isDev(envType) ? new String[] { "<EMAIL>" } : (String[])this.emails.toArray();
	}

	@Override
	public String getFromEmail() {
		return "<EMAIL>";
	}

	@Override
	public String subject() {
		String subject = "Asset Lost Detected During Stock Taking event at unit " + AssetRecoveryDefinitionList.get(0).getUnitId()  + " on "
				+ SCMUtil.getFormattedTime(SCMUtil.getCurrentDateIST(), "EEE dd MMM yyyy");
		;
		if (SCMUtil.isDev(envType)) {
			subject = "Dev " + subject;
		}
		return subject;
	}

	public String body() throws EmailGenerationException {
		try {
			return template.getContent();
		} catch (TemplateRenderingException e) {
			throw new EmailGenerationException("Failed to render the template", e);
		}
	}

	@Override
	public EnvType getEnvironmentType() {
		return envType;
	}
}

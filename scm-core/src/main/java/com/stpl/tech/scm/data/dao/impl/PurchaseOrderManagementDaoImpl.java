package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.dao.PurchaseOrderManagementDao;
import com.stpl.tech.scm.data.model.PurchaseOrderData;
import com.stpl.tech.scm.data.model.PurchaseOrderItemData;
import com.stpl.tech.scm.data.model.StateSequenceId;
import com.stpl.tech.scm.data.model.VendorGrPoItemMappingData;
import com.stpl.tech.scm.domain.model.*;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by Rahul Singh on 22-06-2016.
 */
@Repository
public class PurchaseOrderManagementDaoImpl extends SCMAbstractDaoImpl implements PurchaseOrderManagementDao {

    private static final Logger LOG = LoggerFactory.getLogger(PurchaseOrderManagementDaoImpl.class);

    @Override
    public List<PurchaseOrderData> findClonablePurchaseOrders(int vendorId, int deliveryUnitId, int dispatchId) {
        List<PurchaseOrderData> purchaseOrderDataList = null;

        String queryString = "FROM PurchaseOrderData r WHERE r.generatedForVendor = :vendorId " +
                "AND r.dispatchLocationId = :dispatchId AND r.deliveryLocationId = :deliveryUnitId " +
                "AND r.status not in (:cancelled) ORDER BY id DESC";

        List<String> rejectedStatuses = Arrays.asList(PurchaseOrderStatus.CANCELLED.name(),
                PurchaseOrderStatus.REJECTED.name());

        Query query = manager.createQuery(queryString);
        query.setParameter("vendorId",vendorId);
        query.setParameter("dispatchId",dispatchId);
        query.setParameter("deliveryUnitId",deliveryUnitId);
        query.setParameter("cancelled", rejectedStatuses);
        query.setMaxResults(15);
        purchaseOrderDataList = query.getResultList();
        return purchaseOrderDataList;
    }

    @Override
    public List<PurchaseOrderData> findPurchaseOrdersByStatus(Integer vendorId, Integer dispatchId, int deliveryUnitId,
                                                              List<Integer> skus, List<PurchaseOrderStatus> statusList,
                                                              Date startDate, Date endDate, Integer PurchaseOrderId, PurchaseOrderStatus status,PurchaseOrderExtendedStatus expiryStatus) {
        List<PurchaseOrderData> purchaseOrderDataList = null;
        List<String> statuses = statusList.stream().map(PurchaseOrderStatus::name).collect(Collectors.toList());
        if(statuses!=null && !statuses.isEmpty()){

            StringBuilder queryString = new StringBuilder("SELECT DISTINCT r FROM PurchaseOrderData r, PurchaseOrderItemData s");
            queryString.append(" WHERE r.deliveryLocationId = :deliveryUnitId and r.id = s.purchaseOrderData.id");
            queryString.append("  AND r.generationTime >= :startDate");
            queryString.append(" AND r.generationTime <= :endDate");


            if(expiryStatus!=null){
                queryString.append(" AND (r.expiryStatus != :expiryStatus OR r.expiryStatus IS NULL)");
            }

            if(vendorId!=null){
                queryString.append(" AND r.generatedForVendor = :vendorId");
            }
            
            if (status != null) {
                queryString.append(" AND r.status = :status ");
            }else {
            	queryString.append(" AND r.status in (:statusList)");
            }
            
            if (PurchaseOrderId != null) {
                queryString.append(" AND r.id = :id ");
            }

            if(dispatchId!=null){
                queryString.append(" AND r.dispatchLocationId = :dispatchId");
            }

            if(skus!=null && !skus.isEmpty()){
                queryString.append(" AND s.skuId in (:skus)");
            }

            queryString.append(" ORDER BY r.id ASC");

            Query query = manager.createQuery(queryString.toString());

           
            query.setParameter("deliveryUnitId", deliveryUnitId);
            query.setParameter("startDate", startDate);
            query.setParameter("endDate", endDate);
            if(expiryStatus!=null) {
                query.setParameter("expiryStatus", PurchaseOrderExtendedStatus.EXPIRED.value());
            }

            if(vendorId!=null){
                query.setParameter("vendorId", vendorId);
            }

            if(dispatchId!=null) {
                query.setParameter("dispatchId", dispatchId);
            }
            
            if (status != null) {
                query.setParameter("status", status.name());
            }else {
            	 query.setParameter("statusList", statuses);
            }
            
            if (PurchaseOrderId != null) {
                query.setParameter("id", PurchaseOrderId);
            }

            if(skus!=null && !skus.isEmpty()){
                query.setParameter("skus", skus);
            }
            purchaseOrderDataList = query.getResultList();
        }
        return purchaseOrderDataList;
    }
    @Override
    public List<Integer> getSkuPriceDataId(Integer skuId, Integer skuPackagingId, Integer vendorId, Integer dispatchLocationId, Integer deliveryLocationId){

            StringBuilder queryString = new StringBuilder("SELECT s.skuPriceKeyId " +
                    "FROM SkuPriceData s");
            queryString.append(" WHERE s.skuId = :skuId AND");
            queryString.append(" s.packagingId = :skuPackagingId AND");
            queryString.append(" s.vendorId = :vendorId AND");
            queryString.append(" s.dispatchLocationId = :dispatchLocationId AND");
            queryString.append(" s.deliveryLocationId = :deliveryLocationId");
            Query query = manager.createQuery(queryString.toString());
            query.setParameter("skuId", skuId);
            query.setParameter("skuPackagingId", skuPackagingId);
            query.setParameter("vendorId", vendorId);
            query.setParameter("dispatchLocationId", dispatchLocationId);
            query.setParameter("deliveryLocationId", deliveryLocationId);

            return query.getResultList();
    }

    @Override
    public List<Pair<BigDecimal, String>> getSkuPriceHistory(Integer skuPriceDataId) {
        List<Pair<BigDecimal, String>> skuPriceHistoryList = new ArrayList<Pair<BigDecimal, String>>();
        StringBuilder queryString = new StringBuilder("SELECT s.negotiatedPrice, s.createdAt " +
                "FROM SkuPriceHistory s");
        queryString.append(" WHERE s.skuPriceDataId = :skuPriceDataId AND s.changeType = :changeType ");
        queryString.append(" ORDER BY s.createdAt DESC" );
        Query query = manager.createQuery(queryString.toString());
        query.setParameter("skuPriceDataId", skuPriceDataId);
        query.setParameter("changeType", ChangeType.PRICE_UPDATE.name());
        query.setMaxResults(5);
        List<Object[]> result = query.getResultList();
        for(Object[] o : result){
            skuPriceHistoryList.add(new Pair<>((BigDecimal) o[0], AppUtils.getDateString(AppUtils.covertDateIST(((Timestamp) o[1]).getTime()),"dd MMM yyyy HH:mm:ss")));
        }
        return skuPriceHistoryList;
    }

    @Override
    public List<PurchaseOrderItemData> checkExtraGrEligibility(Integer unitId, Integer vendorId, Integer dispatchId,
                                                               List<Integer> poIds, List<Integer> skus) {

        String queryStr = "SELECT DISTINCT F FROM PurchaseOrderData E, PurchaseOrderItemData F " +
                "WHERE E.id = F.purchaseOrderData.id AND E.status IN (:statusList) and E.id NOT in (:poIds) " +
                "AND E.generatedForVendor = :vendorId AND E.dispatchLocationId = :dispatchId AND E.deliveryLocationId = :unitId " +
                "AND F.skuId IN (:skus)";
        Query query = manager.createQuery(queryStr);
        query.setParameter("unitId", unitId)
            .setParameter("vendorId", vendorId)
            .setParameter("dispatchId", dispatchId)
            .setParameter("poIds", poIds)
            .setParameter("skus", skus)
            .setParameter("statusList", Arrays.asList(PurchaseOrderStatus.APPROVED.name(),PurchaseOrderStatus.IN_PROGRESS.name()));

        return query.getResultList();
    }

    @Override
    public List<Object[]> getConsumptionForPurchase(int daysInPast, List<Integer> skus, Integer unitId) {
        Date toDate = SCMUtil.getCurrentBusinessDate();
        Date fromDate = SCMUtil.getDayBeforeOrAfterDay(toDate,-daysInPast);
        String queryStr = "SELECT E.skuId, E.closureEvent.businessDate as BUSINESS_DATE, SUM(COALESCE(E.transferred,0) + COALESCE(E.consumed,0) + COALESCE(E.wasted,0)) as CONSUMED " +
                "FROM DayCloseInventoryDrillDown E " +
                "WHERE E.closureEvent.businessDate >= :fromDate AND  E.closureEvent.businessDate < :toDate AND E.skuId IN (:skus) and E.closureEvent.status = :closed " +
                "AND E.closureEvent.dayCloseEventType = :eventType AND E.closureEvent.unitId = :unitId GROUP BY E.skuId, E.closureEvent.businessDate";

        Query query = manager.createQuery(queryStr);
        query.setParameter("unitId", unitId)
                .setParameter("eventType", StockEventType.WH_CLOSING.name())
                .setParameter("fromDate", fromDate)
                .setParameter("toDate", toDate)
                .setParameter("skus", skus)
                .setParameter("closed", StockEventStatus.CLOSED.name());
        return query.getResultList();
    }

    @Override
    public int getNextStateInvoiceId(int stateId, String type) {

        Query query = manager.createQuery("FROM StateSequenceId E where E.stateId = :stateId and E.idType = :idType and E.financialYear IS NULL");
        query.setParameter("idType", type);
        query.setParameter("stateId", stateId);
        StateSequenceId sequence = null;
        try {
            sequence = (StateSequenceId) query.getSingleResult();
        } catch (NoResultException e) {
            sequence = addStateSequenceId(stateId, type);
        }
        int currentValue = sequence.getNextValue();
        sequence.setNextValue(currentValue + 1);
        return currentValue;
    }

    @Override
    public int getNextStateInvoiceId(int stateId, String type, Integer financialYear) {
        Query query = manager.createQuery("FROM StateSequenceId E where E.stateId = :stateId and E.idType = :idType and E.financialYear =:financialYear");
        query.setParameter("idType", type);
        query.setParameter("stateId", stateId);
        query.setParameter("financialYear", financialYear);
        StateSequenceId sequence = null;
        try {
            sequence = (StateSequenceId) query.getSingleResult();
        } catch (NoResultException e) {
            sequence = addStateSequenceId(stateId, type, financialYear);
        }
        int currentValue = sequence.getNextValue();
        sequence.setNextValue(currentValue + 1);
        return currentValue;
    }

    private StateSequenceId addStateSequenceId(int stateId, String type, Integer financialYear) {
        StateSequenceId info = new StateSequenceId(stateId, type, 1, financialYear);
        manager.persist(info);
        return info;
    }

    private StateSequenceId addStateSequenceId(int stateId, String type) {
        StateSequenceId info = new StateSequenceId(stateId, type, 1);
        manager.persist(info);
        return info;

    }


    @Override
    public List<PurchaseOrderData> getAllToBeExpirePurchaseOrder() {
        Date toDate = SCMUtil.getCurrentBusinessDate();
        System.out.println("current date is:"+ toDate);
        Date fromDate = SCMUtil.getDayBeforeOrAfterDay(toDate,-7);
        Query query = manager.createQuery("FROM PurchaseOrderData E where E.expiryDate = :expiryDate and E.status <>:closedStatus");
        query.setParameter("expiryDate", SCMUtil.getCurrentBusinessDate());
        query.setParameter("closedStatus", PurchaseOrderStatus.CLOSED.name());
        return query.getResultList();
    }

    @Override
    public List<PurchaseOrderData> getAllExpiredPurchaseOrders() {
        Query query = manager.createQuery("FROM PurchaseOrderData E where E.expiryDate <:expiryDate and E.status in (:inProgressStatus)");
        query.setParameter("expiryDate", SCMUtil.getCurrentBusinessDate());
        query.setParameter("inProgressStatus", Arrays.asList(PurchaseOrderStatus.IN_PROGRESS.name(), PurchaseOrderStatus.APPROVED.name()));
        return query.getResultList();
    }

    @Override
    public List<PurchaseOrderData> getAllExpiredAndNeedToClosePurchaseOrders() {
        Date toDate = SCMUtil.getCurrentBusinessDate();
        Date fromDate = SCMUtil.getDayBeforeOrAfterDay(toDate,-7);
        Query query = manager.createQuery("FROM PurchaseOrderData E where E.expiryDate = :expiryDate and E.expiryStatus = :expiryStatus and E.status <>:closedStatus");
        query.setParameter("expiryDate", AppUtils.getDayBeforeOrAfterDay(SCMUtil.getCurrentBusinessDate(),-7));
        query.setParameter("expiryStatus",PurchaseOrderExtendedStatus.EXPIRED.value());
        query.setParameter("closedStatus", PurchaseOrderStatus.CLOSED.name());
        return query.getResultList();
    }
    @Override
    public List<PurchaseOrderData> getAllExpiredAndExpiringPurchaseOrders(Date fromDate) {
        Query query = manager.createQuery("FROM PurchaseOrderData E where E.expiryDate =:expiryDate and E.expiryStatus =:expiryStatus and E.status <>:closedStatus");
        query.setParameter("expiryDate", fromDate);
        query.setParameter("expiryStatus",PurchaseOrderExtendedStatus.UN_EXPIRED.value());
        query.setParameter("closedStatus", PurchaseOrderStatus.CLOSED.name());
        return query.getResultList();
    }

    @Override
    public List<PurchaseOrderData> getPosForAdvance(Integer vendorId) {
        Query query = manager.createQuery("FROM PurchaseOrderData E where E.generatedForVendor = :vendorId and E.status =:status" +
                " and E.expiryStatus IN(:expiryStatus)");
        query.setParameter("vendorId", vendorId);
        query.setParameter("status",PurchaseOrderStatus.APPROVED.value());
        query.setParameter("expiryStatus",Arrays.asList(PurchaseOrderExtendedStatus.UN_EXPIRED.value(), PurchaseOrderExtendedStatus.EXTENDED.value()));
        return query.getResultList();
    }

    @Override
    public Map<Integer,BigDecimal> getPoLevelPaidAmount(List<Integer> poIds) throws SumoException {
        try{
            Query query = manager.createNativeQuery("SELECT T.PURCHASE_ORDER_ID, SUM(T.TOTAL_AMOUNT) FROM\n" +
                    "(\n" +
                    "SELECT  PO.PURCHASE_ORDER_ID,VGID.TOTAL_AMOUNT\n" +
                    "FROM PURCHASE_ORDER PO INNER JOIN PURCHASE_ORDER_ITEM_DETAIL POI ON PO.PURCHASE_ORDER_ID = POI.PURCHASE_ORDER_ID\n" +
                    "INNER JOIN PURCHASE_ORDER_TO_GOODS_RECEIVED_MAPPING POGR ON PO.PURCHASE_ORDER_ID = POGR.PURCHASE_ORDER_ID\n" +
                    "inner join VENDOR_GOODS_RECEIVED_DATA VGR ON VGR.GOODS_RECEIVED_ID = POGR.VENDOR_GOODS_RECEIVED_ID \n" +
                    "INNER JOIN VENDOR_GR_ITEM_DETAIL VGID ON VGID.VENDOR_GR_ID = VGR.GOODS_RECEIVED_ID\n" +
                    "INNER JOIN VENDOR_GR_PO_ITEM_MAPPING_DATA VGPOIM ON VGPOIM.VENDOR_GOODS_RECEIVED_ITEM_ID = VGID.GOODS_RECEIVED_ITEM_ID AND VGPOIM.PURCHASE_ORDER_ITEM_ID = POI.PURCHASE_ORDER_ITEM_ID\n" +
                    " INNER JOIN PAYMENT_REQUEST PR ON PR.PAYMENT_REQUEST_ID = VGR.PAYMENT_REQUEST_ID \n" +
                    " WHERE PR.CURRENT_STATUS = \"PAID\" AND PO.PURCHASE_ORDER_ID IN (:poIds)\n" +
                    " ) AS T GROUP BY T.PURCHASE_ORDER_ID;");
            query.setParameter("poIds",poIds);
            List<Object[]> totalAmountList =  query.getResultList();
            Map<Integer,BigDecimal> poIdAndPaidAmount = new HashMap<>();
            if(Objects.nonNull(totalAmountList)){
                totalAmountList.forEach(item ->{
                    poIdAndPaidAmount.put((Integer) item[0], (BigDecimal) item[1]);
                });
            }
            return poIdAndPaidAmount;
        }catch (Exception e){
            LOG.info("Exception while fetching paid amount data for po, error : {}",e.getMessage());
            throw new SumoException(e);
        }
    }

    @Override
    public Boolean isPoClubInGr(Integer poId) throws SumoException {
      try{
          Query poItemQuery = manager.createNativeQuery("SELECT PURCHASE_ORDER_ITEM_ID from  PURCHASE_ORDER PO INNER JOIN PURCHASE_ORDER_ITEM_DETAIL POI ON PO.PURCHASE_ORDER_ID = POI.PURCHASE_ORDER_ID  \n" +
                  "WHERE PO.PURCHASE_ORDER_ID = :poId" );
          poItemQuery.setParameter("poId", poId);
          List<Integer> poItems = poItemQuery.getResultList();

          Query poToGrItemQuery = manager.createNativeQuery("select * from  VENDOR_GR_PO_ITEM_MAPPING_DATA WHERE VENDOR_GOODS_RECEIVED_ITEM_ID IN (\n" +
                  "select VENDOR_GOODS_RECEIVED_ITEM_ID  from VENDOR_GR_PO_ITEM_MAPPING_DATA WHERE PURCHASE_ORDER_ITEM_ID in (:poItems))");
          poToGrItemQuery.setParameter("poItems", poItems);
          List<VendorGrPoItemMappingData> vendorGrPoItemMappingDataList = poToGrItemQuery.getResultList();

          return vendorGrPoItemMappingDataList.size() != poItems.size();
      }catch (Exception e){
          LOG.info("Exception while fetching po club in gr data , error :{}",e.getMessage());
          throw new SumoException(e);
      }

    }

    @Override
   public List<PoItemLevelSummary> getPoItemLevelSummary(List<Integer> poIds) throws SumoException {
        try{
            Query query = manager.createNativeQuery("SELECT  PO.PURCHASE_ORDER_ID, POI.PURCHASE_ORDER_ITEM_ID, POI.SKU_ID, POI.SKU_NAME, POI.REQUESTED_QUANTITY, POI.AMOUNT_PAID, VGR.GOODS_RECEIVED_ID, VGID.GOODS_RECEIVED_ITEM_ID," +
                    "VGID.TOTAL_AMOUNT, PR.CURRENT_STATUS  " +
                    "FROM  PURCHASE_ORDER PO INNER JOIN PURCHASE_ORDER_ITEM_DETAIL POI ON PO.PURCHASE_ORDER_ID = POI.PURCHASE_ORDER_ID " +
                    "INNER JOIN PURCHASE_ORDER_TO_GOODS_RECEIVED_MAPPING POGR ON PO.PURCHASE_ORDER_ID = POGR.PURCHASE_ORDER_ID " +
                    "inner join VENDOR_GOODS_RECEIVED_DATA VGR ON VGR.GOODS_RECEIVED_ID = POGR.VENDOR_GOODS_RECEIVED_ID " +
                    "INNER JOIN VENDOR_GR_ITEM_DETAIL VGID ON VGID.VENDOR_GR_ID = VGR.GOODS_RECEIVED_ID  " +
                    "INNER JOIN VENDOR_GR_PO_ITEM_MAPPING_DATA VGPOIM ON VGPOIM.VENDOR_GOODS_RECEIVED_ITEM_ID = VGID.GOODS_RECEIVED_ITEM_ID AND VGPOIM.PURCHASE_ORDER_ITEM_ID = POI.PURCHASE_ORDER_ITEM_ID " +
                    " INNER JOIN PAYMENT_REQUEST PR ON PR.PAYMENT_REQUEST_ID = VGR.PAYMENT_REQUEST_ID " +
                    " WHERE  PO.PURCHASE_ORDER_ID IN (:poIds)");
            query.setParameter("poIds",poIds);
            List<Object[]> result = query.getResultList();
            List<PoItemLevelSummary> res = new ArrayList<>();
            if(!result.isEmpty()){
                for(Object[] obj : result){
                    PoItemLevelSummary poItemLevelSummary = new PoItemLevelSummary((Integer) obj[0],(Integer) obj[1],(Integer) obj[2],(String)obj[3],(BigDecimal) obj[4],(BigDecimal)obj[5],(Integer) obj[6],(Integer) obj[7],(BigDecimal) obj[8],(String) obj[9]);
                res.add(poItemLevelSummary);
                }
            }
        return res;
        }catch (Exception e){
            LOG.info("Exception while fetching po item level summary , error :{}",e.getMessage());
            throw new SumoException(e);
        }

   }

   @Override
   public Map<Integer,BigDecimal> getPoAdvancePaymentData(List<Integer> poIds) throws SumoException {
       try{
           Query query = manager.createNativeQuery("SELECT T.PURCHASE_ORDER_ID, SUM(T.PR_AMOUNT) FROM\n" +
                   "(SELECT PO.PURCHASE_ORDER_ID, APD.PR_AMOUNT FROM PURCHASE_ORDER PO INNER JOIN ADVANCE_PAYMENT_DATA APD ON PO.PURCHASE_ORDER_ID = APD.PURCHASE_ORDER_ID\n" +
                   "  where PO.PURCHASE_ORDER_ID IN (:poIds)\n" +
                   ") T GROUP BY T.PURCHASE_ORDER_ID;");
           query.setParameter("poIds",poIds);
           List<Object[]> totalAmountList =  query.getResultList();
           Map<Integer,BigDecimal> poIdAndAdvanceAmount = new HashMap<>();
           if(Objects.nonNull(totalAmountList)){
               totalAmountList.forEach(item ->{
                   poIdAndAdvanceAmount.put((Integer) item[0], (BigDecimal) item[1]);
               });
           }
           return poIdAndAdvanceAmount;
       }catch (Exception e){
           LOG.info("Exception while fetching advance amount data for po, error : {}",e.getMessage());
           throw new SumoException(e);
       }
   }

    @Override
    public Set<String> findDepartmentNamesByIds(Set<Integer> missingDepartmentIds) {
        if(CollectionUtils.isEmpty(missingDepartmentIds)) {
            return Set.of();
        }
        Query query = manager.createQuery("select l.name from ListDetailData l where l.id in (:deptIds)");
        query.setParameter("deptIds", missingDepartmentIds);
        return new HashSet<>(query.getResultList());
    }

}

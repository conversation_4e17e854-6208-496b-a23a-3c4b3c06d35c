package com.stpl.tech.scm.core.cache.service;

import com.stpl.tech.scm.core.cache.base.BaseCacheManager;
import com.stpl.tech.scm.data.redis.service.CacheRefreshService;
import com.stpl.tech.scm.data.redis.service.RedisHashManager;
import com.stpl.tech.scm.data.redis.service.RedisHashMap;
import com.stpl.tech.scm.domain.model.VendorDetail;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class VendorCacheManager extends BaseCacheManager<Integer, VendorDetail> {

    public VendorCacheManager(RedisHashManager redisHashManager,
                              CacheRefreshService cacheRefreshService,
                              @Qualifier("taskExecutor") ThreadPoolTaskExecutor taskExecutor) {
        super(redisHashManager, cacheRefreshService, taskExecutor);
    }

    @Override
    protected RedisHashMap<Integer, VendorDetail> getRedisHash() {
        return redisHashManager.vendorDetail();
    }

    @Override
    protected Map<Integer, VendorDetail> loadFromSource() {
        return cacheRefreshService.reloadVendorCache();
    }

    @Override
    protected Integer extractKey(VendorDetail vendor) {
        return vendor.getVendorId();
    }
}

package com.stpl.tech.scm.core.service;

import com.stpl.tech.master.domain.model.ApiResponse;
import com.stpl.tech.scm.domain.model.UnitBrandFilterRequest;

import java.util.List;

public interface SCMFilterService {
    ApiResponse filterProductBasicDetail(UnitBrandFilterRequest filterRequest);

    List<Integer> getMappedBrands(Integer companyId);

    Integer getCompanyIdWithFilterChecks();

    boolean isExternalCompany(Integer companyId);

}

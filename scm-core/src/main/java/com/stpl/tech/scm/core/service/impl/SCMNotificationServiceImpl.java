package com.stpl.tech.scm.core.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.amazonaws.services.simpleemail.model.SendEmailResult;
import com.stpl.tech.master.core.CacheReferenceType;
import com.stpl.tech.scm.core.service.RequestOrderManagementService;
import com.stpl.tech.scm.data.dao.EmailNotificationStatusDao;
import com.stpl.tech.scm.data.enums.NotificationKeyType;
import com.stpl.tech.scm.data.enums.SchedulerStatus;
import com.stpl.tech.scm.data.model.EmailNotificationStatus;
import com.stpl.tech.scm.domain.model.LostAssetEmailObject;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.data.model.VendorGoodsReceivedData;
import com.stpl.tech.scm.domain.model.AssetRecoveryDefinition;
import com.stpl.tech.scm.domain.model.RoEmailMetadata;
import com.stpl.tech.scm.notification.email.*;
import com.stpl.tech.scm.notification.email.template.*;
import com.stpl.tech.util.AppConstants;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.external.notification.service.SMSClientProviderService;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.PurchaseOrderCreationException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.SCMNotificationService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.dao.PurchaseOrderManagementDao;
import com.stpl.tech.scm.data.model.GoodsReceivedData;
import com.stpl.tech.scm.data.model.GoodsReceivedItemData;
import com.stpl.tech.scm.data.model.PurchaseOrderData;
import com.stpl.tech.scm.data.model.AssetDefinitionData;
import com.stpl.tech.scm.data.model.PurchaseOrderNotificationData;
import com.stpl.tech.scm.data.model.SCMDayCloseEventData;
import com.stpl.tech.scm.data.model.SalesPerformaDetailData;
import com.stpl.tech.scm.data.model.ServiceOrderData;
import com.stpl.tech.scm.data.model.ServiceOrderNotificationData;
import com.stpl.tech.scm.data.model.VendorRegistrationRequestDetail;
import com.stpl.tech.scm.domain.model.NotificationType;
import com.stpl.tech.scm.domain.model.PurchaseOrderStatus;
import com.stpl.tech.scm.domain.model.RequestOrder;
import com.stpl.tech.scm.domain.model.RequestOrderItem;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorStatus;
import com.stpl.tech.scm.notification.SMS.VendorROSMSNotification;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.notification.AttachmentData;

import javax.jms.JMSException;

/**
 * Created by Chaayos on 22-09-2016.
 */
@Service
public class SCMNotificationServiceImpl implements SCMNotificationService {

	Logger LOG = LoggerFactory.getLogger(SCMNotificationServiceImpl.class);

	@Autowired
	private NotificationService notificationService;

	@Autowired
	private MasterDataCache masterDataCache;

	@Autowired
	private EnvProperties envProperties;

	@Autowired
	private PurchaseOrderManagementDao purchaseOrderDao;

	@Autowired
	private SCMCache scmCache;

	@Autowired
	private SMSClientProviderService providerService;

	@Autowired
	private RequestOrderManagementService requestOrderManagementService;

	@Autowired
	private EmailNotificationStatusDao emailNotificationStatusDao;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void sendVendorRONotification(boolean enabled, List<VendorDetail> vendorDetails, List<RequestOrder> requestOrderList, boolean isMilk,
			NotificationType type) {
		sendVendorNotification(enabled, vendorDetails, requestOrderList, isMilk, type, false);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<Integer> sendVendorRONotification(boolean enabled, List<RequestOrder> requestOrderList,
												  NotificationType type, boolean isScheduled) {
		List<Integer> list = new ArrayList<>();
		if (requestOrderList != null && requestOrderList.size() > 0) {
			sendVendorNotification(enabled, requestOrderList, type,isScheduled);
			for (RequestOrder ro : requestOrderList) {
				list.add(ro.getId());
			}
		}
		return list;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void sendVendorGRNotification(boolean enabled, List<VendorDetail> vendorDetails, List<RequestOrder> requestOrders, boolean isMilk,
			NotificationType type) {
		sendVendorNotification(enabled, vendorDetails, requestOrders, isMilk, type, true);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public PurchaseOrderNotificationData sendPONotification(PurchaseOrderData purchaseOrderData, VendorDetail vendor,
			VendorPOEmailNotificationTemplate template, File poInvoice) throws PurchaseOrderCreationException {

		VendorPOEmailNotification emailNotification = new VendorPOEmailNotification(template,
				envProperties.getEnvType(), envProperties.vendorPurchaseEmailCC(),
				purchaseOrderData.getFulfillmentDate());
		try {
			List<AttachmentData> attachments = new ArrayList<>();
			AttachmentData invoiceDetail = null;
			invoiceDetail = new AttachmentData(IOUtils.toByteArray(new FileInputStream(poInvoice)), poInvoice.getName(),
					"application/pdf");
			attachments.add(invoiceDetail);
			emailNotification.sendRawMail(attachments);

			PurchaseOrderNotificationData notificationData = new PurchaseOrderNotificationData();
			notificationData.setMessage(String.format(
					"PURCHASE PURCHASE_ORDER CREATION NOTIFICATION FROM CHAAYOS TO %s", vendor.getEntityName()));
			notificationData.setNotificationCarrier("Amazon");
			notificationData.setServiceClient("AWS-SES");
			notificationData.setContact(template.getDispatchLocation().getContactEmail());
			notificationData.setNotificationSent(SCMServiceConstants.SCM_CONSTANT_YES);
			notificationData.setVendorNotificationType(NotificationType.EMAIL.name());
			notificationData.setPurchaseOrderId(purchaseOrderData);
			notificationData.setNotificationTime(SCMUtil.getCurrentTimestamp());
			notificationData = purchaseOrderDao.add(notificationData, true);
			poInvoice.delete();// delete the temp file created on Server
			return notificationData;
		} catch (Exception e) {
			LOG.error(
					"Encountered error while sending PO email notification to Vendor ::::: {}, {} for Dispatch Location {}",
					vendor.getVendorId(), vendor.getEntityName(), template.getDispatchLocation().getDispatchId(), e);
			throw new PurchaseOrderCreationException("Error while sending email to vendor ::::" + vendor.getVendorId());
		}

	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public PurchaseOrderNotificationData sendCancelledPONotification(PurchaseOrderData purchaseOrderData, VendorDetail vendor,
			VendorPOEmailNotificationTemplate template, File poInvoice) throws PurchaseOrderCreationException {

		VendorCancelledPOEmailNotification emailNotification = new VendorCancelledPOEmailNotification(template,
				envProperties.getEnvType(), envProperties.vendorPurchaseEmailCC(),
				purchaseOrderData.getFulfillmentDate());
		try {
			List<AttachmentData> attachments = new ArrayList<>();
			AttachmentData invoiceDetail = null;
			invoiceDetail = new AttachmentData(IOUtils.toByteArray(new FileInputStream(poInvoice)), poInvoice.getName(),
					"application/pdf");
			attachments.add(invoiceDetail);
			emailNotification.sendRawMail(attachments);

			PurchaseOrderNotificationData notificationData = new PurchaseOrderNotificationData();
			notificationData.setMessage(String.format(
					"CANCELLED PURCHASE PURCHASE_ORDER NOTIFICATION FROM CHAAYOS TO %s", vendor.getEntityName()));
			notificationData.setNotificationCarrier("Amazon");
			notificationData.setServiceClient("AWS-SES");
			notificationData.setContact(template.getDispatchLocation().getContactEmail());
			notificationData.setNotificationSent(SCMServiceConstants.SCM_CONSTANT_YES);
			notificationData.setVendorNotificationType(NotificationType.EMAIL.name());
			notificationData.setPurchaseOrderId(purchaseOrderData);
			notificationData.setNotificationTime(SCMUtil.getCurrentTimestamp());
			notificationData = purchaseOrderDao.add(notificationData, true);
			poInvoice.delete();// delete the temp file created on Server
			return notificationData;
		} catch (Exception e) {
			LOG.error(
					"Encountered error while sending PO email notification to Vendor ::::: {}, {} for Dispatch Location",
					vendor.getVendorId(), vendor.getEntityName(), template.getDispatchLocation().getDispatchId(), e);
			throw new PurchaseOrderCreationException("Error while sending email to vendor ::::" + vendor.getVendorId());
		}

	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void sendRejectedPONotification(PurchaseOrderData purchaseOrderData, VendorDetail vendor,
																	 VendorPOEmailNotificationTemplate template, File poInvoice) throws PurchaseOrderCreationException {

		RejectedPOEmailNotification emailNotification = new RejectedPOEmailNotification(template,
				envProperties.getEnvType(), envProperties.vendorPurchaseEmailCC(),
				purchaseOrderData.getFulfillmentDate());
		try {
			List<AttachmentData> attachments = new ArrayList<>();
			AttachmentData invoiceDetail = null;
			invoiceDetail = new AttachmentData(IOUtils.toByteArray(new FileInputStream(poInvoice)), poInvoice.getName(),
					"application/pdf");
			attachments.add(invoiceDetail);
			emailNotification.sendRawMail(attachments);
			poInvoice.delete();// delete the temp file created on Server
		} catch (Exception e) {
			LOG.error("Error encountered while sending Rejected PO mail :: ",e);
			throw new PurchaseOrderCreationException("Error while sending email to vendor ::::" + vendor.getVendorId());
		}

	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public ServiceOrderNotificationData sendSONotification(ServiceOrderData serviceOrderData, VendorDetail vendor,
														   VendorSOEmailNotificationTemplate template, File poInvoice, File uploadedFile, String uploadedMimeType,
														   String[] emails, Boolean sendMail,Boolean logStore) throws SumoException {

		VendorSOEmailNotification emailNotification = new VendorSOEmailNotification(template, envProperties.getEnvType(), emails,sendMail);
		try {
			List<AttachmentData> attachments = new ArrayList<>();
			AttachmentData invoiceDetail = null;
			invoiceDetail = new AttachmentData(IOUtils.toByteArray(new FileInputStream(poInvoice)), poInvoice.getName(),
					"application/pdf");
			attachments.add(invoiceDetail);
			if(uploadedFile !=null && uploadedMimeType !=null) {
				AttachmentData uploadedDocument = null;
				uploadedDocument = new AttachmentData(IOUtils.toByteArray(new FileInputStream(uploadedFile)), uploadedFile.getName(),
						MimeType.mimeValue(uploadedMimeType));
				attachments.add(uploadedDocument);
			}
			emailNotification.sendRawMail(attachments);
			ServiceOrderNotificationData notificationData = new ServiceOrderNotificationData();
			if(Objects.nonNull(logStore) && logStore){
				notificationData.setMessage(String.format(
						"SERVICE PURCHASE_ORDER CREATION NOTIFICATION FROM CHAAYOS TO %s", vendor.getEntityName()));
				notificationData.setNotificationCarrier("Amazon");
				notificationData.setServiceClient("AWS-SES");
				notificationData.setContact(template.getDispatchLocation().getContactEmail());
				notificationData.setNotificationSent(SCMServiceConstants.SCM_CONSTANT_YES);
				notificationData.setVendorNotificationType(NotificationType.EMAIL.name());
				notificationData.setServiceOrderId(serviceOrderData);
				notificationData = purchaseOrderDao.add(notificationData, true);
				poInvoice.delete();// delete the temp file created on Server
			}
			return notificationData;
		} catch (Exception e) {
			LOG.error(
					"Encountered error while sending PO email notification to Vendor ::::: {}, {} for Dispatch Location",
					vendor.getVendorId(), vendor.getEntityName(), template.getDispatchLocation().getDispatchId(), e);
			throw new PurchaseOrderCreationException("Error while sending email to vendor ::::" + vendor.getVendorId());
		}

	}

	@Override
	public Boolean sendProjectionsNotification(ProductProjectionsEmailNotificationTemplate emailTemplate, File uploadedFile, String[] emailIds) {
		ProductProjectionsEmailNotification emailNotification = new ProductProjectionsEmailNotification(emailTemplate,envProperties.getEnvType(),emailIds);
		try {
			List<AttachmentData> attachments = new ArrayList<>();
			AttachmentData projectionsSheet = null;
			projectionsSheet = new AttachmentData(IOUtils.toByteArray(new FileInputStream(uploadedFile)), uploadedFile.getName(),
					AppConstants.EXCEL_MIME_TYPE);
			attachments.add(projectionsSheet);
			emailNotification.sendRawMail(attachments);
			return true;
		}
		catch (Exception e) {
			LOG.error("Exception Occurred while sending Projections email ::: ",e);
			return false;
		}
	}

	@Override
	public Boolean sendMeasurementBookEmail(MeasurementBookTemplate emailTemplate, File uploadedFile, String employeeName , List<String> toEmails , List<String> ccEmails) {
		MeasurementBookEmailNotification measurementBookEmailNotification = new MeasurementBookEmailNotification(emailTemplate,envProperties.getEnvType(),employeeName,toEmails);
		try {
			List<AttachmentData> attachments = new ArrayList<>();
			AttachmentData measurementBook = null;
			measurementBook = new AttachmentData(IOUtils.toByteArray(new FileInputStream(uploadedFile)), uploadedFile.getName(),
					AppConstants.EXCEL_MIME_TYPE);
			attachments.add(measurementBook);
			measurementBookEmailNotification.SendRawEmailWithCC(attachments,ccEmails.toArray(new String[0]), null);
			return true;
		}
		catch (Exception e) {
			LOG.error("Exception Occurred while sending Measurement email ::: ",e);
			return false;
		}
	}

	@Override
	public void sendSRNotification(SrEmailNotificationTemplate emailTemplate, String[] emails) {
		SrEmailNotification srEmailNotification = new SrEmailNotification(emailTemplate,envProperties.getEnvType(),emails);
		try {
			srEmailNotification.sendEmail();
		} catch (Exception e) {
			LOG.error("Error Occurred While Generating Service Received Mail  ::: ",e);
		}
	}


	@Override
	public void sendPOClosureNotification(PurchaseOrderData po, PurchaseOrderStatus status, boolean isAutoClosure) {
		VendorDetail vendorDetail = scmCache.getVendorDetail(po.getGeneratedForVendor());
		StringBuilder message = new StringBuilder("PURCHASE ORDER CLOSURE NOTIFICATION \n");
		message.append("TYPE OF CLOSURE :: " + (isAutoClosure ? "AUTO" : "MANUAL") + "\n");
		message.append("PURCHASE ORDER STATUS :: " + status.name() + "\n");
		message.append("CLOSED BY :: " + masterDataCache.getEmployee(po.getLastUpdatedBy()) + "\n");
		message.append("VENDOR NAME :: " + vendorDetail.getEntityName() + "\n");
		message.append("AMOUNT :: " + po.getPaidAmount() + "\n");
		sendSlackMessage(message, SlackNotification.SUPPLY_CHAIN);

	}

	@Override
	public void poPendingForApprovalNotification(PurchaseOrderData po) {
		VendorDetail vendorDetail = scmCache.getVendorDetail(po.getGeneratedForVendor());
		StringBuilder message = new StringBuilder("NEW PURCHASE ORDER PENDING FOR APPROVAL\n");
		message.append("CREATED BY :: " + masterDataCache.getEmployee(po.getGeneratedBy()) + "\n");
		message.append("VENDOR NAME :: " + vendorDetail.getEntityName() + "\n");
		message.append("AMOUNT :: " + po.getPaidAmount() + "\n");
		sendSlackMessage(message, SlackNotification.SUPPLY_CHAIN);
	}

	@Override
	public void sendGrRejectionNotification(GoodsReceivedData gr, Map<Integer, GoodsReceivedItemData> rejectedItems) {
		String itemsRejected = rejectedItems.values().stream().map(item -> {
			BigDecimal transferred = item.getTransferredQuantity().setScale(2, BigDecimal.ROUND_HALF_UP);
			BigDecimal amount = SCMUtil.multiply(transferred, item.getNegotiatedUnitPrice()).setScale(2,
					BigDecimal.ROUND_HALF_UP);
			return item.getSkuName() + " Qty ::: " + transferred + item.getUnitOfMeasure() + " of amount Rs. " + amount;
		}).collect(Collectors.joining(", \n"));

		StringBuilder message = new StringBuilder("REJECTION OF GR NOTIFICATION\n");
		message.append("GR ID :: " + gr.getId() + "\n");
		message.append(
				"REJECTION FOR :: " + masterDataCache.getUnitBasicDetail(gr.getGenerationUnitId()).getName() + "\n");
		message.append(
				"REJECTED BY :: " + masterDataCache.getUnitBasicDetail(gr.getGeneratedForUnitId()).getName() + "\n");
		message.append("EMPLOYEE NAME :: " + masterDataCache.getEmployee(gr.getReceivedBy()) + "\n");
		message.append(":: LIST OF ITEMS REJECTED ::\n");
		message.append(itemsRejected + "\n");
		sendSlackMessage(message, SlackNotification.SUPPLY_CHAIN);
	}

	@Override
	public void sendGrQualityCheckNotification(VendorGoodsReceivedData vendorGr) {
		VendorDetail vendorDetail = scmCache.getVendorDetail(vendorGr.getGeneratedForVendor());
		StringBuilder message = new StringBuilder("::::::::::::::ACTION REQUIRED:::::::::::::\n" + "QUALITY CHECK OF GR NOTIFICATION\n");
		message.append("GR ID :: " + vendorGr.getGoodsReceivedId() + "\n");
		message.append("VENDOR NAME :: " +  vendorDetail.getEntityName() + "\n");
		message.append("DELIVERY UNIT :: " +  masterDataCache.getUnitBasicDetail(vendorGr.getDeliveryUnitId()).getName() + "\n");
		message.append(":: PLEASE CHECK THE GR QUALITY ::\n");
		sendSlackMessage(message, SlackNotification.SUPPLY_CHAIN);
	}

	@Override
	public void sendVendorApprovalNotification(VendorDetail vendorDetail) {
		StringBuilder message = new StringBuilder("VENDOR APPROVED \n");
		message.append("ENTITY NAME :: " + vendorDetail.getEntityName() + "\n");
		message.append("BUSINESS TYPE :: " + vendorDetail.getCompanyDetails().getBusinessType() + "\n");
		message.append("CREDIT CYCLE :: " + vendorDetail.getCompanyDetails().getCreditDays() + "\n");
		message.append("APPROVED BY :: " + vendorDetail.getUpdatedBy().getName() + "\n");
		message.append("PRIMARY CONTACT :: " + vendorDetail.getPrimaryContact() + "\n");
		message.append("PRIMARY EMAIL :: " + vendorDetail.getPrimaryEmail() + "\n");
		message.append("ACCOUNT NUMBER :: " + vendorDetail.getAccountDetails().getAccountNumber() + "\n");
		message.append("IFSC CODE :: " + vendorDetail.getAccountDetails().getIfscCode().toUpperCase() + "\n");
		sendSlackMessage(message, SlackNotification.ACCOUNTS);
	}

	@Override
	public void sendVendorCompletionNotification(VendorRegistrationRequestDetail request) {
		StringBuilder message = new StringBuilder("VENDOR COMPLETED REGISTRATION REQUEST\n");
		message.append("VENDOR NAME :: " + request.getVendorName() + "\n");
		sendSlackMessage(message, SlackNotification.ACCOUNTS);
	}

	@Override
	public void sendInventoryErrorNotification(String message) {
		StringBuilder msg = new StringBuilder(
				"::::::::::::::ACTION REQUIRED:::::::::::::\n" + "INVENTORY PRICING ERROR IN SYSTEM\n");
		msg.append(message + "\n");
		sendSlackMessage(msg, SlackNotification.SUPPLY_CHAIN);
	}

	@Override
	public void sendDayClosureNotification(SCMDayCloseEventData dayCloseEvent) {
		UnitBasicDetail unitBasicDetail = masterDataCache.getUnitBasicDetail(dayCloseEvent.getUnitId());
		StringBuilder message = new StringBuilder("DAY CLOSURE NOTIFICATION\n");
		message.append("UNIT NAME :: " + unitBasicDetail.getName() + "\n");
		message.append("CLOSURE STARTED BY :: " + masterDataCache.getEmployee(dayCloseEvent.getCreatedBy()) + "\n");
		message.append("CLOSED BY :: " + masterDataCache.getEmployee(dayCloseEvent.getUpdatedBy()) + "\n");
		message.append("CLOSED AT :: " + dayCloseEvent.getUpdatedAt() + "\n");
		sendSlackMessage(message, SlackNotification.SUPPLY_CHAIN);
	}

	@Override
	public void sendDayClosureAlarmNotification(SCMDayCloseEventData dayCloseEvent, BigDecimal variance) {
		UnitBasicDetail unitBasicDetail = masterDataCache.getUnitBasicDetail(dayCloseEvent.getUnitId());
		StringBuilder message = new StringBuilder("DAY CLOSURE ALARM NOTIFICATION\n");
		message.append("UNIT ID :: " + unitBasicDetail.getId() + "\n");
		message.append("UNIT NAME :: " + unitBasicDetail.getName() + "\n");
		message.append("VARIANCE AT :: " + variance + "\n");
		sendSlackMessage(message, SlackNotification.VARIANCE_REPORT_FAILURES);
	}

	@Override
	public void sendDayClosureAlarmNotification(int unitId, String messsage) {
		UnitBasicDetail unitBasicDetail = masterDataCache.getUnitBasicDetail(unitId);
		StringBuilder message = new StringBuilder("DAY CLOSURE ALARM NOTIFICATION\n");
		message.append("UNIT ID :: " + unitBasicDetail.getId() + "\n");
		message.append("UNIT NAME :: " + unitBasicDetail.getName() + "\n");
		message.append(message + "\n");
		sendSlackMessage(message, SlackNotification.VARIANCE_REPORT_FAILURES);
	}

	@Override
	public void sendInvoiceNotification(SalesPerformaDetailData performa, Integer userId) {
		StringBuilder message = new StringBuilder("INVOICE UPLOAD SUCCESSFUL NOTIFICATION\n");
		message.append("INVOICE ID :: " + performa.getInvoiceId() + "\n");
		message.append("DOCS UPLOADED BY :: " + masterDataCache.getEmployee(userId) + "\n");
		message.append(message + "\n");
		sendSlackMessage(message, SlackNotification.SUPPLY_CHAIN);
	}

	@Override
	public void sendInvoiceErrorNotification(StringBuilder message, SlackNotification channel) {
		sendSlackMessage(message, channel);
	}

	private void sendVendorNotification(boolean enabled, List<VendorDetail> vendorDetails, List<RequestOrder> requestOrderList,
			boolean isMilk, NotificationType type, boolean isReceiving) {
		VendorDetail vd = null;
		try {
			for (VendorDetail vendorDetail : vendorDetails) {
				vd = vendorDetail;
				Map<Integer, List<RequestOrder>> roiMap = getRequestOrdersForNotification(requestOrderList,
						vendorDetail, isMilk);
				if (vendorDetail.getStatus().equals(VendorStatus.ACTIVE) && roiMap.keySet().size() > 0) {
					if (type.equals(NotificationType.EMAIL)
							&& (vendorDetail.getPrimaryEmail() != null || vendorDetail.getSecondaryEmail() != null)) {
						Map<Integer, RequestOrderItem> productQtyMap = getConsolidatedProductQty(roiMap);
						sendVendorEmailNotification(vendorDetail, productQtyMap, roiMap, isReceiving,false);
					} else if (type.equals(NotificationType.SMS) && (vendorDetail.getPrimaryContact() != null
							|| vendorDetail.getSecondaryContact() != null)) {
						sendVendorSMSNotification(enabled, vendorDetail, roiMap, isReceiving);
					}
				}
			}
		} catch (Exception e) {
			StringWriter sw = new StringWriter();
			PrintWriter pw = new PrintWriter(sw);
			e.printStackTrace(pw);
			VendorEmailNotificationFailureTemplate template = new VendorEmailNotificationFailureTemplate(vd,
					envProperties.getBasePath(), isReceiving, sw.toString());
			VendorEmailFailureNotification failureNotification = new VendorEmailFailureNotification(template,
					envProperties.getEnvType());
			try {
				failureNotification.sendEmail();
			} catch (EmailGenerationException e1) {
				e1.printStackTrace();
			}
		}

	}

	private void sendVendorNotification(boolean enabled, List<RequestOrder> requestOrderList, NotificationType type, boolean isScheduled) {
		VendorDetail vd = null;
		try {
			Map<Integer, Map<Integer, List<RequestOrder>>> roiMap = new HashMap<>();
			for (RequestOrder ro : requestOrderList) {
				if (!roiMap.containsKey(ro.getVendorId())) {
					roiMap.put(ro.getVendorId(), new HashMap<>());
				}
				if (!roiMap.get(ro.getVendorId()).containsKey(ro.getFulfillmentUnit().getId())) {
					roiMap.get(ro.getVendorId()).put(ro.getFulfillmentUnit().getId(), new ArrayList<>());
				}
				roiMap.get(ro.getVendorId()).get(ro.getFulfillmentUnit().getId()).add(ro);
			}
			for (Integer vendorId : roiMap.keySet()) {
				VendorDetail vendorDetail = scmCache.getVendorDetail(vendorId);
				if (vendorDetail.getStatus().equals(VendorStatus.ACTIVE) && roiMap.keySet().size() > 0) {
					if (type.equals(NotificationType.EMAIL)
							&& (vendorDetail.getPrimaryEmail() != null || vendorDetail.getSecondaryEmail() != null)) {
						Map<Integer, RequestOrderItem> productQtyMap = getConsolidatedProductQty(roiMap.get(vendorId));
						sendVendorEmailNotification(vendorDetail, productQtyMap, roiMap.get(vendorId), false,isScheduled);
					} else if (type.equals(NotificationType.SMS) && (vendorDetail.getPrimaryContact() != null
							|| vendorDetail.getSecondaryContact() != null)) {
						sendVendorSMSNotification(enabled, vendorDetail, roiMap.get(vendorId), false);
					}
				}
			}
		} catch (Exception e) {
			StringWriter sw = new StringWriter();
			PrintWriter pw = new PrintWriter(sw);
			e.printStackTrace(pw);
			VendorEmailNotificationFailureTemplate template = new VendorEmailNotificationFailureTemplate(vd,
					envProperties.getBasePath(), false, sw.toString());
			VendorEmailFailureNotification failureNotification = new VendorEmailFailureNotification(template,
					envProperties.getEnvType());
			try {
				failureNotification.sendEmail();
			} catch (EmailGenerationException e1) {
				e1.printStackTrace();
			}
		}

	}

	private void sendVendorEmailNotification(VendorDetail vendorDetail, Map<Integer, RequestOrderItem> productQtyMap,
			Map<Integer, List<RequestOrder>> roiMap, boolean isReceiving, boolean isScheduled) {
		EmailNotificationStatus emailNotificationStatus = null;
		try {
			if(isScheduled){
				emailNotificationStatus	 = new EmailNotificationStatus();
				emailNotificationStatus.setNotificationKeyType(NotificationKeyType.VENDOR_RO_EMAIL);
				emailNotificationStatus.setScheduledTime(SCMUtil.getCurrentTimestamp());
				RoEmailMetadata roEmailMetadata = new RoEmailMetadata();
				roEmailMetadata.setVendorId(vendorDetail.getVendorId());
				List<Integer> roIds = new ArrayList<>();
				for(Integer i: roiMap.keySet()){
				   roIds.addAll(roiMap.get(i).stream().map(RequestOrder::getId).toList());
				}
				roEmailMetadata.setRoIds(roIds);
				emailNotificationStatus.setMetadata(roEmailMetadata.toString());
			}
		VendorROEmailNotificationTemplate vendorROEmailNotificationTemplate = new VendorROEmailNotificationTemplate(
				roiMap, masterDataCache.getUnits(), vendorDetail, productQtyMap, envProperties.getBasePath(),
				isReceiving);
		VendorROEmailNotification notification = new VendorROEmailNotification(vendorROEmailNotificationTemplate,
				envProperties.getEnvType(), envProperties.vendorEmailCC());
		SendEmailResult sendEmailResult = notification.sendEmailWithTracking();
			if(emailNotificationStatus!=null){
				emailNotificationStatus.setSchedulerStatus(SchedulerStatus.SUCCESS);
			    emailNotificationStatus.setMessageId(sendEmailResult.getMessageId());
			}
		}catch (Exception e) {
			LOG.error("Error sending vendor notification email: " + vendorDetail.getEntityName(), e);
		   if(isScheduled && emailNotificationStatus!=null){
			   emailNotificationStatus.setSchedulerStatus(SchedulerStatus.FAILED);
		   }
		}finally{
			if(emailNotificationStatus!=null){
				emailNotificationStatusDao.saveAndFlush(emailNotificationStatus);
			}
		}
	}

	private void sendVendorSMSNotification(boolean enabled, VendorDetail vendorDetail, Map<Integer, List<RequestOrder>> roiMap,
			boolean isReceiving) {
		for (Integer unitId : roiMap.keySet()) {
			for (RequestOrder requestOrder : roiMap.get(unitId)) {
				try {
					VendorROSMSNotification notification = VendorROSMSNotification.VENDOR_RO_CREATE_NOTIFICATION;
					if (isReceiving) {
						notification = VendorROSMSNotification.VENDOR_RO_RECEIVING_NOTIFICATION;
					}
					String message = notification.getMessage(requestOrder);
					if (enabled && vendorDetail.getPrimaryContact() != null && AppConstants.YES.equalsIgnoreCase(masterDataCache.getCacheReferenceMetadata(CacheReferenceType.SEND_SMS_FROM_SUMO))) {
						notificationService.sendNotification(notification.name(), message,
								vendorDetail.getPrimaryContact(), providerService.getSMSClient(
										notification.getTemplate().getSMSType(), ApplicationName.SCM_SERVICE),
								true, null);
					}
					if (enabled && vendorDetail.getSecondaryContact() != null && AppConstants.YES.equalsIgnoreCase(masterDataCache.getCacheReferenceMetadata(CacheReferenceType.SEND_SMS_FROM_SUMO))) {
						notificationService.sendNotification(notification.name(), message,
								vendorDetail.getSecondaryContact(), providerService.getSMSClient(
										notification.getTemplate().getSMSType(), ApplicationName.SCM_SERVICE),
								true,null);
					}
					LOG.info(String.format("Publishing vendor SMS notification to slack for vendor %s for RO %d",
							vendorDetail.getEntityName(), requestOrder.getId()));
					if (isReceiving) {
						notification = VendorROSMSNotification.VENDOR_RO_RECEIVING_SLACK_NOTIFICATION;
						message = notification.getMessage(requestOrder);
					}
					SlackNotificationService.getInstance().sendNotification(envProperties.getEnvType(), "Kettle",
							SlackNotification.VENDOR_ORDERING, message);
				} catch (IOException | JMSException e) {
					LOG.error("Error while sending the OTP message to " + vendorDetail.getPrimaryContact()
							+ " for RO Id: " + requestOrder.getId(), e);
				}
			}
		}
	}

	private Map<Integer, List<RequestOrder>> getRequestOrdersForNotification(List<RequestOrder> requestOrderList,
			VendorDetail vendorDetail, boolean isMilk) {
		Map<Integer, List<RequestOrder>> roiMap = new HashMap<>();
		for (RequestOrder requestOrder : requestOrderList) {
			List<RequestOrder> orderList = roiMap.get(requestOrder.getRequestUnit().getId());
			if (orderList == null) {
				orderList = new ArrayList<>();
			}
			RequestOrder data = getRequestOrder(requestOrder);
			List<RequestOrderItem> requestOrderItemList = new ArrayList<>();
			for (RequestOrderItem requestOrderItem : requestOrder.getRequestOrderItems()) {
				if (requestOrderItem.getVendor().getId() == vendorDetail.getVendorId()) {
					if (isMilk) {
						if (requestOrderItem.getProductId() == 100234) {
							requestOrderItemList.add(requestOrderItem);
						}
					} else {
						if (requestOrderItem.getProductId() != 100234) {
							requestOrderItemList.add(requestOrderItem);
						}
					}
				}
			}
			if (!requestOrderItemList.isEmpty()) {
				data.getRequestOrderItems().addAll(requestOrderItemList);
				orderList.add(data);
				roiMap.put(requestOrder.getRequestUnit().getId(), orderList);
			}
		}
		return roiMap;
	}

	private Map<Integer, RequestOrderItem> getConsolidatedProductQty(Map<Integer, List<RequestOrder>> roiMap) {
		Map<Integer, RequestOrderItem> productQtyMap = new HashMap<>();
		for (Integer unitId : roiMap.keySet()) {
			for (RequestOrder requestOrder : roiMap.get(unitId)) {
				for (RequestOrderItem requestOrderItem : requestOrder.getRequestOrderItems()) {
					RequestOrderItem roi = productQtyMap.get(requestOrderItem.getProductId());
					if (roi == null) {
						roi = new RequestOrderItem();
						roi.setProductId(requestOrderItem.getProductId());
						roi.setProductName(requestOrderItem.getProductName());
						roi.setUnitOfMeasure(requestOrderItem.getUnitOfMeasure());
						roi.setNegotiatedUnitPrice(requestOrderItem.getNegotiatedUnitPrice());
						roi.setRequestedAbsoluteQuantity(0.0f);
						roi.setReceivedQuantity(0.0f);
					}
					roi.setRequestedAbsoluteQuantity(
							roi.getRequestedAbsoluteQuantity() + requestOrderItem.getRequestedAbsoluteQuantity());
					if (requestOrderItem.getReceivedQuantity() != null) {
						roi.setReceivedQuantity(roi.getReceivedQuantity() + requestOrderItem.getReceivedQuantity());
					}
					productQtyMap.put(requestOrderItem.getProductId(), roi);
				}
			}
		}
		return productQtyMap;
	}

	private RequestOrder getRequestOrder(RequestOrder requestOrder) {
		RequestOrder order = new RequestOrder();
		order.setId(requestOrder.getId());
		order.setGenerationTime(requestOrder.getGenerationTime());
		order.setLastUpdateTime(requestOrder.getLastUpdateTime());
		order.setRequestUnit(requestOrder.getRequestUnit());
		order.setStatus(requestOrder.getStatus());
		return order;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.core.service.SCMNotificationService#
	 * sendAssetOrderNotification(com.stpl.tech.scm.domain.model.RequestOrder)
	 */
	@Override
	public void sendAssetOrderNotification(RequestOrder ro) {
		AssetOrderNotificationTemplate template = new AssetOrderNotificationTemplate(ro, envProperties.getBasePath());
		AssetOrderNotification notification = new AssetOrderNotification(template, envProperties.getEnvType(),
				ro.getRequestUnit().getName());
		try {
			notification.sendEmail();
			SlackNotificationService.getInstance().sendNotification(envProperties.getEnvType(), "Kettle",
					SlackNotification.ASSET_ORDERING, getAssetOrderMessage(ro));
		} catch (Exception e) {
			LOG.error("Error sending asset order notification email or slack for: " + ro.getId(), e);
		}
	}

	@Override
	public void sendAssetLostNotification(List<AssetRecoveryDefinition> assetRecoveryDefinitions, List<String> emails) {
		AssetLostNotificationTemplate template = new AssetLostNotificationTemplate(assetRecoveryDefinitions,
				envProperties.getBasePath()) ;
		AssetLostNotification notification = new AssetLostNotification(template, envProperties.getEnvType(),
				assetRecoveryDefinitions, emails);
		try{
			notification.sendEmail();
		} catch (Exception e){
			LOG.error("Error sending asset Lost notification email  " + assetRecoveryDefinitions.get(0).getUnitId(), e);
		}
	}

	@Override
	public void sendLostAssetNotification(LostAssetEmailObject lostAsset, List<String> emails, Boolean isInsuranceRecovery) {
		if (isInsuranceRecovery) {
			lostAsset.setExpectedRecoveryAmount(null);
		}else{
			lostAsset.setGrId(null);
			lostAsset.setPrId(null);
			lostAsset.setPoId(null);
		}
		LostAssetNotificationTemplate template = new LostAssetNotificationTemplate(lostAsset,
				envProperties.getBasePath()) ;
		LostAssetNotification notification = new LostAssetNotification(masterDataCache ,lostAsset, template , emails, envProperties.getEnvType(),
				 isInsuranceRecovery);
		try{
			notification.sendEmail();
		} catch (Exception e){
			LOG.error("Error sending Lost Asset notification email  " + lostAsset.getUnitId(), e);
		}
	}


	private String getAssetOrderMessage(RequestOrder ro) {

		StringBuffer b = new StringBuffer();
		b.append("Requesting Unit : " + ro.getRequestUnit().getName());
		b.append("\nFulfillment Unit : " + ro.getFulfillmentUnit().getName());
		b.append("\nFulfillment Date : " + AppUtils.getFormattedDate(ro.getFulfillmentDate()));
		b.append("\nGenerated By: " + ro.getGeneratedBy().getName());
		b.append("\nComment: " + ro.getComment());
		b.append("\nRequested Order Count: " + ro.getRequestOrderItems().size());
		for (RequestOrderItem item : ro.getRequestOrderItems()) {
			b.append("\n\t" + item.getProductName() + "\t" + item.getRequestedAbsoluteQuantity() + "\t"
					+ item.getUnitOfMeasure());
		}
		return b.toString();
	}

	private void sendSlackMessage(StringBuilder message, SlackNotification notification) {
		message.append("--------------------------------------------------------");
		SlackNotificationService.getInstance().sendNotification(envProperties.getEnvType(), "SUMO", notification,
				message.toString());
	}

	@Override
	public PurchaseOrderNotificationData sendClosedPONotification(PurchaseOrderData purchaseOrderData,
			VendorDetail vendor, VendorClosedPOEmailNotificationTemplate template, File poInvoice) throws PurchaseOrderCreationException {
		VendorClosedPOEmailNotification emailNotification = new VendorClosedPOEmailNotification(template,
				envProperties.getEnvType(), envProperties.vendorPurchaseEmailCC(),
				purchaseOrderData.getFulfillmentDate());
		try {
			List<AttachmentData> attachments = new ArrayList<>();
			AttachmentData invoiceDetail = null;
			invoiceDetail = new AttachmentData(IOUtils.toByteArray(new FileInputStream(poInvoice)), poInvoice.getName(),
					"application/pdf");
			attachments.add(invoiceDetail);
			emailNotification.sendRawMail(attachments);

			PurchaseOrderNotificationData notificationData = new PurchaseOrderNotificationData();
			notificationData.setMessage(String.format(
					"CLOSED PURCHASE PURCHASE_ORDER NOTIFICATION FROM CHAAYOS TO %s", vendor.getEntityName()));
			notificationData.setNotificationCarrier("Amazon");
			notificationData.setServiceClient("AWS-SES");
			notificationData.setContact(template.getDispatchLocation().getContactEmail());
			notificationData.setNotificationSent(SCMServiceConstants.SCM_CONSTANT_YES);
			notificationData.setVendorNotificationType(NotificationType.EMAIL.name());
			notificationData.setPurchaseOrderId(purchaseOrderData);
			notificationData.setNotificationTime(SCMUtil.getCurrentTimestamp());
			notificationData = purchaseOrderDao.add(notificationData, true);
			poInvoice.delete();// delete the temp file created on Server
			return notificationData;
		} catch (Exception e) {
			LOG.error(
					"Encountered error while sending PO email notification to Vendor ::::: {}, {} for Dispatch Location",
					vendor.getVendorId(), vendor.getEntityName(), template.getDispatchLocation().getDispatchId(), e);
			throw new PurchaseOrderCreationException("Error while sending email to vendor ::::" + vendor.getVendorId());
		}

	}

	@Override
	public boolean sendTDSCertificateToVendor(String vendorName, String quarter, String financialYear,
											  String[] vendorEmailList, File tdsCertificateFile
											  ) throws SumoException {

		TDSEmailTemplate emailTemplate = new TDSEmailTemplate(envProperties.getBasePath(), vendorName, quarter, financialYear);
		VendorTDSEmailNotification vendorTDSEmailNotification = new VendorTDSEmailNotification(emailTemplate,
				envProperties.getEnvType(), Arrays.asList(vendorEmailList), quarter, financialYear, vendorName);

		try {
			List<AttachmentData> attachments = new ArrayList<>();
			AttachmentData attachmentFile;
			String[] ccList;
			if(SCMUtil.isDev(envProperties.getEnvType())) {
				ccList = new String[]{"<EMAIL>"};
			}else {
				ccList = new String[]{"<EMAIL>"};
			}
			attachmentFile = new AttachmentData(IOUtils.toByteArray(new FileInputStream(tdsCertificateFile)),
					tdsCertificateFile.getName(), "application/pdf");
			attachments.add(attachmentFile);
			vendorTDSEmailNotification.SendRawEmailWithCC(attachments, ccList, null);

//			notificationData = purchaseOrderDao.add(notificationData, true);
//			poInvoice.delete();// delete the temp file created on
			tdsCertificateFile.delete();
			return true;
		} catch (Exception e) {
			LOG.error(
					"Encountered error while sending TDS email notification to Vendor ::::: {}, {} ",
					vendorName, financialYear, e);
			throw new SumoException("Error while sending email TDS certificate to vendor ::::");
		}

	}

	@Override
	public void setRejectedGrNotification(RejectedGREmailNotificationTemplate rejectedGREmailNotificationTemplate) {
		RejectedGREmailNotification rejectedGREmailNotification = new RejectedGREmailNotification(rejectedGREmailNotificationTemplate,envProperties.getEnvType());
		try {
			rejectedGREmailNotification.sendEmail();
		}
		catch (Exception e) {
			LOG.error("Error encountered while sending Rejected Gr Mail :: ",e);
		}
	}


	@Override
	public void sendAssetCacheFailureNotification(AssetDefinitionData assetDefinition,String error) {
		StringBuilder message = new StringBuilder("ASSET CACHE ERROR NOTIFICATION\n");
		message.append("ASSET ID :: " + assetDefinition.getAssetId() + "\n");
		message.append("ASSET NAME :: " + assetDefinition.getAssetName() + "\n");
		message.append("UNIT :: " + assetDefinition.getUnitId() + "\n");
		message.append("ERROR :: " + error + "\n");
		sendSlackMessage(message, SlackNotification.SUPPLY_CHAIN);
	}
}

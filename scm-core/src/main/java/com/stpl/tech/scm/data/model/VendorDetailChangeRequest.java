package com.stpl.tech.scm.data.model;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "VENDOR_DETAIL_CHANGE_REQUEST")
public class VendorDetailChangeRequest implements java.io.Serializable{


    private Integer vendorDetailChangeRequestId;
    private Integer vendorId;
    private List<VendorDetailChangeRequestData> vendorDetailChangeRequestDataList = new ArrayList<>();

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "VENDOR_DETAIL_CHANGE_REQUEST_ID", unique = true, nullable = false)
    public Integer getVendorDetailChangeRequestId() {
        return vendorDetailChangeRequestId;
    }

    public void setVendorDetailChangeRequestId(Integer vendorDetailChangeRequestId) {
        this.vendorDetailChangeRequestId = vendorDetailChangeRequestId;
    }

    @Column(name = "VENDOR_ID",nullable = false)
    public Integer getVendorId() {
        return vendorId;
    }

    public void setVendorId(Integer vendorId) {
        this.vendorId = vendorId;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "vendorDetailChangeRequest")
    public List<VendorDetailChangeRequestData> getVendorDetailChangeRequestDataList() {
        return vendorDetailChangeRequestDataList;
    }

    public void setVendorDetailChangeRequestDataList(List<VendorDetailChangeRequestData> vendorDetailChangeRequestDataList) {
        this.vendorDetailChangeRequestDataList = vendorDetailChangeRequestDataList;
    }
}

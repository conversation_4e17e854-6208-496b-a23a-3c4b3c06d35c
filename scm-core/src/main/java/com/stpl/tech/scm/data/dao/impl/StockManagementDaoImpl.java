/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.kettle.core.data.budget.vo.ConsumablesAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.InventoryAggregate;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.TaxDataCache;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.tax.model.TaxData;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.ThresholdType;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.model.BookingConsumptionDataVO;
import com.stpl.tech.scm.core.util.model.ProductionBookingDataVO;
import com.stpl.tech.scm.core.util.model.StrategyType;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.StockManagementDao;
import com.stpl.tech.scm.data.model.ApprovalDetailData;
import com.stpl.tech.scm.data.model.BookingConsumptionData;
import com.stpl.tech.scm.data.model.ConsumableStockState;
import com.stpl.tech.scm.data.model.CostDetailData;
import com.stpl.tech.scm.data.model.DayCloseEventLogData;
import com.stpl.tech.scm.data.model.DayCloseInventoryDrillDown;
import com.stpl.tech.scm.data.model.DayCloseProductPackagingMappings;
import com.stpl.tech.scm.data.model.DayCloseTxnEventDrillDownData;
import com.stpl.tech.scm.data.model.DayCloseTxnEventMapping;
import com.stpl.tech.scm.data.model.GatepassItemData;
import com.stpl.tech.scm.data.model.GoodsReceivedData;
import com.stpl.tech.scm.data.model.GoodsReceivedItemData;
import com.stpl.tech.scm.data.model.MonkWastageDetailData;
import com.stpl.tech.scm.data.model.ProductDefinitionData;
import com.stpl.tech.scm.data.model.ProductionBookingData;
import com.stpl.tech.scm.data.model.RegularOrderingEvent;
import com.stpl.tech.scm.data.model.StockTakeSumoDayCloseEvent;
import com.stpl.tech.scm.data.model.StockTakeSumoDayCloseProducts;
import com.stpl.tech.scm.data.model.SuggestiveOrderingStrategyMetadata;
import com.stpl.tech.scm.data.model.VarianceAcknowledgementData;
import com.stpl.tech.scm.data.model.RequestOrderData;
import com.stpl.tech.scm.data.model.ReverseProductionBookingData;
import com.stpl.tech.scm.data.model.SCMDayCloseEventData;
import com.stpl.tech.scm.data.model.SCMDayCloseEventRangeData;
import com.stpl.tech.scm.data.model.SCMProductConsumptionData;
import com.stpl.tech.scm.data.model.SCMProductInventoryData;
import com.stpl.tech.scm.data.model.SCMWastageData;
import com.stpl.tech.scm.data.model.SCMWastageEventData;
import com.stpl.tech.scm.data.model.SalesPerformaDetailData;
import com.stpl.tech.scm.data.model.SkuDefinitionData;
import com.stpl.tech.scm.data.model.StockEntryEventData;
import com.stpl.tech.scm.data.model.StockEventCalendarData;
import com.stpl.tech.scm.data.model.TransferOrderData;
import com.stpl.tech.scm.data.model.TransferOrderItemData;
import com.stpl.tech.scm.data.model.UnitOrderScheduleData;
import com.stpl.tech.scm.data.model.VendorGoodsReceivedData;
import com.stpl.tech.scm.data.model.WastageDataDrilldown;
import com.stpl.tech.scm.data.model.WastageLimitLookup;
import com.stpl.tech.scm.domain.model.ApprovalType;
import com.stpl.tech.scm.domain.model.BookingStatus;
import com.stpl.tech.scm.domain.model.DayCloseEventLogType;
import com.stpl.tech.scm.domain.model.GatepassStatus;
import com.stpl.tech.scm.domain.model.InventoryItemDrilldown;
import com.stpl.tech.scm.domain.model.MonkWastageDetail;
import com.stpl.tech.scm.domain.model.OrderTransferType;
import com.stpl.tech.scm.domain.model.PriceUpdateEntryType;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.PurchaseOrderStatus;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.SalesPerformaStatus;
import com.stpl.tech.scm.domain.model.ScmProductConsumptionDTO;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.StockCalendarEventStatus;
import com.stpl.tech.scm.domain.model.StockEventStatus;
import com.stpl.tech.scm.domain.model.StockEventStatusType;
import com.stpl.tech.scm.domain.model.StockEventType;
import com.stpl.tech.scm.domain.model.StockTakeSubType;
import com.stpl.tech.scm.domain.model.StockTakeSumoDayCloseStatus;
import com.stpl.tech.scm.domain.model.StockTakeType;
import com.stpl.tech.scm.domain.model.SwitchStatus;
import com.stpl.tech.scm.domain.model.TransferOrderType;
import com.stpl.tech.scm.domain.model.VarianceType;
import com.stpl.tech.scm.domain.model.WastageData;
import com.stpl.tech.scm.domain.model.WastageEvent;
import com.stpl.tech.scm.reports.modal.CafeUnsettledTOModal;
import com.stpl.tech.scm.reports.modal.CafeWastageModal;
import com.stpl.tech.scm.reports.modal.VarianceModal;
import com.stpl.tech.scm.reports.modal.VarianceSummaryModal;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository
public class StockManagementDaoImpl extends SCMAbstractDaoImpl implements StockManagementDao {

	private static final Logger LOG = LoggerFactory.getLogger(StockManagementDaoImpl.class);

	@Autowired
	private MasterDataCache masterCache;

	@Autowired
	private SCMCache scmCache;

	@Autowired
	private TaxDataCache taxCache;
	@Autowired
	private EnvProperties env;

	@Override
	public List<SCMWastageEventData> getAllWastageEventsForUnit(int unit, Date businessDate) {
		LOG.info("Got params :::: {} :::: {}", unit, businessDate);
		Query query = manager.createQuery(
				"SELECT E FROM SCMWastageEventData E where E.unitId = :unitId and E.businessDate = :businessDate and E.status <> :status");
		query.setParameter("unitId", unit).setParameter("businessDate", AppUtils.getDate(businessDate))
				.setParameter("status", StockEventStatus.CANCELLED.toString());
		return (List<SCMWastageEventData>) query.getResultList();
	}

	@Override
	public SCMWastageEventData addWastageEvent(WastageEvent wastageEvent) {
		try {
			SCMWastageEventData wastageData = convert(wastageEvent);
			wastageData = add(wastageData, true);
			if (wastageEvent.getItems() != null) {
				addAllItems(wastageData, wastageEvent);
			}
			return wastageData;
		} catch (Exception e) {
			LOG.error("Could not add event ::::", e);
			return null;
		}
	}

	private SCMWastageEventData convert(WastageEvent wastage) {
		SCMWastageEventData scmWastageEventData = new SCMWastageEventData();
		scmWastageEventData.setBusinessDate(SCMUtil.getBusinessDate());
		scmWastageEventData.setGenerationTime(SCMUtil.getCurrentTimestamp());
		scmWastageEventData.setUnitId(wastage.getUnitId());
		scmWastageEventData.setStatus(wastage.getStatus().toString());
		scmWastageEventData.setGeneratedBy(wastage.getGeneratedBy());
		scmWastageEventData.setLinkedGRId(wastage.getLinkedGrId());
		scmWastageEventData.setLinkedRefId(wastage.getLinkedKettleId());
		scmWastageEventData.setLinkedRefIdType(wastage.getLinkedKettleIdType());
		scmWastageEventData.setGrReason(wastage.getGrReason());
		scmWastageEventData.setKettleReason(wastage.getKettleReason());
		scmWastageEventData.setType(wastage.getType().name());
		scmWastageEventData.setItems(new ArrayList<>());
		return scmWastageEventData;
	}

	@Override
	public List<Integer> getMonkCalibrationExcludedProductIdsList() {
		try {
			String productIds = env.getMonkCalibrationExcludedProductIdsList();
			if (Objects.nonNull(productIds) && !productIds.equalsIgnoreCase("")) {
				return Arrays.stream(productIds.split(",")).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList());
			} else {
				return new ArrayList<>();
			}
		} catch (Exception e) {
			LOG.error("Exception Occurred While Getting getComplimentaryCodesList ::: ", e);
			return new ArrayList<>();
		}
	}

	private void addAllItems(SCMWastageEventData wastage, WastageEvent event) throws SumoException {
		List<Integer> ids = new ArrayList<>();
		if (event.getItems().get(0).getSkuId() != null) {
			event.getItems().forEach((cost) -> {
				ids.add(cost.getSkuId());
			});
		} else {
			event.getItems().forEach((cost) -> {
				ids.add(cost.getProductId());
			});
		}
		for (WastageData data : event.getItems()) {
			if(SCMUtil.MONK_CALIBRATION.equals(data.getComment()) && getMonkCalibrationExcludedProductIdsList().contains(data.getProductId())) continue;
			SCMWastageData w = new SCMWastageData();
			if (data.getWasteDrillDown() != null && !data.getWasteDrillDown().isEmpty()) {
				// aggregate qty in case there is a monk drill down list available
				w.setQuantity(getQuantity(data));
			} else {
				w.setQuantity(data.getQuantity());
			}

			w.setComment(data.getComment());
			if (Objects.nonNull(data.getEnteredComment())) {
				w.setEnteredComment(data.getEnteredComment());
			}
			ProductDefinitionData product;
			if (data.getSkuId() != null) {
				SkuDefinitionData sku = manager.find(SkuDefinitionData.class, data.getSkuId());
				product = manager.find(ProductDefinitionData.class, sku.getLinkedProduct().getProductId());

				w.setSku(sku);
			} else {
				product = manager.find(ProductDefinitionData.class, data.getProductId());
			}
			w.setProduct(product);
			w.setCost(AppUtils.multiply(w.getPrice(), w.getQuantity()));
			w.setWastage(wastage);
			int stateId = masterCache.getUnit(wastage.getUnitId()).getLocation().getState().getId();
			String hsnCode = product.getTaxCategoryCode();
			TaxData taxData = taxCache.getTaxData(stateId, hsnCode);
			BigDecimal igstTaxPercentage = taxData.getState().getIgst();
			BigDecimal igstTax = AppUtils.percentOfWithScale10(w.getCost(), igstTaxPercentage);
			w.setTaxType("IGST");
			w.setTaxPercentage(igstTaxPercentage);
			w.setTax(igstTax);

			w = add(w, false);

			for (InventoryItemDrilldown dd : data.getDrillDowns()) {
				WastageDataDrilldown wdd = SCMDataConverter.convertToWastageItemDrillDown(dd, w);
				add(wdd, false);
				w.getItems().add(wdd);
			}

			// to add monk wastage drill downs
			if (data.getWasteDrillDown() != null && !data.getWasteDrillDown().isEmpty()) {
				for (MonkWastageDetail monkWastageDetail : data.getWasteDrillDown()) {
					MonkWastageDetailData mdd = SCMDataConverter.convertToMonkWastageDetail(monkWastageDetail, w);
					add(mdd, false);
				}
			}

			wastage.getItems().add(w);
		}
		flush();

	}

	private BigDecimal getQuantity(WastageData data) {
		double qty = data.getWasteDrillDown().stream()
				.map(MonkWastageDetail::getQuantity)
				.reduce(0d, (qty1, qty2) -> qty1 + qty2);
		return qty > 0 ? SCMUtil.convertToBigDecimal(qty) : data.getQuantity();
	}

	@Override
	public SCMDayCloseEventData getLastDayCloseEvent(int unit, StockEventType eventType, boolean fetchNonAutoDayClose) {
		return getLastDayCloseEvent(unit, eventType, null, fetchNonAutoDayClose);
	}

	@Override
	public SCMDayCloseEventData getLastDayCloseEventOfType(int unit, StockTakeType stockTakeType,
														   StockEventType eventType) {
		return getLastDayCloseEventOfType(unit, stockTakeType, eventType, null);
	}



	public SCMDayCloseEventData getLastDayCloseEventOfType(int unit, StockTakeType stockTakeType,
														   StockEventType eventType, Date businessDate) {
		SCMDayCloseEventData returnEvent = null;
		List<String> stockTypes = Collections.singletonList(stockTakeType.name());

		// to get monthly event in case the month has changed
		if (stockTakeType.equals(StockTakeType.WEEKLY)) {
			stockTypes = Arrays.asList(StockTakeType.WEEKLY.name(), StockTakeType.MONTHLY.name());
		}

		try {
			String queryString = "SELECT E FROM SCMDayCloseEventData E where E.unitId = :unitId "
					+ " and E.status = :status and E.dayCloseEventType = :type "
					+ " and E.eventFrequencyType in (:stockTakeType)";

			if (businessDate != null) {
				queryString = queryString + " and E.businessDate < :businessDate ";
			}
			queryString = queryString + " ORDER BY E.businessDate DESC ";

			Query query = manager.createQuery(queryString);
			query.setParameter("unitId", unit).setParameter("status", StockEventStatus.CLOSED.toString())
					.setParameter("type", eventType.toString()).setParameter("stockTakeType", stockTypes);
			if (businessDate != null) {
				query.setParameter("businessDate", businessDate);
			}
			query.setMaxResults(1);
			returnEvent = (SCMDayCloseEventData) query.getSingleResult();
		} catch (Exception e) {
			LOG.error("Could not find last day close event for unit :::: {} of type ::: {} ", unit, stockTakeType);
		}
		return returnEvent;
	}

	@Override
	public SCMDayCloseEventData getLastDayCloseEventOfTypeOnBusinessDate(int unit, StockTakeType stockTakeType,
																		 StockEventType eventType, Date businessDate) {
		SCMDayCloseEventData returnEvent = null;
		List<String> stockTypes = Collections.singletonList(stockTakeType.name());
		// to get monthly event in case the month has changed
		if (stockTakeType.equals(StockTakeType.WEEKLY)) {
			stockTypes = Arrays.asList(StockTakeType.WEEKLY.name(), StockTakeType.MONTHLY.name());
		}
		try {
			String queryString = "SELECT E FROM SCMDayCloseEventData E where E.unitId = :unitId "
					+ " and E.status = :status and E.dayCloseEventType = :type "
					+ " and E.eventFrequencyType in (:stockTakeType)";
			queryString = queryString + " and E.businessDate = :businessDate ";
			queryString = queryString + " ORDER BY E.businessDate DESC ";

			Query query = manager.createQuery(queryString);
			query.setParameter("unitId", unit).setParameter("status", StockEventStatus.CLOSED.toString())
					.setParameter("type", eventType.toString()).setParameter("stockTakeType", stockTypes);
			query.setParameter("businessDate", businessDate);
			query.setMaxResults(1);
			returnEvent = (SCMDayCloseEventData) query.getSingleResult();
		} catch (Exception e) {
			LOG.error("Could not find last day close event for unit :::: {} of type ::: {} ", unit, stockTakeType);
		}
		return returnEvent;
	}

	@Override
	public Boolean checkInventoryLoaded(Integer unit, Date businessDate, StockTakeType stockType) {
		try {
			List<String> stockTakeTypes = null;
			if (stockType == null) {
				stockTakeTypes = Arrays.asList(StockTakeType.DAILY.name(), StockTakeType.MONTHLY.name());
			} else {
				stockTakeTypes = Arrays.asList(stockType.name());
			}
			SCMDayCloseEventData dayCloseEventData = getDayCloseEventData(unit, StockEventStatus.INITIATED,
					StockEventType.STOCK_TAKE, stockTakeTypes);
			boolean flag = false;
			if (dayCloseEventData != null) {
				flag = SCMUtil.checkDate(businessDate, dayCloseEventData.getBusinessDate());
			}
			return flag;
		} catch (Exception e) {
			LOG.error("Could not find last day close event for unit ::: {}", unit, e);
			return false;
		}
	}

	@Override
	public Boolean checkStockUpdated(Integer unit, Date businessDate, StockTakeType stockType) {
		try {
			List<String> stockTakeTypes = null;
			if (stockType == null) {
				stockTakeTypes = Arrays.asList(StockTakeType.DAILY.name(), StockTakeType.MONTHLY.name());
			} else {
				stockTakeTypes = Arrays.asList(stockType.name());
			}
			SCMDayCloseEventData dayCloseEventData = getDayCloseEventData(unit, StockEventStatus.CLOSED,
					StockEventType.STOCK_TAKE, stockTakeTypes);
			boolean flag = false;
			if (dayCloseEventData != null) {
				flag = SCMUtil.checkDate(businessDate, dayCloseEventData.getBusinessDate());
			}
			return flag;
		} catch (Exception e) {
			LOG.error("Could not find last day close event for unit ::: {}", unit, e);
			return false;
		}
	}

	@Override
	public List<StockEventCalendarData> checkPendingCalendarEventByType(Integer unitId, StockTakeType type, Date businessDate) {
		Query query = manager.createQuery("SELECT s FROM StockEventCalendarData s WHERE s.scheduledDate <= :businessDate AND " +
				"s.status = :pendingStatus and s.unitId = :unitId and s.eventFrequencyType = :type");
		query.setParameter("businessDate", businessDate);
		query.setParameter("pendingStatus", StockCalendarEventStatus.PENDING.name());
		query.setParameter("unitId", unitId);
		query.setParameter("type", type.name());
		return query.getResultList();
	}

	@Override
	public boolean closePendingCalendarEventsByType(Integer unitId, StockTakeType type, Date businessDate) {
		StringBuilder queryStr = new StringBuilder("UPDATE StockEventCalendarData");
		queryStr.append(" SET status = :closedStatus , eventTime = :eventTime");
		queryStr.append(" WHERE scheduledDate <= :businessDate AND status = :pendingStatus and unitId = :unitId");
		Query query = manager.createQuery(queryStr.toString());
		Date eventTime = SCMUtil.getCurrentTimestamp();
		query.setParameter("businessDate", businessDate);
		query.setParameter("pendingStatus", StockCalendarEventStatus.PENDING.name());
		query.setParameter("closedStatus", StockCalendarEventStatus.CLOSED.name());
		query.setParameter("eventTime", eventTime);
		query.setParameter("unitId", unitId);
		query.executeUpdate();
		return true;
	}

	@Override
	public List<StockEventCalendarData> getPendingCalendarEventForUnit(Integer unitId, Date businessDate) {
		Query query = manager.createQuery("SELECT s FROM StockEventCalendarData s WHERE s.scheduledDate < :businessDate AND " +
				"s.status = :pendingStatus and s.unitId = :unitId order by s.eventId DESC");
		query.setParameter("businessDate", businessDate);
		query.setParameter("pendingStatus", StockCalendarEventStatus.PENDING.name());
		query.setParameter("unitId", unitId);
		return query.getResultList();
	}

	@Override
	public List<StockEventCalendarData> getPendingCalendarEvent(Integer unitId, Date businessDate) {
		Query query = manager.createQuery("SELECT s FROM StockEventCalendarData s WHERE s.scheduledDate <=:businessDate AND " +
				"s.status = :pendingStatus and s.unitId = :unitId order by s.eventId DESC");
		query.setParameter("businessDate", businessDate);
		query.setParameter("pendingStatus", StockCalendarEventStatus.PENDING.name());
		query.setParameter("unitId", unitId);
		return query.getResultList();
	}

	@Override
	public List<StockEventCalendarData> getPlannedCalendarEventByType(Integer unitId, StockTakeType type, Date businessDate) {
		Query query = manager.createQuery("SELECT s FROM StockEventCalendarData s WHERE s.scheduledDate > :businessDate AND " +
				"s.status = :pendingStatus and s.unitId = :unitId and s.eventFrequencyType = :type order by s.eventId DESC");
		query.setParameter("businessDate", businessDate);
		query.setParameter("pendingStatus", StockCalendarEventStatus.PENDING.name());
		query.setParameter("unitId", unitId);
		query.setParameter("type", type.name());
		return query.getResultList();
	}

	/*
	 * @Override public List<SCMProductInventoryData> getSettledInventory(int unit,
	 * StockTakeType frequency) { try{
	 *
	 * SCMDayCloseEventData lastDayCloseEvent =
	 * frequency.equals(StockTakeType.DAILY) ?
	 * getLastDayCloseEvent(unit,StockEventType.CLOSING) :
	 * getLastDayCloseEventOfType(unit, frequency,StockEventType.CLOSING); String
	 * queryString = "SELECT E FROM SCMProductInventoryData E "+
	 * "WHERE E.unitId = :unitId and E.eventType = :eventType and E.currentDayCloseEvent = :eventId"
	 * ; Query query = manager.createQuery(queryString) .setParameter("unitId",
	 * unit) .setParameter("eventType", frequency.toString())
	 * .setParameter("eventId", lastDayCloseEvent.getEventId()); return
	 * (List<SCMProductInventoryData>) query.getResultList(); } catch (Exception e)
	 * { LOG.error(
	 * "No inventory data found for the unit for this business date for unit ::: {}"
	 * , unit); return null; } }
	 */

	@Override
	public SCMDayCloseEventData getInitiatedDayCloseEvents(Integer unit, Date businessDate) {
		try {
			String queryString = "SELECT E FROM SCMDayCloseEventData E "
					+ "WHERE E.businessDate = :businessDate and E.status = :status and E.unitId = :unitId and E.dayCloseEventType = :eventType";
			Query query = manager.createQuery(queryString).setParameter("unitId", unit)
					.setParameter("eventType", StockEventType.CLOSING.toString())
					.setParameter("status", StockEventStatus.INITIATED.toString())
					.setParameter("businessDate", SCMUtil.getDate(businessDate));
			return (SCMDayCloseEventData) query.getSingleResult();
		} catch (NoResultException nre) {
			LOG.info("No day close event found for the unit {} for business date {}", unit, businessDate);
		} catch (Exception e) {
			LOG.error("ERROR in day close event found for unit {} for business date {}", unit, businessDate, e);
		}
		return null;
	}

	@Override
	public List<Pair<StockTakeType, Integer>> getLatestClosureEvents(Integer unit, SCMDayCloseEventData lastClosingEvent, boolean getStockTakeEvents) {
		List<Pair<StockTakeType, Integer>> result = new ArrayList<>();
		try {
			StringBuilder queryString = new StringBuilder("SELECT E.eventFrequencyType, max(E.eventId) FROM SCMDayCloseEventData E WHERE E.unitId = :unitId and E.status = :status ");
			if (Objects.nonNull(lastClosingEvent)) {
				queryString.append("and E.eventId <= :lastClosingEventId ");
			}
			if (getStockTakeEvents) {
				queryString.append(" and E.dayCloseEventType IN (:dayCloseEventTypes) and E.generationTime >= :generationTime ");
			}
			queryString.append("group by E.eventFrequencyType");
			Query query = manager.createQuery(queryString.toString()).setParameter("unitId", unit).setParameter("status",
					StockEventStatus.CLOSED.toString());
			if (Objects.nonNull(lastClosingEvent)) {
				query.setParameter("lastClosingEventId", lastClosingEvent.getEventId());
			}
			if (getStockTakeEvents) {
				query.setParameter("dayCloseEventTypes", Arrays.asList(StockEventType.STOCK_TAKE.value(), StockEventType.OPENING.value()));
				query.setParameter("generationTime", AppUtils.getDayBeforeOrAfterDay(AppUtils.getCurrentDate(), -31));
			}
			List<Object[]> list = query.getResultList();
			if (list == null) {
				LOG.error("No events found for the unit {} ", unit);
				return result;
			} else {
				for (Object[] array : list) {
					result.add(new Pair<>(StockTakeType.valueOf((String) array[0]), (Integer) array[1]));
				}
			}
			return result;
		} catch (Exception e) {
			LOG.error("No events found for the unit {} ", unit, e);
		}
		return result;
	}

	/*
	 * @Override public List<SCMProductInventoryData>
	 * getOpeningStockValues(SCMDayCloseEventData openingEvent) {
	 *
	 * try {
	 *
	 * String queryString = "SELECT E FROM SCMProductInventoryData E " +
	 * "WHERE E.unitId = :unitId and E.status = :status and E.currentDayCloseEvent = :eventId"
	 * ; Query query = manager.createQuery(queryString).setParameter("unitId",
	 * openingEvent.getUnitId()) .setParameter("status",
	 * StockEventStatus.CLOSED.toString()) .setParameter("eventId",
	 * openingEvent.getEventId()); return (List<SCMProductInventoryData>)
	 * query.getResultList();
	 *
	 * } catch (Exception e) { LOG.error("Found some exception", e); return null; }
	 * }
	 *
	 *
	 * @Override public SCMDayCloseEventData getCurrentDayCloseEvent(int unit) { try
	 * { return getDayCloseEventData(unit, StockEventStatus.INITIATED,
	 * StockEventType.CLOSING); } catch (Exception e) { LOG.error(
	 * "Could not find current day close event :::", e); return null; } }
	 *
	 * @Override public List<SCMProductConsumptionData>
	 * getProductConsumptionData(int eventId) { Query query = manager.createQuery(
	 * "SELECT E FROM SCMProductConsumptionData E where E.eventId.eventId= :eventId"
	 * ) .setParameter("eventId", eventId); return (List<SCMProductConsumptionData>)
	 * query.getResultList(); }
	 */

	@Override
	public SCMDayCloseEventData createConsumptionEvent(StockEventType eventType, int unitId, StockTakeType frequency,
													   StockEventStatus eventStatus, Date businessDate, Integer dayClosureId) {

		SCMDayCloseEventData event = new SCMDayCloseEventData();
		event.setGenerationTime(SCMUtil.getCurrentTimestamp());
		event.setStatus(eventStatus.toString());
		event.setBusinessDate(businessDate);
		event.setDayCloseEventType(eventType.toString());
		event.setEventFrequencyType(frequency.toString());
		event.setUnitId(unitId);
		event.setDayClosureId(dayClosureId);
		try {
			add(event, true);
		} catch (Exception e) {
			LOG.error("Exception occurred while inserting event ::::::: ", e);
			return null;
		}
		return event;
	}

	@Override
	public SCMDayCloseEventRangeData createDayCloseEventRecord(SCMDayCloseEventData event, int startId, int endId,
															   StockEventType eventType, StockTakeType frequency) {
		SCMDayCloseEventRangeData eventRangeData = new SCMDayCloseEventRangeData();
		eventRangeData.setEventId(event);
		eventRangeData.setStartId(startId);
		eventRangeData.setEndId(endId);
		eventRangeData.setType(eventType.toString());
		eventRangeData.setStockType(frequency.name());
		try {
			add(eventRangeData, true);
		} catch (Exception e) {
			LOG.error("Got error while persisting event range data for :::: {} ::: {}", event.getEventId(), eventType,
					e);
			return null;
		}
		return eventRangeData;
	}

	@Override
	public SCMDayCloseEventData getOpeningEventForUnit(int unitId) {
		try {
			LOG.info("Checking SUMO Opening Event for unit {}", unitId);
			return getDayCloseEventData(unitId, StockEventStatus.CLOSED, StockEventType.OPENING,
					Arrays.asList(StockTakeType.ALL.name()));
		} catch (Exception e) {
			LOG.error("No opening event found for the unit:::");
			return null;
		}
	}

	@Override
	public SCMDayCloseEventData checkClosingInitiated(int unit, Date businessDate) {
		try {
			String queryString = "SELECT E FROM SCMDayCloseEventData E "
					+ "WHERE E.dayCloseEventType=:eventType and E.unitId = :unitId and E.status = :status and E.businessDate = :date "
					+ "ORDER BY E.businessDate DESC";
			Query query = manager.createQuery(queryString).setParameter("unitId", unit)
					.setParameter("eventType", StockEventType.CLOSING.name())
					.setParameter("status", StockEventStatus.INITIATED.name())
					.setParameter("date", SCMUtil.getDate(businessDate)).setMaxResults(1);
			return (SCMDayCloseEventData) query.getSingleResult();

		} catch (Exception e) {
			LOG.error("No opening event found for the unit:::");
			return null;
		}
	}

	@Override
	public SCMDayCloseEventData checkLastClosingInitiated(int unit, Date lastOperationDate) {
		try {
			String queryString = "SELECT E FROM SCMDayCloseEventData E "
					+ "WHERE E.dayCloseEventType=:eventType and E.unitId = :unitId and E.status = :status and E.businessDate >= :date "
					+ "ORDER BY E.businessDate DESC";
			Query query = manager.createQuery(queryString).setParameter("unitId", unit)
					.setParameter("eventType", StockEventType.CLOSING.name())
					.setParameter("status", StockEventStatus.INITIATED.name())
					.setParameter("date", SCMUtil.getDate(lastOperationDate))
					.setMaxResults(1);
			return (SCMDayCloseEventData) query.getSingleResult();

		} catch (Exception e) {
			LOG.error("No opening event found for the unit:::");
			return null;
		}
	}

	@Override
	public boolean cancelDayCloseForUnit(int unitId, int closureId) {
		boolean flag = false;
		try {
			StringBuilder queryStr = new StringBuilder("UPDATE SCMDayCloseEventData");
			queryStr.append(" SET status = :status");
			queryStr.append(" WHERE unitId = :unitId AND dayClosureId= :closureId AND status = :eventStatus");
			queryStr.append(" AND eventFrequencyType = :eventFrequencyType AND dayCloseEventType = :eventType");
			Query query = manager.createQuery(queryStr.toString())
					.setParameter("status", StockEventStatus.CANCELLED.name())
					.setParameter("unitId", unitId)
					.setParameter("closureId", closureId)
					.setParameter("eventStatus", StockEventStatus.INITIATED.name())
					.setParameter("eventFrequencyType", StockTakeType.DAILY.name())
					.setParameter("eventType", StockEventType.CLOSING.name());
			query.executeUpdate();
			flag = true;
		} catch (Exception e) {
			LOG.error("Error encountered  while cancelling all previous day close events", e);
		}
		return flag;
	}

	@Override
	public StockEventCalendarData addStockCalendarEvent(Integer unitId, StockTakeType stockType, Date nextDate)
			throws SumoException {

		StockEventCalendarData stockEventCalendarData = new StockEventCalendarData();
		stockEventCalendarData.setCreationTime(SCMUtil.getCurrentTimestamp());
		stockEventCalendarData.setEventFrequencyType(stockType.name());
		stockEventCalendarData.setScheduledDate(nextDate);
		stockEventCalendarData.setStatus(StockCalendarEventStatus.PENDING.name());
		stockEventCalendarData.setUnitId(unitId);
		return add(stockEventCalendarData, true);

	}

	@Override
	public List<TransferOrderItemData> getTransferList(int unit, Date businessDate, Date lastBusinessDate,
													   List<String> statusList, boolean excludeSpecilizedOrders, List<Integer> currentProductIds) {
		try {
			StringBuilder queryString = new StringBuilder("SELECT distinct F FROM TransferOrderItemData F, TransferOrderData E "
					+ " WHERE F.transferOrderData.id = E.id and E.generationUnitId = :unitId and E.generationTime >= :startTime and E.generationTime <= :endTime "
					+ " AND E.status in (:status)");

			if (excludeSpecilizedOrders) {
				queryString.append(" AND E.generationUnitId <> E.generatedForUnitId ");
			}
			queryString.append(" ORDER BY F.transferOrderData.id ASC");
			StringBuilder newQuery = null;
			if (Objects.nonNull(currentProductIds)) {
				newQuery = new StringBuilder("SELECT distinct F FROM TransferOrderItemData F, TransferOrderData E, SkuDefinitionData s"
						+ " WHERE F.transferOrderData.id = E.id and F.skuId = s.skuId and s.linkedProduct.productId IN(:currentProductIds) and E.generationUnitId = :unitId " +
						" and E.generationTime >= :startTime and E.generationTime <= :endTime "
						+ " AND E.status in (:status)");
				if (excludeSpecilizedOrders) {
					newQuery.append(" AND E.generationUnitId <> E.generatedForUnitId ");
				}
				newQuery.append(" ORDER BY F.transferOrderData.id ASC");
			}

			Query query = manager.createQuery(Objects.nonNull(currentProductIds) ? newQuery.toString(): queryString.toString()).setParameter("unitId", unit)
					.setParameter("status", statusList)
					.setParameter("startTime", lastBusinessDate)
					.setParameter("endTime", businessDate);
			if (Objects.nonNull(currentProductIds)) {
				query.setParameter("currentProductIds", currentProductIds);
			}
			return (List<TransferOrderItemData>) query.getResultList();

		} catch (Exception e) {
			LOG.error("No transfer event found for the unit for this business date:::", e);
			return null;
		}
	}

	@Override
	public List<GoodsReceivedItemData> getReceivingList(int unit, Date businessDate, Date lastBusinessDate, List<Integer> currentProductIds) {
		try {
			StringBuilder queryString = new StringBuilder();
			if (Objects.nonNull(currentProductIds)) {
				queryString.append("SELECT distinct F FROM GoodsReceivedData E, GoodsReceivedItemData F, SkuDefinitionData s "
						+ "WHERE E.id = F.goodsReceivedData.id and E.generatedForUnitId = :unitId and F.skuId = s.skuId and s.linkedProduct.productId IN(:currentProductIds)" +
						" and E.lastUpdateTime >= :startTime "
						+ "and E.lastUpdateTime <= :endTime " + "and E.status = :status ORDER BY F.goodsReceivedData.id ASC");
			} else {
				queryString.append("SELECT distinct F FROM GoodsReceivedData E, GoodsReceivedItemData F "
						+ "WHERE E.id = F.goodsReceivedData.id and E.generatedForUnitId = :unitId and E.lastUpdateTime >= :startTime "
						+ "and E.lastUpdateTime <= :endTime " + "and E.status = :status ORDER BY F.goodsReceivedData.id ASC");
			}
			Query query = manager.createQuery(queryString.toString()).setParameter("unitId", unit)
					.setParameter("status", SCMOrderStatus.SETTLED.toString())
					.setParameter("startTime", lastBusinessDate)
					.setParameter("endTime", businessDate);
			if (Objects.nonNull(currentProductIds)) {
				query.setParameter("currentProductIds", currentProductIds);
			}
			return (List<GoodsReceivedItemData>) query.getResultList();
		} catch (Exception e) {
			LOG.error("No receiving event found for the unit for this business date ::: ", e);
			return null;
		}
	}

	@Override
	public List<SCMWastageData> getWastageEventList(int unit, Date businessDate, Date lastBusinessDate, List<Integer> currentProductIds) {
		try {
			StringBuilder queryString = new StringBuilder("SELECT distinct F FROM SCMWastageEventData E, SCMWastageData F "
					+ "WHERE E.id = F.wastage.id and E.unitId = :unitId and E.generationTime <= :businessDate "
					+ "and E.generationTime > :lastBusinessDate and E.status = :status ");
			if (Objects.nonNull(currentProductIds)) {
				queryString.append(" and F.product.productId IN(:currentProductIds) ");
			}
			queryString.append("ORDER BY F.wastage.id ASC");
			Query query = manager.createQuery(queryString.toString()).setParameter("unitId", unit)
					.setParameter("status", StockEventStatus.SETTLED.toString())
					.setParameter("businessDate", businessDate)
					.setParameter("lastBusinessDate", lastBusinessDate);
			if (Objects.nonNull(currentProductIds)) {
				query.setParameter("currentProductIds", currentProductIds);
			}
			return (List<SCMWastageData>) query.getResultList();
		} catch (Exception e) {
			LOG.error("No wastage event found for the unit for this business date ::: ", e);
			return null;
		}
	}

	@Override
	public List<SCMWastageData> getWastageEventListByDayCloseEventRangeData(int unitId, Date businessDate,
																			StockTakeType type) {

		Integer eventId = getDayCloseEvent(unitId, businessDate, type);
		SCMDayCloseEventRangeData range = getSCMDayCloseEventRangeData(eventId, StockEventType.WASTAGE, type);

		if (range == null) {
			return new ArrayList<SCMWastageData>();
		}

		try {
			String queryString = "SELECT F FROM SCMWastageEventData E, SCMWastageData F "
					+ "WHERE E.id = F.wastage.id and E.unitId = :unitId and E.wastageId <= :endId "
					+ "and E.wastageId >= :startId and E.status = :status ORDER BY E.wastageId ASC";
			Query query = manager.createQuery(queryString).setParameter("unitId", unitId)
					.setParameter("status", StockEventStatus.SETTLED.toString())
					.setParameter("startId", range.getStartId()).setParameter("endId", range.getEndId());
			return (List<SCMWastageData>) query.getResultList();
		} catch (Exception e) {
			LOG.error("No wastage event found for the unit for this business date ::: ", e);
			return new ArrayList<SCMWastageData>();
		}
	}

	@Override
	public Integer getDayCloseEvent(int unitId, Date businessDate, StockTakeType type) {
		if (SCMUtil.isMonthly(type)) {
			return getMonthlySCMDayCloseEventForBusinessDate(unitId, businessDate);
		} else {
			return getSCMDayCloseEventForBusinessDate(unitId, businessDate);
		}
	}

	@Override
	public List<SCMDayCloseEventData> getInitiatedStockEvents(Integer unitId) {
		try {
			String queryString = "FROM SCMDayCloseEventData E WHERE dayCloseEventType = :eventType " +
					"AND E.status = :status AND E.businessDate = :businessDate AND E.generationTime <=:oneHourBeforeTime";

			if (unitId != null) {
				queryString += " AND E.unitId=:unitId";
			}

			Query query = manager.createQuery(queryString)
					.setParameter("eventType", StockEventType.STOCK_TAKE.name())
					.setParameter("status", StockEventStatus.INITIATED.name())
					.setParameter("businessDate", SCMUtil.getCurrentBusinessDate())
					.setParameter("oneHourBeforeTime", SCMUtil.getDateBeforeOrAfterInSeconds(AppUtils.getCurrentTimestamp(),-3600));

			if (unitId != null) {
				query.setParameter("unitId", unitId);
			}

			return (List<SCMDayCloseEventData>) query.getResultList();
		} catch (Exception e) {
			LOG.error("No initiated stock events found", e);
			return null;
		}
	}

	private Integer getMonthlySCMDayCloseEventForBusinessDate(int unitId, Date businessDate) {

		List<String> frequencyList = new ArrayList<>();
		frequencyList.add(StockTakeType.MONTHLY.name());
		try {
			String queryString = "SELECT eventId FROM SCMDayCloseEventData F "
					+ "WHERE F.dayCloseEventType  = :dayCloseEventType "
					+ "and F.status = :status and F.businessDate >= :startDate AND F.businessDate <= :endDate "
					+ " AND F.eventFrequencyType IN :frequencyList" + " AND F.unitId = :unitId";
			Query query = manager.createQuery(queryString)
					.setParameter("dayCloseEventType", StockEventType.STOCK_TAKE.name())
					.setParameter("status", StockEventStatus.CLOSED.name())
					.setParameter("startDate", SCMUtil.getMonthlyCheckDate(businessDate))
					.setParameter("endDate", SCMUtil.getMonthlyCheckDateEnd(businessDate))
					.setParameter("unitId", unitId).setParameter("frequencyList", frequencyList);

			return (Integer) query.getSingleResult();
		} catch (Exception e) {
			LOG.error("Could not find any MONTHLY Day closing event for the date ::::: {}", businessDate, e);
		}
		return null;
	}


	@Override
	public SCMDayCloseEventRangeData getTransfersRangeData(SCMDayCloseEventData event, StockTakeType stockTakeType) {
		return getSCMDayCloseEventRangeData(event.getEventId(), StockEventType.TRANSFER_OUT, stockTakeType);
	}

	@Override
	public void updateTransfers(SCMDayCloseEventData stockEvent, int startId, int endId, StockTakeType stockTakeType) {
		try {
			List<String> statusList = Arrays.asList(
					SCMOrderStatus.CREATED.toString(),
					SCMOrderStatus.TRANSFERRED.toString(),
					SCMOrderStatus.SETTLED.toString()
			);
			StringBuilder queryStr = new StringBuilder("UPDATE TransferOrderData SET closureEventId=:closureEventId ");
			queryStr.append(" WHERE closureEventId IS NULL AND generationUnitId = :unitId ")
					.append(" AND id BETWEEN :startOrderId AND :endOrderId ")
					.append(" AND status IN (:statusList) ")
					.append(" AND generationUnitId <> generatedForUnitId");
			Query query = manager.createQuery(queryStr.toString())
					.setParameter("closureEventId", stockEvent.getEventId())
					.setParameter("unitId", stockEvent.getUnitId())
					.setParameter("startOrderId", startId)
					.setParameter("endOrderId", endId)
					.setParameter("statusList", statusList);
			query.executeUpdate();
		} catch (Exception e) {
			LOG.error("Error while updating transfers with closure event ID ::: {}", stockEvent.getEventId(), e);
		}
	}

	private SCMDayCloseEventRangeData getSCMDayCloseEventRangeData(Integer eventId, StockEventType type,
																   StockTakeType frequency) {
		try {
			String queryString = "SELECT E FROM SCMDayCloseEventRangeData E where E.eventId.eventId = :eventId"
					+ " and E.type = :type and E.stockType = :frequency";
			Query query = manager.createQuery(queryString);
			query.setParameter("eventId", eventId).setParameter("type", type.name())
					.setParameter("frequency", frequency.name()).setMaxResults(1);
			return (SCMDayCloseEventRangeData) query.getSingleResult();
		} catch (NoResultException nre) {
			LOG.info("No Range data for event Id: {}", eventId);
		}
		return null;
	}

	private SCMDayCloseEventData getDayCloseEventData(int unit, StockEventStatus status, StockEventType type,
													  List<String> stockTakeTypes) {
		String queryString = "SELECT E FROM SCMDayCloseEventData E where E.unitId = :unitId and E.status = :status"
				+ " and E.eventFrequencyType in :types and E.dayCloseEventType = :type ORDER BY E.businessDate DESC";
		Query query = manager.createQuery(queryString);
		query.setParameter("unitId", unit).setParameter("status", status.toString())
				.setParameter("type", type.toString()).setParameter("types", stockTakeTypes).setMaxResults(1);
		return (SCMDayCloseEventData) query.getSingleResult();
	}

	@Override
	public Map<Integer, BigDecimal> getCurrentStock(Integer unit) {
		Map<Integer, BigDecimal> map = new HashMap<>();
		List<Pair<StockTakeType, Integer>> events = getLatestClosureEvents(unit, null, false);
		List<SCMDayCloseEventData> eventIds = new ArrayList<>();
		if (events == null || events.size() == 0) {
			return map;
		}
		for (Pair<StockTakeType, Integer> event : events) {
			eventIds.add(manager.find(SCMDayCloseEventData.class, event.getValue()));
		}
		try {
			String queryString = "SELECT * FROM (SELECT s.* FROM STOCK_INVENTORY s, PRODUCT_DEFINITION p WHERE s.PRODUCT_ID = p.PRODUCT_ID " +
					"AND s.STOCK_TYPE=p.STOCK_KEEPING_FREQUENCY AND s.CURRENT_EVENT_ID IN (:eventIds) " +
					"GROUP BY s.CURRENT_EVENT_ID,s.PRODUCT_ID ORDER BY s.CURRENT_EVENT_ID DESC) A GROUP BY A.PRODUCT_ID";
			Query query = manager.createNativeQuery(queryString, SCMProductInventoryData.class).setParameter("eventIds", eventIds);
			List<SCMProductInventoryData> list = (List<SCMProductInventoryData>) query.getResultList();
			if (list != null && list.size() > 0) {
				for (SCMProductInventoryData value : list) {
					map.put(value.getProductId(), value.getActualClosing());
				}
			}
		} catch (Exception e) {
			LOG.error("No inventory data found for the unit for this business date:::", e);
		}
		return map;
	}

	@Override
	public Map<Integer, Pair<SCMDayCloseEventData, BigDecimal>> getClosingStockWithDayCloseId(Integer unit, SCMDayCloseEventData lastClosingEvent) {
		Map<Integer, Pair<SCMDayCloseEventData, BigDecimal>> map = new HashMap<>();
		List<Pair<StockTakeType, Integer>> events = getLatestClosureEvents(unit, null, true);
		List<Integer> eventIds = new ArrayList<>();
		if (events == null || events.size() == 0) {
			return map;
		}
		for (Pair<StockTakeType, Integer> event : events) {
			eventIds.add(event.getValue());
		}
		try {
			LOG.info("Last Successful day close events are :: {}", Arrays.asList(eventIds.toArray()));
			String queryString = "SELECT s.PRODUCT_ID, MIN(s.STOCKING_ID) FROM STOCK_INVENTORY s INNER JOIN DAY_CLOSE_EVENT dce " +
					"ON s.CURRENT_EVENT_ID = dce.EVENT_ID and s.CURRENT_EVENT_ID IN (:eventIds) and dce.CLOSURE_EVENT_FREQUENCY = s.STOCK_TYPE" +
					" GROUP BY s.PRODUCT_ID";
			Query query = manager.createNativeQuery(queryString).setParameter("eventIds", eventIds);
			List<Object[]> list = query.getResultList();
			if (list != null && list.size() > 0) {
				for (Object[] detail : list) {
					SCMProductInventoryData scmProductInventoryData = find(SCMProductInventoryData.class, (Integer)detail[1]);
					map.put((Integer) detail[0], new Pair<>(scmProductInventoryData.getCurrentDayCloseEvent(), scmProductInventoryData.getActualClosing()));
				}
			}
		} catch (Exception e) {
			LOG.error("No Current Stock with day close id :::", e);
		}
		return map;
	}

	@Override
	public SCMDayCloseEventData createStockTakeEvent(StockEventType eventType, int unitId, Date currentBusinessDate,
													 StockTakeType stockType, Integer generatedBy) {
		SCMDayCloseEventData dayCloseEventData = new SCMDayCloseEventData();
		StockEventStatus status = eventType.equals(StockEventType.OPENING) ? StockEventStatus.CLOSED
				: StockEventStatus.INITIATED;
		StockTakeType stockTakeType = eventType.equals(StockEventType.OPENING) ? StockTakeType.ALL : stockType;
		dayCloseEventData.setBusinessDate(SCMUtil.getDate(currentBusinessDate));
		dayCloseEventData.setUnitId(unitId);
		dayCloseEventData.setDayCloseEventType(eventType.toString());
		dayCloseEventData.setStatus(status.toString());
		dayCloseEventData.setGenerationTime(SCMUtil.getCurrentTimestamp());
		dayCloseEventData.setDayClosureId(getClosureIdForStock(unitId, currentBusinessDate));
		dayCloseEventData.setEventFrequencyType(stockTakeType.name());
		dayCloseEventData.setCreatedBy(generatedBy);
		try {
			add(dayCloseEventData, true);
			return dayCloseEventData;
		} catch (Exception e) {
			LOG.error("Got error while persisting stock take entry for unit {}", unitId, e);
			return null;
		}
	}

	private Integer getClosureIdForStock(int unitId, Date currentBusinessDate) {
		try {
			SCMDayCloseEventData dayCloseEventData = getInitiatedDayCloseEvents(unitId, currentBusinessDate);
			return dayCloseEventData.getDayClosureId();
		} catch (Exception e) {
			LOG.error("Error found while getting initiated event for unit {}", unitId, e);
			return null;
		}
	}

	@Override
	public Map<Integer, BigDecimal> getStockForUnit(int unitId, int eventId) {
		try {
			String queryString = "SELECT si FROM StockEntryEventData si WHERE si.unitId = :unitId and si.updateEventId = :eventId";
			Query query = manager.createQuery(queryString).setParameter("unitId", unitId).setParameter("eventId",
					eventId);
			List<StockEntryEventData> list = (List<StockEntryEventData>) query.getResultList();
			if (list != null && list.size() > 0) {
				Map<Integer, BigDecimal> map = new HashMap<>();
				for (StockEntryEventData value : list) {
					map.put(value.getProductId(), value.getCurrentStock());
				}
				return map;
			} else {
				return getCurrentStock(unitId);
			}
		} catch (Exception e) {
			LOG.error("No inventory data found for the unit for this business date:::", e);
		}
		return null;
	}

	@Override
	public SCMDayCloseEventData getStockEventForUnit(int unitId, Integer dayClosureId, List<String> stockTakeTypes) {
		try {
			String queryString = "SELECT si FROM SCMDayCloseEventData si"
					+ " WHERE si.unitId = :unitId and si.dayCloseEventType = :eventType"
					+ (dayClosureId != null ? " AND si.dayClosureId = :dayClosureId" : "")
					+ " AND si.eventFrequencyType in :eventFrequencyType AND si.status = :status ORDER BY si.eventId DESC ";
			Query query = manager.createQuery(queryString).setParameter("unitId", unitId);
			if (dayClosureId != null) {
				query.setParameter("dayClosureId", dayClosureId);
			}
			query.setParameter("eventType", StockEventType.STOCK_TAKE.name())
					.setParameter("eventFrequencyType", stockTakeTypes)
					.setParameter("status", StockEventStatus.INITIATED.name())
					.setMaxResults(1);
			return (SCMDayCloseEventData) query.getSingleResult();
		} catch (Exception e) {
			LOG.error("No inventory data found for the unit for current business date in INITIATED state::: {}", unitId, e);
		}
		return null;
	}

	@Override
	public List<SCMProductConsumptionData> getConsumptionForBusinessDate(int unitId, Date businessDate,
																		 Date previousDate, List<Integer> currentProductIds) {
		try {

			StringBuilder queryString = new StringBuilder("SELECT distinct E FROM SCMProductConsumptionData E, SCMDayCloseEventData F "
					+ "WHERE E.unitId = :unitId and E.businessDate > :lastDayCloseDate and E.businessDate <= :currentDayCloseDate "
					+ "and E.eventId.eventId = F.eventId and F.dayCloseEventType = :closing and F.status <> :status ");
			if (Objects.nonNull(currentProductIds)) {
				queryString.append("and E.productId IN(:currentProductIds) ");
			}
			queryString.append("ORDER BY E.productConsumptionId ASC");
			Query query = manager.createQuery(queryString.toString()).setParameter("unitId", unitId)
					.setParameter("lastDayCloseDate", SCMUtil.getDate(previousDate))
					.setParameter("currentDayCloseDate", SCMUtil.getDate(businessDate))
					.setParameter("closing", StockEventType.CLOSING.toString())
					.setParameter("status", StockEventStatus.CANCELLED.toString());
			if (Objects.nonNull(currentProductIds)) {
				query.setParameter("currentProductIds", currentProductIds);
			}
			return (List<SCMProductConsumptionData>) query.getResultList();
		} catch (Exception e) {
			LOG.error("Found some while getting consumption for the business date for unit ::::: {}", unitId, e);
			return null;
		}

	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.data.dao.StockManagementDao#getConsumption(int)
	 */
	@Override
	public List<SCMProductConsumptionData> getConsumption(int event) {
		try {

			String queryString = "SELECT E FROM SCMProductConsumptionData E " + "WHERE E.eventId.eventId = :eventId";
			Query query = manager.createQuery(queryString).setParameter("eventId", event);
			return (List<SCMProductConsumptionData>) query.getResultList();
		} catch (Exception e) {
			LOG.error("Found some while getting consumption for the business date for event ::::: {}", event, e);
			return null;
		}
	}

	/**************************
	 * Warehouse stock management related functions here
	 ***********************************/

	@Override
	public SCMDayCloseEventData getActiveDayCloseEvent(int unitId) {
		List<String> statusList = Arrays.asList(StockEventStatus.INITIATED.toString());
		return getWarehouseDayClosureEvent(unitId, statusList);
	}

	@Override
	public List<Integer> getMonthlySkuList(int unitId) {
		try {
			String queryString = "SELECT T.SKU_ID from (SELECT D.SKU_ID,MAX(D.VARIANCE) VARIANCE FROM INVENTORY_DRILLDOWN D "
					+ "INNER JOIN STOCK_ENTRY SE ON SE.SKU_ID = D.SKU_ID "
					+ "WHERE SE.UPDATE_EVENT_ID = (SELECT EVENT_ID FROM DAY_CLOSE_EVENT WHERE UNIT_ID=:unitId "
					+ "AND CLOSURE_EVENT_TYPE = :eventType AND STATUS = :status)\n"
					+ "GROUP BY D.SKU_ID ORDER BY VARIANCE DESC) as T";
			Query query = manager.createNativeQuery(queryString).setParameter("unitId", unitId)
					.setParameter("eventType", StockEventType.WH_OPENING.name())
					.setParameter("status", StockEventStatus.INITIATED.name()).setMaxResults(100);
			return (List<Integer>) query.getResultList();
		} catch (Exception e) {
			LOG.error("Could not find any highly variant products for the unitId ::::: {}", unitId);
			return new ArrayList<>();
		}
	}

	private SCMDayCloseEventData getWarehouseDayClosureEvent(int unitId, List<String> statusList) {
		try {
			String queryString = "SELECT F FROM SCMDayCloseEventData F "
					+ "WHERE F.unitId = :unitId and F.dayCloseEventType  = :closing "
					+ "and F.status in :statusList order by F.eventId desc";
			Query query = manager.createQuery(queryString).setParameter("unitId", unitId)
					.setParameter("closing", StockEventType.WH_CLOSING.toString())
					.setParameter("statusList", statusList)
					.setMaxResults(1);
			return (SCMDayCloseEventData) query.getSingleResult();
		} catch (Exception e) {
			LOG.error("Could not find any active warehouse closing event for the unit ::::: {}", unitId);
			return null;
		}
	}

	@Override
	public List<SCMWastageEventData> getWastagesForWHUnit(int unitId, List<Integer> skuList) {
		try {
			StringBuilder queryString = new StringBuilder(
					"SELECT DISTINCT E FROM SCMWastageEventData E, SCMWastageData F");
			queryString.append(
					" WHERE E.wastageId = F.wastage.wastageId AND E.unitId = :unitId AND E.closureEventId IS NULL");
			if (skuList != null && skuList.size() > 0) {
				queryString.append(" and F.sku.skuId in (:skuList)");
			}
			queryString.append(" and E.status = :status ORDER BY E.wastageId ASC");
			Query query = manager.createQuery(queryString.toString()).setParameter("unitId", unitId)
					.setParameter("status", StockEventStatus.SETTLED.toString());
			if (skuList != null && skuList.size() > 0) {
				query.setParameter("skuList", skuList);
			}
			List<SCMWastageEventData> wastageList = (List<SCMWastageEventData>) query.getResultList();
			LOG.info("Found waste list for WH Unit :::::: " + wastageList.size());
			return wastageList;
		} catch (Exception e) {
			LOG.error("No wastage event found for the unit for this business date ::: ", e);
			return null;
		}

	}

	@Override
	public List<TransferOrderData> getTransferItemsForWHUnit(int unitId, List<Integer> skuList) {
		try {
			String[] statusList = new String[]{SCMOrderStatus.CREATED.toString(),
					SCMOrderStatus.TRANSFERRED.toString(), SCMOrderStatus.SETTLED.toString()};
			StringBuilder queryString = new StringBuilder(
					"SELECT DISTINCT E FROM TransferOrderData E, TransferOrderItemData F");
			queryString.append(" WHERE E.id=F.transferOrderData.id and E.toType=:toType");
			if (skuList != null) {
				queryString.append(" and F.skuId in (:skuList)");
			}
			queryString.append(
					" and E.generationUnitId = :unitId and E.closureEventId is null and E.status in (:status) ORDER BY E.id ASC");
			Query query = manager.createQuery(queryString.toString()).setParameter("unitId", unitId)
					.setParameter("status", Arrays.asList(statusList)).setParameter("toType", TransferOrderType.REGULAR_TRANSFER.value());
			if (skuList != null) {
				query.setParameter("skuList", skuList);
			}
			List<TransferOrderData> transfers = (List<TransferOrderData>) query.getResultList();
			LOG.info("Found transfers for WH Unit :::::: " + transfers.size());
			return transfers;
		} catch (Exception e) {
			LOG.error("No transfer event found for the unit for this business date:::", e);
			return null;
		}
	}

	@Override
	public List<GatepassItemData> getGatepassItemsForWHUnit(int unitId, List<Integer> skuList, String transType) {
		try {
			StringBuilder queryString = new StringBuilder("SELECT DISTINCT F FROM GatepassData E, GatepassItemData F");
			queryString.append(" WHERE E.id=F.gatepassData.id and F.transType = :transType ");
			if (skuList != null) {
				queryString.append(" and F.skuId in (:skuList)");
			}
			queryString.append(
					" and E.sendingUnit = :unitId and F.closureId is null and E.status <> :status");
			Query query = manager.createQuery(queryString.toString()).setParameter("unitId", unitId)
					.setParameter("status", GatepassStatus.CANCELLED.name());
			query.setParameter("transType", transType);
			if (skuList != null) {
				query.setParameter("skuList", skuList);
			}
			List<GatepassItemData> gatepasses = (List<GatepassItemData>) query.getResultList();
			LOG.info("Found gatepasses for WH Unit :::::: " + gatepasses.size());
			return gatepasses;
		} catch (Exception e) {
			LOG.error("No gatepass event found for the unit for this business date:::", e);
			return null;
		}
	}

	@Override
	public DayCloseEventLogData findByClosureEvent(int eventId, DayCloseEventLogType type) {
		try {
			Query query = manager.createQuery("FROM DayCloseEventLogData E WHERE E.dayCloseEventData.eventId=:eventId "
					+ "AND E.eventCompleted=:eventType AND E.status=:status");
			query.setParameter("eventId", eventId).setParameter("eventType", type.name()).setParameter("status",
					SwitchStatus.ACTIVE.name());
			return (DayCloseEventLogData) query.getSingleResult();
		} catch (NoResultException nre) {
			LOG.error("No event log of type {} found for event {}", type.name(), eventId);
			return null;
		}
	}

	@Override
	public List<SalesPerformaDetailData> getInvoicesForWhUnit(int unitId, List<Integer> skuList) {
		ArrayList<String> statusList = new ArrayList<>();
		statusList.add(GatepassStatus.CLOSED.name());
		statusList.add(SalesPerformaStatus.DELIVERED.name());
		statusList.add(SalesPerformaStatus.CORRECTION_APPROVAL.name());
		statusList.add(SalesPerformaStatus.DELIVERED_WITH_CORRECTION.name());
		try {
			StringBuilder queryString = new StringBuilder(
					"SELECT DISTINCT E FROM SalesPerformaDetailData E, SalesPerformaInvoiceItemData F");
			queryString.append(" WHERE E.invoiceId=F.invoice.invoiceId ");
			if (skuList != null) {
				queryString.append(" and F.skuId in (:skuList)");
			}
			queryString.append(
					" and E.sendingUnit = :unitId and E.closureId is null and E.status in(:status) ORDER BY E.invoiceId ASC");
			Query query = manager.createQuery(queryString.toString()).setParameter("unitId", unitId)
					.setParameter("status", statusList);
			if (skuList != null) {
				query.setParameter("skuList", skuList);
			}
			List<SalesPerformaDetailData> invoices = (List<SalesPerformaDetailData>) query.getResultList();
			LOG.info("Found invoices for WH Unit :::::: " + invoices.size());
			return invoices;
		} catch (Exception e) {
			LOG.error("No b2b sales invoice event found for the unit for this business date:::", e);
			return null;
		}
	}

	@Override
	public List<GoodsReceivedData> getGRsForWHUnit(int unitId, List<Integer> skuList) {
		try {
			StringBuilder queryString = new StringBuilder("SELECT DISTINCT E FROM GoodsReceivedData E");
			if (skuList != null) {
				queryString.append(", GoodsReceivedItemData F");
			}
			queryString.append(" WHERE E.generatedForUnitId = :unitId and E.closureEventId is null");
			if (skuList != null) {
				queryString.append(" and E.id=F.goodsReceivedData.id and F.skuId in (:skuList)");
			}
			queryString.append(" and E.status = :status ORDER BY E.id ASC");
			Query query = manager.createQuery(queryString.toString()).setParameter("unitId", unitId)
					.setParameter("status", SCMOrderStatus.SETTLED.toString());
			if (skuList != null) {
				query.setParameter("skuList", skuList);
			}
			List<GoodsReceivedData> grList = (List<GoodsReceivedData>) query.getResultList();
			LOG.info("Number of receivings found for summary ::: {}", grList.size());
			return grList;
		} catch (Exception e) {
			LOG.error("No receiving event found for the unit for this business date ::: ", e);
			return null;
		}
	}

	@Override
	public List<VendorGoodsReceivedData> getVendorGRItemsForWHUnit(int unitId, List<Integer> skuList) {
		List<String> status = new ArrayList<>();
		status.add(PurchaseOrderStatus.CREATED.toString());
		if(skuList != null) {
			status.add(PurchaseOrderStatus.INITIATED.toString());
		}
		try {
			StringBuilder queryString = new StringBuilder("SELECT DISTINCT E FROM VendorGoodsReceivedData E");
			if (skuList != null) {
				queryString.append(", VendorGoodsReceivedItemData F");
			}

			queryString.append(" WHERE E.deliveryUnitId = :unitId and E.closureEventId is null");
			if (skuList != null) {
				queryString
						.append(" and E.goodsReceivedId=F.goodsReceivedData.goodsReceivedId and F.skuId in (:skuList)");
			}
			queryString.append(" and E.grStatus in (:status) ORDER BY E.id ASC");

			Query query = manager.createQuery(queryString.toString()).setParameter("unitId", unitId)
					.setParameter("status", status);
			if (skuList != null) {
				query.setParameter("skuList", skuList);
			}
			List<VendorGoodsReceivedData> vendorGRs = (List<VendorGoodsReceivedData>) query.getResultList();
			LOG.info("Found vendor GRs for WH Unit :::::: " + vendorGRs.size());
			return vendorGRs;
		} catch (Exception e) {
			LOG.error("No receiving event found for the unit for this business date ::: ", e);
			return null;
		}
	}

	@Deprecated
	@Override
	public List<ProductionBookingData> getProductionBookingsForWHUnit(int unitId, List<Integer> skuList) {
		try {
			StringBuilder queryString = new StringBuilder("SELECT DISTINCT E FROM ProductionBookingData E");
			queryString.append(" WHERE E.unitId = :unitId and E.closureEventId is null");
			if (skuList != null) {
				queryString.append(" and E.skuId in (:skuList)");
			}
			queryString.append(" and E.bookingStatus = :status ORDER BY E.id ASC");
			Query query = manager.createQuery(queryString.toString()).setParameter("unitId", unitId)
					.setParameter("status", BookingStatus.CREATED.toString());
			if (skuList != null) {
				query.setParameter("skuList", skuList);
			}
			List<ProductionBookingData> bookings = (List<ProductionBookingData>) query.getResultList();
			LOG.info("Found bookings for WH Unit :::::: " + bookings.size());
			return bookings;
		} catch (Exception e) {
			LOG.error("No receiving event found for the unit for this business date ::: ", e);
			return null;
		}
	}

	@Override
	public List<ProductionBookingData> getProductionBookingsDetailsForWHUnit(int unitId, List<Integer> bookingIds) {
		try {
			StringBuilder queryString = new StringBuilder("SELECT E FROM ProductionBookingData E");
			queryString.append(" WHERE E.unitId = :unitId and E.closureEventId is null");
			queryString.append(" and E.bookingStatus = :status and E.bookingId IN (:bookingIds) ORDER BY E.id ASC");
			Query query = manager.createQuery(queryString.toString()).setParameter("unitId", unitId)
					.setParameter("status", BookingStatus.CREATED.toString());
			query.setParameter("bookingIds", bookingIds);
			List<ProductionBookingData> bookings = (List<ProductionBookingData>) query.getResultList();
			LOG.info("Found bookings for WH Unit :::::: " + bookings.size());
			return bookings;
		} catch (Exception e) {
			LOG.error("No receiving event found for the unit for this business date ::: ", e);
			return null;
		}
	}

	@Override
	public List<ReverseProductionBookingData> getReverseProductionBookingsDetailsForWHUnit(int unitId, List<Integer> bookingIds) {
		try {
			StringBuilder queryString = new StringBuilder("SELECT E FROM ReverseProductionBookingData E");
			queryString.append(" WHERE E.unitId = :unitId and E.closureEventId is null");
			queryString.append(" and E.bookingStatus = :status and E.bookingId IN (:bookingIds) ORDER BY E.id ASC");
			Query query = manager.createQuery(queryString.toString()).setParameter("unitId", unitId)
					.setParameter("status", BookingStatus.CREATED.toString());
			query.setParameter("bookingIds", bookingIds);
			List<ReverseProductionBookingData> bookings = (List<ReverseProductionBookingData>) query.getResultList();
			LOG.info("Found Reverse bookings for WH Unit :::::: " + bookings.size());
			return bookings;
		} catch (Exception e) {
			LOG.error("No Reverse Productions Bookings found for the unit for this business date ::: ", e);
			return null;
		}
	}


	@Override
	public List<Integer> getProductionBookingsIdsForWHUnit(int unitId, List<Integer> skuList) {
		try {
			StringBuilder queryString = new StringBuilder("SELECT DISTINCT E.bookingId FROM ProductionBookingData E");
			queryString.append(" WHERE E.unitId = :unitId and E.closureEventId is null");
			if (skuList != null) {
				queryString.append(" and E.skuId in (:skuList)");
			}
			queryString.append(" and E.bookingStatus = :status ORDER BY E.id ASC");
			Query query = manager.createQuery(queryString.toString()).setParameter("unitId", unitId)
					.setParameter("status", BookingStatus.CREATED.toString());
			if (skuList != null) {
				query.setParameter("skuList", skuList);
			}
			List<Integer> bookings = (List<Integer>) query.getResultList();
			LOG.info("Found bookings ids for WH Unit :::::: " + bookings.size());
			return bookings;
		} catch (Exception e) {
			LOG.error("No receiving  ids event found for the unit for this business date ::: ", e);
			return null;
		}
	}

	@Override
	public List<Integer> getReverseProductionBookingsIdsForWHUnit(int unitId, List<Integer> skuList) {
		try {
			StringBuilder queryString = new StringBuilder("SELECT DISTINCT E.bookingId FROM ReverseProductionBookingData E");
			queryString.append(" WHERE E.unitId = :unitId and E.closureEventId is null");
			if (skuList != null) {
				queryString.append(" and E.skuId in (:skuList)");
			}
			queryString.append(" and E.bookingStatus = :status ORDER BY E.id ASC");
			Query query = manager.createQuery(queryString.toString()).setParameter("unitId", unitId)
					.setParameter("status", BookingStatus.CREATED.toString());
			if (skuList != null) {
				query.setParameter("skuList", skuList);
			}
			List<Integer> bookings = (List<Integer>) query.getResultList();
			LOG.info("Found Reverse bookings ids for WH Unit :::::: " + bookings.size());
			return bookings;
		} catch (Exception e) {
			LOG.error("No Reverse receiving  ids event found for the unit for this business date ::: ", e);
			return null;
		}
	}

	@Override
	public Collection<ProductionBookingDataVO> getProductionBookingsAggregateForWHUnit(int unitId, List<Integer> bookingIds,
																					   boolean loadDrilldowns) {
		if (bookingIds == null || bookingIds.size() == 0) {
			return new ArrayList<>();
		}
		try {
			StringBuilder queryString = new StringBuilder(
					"SELECT E.productId, E.productName, E.skuId, E.unitOfMeasure, E.unitId, E.profile, sum(E.totalCost), sum(E.quantity) FROM ProductionBookingData E");
			queryString.append(
					" WHERE E.bookingId IN (:bookingIds) group by E.productId");
			Query query = manager.createQuery(queryString.toString());
			query.setParameter("bookingIds", bookingIds);
			List<Object[]> response = query.getResultList();
			Map<Integer, ProductionBookingDataVO> map = new HashMap<Integer, ProductionBookingDataVO>();
			if (response != null && response.size() > 0) {
				for (Object[] o : response) {
					ProductionBookingDataVO vo = getProductionBookingDataVO(o);
					map.put(vo.getProductId(), vo);
				}
			}
			LOG.info("Found bookings for WH Unit :::::: " + map.size());
			if (loadDrilldowns) {
				StringBuilder queryString1 = new StringBuilder(
						"SELECT E.productId, B.skuName, B.skuId, B.unitOfMeasure, sum(B.totalCost), "
								+ "sum(B.calculatedQuantity) FROM ProductionBookingData E, BookingConsumptionData B");
				queryString1.append(
						" WHERE E.bookingId = B.booking.bookingId and E.bookingId IN (:bookingIds) group by E.productId, B.skuId");
				Query query1 = manager.createQuery(queryString1.toString());
				query1.setParameter("bookingIds", bookingIds);
				List<Object[]> response1 = query1.getResultList();
				if (response1 != null && response1.size() > 0) {
					for (Object[] o : response1) {
						int productId = (Integer) o[0];
						BookingConsumptionDataVO vo = getBookingConsumptionDataVO(o);
						if (map.containsKey(productId)) {
							map.get(productId).getConsumption().add(vo);
						}
					}
				}
			}
			return map.values();
		} catch (Exception e) {
			LOG.error("No receiving event found for the unit for this business date ::: ", e);
			return new ArrayList<>();
		}
	}

	@Override
	public Collection<ProductionBookingDataVO> getReserveProductionBookingsAggregateForWHUnit(int unitId, List<Integer> bookingIds,
																							  boolean loadDrilldowns) {
		if (bookingIds == null || bookingIds.size() == 0) {
			return new ArrayList<>();
		}
		try {
			StringBuilder queryString = new StringBuilder(
					"SELECT E.productId, E.productName, E.skuId, E.unitOfMeasure, E.unitId, E.profile, sum(E.totalCost), sum(E.quantity) FROM ReverseProductionBookingData E");
			queryString.append(
					" WHERE E.bookingId IN (:bookingIds) group by E.productId");
			Query query = manager.createQuery(queryString.toString());
			query.setParameter("bookingIds", bookingIds);
			List<Object[]> response = query.getResultList();
			Map<Integer, ProductionBookingDataVO> map = new HashMap<Integer, ProductionBookingDataVO>();
			if (response != null && response.size() > 0) {
				for (Object[] o : response) {
					ProductionBookingDataVO vo = getProductionBookingDataVO(o);
					map.put(vo.getProductId(), vo);
				}
			}
			LOG.info("Found bookings for WH Unit :::::: " + map.size());
			if (loadDrilldowns) {
				StringBuilder queryString1 = new StringBuilder(
						"SELECT E.productId, B.skuName, B.skuId, B.unitOfMeasure, sum(B.totalCost), "
								+ "sum(B.calculatedQuantity) FROM ReverseProductionBookingData E, ReverseBookingConsumptionData B");
				queryString1.append(
						" WHERE E.bookingId = B.booking.bookingId and E.bookingId IN (:bookingIds) group by E.productId, B.skuId");
				Query query1 = manager.createQuery(queryString1.toString());
				query1.setParameter("bookingIds", bookingIds);
				List<Object[]> response1 = query1.getResultList();
				if (response1 != null && response1.size() > 0) {
					for (Object[] o : response1) {
						int productId = (Integer) o[0];
						BookingConsumptionDataVO vo = getBookingConsumptionDataVO(o);
						if (map.containsKey(productId)) {
							map.get(productId).getConsumption().add(vo);
						}
					}
				}
			}
			return map.values();
		} catch (Exception e) {
			LOG.error("No Reverse receiving event found for the unit for this business date ::: ", e);
			return new ArrayList<>();
		}
	}

	private static BookingConsumptionDataVO getBookingConsumptionDataVO(Object[] o) {
		BookingConsumptionDataVO vo = new BookingConsumptionDataVO();
		vo.setSkuName((String) o[1]);
		vo.setSkuId((Integer) o[2]);
		vo.setUnitOfMeasure((String) o[3]);
		vo.setTotalCost((BigDecimal) o[4]);
		vo.setCalculatedQuantity((BigDecimal) o[5]);
		vo.setUnitPrice(AppUtils.divide(vo.getTotalCost(), vo.getCalculatedQuantity()));
		return vo;
	}

	private static ProductionBookingDataVO getProductionBookingDataVO(Object[] o) {
		ProductionBookingDataVO vo = new ProductionBookingDataVO();
		vo.setProductId((Integer) o[0]);
		vo.setProductName((String) o[1]);
		vo.setSkuId((Integer) o[2]);
		vo.setUnitOfMeasure((String) o[3]);
		vo.setUnitId((Integer) o[4]);
		vo.setProfile((String) o[5]);
		vo.setTotalCost((BigDecimal) o[6]);
		vo.setQuantity((BigDecimal) o[7]);
		vo.setUnitPrice(AppUtils.divide(vo.getTotalCost(), vo.getQuantity()));
		return vo;
	}


	@Override
	public List<StockEntryEventData> getActiveOpeningEventForUnit(int unitId, List<Integer> skuList) {
		try {
			StringBuilder queryString = new StringBuilder(
					"SELECT E FROM StockEntryEventData E, SCMDayCloseEventData F");
			queryString.append(
					" WHERE E.updateEventId=F.eventId and F.unitId = :unitId and F.dayCloseEventType=:stockType");
			if (skuList != null) {
				queryString.append(" and E.skuId in (:skuList)");
			}
			queryString.append(" and F.status = :status ORDER BY E.stockEntryId ASC");
			Query query = manager.createQuery(queryString.toString()).setParameter("unitId", unitId)
					.setParameter("status", StockEventStatus.INITIATED.name())
					.setParameter("stockType", StockEventType.WH_OPENING.name());
			if (skuList != null) {
				query.setParameter("skuList", skuList);
			}
			return (List<StockEntryEventData>) query.getResultList();
		} catch (Exception e) {
			LOG.error("No opening event found for the unit for warehouse ::: {} ", unitId, e);
			return null;
		}
	}

	@Override
	public List<Integer> getSkuListForUnit(int unitId) {
		try {
			String queryString = "SELECT E.skuId FROM UnitSkuMapping E "
					+ "WHERE E.unitId = :unitId and E.mappingStatus=:status " + "ORDER BY E.unitSkuMappingId ASC";
			Query query = manager.createQuery(queryString).setParameter("unitId", unitId).setParameter("status",
					SwitchStatus.ACTIVE.name());
			return (List<Integer>) query.getResultList();
		} catch (Exception e) {
			LOG.error("No active sku found for the unit for warehouse ::: {} ", unitId, e);
			return null;
		}
	}

	@Override
	public List<DayCloseTxnEventDrillDownData> getDrillDownData(SCMDayCloseEventData eventData) {
		try {
			String queryString = "SELECT E FROM DayCloseTxnEventDrillDownData E, SCMDayCloseEventData F "
					+ "WHERE E.event.eventId = F.eventId and F.eventId = :eventId";
			Query query = manager.createQuery(queryString).setParameter("eventId", eventData.getEventId());
			return (List<DayCloseTxnEventDrillDownData>) query.getResultList();
		} catch (Exception e) {
			LOG.error("No drill down data found for the unit for event ::: {} ", eventData.getEventId(), e);
			return null;
		}
	}

	@Override
	public List<DayCloseInventoryDrillDown> getInventoryList(SCMDayCloseEventData eventData) {
		try {
			String queryString = "SELECT E FROM DayCloseInventoryDrillDown E, SCMDayCloseEventData F "
					+ "WHERE E.closureEvent.eventId = F.eventId and F.eventId = :eventId";
			Query query = manager.createQuery(queryString).setParameter("eventId", eventData.getEventId());
			return (List<DayCloseInventoryDrillDown>) query.getResultList();
		} catch (Exception e) {
			LOG.error("No drill down data found for the unit for event ::: {} ", eventData.getEventId(), e);
			return null;
		}
	}



	@Override
	public List<ConsumableStockState> getConsumableStockState(List<Integer> skuIds , Integer unitId){
		try {
			String queryString = " SELECT E FROM ConsumableStockState E "
					+ " WHERE E.skuId IN  (:skuIds) and E.unitId = :unitId ";
			Query query = manager.createQuery(queryString).setParameter("skuIds", skuIds).setParameter("unitId",unitId);
			return (List<ConsumableStockState>) query.getResultList();
		} catch (NoResultException e) {
			LOG.error("No Consumable State Founds For Sku Ids :::: {} , Unit Id :::: {} ",skuIds,unitId);
			return new ArrayList<>();
		}

	}

	@Override
	public List<VarianceModal> getCafeVarianceStock(int unitId, Date businessDate, StockTakeType varianceType) {

		List<VarianceModal> list = new ArrayList<>();
		try {
			/*
			 * String queryString = "SELECT P.productId, " + " P.productName, " +
			 * " P.unitOfMeasure, " + " P.categoryDefinition.categoryCode, " +
			 * " P.subCategoryDefinition.code, " + " A.openingStock, " + " B.transferOut, "
			 * + " B.received, " + " B.wastage, " + " B.consumption, " +
			 * " A.actualClosing, " + " A.expectedClosing, " + " A.variance, " +
			 * " P.negotiatedUnitPrice " +
			 * " FROM SCMProductInventoryData A, ProductDefinitionData P, SCMProductConsumptionData B "
			 * + " WHERE A.productId = P.productId AND A.businessDate = :businessDate " +
			 * " AND A.unitId = :unitId AND  A.currentDayCloseEvent.eventFrequencyType = :varianceType "
			 * +
			 * " AND B.productId = P.productId AND A.currentDayCloseEvent.eventId = B.eventId.eventId "
			 * ;
			 */

			String queryString = "SELECT si.PRODUCT_ID,pd.PRODUCT_NAME,pd.UNIT_OF_MEASURE, "
					+ "cd.CATEGORY_NAME,sd.SUB_CATEGORY_NAME,si.OPENING_STOCK, "
					+ "COALESCE(dcpv.TRANSFER_OUT, 0) TRANSFERRED,COALESCE(dcpv.RECEIVED,0) RECEIVED, "
					+ "COALESCE(dcpv.WASTAGE, 0) WASTAGE,COALESCE(dcpv.CONSUMPTION,0) CONSUMPTION, "
					+ "si.CLOSING_STOCK,si.EXPECTED_CLOSING_VALUE,si.VARIANCE,si.VARIANCE_PRICE,pd.VARIANCE_TYPE "
					+ "FROM STOCK_INVENTORY si LEFT OUTER JOIN DAY_CLOSE_PRODUCT_VALUES dcpv "
					+ "ON dcpv.EVENT_ID=si.CURRENT_EVENT_ID AND si.PRODUCT_ID = dcpv.PRODUCT_ID "
					+ "AND dcpv.STOCK_TYPE=si.STOCK_TYPE "
					+ "INNER JOIN PRODUCT_DEFINITION pd ON pd.PRODUCT_ID = si.PRODUCT_ID "
					+ "INNER JOIN CATEGORY_DEFINITION cd ON cd.CATEGORY_ID = pd.CATEGORY_ID "
					+ "INNER JOIN SUB_CATEGORY_DEFINITION sd ON sd.SUB_CATEGORY_ID = pd.SUB_CATEGORY_ID "
					+ "INNER JOIN DAY_CLOSE_EVENT dce ON dce.EVENT_ID = si.CURRENT_EVENT_ID "
					+ "WHERE si.UNIT_ID=:unitId AND si.BUSINESS_DATE=:businessDate AND si.STOCK_TYPE = :varianceType "
					+ "AND dce.CLOSURE_EVENT_TYPE= :eventType AND si.STATUS = :inventoryStatus AND dce.STATUS=:stockStatus";

			Query query = manager.createNativeQuery(queryString);
			query.setParameter("unitId", unitId).setParameter("businessDate", SCMUtil.getDate(businessDate))
					.setParameter("eventType", StockEventType.STOCK_TAKE.name())
					.setParameter("varianceType", varianceType.name())
					.setParameter("stockStatus", StockEventStatus.CLOSED.name())
					.setParameter("inventoryStatus", StockEventStatus.CLOSED.name());

			List<Object[]> results = query.getResultList();
			results.stream().forEach((record) -> {
				VarianceModal o = new VarianceModal();
				o.setId((Integer) record[0]);
				o.setName((String) record[1]);
				o.setUom((String) record[2]);
				o.setCategory((String) record[3]);
				o.setSubCategory((String) record[4]);
				o.setOpeningStock((BigDecimal) record[5]);
				o.setTransferred((BigDecimal) record[6]);
				o.setReceived((BigDecimal) record[7]);
				o.setWasted((BigDecimal) record[8]);
				o.setConsumption((BigDecimal) record[9]);
				o.setClosingStock((BigDecimal) record[10]);
				o.setExpectedValue((BigDecimal) record[11]);
				o.setVariance((BigDecimal) record[12]);
				o.setUnitCost((BigDecimal) record[13]);
				o.setTypeOfVariance((String) record[14]);
				o.setVarianceCost(SCMUtil.multiply(o.getVariance(), o.getUnitCost()));
				list.add(o);
			});

			return list;

		} catch (Exception e) {
			LOG.error("Exception in data for getCafeVarianceStock Unit {}, business date {}, variance type {}", unitId,
					businessDate, varianceType, e);
			return null;
		}
	}

	@Override
	public List<CafeWastageModal> getCafeWastage(int unitId, StockTakeType varianceType, Date businessDate) {
		List<SCMWastageData> wastageEventItems = getWastageEventListByDayCloseEventRangeData(unitId, businessDate, varianceType);
		List<CafeWastageModal> list = new ArrayList<>();
		for (SCMWastageData p : wastageEventItems) {
			CafeWastageModal e = new CafeWastageModal();
			e.setWastageId(p.getWastageItemId());
			e.setGeneratedBy(masterCache.getEmployee(p.getWastage().getGeneratedBy()));
			e.setComment(p.getComment());
			e.setCategory(p.getProduct().getCategoryDefinition().getCategoryCode());
			e.setSubCategory(p.getProduct().getSubCategoryDefinition().getCode());
			e.setProductName(p.getProduct().getProductName());
			e.setUom(p.getProduct().getUnitOfMeasure());
			e.setQuantity(p.getQuantity());
			e.setUnitCost(p.getPrice());
			e.setValue(p.getCost());
			list.add(e);
		}
		return list;
	}

	public SCMDayCloseEventData getLastDayCloseEvent(int unitId, StockEventType stockTake, Date businessDate, boolean fetchNonAutoDayClose) {
		SCMDayCloseEventData returnEvent = null;
		try {
			String queryString = "SELECT E FROM SCMDayCloseEventData E where E.unitId = :unitId"
					+ " and E.status = :status and E.dayCloseEventType = :type" + " and E.eventFrequencyType != :faType"
					+ (businessDate != null ? " and E.businessDate < :businessDate" : "")
					+ (fetchNonAutoDayClose ? " and E.isAutoDayClose <> 'Y'" : "")
					+ " ORDER BY E.businessDate DESC";
			Query query = manager.createQuery(queryString);
			query.setParameter("unitId", unitId).setParameter("faType", StockTakeType.FIXED_ASSETS.name())
					.setParameter("status", StockEventStatus.CLOSED.toString())
					.setParameter("type", stockTake.toString());
			if (businessDate != null) {
				query.setParameter("businessDate", businessDate);
			}
			query.setMaxResults(1);
			returnEvent = (SCMDayCloseEventData) query.getSingleResult();
		} catch (Exception e) {
			LOG.error("Could not find last day close event :::");
		}
		return returnEvent;
	}

	@Override
	public Map<Integer, BigDecimal> getVarianceTillNow(SCMDayCloseEventData lastDayCloseEventData, List<Integer> productIds) {
		Map<Integer, BigDecimal> result = new HashMap<>();
		StringBuilder queryString = new StringBuilder("SELECT PRODUCT_ID, SUM(SI.VARIANCE) FROM DAY_CLOSE_EVENT DCE INNER JOIN STOCK_INVENTORY SI ON" +
				" DCE.EVENT_ID = SI.CURRENT_EVENT_ID AND DCE.CLOSURE_EVENT_FREQUENCY = 'DAILY'" +
				" WHERE DCE.STATUS =:closedStatus AND DCE.CLOSURE_EVENT_TYPE =:closureEventType AND DCE.EVENT_ID >:eventId AND DCE.UNIT_ID =:unitId ");
		if (Objects.nonNull(productIds) && !productIds.isEmpty()) {
			queryString.append("AND PRODUCT_ID IN(:productIds) ");
		}
		queryString.append("GROUP BY PRODUCT_ID");
		Query query = manager.createNativeQuery(queryString.toString());
		query.setParameter("closedStatus", StockEventStatus.CLOSED.name());
		query.setParameter("closureEventType",  StockEventType.STOCK_TAKE.name());
		query.setParameter("eventId", lastDayCloseEventData.getEventId());
		query.setParameter("unitId", lastDayCloseEventData.getUnitId());
		if (Objects.nonNull(productIds) && !productIds.isEmpty()) {
			query.setParameter("productIds", productIds);
		}
		List<Object[]> resultList = query.getResultList();
		if (Objects.nonNull(resultList) && !resultList.isEmpty()) {
			for (Object[] data : resultList) {
				result.put((Integer) data[0], (BigDecimal) data[1]);
			}
		}
		return result;
	}

	@Override
	public List<CafeUnsettledTOModal> getCafeUnsettledTO(int unitId, StockTakeType varianceType, Date businessDate) {
		SCMDayCloseEventData lastEvent = getLastDayCloseEventOfType(unitId, varianceType, StockEventType.STOCK_TAKE,
				businessDate);
		Date lastBusinessDate = businessDate;
		if (lastEvent != null) {
			lastBusinessDate = lastEvent.getBusinessDate();
		}
		String[] statusList = new String[]{SCMOrderStatus.CREATED.toString(), SCMOrderStatus.TRANSFERRED.toString()};
		List<TransferOrderItemData> itemDataList = getTransferList(unitId, businessDate, lastBusinessDate,
				Arrays.asList(statusList), false, null);
		List<CafeUnsettledTOModal> list = new ArrayList<>();
		for (TransferOrderItemData item : itemDataList) {
			CafeUnsettledTOModal entity = new CafeUnsettledTOModal();
			TransferOrderData to = item.getTransferOrderData();
			entity.setTransferOrderNo(to.getId());
			entity.setTransferOrderItemNo(item.getId());
			entity.setTransferDate(to.getGenerationTime());
			entity.setSkuName(item.getSkuName());
			entity.setUom(item.getUnitOfMeasure());
			int productId = scmCache.getSkuDefinition(item.getSkuId()).getLinkedProduct().getId();
			ProductDefinition p = scmCache.getProductDefinition(productId);
			entity.setCategory(p.getCategoryDefinition().getCode());
			entity.setSubCategory(p.getSubCategoryDefinition().getCode());
			entity.setReceivingUnitId(to.getGeneratedForUnitId());
			entity.setReceivingUnitName(scmCache.getUnitDetail(to.getGeneratedForUnitId()).getUnitName());
			entity.setTransferred(item.getTransferredQuantity());
			entity.setReceived(item.getReceivedQuantity());
			list.add(entity);
		}
		return list;
	}

	private List<SkuDefinition> filterBySubCategory(List<SkuDefinition> taggedInventoryListSkus, List<Integer> subCategoryIds) {
		if (Objects.isNull(subCategoryIds) || subCategoryIds.isEmpty()) {
			return taggedInventoryListSkus;
		}
		return taggedInventoryListSkus.stream().filter(sku -> subCategoryIds.contains(scmCache.getProductDefinition(sku.getLinkedProduct().getId())
				.getSubCategoryDefinition().getId())).collect(Collectors.toList());
	}


	@Override
	public List<VarianceModal> getWareHouseVarianceStock(SCMDayCloseEventData eventData) {

		List<DayCloseInventoryDrillDown> inventory = getInventoryList(eventData);
		List<VarianceModal> list = new ArrayList<>();
		Map<Integer, Integer> mappedSkuWithInventory = getMappedSkuWithInventoryList(eventData.getUnitId());
		Boolean isAllListType = eventData.getEventFrequencyType().equals(SCMUtil.FULL_AUDIT_DAY_CLOSE) ? true : false;
		List<Integer> subCategories = new ArrayList<>();
		if (Objects.nonNull(eventData.getSubCategories()) && !eventData.getSubCategories().equals("")) {
			subCategories = Arrays.stream(eventData.getSubCategories().split(",")).
					map(str -> Integer.parseInt(str)).collect(Collectors.toList());
		}
		List<Integer> skusForToday = filterBySubCategory(getTaggedInventoryListForWarehouse(eventData.getUnitId(), mappedSkuWithInventory, isAllListType),
				subCategories).stream()
				.map(SkuDefinition::getSkuId).collect(Collectors.toList());

		inventory.stream()
				.filter(rec -> skusForToday.contains(rec.getSkuId())
						|| BigDecimal.ZERO.compareTo(SCMUtil.convertToBigDecimal(rec.getVariance())) != 0)
				.forEach(record -> {
					SkuDefinition sku = scmCache.getSkuDefinition(record.getSkuId());
					ProductDefinition product = scmCache.getProductDefinition(sku.getLinkedProduct().getId());

					VarianceModal o = new VarianceModal();
					o.setId(record.getSkuId());
					o.setName(sku.getSkuName());
					o.setUom(record.getUnitOfMeasure());
					o.setCategory(product.getCategoryDefinition().getCode());
					o.setSubCategory(product.getSubCategoryDefinition().getCode());
					o.setOpeningStock(record.getOpeningStock());
					o.setTransferred(record.getTransferred());
					o.setReceived(record.getReceived());
					o.setBooking(record.getBooked());
					o.setWasted(record.getWasted());
					o.setConsumption(record.getConsumed());
					o.setClosingStock(record.getActual());
					o.setExpectedValue(record.getExpected());
					o.setVariance(record.getVariance());
					o.setUnitCost(record.getSkuPrice());
					o.setVarianceCost(record.getVarianceCost());
					o.setReverseBooking(record.getReverseBooked());
					o.setReverseConsumption(record.getReverseConsumed());
					list.add(o);
				});

		return list;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.data.dao.StockManagementDao#generateDailySummary(java.
	 * lang.Integer)
	 */
	@Override
	public void generateDailySummary(Integer eventId) {
		Query query = manager.createNativeQuery("CALL SP_DAILY_DAY_CLOSURE_AGGREGATED_DATA(:eventId)");
		query.setParameter("eventId", eventId);
		query.executeUpdate();
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.data.dao.StockManagementDao#generateDailySummary(java.
	 * lang.Integer)
	 */
	@Override
	public List<VarianceSummaryModal> getDailySummary(Integer eventId) {
		List<VarianceSummaryModal> result = new ArrayList<>();
		Query query = manager.createNativeQuery("CALL SP_DAILY_DAY_CLOSURE_AGGREGATED_DATA_VIEW(:eventId)");
		query.setParameter("eventId", eventId);
		List<Object[]> list = query.getResultList();
		for (Object[] data : list) {
			VarianceSummaryModal modal = new VarianceSummaryModal((String) data[0], (String) data[1], (String) data[2],
					(String) data[3], (String) data[4], (BigDecimal) data[5]);
			result.add(modal);
		}
		return result;
	}

	@Override
	public List<SkuDefinition> getTaggedInventoryListForWarehouse(int unitId) {

		List<Integer> inventoryLists = Arrays.asList(SCMUtil.getListTypeForUnit(), 16);
		Date businessDate = SCMUtil.getCurrentBusinessDate();

		// get top 100 SKUs with highest variance at month end
		List<Integer> skuList = SCMUtil.getRequiredFrequency(businessDate).equals(StockTakeType.MONTHLY)
				? getMonthlySkuList(unitId)
				: new ArrayList<>();

		List<SkuDefinition> returnList = scmCache.getSkuDefinitions().values().stream()
				.filter(sku -> inventoryLists.contains(sku.getInventoryList()) || skuList.contains(sku.getSkuId()))
				.collect(Collectors.toList());
		return returnList;

	}

	@Override
	public List<SkuDefinition> getTaggedInventoryListForWarehouse(int unitId, Map<Integer, Integer> mappedSkuwithInventoryList, Boolean isAllListType) {
		List<Integer> inventoryLists = new ArrayList<>();
		if (Objects.nonNull(isAllListType) && isAllListType.equals(Boolean.TRUE)) {
			for (Integer i = 2; i <= 16; i++) {
				inventoryLists.add(i);
			}
		} else {
			inventoryLists = Arrays.asList(SCMUtil.getListTypeForUnit(), 16);
		}
		Date businessDate = SCMUtil.getCurrentBusinessDate();

		// get top 100 SKUs with highest variance at month end
		List<Integer> skuList = SCMUtil.getRequiredFrequency(businessDate).equals(StockTakeType.MONTHLY)
				? getMonthlySkuList(unitId)
				: new ArrayList<>();

		List<Integer> finalInventoryLists = inventoryLists;
		List<SkuDefinition> returnList = scmCache.getSkuDefinitions().values().stream()
				.filter(sku -> finalInventoryLists.contains(mappedSkuwithInventoryList.get(sku.getSkuId())) || skuList.contains(sku.getSkuId()))
				.collect(Collectors.toList());

		return returnList;

	}

	@Override
	public List<ApprovalDetailData> getAssetApprovals(Integer approvalRequestId, Integer unitId, Integer eventId, Integer assetId, String status,List<ApprovalType>  types) throws SumoException {

		List<ApprovalDetailData> returnList = new ArrayList<>();
		StringBuilder queryString = new StringBuilder("FROM ApprovalDetailData A WHERE ");
		if(Objects.nonNull(approvalRequestId)){
			queryString.append(" A.approvalRequestId = :approvalRequestId AND ");
		}
		if(Objects.nonNull(unitId)){
			queryString.append(" A.unitId = :unitId AND ");
		}
		if(Objects.nonNull(eventId)){
			queryString.append(" A.eventId = :eventId AND ");
		}
		if(Objects.nonNull(assetId)){
			queryString.append(" A.assetId = :assetId AND ");
		}
		if(Objects.nonNull(types)){
			queryString.append(" A.type in (:types) AND ");
		}
		queryString.append(" A.status = :status ");
		queryString.append(" ORDER BY A.approvalRequestId DESC ");
		Query query = manager.createQuery(String.valueOf(queryString))
						.setParameter("status", status);
		if(Objects.nonNull(approvalRequestId)){
			query.setParameter("approvalRequestId", approvalRequestId);
		}
		if(Objects.nonNull(unitId)){
			query.setParameter("unitId", unitId);
		}
		if(Objects.nonNull(eventId)){
			query.setParameter("eventId", eventId);
		}
		if(Objects.nonNull(assetId)){
			query.setParameter("assetId", assetId);
		}
		if(Objects.nonNull(types)){
			query.setParameter("types",types.stream().map((ApprovalType e)->e.value()).collect(Collectors.toList()));
		}
		returnList = query.getResultList();

		return returnList;
	}

	@Override
	public List<Integer> getAuditEventIdList(List<Integer> eventIds){
		List<Integer> resultList = new ArrayList<>();
		try {
			StringBuilder queryString = new StringBuilder("SELECT S.eventId FROM StockEventDefinitionData S WHERE ");
			if(!CollectionUtils.isEmpty(eventIds)){
				queryString.append(" S.eventId in (:eventIds) AND");
				queryString.append(" S.subType = :subType");
				Query query = manager.createQuery(String.valueOf(queryString));
				query.setParameter("eventIds",eventIds);
				query.setParameter("subType", StockTakeSubType.AUDIT.value());
				resultList = query.getResultList();
			}
			if(Objects.isNull(resultList)){
				return new ArrayList<>();
			}
			return resultList;
		}catch (Exception e){
			LOG.info("Error in fetching Event Ids List for Audit : {}",e);
			return resultList;
		}
	}

	@Override
	public Map<Integer, Integer> getMappedSkuWithInventoryList(int unitId) {

		try {
			Map<Integer, Integer> mappedSkuAndInventoryList = new HashMap<>();
			String queryString = "SELECT E.skuId,E.inventoryList FROM UnitSkuMapping E "
					+ "WHERE E.unitId = :unitId and E.mappingStatus=:status " + "ORDER BY E.unitSkuMappingId ASC";
			Query query = manager.createQuery(queryString).setParameter("unitId", unitId).setParameter("status",
					SwitchStatus.ACTIVE.name());
			List<Object[]> results = query.getResultList();
			results.stream().forEach((record) -> {
				if ((Integer) record[1] != 0 && record[0] != null) {
					mappedSkuAndInventoryList.put((Integer) record[0], (Integer) record[1]);
				}
			});
			return mappedSkuAndInventoryList;

		} catch (Exception e) {
			LOG.error("No active sku found for the unit for warehouse ::: {} ", unitId, e);
			return null;
		}
	}
	@Override
    public Map<Integer, Integer> getMappedSkuWithInventoryListCafe() {

        try {
            Map<Integer, Integer> mappedSkuAndInventoryList = new HashMap<>();
            String queryString = "SELECT E.skuId,E.inventoryList FROM CafeSkuMapping E "
                + "WHERE E.mappingStatus=:status";
            Query query = manager.createQuery(queryString).setParameter("status",
                SwitchStatus.ACTIVE.name());
            List<Object[]> results = query.getResultList();
            results.stream().forEach((record) -> {
                if ((Integer) record[1] != 0 && record[0] != null) {
                    mappedSkuAndInventoryList.put((Integer) record[0], (Integer) record[1]);
                }
            });
            return mappedSkuAndInventoryList;

        } catch (Exception e) {
            LOG.error("error fetching cafe sku mapping :: ",e);
            return null;
        }
    }

	@Override
	public List<BookingConsumptionDataVO> getBookingConsumptionsAggregate(int unitId,
																		  List<Integer> bookingConsumptionIds) {
		try {
			if (bookingConsumptionIds == null || bookingConsumptionIds.isEmpty()) {
				return new ArrayList<BookingConsumptionDataVO>();
			}
			List<BookingConsumptionDataVO> list = new ArrayList<BookingConsumptionDataVO>();
			StringBuilder queryString1 = new StringBuilder(
					"SELECT E.skuName, E.skuId, E.unitOfMeasure, sum(E.totalCost), "
							+ "sum(E.calculatedQuantity) FROM BookingConsumptionData E");
			queryString1.append(" WHERE E.id IN (:bookingConsumptionIds) group by E.skuId");
			Query query1 = manager.createQuery(queryString1.toString());
			query1.setParameter("bookingConsumptionIds", bookingConsumptionIds);
			List<Object[]> response1 = query1.getResultList();
			LOG.info("Found booking consumptions for WH Unit :::::: " + response1.size());
			if (response1 != null && response1.size() > 0) {
				for (Object[] o : response1) {
					BookingConsumptionDataVO vo = new BookingConsumptionDataVO();
					vo.setSkuName((String) o[0]);
					vo.setSkuId((Integer) o[1]);
					vo.setUnitOfMeasure((String) o[2]);
					vo.setTotalCost((BigDecimal) o[3]);
					vo.setCalculatedQuantity((BigDecimal) o[4]);
					vo.setUnitPrice(AppUtils.divide(vo.getTotalCost(), vo.getCalculatedQuantity()));
					list.add(vo);
				}
			}
			return list;
		} catch (Exception e) {
			LOG.error("No receiving event found for the unit for this business date ::: ", e);
			return new ArrayList<BookingConsumptionDataVO>();
		}

	}

	@Override
	public List<BookingConsumptionDataVO> getReverseBookingConsumptionsAggregate(int unitId,
																				 List<Integer> bookingConsumptionIds) {
		try {
			if (bookingConsumptionIds == null || bookingConsumptionIds.isEmpty()) {
				return new ArrayList<>();
			}
			List<BookingConsumptionDataVO> list = new ArrayList<>();
			StringBuilder queryString1 = new StringBuilder(
					"SELECT E.skuName, E.skuId, E.unitOfMeasure, sum(E.totalCost), "
							+ "sum(E.calculatedQuantity) FROM ReverseBookingConsumptionData E");
			queryString1.append(" WHERE E.id IN (:bookingConsumptionIds) group by E.skuId");
			Query query1 = manager.createQuery(queryString1.toString());
			query1.setParameter("bookingConsumptionIds", bookingConsumptionIds);
			List<Object[]> response1 = query1.getResultList();
			LOG.info("Found booking consumptions for WH Unit :::::: " + response1.size());
			if (response1 != null && response1.size() > 0) {
				for (Object[] o : response1) {
					BookingConsumptionDataVO vo = new BookingConsumptionDataVO();
					vo.setSkuName((String) o[0]);
					vo.setSkuId((Integer) o[1]);
					vo.setUnitOfMeasure((String) o[2]);
					vo.setTotalCost((BigDecimal) o[3]);
					vo.setCalculatedQuantity((BigDecimal) o[4]);
					vo.setUnitPrice(AppUtils.divide(vo.getTotalCost(), vo.getCalculatedQuantity()));
					list.add(vo);
				}
			}
			return list;
		} catch (Exception e) {
			LOG.error("No Reverse receiving event found for the unit for this business date ::: ", e);
			return new ArrayList<>();
		}

	}

	@Deprecated
	@Override
	public List<BookingConsumptionData> getBookingConsumptions(int unitId, List<Integer> skuList) {
		try {
			StringBuilder queryString = new StringBuilder("SELECT DISTINCT E FROM BookingConsumptionData E");
			queryString.append(" WHERE E.booking.unitId = :unitId and E.booking.closureEventId is null");
			if (skuList != null) {
				queryString.append(" and E.skuId in (:skuList)");
			}
			queryString.append(" and E.booking.bookingStatus = :status ORDER BY E.id ASC");
			Query query = manager.createQuery(queryString.toString()).setParameter("unitId", unitId)
					.setParameter("status", BookingStatus.CREATED.toString());
			if (skuList != null) {
				query.setParameter("skuList", skuList);
			}
			List<BookingConsumptionData> consumptionData = (List<BookingConsumptionData>) query.getResultList();
			LOG.info("Found booking consumptions for WH Unit :::::: " + consumptionData.size());
			return consumptionData;
		} catch (Exception e) {
			LOG.error("No receiving event found for the unit for this business date ::: ", e);
			return null;
		}

	}


	@Override
	public List<Integer> getBookingConsumptionsIds(int unitId, List<Integer> skuList) {
		try {
			StringBuilder queryString = new StringBuilder("SELECT distinct E.id FROM BookingConsumptionData E");
			queryString.append(" WHERE E.booking.unitId = :unitId and E.booking.closureEventId is null");
			if (skuList != null) {
				queryString.append(" and E.skuId in (:skuList)");
			}
			queryString.append(" and E.booking.bookingStatus = :status ORDER BY E.id ASC");
			Query query = manager.createQuery(queryString.toString()).setParameter("unitId", unitId)
					.setParameter("status", BookingStatus.CREATED.toString());
			if (skuList != null) {
				query.setParameter("skuList", skuList);
			}
			List<Integer> consumptionData = (List<Integer>) query.getResultList();
			LOG.info("Found booking consumptions for WH Unit :::::: " + consumptionData.size());
			return consumptionData;
		} catch (Exception e) {
			LOG.error("No receiving event found for the unit for this business date ::: ", e);
			return null;
		}

	}


	@Override
	public List<Integer> getReverseBookingConsumptionsIds(int unitId, List<Integer> skuList) {
		try {
			StringBuilder queryString = new StringBuilder("SELECT distinct E.id FROM ReverseBookingConsumptionData E");
			queryString.append(" WHERE E.booking.unitId = :unitId and E.booking.closureEventId is null");
			if (skuList != null) {
				queryString.append(" and E.skuId in (:skuList)");
			}
			queryString.append(" and E.booking.bookingStatus = :status ORDER BY E.id ASC");
			Query query = manager.createQuery(queryString.toString()).setParameter("unitId", unitId)
					.setParameter("status", BookingStatus.CREATED.toString());
			if (skuList != null) {
				query.setParameter("skuList", skuList);
			}
			List<Integer> consumptionData = (List<Integer>) query.getResultList();
			LOG.info("Found Reverse booking consumptions for WH Unit :::::: " + consumptionData.size());
			return consumptionData;
		} catch (Exception e) {
			LOG.error("No receiving Reverse event found for the unit for this business date ::: ", e);
			return null;
		}

	}

	@Override
	public Map<Integer, String> getSkuClosingDates(Integer unitId, List<Integer> skus) {
		try {
			Query query = manager.createQuery("SELECT g.skuId, MAX(g.eventLogData.createdAt)"
					+ " FROM WriteOffItemData g, SCMDayCloseEventData r WHERE g.skuId in (:skus)"
					+ " AND g.eventLogData.dayCloseEventData.eventId = r.eventId AND r.unitId=:unitId"
					+ " AND r.dayCloseEventType = :closingType AND r.status = :closingStatus" + " GROUP BY g.skuId");
			query.setParameter("skus", skus).setParameter("unitId", unitId)
					.setParameter("closingType", StockEventType.WH_CLOSING.name())
					.setParameter("closingStatus", StockEventStatus.CLOSED.name());
			List<Object[]> resultList = (List<Object[]>) query.getResultList();
			if (resultList != null && !resultList.isEmpty()) {
				return resultList.stream()
						.collect(Collectors.toMap(o -> Integer.parseInt(o[0].toString()), o -> String.valueOf(o[1])));
			}
		} catch (Exception e) {
			LOG.error("Not able to fetch closing dates for SKUs on warehouse::: ", e);
		}
		return null;
	}

	@Override
	public List<Integer> getDayCloseEventForBusinessDate(int unitId, Date businessDate, StockTakeType stockTakeType) {

		try {
			Date startDate = businessDate;
			Date endDate = businessDate;
			if (SCMUtil.isMonthly(stockTakeType)) {
				startDate = SCMUtil.getFirstDayOfMonth(businessDate);
				endDate = SCMUtil.getLastDayOfThisMonth(businessDate);
			}

			String queryString = "SELECT eventId FROM SCMDayCloseEventData F "
					+ "WHERE F.unitId = :unitId and F.dayCloseEventType  = :closing "
					+ "and F.status = :status and F.businessDate >= :startDate AND F.businessDate <= :endDate";
			Query query = manager.createQuery(queryString).setParameter("unitId", unitId)
					.setParameter("closing", StockEventType.CLOSING.toString())
					.setParameter("status", StockEventStatus.CLOSED.toString())
					.setParameter("startDate", SCMUtil.getDate(startDate))
					.setParameter("endDate", SCMUtil.getDate(endDate));
			return new ArrayList<Integer>(query.getResultList());
		} catch (NoResultException nre) {
			LOG.error("Could not find any closed Day closing event for the unit ::::: {}", unitId);
			return null;
		} catch (Exception e) {
			LOG.error("Could not find any closed Day closing event for the unit ::::: {}", unitId, e);
			return null;
		}
	}

	@Override
	public List<Pair<String, Pair<BigDecimal, BigDecimal>>> getCOGSAggregate(List<Integer> eventIds) {

		List<Pair<String, Pair<BigDecimal, BigDecimal>>> list = new ArrayList<>();

		try {
			String queryString = "SELECT B.consumptionType,  SUM(B.consumption * A.consumptionPrice),  SUM(B.taxableConsumption / A.taxableConsumption * A.taxAmount) FROM SCMProductConsumptionData A, SCMProductConsumptionDrillDown B "
					+ " WHERE A.unitId = B.unitId AND A.productId = B.productId "
					+ " AND A.unitOfMeasure = B.unitOfMeasure AND A.eventId.eventId = B.eventId.eventId "
					+ " AND A.eventId.eventId IN (:eventIds) " + " GROUP BY B.consumptionType";
			Query query = manager.createQuery(queryString).setParameter("eventIds", eventIds);

			List<Object[]> resultSet = query.getResultList();
			for (Object[] record : resultSet) {
				list.add(new Pair<>((String) record[0],
						new Pair<>(record[1] == null ? BigDecimal.ZERO : (BigDecimal) record[1],
								record[2] == null ? BigDecimal.ZERO : (BigDecimal) record[2])));
			}
			return list;
		} catch (Exception e) {
			LOG.error("Error while calculation consumption aggregate for event ::::: {}", eventIds, e);
			return null;
		}
	}

	@Override
	public InventoryAggregate getStockVarianceAmount(Integer eventId, StockTakeType type) {

		InventoryAggregate inventoryAggregate = new InventoryAggregate();

		if (eventId == null) {
			return inventoryAggregate;
		}

		List<Integer> categories = new ArrayList<>();
		categories.add(1); // COGS
		categories.add(4); // SEMI_FINISHED

		try {
			String queryString = "SELECT pd.varianceType, SUM(A.varianceCost), SUM(A.varianceTax) FROM SCMProductInventoryData A, ProductDefinitionData pd "
					+ "	WHERE A.currentDayCloseEvent.eventId = :eventId"
					+ " AND A.eventType = :eventType  AND A.stockType = :stockType "
					+ " AND A.currentDayCloseEvent.status = :status AND pd.productId = A.productId "
					+ " AND A.categoryId IN (:categories) GROUP BY pd.varianceType";

			Query query = manager.createQuery(queryString);
			query.setParameter("eventId", eventId);
			query.setParameter("stockType", type.name());
			query.setParameter("eventType", StockEventType.STOCK_TAKE.name());
			query.setParameter("status", StockEventStatus.CLOSED.name());
			query.setParameter("categories", categories);

			List<Object[]> resultSet = query.getResultList();
			setValues(inventoryAggregate, resultSet);
		} catch (Exception e) {
			LOG.error("Error while calculation consumption aggregate for event ::::: {}", eventId, e);
		}
		return inventoryAggregate;
	}

	private void setValues(InventoryAggregate inventoryAggregate, List<Object[]> resultSet) {
		if (resultSet != null && !resultSet.isEmpty()) {
			for (Object[] record : resultSet) {
				VarianceType varianceType = VarianceType.valueOf((String) record[0]);
				inventoryAggregate
						.setStockVariance(AppUtils.add(inventoryAggregate.getStockVariance(), (BigDecimal) record[1]));
				inventoryAggregate
						.setStockVarianceTax(AppUtils.add(inventoryAggregate.getStockVarianceTax(),
								record[2] != null ? (BigDecimal) record[2] : BigDecimal.ZERO));
				switch (varianceType) {
					case ZERO_VARIANCE:
						inventoryAggregate.setZeroVariance(
								AppUtils.add(inventoryAggregate.getZeroVariance(), (BigDecimal) record[1]));
						inventoryAggregate.setZeroVarianceTax(
								AppUtils.add(inventoryAggregate.getZeroVarianceTax(), record[2] != null ? (BigDecimal) record[2] : BigDecimal.ZERO));
						break;
					case PCC:
						inventoryAggregate
								.setVariancePCC(AppUtils.add(inventoryAggregate.getVariancePCC(), (BigDecimal) record[1]));
						inventoryAggregate
								.setVariancePCCTax(AppUtils.add(inventoryAggregate.getVariancePCCTax(),
										record[2] != null ? (BigDecimal) record[2] : BigDecimal.ZERO));
						break;
					case YIELD_CONTROLLABLES:
						inventoryAggregate
								.setVarianceYC(AppUtils.add(inventoryAggregate.getVarianceYC(), (BigDecimal) record[1]));
						inventoryAggregate
								.setVarianceYCTax(AppUtils.add(inventoryAggregate.getVarianceYCTax(),
										record[2] != null ? (BigDecimal) record[2] : BigDecimal.ZERO));
						break;
					default:
						inventoryAggregate.setVarianceOthers(
								AppUtils.add(inventoryAggregate.getVarianceOthers(), (BigDecimal) record[1]));
						inventoryAggregate.setVarianceOthersTax(
								AppUtils.add(inventoryAggregate.getVarianceOthersTax(),
										record[2] != null ? (BigDecimal) record[2] : BigDecimal.ZERO));
				}
			}
		}
	}

	@Override
	public InventoryAggregate getStockVarianceAmountMTD(int unitId, Date businessDate, StockTakeType type) {

		InventoryAggregate inventoryAggregate = new InventoryAggregate();

		List<Integer> categories = new ArrayList<>();
		categories.add(1); // COGS
		categories.add(4); // SEMI_FINISHED

		InventoryAggregate monthly = null;
		InventoryAggregate weekly = null;
		InventoryAggregate daily = null;
		InventoryAggregate dailyWithWrongMonthly = null;

		Date startDate = AppUtils.getDate(AppUtils.getFirstDayOfMonth(businessDate));

		if (SCMUtil.isCheckMonthly(businessDate) || StockTakeType.MONTHLY.equals(type)) {
			Integer eventId = getDayCloseEventIdOfType(unitId, StockTakeType.MONTHLY, StockEventType.STOCK_TAKE,
					SCMUtil.getMonthlyCheckDate(businessDate), SCMUtil.getLastDayOfThisMonth(businessDate));
			if (eventId != null && eventId > 0) {
				monthly = getMonthlyVarianceAggregated(unitId, eventId, categories);
			}
		}

		if (monthly == null) {

			Integer firstWeeklyEventId = getFistDayCloseEventIdOfType(unitId, StockTakeType.WEEKLY,
					StockEventType.STOCK_TAKE, businessDate);
			Integer firstMonthlyEventId = getFistDayCloseEventIdOfType(unitId, StockTakeType.MONTHLY,
					StockEventType.STOCK_TAKE, businessDate);

			SCMDayCloseEventData firstWeeklyEvent = null;
			SCMDayCloseEventData firstMonthlyEvent = null;
			if (firstWeeklyEventId != null) {
				firstWeeklyEvent = manager.find(SCMDayCloseEventData.class, firstWeeklyEventId);
			}
			if (firstMonthlyEventId != null) {
				firstMonthlyEvent = manager.find(SCMDayCloseEventData.class, firstMonthlyEventId);
			}

			if (firstMonthlyEvent != null) {
				if (firstWeeklyEvent != null) {
					if (firstWeeklyEvent.getBusinessDate().compareTo(firstMonthlyEvent.getBusinessDate()) >= 0) {
						// include days until monthly
						dailyWithWrongMonthly = getDailyVarianceAggregated(unitId, startDate,
								firstMonthlyEvent.getBusinessDate(), categories);
						startDate = AppUtils.getNextDate(firstMonthlyEvent.getBusinessDate());
					}
				} else {
					dailyWithWrongMonthly = getDailyVarianceAggregated(unitId, startDate,
							firstMonthlyEvent.getBusinessDate(), categories);
					startDate = AppUtils.getNextDate(firstMonthlyEvent.getBusinessDate());
				}
			}

			// NORMAL FLOW

			// WEEKLY
			weekly = getWeeklyVarianceAggregated(unitId, startDate, businessDate, categories);

			// Daily After last weekly not included
			daily = getDailyVarianceAggregated(unitId, startDate, businessDate, categories);
		}
		addInventoryAggregate(monthly, inventoryAggregate);
		addInventoryAggregate(weekly, inventoryAggregate);
		addInventoryAggregate(daily, inventoryAggregate);
		addInventoryAggregate(dailyWithWrongMonthly, inventoryAggregate);

		return inventoryAggregate;
	}

	// will add i1 to i2
	private void addInventoryAggregate(InventoryAggregate i1, InventoryAggregate i2) {
		if (i1 == null) {
			return;
		}
		i2.setZeroVariance(AppUtils.add(i1.getZeroVariance(), i2.getZeroVariance()));
		i2.setVarianceOthers(AppUtils.add(i1.getVarianceOthers(), i2.getVarianceOthers()));
		i2.setVariancePCC(AppUtils.add(i1.getVariancePCC(), i2.getVariancePCC()));
		i2.setVarianceYC(AppUtils.add(i1.getVarianceYC(), i2.getVarianceYC()));
		i2.setStockVariance(AppUtils.add(i1.getStockVariance(), i2.getStockVariance()));

	}

	private InventoryAggregate getWeeklyVarianceAggregated(int unitId, Date startDate, Date businessDate,
														   List<Integer> categories) {
		InventoryAggregate weekly = new InventoryAggregate();
		try {
			String queryString = "SELECT pd.varianceType, SUM(A.varianceCost), SUM(A.varianceTax) FROM SCMProductInventoryData A, ProductDefinitionData pd "
					+ "	WHERE A.stockType = :stockType AND A.eventType = :eventType AND A.currentDayCloseEvent.status = :status "
					+ " AND A.currentDayCloseEvent.businessDate >= :monthStart "
					+ " AND A.currentDayCloseEvent.businessDate <= :businessDate " + " AND pd.productId = A.productId "
					+ " AND A.categoryId IN (:categories) "
					+ " AND A.currentDayCloseEvent.unitId = :unitId GROUP BY pd.varianceType";
			Query query = manager.createQuery(queryString);
			query.setParameter("stockType", StockTakeType.WEEKLY.name());
			query.setParameter("eventType", StockEventType.STOCK_TAKE.name());
			query.setParameter("status", StockEventStatus.CLOSED.name());
			query.setParameter("businessDate", SCMUtil.getDate(businessDate));
			query.setParameter("monthStart", startDate);
			query.setParameter("unitId", unitId);
			query.setParameter("categories", categories);

			List<Object[]> resultSet = query.getResultList();
			setValues(weekly, resultSet);
		} catch (Exception e) {
			LOG.error("Error while Weekly Variance aggregate for unitId: {} , business Date {} , error: {}", unitId,
					businessDate, e.getMessage());
		}
		return weekly;
	}

	private InventoryAggregate getDailyVarianceAggregated(int unitId, Date startDate, Date businessDate,
														  List<Integer> categories) {

		InventoryAggregate daily = new InventoryAggregate();
		Integer eventId = getLastDayCloseEventIdOfType(unitId, StockTakeType.WEEKLY, StockEventType.STOCK_TAKE,
				businessDate);

		try {

			String queryString = "SELECT pd.varianceType, SUM(A.varianceCost), SUM(A.varianceTax) FROM SCMProductInventoryData A, ProductDefinitionData pd  "
					+ "	WHERE A.stockType = :stockType AND A.eventType = :eventType AND A.currentDayCloseEvent.status = :status "
					+ " AND A.currentDayCloseEvent.businessDate >= :monthStart "
					+ " AND A.currentDayCloseEvent.businessDate <= :businessDate "
					+ " AND A.currentDayCloseEvent.unitId = :unitId AND A.currentDayCloseEvent.eventId > :eventId "
					+ " AND pd.productId = A.productId AND A.categoryId IN (:categories) GROUP BY pd.varianceType";

			// group be omitted to get single aggregation
			Query query = manager.createQuery(queryString);
			query.setParameter("eventType", StockEventType.STOCK_TAKE.name());
			query.setParameter("stockType", StockTakeType.DAILY.name());
			query.setParameter("status", StockEventStatus.CLOSED.name());
			query.setParameter("businessDate", SCMUtil.getDate(businessDate));
			query.setParameter("monthStart", SCMUtil.getDate(startDate));
			query.setParameter("unitId", unitId);
			query.setParameter("eventId", eventId);
			query.setParameter("categories", categories);

			List<Object[]> resultSet = query.getResultList();
			setValues(daily, resultSet);
		} catch (NoResultException nre) {
			LOG.info("No results for Daily aggregate for unitId: {} , business Date {}", unitId, businessDate);
		} catch (Exception e) {
			LOG.error("Error while Daily Variance aggregate for unitId: {} , business Date {} , error: {}", unitId,
					businessDate, e.getMessage());
		}
		return daily;
	}

	private Integer getLastDayCloseEventIdOfType(int unitId, StockTakeType stockTakeType, StockEventType stockEventType,
												 Date businessDate) {
		try {
			String queryString = "SELECT MAX(eventId) FROM SCMDayCloseEventData E where E.unitId = :unitId "
					+ " and E.status = :status and E.dayCloseEventType = :stockEventType "
					+ " and E.eventFrequencyType = :stockTakeType and E.businessDate <= :businessDate AND E.businessDate >= :businessDate";

			Query query = manager.createQuery(queryString);

			query.setParameter("unitId", unitId);
			query.setParameter("status", StockEventStatus.CLOSED.name());
			query.setParameter("stockEventType", stockEventType.name());
			query.setParameter("stockTakeType", stockTakeType.name());
			query.setParameter("businessDate", AppUtils.getDate(businessDate));
			query.setParameter("startDate", AppUtils.getDate(AppUtils.getFirstDayOfMonth(businessDate)));
			return (Integer) query.getSingleResult();
		} catch (NoResultException nre) {
			LOG.info("No Results for day close event for unit {} of type {} ", unitId, stockTakeType);
		} catch (Exception e) {
			LOG.error("Could not find last day close event for unit {} of type {} ", unitId, stockTakeType);
		}
		return 0;
	}

	private Integer getFistDayCloseEventIdOfType(int unitId, StockTakeType stockTakeType, StockEventType stockEventType,
												 Date businessDate) {
		try {
			String queryString = "SELECT MIN(eventId) FROM SCMDayCloseEventData E where E.unitId = :unitId "
					+ " and E.status = :status and E.dayCloseEventType = :stockEventType "
					+ " and E.eventFrequencyType = :stockTakeType and E.businessDate <= :businessDate AND E.businessDate >= :startDate";

			Query query = manager.createQuery(queryString);

			query.setParameter("unitId", unitId);
			query.setParameter("status", StockEventStatus.CLOSED.name());
			query.setParameter("stockEventType", stockEventType.name());
			query.setParameter("stockTakeType", stockTakeType.name());
			query.setParameter("businessDate", AppUtils.getDate(businessDate));
			query.setParameter("startDate", AppUtils.getDate(AppUtils.getFirstDayOfMonth(businessDate)));
			return (Integer) query.getSingleResult();
		} catch (NoResultException nre) {
			LOG.info("No results for last day close event for unit {} of type {} ", unitId, stockTakeType);
		} catch (Exception e) {
			LOG.error("Could not find last day close event for unit {} of type {} ", unitId, stockTakeType);
		}
		return null;
	}

	public Integer getDayCloseEventIdOfType(int unitId, StockTakeType stockTakeType, StockEventType stockEventType,
											Date startDate, Date endDate) {
		try {
			String queryString = "SELECT MIN(eventId) FROM SCMDayCloseEventData E where E.unitId = :unitId "
					+ " and E.status = :status and E.dayCloseEventType = :stockEventType "
					+ " and E.eventFrequencyType = :stockTakeType and E.businessDate <= :endDate AND E.businessDate >= :startDate";

			Query query = manager.createQuery(queryString);

			query.setParameter("unitId", unitId);
			query.setParameter("status", StockEventStatus.CLOSED.name());
			query.setParameter("stockEventType", stockEventType.name());
			query.setParameter("stockTakeType", stockTakeType.name());
			query.setParameter("endDate", AppUtils.getDate(endDate));
			query.setParameter("startDate", AppUtils.getDate(startDate));
			return (Integer) query.getSingleResult();
		} catch (NoResultException nre) {
			LOG.info("No results for last day close event for unit {} of type {} ", unitId, stockTakeType);
		} catch (Exception e) {
			LOG.error("Could not find last day close event for unit {} of type {} ", unitId, stockTakeType);
		}
		return null;
	}

	private InventoryAggregate getMonthlyVarianceAggregated(int unitId, int eventId, List<Integer> categories) {
		InventoryAggregate monthly = new InventoryAggregate();
		try {
			String queryString = "SELECT pd.varianceType, SUM(A.varianceCost), SUM(A.varianceTax) FROM SCMProductInventoryData A, ProductDefinitionData pd "
					+ "	WHERE A.stockType = :stockType AND A.eventType = :eventType AND A.currentDayCloseEvent.status = :status "
					+ " AND A.currentDayCloseEvent.unitId = :unitId AND pd.productId = A.productId "
					+ " AND A.categoryId IN (:categories) "
					+ " AND A.currentDayCloseEvent.eventId = :eventId GROUP BY pd.varianceType";

			Query query = manager.createQuery(queryString);
			query.setParameter("eventType", StockEventType.STOCK_TAKE.name());
			query.setParameter("stockType", StockTakeType.MONTHLY.name());
			query.setParameter("status", StockEventStatus.CLOSED.name());
			query.setParameter("eventId", eventId);
			query.setParameter("unitId", unitId);
			query.setParameter("categories", categories);

			List<Object[]> resultSet = query.getResultList();
			setValues(monthly, resultSet);
		} catch (Exception e) {
			LOG.error("Error while Monthly Variance aggregate for unitId: {} , eventId {} , error: {}", unitId, eventId,
					e.getMessage());
		}
		return monthly;
	}

	@Override
	public Integer getSCMDayCloseEventForBusinessDate(int unitId, Date businessDate) {

		List<String> frequencyList = new ArrayList<>();
		frequencyList.add(StockTakeType.DAILY.name());
		frequencyList.add(StockTakeType.WEEKLY.name());
		frequencyList.add(StockTakeType.MONTHLY.name());

		try {
			String queryString = "SELECT eventId FROM SCMDayCloseEventData F "
					+ "WHERE F.unitId = :unitId and F.dayCloseEventType  = :dayCloseEventType "
					+ "and F.status = :status and F.businessDate = :businessDate AND F.eventFrequencyType IN :frequencyList ";
			Query query = manager.createQuery(queryString).setParameter("unitId", unitId)
					.setParameter("dayCloseEventType", StockEventType.STOCK_TAKE.name())
					.setParameter("status", StockEventStatus.CLOSED.name())
					.setParameter("businessDate", SCMUtil.getDate(businessDate))
					.setParameter("frequencyList", frequencyList);

			return (Integer) query.getSingleResult();
		} catch (NoResultException nre) {
			LOG.error("Could not find any closed Day closing event for the unit ::::: {}", unitId);
		} catch (Exception e) {
			LOG.error("Could not find any closed Day closing event for the unit ::::: {}", unitId, e);
		}
		return null;
	}

	@Override
	public Map<Integer, Pair<BigDecimal, BigDecimal>> getConsumableAggregate(int unitId, Date businessDate,
																			 StockTakeType type) {
		Map<Integer, Pair<BigDecimal, BigDecimal>> map = new HashMap<>();
		List<Pair<Integer, BigDecimal>> grs = getConsumableAggregate(unitId, businessDate, type, false);
		for (Pair<Integer, BigDecimal> l : grs) {
			if (!map.containsKey(l.getKey())) {
				map.put(l.getKey(), new Pair<>(BigDecimal.ZERO, BigDecimal.ZERO));
			}
			map.get(l.getKey()).setKey(l.getValue());
		}

		List<Pair<Integer, BigDecimal>> grs1 = getConsumableAggregate(unitId, businessDate, type, true);
		for (Pair<Integer, BigDecimal> l : grs1) {
			if (!map.containsKey(l.getKey())) {
				map.put(l.getKey(), new Pair<>(BigDecimal.ZERO, BigDecimal.ZERO));
			}
			map.get(l.getKey()).setValue(l.getValue());
		}
		return map;
	}


	public List<Pair<Integer, BigDecimal>> getConsumableAggregate(int unitId, Date businessDate, StockTakeType type,
																  boolean transfers) {

		List<Pair<Integer, BigDecimal>> list = new ArrayList<>();

		// Integer eventId = getDayCloseEvent(unitId, businessDate, type);
		// SCMDayCloseEventRangeData range = getSCMDayCloseEventRangeData(eventId,
		// StockEventType.RECEIVED, type);

		Date startTime = SCMUtil.getStartOfBusinessDay(businessDate);
		Date endTime = SCMUtil.getEndOfBusinessDay(businessDate);

		if (SCMUtil.isMonthly(type)) {
			startTime = SCMUtil.getStartOfBusinessDay(SCMUtil.getFirstDayOfMonth(businessDate));
			endTime = SCMUtil.getEndOfBusinessDay(SCMUtil.getLastDayOfThisMonth(businessDate));
		}

		try {
			String queryString = "SELECT A.subCategoryId, SUM(A.calculatedAmount) "
					+ " FROM GoodsReceivedItemData A, SkuDefinitionData B WHERE A.skuId = B.skuId "
					+ (transfers ? " AND A.goodsReceivedData.generationUnitId = :unitId "
					: " AND A.goodsReceivedData.generatedForUnitId = :unitId ")
					+ " AND A.goodsReceivedData.lastUpdateTime >= :startTime AND A.goodsReceivedData.lastUpdateTime < :endTime "
					+ " AND A.goodsReceivedData.status = :status "
					+ " AND A.categoryId = :categoryId  "
					+ " GROUP BY A.subCategoryId ";
			Query query = manager.createQuery(queryString);
			query.setParameter("unitId", unitId);
			query.setParameter("startTime", startTime);
			query.setParameter("endTime", endTime);
			query.setParameter("status", SCMOrderStatus.SETTLED.name());
			query.setParameter("categoryId", SCMServiceConstants.CATEGORY_CONSUMABLE);
			List<Object[]> resultSet = query.getResultList();
			for (Object[] record : resultSet) {
				list.add(new Pair<>((Integer) record[0], record[1] == null ? BigDecimal.ZERO : (BigDecimal) record[1]));
			}
			return list;
		} catch (Exception e) {
			LOG.error("Error while calculation of consumable aggregate for event ::::: {}", e);
			return null;
		}
	}

	@Override
	public Map<Integer, Pair<BigDecimal, BigDecimal>> getConsumableTaxAggregate(int unitId, Date businessDate,
																				StockTakeType type) {
		Map<Integer, Pair<BigDecimal, BigDecimal>> map = new HashMap<>();
		List<Pair<Integer, BigDecimal>> grs = getConsumableTaxAggregate(unitId, businessDate, type, false);
		for (Pair<Integer, BigDecimal> l : grs) {
			if (!map.containsKey(l.getKey())) {
				map.put(l.getKey(), new Pair<>(BigDecimal.ZERO, BigDecimal.ZERO));
			}
			map.get(l.getKey()).setKey(l.getValue());
		}

		List<Pair<Integer, BigDecimal>> grs1 = getConsumableTaxAggregate(unitId, businessDate, type, true);
		for (Pair<Integer, BigDecimal> l : grs1) {
			if (!map.containsKey(l.getKey())) {
				map.put(l.getKey(), new Pair<>(BigDecimal.ZERO, BigDecimal.ZERO));
			}
			map.get(l.getKey()).setValue(l.getValue());
		}
		return map;
	}

	public List<Pair<Integer, BigDecimal>> getConsumableTaxAggregate(int unitId, Date businessDate,
																	 StockTakeType stockTakeType, boolean transfers) {

		List<Pair<Integer, BigDecimal>> list = new ArrayList<>();

		Date startTime = SCMUtil.getStartOfBusinessDay(businessDate);
		Date endTime = SCMUtil.getEndOfBusinessDay(businessDate);

		if (SCMUtil.isMonthly(stockTakeType)) {
			startTime = SCMUtil.getStartOfBusinessDay(SCMUtil.getFirstDayOfMonth(businessDate));
			endTime = SCMUtil.getEndOfBusinessDay(SCMUtil.getLastDayOfThisMonth(businessDate));
		}

		try {
			String queryString = "SELECT C.subCategoryId, SUM(A.taxAmount) "
					+ " FROM TransferOrderItemData A,  GoodsReceivedItemData C, SkuDefinitionData B WHERE A.skuId = B.skuId AND C.transferOrderItemData.id = A.id"
					+ (transfers ? " AND A.transferOrderData.generationUnitId = :unitId "
					: " AND A.transferOrderData.generatedForUnitId = :unitId ")
					+ " AND C.goodsReceivedData.lastUpdateTime >= :startTime AND C.goodsReceivedData.lastUpdateTime < :endTime "
					+ " AND A.transferOrderData.status = :status "
					+ " AND C.categoryId = :categoryId  "
					+ " GROUP BY C.subCategoryId";
			Query query = manager.createQuery(queryString);
			query.setParameter("unitId", unitId);
			query.setParameter("startTime", startTime);
			query.setParameter("endTime", endTime);
			query.setParameter("status", SCMOrderStatus.SETTLED.name());
			query.setParameter("categoryId", SCMServiceConstants.CATEGORY_CONSUMABLE);
			List<Object[]> resultSet = query.getResultList();
			for (Object[] record : resultSet) {
				list.add(new Pair<>((Integer) record[0], record[1] == null ? BigDecimal.ZERO : (BigDecimal) record[1]));
			}
			return list;
		} catch (Exception e) {
			LOG.error("Error while calculation of consumable aggregate for event ::::: {}", e);
			return null;
		}
	}

	@Override
	public BigDecimal getFATaxAggregate(int unitId, Date businessDate, ThresholdType type, BigDecimal threshold,
										StockTakeType stockTakeType) {

		Date startTime = SCMUtil.getStartOfBusinessDay(businessDate);
		Date endTime = SCMUtil.getEndOfBusinessDay(businessDate);

		if (SCMUtil.isMonthly(stockTakeType)) {
			startTime = SCMUtil.getStartOfBusinessDay(SCMUtil.getFirstDayOfMonth(businessDate));
			endTime = SCMUtil.getEndOfBusinessDay(SCMUtil.getLastDayOfThisMonth(businessDate));
		}

		try {
			String queryString = "SELECT SUM(A.taxAmount) "
					+ " FROM TransferOrderItemData A, SkuDefinitionData B, GoodsReceivedItemData C "
					+ " WHERE A.skuId = B.skuId "
					+ " AND A.transferOrderData.generatedForUnitId = :unitId AND C.transferOrderItemData.id = A.id"
					+ " AND C.goodsReceivedData.lastUpdateTime >= :startTime AND C.goodsReceivedData.lastUpdateTime < :endTime "
					+ " AND A.transferOrderData.status = :status ";
			if (ThresholdType.BELOW.equals(type)) {
				queryString = queryString + " AND A.unitPrice <= :threshold ";
			} else if (ThresholdType.ABOVE.equals(type)) {
				queryString = queryString + " AND A.unitPrice > :threshold ";
			}
			queryString = queryString + " AND B.linkedProduct.categoryDefinition.categoryId = :categoryId  ";
			Query query = manager.createQuery(queryString);
			query.setParameter("unitId", unitId);
			query.setParameter("startTime", startTime);
			query.setParameter("endTime", endTime);
			query.setParameter("status", SCMOrderStatus.SETTLED.name());
			query.setParameter("threshold", threshold);
			query.setParameter("categoryId", SCMServiceConstants.CATEGORY_FIXED_ASSETS);
			Object resultSet = query.getSingleResult();
			return resultSet != null ? (BigDecimal) resultSet : BigDecimal.ZERO;
		} catch (Exception e) {
			LOG.error("Error while calculation of tax aggregate for FA for event ::::: {}", e);
			return BigDecimal.ZERO;
		}
	}

	@Override
	public Map<Integer, BigDecimal> getFATaxAggregate(int unitId, Date businessDate, ThresholdType type, BigDecimal threshold,
													  StockTakeType stockTakeType, Date handoverDate, boolean beforehandOver) {

		Date startTime = SCMUtil.getStartOfBusinessDay(businessDate);
		Date endTime = SCMUtil.getEndOfBusinessDay(businessDate);

		if (SCMUtil.isMonthly(stockTakeType)) {
			startTime = SCMUtil.getStartOfBusinessDay(SCMUtil.getFirstDayOfMonth(businessDate));
			endTime = SCMUtil.getEndOfBusinessDay(SCMUtil.getLastDayOfThisMonth(businessDate));
		}

		String queryHelperString = beforehandOver ? " AD.startDate < handoverDate " : " AD.startDate >= handoverDate ";
		try {
			String queryString = "SELECT P.subCategoryDefinition.id,  SUM(A.taxAmount)  "
					+ " FROM TransferOrderItemData A, SkuDefinitionData B, GoodsReceivedItemData C, ProductDefinitionData P, AssetDefinitionData AD "
					+ " WHERE A.skuId = B.skuId "
					+ " AND B.linkedProduct.productId = P.productId "

					+ " AND A.transferOrderData.generatedForUnitId = :unitId AND C.transferOrderItemData.id = A.id"
					+ " AND C.goodsReceivedData.lastUpdateTime >= :startTime AND C.goodsReceivedData.lastUpdateTime < :endTime "
					+ " AND A.associatedAssetId = AD.assetId "
					+ " AND " + queryHelperString
					+ " AND A.transferOrderData.status = :status group by P.subCategoryDefinition.id ";
			if (ThresholdType.BELOW.equals(type)) {
				queryString = queryString + " AND A.unitPrice <= :threshold ";
			} else if (ThresholdType.ABOVE.equals(type)) {
				queryString = queryString + " AND A.unitPrice > :threshold ";
			}
			queryString = queryString + " AND B.linkedProduct.categoryDefinition.categoryId = :categoryId  ";
			Query query = manager.createQuery(queryString);
			query.setParameter("unitId", unitId);
			query.setParameter("startTime", startTime);
			query.setParameter("endTime", endTime);
			query.setParameter("status", SCMOrderStatus.SETTLED.name());
			query.setParameter("threshold", threshold);
			query.setParameter("categoryId", SCMServiceConstants.CATEGORY_FIXED_ASSETS);
			List<Object[]> resultSet = query.getResultList();
			if (resultSet == null) {
				resultSet = new ArrayList<>();
			}
			Map<Integer, BigDecimal> subCategoryIdTaxMap = new HashMap<>();
			for (Object[] array : resultSet) {
				subCategoryIdTaxMap.put((Integer) array[0], array[1] != null ? new BigDecimal((Double) array[1]) : java.math.BigDecimal.ZERO);
			}
			return subCategoryIdTaxMap;
		} catch (Exception e) {
			LOG.error("Error while calculation of tax aggregate for FA for event ::::: {}", e);
			return new HashMap<>();
		}
	}

	@Override
	public BigDecimal getTaxAggregate(int unitId, Date businessDate, List<Integer> categories,
									  StockTakeType stockTakeType) {

		Date startTime = SCMUtil.getStartOfBusinessDay(businessDate);
		Date endTime = SCMUtil.getEndOfBusinessDay(businessDate);

		if (SCMUtil.isMonthly(stockTakeType)) {
			startTime = SCMUtil.getStartOfBusinessDay(SCMUtil.getFirstDayOfMonth(businessDate));
			endTime = SCMUtil.getEndOfBusinessDay(SCMUtil.getLastDayOfThisMonth(businessDate));
		}

		try {
			String queryString = "SELECT SUM(A.taxAmount) FROM TransferOrderItemData A, SkuDefinitionData B, GoodsReceivedItemData C"
					+ " WHERE A.skuId = B.skuId AND A.transferOrderData.generatedForUnitId = :unitId AND C.transferOrderItemData.id = A.id "
					+ " AND C.goodsReceivedData.lastUpdateTime >= :startTime AND C.goodsReceivedData.lastUpdateTime < :endTime "
					+ " AND A.transferOrderData.status = :status AND A.transferOrderData.transferType = :transferType "
					+ " AND B.linkedProduct.categoryDefinition.categoryId IN (:categoryIds) ";
			Query query = manager.createQuery(queryString);
			query.setParameter("unitId", unitId);
			query.setParameter("startTime", startTime);
			query.setParameter("endTime", endTime);
			query.setParameter("status", SCMOrderStatus.SETTLED.name());
			query.setParameter("categoryIds", categories);
			query.setParameter("transferType", OrderTransferType.INVOICE.name());
			Object resultSet = query.getSingleResult();
			return resultSet != null ? (BigDecimal) resultSet : BigDecimal.ZERO;
		} catch (Exception e) {
			LOG.error("Error while calculation of tax aggregate for Categories for event ::::: {}", e);
			return BigDecimal.ZERO;
		}
	}

	@Override
	public boolean cancelAllPreviousEvents(int unitId, Date businessDate) {
		boolean flag = false;
		try {
			StringBuilder queryStr = new StringBuilder("UPDATE SCMDayCloseEventData");
			queryStr.append(" SET status = :status");
			queryStr.append(" WHERE unitId = :unitId AND businessDate= :businessDate AND status = :eventStatus");
			queryStr.append(" AND eventFrequencyType = :eventFrequencyType AND dayCloseEventType = :eventType");
			Query query = manager.createQuery(queryStr.toString())
					.setParameter("status", StockEventStatus.CANCELLED.name()).setParameter("unitId", unitId)
					.setParameter("businessDate", businessDate)
					.setParameter("eventStatus", StockEventStatus.INITIATED.name())
					.setParameter("eventFrequencyType", StockTakeType.DAILY.name())
					.setParameter("eventType", StockEventType.CLOSING.name());
			query.executeUpdate();
			flag = true;
		} catch (Exception e) {
			LOG.error("Error encountered  while cancelling all previous day close events", e);
		}
		return flag;
	}

	@Override
	public ConsumablesAggregate getFixedAssetsAggregate(int unitId, Date businessDate, StockTakeType type, Date handoverDate) {

		ConsumablesAggregate consumablesAggregate = new ConsumablesAggregate();
		BigDecimal zero = BigDecimal.ZERO;
		consumablesAggregate.setFixedAssetsEquipmentTax(zero);
		consumablesAggregate.setFixedAssetFurnitureTax(zero);
		consumablesAggregate.setFixedAssetsItTax(zero);
		consumablesAggregate.setFixedAssetsKitchenEquipmentTax(zero);
		consumablesAggregate.setFixedAssetsOfficeEquipmentTax(zero);
		consumablesAggregate.setFixedAssetsVehicleTax(zero);
		consumablesAggregate.setFixedAssetsOthersSubCategoryCafeTax(zero);

		consumablesAggregate.setFixedAssetsEquipmentHqTax(zero);
		consumablesAggregate.setFixedAssetFurnitureHqTax(zero);
		consumablesAggregate.setFixedAssetsItHqTax(zero);
		consumablesAggregate.setFixedAssetsKitchenEquipmentHqTax(zero);
		consumablesAggregate.setFixedAssetsOfficeEquipmentHqTax(zero);
		consumablesAggregate.setFixedAssetsVehicleHqTax(zero);
		consumablesAggregate.setFixedAssetsOthersSubCategoryHqTax(zero);

		consumablesAggregate.setFixedAssetsEquipmentHq(zero);
		consumablesAggregate.setFixedAssetFurnitureHq(zero);
		consumablesAggregate.setFixedAssetsItHq(zero);
		consumablesAggregate.setFixedAssetsKitchenEquipmentHq(zero);
		consumablesAggregate.setFixedAssetsOfficeEquipmentHq(zero);
		consumablesAggregate.setFixedAssetsVehicleHq(zero);
		consumablesAggregate.setFixedAssetsOthersSubCategoryHq(zero);

		/*Integer eventId = getDayCloseEvent(unitId, businessDate, type);
		SCMDayCloseEventRangeData range = getSCMDayCloseEventRangeData(eventId, StockEventType.RECEIVED, type);

		if (range == null) {
			return consumablesAggregate;
		}*/


		Date startTime = SCMUtil.getStartOfBusinessDay(businessDate);
		Date endTime = SCMUtil.getEndOfBusinessDay(businessDate);

		if (SCMUtil.isMonthly(type)) {
			startTime = SCMUtil.getStartOfBusinessDay(SCMUtil.getFirstDayOfMonth(businessDate));
			endTime = SCMUtil.getEndOfBusinessDay(SCMUtil.getLastDayOfThisMonth(businessDate));
		}

//		+ " SUM(CASE WHEN A.unitPrice > :threshold THEN A.calculatedAmount ELSE 0 END) ,  "
//				+ " SUM(CASE WHEN A.unitPrice <= :threshold THEN A.calculatedAmount ELSE 0 END) , "

		try {
			//capex > threshold
			String queryString = "SELECT A.categoryId,A.subCategoryId,"
					+ " SUM(CASE WHEN A.goodsReceivedData.generationTime > :handoverDate THEN A.calculatedAmount ELSE 0 END) ,  "
					+ " SUM(CASE WHEN A.goodsReceivedData.generationTime <= :handoverDate THEN A.calculatedAmount ELSE 0 END) , "
					+ " SUM(CASE WHEN A.goodsReceivedData.generationTime > :handoverDate AND A.transferOrderItemData is not null THEN A.transferOrderItemData.taxAmount ELSE 0 END ), "
					+ " SUM(CASE WHEN A.goodsReceivedData.generationTime <= :handoverDate AND A.transferOrderItemData is not null THEN A.transferOrderItemData.taxAmount ELSE 0 END ) "
					+ " FROM GoodsReceivedItemData A, SkuDefinitionData B WHERE A.skuId = B.skuId "
					+ " AND A.goodsReceivedData.generatedForUnitId = :unitId "
					+ " AND A.goodsReceivedData.lastUpdateTime >= :startTime AND A.goodsReceivedData.lastUpdateTime <= :endTime "
					+ " AND A.goodsReceivedData.status = :status "
					+ " AND A.categoryId = :categoryId  "
					//+ " AND B.linkedProduct.participatesInPnl = :participatesInPnl  "
					+ " GROUP BY A.categoryId, A.subCategoryId";
			Query query = manager.createQuery(queryString);
			query.setParameter("unitId", unitId);
			query.setParameter("startTime", startTime);
			query.setParameter("endTime", endTime);
			query.setParameter("status", SCMOrderStatus.SETTLED.name());
			query.setParameter("categoryId", SCMServiceConstants.CATEGORY_FIXED_ASSETS);
			// TODO check this is used
			//query.setParameter("participatesInPnl", AppConstants.YES);
			//query.setParameter("threshold", SCMServiceConstants.FA_PRICE_THRESHOLD);
			query.setParameter("handoverDate", handoverDate);
			List<Object[]> resultSet = query.getResultList();
			if (resultSet != null && !resultSet.isEmpty()) {
				for (Object[] record : resultSet) {
					Integer subcategory = (Integer) record[1];

					//if(!AppUtils.isBefore(grDate, handoverDate)){
					consumablesAggregate.setFixedAssetsCapex(
							AppUtils.add(consumablesAggregate.getFixedAssetsCapex(), (BigDecimal) record[2]));
					consumablesAggregate.setFixedAssets(
							AppUtils.add(consumablesAggregate.getFixedAssets(), (BigDecimal) record[3]));

					switch (subcategory) {
						case SCMServiceConstants.SUB_CATEGORY_FA_EQUIPMENT:
							consumablesAggregate.setFixedAssetsEquipment((BigDecimal) record[2]);
							consumablesAggregate.setFixedAssetsEquipmentTax((BigDecimal) record[4]);
							consumablesAggregate.setFixedAssetsEquipmentHq((BigDecimal) record[3]);
							consumablesAggregate.setFixedAssetsEquipmentHqTax((BigDecimal) record[5]);
							break;
						case SCMServiceConstants.SUB_CATEGORY_FA_FURNITURE:
							consumablesAggregate.setFixedAssetFurniture((BigDecimal) record[2]);
							consumablesAggregate.setFixedAssetFurnitureTax((BigDecimal) record[4]);
							consumablesAggregate.setFixedAssetFurnitureHq((BigDecimal) record[3]);
							consumablesAggregate.setFixedAssetFurnitureHqTax((BigDecimal) record[5]);
							break;
						case SCMServiceConstants.SUB_CATEGORY_FA_IT:
							consumablesAggregate.setFixedAssetsIT((BigDecimal) record[2]);
							consumablesAggregate.setFixedAssetsItTax((BigDecimal) record[4]);
							consumablesAggregate.setFixedAssetsItHq((BigDecimal) record[3]);
							consumablesAggregate.setFixedAssetsItHqTax((BigDecimal) record[5]);
							break;
						case SCMServiceConstants.SUB_CATEGORY_FA_KITCHEN:
							consumablesAggregate.setFixedAssetsKitchenEquipment((BigDecimal) record[2]);
							consumablesAggregate.setFixedAssetsKitchenEquipmentTax((BigDecimal) record[4]);
							consumablesAggregate.setFixedAssetsKitchenEquipmentHq((BigDecimal) record[3]);
							consumablesAggregate.setFixedAssetsKitchenEquipmentHqTax((BigDecimal) record[5]);
							break;
						case SCMServiceConstants.SUB_CATEGORY_FA_OFFICE:
							consumablesAggregate.setFixedAssetsOfficeEquipment((BigDecimal) record[2]);
							consumablesAggregate.setFixedAssetsOfficeEquipmentTax((BigDecimal) record[4]);
							consumablesAggregate.setFixedAssetsOfficeEquipmentHq((BigDecimal) record[3]);
							consumablesAggregate.setFixedAssetsOfficeEquipmentHqTax((BigDecimal) record[5]);
							break;
						case SCMServiceConstants.SUB_CATEGORY_FA_VEHICLE:
							consumablesAggregate.setFixedAssetsVehicle((BigDecimal) record[2]);
							consumablesAggregate.setFixedAssetsVehicleTax((BigDecimal) record[4]);
							consumablesAggregate.setFixedAssetsVehicleHq((BigDecimal) record[3]);
							consumablesAggregate.setFixedAssetsVehicleHqTax((BigDecimal) record[5]);
							break;

						default:
							consumablesAggregate.setFixedAssetsOthersSubCategory(AppUtils
									.add(consumablesAggregate.getFixedAssetsOthersSubCategory(), (BigDecimal) record[2]));
							consumablesAggregate.setFixedAssetsOthersSubCategoryCafeTax(
									AppUtils.add((BigDecimal) record[4], consumablesAggregate.getFixedAssetsOthersSubCategoryCafeTax()));
							consumablesAggregate.setFixedAssetsOthersSubCategoryHq(AppUtils
									.add(consumablesAggregate.getFixedAssetsOthersSubCategoryHq(), (BigDecimal) record[3]));
							consumablesAggregate.setFixedAssetsOthersSubCategoryHqTax(
									AppUtils.add((BigDecimal) record[5], consumablesAggregate.getFixedAssetsOthersSubCategoryHqTax()));
					}
				}
			}
			return consumablesAggregate;
		} catch (Exception e) {
			LOG.error("Error while calculation of consumable aggregate for event ::::: {}", e);
			return null;
		}
	}

	@Override
	public List<Pair<Integer, BigDecimal>> getConsumableAggregateForMonth(int unitId, Date monthStart,
																		  Date businessDate) {

		List<Pair<Integer, BigDecimal>> list = new ArrayList<>();

		try {
			String queryString = "SELECT B.linkedProduct.subCategoryDefinition.id, SUM(A.calculatedAmount)"
					+ " FROM GoodsReceivedItemData A, SkuDefinitionData B" + " WHERE A.skuId = B.skuId "
					+ " AND A.goodsReceivedData.generatedForUnitId = :unitId "
					+ " AND A.goodsReceivedData.lastUpdateTime >= :startTime "
					+ " AND A.goodsReceivedData.lastUpdateTime < :endTime "
					+ " AND A.goodsReceivedData.status != :status "
					+ " AND B.linkedProduct.categoryDefinition.categoryId = :categoryId  "
					+ " GROUP BY B.linkedProduct.subCategoryDefinition.id ";
			Query query = manager.createQuery(queryString);
			query.setParameter("unitId", unitId);
			query.setParameter("startTime", SCMUtil.getStartOfBusinessDay(monthStart));
			query.setParameter("endTime", SCMUtil.getEndOfBusinessDay(businessDate));
			query.setParameter("status", SCMOrderStatus.CANCELLED.name());
			query.setParameter("categoryId", SCMServiceConstants.CATEGORY_CONSUMABLE);
			List<Object[]> resultSet = query.getResultList();
			for (Object[] record : resultSet) {
				list.add(new Pair<>((Integer) record[0], record[1] == null ? BigDecimal.ZERO : (BigDecimal) record[1]));
			}
			return list;
		} catch (Exception e) {
			LOG.error("Error while calculation of consumable aggregate for month  ::::: {}", e);
			return null;
		}
	}

	@Override
	public BigDecimal getFixedAssetsAggregateForMonth(int unitId, Date monthStartDate, Date businessDate) {

		List<Pair<Integer, BigDecimal>> list = new ArrayList<>();

		try {
			String queryString = "SELECT B.linkedProduct.categoryDefinition.categoryId, SUM(A.calculatedAmount) "
					+ " FROM GoodsReceivedItemData A, SkuDefinitionData B" + " WHERE A.skuId = B.skuId "
					+ " AND A.goodsReceivedData.generatedForUnitId = :unitId "
					+ " AND A.goodsReceivedData.lastUpdateTime >= :startTime "
					+ " AND A.goodsReceivedData.lastUpdateTime < :endTime "
					+ " AND A.goodsReceivedData.status != :status "
					+ " AND B.linkedProduct.categoryDefinition.categoryId = :categoryId  "
					+ " AND B.linkedProduct.participatesInPnl = :participatesInPnl  "
					+ " GROUP BY B.linkedProduct.categoryDefinition.categoryId ";
			Query query = manager.createQuery(queryString);
			query.setParameter("unitId", unitId);
			query.setParameter("startTime", SCMUtil.getStartOfBusinessDay(monthStartDate));
			query.setParameter("endTime", SCMUtil.getEndOfBusinessDay(businessDate));
			query.setParameter("status", SCMOrderStatus.CANCELLED.name());
			query.setParameter("categoryId", SCMServiceConstants.CATEGORY_FIXED_ASSETS);
			query.setParameter("participatesInPnl", AppConstants.YES);
			List<Object[]> resultSet = query.getResultList();
			for (Object[] record : resultSet) {
				list.add(new Pair<>((Integer) record[0], record[1] == null ? BigDecimal.ZERO : (BigDecimal) record[1]));
			}
			if (list.isEmpty()) {
				return BigDecimal.ZERO;
			}
			return list.get(0).getValue();
		} catch (Exception e) {
			LOG.error("Error while calculation of  fixed asset for month ::::: {}", e);
			return null;
		}
	}

	@Override
	public List<Pair<Integer, BigDecimal>> getRequestedConsumableAggregate(int unitId, Date monthStartDate,
																		   Date businessDate) {
		List<Pair<Integer, BigDecimal>> list = new ArrayList<>();

		try {
			String queryString = "SELECT B.subCategoryDefinition.id, SUM(A.calculatedAmount) "
					+ " FROM RequestOrderItemData A, ProductDefinitionData B" + " WHERE A.productId = B.productId "
					+ " AND A.requestOrderData.requestUnitId = :unitId "
					+ " AND A.requestOrderData.lastUpdateTime >= :startTime "
					+ " AND A.requestOrderData.lastUpdateTime < :endTime "
					+ " AND A.requestOrderData.status IN :statusList "
					+ " AND B.categoryDefinition.categoryId = :categoryId  " + " GROUP BY B.subCategoryDefinition.id ";
			Query query = manager.createQuery(queryString);
			query.setParameter("unitId", unitId);
			query.setParameter("startTime", SCMUtil.getStartOfBusinessDay(monthStartDate));
			query.setParameter("endTime", SCMUtil.getEndOfBusinessDay(businessDate));
			List<String> statusList = new ArrayList<>();
			statusList.add(SCMOrderStatus.ACKNOWLEDGED.name());
			statusList.add(SCMOrderStatus.CREATED.name());
			query.setParameter("statusList", statusList);
			query.setParameter("categoryId", SCMServiceConstants.CATEGORY_CONSUMABLE);
			List<Object[]> resultSet = query.getResultList();
			for (Object[] record : resultSet) {
				list.add(new Pair<>((Integer) record[0], record[1] == null ? BigDecimal.ZERO : (BigDecimal) record[1]));
			}
			return list;
		} catch (Exception e) {
			LOG.error("Error while calculation of requested consumable aggregate for month  ::::: {}", e);
			return null;
		}
	}

	@Override
	public BigDecimal getRequestedFixedAssetsAggregate(int unitId, Date monthStartDate, Date businessDate) {
		List<Pair<Integer, BigDecimal>> list = new ArrayList<>();

		try {
			String queryString = "SELECT B.categoryDefinition.categoryId, SUM(A.calculatedAmount) "
					+ " FROM RequestOrderItemData A, ProductDefinitionData B" + " WHERE A.productId = B.productId "
					+ " AND A.requestOrderData.requestUnitId  = :unitId "
					+ " AND A.requestOrderData.lastUpdateTime >= :startTime "
					+ " AND A.requestOrderData.lastUpdateTime < :endTime "
					+ " AND A.requestOrderData.status IN :statusList "
					+ " AND B.categoryDefinition.categoryId = :categoryId  "
					+ " AND B.participatesInPnl = :participatesInPnl  " + " GROUP BY B.categoryDefinition.categoryId ";
			Query query = manager.createQuery(queryString);
			query.setParameter("unitId", unitId);
			query.setParameter("startTime", SCMUtil.getStartOfBusinessDay(monthStartDate));
			query.setParameter("endTime", SCMUtil.getEndOfBusinessDay(businessDate));
			List<String> statusList = new ArrayList<>();
			statusList.add(SCMOrderStatus.ACKNOWLEDGED.name());
			statusList.add(SCMOrderStatus.CREATED.name());
			query.setParameter("statusList", statusList);
			query.setParameter("categoryId", SCMServiceConstants.CATEGORY_FIXED_ASSETS);
			query.setParameter("participatesInPnl", AppConstants.YES);
			List<Object[]> resultSet = query.getResultList();
			for (Object[] record : resultSet) {
				list.add(new Pair<>((Integer) record[0], record[1] == null ? BigDecimal.ZERO : (BigDecimal) record[1]));
			}
			if (list.isEmpty()) {
				return BigDecimal.ZERO;
			}
			return list.get(0).getValue();
		} catch (Exception e) {
			LOG.error("Error while calculation of requested  fixed asset for month ::::: {}", e);
			return null;
		}
	}

	@Override
	public List<Pair<Integer, BigDecimal>> getTransferredConsumableAggregateForMonth(int unitId, Date monthStartDate,
																					 Date businessDate) {
		List<Pair<Integer, BigDecimal>> list = new ArrayList<>();

		try {
			StringBuilder sb = new StringBuilder(
					"SELECT B.linkedProduct.subCategoryDefinition.id, SUM(A.calculatedAmount) ");
			sb.append(" FROM TransferOrderItemData A, SkuDefinitionData B" + " WHERE A.skuId = B.skuId ");
			sb.append(" AND A.transferOrderData.generatedForUnitId = :unitId ");
			sb.append(" AND A.transferOrderData.lastUpdateTime >= :startTime ");
			sb.append(" AND A.transferOrderData.lastUpdateTime < :endTime ");
			sb.append(" AND A.transferOrderData.status = :status ");
			sb.append(" AND B.linkedProduct.categoryDefinition.categoryId = :categoryId  ");
			sb.append(" GROUP BY B.linkedProduct.subCategoryDefinition.id ");
			Query query = manager.createQuery(sb.toString());
			query.setParameter("unitId", unitId);
			query.setParameter("startTime", SCMUtil.getStartOfBusinessDay(monthStartDate));
			query.setParameter("endTime", SCMUtil.getEndOfBusinessDay(businessDate));
			query.setParameter("status", SCMOrderStatus.CREATED.name());
			query.setParameter("categoryId", SCMServiceConstants.CATEGORY_CONSUMABLE);
			List<Object[]> resultSet = query.getResultList();
			for (Object[] record : resultSet) {
				list.add(new Pair<>((Integer) record[0], record[1] == null ? BigDecimal.ZERO : (BigDecimal) record[1]));
			}
			return list;
		} catch (Exception e) {
			LOG.error("Error while calculation of transferred consumable aggregate for month  ::::: {}", e);
			return null;
		}
	}

	@Override
	public BigDecimal getTransferredFixedAssetsForMonth(int unitId, Date monthStartDate, Date businessDate) {
		List<Pair<Integer, BigDecimal>> list = new ArrayList<>();

		try {
			StringBuilder sb = new StringBuilder(
					"SELECT B.linkedProduct.categoryDefinition.categoryId, SUM(A.calculatedAmount) ");
			sb.append(" FROM TransferOrderItemData A, SkuDefinitionData B" + " WHERE A.skuId = B.skuId ");
			sb.append(" AND A.transferOrderData.generatedForUnitId = :unitId ");
			sb.append(" AND A.transferOrderData.lastUpdateTime >= :startTime ");
			sb.append(" AND A.transferOrderData.lastUpdateTime < :endTime ");
			sb.append(" AND A.transferOrderData.status = :status ");
			sb.append(" AND B.linkedProduct.categoryDefinition.categoryId = :categoryId  ");
			sb.append(" AND B.linkedProduct.participatesInPnl = :participatesInPnl  ");
			sb.append(" GROUP BY B.linkedProduct.categoryDefinition.categoryId ");
			Query query = manager.createQuery(sb.toString());
			query.setParameter("unitId", unitId);
			query.setParameter("startTime", SCMUtil.getStartOfBusinessDay(monthStartDate));
			query.setParameter("endTime", SCMUtil.getEndOfBusinessDay(businessDate));
			query.setParameter("status", SCMOrderStatus.CREATED.name());
			query.setParameter("categoryId", SCMServiceConstants.CATEGORY_FIXED_ASSETS);
			query.setParameter("participatesInPnl", AppConstants.YES);
			List<Object[]> resultSet = query.getResultList();
			for (Object[] record : resultSet) {
				list.add(new Pair<>((Integer) record[0], record[1] == null ? BigDecimal.ZERO : (BigDecimal) record[1]));
			}
			if (list.isEmpty()) {
				return BigDecimal.ZERO;
			}
			return list.get(0).getValue();
		} catch (Exception e) {
			LOG.error("Error while calculation of transferred  fixed asset for month ::::: {}", e);
			return null;
		}
	}

	@Override
	public Integer checkSpecialOrders(int unitId, Date fulfillmentDate) {
		try {
			StringBuilder sb = new StringBuilder("SELECT E FROM RequestOrderData E WHERE ");
			sb.append("E.isSpecialOrder=:special AND E.fulfillmentDate=:date ")
					.append("AND E.status IN (:closed) AND E.requestUnitId=E.fulfillmentUnitId ")
					.append("AND E.requestUnitId=:unitId");
			Query query = manager.createQuery(sb.toString());
			query.setParameter("special", SCMServiceConstants.SCM_CONSTANT_YES).setParameter("date", fulfillmentDate)
					.setParameter("closed",
							Arrays.asList(SCMOrderStatus.CREATED.name(), SCMOrderStatus.ACKNOWLEDGED.name()))
					.setParameter("unitId", unitId);
			return query.getResultList().size();
		} catch (Exception e) {
			LOG.error("Error while getting unsettled special orders for unit {} and date {} :::::", unitId,
					fulfillmentDate, e);
			return 0;
		}
	}

	@Override
	public SCMDayCloseEventData fetchUnitLastDayClose(int unitId) {
		return getLastDayCloseEvent(unitId, StockEventType.STOCK_TAKE, false);
	}

	@Override
	public List<Integer> getUnitsWithMonthlyDone(Date date) {

		List<String> frequencyList = new ArrayList<>();
		frequencyList.add(StockTakeType.MONTHLY.name());

		Date businessDate = SCMUtil.addMonth(date, -1);
		try {
			String queryString = "SELECT unitId FROM SCMDayCloseEventData F "
					+ "WHERE F.dayCloseEventType  = :dayCloseEventType "
					+ "and F.status = :status and F.businessDate >= :startDate AND F.businessDate <= :endDate  AND F.eventFrequencyType IN :frequencyList ";
			Query query = manager.createQuery(queryString)
					.setParameter("dayCloseEventType", StockEventType.STOCK_TAKE.name())
					.setParameter("status", StockEventStatus.CLOSED.name())
					.setParameter("startDate", SCMUtil.getMonthlyCheckDate(businessDate))
					.setParameter("endDate", SCMUtil.getMonthlyCheckDateEnd(businessDate))
					.setParameter("frequencyList", frequencyList);

			return query.getResultList();
		} catch (Exception e) {
			LOG.error("Could not find any MONTHLY Day closing event for the date ::::: {}", date, e);
		}
		return new ArrayList<>();
	}

	@Deprecated
	@Override
	public int removeDuplicateKey() {
		Query query = manager.createNativeQuery("CALL REMOVE_DUPLICATE_KEY_ERROR()");
		return query.executeUpdate();
	}

	@Override
	public List<Integer> fetchDuplicateKeyWh() {
		try {

			Query query = manager
					.createNativeQuery("SELECT  MIN(COST_DETAIL_DATA_ID) COST_DETAIL_DATA_ID FROM "
							+ "   COST_DETAIL_DATA_WH  WHERE  IS_LATEST = :isLatest "
							+ "   GROUP BY UNIT_ID , KEY_ID  HAVING COUNT(*) > 1")
					.setParameter("isLatest", AppConstants.YES);
			return query.getResultList();
		} catch (Exception e) {
			LOG.error("Could not find any duplicate entries for cost_detail_data", e);
		}
		return null;
	}

	@Override
	public List<Integer> fetchDuplicateKeyCafe() {
		try {

			Query query = manager
					.createNativeQuery("SELECT  MIN(COST_DETAIL_DATA_ID) COST_DETAIL_DATA_ID FROM "
							+ "   COST_DETAIL_DATA_CAFE  WHERE  IS_LATEST = :isLatest "
							+ "   GROUP BY UNIT_ID , KEY_ID  HAVING COUNT(*) > 1")
					.setParameter("isLatest", AppConstants.YES);
			return query.getResultList();
		} catch (Exception e) {
			LOG.error("Could not find any duplicate entries for cost_detail_data", e);
		}
		return null;
	}

	@Override
	public int updateDuplicateKeyLatestFlag(List<Integer> ids, Boolean isCafe) {
		try {
			Query query = null;
			if (Boolean.TRUE.equals(isCafe)) {
				query = manager
						.createQuery("update CostDetailDataCafe set latest = :no where costDetailDataId in (:ids)");
			} else {
				query = manager
						.createQuery("update CostDetailDataWh set latest = :no where costDetailDataId in (:ids)");
			}

			query.setParameter("no", AppConstants.NO);
			query.setParameter("ids", ids);
			int results = query.executeUpdate();
			manager.flush();
			return results;
		} catch (Exception e) {
			LOG.error("Could not update cost_detail_data to remove duplicate ids {}", ids, e);
		}
		return -1;
	}


	@Override
	public Integer getSCMDayCloseEventForBusinessDateForClosingType(int unitId, Date businessDate) {

		List<String> frequencyList = new ArrayList<>();
		frequencyList.add(StockTakeType.DAILY.name());
		frequencyList.add(StockTakeType.WEEKLY.name());
		frequencyList.add(StockTakeType.MONTHLY.name());

		try {
			String queryString = "SELECT eventId FROM SCMDayCloseEventData F "
					+ "WHERE F.unitId = :unitId and F.dayCloseEventType  = :dayCloseEventType "
					+ "and F.status = :status and F.businessDate = :businessDate AND F.eventFrequencyType IN :frequencyList ";
			Query query = manager.createQuery(queryString).setParameter("unitId", unitId)
					.setParameter("dayCloseEventType", StockEventType.CLOSING.name())
					.setParameter("status", StockEventStatus.CLOSED.name())
					.setParameter("businessDate", SCMUtil.getDate(businessDate))
					.setParameter("frequencyList", frequencyList);

			return (Integer) query.getSingleResult();
		} catch (NoResultException nre) {
			LOG.error("Could not find any closed Day closing event for the unit ::::: {}", unitId);
		} catch (Exception e) {
			LOG.error("Could not find any closed Day closing event for the unit ::::: {}", unitId, e);
		}
		return null;
	}

	@Override
	public void updatePriceAndVarianceCost(Integer eventId) {
		try {
			Query query = manager.createNativeQuery(
					"update INVENTORY_DRILLDOWN a, PRODUCT_STOCK_DRILL_DOWN_UPDATE_EVENT b set a.PRICE = b.PRICE "
							+ ", a.VARIANCE_COST = b.COST where b.EVENT_ID = :eventId and a.INVENTORY_ID = b.INVENTORY_ID");
			query.setParameter("eventId", eventId);
			query.executeUpdate();
			manager.flush();
		} catch (Exception e) {
			LOG.error("Could not update update Price And Variance Cost in bulk for eventId {}", eventId);
			throw e;
		}
	}

	@Override
	public void deleteAllProductStockDrillDownUpdateEvent(Integer eventId) {
		try {
			Query query = manager.createQuery(
					"delete from ProductStockDrillDownUpdateEvent b where b.eventId = :eventId");
			query.setParameter("eventId", eventId);
			query.executeUpdate();
			manager.flush();
		} catch (Exception e) {
			LOG.error("Could not update delete Price And Variance Cost in bulk for eventId {}", eventId);
			throw e;
		}
	}

	@Override
	public WastageLimitLookup findWastageLimitById(int id) {
		try {
			Query query = manager.createQuery("FROM WastageLimitLookup w WHERE w.designationId = :designationId");
			query.setParameter("designationId", id);
			return (WastageLimitLookup) query.getSingleResult();
		} catch (Exception e) {
			LOG.error("Exception Occurred while fetching wastage limit for designation Id : {}", id);
			return null;
		}
	}

	@Override
	public List<SCMWastageEventData> getAllWastageEventsForDates(String startDate, String endDate, Integer unitId) {
		try {
			Query query = manager.createQuery("SELECT E FROM SCMWastageEventData E where E.unitId = :unitId and E.businessDate >= :startDate and E.businessDate <= :endDate " +
					"and E.status <> :status");
			query.setParameter("unitId", unitId).setParameter("startDate", AppUtils.getDate(AppUtils.getDate(startDate, "yyyy-MM-dd")))
					.setParameter("endDate", AppUtils.getDate(AppUtils.getDate(endDate, "yyyy-MM-dd"))).setParameter("status", StockEventStatus.CANCELLED.toString());
			return (List<SCMWastageEventData>) query.getResultList();
		} catch (Exception e) {
			LOG.error("Exception Occurred While fetching wastage events for dates...! : : ", e);
			return new ArrayList<>();
		}
	}

	@Override
	public List<RegularOrderingEvent> checkOrderingEvents(SCMDayCloseEventData dayClose, String brand, UnitOrderScheduleData unitOrderScheduleData, Date fulfilmentDate) {
		try {
			List<String> status = new ArrayList<>();
			status.add("CREATED");
			status.add("COMPLETED");
			Query query = manager.createQuery("FROM RegularOrderingEvent roe where roe.unitId = :unitId AND roe.brand = :brand AND roe.fulfilmentDate =:fulfilmentDate" +
					" AND roe.orderingDays = :orderingDays AND roe.status IN(:status)");
			query.setParameter("unitId", dayClose.getUnitId()).setParameter("brand", brand).setParameter("fulfilmentDate", fulfilmentDate)
					.setParameter("orderingDays", unitOrderScheduleData.getOrderingDays()).setParameter("status", status);
			return (List<RegularOrderingEvent>) query.getResultList();
		} catch (Exception e) {
			LOG.error("Exception Occurred While fetching wastage events for dates...! : : ", e);
			return new ArrayList<>();
		}
	}

	@Override
	public List<RequestOrderData> getAcknowledgedROProducts(Integer unitId, Date firstRemainingDate, Date lastDate) {
		LOG.info("Checking Acknowledge Ro's For Unit Id : {} and got the last Date as : {}", unitId, lastDate);
		try {
			String queryString = "FROM RequestOrderData r WHERE r.requestUnitId =:requestUnitId AND r.status =:status" +
					" AND r.assetOrder =:assetOrder AND r.bulkOrder =:bulkOrder AND r.fulfillmentDate BETWEEN :firstDate AND :lastDate";
			Query query1 = manager.createQuery(queryString);
			query1.setParameter("requestUnitId", unitId).setParameter("status", SCMOrderStatus.ACKNOWLEDGED.name())
					.setParameter("assetOrder", AppConstants.NO).setParameter("bulkOrder", AppConstants.NO)
					.setParameter("firstDate", firstRemainingDate)
					.setParameter("lastDate", lastDate);
			return  (List<RequestOrderData>) query1.getResultList();
		} catch (Exception e) {
			LOG.error("Exception Occurred While finding getAcknowledgedROProducts ::: ", e);
		}
		return new ArrayList<>();
	}

	@Override
	public List<RequestOrderData> getRosFromRoId(Integer childRO) {
		List<RequestOrderData> result = new ArrayList<>();
		try {
			Query query = manager.createQuery("FROM RequestOrderData r WHERE r.childRO.id =:childRO");
			query.setParameter("childRO", childRO);
			result = (List<RequestOrderData>) query.getResultList();
		} catch (Exception e) {
			LOG.error("Exception Occurred While finding List of RO's from Bulk RO ID ::: ", e);
		}
		return result;
	}

	@Override
	public void insertCostDetailDumpData() {
		try {
			Query query = manager.createNativeQuery("CALL INSERT_COST_DETAIL_DATA_DUMP()");
			query.executeUpdate();
			manager.flush();
		} catch (Exception e) {
			LOG.error("Exception Occurred While Inserting Cost Detail Dump Data ::: ", e);
		}
	}

	@Override
	public void insertToItemInTransitDumpData() {
		try {
			Query query = manager.createNativeQuery("CALL INSERT_TRANSFER_ORDER_ITEM_INTRANSIT_DUMP()");
			query.executeUpdate();
			manager.flush();
		} catch (Exception e) {
			LOG.error("Exception Occurred While Inserting TO Item In transit Dump Data ::: ", e);
		}
	}

	@Override
	public SCMDayCloseEventData getSumoDayCloseStatus(Integer unitId, Date previousDate) {
		Query query = manager.createQuery("FROM SCMDayCloseEventData where status=:status and businessDate = :businessDate and dayCloseEventType =:dayCloseEventType and unitId = :unitId");
		query.setParameter("status", StockEventStatus.CLOSED.name());
		query.setParameter("dayCloseEventType", StockEventType.STOCK_TAKE.name());
		query.setParameter("businessDate", AppUtils.getDate(previousDate));
		query.setParameter("unitId", unitId);
		List<SCMDayCloseEventData> scmDayCloseEventData = query.getResultList();
		return scmDayCloseEventData != null && scmDayCloseEventData.size() > 0 ? scmDayCloseEventData.get(0) : null;
	}

	@Override
	public List<SCMDayCloseEventData> verifyDayCloses(List<Integer> unitIds) {
		Query query = manager.createQuery("FROM SCMDayCloseEventData where " +
				" businessDate = :businessDate and dayCloseEventType =:dayCloseEventType" +
				" and unitId in (:unitId) and status = :status");
		query.setParameter("dayCloseEventType", StockEventType.STOCK_TAKE.value());
		query.setParameter("businessDate", SCMUtil.getPreviousBusinessDate());
		query.setParameter("unitId", unitIds);
		query.setParameter("status", StockEventStatus.CLOSED.toString());
		return query.getResultList();
	}

	@Override
	public List<CostDetailData> fetchUnitProductInventory(Integer unitId, String keyType) {
		try {
			List<CostDetailData> result = new ArrayList<>();
			Query query = null;
			if (keyType.equalsIgnoreCase(PriceUpdateEntryType.SKU.value())) {
				query = manager.createQuery("FROM CostDetailDataWh p WHERE p.unitId = :unitId and p.keyType = :keyType");
			} else {
				query = manager.createQuery("FROM CostDetailDataCafe p WHERE p.unitId = :unitId and p.keyType = :keyType");
			}
			query.setParameter("unitId", unitId).setParameter("keyType", keyType);
			result = (List<CostDetailData>) query.getResultList();
			return result;
		} catch (Exception e) {
			LOG.error("Exception Occurred While getting the Unit Product Inventory ::: ", e);
		}
		return null;
	}

	@Override
	public List<CostDetailData> fetchProductInventory(String keyType , List<Integer> keyIds) {
		try {
			List<CostDetailData> result = new ArrayList<>();
			Query query = null;
			if(keyType.equalsIgnoreCase(PriceUpdateEntryType.SKU.value())){
				query = manager.createQuery("FROM CostDetailDataWh p WHERE p.keyType = :keyType and p.keyId in :keyIds ");
			}else{
				query = manager.createQuery("FROM CostDetailDataCafe p WHERE p.keyType = :keyType and p.keyId in :keyIds ");
			}
			query.setParameter("keyType", keyType).setParameter("keyIds", keyIds);
			result = (List<CostDetailData>) query.getResultList();
			return result;
		} catch (Exception e) {
			LOG.error("Exception Occurred While getting the Unit Product Inventory ::: ",e);
		}
		return null;
	}

	@Override
	public Date getLastDayCloseDone(Integer unitId, String subType) {
		Date response = null;
		try {
			StringBuilder queryString = new StringBuilder("SELECT eventCreationDate FROM StockEventDefinitionData WHERE ");
			queryString.append(" unitId = :unitId ");
			if (Objects.isNull(subType)) {
				queryString.append("AND substring(subType,-8) = :subType ");
			} else {
				queryString.append("AND subType = :subType ");
			}
			queryString.append("AND eventStatus = :eventStatus ");
			queryString.append("AND parentId is null ");
				queryString.append("ORDER BY eventCreationDate DESC");
			Query query = manager.createQuery(queryString.toString()).setMaxResults(1);
			query.setParameter("unitId", unitId);
			if (Objects.isNull(subType)) {
				query.setParameter("subType", "DAYCLOSE");
			} else {
				query.setParameter("subType", subType);
			}
			query.setParameter("eventStatus", StockEventStatusType.COMPLETED.toString());
			response = (Date) query.getSingleResult();
		} catch (Exception e) {
			LOG.info(e.getMessage());
		}

		return response;
	}

	@Override
	public SCMDayCloseEventData getLastSuccessfulDayCloseEventData(Integer unitId) {
		try {
			String queryString = "SELECT E FROM SCMDayCloseEventData E where E.unitId = :unitId and E.status = :status"
					+ " and E.dayCloseEventType = :type ORDER BY E.eventId DESC";
			Query query = manager.createQuery(queryString);
			query.setParameter("unitId", unitId).setParameter("status", "CLOSED")
					.setParameter("type", "STOCK_TAKE").setMaxResults(1);
			return (SCMDayCloseEventData) query.getSingleResult();
		} catch (Exception e) {
			LOG.info("Exception Occurred While getting the last Day close Event Data ::: ", e);
		}
		return null;
	}

	@Override
	public List<SCMProductInventoryData> getScmProductInventoryDataList(SCMDayCloseEventData dayCloseEventData, boolean getByCurrentStockType) {
		try {
			StringBuilder queryString = new StringBuilder("FROM SCMProductInventoryData a WHERE a.currentDayCloseEvent.eventId =:eventId and a.originalVariance <>:variance");
			if (getByCurrentStockType) {
				queryString.append(" and a.stockType =:stockType");
			} else {
				queryString.append(" and a.stockType <>:stockType");
			}
			Query query = manager.createQuery(queryString.toString());
			query.setParameter("eventId", dayCloseEventData.getEventId());
			query.setParameter("variance", BigDecimal.ZERO);
			query.setParameter("stockType", dayCloseEventData.getEventFrequencyType());
			return (List<SCMProductInventoryData>) query.getResultList();
		} catch (Exception e) {
			LOG.info("Exception Occurred While getting the Scm Product Inventory Data List  ::: ", e);
		}
		return new ArrayList<>();
	}

	@Override
	public List<SCMProductInventoryData> getScmProductInventoryDataListByFrequency(SCMDayCloseEventData stockEvent, String stockTakeType) {
		try {
			StringBuilder stringBuilder = new StringBuilder("FROM SCMProductInventoryData a WHERE a.currentDayCloseEvent.eventId =:eventId ");
			if (Objects.nonNull(stockTakeType)) {
				stringBuilder.append("and a.stockType =:stockType");
			}
			Query query = manager.createQuery(stringBuilder.toString());
			query.setParameter("eventId", stockEvent.getEventId());
			if (Objects.nonNull(stockTakeType)) {
				query.setParameter("stockType", stockTakeType);
			}
			return (List<SCMProductInventoryData>) query.getResultList();
		} catch (Exception e) {
			LOG.info("Exception Occurred While getting the Scm Product Inventory Data List  ::: ", e);
		}
		return new ArrayList<>();
	}

	@Override
	public List<SCMDayCloseEventRangeData> getScmDayCloseEventRangeDataList(List<Integer> dayCloseEventIds) {
		try {
			String queryString = "SELECT E FROM SCMDayCloseEventRangeData E where E.eventId.eventId IN(:dayCloseEventIds)";
			Query query = manager.createQuery(queryString);
			query.setParameter("dayCloseEventIds", dayCloseEventIds);
			return (List<SCMDayCloseEventRangeData>) query.getResultList();
		} catch (Exception e) {
			LOG.error("Exception Occurred While getting Range data for event Ids :: ", e);
		}
		return new ArrayList<>();
	}

	@Override
	public List<ScmProductConsumptionDTO> getScmProductConsumptionDataOfDayCloses(List<Integer> kettleDayCloseIds) {
		List<ScmProductConsumptionDTO> result = new ArrayList<>();
		try {
			Query query = manager.createQuery("SELECT E.productConsumptionId, E.productId, E.businessDate, E.consumption, E.eventId.eventId FROM SCMProductConsumptionData E WHERE E.eventId.eventId IN(:kettleDayCloseIds)").setParameter("kettleDayCloseIds", kettleDayCloseIds);
			List<Object[]> resultList = (List<Object[]>) query.getResultList();
			if (Objects.nonNull(resultList) && !resultList.isEmpty()) {
				for (Object[] detail : resultList) {
					ScmProductConsumptionDTO consumptionDTO = new ScmProductConsumptionDTO();
					consumptionDTO.setProductConsumptionId((Integer) detail[0]);
					consumptionDTO.setProductId((Integer) detail[1]);
					consumptionDTO.setBusinessDate((Date) detail[2]);
					consumptionDTO.setConsumptionQuantity(SCMUtil.convertToBigDecimal((BigDecimal) detail[3]));
					consumptionDTO.setDayCloseEventId((Integer) detail[4]);
					result.add(consumptionDTO);
				}
			}
		} catch (Exception e) {
			LOG.error("Found some while getScmProductConsumptionDataOfDayCloses ::::: ", e);
		}
		return result;
	}

	@Override
	public List<SCMProductInventoryData> getScmProductInventoryDataListWithIds(List<Integer> stockingIds) {
		Query query = manager.createQuery("FROM SCMProductInventoryData a WHERE a.stockingId IN(:stockingIds)");
		query.setParameter("stockingIds", stockingIds);
		return (List<SCMProductInventoryData>) query.getResultList();
	}

	@Override
	public List<StockEntryEventData> getStockEntryEventDataForDayCloseId(Integer eventId) {
		Query query = manager.createQuery("FROM StockEntryEventData a WHERE a.updateEventId =:updateEventId");
		query.setParameter("updateEventId", eventId);
		return (List<StockEntryEventData>) query.getResultList();
	}

	@Override
	public List<VarianceAcknowledgementData> getVarianceAcknowledgementData(Date startDate, Date endDate, List<Integer> unitIds, String isAcknowledged, String acknowledgementType) {
		StringBuilder queryString = new StringBuilder("FROM VarianceAcknowledgementData v WHERE ");
		if (Objects.nonNull(acknowledgementType)) {
			queryString.append(" v.acknowledgementType =:acknowledgementType  ");
		}
		if (Objects.nonNull(startDate)) {
			queryString.append(" and v.businessDate >=:startDate");
		}
		if (Objects.nonNull(startDate)) {
			queryString.append(" and v.businessDate <=:endDate");
		}
		if (Objects.nonNull(unitIds)) {
			queryString.append(" and v.unitId in (:unitIds)  ");
		}
		if (Objects.nonNull(isAcknowledged)) {
			queryString.append(" and v.acknowledged =:acknowledged  ");
		}
		queryString.append(" and v.acknowledgementRequired =:acknowledgementRequired ");
		Query query = manager.createQuery(queryString.toString());
		if (Objects.nonNull(startDate)) {
			query.setParameter("startDate", startDate);
		}
		if (Objects.nonNull(endDate)) {
			query.setParameter("endDate", endDate);
		}
		if (Objects.nonNull(unitIds)) {
			query.setParameter("unitIds", unitIds);
		}
		if (Objects.nonNull(isAcknowledged)) {
			query.setParameter("acknowledged", isAcknowledged);
		}
		if (Objects.nonNull(acknowledgementType)) {
			query.setParameter("acknowledgementType", acknowledgementType);
		}
		query.setParameter("acknowledgementRequired", SCMUtil.YES);
		List<VarianceAcknowledgementData> vadList = (List<VarianceAcknowledgementData>) query.getResultList();
		if (!vadList.isEmpty()) {
			return vadList;
		}
		return null;
	}

	@Override
	public VarianceAcknowledgementData getLatestNonAcknowledgedData(Integer unitId, String isAcknowledged, String acknowledgementType) {
		StringBuilder queryString = new StringBuilder("FROM VarianceAcknowledgementData v WHERE v.unitId =:unitId and " +
				"v.acknowledged =:acknowledged and v.acknowledgementRequired =:acknowledgementRequired " +
				" and v.acknowledgementType =:acknowledgementType order by businessDate");

		Query query = manager.createQuery(queryString.toString());

		if (Objects.nonNull(unitId)) {
			query.setParameter("unitId", unitId);
		}
		if (Objects.nonNull(isAcknowledged)) {
			query.setParameter("acknowledged", isAcknowledged);
		}
		query.setParameter("acknowledgementRequired", SCMUtil.YES);
		if (Objects.nonNull(acknowledgementType)) {
			query.setParameter("acknowledgementType", acknowledgementType);
		}
		List<VarianceAcknowledgementData> vadl = (List<VarianceAcknowledgementData>) query.getResultList();
		if (!vadl.isEmpty()) {
			return vadl.get(0);
		}
		return null;
	}

	@Override
	public VarianceAcknowledgementData getVarianceAcknowledgementData(Integer unitId, Date businessDate) {
		StringBuilder queryString = new StringBuilder("FROM VarianceAcknowledgementData v  ");
		queryString.append(" WHERE v.unitId =:unitId and  ");
		queryString.append(" v.businessDate =:businessDate  order by v.id desc");
		Query query = manager.createQuery(queryString.toString());
		query.setParameter("unitId", unitId);
		query.setParameter("businessDate", businessDate);
		List<VarianceAcknowledgementData> vadList = (List<VarianceAcknowledgementData>) query.getResultList();
		if (!vadList.isEmpty()) {
			return vadList.get(0);
		}
		return null;
	}

	@Override
	public List<SCMProductInventoryData> getScmProductInventoryDataListWithEventIdAndFrequencyType(Integer eventId, String frequencyType) {
		StringBuilder queryString = new StringBuilder("FROM SCMProductInventoryData a WHERE a.currentDayCloseEvent.eventId =:eventId");
		queryString.append(" and a.currentDayCloseEvent.eventFrequencyType =:frequencyType  ");
		Query query = manager.createQuery(queryString.toString());
		query.setParameter("eventId", eventId);
		query.setParameter("frequencyType", frequencyType);
		return (List<SCMProductInventoryData>) query.getResultList();
	}

    @Override
    public List<Integer> getClassListFromUnitType(String unitCategory, String type, String frequency) {
		List<Integer> classList = new ArrayList<Integer>();
		StringBuilder queryString = new StringBuilder("SELECT classificationId FROM StockTakeFrequencyData a WHERE ");
		if(Objects.nonNull(unitCategory)){
			queryString.append("a.unitCategory =:unitCategory AND ");
		}
		if(Objects.nonNull(type)) {
			queryString.append("a.type =:type AND ");
		}
		queryString.append("a.frequency =:frequency ");
		Query query = manager.createQuery(queryString.toString());
		if(Objects.nonNull(unitCategory)){
			query.setParameter("unitCategory", unitCategory);
		}
		if(Objects.nonNull(type)) {
			query.setParameter("type", type);
		}
		query.setParameter("frequency", frequency);
		classList = (List<Integer>) query.getResultList();
        return classList;
    }

	@Override
	public Integer getInventoryDayForUnit(String unitCategory, String type, String frequency) {
		Integer stockTakeDay  = 0;
		StringBuilder queryString = new StringBuilder("SELECT inventoryListId FROM StockTakeFrequencyData a WHERE ");
		if(Objects.nonNull(unitCategory)){
			queryString.append("a.unitCategory =:unitCategory AND ");
		}
		if(Objects.nonNull(type)) {
			queryString.append("a.type =:type AND ");
		}
		queryString.append("a.frequency =:frequency ");
		Query query = manager.createQuery(queryString.toString());
		if(Objects.nonNull(unitCategory)){
			query.setParameter("unitCategory", unitCategory);
		}
		if(Objects.nonNull(type)) {
			query.setParameter("type", type);
		}
		query.setParameter("frequency", frequency);
		query.setMaxResults(1);
		stockTakeDay = (Integer) query.getResultList().get(0);
		return stockTakeDay;
	}

	@Override
	public List<VarianceAcknowledgementData> getVarianceAcknowledgementData(String acknowledgementType, Integer unitId, Date date){
		StringBuilder queryString = new StringBuilder("FROM VarianceAcknowledgementData v WHERE ");
		if (Objects.nonNull(acknowledgementType)) {
			queryString.append(" v.acknowledgementType =:acknowledgementType  ");
		}
		if (Objects.nonNull(date)) {
			queryString.append(" and v.businessDate =:date");
		}
		if (Objects.nonNull(unitId)) {
			queryString.append(" and v.unitId =:unitId ");
		}
		queryString.append(" and v.acknowledgementRequired =:acknowledgementRequired ");
		Query query = manager.createQuery(queryString.toString());
		if (Objects.nonNull(acknowledgementType)) {
			query.setParameter("acknowledgementType", acknowledgementType);
		}
		if (Objects.nonNull(date)) {
			query.setParameter("date", date);
		}
		if (Objects.nonNull(unitId)) {
			query.setParameter("unitId", unitId);
		}
		query.setParameter("acknowledgementRequired", SCMUtil.YES);
		List<VarianceAcknowledgementData> vadList = (List<VarianceAcknowledgementData>) query.getResultList();
		if (!vadList.isEmpty()) {
			return vadList;
		}
		return null;
      }


	@Override
	public SCMDayCloseEventData getLatestKettleDayClose(Integer unitId) {
		try {
			String queryString = "SELECT E FROM SCMDayCloseEventData E "
					+ "WHERE E.dayCloseEventType=:eventType and E.unitId = :unitId "
					+ "ORDER BY E.eventId DESC";
			Query query = manager.createQuery(queryString).setParameter("unitId", unitId)
					.setParameter("eventType", StockEventType.CLOSING.name())
					.setMaxResults(1);
			SCMDayCloseEventData dayCloseEventData = (SCMDayCloseEventData) query.getSingleResult();
			return dayCloseEventData;
		} catch (NoResultException e) {
			LOG.error("No Opening Event Found No Result");
			return null;
		}
		catch (Exception e) {
			LOG.error("No opening event found for the unit:::");
			return null;
		}
	}

	@Override
	public StockTakeSumoDayCloseEvent checkForStockTakeSumoDayClose(Integer unitId, List<String> status, boolean fetchNestedEntities, StockTakeSumoDayCloseEvent stockTakeSumoDayCloseTempEvent) {
		try {
			String queryString;
			if (fetchNestedEntities) {
				queryString = "SELECT DISTINCT E FROM StockTakeSumoDayCloseEvent E" +
						" LEFT JOIN FETCH E.stockTakeSumoDayCloseProducts AS dayCloseProducts" +
						" WHERE E.unitId = :unitId and E.eventStatus IN(:status) and E.stockTakeSumoDayCloseEventId IN(:stockTakeSumoDayCloseEventId) ORDER BY E.stockTakeSumoDayCloseEventId DESC";
			} else {
				queryString = "FROM StockTakeSumoDayCloseEvent E "
						+ "WHERE E.unitId = :unitId and E.eventStatus IN(:status) ORDER BY E.stockTakeSumoDayCloseEventId DESC";
			}
			Query query = manager.createQuery(queryString).setParameter("unitId", unitId)
					.setParameter("status", status);
			if (Objects.nonNull(stockTakeSumoDayCloseTempEvent)) {
				query.setParameter("stockTakeSumoDayCloseEventId", stockTakeSumoDayCloseTempEvent.getStockTakeSumoDayCloseEventId());
				List<StockTakeSumoDayCloseEvent> stockTakeSumoDayCloseEvents = (List<StockTakeSumoDayCloseEvent>) query.getResultList();
				if (!stockTakeSumoDayCloseEvents.isEmpty()) {
					return stockTakeSumoDayCloseEvents.get(0);
				}
			} else {
				query.setMaxResults(1);
				return (StockTakeSumoDayCloseEvent) query.getSingleResult();
			}
		} catch (NoResultException e) {
			LOG.error("No Result Found checkForStockTakeSumoDayClose for Unit Id : {}", unitId);
			return null;
		} catch (Exception e) {
			LOG.error("Exception while checkForStockTakeSumoDayClose :: ", e);
			return null;
		}
		return null;
	}

	@Override
	public List<DayCloseProductPackagingMappings> findAllProductPackagingMappingsByItemIds(List<Integer> sumoDayCloseProductItemIds) {
		try {
			String queryString = "FROM DayCloseProductPackagingMappings E "
					+ "WHERE E.sumoDayCloseProductItemId.sumoDayCloseProductItemId IN(:sumoDayCloseProductItemIds)";
			Query query = manager.createQuery(queryString).setParameter("sumoDayCloseProductItemIds", sumoDayCloseProductItemIds);
			return query.getResultList();
		} catch (Exception e) {
			LOG.error("Exception while findAllProductPackagingMappingsByItemIds :: ", e);
			return new ArrayList<>();
		}
	}

	@Override
	public SCMProductConsumptionData getLastDayCloseConsumptionOfProduct(SCMDayCloseEventData eventData, Integer productId) {
		try {
			Query query = manager.createQuery("FROM SCMProductConsumptionData E WHERE E.eventId.eventId =:eventId and E.productId =:productId and E.stockType =:stockType")
					.setParameter("eventId", eventData.getEventId()).setParameter("productId", productId).setParameter("stockType", eventData.getEventFrequencyType());
			return (SCMProductConsumptionData) query.getSingleResult();
		} catch (NoResultException e) {
			LOG.info("No Result Found For Product Id : {} and Event Id : {}",productId, eventData.getEventId());
			return null;
		} catch (Exception e) {
			LOG.error("Exception while getLastDayCloseConsumptionOfProduct :: ", e);
			return null;
		}
	}

	@Override
	public List<StockTakeSumoDayCloseEvent> getPendingStockTakeEventsOfApp() {
		try {
			Query query = manager.createQuery("FROM StockTakeSumoDayCloseEvent E WHERE E.eventStatus IN(:status)").
					setParameter("status", Arrays.asList(StockTakeSumoDayCloseStatus.IN_PROGRESS.value(), StockTakeSumoDayCloseStatus.SUBMITTED.value()));
			return (List<StockTakeSumoDayCloseEvent>) query.getResultList();
		} catch (Exception e) {
			LOG.error("Exception while getPendingStockTakeEventsOfApp :: ", e);
			return new ArrayList<>();
		}
	}

	@Override
	public List<SCMDayCloseEventData> getKettleDayCloseForBusinessDates(List<Date> last8WeekDates, int unitId) {
		try {
			Query query = manager.createQuery("FROM SCMDayCloseEventData E WHERE E.businessDate IN(:businessDates) and E.status = :status and E.unitId = :unitId and E.dayCloseEventType = :eventType");
			query.setParameter("businessDates",last8WeekDates)
					.setParameter("status", StockEventStatus.CLOSED.toString())
					.setParameter("unitId", unitId)
					.setParameter("eventType", StockEventType.CLOSING.toString());
			return (List<SCMDayCloseEventData>) query.getResultList();
		} catch (Exception e) {
			LOG.error("Exception while getKettleDayCloseIdsForBusinessDates :: ", e);
			return new ArrayList<>();
		}
	}

	@Override
	public List<Integer> getStockTakeDailyDayCloseForBusinessDates(List<Date> lastNWeekDates, int unitId) {
		try {
			Query query = manager.createQuery("select  E.eventId FROM SCMDayCloseEventData E WHERE E.businessDate IN(:businessDates) and " +
					"E.status = :status and E.unitId = :unitId " +
					" and E.dayCloseEventType = :eventType ");
			query.setParameter("businessDates",lastNWeekDates)
					.setParameter("status", StockEventStatus.CLOSED.toString())
					.setParameter("unitId", unitId)
					.setParameter("eventType", StockEventType.STOCK_TAKE.toString());
			return  query.getResultList();
		} catch (Exception e) {
			LOG.error("Exception while getStockTakeDailyDayCloseIdsForBusinessDates :: ", e);
			return new ArrayList<>();
		}
	}

	@Override
	public List<Integer> getProductIdsByCategoryLevelAndProductType(String categoryLevel , String productType){
		try {
			Query query = manager.createQuery("select  productId FROM ProductDefinitionData E WHERE E.categoryLevel = :categoryLevel and " +
					"E.productStatus = :status and E.productType = :productType ");
			query.setParameter("status",AppConstants.ACTIVE)
					.setParameter("categoryLevel", categoryLevel)
					.setParameter("productType", productType);
			return  query.getResultList();
		} catch (Exception e) {
			LOG.error("Exception while getting Product Ids:: ", e);
			return new ArrayList<>();
		}
	}

	@Override
	public Map<Integer,Map<Integer,Pair<BigDecimal,BigDecimal>>> getAggregatedStockOutData(List<Date> lastNWeekDates,
																						   int unitId,Integer brandId ) {
		Map<Integer,Map<Integer,Pair<BigDecimal,BigDecimal>>> aggregatedStockOutMap = new HashMap<>();
		try {
			String databaseName="KETTLE_STAGE";
			if(env.getEnvType().equals(EnvType.PROD) || env.getEnvType().equals(EnvType.SPROD)){
				databaseName="KETTLE";
			}
			Query query = manager.createNativeQuery("SELECT \n" +
					"    PRODUCT_ID, \n" +
					"    DAYOFWEEK(BUSINESS_DATE) AS DAY_OF_WEEK, \n" +
					"    SUM(TOTAL_DOWNTIME) AS TOTAL_DOWNTIME, \n" +
					"    SUM(CAFE_TOTAL_HOURS) AS TOTAL_HOURS\n" +
					"FROM \n" +
					databaseName +
					".SCM_STOCK_DATA\n" +
					"WHERE \n" +
					"    BUSINESS_DATE IN (:businessDates) \n" +
					"    AND UNIT_ID = :unitId\n" +
					"GROUP BY \n" +
					"    PRODUCT_ID, DAYOFWEEK(BUSINESS_DATE)\n" +
					"ORDER BY \n" +
					"    PRODUCT_ID, DAYOFWEEK(BUSINESS_DATE);");
			query.setParameter("businessDates",lastNWeekDates)
					.setParameter("unitId", unitId);
			List<Objects[]> objectList = query.getResultList();
			for(Object[] objects : objectList){
				Integer productId = (Integer)objects[0];
				Integer dayOfWeek = (Integer)objects[1];
				BigDecimal totalDownTime = (BigDecimal) objects[2];
				BigDecimal totalCafeTime = (BigDecimal) objects[3];
				if(!aggregatedStockOutMap.containsKey(productId)){
					aggregatedStockOutMap.put(productId,new HashMap<>());
				}
				if(!aggregatedStockOutMap.get(productId).containsKey(dayOfWeek)){
					aggregatedStockOutMap.get(productId).put(dayOfWeek,new Pair<>(totalDownTime,totalCafeTime));
				}
			}
            return aggregatedStockOutMap;

		} catch (Exception e) {
			LOG.error("Exception while getting Aggregated Stock out Data :: ", e);
			return aggregatedStockOutMap;
		}
	}

	@Override
	public Map<Integer,Map<Integer,BigDecimal>> getAggregatedWastageData(List<Date> lastNWeekDates,
																						   int unitId,List<Integer> eventIds
	,List<Integer> productIds) {
		Map<Integer,Map<Integer,BigDecimal>> aggregatedWastageMap = new HashMap<>();
		try {
			Query query = manager.createNativeQuery("SELECT \n" +
					"    D.PRODUCT_ID, \n" +
					"    DAYOFWEEK(D.BUSINESS_DATE) AS DAY_NUMBER,  \n" +
					"    SUM(COALESCE(D.WASTAGE, 0)) AS TOTAL_WASTAGE\n" +
					"FROM \n" +
					"    DAY_CLOSE_PRODUCT_VALUES D \n" +
					"WHERE \n" +
					"     D.UNIT_ID = :unitId \n" +
					"    and D.EVENT_ID in (:eventIds) and D.STOCK_TYPE = :eventFrequencyType and PRODUCT_ID in (:productIds) GROUP BY \n" +
					"    D.PRODUCT_ID, DAYOFWEEK(D.BUSINESS_DATE);\n");
			query.setParameter("unitId", unitId)
					.setParameter("productIds",productIds)
					.setParameter("eventFrequencyType", StockTakeType.DAILY.value())
					.setParameter("eventIds",eventIds);
			List<Objects[]> objectList = query.getResultList();
			for(Object[] objects : objectList){
				Integer productId = (Integer)objects[0];
				Integer dayOfWeek = (Integer)objects[1];
				BigDecimal totalWastage = (BigDecimal) objects[2];
				if(!aggregatedWastageMap.containsKey(productId)){
					aggregatedWastageMap.put(productId,new HashMap<>());
				}
				if(!aggregatedWastageMap.get(productId).containsKey(dayOfWeek)){
					aggregatedWastageMap.get(productId).put(dayOfWeek,totalWastage);
				}
			}
			return aggregatedWastageMap;

		} catch (Exception e) {
			LOG.error("Exception while getting Aggregated Stock out Data :: ", e);
			return aggregatedWastageMap;
		}
	}

	@Override
	public List<Object[]> getScmProductsConsumptionAverage(int unitId, String brandName, List<Integer> kettleDayCloseIds) {
		try {
			Query query = manager.createNativeQuery("SELECT PRODUCT_ID," +
					"DAYOFWEEK(BUSINESS_DATE) DAY_OF_WEEK," +
					"ROUND(AVG(CONSUMPTION),6) DAY_OF_WEEK_CONSUMPTION FROM DAY_CLOSE_PRODUCT_VALUES WHERE EVENT_ID IN(:kettleDayCloseIds) GROUP BY PRODUCT_ID, DAY_OF_WEEK");
			query.setParameter("kettleDayCloseIds", kettleDayCloseIds);
			return (List<Object[]>) query.getResultList();
		} catch (Exception e) {
			LOG.error("Exception while getKettleDayCloseIdsForBusinessDates :: ", e);
			return new ArrayList<>();
		}
	}

	@Override
	public List<Date> getChaayosBigDays(List<Date> last8WeekDates) {
		try {
			Query query = manager.createQuery("SELECT E.holidayDate FROM HolidaysListData E WHERE E.holidayDate IN(:last8WeekDates) AND E.status =:activeStatus AND E.holidayType =:holidayType");
			query.setParameter("last8WeekDates", last8WeekDates).setParameter("activeStatus",AppConstants.ACTIVE).setParameter("holidayType", SCMServiceConstants.CHAAYOS_BIG_DAY);
			return (List<Date>) query.getResultList();
		} catch (Exception e) {
			LOG.error("Exception while getKettleDayCloseIdsForBusinessDates :: ", e);
			return new ArrayList<>();
		}
	}

	public List<SuggestiveOrderingStrategyMetadata> getSuggestiveStrategyMetadataByUnit(Integer unitId) {
		Query query = manager.createQuery("FROM SuggestiveOrderingStrategyMetadata E WHERE E.unitId = :unitId");
		query.setParameter("unitId", unitId);
		List<SuggestiveOrderingStrategyMetadata> result = (List<SuggestiveOrderingStrategyMetadata>) query.getResultList();
		if (CollectionUtils.isNotEmpty(result)) {
			return result;
		}
		return null;
	}

	public SuggestiveOrderingStrategyMetadata getSuggestiveStrategyMetadataByUnitAndType(Integer unitId, StrategyType strategyType) {
		Query query = manager.createQuery("FROM SuggestiveOrderingStrategyMetadata E WHERE E.unitId = :unitId and E.strategyType = :strategyType");
		query.setParameter("unitId", unitId);
		query.setParameter("strategyType", strategyType);
		List<SuggestiveOrderingStrategyMetadata> result = (List<SuggestiveOrderingStrategyMetadata>) query.getResultList();
		if (CollectionUtils.isNotEmpty(result)) {
			return result.get(0);
		}
		return null;
	}

	@Override
	public Map<Integer,BigDecimal> dayCloseCriticalProductClosing(Integer unitId) {
          Query query = manager.createNativeQuery("SELECT PRODUCT_ID,CLOSING_STOCK FROM STOCK_INVENTORY where CURRENT_EVENT_ID = \n" +
				  " (select EVENT_ID from DAY_CLOSE_EVENT where UNIT_ID= :unitId and STATUS = \"CLOSED\" and CLOSURE_EVENT_TYPE=\"STOCK_TAKE\"  order by 1 desc limit 1)\n" +
				  " and PRODUCT_ID in (SELECT distinct(SCM_PRODUCT_ID) FROM "+ (AppUtils.isProd(env.getEnvType()) ? "KETTLE_MASTER.MENU_TO_SCM_PRODUCT_MAP" : "KETTLE_MASTER_STAGE.MENU_TO_SCM_PRODUCT_MAP") +" where PRODUCT_CLASSIFICATION=\"MENU\")");
		query.setParameter("unitId", unitId);
		  List<Object[]> res = query.getResultList();
		Map<Integer,BigDecimal> dayCloseInv = new HashMap<>();

		for(Object[] obj : res){
			dayCloseInv.put((Integer) obj[0],(BigDecimal) obj[1]);
		}
		return dayCloseInv;
	}


	@Override
	public List<StockTakeSumoDayCloseProducts> getListOfStockTakeSumoDayCloseProducts(List<Integer> sumoDayCloseProductItemIds) {
		try {
			Query query = manager.createQuery("SELECT DISTINCT E FROM StockTakeSumoDayCloseProducts E " +
							"LEFT JOIN FETCH E.dayCloseProductPackagingMappings AS productPackaging " +
							"WHERE E.sumoDayCloseProductItemId IN(:sumoDayCloseProductItemIds)").
					setParameter("sumoDayCloseProductItemIds", sumoDayCloseProductItemIds);
			return (List<StockTakeSumoDayCloseProducts>) query.getResultList();
		} catch (Exception e) {
			LOG.error("Exception while getListOfStockTakeSumoDayCloseProducts :: ", e);
			return new ArrayList<>();
		}
	}

	@Override
	public List<Integer> getDayClosedUnitsOfBusinessDate(Date currentBusinessDate, StockEventType stockEventType) {
		try {
			Query query = manager.createQuery("SELECT F.unitId FROM SCMDayCloseEventData F WHERE F.dayCloseEventType  = :closing and F.status =:closedStatus and F.businessDate = :currentBusinessDate")
					.setParameter("closing", stockEventType.toString())
					.setParameter("closedStatus", StockEventStatus.CLOSED.toString())
					.setParameter("currentBusinessDate", currentBusinessDate);
			return (List<Integer>) query.getResultList();
		} catch (Exception e) {
			LOG.error("Error Occurred While getting  getDayClosedUnitsOfBusinessDate from DB::::: {}", e);
			return new ArrayList<>();
		}
	}

	@Override
	public List<DayCloseTxnEventMapping> getDayCloseTxnEventMappings(int eventId) {
		try {
			Query query = manager.createQuery("FROM DayCloseTxnEventMapping F WHERE F.closureEvent.eventId  = :eventId")
					.setParameter("eventId", eventId);
			return query.getResultList();
		} catch (Exception e) {
			LOG.error("Error Occurred While getting  getDayCloseTxnEventMappings from DB::::: {}", e);
			return new ArrayList<>();
		}
	}

	@Override
	public void deleteNegativeVarianceExpiryData(SCMDayCloseEventData dayCloseEvent) {
		try {
			Query query = manager.createQuery(
					"delete from VarianceExpiryDrillDownData b where b.dayCloseEventData.eventId = :eventId");
			query.setParameter("eventId", dayCloseEvent.getEventId());
			query.executeUpdate();
			manager.flush();
		} catch (Exception e) {
			LOG.error("Exception Occurred While deleteNegativeVarianceExpiryData for Event Id {}", dayCloseEvent.getEventId(), e);
		}
	}
}

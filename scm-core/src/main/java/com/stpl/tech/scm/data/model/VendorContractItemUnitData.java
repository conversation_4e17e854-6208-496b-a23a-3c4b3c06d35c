package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "VENDOR_CONTRACT_ITEM_UNITS")
public class VendorContractItemUnitData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CONTRACT_ITEM_UNIT_ID", unique = true)
    private Integer contractItemUnitId;

    @JoinColumn(name = "CONTRACT_ITEM_ID", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private VendorContractItemData contractItem;

    @Column(name = "UNIT_ID", nullable = false)
    private Integer unitId;

    @Column(name = "STATUS", nullable = false)
    private String Status;
}

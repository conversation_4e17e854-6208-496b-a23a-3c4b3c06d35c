package com.stpl.tech.scm.data.model;

import com.stpl.tech.scm.domain.model.TransferOrderType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Version;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 11-06-2016.
 */
@Entity
@Table(name = "TRANSFER_ORDER")
public class TransferOrderData {

	private Integer id;
	private Date generationTime;
	private Date initiationTime;
	private Date lastUpdateTime;
	private int generationUnitId;
	private int generatedForUnitId;
	private int sourceCompanyId;
	private int receivingCompanyId;
	private int generatedBy;
	private Integer lastUpdatedBy;
	private String status;
	private String comment;
	private BigDecimal totalAmount;
	private Integer closureEventId;
	private RequestOrderData requestOrderData;
	private List<TransferOrderItemData> transferOrderItemDatas = new ArrayList<TransferOrderItemData>(0);
	private List<TransferOrderTaxDetail> orderTaxes = new ArrayList<>();
	private BigDecimal taxAmount;
	private String transferType;
	private String generatedInvoiceId;
	private String external = "N";
	private ExternalTransferDetailData externalTransferDetail;
	private String ewayApplicable = "N";
	private String toType = TransferOrderType.REGULAR_TRANSFER.value();
	private String type;
	private Integer bulkTransferEventId;
	private String eInvoiceGenerated;
	private String partialInvoiceIrn;
	private Integer rowVersion;


	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "TRANSFER_ORDER_ID", nullable = false, unique = true)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	/**
	 * @return the transferType
	 */
	@Column(name = "TRANSFER_TYPE", nullable = false)
	public String getTransferType() {
		return transferType;
	}

	/**
	 * @param transferType the transferType to set
	 */
	public void setTransferType(String transferType) {
		this.transferType = transferType;
	}

	/**
	 * @return the generatedInvoiceId
	 */
	@Column(name = "GENERATED_INVOICE_ID", nullable = true)
	public String getGeneratedInvoiceId() {
		return generatedInvoiceId;
	}

	/**
	 * @param generatedInvoiceId the generatedInvoiceId to set
	 */
	public void setGeneratedInvoiceId(String generatedInvoiceId) {
		this.generatedInvoiceId = generatedInvoiceId;
	}

	@Column(name = "GENERATION_TIME", nullable = false)
	public Date getGenerationTime() {
		return generationTime;
	}

	public void setGenerationTime(Date generationTime) {
		this.generationTime = generationTime;
	}

	@Column(name = "GENERATION_UNIT_ID", nullable = false)
	public int getGenerationUnitId() {
		return generationUnitId;
	}

	public void setGenerationUnitId(int generationUnitId) {
		this.generationUnitId = generationUnitId;
	}

	@Column(name = "GENERATED_FOR_UNIT_ID", nullable = false)
	public int getGeneratedForUnitId() {
		return generatedForUnitId;
	}

	public void setGeneratedForUnitId(int generatedForUnitId) {
		this.generatedForUnitId = generatedForUnitId;
	}

	@Column(name = "GENERATED_BY", nullable = false)
	public int getGeneratedBy() {
		return generatedBy;
	}

	public void setGeneratedBy(int generatedBy) {
		this.generatedBy = generatedBy;
	}

	@Column(name = "TRANSFER_ORDER_STATUS", nullable = false)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "COMMENT", nullable = true, length = 1000)
	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "REQUEST_ORDER_ID", nullable = true)
	public RequestOrderData getRequestOrderData() {
		return requestOrderData;
	}

	public void setRequestOrderData(RequestOrderData requestOrderData) {
		this.requestOrderData = requestOrderData;
	}

	@Column(name = "INITIATION_TIME", nullable = true)
	public Date getInitiationTime() {
		return initiationTime;
	}

	public void setInitiationTime(Date initiationTime) {
		this.initiationTime = initiationTime;
	}

	@Column(name = "LAST_UPDATE_TIME", nullable = false)
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "transferOrderData")
	public List<TransferOrderItemData> getTransferOrderItemDatas() {
		return transferOrderItemDatas;
	}

	public void setTransferOrderItemDatas(List<TransferOrderItemData> transferOrderItemDatas) {
		this.transferOrderItemDatas = transferOrderItemDatas;
	}

	@Column(name = "TOTAL_AMOUNT", nullable = true)
	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	@Column(name = "LAST_UPDATED_BY", nullable = true)
	public Integer getLastUpdatedBy() {
		return lastUpdatedBy;
	}

	public void setLastUpdatedBy(Integer lastUpdatedBy) {
		this.lastUpdatedBy = lastUpdatedBy;
	}

	@Column(name = "CLOSURE_EVENT_ID")
	public Integer getClosureEventId() {
		return closureEventId;
	}

	public void setClosureEventId(Integer closureEventId) {
		this.closureEventId = closureEventId;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderDetail")
	public List<TransferOrderTaxDetail> getOrderTaxes() {
		return orderTaxes;
	}

	public void setOrderTaxes(List<TransferOrderTaxDetail> orderTaxes) {
		this.orderTaxes = orderTaxes;
	}

	@Column(name = "TOTAL_TAX", precision = 10)
	public BigDecimal getTaxAmount() {
		return taxAmount;
	}

	public void setTaxAmount(BigDecimal taxAmount) {
		this.taxAmount = taxAmount;
	}

	@Column(name = "EXTERNAL_TRANSFER", nullable = true)
	public String getExternal() {
		return external;
	}

	public void setExternal(String external) {
		this.external = external;
	}

	@OneToOne(fetch = FetchType.LAZY, mappedBy = "transferOrderData")
	public ExternalTransferDetailData getExternalTransferDetail() {
		return externalTransferDetail;
	}

	public void setExternalTransferDetail(ExternalTransferDetailData externalTransferDetail) {
		this.externalTransferDetail = externalTransferDetail;
	}

	@Column(name = "SOURCE_COMPANY_ID", precision = 10)
	public int getSourceCompanyId() {
		return sourceCompanyId;
	}

	public void setSourceCompanyId(int sourceCompanyId) {
		this.sourceCompanyId = sourceCompanyId;
	}

	@Column(name = "RECEIVING_COMPANY_ID", precision = 10)
	public int getReceivingCompanyId() {
		return receivingCompanyId;
	}

	public void setReceivingCompanyId(int receivingCompanyId) {
		this.receivingCompanyId = receivingCompanyId;
	}

	@Column(name = "E_WAY_APPLICABLE", nullable = true)
	public String getEwayApplicable() {
		return ewayApplicable;
	}

	public void setEwayApplicable(String ewayApplicable) {
		this.ewayApplicable = ewayApplicable;
	}

	@Column(name = "TO_TYPE", nullable = true)
	public String getToType() {
		return toType;
	}

	public void setToType(String toType) {
		this.toType = toType;
	}

	@Column(name = "TYPE", nullable = true)
	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Column(name = "BULK_TRANSFER_EVENT_ID", nullable = true)
	public Integer getBulkTransferEventId() {
		return bulkTransferEventId;
	}

	public void setBulkTransferEventId(Integer bulkTransferEventId) {
		this.bulkTransferEventId = bulkTransferEventId;
	}

	@Column(name="E_INVOICE_GENERATED")
	public String geteInvoiceGenerated() {
		return eInvoiceGenerated;
	}

	public void seteInvoiceGenerated(String eInvoiceGenerated) {
		this.eInvoiceGenerated = eInvoiceGenerated;
	}

	@Column(name="PARTIAL_INVOICE_IRN")
	public String getPartialInvoiceIrn() {
		return partialInvoiceIrn;
	}

	public void setPartialInvoiceIrn(String partialInvoiceIrn) {
		this.partialInvoiceIrn = partialInvoiceIrn;
	}

	@Version
	@Column(name="ROW_VERSION")
	public Integer getRowVersion() {
		return rowVersion;
	}

	public void setRowVersion(Integer rowVersion) {
		this.rowVersion = rowVersion;
	}
}

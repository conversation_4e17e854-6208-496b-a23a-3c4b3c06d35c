package com.stpl.tech.scm.core.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.stpl.tech.kettle.domain.model.DayWiseOrderConsumptionItem;
import com.stpl.tech.kettle.domain.model.DayWiseOrderConsumptionRequest;
import com.stpl.tech.kettle.domain.model.DayCloseEstimateData;
import com.stpl.tech.kettle.domain.model.F9SalesRequest;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.domain.model.ApiResponse;
import com.stpl.tech.master.domain.model.Consumable;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.ProductProjectionsSevice;
import com.stpl.tech.scm.core.service.ReferenceOrderManagementService;
import com.stpl.tech.scm.core.service.RequestOrderManagementService;
import com.stpl.tech.scm.core.service.SCMReportingService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.model.MeanTypeEnum;
import com.stpl.tech.scm.core.util.SuggestiveOrderingCompanyTypes;
import com.stpl.tech.scm.core.util.model.StrategyType;
import com.stpl.tech.scm.core.util.webservice.KettleServiceClientEndpoints;
import com.stpl.tech.scm.core.util.webservice.MasterServiceClientEndpoints;
import com.stpl.tech.scm.core.util.webservice.WebServiceHelper;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.ReferenceOrderManagementDao;
import com.stpl.tech.scm.data.dao.SalesForecastingInputDataDao;
import com.stpl.tech.scm.data.dao.StockManagementDao;
import com.stpl.tech.scm.data.dao.SuggestiveOrderingStrategyDao;
import com.stpl.tech.scm.data.model.DayWiseOrderData;
import com.stpl.tech.scm.data.model.ExceptionDateEntry;
import com.stpl.tech.scm.data.model.ForecastReportResponse;
import com.stpl.tech.scm.data.model.ForecastReportScmResponse;
import com.stpl.tech.scm.data.model.FulfillmentUnitMappingData;
import com.stpl.tech.scm.data.model.MenuScmKeyData;
import com.stpl.tech.scm.data.model.ROMenuItemVariantData;
import com.stpl.tech.scm.data.model.ReferenceOrderData;
import com.stpl.tech.scm.data.model.ReferenceOrderMenuItemData;
import com.stpl.tech.scm.data.model.ReferenceOrderOrderingDays;
import com.stpl.tech.scm.data.model.ReferenceOrderScmItemData;
import com.stpl.tech.scm.data.model.RegularOrderUnitBrandData;
import com.stpl.tech.scm.data.model.RegularOrderingEvent;
import com.stpl.tech.scm.data.model.RequestOrderData;
import com.stpl.tech.scm.data.model.RequestOrderItemData;
import com.stpl.tech.scm.data.model.SCMDayCloseEventData;
import com.stpl.tech.scm.data.model.SCMDayCloseEventRangeData;
import com.stpl.tech.scm.data.model.SalesForecastingInputData;
import com.stpl.tech.scm.data.model.StrategyMeanMetaData;
import com.stpl.tech.scm.data.model.SuggestiveOrderingStrategyMetadata;
import com.stpl.tech.scm.data.model.UnitOrderScheduleData;
import com.stpl.tech.scm.domain.model.EstimationSalesDataRequest;
import com.stpl.tech.scm.domain.model.EstimationShortData;
import com.stpl.tech.scm.domain.model.ExpiryProduct;
import com.stpl.tech.scm.domain.model.Fountain9SalesComparison;
import com.stpl.tech.scm.domain.model.FulfillmentType;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.MenuProductSalesAverageDto;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductProjectionsUnitDetail;
import com.stpl.tech.scm.domain.model.ProductStatus;
import com.stpl.tech.scm.domain.model.ProductType;
import com.stpl.tech.scm.domain.model.RefCreateRequest;
import com.stpl.tech.scm.domain.model.ReferenceOrder;
import com.stpl.tech.scm.domain.model.ReferenceOrderMenuItem;
import com.stpl.tech.scm.domain.model.ReferenceOrderMenuVariant;
import com.stpl.tech.scm.domain.model.ReferenceOrderResponse;
import com.stpl.tech.scm.domain.model.ReferenceOrderScmItem;
import com.stpl.tech.scm.domain.model.RegularOrderEvent;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.ScmProductConsumptionAverage;
import com.stpl.tech.scm.domain.model.ScmProductConsumptionDTO;
import com.stpl.tech.scm.domain.model.SwitchStatus;
import com.stpl.tech.scm.notification.email.F9ComparisonEmailNotification;
import com.stpl.tech.scm.notification.email.template.F9ComparisonEmailNotificationTemplate;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.notification.AttachmentData;
import org.apache.http.HttpResponse;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.IsoFields;
import java.time.temporal.WeekFields;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * Created by Rahul Singh on 13-06-2016.
 */
@Service
public class ReferenceOrderManagementServiceImpl implements ReferenceOrderManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(ReferenceOrderManagementServiceImpl.class);

    @Autowired
    private ReferenceOrderManagementDao referenceOrderManagementDao;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private RequestOrderManagementService requestOrderManagementService;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private EnvProperties env;

    @Autowired
    private SCMReportingService reportingService;

    @Autowired
    private ProductProjectionsSevice productProjectionsSevice;

    @Autowired
    private StockManagementDao stockManagementDao;

    @Autowired
    private SalesForecastingInputDataDao salesForecastingInputDataDao;

    @Autowired
    private SuggestiveOrderingStrategyDao suggestiveOrderingStrategyDao;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ReferenceOrder> getReferenceOrders(Integer requestingUnitId, Date startDate, Date endDate, SCMOrderStatus status, Integer referenceOrderId) {
        List<ReferenceOrderData> referenceOrderDataList = referenceOrderManagementDao.getReferenceOrders(requestingUnitId, startDate, endDate, status, referenceOrderId);
        List<ReferenceOrder> referenceOrders = new ArrayList<>();
        for (ReferenceOrderData referenceOrderData : referenceOrderDataList) {
            IdCodeName generatedBy = SCMUtil.generateIdCodeName(referenceOrderData.getGeneratedBy(), "", masterDataCache.getEmployees().get(referenceOrderData.getGeneratedBy()));
            IdCodeName requestingUnit = SCMUtil.generateIdCodeName(referenceOrderData.getRequestUnitId(), "", masterDataCache.getUnitBasicDetail(referenceOrderData.getRequestUnitId()).getName());
            referenceOrders.add(SCMDataConverter.convert(referenceOrderData, generatedBy, null, requestingUnit));
        }
        return referenceOrders;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<IdCodeName> getExpiryProduct() {
        return referenceOrderManagementDao.getExpiryProduct();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<ExceptionDateEntry> generateExceptionDateEntries(List<ExceptionDateEntry> entry) {
        for (ExceptionDateEntry exceptionDateEntry : entry) {
            referenceOrderManagementDao.deleteEntry(exceptionDateEntry);
        }
        return referenceOrderManagementDao.addAll(entry);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ExceptionDateEntry> getExceptionDateEntries(Date businessDate) {
        return referenceOrderManagementDao.getExceptionDate(businessDate);
    }

    @Override
    public List<String> getDateWithoutException(Integer unitId, Date date, Integer dayDiff, Integer calcDate) {

        Date currentDate = date;
        List<String> dates = new ArrayList<>();
        while (dates.size() < calcDate) {
            Date dateWithoutException = AppUtils.getDateAfterDays(currentDate, dayDiff);
            if (!referenceOrderManagementDao.getExceptionDateWithUnitId(dateWithoutException, unitId)) {
                dates.add(AppUtils.getSQLFormattedDate(dateWithoutException));
            }
            currentDate = dateWithoutException;
        }
        return dates;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ReferenceOrder getReferenceOrder(int referenceOrderId) {
        ReferenceOrderData referenceOrderData = referenceOrderManagementDao.find(ReferenceOrderData.class, referenceOrderId);
        IdCodeName generatedBy = new IdCodeName();
        generatedBy.setId(referenceOrderData.getGeneratedBy());
        generatedBy.setName(masterDataCache.getEmployees().get(referenceOrderData.getGeneratedBy()));
        IdCodeName fulfillmentUnit = new IdCodeName();
        if (referenceOrderData.getFulfillmentUnitId() != null) {
            fulfillmentUnit.setId(referenceOrderData.getFulfillmentUnitId());
            fulfillmentUnit.setName(masterDataCache.getUnitBasicDetail(referenceOrderData.getFulfillmentUnitId()).getName());
        }
        IdCodeName requestingUnit = new IdCodeName();
        requestingUnit.setId(referenceOrderData.getRequestUnitId());
        requestingUnit.setName(masterDataCache.getUnitBasicDetail(referenceOrderData.getRequestUnitId()).getName());
        List<RequestOrderData> requestOrderDataList = referenceOrderManagementDao.getRequestOrderFromReferenceOrder(referenceOrderData.getId());
        return SCMDataConverter.convertFullRefOrder(referenceOrderData, generatedBy, fulfillmentUnit, requestingUnit, requestOrderDataList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Integer addReferenceOrder(ReferenceOrder referenceOrder) throws InventoryUpdateException, SumoException {
        if (referenceOrder.getStatus().equals(SCMOrderStatus.CREATED)) {
            referenceOrder.setInitiationTime(SCMUtil.getCurrentTimestamp());
        }
        referenceOrder.setGenerationTime(SCMUtil.getCurrentTimestamp());
        referenceOrder.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
        ReferenceOrderData referenceOrderData = SCMDataConverter.convert(referenceOrder, false, scmCache);
        referenceOrderData = referenceOrderManagementDao.add(referenceOrderData, true);
        if (referenceOrderData != null) {
            Map<Integer, List<String>> expiryUsageLogs = getExpiryUsageLogs(referenceOrder.getExpiryUsageLogs());
            referenceOrderData = persistReferenceOrderItems(referenceOrder, referenceOrderData, expiryUsageLogs);
            if (referenceOrderData != null) {
                requestOrderManagementService.createRequestOrdersFromReferenceOrder(referenceOrderData,referenceOrder.getOrderEvent());
                return referenceOrderData.getId();
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public ReferenceOrderResponse addNewReferenceOrder(ReferenceOrder referenceOrder) throws InventoryUpdateException, SumoException {
        if (referenceOrder.getStatus().equals(SCMOrderStatus.CREATED)) {
            referenceOrder.setInitiationTime(SCMUtil.getCurrentTimestamp());
        }
        referenceOrder.setGenerationTime(SCMUtil.getCurrentTimestamp());
        referenceOrder.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
        ReferenceOrderData referenceOrderData = SCMDataConverter.convert(referenceOrder, false, scmCache);
        referenceOrderData = referenceOrderManagementDao.add(referenceOrderData, true);
        Map<String, Map<Integer, ScmProductConsumptionAverage>> dayWiseProductWiseConsumptionAverage = getDayWiseProductWiseConsumptionAverage(referenceOrder.getScmProductConsumptionAverages());
        Map<Integer, List<String>> expiryUsageLogs = getExpiryUsageLogs(referenceOrder.getExpiryUsageLogs());
        addReferenceOrderOrderingDays(referenceOrderData, referenceOrder, dayWiseProductWiseConsumptionAverage);
        if (referenceOrderData != null) {
            referenceOrderData = persistReferenceOrderItems(referenceOrder, referenceOrderData, expiryUsageLogs);
            if (referenceOrderData != null) {
                requestOrderManagementService.createRequestOrdersFromReferenceOrder(referenceOrderData,referenceOrder.getOrderEvent());
//                List<RegularOrderEvent> regularOrderingEvents = updateRegularOrderingEvent(referenceOrder.getOrderEvent(),referenceOrderData);
                ReferenceOrderResponse response = new ReferenceOrderResponse();
                response.setReferenceOrderId(referenceOrderData.getId());
//                response.setRegularOrderEvents(regularOrderingEvents);
//                try {
//                    referenceOrderManagementDao.updateForecastData(referenceOrderData.getId(),referenceOrder.getRefreshDate(),referenceOrder.getRequestUnit().getId());
//                }
//                catch (Exception e) {
//                    LOG.error("Error Occurred While Updating the reference Order Id in Forecast report data :: ",e);
//                }
//                if (Objects.nonNull(referenceOrder.getMissedF9Products())) {
//                    try {
//                        String finalMessage = referenceOrder.getMissedF9Products();
//                        if (finalMessage.length() > 4096) {
//                            finalMessage = finalMessage.substring(0,4096);
//                        }
//                        SlackNotificationService.getInstance().sendNotification(env.getEnvType(), "SUMO",null,
//                                SlackNotification.F9_NOTIFICATION.getChannel(env.getEnvType()), finalMessage);
//                    } catch (Exception e) {
//                        LOG.error("Exception Occurred While Sending F9 Missed Products Notification ::: ",e);
//                    }
//                }
                return response;
            }
        }
        return null;
    }

    private Map<Integer, List<String>> getExpiryUsageLogs(Map<Integer, List<String>> expiryUsageLogs) {
        if (Objects.isNull(expiryUsageLogs)) {
            return new HashMap<>();
        } else {
            return expiryUsageLogs;
        }
    }

    private Map<String, Map<Integer, ScmProductConsumptionAverage>> getDayWiseProductWiseConsumptionAverage(Map<String, List<ScmProductConsumptionAverage>> scmProductConsumptionAverages) {
        Map<String, Map<Integer, ScmProductConsumptionAverage>> result = new HashMap<>();
        try {
            if (Objects.nonNull(scmProductConsumptionAverages)) {
                scmProductConsumptionAverages.forEach((key, value) -> {
                    Map<Integer, ScmProductConsumptionAverage> productConsumptionAverageMap = new HashMap<>();
                    value.forEach(scmProductConsumptionAverage -> productConsumptionAverageMap.put(scmProductConsumptionAverage.getProductId(), scmProductConsumptionAverage));
                    result.put(key, productConsumptionAverageMap);
                });
            }
        } catch (Exception e) {
            LOG.error("Error occurred while converting the product consumption average data :: ", e);
        }
        return result;
    }

    public void addReferenceOrderOrderingDays(ReferenceOrderData referenceOrderData, ReferenceOrder referenceOrder,
                                              Map<String, Map<Integer, ScmProductConsumptionAverage>> dayWiseProductWiseConsumptionAverage) throws SumoException {
        for (EstimationSalesDataRequest estimationSalesDataRequest : referenceOrder.getEstimationSalesDataRequests()) {
            ReferenceOrderOrderingDays referenceOrderOrderingDays = new ReferenceOrderOrderingDays();
            referenceOrderOrderingDays.setDayType(estimationSalesDataRequest.getDayType());
            referenceOrderOrderingDays.setDateOfOrdering(estimationSalesDataRequest.getDate());
            String dateString = AppUtils.getDateString(estimationSalesDataRequest.getDate(), "yyyy-MM-dd");
            referenceOrderOrderingDays.setReferenceOrderId(referenceOrderData.getId());
            referenceOrderOrderingDays.setOrderingPercentage(estimationSalesDataRequest.getOrderingPercentage());
            ReferenceOrderOrderingDays addedData = referenceOrderManagementDao.add(referenceOrderOrderingDays, true);
            // save day wise predicted qty
            if (Objects.nonNull(dayWiseProductWiseConsumptionAverage) && dayWiseProductWiseConsumptionAverage.containsKey(dateString)
                    && Objects.nonNull(dayWiseProductWiseConsumptionAverage.get(dateString))) {
                Map<Integer, ScmProductConsumptionAverage> averageConsumptionData = dayWiseProductWiseConsumptionAverage.get(dateString);
                averageConsumptionData.forEach((productId, scmProductConsumptionAverage) -> {
                    if (Objects.nonNull(scmProductConsumptionAverage)) {
                        DayWiseOrderData dayWiseOrderData = new DayWiseOrderData();
                        dayWiseOrderData.setProductId(productId);
                        dayWiseOrderData.setRoOrderingDaysId(addedData.getRoOrderingDaysId());
                        dayWiseOrderData.setOrderDate(estimationSalesDataRequest.getDate());
                        dayWiseOrderData.setAverageConsumption(scmProductConsumptionAverage.getAverageConsumption());
                        dayWiseOrderData.setSuggestedQuantityToOrder(scmProductConsumptionAverage.getOrderingQuantity());
                        dayWiseOrderData.setConsumptionData(JSONSerializer.toJSON(scmProductConsumptionAverage));
                        try {
                            referenceOrderManagementDao.add(dayWiseOrderData, true);
                        } catch (Exception e) {
                            LOG.error("Error occurred while saving the day wise order data :: ", e);
                        }
                    }
                });
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean getFountain9ComparisonReport(List<Integer> unitIds) {
        try {
            //Hitting Kettle-service for getting sales Data on business Days.
            List<Integer> fountain9UnitIds = getFountain9Units(null, false);
            if (Objects.nonNull(unitIds) && unitIds.size() > 0) {
                fountain9UnitIds.retainAll(unitIds);
            }
            String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.GET_DAY_CLOSE_ESTIMATES_DATA;
            Map<String, String> uriVariables = new HashMap<>();
            String startDate = AppUtils.getFormattedTime(AppUtils.getDateAfterDays(AppUtils.getCurrentDate(),-7),"yyyy-MM-dd");
            String  endDate = AppUtils.getFormattedTime(AppUtils.getDateAfterDays(AppUtils.getCurrentDate(),-1),"yyyy-MM-dd");
            uriVariables.put("startDate", startDate);
            uriVariables.put("endDate", endDate);
            F9SalesRequest estimateDatas = new F9SalesRequest();
            estimateDatas.setStartDate(startDate);
            estimateDatas.setEndDate(endDate);
            estimateDatas.setUnitIds(fountain9UnitIds);
            List kettleResult = WebServiceHelper.exchangeWithAuth(endpoint, env.getAuthToken(), HttpMethod.POST, List.class, JSONSerializer.toJSON(estimateDatas), uriVariables);
            ObjectMapper mapper = new ObjectMapper();
            List<DayCloseEstimateData> finalList = mapper.convertValue(kettleResult, new TypeReference<List<DayCloseEstimateData>>() {});
            Map<Integer,List<DayCloseEstimateData>> result2 = new HashMap<>();

            for (DayCloseEstimateData data : finalList) {
                Integer unitId = data.getUnitId();
                if (result2.containsKey(unitId)) {
                    List<DayCloseEstimateData> estimateData = result2.get(unitId);
                    estimateData.add(data);
                    result2.put(unitId,estimateData);
                }
                else {
                    List<DayCloseEstimateData> estimateData = new ArrayList<>();
                    estimateData.add(data);
                    result2.put(unitId,estimateData);
                }
            }
            //Getting Fountain 9 Data for all the dates date wise.
            List<ForecastReportResponse> forecastReportResponses = referenceOrderManagementDao.getForecastResponses(AppUtils.getDateAfterDays(AppUtils.getCurrentDate(),-7),
                    AppUtils.getDateAfterDays(AppUtils.getCurrentDate(),-1));
            LOG.info("Size of Fountain 9 is : {}",forecastReportResponses.size());
            Map<Integer,Map<String,ForecastReportResponse>> suggestedMap = new HashMap<>();
            for (ForecastReportResponse response : forecastReportResponses) {
                Date businessDate = AppUtils.getDate(response.getDate());
                Integer productId = getProductIdFromSkuId(response.getSkuId());
                String dimension = getDimensionFromSkuId(response.getSkuId());
                String orderSource = response.getOrderSource();
                Integer unitId = getUnitId(response.getCafeId());
                String brandId = getBrandId(response.getBrand());
                if (suggestedMap.containsKey(unitId)) {
                    Map<String,ForecastReportResponse> innerMap = suggestedMap.get(unitId);
                    String key = AppUtils.getDateString(businessDate,"yyyy-MM-dd:HH:mm:ss")+ "_"+productId+"_"+dimension+"_"+orderSource+"_"+brandId;
                    if (suggestedMap.get(unitId).containsKey(key)) {
                        ForecastReportResponse data = suggestedMap.get(unitId).get(key);
                        if (response.getRefreshDate().compareTo(data.getRefreshDate()) > 0 && response.getOrderSource().equalsIgnoreCase(orderSource)) {
                            innerMap.put(key,response);
                        }
                    }
                    else {
                        innerMap.put(key,response);
                    }
                    suggestedMap.put(unitId,innerMap);
                }
                else {
                    String key = AppUtils.getDateString(businessDate,"yyyy-MM-dd:HH:mm:ss")+ "_"+productId+"_"+dimension+"_"+orderSource+"_"+brandId;
                    Map<String,ForecastReportResponse> innerMap = new HashMap<>();
                    innerMap.put(key,response);
                    suggestedMap.put(unitId,innerMap);
                }
            }
            List<Date> datesList = AppUtils.getDaysBetweenDates(AppUtils.getDateAfterDays(AppUtils.getCurrentDate(),-7),
                    AppUtils.getDateAfterDays(AppUtils.getCurrentDate(),-1),true);

            Map<Integer,Map<String, Fountain9SalesComparison>> finalMap = getAllUnitsComparisonMap(suggestedMap,datesList,result2);
            List<String> dateListString = new ArrayList<>();
            List<String> finalDatesList = new ArrayList<>();
            List<String> scmSheetColumns = new ArrayList<>();
            List<Date> dates = new ArrayList<>();
            datesList.forEach(e -> {
                dateListString.add(AppUtils.getFormattedTime(e,"dd-MM-yyyy") + "-DINE_IN-Sales");
                finalDatesList.add(AppUtils.getFormattedTime(e,"yyyy-MM-dd") + "DINE_IN");
                dateListString.add(AppUtils.getFormattedTime(e,"dd-MM-yyyy") + "-DINE_IN-Fountain9");
                finalDatesList.add(AppUtils.getFormattedTime(e,"yyyy-MM-dd") + "DINE_IN");
                dateListString.add(AppUtils.getFormattedTime(e,"dd-MM-yyyy") + "-DELIVERY-Sales");
                finalDatesList.add(AppUtils.getFormattedTime(e,"yyyy-MM-dd") + "DELIVERY");
                dateListString.add(AppUtils.getFormattedTime(e,"dd-MM-yyyy") + "-DELIVERY-Fountain9");
                finalDatesList.add(AppUtils.getFormattedTime(e,"yyyy-MM-dd") + "DELIVERY");
                dates.add(e);
            });
            datesList.forEach(e -> {
                scmSheetColumns.add(AppUtils.getFormattedTime(e,"yyyy-MM-dd") + "_Sales");
                scmSheetColumns.add(AppUtils.getFormattedTime(e,"yyyy-MM-dd") + "_F9");
                scmSheetColumns.add(AppUtils.getFormattedTime(e,"yyyy-MM-dd") + "_Adhoc");
                scmSheetColumns.add(AppUtils.getFormattedTime(e,"yyyy-MM-dd") + "_Adhoc_Reason");
            });
            generateFountain9ComparisonExcel(finalMap,dateListString,finalDatesList,dates,scmSheetColumns);
            return true;
        }
        catch (Exception e) {
            LOG.info("Exception Occurred While generating F9 Comparison Report..!",e);
            return false;
        }
    }

    private String getBrandName(Integer brandId) {
        if (brandId == 1) {
            return "Chaayos";
        }
        else if (brandId == 3) {
            return "Ghee and Turmeric";
        }
        else {
            return "WareHouse";
        }
    }

    private String getBrandId(String brand) {
        if (brand.equalsIgnoreCase("Chaayos")) {
            return "1";
        }
        else if (brand.equalsIgnoreCase("Ghee and Turmeric")) {
            return "3";
        }
        else {
            return "2";
        }
    }

    private void generateFountain9ComparisonExcel(Map<Integer, Map<String, Fountain9SalesComparison>> finalMap, List<String> datesList,
                                                  List<String> finalDatesList, List<Date> dates, List<String> scmSheetColumns) {
            try {
                LOG.info("Trying to generate Excel for Fountain 9 Vs Sales");
                for (Map.Entry<Integer,Map<String, Fountain9SalesComparison>> entry : finalMap.entrySet()) {
                    List<Fountain9SalesComparison> chaayosFinalList = new ArrayList<>();
                    List<Fountain9SalesComparison> gntFinalList = new ArrayList<>();
                    entry.getValue().values().stream().forEach(e -> {
                        if (e.getBrandId().equals(1)) {
                            chaayosFinalList.add(e);
                        }
                        if (e.getBrandId().equals(3)) {
                            gntFinalList.add(e);
                        }
                    });
                    Workbook workbook = new XSSFWorkbook();
                    String fileName = "Fountain9_Comparison_" + entry.getKey() + "_" + AppUtils.getCurrentTimeISTStringWithNoColons();
                    for (int i=0; i< 2; i++) {
                        Sheet sheet = workbook.createSheet();
                        if (i == 0) {
                            workbook.setSheetName(i, "Chaayos-Menu-Products");
                        }
                        else {
                            workbook.setSheetName(i, "GnT-Menu-Products");
                        }
                        int rowCount = 0;
                        int columnCount = 0;
                        Row row = sheet.createRow(rowCount++);
                        Cell cell = row.createCell(columnCount++);
                        cell.setCellValue("Product Name");
                        cell.setCellStyle(requestOrderManagementService.getHeaderStyle((XSSFWorkbook) workbook));
                        for (String fieldName : datesList) {
                            Cell cell1 = row.createCell(columnCount++);
                            cell1.setCellValue(fieldName);
                            Boolean check = fieldName.contains("DINE");
                            if (check) {
                                cell1.setCellStyle(requestOrderManagementService.getHeaderStyle((XSSFWorkbook) workbook));
                            }
                            else {
                                CellStyle headerStyle = requestOrderManagementService.getHeaderStyle((XSSFWorkbook) workbook);
                                headerStyle.setFillForegroundColor(IndexedColors.RED.getIndex());
                                cell1.setCellStyle(headerStyle);
                            }
                        }
                        List<Fountain9SalesComparison> listName = (i == 0) ? chaayosFinalList : gntFinalList;
                        for (int k = 0; k < listName.size(); k++) {
                            Row rows = sheet.createRow(rowCount++);
                            for (int d=0;d <= finalDatesList.size();d++) {
                                if (d == 0 ) {
                                    Cell cell1 = rows.createCell(d);
                                    cell1.setCellValue(listName.get(k).getCompleteProductName());
                                }
                                else {
                                    Cell cell1 = rows.createCell(d);
                                    String[] s = row.getCell(d).toString().split("-");
                                    String type = s[s.length -1];
                                    Map<String,BigDecimal> map = new HashMap<>();
                                    if (type.equalsIgnoreCase("Sales")) {
                                        map = listName.get(k).getSalesDataMap();
                                    }
                                    else {
                                        map = listName.get(k).getFountain9DataMap();
                                    }
                                    Double value = map.getOrDefault(finalDatesList.get(d-1), BigDecimal.valueOf(0)).doubleValue();
                                    cell1.setCellValue(value);
                                }
                            }
                        }
                        for (int s = 0; s < columnCount; s++) {
                            sheet.autoSizeColumn(s);
                        }
                    }
                    try {
                        //getting scm out put from product Projections service <Date,<F9/Sales,Projection>>
                        Map<String,Map<String,List<ProductProjectionsUnitDetail>>> chaayosDateWiseTotals = getDateWiseTotals(chaayosFinalList,dates,entry.getKey());
                        Map<String,Map<String,List<ProductProjectionsUnitDetail>>> gntDateWiseTotals = getDateWiseTotals(gntFinalList,dates,entry.getKey());
                        Map<String,Map<String,List<ProductProjectionsUnitDetail>>> chaayosScmDateWise = getScmDateWiseOutput(chaayosDateWiseTotals);
                        Map<String,Map<String,List<ProductProjectionsUnitDetail>>> gntScmDateWise = getScmDateWiseOutput(gntDateWiseTotals);
                        List<RequestOrderData> adhocOrderData = referenceOrderManagementDao.getLastWeekAdhocOrders(dates,entry.getKey());
                        Map<String, List<RequestOrderItemData>> roItemMap = makeRoItemMap(adhocOrderData);
                        Map<String,Fountain9SalesComparison> scmProductListChaayos = makeFinalScmList(chaayosScmDateWise,roItemMap);
                        Map<String,Fountain9SalesComparison> scmProductListGnt = makeFinalScmList(gntScmDateWise,roItemMap);
                        for (int i=2; i< 4; i++) {
                            Sheet sheet = workbook.createSheet();
                            if (i == 2) {
                                workbook.setSheetName(i, "Chaayos-SCM-Products");
                            }
                            else {
                                workbook.setSheetName(i, "GnT-SCM -Products");
                            }
                            int rowCount = 0;
                            int columnCount = 0;
                            Row row = sheet.createRow(rowCount++);
                            Cell cell = row.createCell(columnCount++);
                            cell.setCellValue("Product Name");
                            cell.setCellStyle(requestOrderManagementService.getHeaderStyle((XSSFWorkbook) workbook));
                            List<String> colorStrings = new ArrayList<>();
                            for (String fieldName : scmSheetColumns) {
                                String field = fieldName.split("_")[0];
                                if (!colorStrings.contains(field)) {
                                    colorStrings.add(field);
                                }
                            }
                            List<String> greenColor = new ArrayList<>();
                            for (int s=0 ;s<colorStrings.size();s++) {
                                if (s % 2 == 0) {
                                    greenColor.add(colorStrings.get(s));
                                }
                            }
                            for (String fieldName : scmSheetColumns) {
                                Cell cell1 = row.createCell(columnCount++);
                                cell1.setCellValue(fieldName);
                                cell1.setCellStyle(requestOrderManagementService.getHeaderStyle((XSSFWorkbook) workbook));
                                String currentField = fieldName.split("_")[0];
                                if (greenColor.contains(currentField)) {
                                    cell1.setCellStyle(requestOrderManagementService.getHeaderStyle((XSSFWorkbook) workbook));
                                }
                                else {
                                    CellStyle headerStyle = requestOrderManagementService.getHeaderStyle((XSSFWorkbook) workbook);
                                    headerStyle.setFillForegroundColor(IndexedColors.RED.getIndex());
                                    cell1.setCellStyle(headerStyle);
                                }
                            }
                            List<Fountain9SalesComparison> listName;
                            if (i == 2) {
                                listName = new ArrayList<>(scmProductListChaayos.values());
                            }
                            else {
                                listName = new ArrayList<>(scmProductListGnt.values());
                            }
                            for (int k = 0; k < listName.size(); k++) {
                                Row rows = sheet.createRow(rowCount++);
                                for (int d=0;d <= scmSheetColumns.size();d++) {
                                    if (d == 0 ) {
                                        Cell cell1 = rows.createCell(d);
                                        cell1.setCellValue(listName.get(k).getCompleteScmProductName());
                                    }
                                    else {
                                        Cell cell1 = rows.createCell(d);
                                        String[] s = row.getCell(d).toString().split("_");
                                        String type = s[s.length -1];
                                        if (type.equalsIgnoreCase("Reason")) {
                                            Map<String, String> map = listName.get(k).getReasonsMap();
                                            if (Objects.nonNull(map)) {
                                                String reason = map.getOrDefault(scmSheetColumns.get(d - 1), "");
                                                cell1.setCellValue(reason);
                                            }
                                        }
                                        else {
                                            Map<String, BigDecimal> map = listName.get(k).getF9TotalMap();
                                            if (Objects.nonNull(map)) {
                                                Double value = map.getOrDefault(scmSheetColumns.get(d - 1), BigDecimal.valueOf(0)).doubleValue();
                                                cell1.setCellValue(value);
                                            }
                                        }
                                    }
                                }
                            }
                            for (int s = 0; s < columnCount; s++) {
                                if (s == 0) {
                                    sheet.autoSizeColumn(s);
                                }
                                else {
                                    sheet.setDefaultColumnWidth(20);
                                }
                            }
                        }
                    } catch (Exception e) {
                        LOG.error("Exception occurred while generating Scm Output Sheets.. :::  ",e);
                    }
                    ByteArrayOutputStream bos = new ByteArrayOutputStream();
                    try {
                        workbook.write(bos);
                    } catch (IOException e1) {
                        LOG.error("Error Occurred While writing into the Workbook... ::: ", e1);
                    }

                    File fileToUpload = new File(env.getBasePath() + fileName);
                    byte[] barray = null;
                    try {
                        barray = bos.toByteArray();
                    } catch (Exception e) {
                        LOG.error("Error While Creating File");
                    }
                    try {
                        LOG.info("Trying to send Email Of F9 Comparison with Sales ");
                        List<AttachmentData> attachments = new ArrayList<>();
                        AttachmentData f9ComparisonExcel = null;
                        f9ComparisonExcel = new AttachmentData(barray, fileName, AppConstants.EXCEL_MIME_TYPE);
                        attachments.add(f9ComparisonExcel);

                        Unit unit = masterDataCache.getUnit(entry.getKey());
                        List<String> mails = new ArrayList<>();
                        if (Objects.nonNull(unit)) {
                            if (unit.getUnitEmail() != null) {
                                mails.add(unit.getUnitEmail());
                            }
                            if (unit.getManagerEmail() != null) {
                                mails.add(unit.getManagerEmail());
                            }
                            if (unit.getUnitManager().getEmployeeEmail() != null) {
                                mails.add(unit.getUnitManager().getEmployeeEmail());
                            }
                        }
                        F9ComparisonEmailNotificationTemplate notificationTemplate = new F9ComparisonEmailNotificationTemplate(dates,
                                masterDataCache.getUnit(entry.getKey()),env.getBasePath());
                        F9ComparisonEmailNotification f9ComparisonEmailNotification = new F9ComparisonEmailNotification(notificationTemplate,env.getEnvType(),mails);
                        f9ComparisonEmailNotification.sendRawMail(attachments);
                        fileToUpload.delete();
                    } catch (Exception e) {
                        LOG.info("error sending email ::: ", e);
                    }
                }
            } catch (Exception e) {
                LOG.error("error While Generating Excel ::: ", e);
            }
        }

    private Map<String, List<RequestOrderItemData>> makeRoItemMap(List<RequestOrderData> adhocOrderData) {
        Map<String, List<RequestOrderItemData>> result = new HashMap<>();
        try {
            for (RequestOrderData data : adhocOrderData) {
                for (RequestOrderItemData itemData : data.getRequestOrderItemDatas()) {
                    String productName = itemData.getProductName()+ "-"  +itemData.getProductId()+ "(" + itemData.getUnitOfMeasure() + ")";
                    if (result.containsKey(productName)) {
                        List<RequestOrderItemData> list = result.get(productName);
                        list.add(itemData);
                        result.put(productName,list);
                    }
                    else {
                        List<RequestOrderItemData> list = new ArrayList<>();
                        list.add(itemData);
                        result.put(productName,list);
                    }
                }
            }
        }
        catch (Exception e) {
            LOG.error("Exception Occurred while making RO item map with adhoc orders :: ",e);
        }
        return result;
    }

    private Map<String, Fountain9SalesComparison> makeFinalScmList(Map<String, Map<String, List<ProductProjectionsUnitDetail>>> chaayosScmDateWise,
                                                                   Map<String, List<RequestOrderItemData>> roItemMap) {
        Map<String, Fountain9SalesComparison> result = new HashMap<>();
        try {
            for (Map.Entry<String, Map<String, List<ProductProjectionsUnitDetail>>> mainEntry : chaayosScmDateWise.entrySet()) {
                for (Map.Entry<String, List<ProductProjectionsUnitDetail>> innerEntry : mainEntry.getValue().entrySet()) {
                    List<ProductProjectionsUnitDetail> values = innerEntry.getValue();
                    for (ProductProjectionsUnitDetail detail : values) {
                        String productName = detail.getScmProductName()+ "-"  +detail.getScmProductId()+ "(" + detail.getUom() + ")";
                        String key = mainEntry.getKey()+"_"+innerEntry.getKey();
                        if (result.containsKey(productName)) {
                            Fountain9SalesComparison comparison = result.get(productName);
                            Map<String,BigDecimal> map = comparison.getF9TotalMap();
                            if (map.containsKey(key)) {
                                map.put(key, map.get(key).add(detail.getQuantity()));
                            }
                            else {
                                map.put(key, detail.getQuantity());
                            }
                        }
                        else {
                            Fountain9SalesComparison comparison = new Fountain9SalesComparison();
                            comparison.setScmProductId(detail.getScmProductId());
                            comparison.setScmProductName(detail.getScmProductName());
                            comparison.setCompleteScmProductName(productName);
                            Map<String,BigDecimal> map = new HashMap<>();
                            map.put(key,detail.getQuantity());
                            comparison.setF9TotalMap(map);
                            result.put(productName,comparison);
                        }
                    }
                }
            }
            for (Map.Entry<String, Map<String, List<ProductProjectionsUnitDetail>>> mainEntry : chaayosScmDateWise.entrySet()) {
                for (Map.Entry<String, Fountain9SalesComparison> entry : result.entrySet()) {
                    String adhocKey = mainEntry.getKey() + "_Adhoc";
                    String adhocReason = mainEntry.getKey() + "_Adhoc_Reason";
                    Fountain9SalesComparison comparison = entry.getValue();
                    Map<String, BigDecimal> map = comparison.getF9TotalMap();
                    Map<String, String> reasonMap = comparison.getReasonsMap();
                    if (roItemMap.containsKey(entry.getKey())) {
                        List<RequestOrderItemData> list = roItemMap.get(entry.getKey());
                        String date = mainEntry.getKey();
                        List<RequestOrderItemData> filteredRoItems = list.stream().filter(e ->
                                AppUtils.getFormattedTime(e.getRequestOrderData().getFulfillmentDate(), "yyyy-MM-dd").equalsIgnoreCase(date)).collect(Collectors.toList());
                        List<RequestOrderItemData> productFiltered = filteredRoItems.stream().filter(e -> e.getProductId() == entry.getValue().getScmProductId()).collect(Collectors.toList());
                        BigDecimal total = BigDecimal.ZERO;
                        StringBuilder reason = new StringBuilder();
                        for (RequestOrderItemData itemData : productFiltered) {
                            total = total.add(itemData.getRequestedAbsoluteQuantity());
                            if (String.valueOf(reason).equalsIgnoreCase("")) {
                                if (Objects.nonNull(itemData.getReason())) {
                                    reason.append(itemData.getReason()).append("-");
                                }
                                reason.append(itemData.getRequestedAbsoluteQuantity().setScale(2, BigDecimal.ROUND_HALF_UP)).append("-").append(itemData.getId()).append("(");
                            }
                            else {
                                reason.append("\n");
                                if (Objects.nonNull(itemData.getReason())) {
                                    reason.append(itemData.getReason()).append("-");
                                }
                                reason.append(itemData.getRequestedAbsoluteQuantity().setScale(2, BigDecimal.ROUND_HALF_UP)).append("-").append(itemData.getId()).append("(");
                            }
                            if (Objects.nonNull(itemData.getComment())) {
                                reason.append(itemData.getComment()).append(")");
                            }
                            else {
                                reason.append("NA)");
                            }
                        }
                        if (map.containsKey(adhocKey)) {
                            map.put(adhocKey,map.get(adhocKey).add(total));
                        }
                        else {
                            map.put(adhocKey, total);
                        }
                        reasonMap.put(adhocReason, String.valueOf(reason));
                    }
                    comparison.setReasonsMap(reasonMap);
                    result.put(entry.getKey(),comparison);
                }
            }
        } catch (Exception e) {
            LOG.error("Exception occurred while making Final scm List :: ",e);
        }
        return result;
    }

    private Map<String, Map<String, List<ProductProjectionsUnitDetail>>> getScmDateWiseOutput(Map<String, Map<String, List<ProductProjectionsUnitDetail>>> chaayosDateWiseTotals) {
        Map<String, Map<String, List<ProductProjectionsUnitDetail>>> result = new HashMap<>();
        try {
            for (Map.Entry<String, Map<String, List<ProductProjectionsUnitDetail>>> mainEntry : chaayosDateWiseTotals.entrySet()) {
                Map<String, List<ProductProjectionsUnitDetail>> innerMap = new HashMap<>();
                for (Map.Entry<String, List<ProductProjectionsUnitDetail>> innerEntry : mainEntry.getValue().entrySet()) {
                    List<ProductProjectionsUnitDetail> values = innerEntry.getValue();
                    if (values.size() > 0) {
                        innerMap.put(innerEntry.getKey(), productProjectionsSevice.getScmOutput(values, new HashMap<>(), new HashMap<>()));
                    }
                }
                result.put(mainEntry.getKey(),innerMap);
            }
        } catch (Exception e) {
            LOG.error("Exception occurred while making scm day wise output :: ",e);
        }
        return result;
    }

    private Map<String, Map<String, List<ProductProjectionsUnitDetail>>> getDateWiseTotals(List<Fountain9SalesComparison> chaayosFinalList, List<Date> dates, Integer unitId) {
        Map<String, Map<String, List<ProductProjectionsUnitDetail>>> result = new HashMap<>();
        try {
            for (Date date : dates) {
                Map<String, List<ProductProjectionsUnitDetail>> innerMap = new HashMap<>();
                for (Fountain9SalesComparison comparison : chaayosFinalList) {
                    Map<String,BigDecimal> map;
                    for (int i=0; i < 2; i++) {
                        String deliveryKey = AppUtils.getFormattedTime(date,"yyyy-MM-dd") + "DELIVERY";
                        String dineInKey = AppUtils.getFormattedTime(date,"yyyy-MM-dd") +  "DINE_IN";
                        if (i == 0 ) {
                            map = comparison.getSalesDataMap();
                            BigDecimal total = BigDecimal.ZERO;
                            if (map.containsKey(deliveryKey)) {
                                total = total.add(map.get(deliveryKey));
                            }
                            if (map.containsKey(dineInKey)) {
                                total = total.add(map.get(dineInKey));
                            }
                            List<ProductProjectionsUnitDetail> list;
                            if (innerMap.containsKey("Sales")) {
                                list = innerMap.get("Sales");
                                ProductProjectionsUnitDetail detail = convertIntoProjections(comparison,total, unitId);
                                if (Objects.nonNull(detail)) {
                                    list.add(detail);
                                }
                            }
                            else {
                                list = new ArrayList<>();
                                ProductProjectionsUnitDetail detail = convertIntoProjections(comparison,total,unitId);
                                if (Objects.nonNull(detail)) {
                                    list.add(detail);
                                }
                            }
                            innerMap.put("Sales",list);
                        }
                        else {
                            map = comparison.getFountain9DataMap();
                            BigDecimal total = BigDecimal.ZERO;
                            if (map.containsKey(deliveryKey)) {
                                total = total.add(map.get(deliveryKey));
                            }
                            if (map.containsKey(dineInKey)) {
                                total = total.add(map.get(dineInKey));
                            }
                            List<ProductProjectionsUnitDetail> list;
                            if (innerMap.containsKey("F9")) {
                                list = innerMap.get("F9");
                                ProductProjectionsUnitDetail detail = convertIntoProjections(comparison,total, unitId);
                                if (Objects.nonNull(detail)) {
                                    list.add(detail);
                                }
                            }
                            else {
                                list = new ArrayList<>();
                                ProductProjectionsUnitDetail detail = convertIntoProjections(comparison,total, unitId);
                                if (Objects.nonNull(detail)) {
                                    list.add(detail);
                                }
                            }
                            innerMap.put("F9",list);
                        }
                    }
                }
                result.put(AppUtils.getFormattedTime(date,"yyyy-MM-dd"),innerMap);
            }
        } catch (Exception e) {
            LOG.info("Getting Date Wise Totals of sales and F9");
        }
        return result;
    }

    private ProductProjectionsUnitDetail convertIntoProjections(Fountain9SalesComparison comparison, BigDecimal total, Integer unitId) {
        try {
//            int id = masterDataCache.getProductCategory(comparison.getProductId()).getDetail().getId();
            return new ProductProjectionsUnitDetail(unitId,comparison.getProductId(),comparison.getProductName(),0,comparison.getDimension(),total);
        } catch (Exception e) {
            LOG.error("Exception Occurred While converting F9 Comparison to Projection :: ",e);
        }
        return null;
    }

    private Map<Integer, Map<String, Fountain9SalesComparison>> getAllUnitsComparisonMap(Map<Integer, Map<String, ForecastReportResponse>> suggestedMap,
                                                                                         List<Date> datesList, Map<Integer, List<DayCloseEstimateData>> result2) {
        Map<Integer, Map<String, Fountain9SalesComparison>> result = new HashMap<>();
        try {
            for (Map.Entry<Integer,List<DayCloseEstimateData>> entry : result2.entrySet()) {
                Map<String, ForecastReportResponse> suggestedData = suggestedMap.getOrDefault(entry.getKey(),null);
                if (result.containsKey(entry.getKey())) {
                    Map<String, Fountain9SalesComparison> innerMap = result.get(entry.getKey());
                    makeSalesCompMap(datesList, result, entry, suggestedData, innerMap);
                }
                else {
                    Map<String, Fountain9SalesComparison> innerMap = new HashMap<>();
                    makeSalesCompMap(datesList, result, entry, suggestedData, innerMap);
                }
            }
        }
        catch (Exception e) {
            LOG.error("Exception Occurred while generating All units Comparison Map :: ",e);
        }
        return result;
    }

    private void makeSalesCompMap(List<Date> datesList, Map<Integer, Map<String, Fountain9SalesComparison>> result,
                                  Map.Entry<Integer, List<DayCloseEstimateData>> entry, Map<String, ForecastReportResponse> suggestedData, Map<String, Fountain9SalesComparison> innerMap) {
        for (DayCloseEstimateData data : entry.getValue()) {
            String productName = data.getProductName() + "_" + data.getDimension()+"("+data.getProductId()+")";
            String key = AppUtils.getDateString(data.getBusinessDate(),"yyyy-MM-dd:HH:mm:ss")+ "_"+data.getProductId()+ "_"+data.getDimension()+"_"+data.getOrderSource()+"_"+data.getBrandId();
            String mapKey = AppUtils.getDateString(data.getBusinessDate(),"yyyy-MM-dd") + data.getOrderSource();
            if (innerMap.containsKey(productName)) {
                Fountain9SalesComparison salesComparison = innerMap.get(productName);
                Map<String, BigDecimal> salesDataMap = salesComparison.getSalesDataMap();
                if (salesDataMap.containsKey(mapKey)) {
                    salesDataMap.put(mapKey, salesDataMap.get(mapKey).add(BigDecimal.valueOf(data.getQuantity())));
                }
                else {
                    if (datesList.contains(data.getBusinessDate())) {
                        salesDataMap.put(mapKey, BigDecimal.valueOf(data.getQuantity()));
                    }
                }

                Map<String, BigDecimal> f9Map = salesComparison.getFountain9DataMap();
                if (Objects.nonNull(suggestedData)) {
                    if (suggestedData.containsKey(key)) {
                        ForecastReportResponse response = suggestedData.get(key);
                        BigDecimal finalQty = response.getPredictedFinal();
                        if (Objects.nonNull(response.getSafetyStock())) {
                            finalQty = finalQty.add(response.getSafetyStock());
                        }
                        finalQty = finalQty.setScale(0, RoundingMode.HALF_UP);
                        if (f9Map.containsKey(mapKey)) {
                            f9Map.put(mapKey, f9Map.get(mapKey).add(finalQty));
                        }
                        else {
                            f9Map.put(mapKey, finalQty);
                        }
                        salesComparison.setFountain9DataMap(f9Map);
                    }
                }
                salesComparison.setSalesDataMap(salesDataMap);
                innerMap.put(productName,salesComparison);
            }
            else {
                Fountain9SalesComparison salesComparison = new Fountain9SalesComparison();
                salesComparison.setCompleteProductName(productName);
                salesComparison.setProductName(data.getProductName());
                salesComparison.setProductId(data.getProductId());
                salesComparison.setDimension(data.getDimension());
                salesComparison.setBrandId(data.getBrandId());
                Map<String , BigDecimal> salesDataMap = new HashMap<>();
                if (datesList.contains(data.getBusinessDate())) {
                    salesDataMap.put(mapKey, BigDecimal.valueOf(data.getQuantity()));
                }
                salesComparison.setSalesDataMap(salesDataMap);
                if (Objects.nonNull(suggestedData)) {
                    if (suggestedData.containsKey(key)) {
                        ForecastReportResponse response = suggestedData.get(key);
                        BigDecimal finalQty = response.getPredictedFinal();
                        if (Objects.nonNull(response.getSafetyStock())) {
                            finalQty = finalQty.add(response.getSafetyStock());
                        }
                        finalQty = finalQty.setScale(0, RoundingMode.HALF_UP);
                        Map<String , BigDecimal> f9Map = new HashMap<>();
                        f9Map.put(mapKey, finalQty);
                        salesComparison.setFountain9DataMap(f9Map);
                    }
                }
                innerMap.put(productName,salesComparison);
            }
        }
        result.put(entry.getKey(),innerMap);
    }

    private List<RegularOrderEvent> updateRegularOrderingEvent(RegularOrderEvent orderEvent, ReferenceOrderData referenceOrderData) {
        List<RegularOrderEvent> events = new ArrayList<>();
        if (Objects.nonNull(orderEvent)) {
            RegularOrderingEvent regularOrderingEvent = referenceOrderManagementDao.find(RegularOrderingEvent.class, orderEvent.getEventId());
            regularOrderingEvent.setStatus("COMPLETED");
            regularOrderingEvent.setUpdatedAt(AppUtils.getCurrentTimestamp());
            regularOrderingEvent.setReferenceOrderData(referenceOrderData);
            regularOrderingEvent = referenceOrderManagementDao.update(regularOrderingEvent,true);
            SCMDayCloseEventData dayCloseEventData = regularOrderingEvent.getDayCloseEventData();
            return checkForEvents(dayCloseEventData);
        }
        return events;
    }

    private List<RegularOrderEvent> checkForEvents(SCMDayCloseEventData dayCloseEventData) {
        List<RegularOrderEvent> result = new ArrayList<>();
        for (RegularOrderingEvent regularOrderingEvent : dayCloseEventData.getRegularOrderingEvents()) {
            if (regularOrderingEvent.getStatus().equalsIgnoreCase("CREATED")) {
                RegularOrderEvent orderEvent = new RegularOrderEvent(regularOrderingEvent.getEventId(), regularOrderingEvent.getUnitId(), regularOrderingEvent.getBrand()
                        , regularOrderingEvent.getFulfilmentDate(), regularOrderingEvent.getOrderingDays(), regularOrderingEvent.getStatus());
                result.add(orderEvent);
            }
        }
        if (result.size() == 0 ) {
            dayCloseEventData.setOrderingSuccess(AppConstants.YES);
            referenceOrderManagementDao.update(dayCloseEventData,true);
//            reportingService.sendVarianceReport(dayCloseEventData.getBusinessDate(), dayCloseEventData.getUnitId(), false);
//            reportingService.sendVarianceReport(dayCloseEventData.getBusinessDate(), dayCloseEventData.getUnitId(), true);
            LOG.info("All Regular Ordering events Completed Successfully ..!");
        }
        return result;
    }

    @Override
    public void getEstimationData(int brandId, int unitId, Date targetDate, BigDecimal targetSale,BigDecimal dineInSale,BigDecimal deliverySale, String dates, Integer categoryBuffer, Integer buffer, String productIds, String orderType) {
        referenceOrderManagementDao.getEstimationData(brandId, unitId, targetDate, targetSale,dineInSale,deliverySale, dates, categoryBuffer, buffer, productIds, orderType);
    }

    @Override
    public BigDecimal getSalesPercentage(int unitId, int brandId, String businessDate) {

        BigDecimal finalPercentage=BigDecimal.ZERO;
        BigDecimal deliveryPercentage=referenceOrderManagementDao.getSalesPercentage(unitId, brandId, businessDate,AppConstants.DELIVERY);
        BigDecimal totalSale=referenceOrderManagementDao.getSalesPercentage(unitId, brandId, businessDate,AppConstants.TOTAL_SALE);
        finalPercentage = AppUtils.percentage(deliveryPercentage,totalSale);
        return finalPercentage;
    }

    @Override
    public BigDecimal getSalesAmount(int unitId, int brandId, String businessDate) {

        BigDecimal finalAmount=BigDecimal.ZERO;
        finalAmount=referenceOrderManagementDao.getSalesPercentage(unitId, brandId, businessDate,AppConstants.DELIVERY);
        return finalAmount;
    }

    @Override
    public List<Integer> getRequestIdsForEstimation(int unitId, List<Date> businessDate) {
        return referenceOrderManagementDao.getRequestIdsForEstimation(unitId, businessDate);
    }

    @Override
    public List<EstimationShortData> getEstimationQuantity(int unitId, String requestIds) {
        return referenceOrderManagementDao.getEstimationQuantity(unitId, requestIds);
    }

    /*private List<ReferenceOrder> createReferenceOrderObject(List<ReferenceOrderData> referenceOrderDataList) {
        List<ReferenceOrder> referenceOrders = new ArrayList<ReferenceOrder>();
        for (ReferenceOrderData referenceOrderData : referenceOrderDataList) {
            IdCodeName generatedBy = new IdCodeName();
            generatedBy.setId(referenceOrderData.getGeneratedBy());
            generatedBy.setName(masterDataCache.getEmployees().get(referenceOrderData.getGeneratedBy()));
            IdCodeName fulfillmentUnit = new IdCodeName();
            if(referenceOrderData.getFulfillmentUnitId()!=null){
                fulfillmentUnit.setId(referenceOrderData.getFulfillmentUnitId());
                fulfillmentUnit.setName(scmCache.getUnitDetails().get(referenceOrderData.getFulfillmentUnitId()).getUnitName());
            }
            IdCodeName requestingUnit = new IdCodeName();
            requestingUnit.setId(referenceOrderData.getRequestUnitId());
            requestingUnit.setName(scmCache.getUnitDetails().get(referenceOrderData.getRequestUnitId()).getUnitName());
            referenceOrders.add(SCMDataConverter.convert(referenceOrderData, generatedBy, fulfillmentUnit, requestingUnit));
        }
        return referenceOrders;
    }*/

    private ReferenceOrderData persistReferenceOrderItems(ReferenceOrder referenceOrder, ReferenceOrderData referenceOrderData,
                                                          Map<Integer, List<String>> expiryUsageLogs) throws SumoException {
        List<ReferenceOrderMenuItemData> referenceOrderMenuItemDataList = new ArrayList<>();
        List<ReferenceOrderScmItemData> referenceOrderScmItemDataList = new ArrayList<>();
        List<MenuScmKeyData> menuScmKeyDataList = new ArrayList<>();
        for (ReferenceOrderMenuItem referenceOrderMenuItem : referenceOrder.getReferenceOrderMenuItems()) {
            ReferenceOrderMenuItemData referenceOrderMenuItemData = SCMDataConverter.convert(referenceOrderMenuItem, referenceOrderData, false);
            referenceOrderMenuItemData = referenceOrderManagementDao.add(referenceOrderMenuItemData, false);
            if(Objects.nonNull(referenceOrderMenuItem.getDateOrderings())) {
                for (String date : referenceOrderMenuItem.getDateOrderings().keySet()) {
                    MenuScmKeyData menuScmKeyData = new MenuScmKeyData();
                    menuScmKeyData.setReferenceOrderKeyItemId(referenceOrderMenuItemData.getId());
                    menuScmKeyData.setReferenceOrderKeyItemType("MENU_ITEM");
                    menuScmKeyData.setDate(SCMUtil.parseDate(date));
                    menuScmKeyData.setDateType("ORDERING");
                    menuScmKeyData.setQuantity(BigDecimal.valueOf(referenceOrderMenuItem.getDateOrderings().get(date)).setScale(0, RoundingMode.HALF_UP));
                    if(Objects.nonNull(referenceOrderMenuItem.getOriginalDateOrderings()) && referenceOrderMenuItem.getOriginalDateOrderings().containsKey(date)) {
                        menuScmKeyData.setSuggestedQuantity(BigDecimal.valueOf(referenceOrderMenuItem.getOriginalDateOrderings().get(date)).setScale(0, RoundingMode.HALF_UP));
                    }
                    if(Objects.nonNull(referenceOrderMenuItem.getMultiplierMap()) && referenceOrderMenuItem.getMultiplierMap().containsKey(date)) {
                        menuScmKeyData.setOrderingPercentage(BigDecimal.valueOf(referenceOrderMenuItem.getMultiplierMap().get(date)).setScale(0, RoundingMode.HALF_UP));
                    }
                    menuScmKeyDataList.add(menuScmKeyData);
                }
            }
            if(Objects.nonNull(referenceOrderMenuItem.getDateRemaining())) {
                for (String date : referenceOrderMenuItem.getDateRemaining().keySet()) {
                    MenuScmKeyData menuScmKeyData = new MenuScmKeyData();
                    menuScmKeyData.setReferenceOrderKeyItemId(referenceOrderMenuItemData.getId());
                    menuScmKeyData.setReferenceOrderKeyItemType("MENU_ITEM");
                    menuScmKeyData.setDate(SCMUtil.parseDate(date));
                    menuScmKeyData.setDateType("REMAINING");
                    menuScmKeyData.setQuantity(BigDecimal.valueOf(referenceOrderMenuItem.getDateRemaining().get(date)).setScale(0, RoundingMode.HALF_UP));
                    if(Objects.nonNull(referenceOrderMenuItem.getOriginalDateRemaining()) && referenceOrderMenuItem.getOriginalDateRemaining().containsKey(date)) {
                        menuScmKeyData.setSuggestedQuantity(BigDecimal.valueOf(referenceOrderMenuItem.getOriginalDateRemaining().get(date)).setScale(0, RoundingMode.HALF_UP));
                    }
                    menuScmKeyDataList.add(menuScmKeyData);
                }
            }
            referenceOrderMenuItemDataList.add(referenceOrderMenuItemData);
            List<ROMenuItemVariantData> roMenuItemVariantDatas = new ArrayList<>();
            for (ReferenceOrderMenuVariant referenceOrderMenuVariant : referenceOrderMenuItem.getVariants()) {
                ROMenuItemVariantData roMenuItemVariantData = SCMDataConverter.convert(referenceOrderMenuVariant, referenceOrderMenuItemData);
                roMenuItemVariantData = referenceOrderManagementDao.add(roMenuItemVariantData, false);
                roMenuItemVariantDatas.add(roMenuItemVariantData);
            }
            referenceOrderMenuItemData.setVariants(roMenuItemVariantDatas);
        }
        referenceOrderData.setReferenceOrderMenuItemDatas(referenceOrderMenuItemDataList);
        referenceOrderManagementDao.flush();
        for (ReferenceOrderScmItem referenceOrderScmItem : referenceOrder.getReferenceOrderScmItems()) {
            FulfillmentType fulfillmentType = referenceOrderScmItem.getFulfillmentType();
            ReferenceOrderScmItemData referenceOrderScmItemData = SCMDataConverter.convert(referenceOrderScmItem, referenceOrderData, fulfillmentType, expiryUsageLogs);
            referenceOrderScmItemData = referenceOrderManagementDao.add(referenceOrderScmItemData, false);
            referenceOrderManagementDao.addAll(referenceOrderScmItemData.getRoScmItemExpiryData());
            referenceOrderScmItemDataList.add(referenceOrderScmItemData);

            if(Objects.nonNull(referenceOrderScmItem.getDateOrderings())) {
                for (String date : referenceOrderScmItem.getDateOrderings().keySet()) {
                    MenuScmKeyData menuScmKeyData = new MenuScmKeyData();
                    menuScmKeyData.setReferenceOrderKeyItemId(referenceOrderScmItemData.getId());
                    menuScmKeyData.setReferenceOrderKeyItemType("SCM_ITEM");
                    menuScmKeyData.setDate(SCMUtil.parseDate(date));
                    menuScmKeyData.setDateType("ORDERING");
                    menuScmKeyData.setQuantity(BigDecimal.valueOf(referenceOrderScmItem.getDateOrderings().get(date)).setScale(4, RoundingMode.HALF_UP));
                    if (Objects.nonNull(referenceOrderScmItem.getOriginalDateOrderings()) && referenceOrderScmItem.getOriginalDateOrderings().containsKey(date)) {
                        menuScmKeyData.setSuggestedQuantity(BigDecimal.valueOf(referenceOrderScmItem.getOriginalDateOrderings().get(date)).setScale(4, RoundingMode.HALF_UP));
                    }
                    menuScmKeyDataList.add(menuScmKeyData);
                }
            }
            if(Objects.nonNull(referenceOrderScmItem.getDateRemaining())) {
                for (String date : referenceOrderScmItem.getDateRemaining().keySet()) {
                    MenuScmKeyData menuScmKeyData = new MenuScmKeyData();
                    menuScmKeyData.setReferenceOrderKeyItemId(referenceOrderScmItemData.getId());
                    menuScmKeyData.setReferenceOrderKeyItemType("SCM_ITEM");
                    menuScmKeyData.setDate(SCMUtil.parseDate(date));
                    menuScmKeyData.setDateType("REMAINING");
                    menuScmKeyData.setQuantity(BigDecimal.valueOf(referenceOrderScmItem.getDateRemaining().get(date)).setScale(4, RoundingMode.HALF_UP));
                    if(Objects.nonNull(referenceOrderScmItem.getOriginalDateRemaining()) && referenceOrderScmItem.getOriginalDateRemaining().containsKey(date)) {
                        menuScmKeyData.setSuggestedQuantity(BigDecimal.valueOf(referenceOrderScmItem.getOriginalDateRemaining().get(date)).setScale(4, RoundingMode.HALF_UP));
                    }
                    menuScmKeyDataList.add(menuScmKeyData);
                }
            }
        }
        referenceOrderManagementDao.addAll(menuScmKeyDataList);
        referenceOrderData.setReferenceOrderScmItemDatas(referenceOrderScmItemDataList);
        referenceOrderManagementDao.flush();
        return referenceOrderData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void cloneDayCloseDataForUnit(int newUnitId, int cloningUnitId) {
       if(referenceOrderManagementDao.checkIfDataExistForRegularOrderingForCafe(newUnitId)<=0) {
           referenceOrderManagementDao.cloneDayCloseDataForUnit(newUnitId, cloningUnitId, AppUtils.getDateAfterDays(AppUtils.getCurrentDate(), env.getCloningDayDiff()));
       }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<ForecastReportScmResponse> getForecastScmReport(HttpResponse httpScmResponse, RefCreateRequest refCreateRequest, String dateString, String requestedBy) throws IOException{
        BufferedReader in = new BufferedReader(new InputStreamReader(httpScmResponse.getEntity().getContent()));
        String line = null;
        List<ForecastReportScmResponse> forecastScmReportResponseList = new ArrayList<>();
        int counter = 0;
        Date time = AppUtils.getDate(dateString,"yyyy-MM-dd HH:mm:ss");
        while ((line = in.readLine()) != null) {
            if (counter == 0) {
                counter += 1;
                continue;
            }
            String[] data = line.split(",");
            try {
                if (data.length == env.getFountain9ScmColumns().split(",").length) {
                    forecastScmReportResponseList.add(new ForecastReportScmResponse(AppUtils.parseDate(data[0]),data[1],Integer.parseInt(data[2]),data[3],
                            BigDecimal.valueOf(Double.parseDouble(data[4])),BigDecimal.valueOf(Double.parseDouble(data[5])),
                            BigDecimal.valueOf(Double.parseDouble(data[6])),BigDecimal.valueOf(Double.parseDouble(data[7])),BigDecimal.valueOf(Double.parseDouble(data[8])),
                            BigDecimal.valueOf(Double.parseDouble(data[9])),BigDecimal.valueOf(Double.parseDouble(data[10])),BigDecimal.valueOf(Double.parseDouble(data[11])),
                            BigDecimal.valueOf(Double.parseDouble(data[12])),BigDecimal.valueOf(Double.parseDouble(data[13])),time,requestedBy));
                }
                else {
                    String msg = "Error Occurred While parsing the F9 SCM Suggestions for Product Id : " + Integer.parseInt(data[2]) + " Complete Data is :  " + Arrays.toString(data);
                    SlackNotificationService.getInstance().sendNotification(env.getEnvType(), "SUMO",
                            SlackNotification.SUPPLY_CHAIN, msg);
                }
            }
            catch (Exception e) {
                LOG.error("Exception occurred while parsing scm Suggestion ::: ",e);
            }
        }
        try {
            in.close();
        } catch (Exception e) {
            LOG.error("Error in closing buffered reader", e);
        }
        return forecastScmReportResponseList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void saveSuggestions(List<ForecastReportResponse> value, List<ForecastReportScmResponse> forecastScmReportResponseList, int unitId) {
        LOG.info("Saving Fountain 9 Menu Suggestions For Predictive Quantity for Unit :: {}",unitId);
        referenceOrderManagementDao.addAll(value);
        LOG.info("Saving Fountain 9 Scm Suggestions For Predictive Quantity for Unit :: {}",unitId);
        referenceOrderManagementDao.addAll(forecastScmReportResponseList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<String, Integer> getFulfilmentUnits(Integer requestingUnitId) {
        Map<String, Integer> result = new HashMap<>();
        List<FulfillmentUnitMappingData> fulfillmentUnitMappingData = referenceOrderManagementDao.getFulfilmentUnits(requestingUnitId);
        fulfillmentUnitMappingData.forEach(e -> {
            if (!e.getFulfillmentType().equalsIgnoreCase(FulfillmentType.EXTERNAL.value())) {
                result.put(e.getFulfillmentType(), e.getFulfillingUnitId());
            }
        });
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Pair<String, List<ForecastReportResponse>> getForecastReport(HttpResponse response, RefCreateRequest refCreateRequest) throws IOException {
		BufferedReader in = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
		String line = null;
		List<ForecastReportResponse> forecastReportResponseList = new ArrayList<>();
		int counter = 0;
        Date timeStamp = AppUtils.getCurrentTimestamp();
        String dateString = AppUtils.getDateString(timeStamp,"yyyy-MM-dd HH:mm:ss");
        Date time = AppUtils.getDate(dateString,"yyyy-MM-dd HH:mm:ss");
		while ((line = in.readLine()) != null) {
			if (counter == 0) {
				counter += 1;
				continue;
			}
			String[] data = line.split(",");
            String cafeId = data[2];
            BigDecimal safetyStock = Objects.nonNull(refCreateRequest.getMax_capping_safety_stock_multiplier()) ? BigDecimal.valueOf(Double.parseDouble(data[21])) : null;
			forecastReportResponseList.add(new ForecastReportResponse(AppUtils.parseDate(data[0]), data[1], cafeId.length() > 50 ? cafeId.substring(0,50) : cafeId,
					data[3], data[4], data[5], data[6], data[7], data[8], data[9], data[10], data[11],
					BigDecimal.valueOf(Double.parseDouble(data[12])), BigDecimal.valueOf(Double.parseDouble(data[13])),
					BigDecimal.valueOf(Double.parseDouble(data[14])), BigDecimal.valueOf(Double.parseDouble(data[15])),
					BigDecimal.valueOf(Double.parseDouble(data[16])), BigDecimal.valueOf(Double.parseDouble(data[17])),
					BigDecimal.valueOf(Double.parseDouble(data[18])), BigDecimal.valueOf(Double.parseDouble(data[19])),
					BigDecimal.valueOf(Double.parseDouble(data[20])), time ,safetyStock));
		}
        try {
            in.close();
        } catch (Exception e) {
            LOG.error("Error in closing buffered reader", e);
        }
        return new Pair<>(dateString,forecastReportResponseList);
	}

    public Integer getProductIdFromSkuId(String skuID){
        String[] list=skuID.split("_");
        return Integer.parseInt(list[0]);
    }
    public String getDimensionFromSkuId(String skuID){
        String[] list=skuID.split("_");
        return list[1];
    }

    @Override
    public void convertForecastEstimationDetail(List<ForecastReportResponse> forecastReportResponseList, int unitId, List<String> remainingDays,
                                                List<EstimationShortData> remainingDayData, List<EstimationShortData> orderingDayData) {
        Map<ForecastEstimationKey,EstimationShortData> remDayShortDataMap=new HashMap<>();
        Map<ForecastEstimationKey,EstimationShortData> ordDayShortDataMap=new HashMap<>();

        List<Integer> navratraProducts = new ArrayList<>(Arrays.asList(1022, 1024, 1025, 1217, 1220, 1234, 1260, 1301, 1302, 1409, 1410, 1454, 1455, 1000006, 1000122, 1000123, 1000124, 1000125));
        for(ForecastReportResponse forecastReportResponse:forecastReportResponseList) {
            if (navratraProducts.contains(getProductIdFromSkuId(forecastReportResponse.getSkuId()))) {
                LOG.info("This is a Navrata Product : {} response id is : {}",forecastReportResponse.getSkuId(),forecastReportResponse.getId());
                continue;
            }

            if(remainingDays.contains(AppUtils.generateSimpleDateFormat(forecastReportResponse.getDate()))){
                setShortDataMap(forecastReportResponse, unitId, remDayShortDataMap);
            }else{
                setShortDataMap(forecastReportResponse, unitId, ordDayShortDataMap);
            }
        }
        remainingDayData.addAll(new ArrayList<>(remDayShortDataMap.values()));
        orderingDayData.addAll(new ArrayList<>(ordDayShortDataMap.values()));
    }

    public void setShortDataMap(ForecastReportResponse forecastReportResponse, Integer unitId, Map<ForecastEstimationKey, EstimationShortData> shortDataMap){
        try{
            ForecastEstimationKey forecastEstimationKey=new ForecastEstimationKey(forecastReportResponse.getSkuId(),forecastReportResponse.getL2Category(),
                    AppUtils.getFormattedTime(forecastReportResponse.getDate(),"yyyy-MM-dd"));
            BigDecimal safetyStock = Objects.nonNull(forecastReportResponse.getSafetyStock()) ? forecastReportResponse.getSafetyStock() : new BigDecimal("0");
            BigDecimal total = BigDecimal.valueOf(forecastReportResponse.getPredictedFinal().intValue()).add(BigDecimal.valueOf(safetyStock.intValue()));
            BigDecimal totalOriginal = forecastReportResponse.getPredictedFinal().add(safetyStock);
            LOG.info("for sku : {} predicted final is : {} and safety stock is : {} total is : {}",forecastReportResponse.getSkuId(),forecastReportResponse.getPredictedFinal(),safetyStock,total);
//            total = total.setScale(0, RoundingMode.HALF_UP);
            int finalValue = total.intValue();
            LOG.info("total got after Rounding Half Up is : {} Final Value after Conversion is : {}",total,finalValue);
            if(shortDataMap.containsKey(forecastEstimationKey)){
                EstimationShortData estimationShortDataKey= shortDataMap.get(forecastEstimationKey);
                estimationShortDataKey.setQuantity(estimationShortDataKey.getQuantity() + finalValue);
                BigDecimal finalQty = estimationShortDataKey.getOriginalQuantity().add(totalOriginal);
                estimationShortDataKey.setOriginalQuantity(finalQty);
                estimationShortDataKey.setSales(AppUtils.add(estimationShortDataKey.getSales(),forecastReportResponse.getValue()));
                shortDataMap.put(forecastEstimationKey,estimationShortDataKey);
                LOG.info("Quantity got for product {} {} ::{}",forecastReportResponse.getSkuId(),
                        forecastReportResponse.getPredictedFinal(),estimationShortDataKey.getQuantity());
            }
            else{
                EstimationShortData estimationShortData=new EstimationShortData();
                estimationShortData.setCategoryId(masterDataCache.getProductBasicDetail(getProductIdFromSkuId(forecastReportResponse.getSkuId())).getType());
                estimationShortData.setDimension(getDimensionFromSkuId(forecastReportResponse.getSkuId()));
                estimationShortData.setQuantity(finalValue);
                estimationShortData.setOriginalQuantity(totalOriginal);
                estimationShortData.setSales(forecastReportResponse.getValue());
                estimationShortData.setUnitId(unitId);
                estimationShortData.setProductId(getProductIdFromSkuId(forecastReportResponse.getSkuId()));
                estimationShortData.setCategory(masterDataCache.getProductCategory(estimationShortData.getCategoryId()).getDetail().getName());
                estimationShortData.setDate(AppUtils.getFormattedTime(forecastReportResponse.getDate(),"yyyy-MM-dd"));
                shortDataMap.put(forecastEstimationKey,estimationShortData);
                LOG.info("Quantity got for product {} {} ::{}",forecastReportResponse.getSkuId(),
                        forecastReportResponse.getPredictedFinal(),estimationShortData.getQuantity());
            }
        }catch (Exception e){
            LOG.error("Exception Caught While getting day for product {}",forecastReportResponse.getSkuId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<String, Map<String, Boolean>> getDayWiseBrandStatus(int unitId) {
        Map<String, Map<String, Boolean>> result = new HashMap<>();
        try {
            List<RegularOrderUnitBrandData> regularOrderUnitBrandDataList = referenceOrderManagementDao.getUnitOrderingSchedule(unitId, true);
            for (RegularOrderUnitBrandData brandData : regularOrderUnitBrandDataList) {
                for (UnitOrderScheduleData scheduleData : brandData.getUnitOrderScheduleData()) {
                    if (result.containsKey(getBrandName(brandData.getBrandId()))) {
                        Map<String, Boolean> innerMap = result.get(getBrandName(brandData.getBrandId()));
                        if (innerMap.containsKey(scheduleData.getOrderingDay())) {
                            LOG.info("Day type : {} is already stored in Map ", scheduleData.getOrderingDay());
                        } else {
                            innerMap.put(scheduleData.getOrderingDay(), scheduleData.getIsFunctional().equalsIgnoreCase(AppConstants.YES));
                            result.put(getBrandName(brandData.getBrandId()), innerMap);
                        }
                    } else {
                        Map<String, Boolean> innerMap = new HashMap<>();
                        innerMap.put(scheduleData.getOrderingDay(), scheduleData.getIsFunctional().equalsIgnoreCase(AppConstants.YES));
                        result.put(getBrandName(brandData.getBrandId()), innerMap);
                    }
                }
            }

        } catch (Exception e) {
            LOG.error("Exception Occurred While making the Day Brand Status :: ", e);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<RegularOrderUnitBrand> getUnitOrderingSchedule(Integer unitId) {
        List<RegularOrderUnitBrand> result = new ArrayList<>();
        try {
            List<RegularOrderUnitBrandData> regularOrderUnitBrandData = referenceOrderManagementDao.getUnitOrderingSchedule(unitId,false);
            getOrderSchedule(result, regularOrderUnitBrandData);
        }
        catch (Exception e) {
            LOG.error("Error Occurred While finding the unit ordering schedule ",e);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<RegularOrderUnitBrand> getAllOrderingSchedules() {
        List<RegularOrderUnitBrand> result = new ArrayList<>();
        try {
            List<RegularOrderUnitBrandData> regularOrderUnitBrandData = referenceOrderManagementDao.getAllOrderingSchedules();
            getOrderSchedule(result, regularOrderUnitBrandData);
        }
        catch (Exception e) {
            LOG.error("Error Occurred While finding the unit ordering schedule ",e);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public String getMaxCap(int unitId, String brandName) {
        try {
            RegularOrderUnitBrandData regularOrderUnitBrandData = referenceOrderManagementDao.getMaxCap(unitId,Integer.parseInt(getBrandId(brandName)));
            if (Objects.nonNull(regularOrderUnitBrandData) && Objects.nonNull(regularOrderUnitBrandData.getMaxCap())) {
                return regularOrderUnitBrandData.getMaxCap();
            }
        }
        catch (Exception e) {
            LOG.error("Error Occurred While finding the unit Max Cap ::: ",e);
        }
        return "-1";
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Boolean checkForF9Orders(Integer requestingUnitId, Integer fulfilmentUnitId, Date fulfilmentDate) {
        try {
            List<RequestOrderData> requestOrderData = referenceOrderManagementDao.checkForF9Orders(requestingUnitId,fulfilmentUnitId,fulfilmentDate);
            return !requestOrderData.isEmpty();
        } catch (Exception e) {
            LOG.error("Exception Occurred While Checking for cancelled F9 Orders.. :: ",e);
        }
        return false;
    }

    @Override
    public Boolean validateReferenceOrderTime(RegularOrderEvent orderEvent) {
        try {
            RegularOrderingEvent eventData = referenceOrderManagementDao.find(RegularOrderingEvent.class,orderEvent.getEventId());
            Date finalTime = AppUtils.getUpdatedTimeInDate(9,30,0,AppUtils.getDayBeforeOrAfterDay(eventData.getFulfilmentDate(),-2));
            LOG.info("Current Time is : {} and Final Time is : {} and fulfilment date is : {} for event Id : {}",AppUtils.getCurrentTimestamp(),
                    finalTime,eventData.getFulfilmentDate(),eventData.getEventId());
            if (AppUtils.getCurrentTimestamp().compareTo(finalTime) <= 0) {
                LOG.info("Reference Order is Placed before 9.30 AM :: ");
                return true;
            }
            return false;
        } catch (Exception e) {
            LOG.error("Exception Occurred while validating Reference Order Time ::: ", e);
            return false;
        }
    }

    private void getOrderSchedule(List<RegularOrderUnitBrand> result, List<RegularOrderUnitBrandData> regularOrderUnitBrandData) {
        for (RegularOrderUnitBrandData data : regularOrderUnitBrandData) {
            RegularOrderUnitBrand entry = new RegularOrderUnitBrand(data.getId(), data.getUnitId(), data.getBrandId());
            entry.setFunctional(data.getIsFunctional().equalsIgnoreCase(AppConstants.YES));
            List<UnitOrderSchedule> unitOrderSchedules = new ArrayList<>();
            for (UnitOrderScheduleData scheduleData : data.getUnitOrderScheduleData()) {
                UnitOrderSchedule orderSchedule = new UnitOrderSchedule(scheduleData.getId(), scheduleData.getOrderingDay(),scheduleData.getOrderingDays());
                orderSchedule.setOrderingDay(scheduleData.getIsOrderingDay().equalsIgnoreCase(AppConstants.YES));
                orderSchedule.setFunctional(scheduleData.getIsFunctional().equalsIgnoreCase(AppConstants.YES));
                unitOrderSchedules.add(orderSchedule);
            }
            entry.setUnitOrderSchedules(unitOrderSchedules);
            result.add(entry);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean addUnitOrderingSchedule(List<RegularOrderUnitBrand> unitBrandList,Integer userId) throws SumoException {
        if (checkForSchedule(unitBrandList)) {
            try {
                for (RegularOrderUnitBrand orderUnitBrand : unitBrandList) {
                    LOG.info("JSpn is : {}", JSONSerializer.toJSON(orderUnitBrand));
                    createUnitOrderingSchedule(userId, orderUnitBrand);
                }
                return true;
            } catch (Exception e) {
                LOG.error("Error Occurred While Submitting unit ordering schedule..! :: ", e);
                return false;
            }
        }
        else {
            throw new SumoException("Schedule already Created ...!","Ordering Schedule is Already Created for unit : <b> "+masterDataCache.getUnit(unitBrandList.get(0).getUnitId()).getName()+" </b>");
        }
    }

    private void createUnitOrderingSchedule(Integer userId, RegularOrderUnitBrand orderUnitBrand) throws SumoException {
        RegularOrderUnitBrandData regularOrderUnitBrandData = new RegularOrderUnitBrandData();
        regularOrderUnitBrandData.setUnitId(orderUnitBrand.getUnitId());
        regularOrderUnitBrandData.setBrandId(orderUnitBrand.getBrandId());
        regularOrderUnitBrandData.setMaxCap("20");
        regularOrderUnitBrandData.setIsFunctional(orderUnitBrand.getFunctional() ? AppConstants.YES : AppConstants.NO);
        regularOrderUnitBrandData.setIsManual(orderUnitBrand.getManual().equalsIgnoreCase("Manual") ? AppConstants.YES : AppConstants.NO);
        regularOrderUnitBrandData.setCreatedBy(userId);
        regularOrderUnitBrandData.setCreatedAt(AppUtils.getCurrentTimestamp());
        regularOrderUnitBrandData = referenceOrderManagementDao.add(regularOrderUnitBrandData, true);
        if (Objects.nonNull(regularOrderUnitBrandData)) {
            List<UnitOrderScheduleData> list = new ArrayList<>();
            for (UnitOrderSchedule orderSchedule : orderUnitBrand.getUnitOrderSchedules()) {
                UnitOrderScheduleData scheduleData = new UnitOrderScheduleData();
                scheduleData.setOrderingDay(orderSchedule.getOrderingDayType());
                scheduleData.setIsFunctional(orderSchedule.getFunctional() ? AppConstants.YES : AppConstants.NO);
                scheduleData.setIsOrderingDay(orderSchedule.getOrderingDay() ? AppConstants.YES : AppConstants.NO);
                scheduleData.setOrderingDays(orderSchedule.getOrderingDays());
                scheduleData.setRegularOrderUnitBrandData(regularOrderUnitBrandData);
                scheduleData = referenceOrderManagementDao.add(scheduleData, false);
                list.add(scheduleData);
            }
            regularOrderUnitBrandData.setUnitOrderScheduleData(list);
            referenceOrderManagementDao.update(regularOrderUnitBrandData, true);
        }
    }

    private boolean checkForSchedule(List<RegularOrderUnitBrand> unitBrandList) {
        Integer unitId = unitBrandList.get(0).getUnitId();
        return referenceOrderManagementDao.checkForOrderingSchedule(unitId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean updateUnitOrderingSchedule(List<RegularOrderUnitBrand> unitBrandList, Integer userId) {
        try {
            for (RegularOrderUnitBrand orderUnitBrand : unitBrandList) {
                if (Objects.nonNull(orderUnitBrand.getId())) {
                    RegularOrderUnitBrandData regularOrderUnitBrandData = referenceOrderManagementDao.find(RegularOrderUnitBrandData.class, orderUnitBrand.getId());
                    if (Objects.nonNull(regularOrderUnitBrandData)) {
                        regularOrderUnitBrandData.setIsFunctional(orderUnitBrand.getFunctional() ? AppConstants.YES : AppConstants.NO);
                        regularOrderUnitBrandData.setLastUpdatedBy(userId);
                        regularOrderUnitBrandData.setLastUpdatedTime(AppUtils.getCurrentTimestamp());
                        for (UnitOrderSchedule orderSchedule : orderUnitBrand.getUnitOrderSchedules()) {
                            UnitOrderScheduleData unitOrderScheduleData = referenceOrderManagementDao.find(UnitOrderScheduleData.class, orderSchedule.getId());
                            if (Objects.nonNull(unitOrderScheduleData)) {
                                unitOrderScheduleData.setIsFunctional(orderSchedule.getFunctional() ? AppConstants.YES : AppConstants.NO);
                                unitOrderScheduleData.setIsOrderingDay(orderSchedule.getOrderingDay() ? AppConstants.YES : AppConstants.NO);
                                unitOrderScheduleData.setOrderingDays(orderSchedule.getOrderingDays());
                            }
                            referenceOrderManagementDao.update(unitOrderScheduleData, false);
                        }
                    }
                    referenceOrderManagementDao.update(regularOrderUnitBrandData, false);
                } else {
                    createUnitOrderingSchedule(userId,orderUnitBrand);
                }
            }
            referenceOrderManagementDao.flush();
            return true;
        } catch (Exception e) {
            LOG.error("Error Occurred while Updating Ordering Schedule ::: ",e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public OrderScheduleClone getAvailableUnitsForSchedule() {
        OrderScheduleClone result = new OrderScheduleClone();
        try {
            List<Integer> f9EnabledUnits = getFountain9Units(null, false);
            List<Integer> unitsWithSchedules = referenceOrderManagementDao.getUnitsWithSchedules();
            List<Integer> unitsWithNoSchedules = new ArrayList<>();
            result.setUnitsWithSchedules(unitsWithSchedules);
            f9EnabledUnits.forEach(e -> {
                if (!unitsWithSchedules.contains(e)) {
                    unitsWithNoSchedules.add(e);
                }
            });
            result.setUnitsWithNoSchedules(unitsWithNoSchedules);
        } catch (Exception e) {
            LOG.error("Exception Occurred while getting the Units Available to make Schedule ::: ",e);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean cloneUnitOrderingSchedule(OrderScheduleClone orderScheduleClone, Integer userId) throws SumoException {
        try {
            List<RegularOrderUnitBrandData> regularOrderUnitBrandDataList = referenceOrderManagementDao.getUnitOrderingSchedule(orderScheduleClone.getCloneFromUnit(), false);
            List<Integer> unitsSelectedForCloning = orderScheduleClone.getSelectedUnitsForCloning();
            List<Integer> unitsWithSchedules = referenceOrderManagementDao.getUnitsWithSchedules();
            List<String> unitsAlreadyHadSchedules = new ArrayList<>();
            for (Integer unitId : unitsWithSchedules) {
                if (unitsSelectedForCloning.contains(unitId)) {
                    unitsAlreadyHadSchedules.add(masterDataCache.getUnit(unitId).getName());
                }
            }
            if (!unitsAlreadyHadSchedules.isEmpty()) {
                throw new SumoException("Some selected Units already have Schedules", "List of Units Which Already Have Schedules : " + Arrays.toString(unitsAlreadyHadSchedules.toArray()));
            }
            if (!regularOrderUnitBrandDataList.isEmpty()) {
                for (Integer unitId : orderScheduleClone.getSelectedUnitsForCloning()) {
                    for (RegularOrderUnitBrandData cloneFromUnitData : regularOrderUnitBrandDataList) {
                        RegularOrderUnitBrandData regularOrderUnitBrandData = new RegularOrderUnitBrandData();
                        regularOrderUnitBrandData.setUnitId(unitId);
                        regularOrderUnitBrandData.setBrandId(cloneFromUnitData.getBrandId());
                        regularOrderUnitBrandData.setMaxCap(cloneFromUnitData.getMaxCap());
                        regularOrderUnitBrandData.setIsFunctional(cloneFromUnitData.getIsFunctional());
                        regularOrderUnitBrandData.setIsManual(cloneFromUnitData.getIsManual());
                        regularOrderUnitBrandData.setCreatedBy(userId);
                        regularOrderUnitBrandData.setCreatedAt(AppUtils.getCurrentTimestamp());
                        regularOrderUnitBrandData = referenceOrderManagementDao.add(regularOrderUnitBrandData, true);
                        if (Objects.nonNull(regularOrderUnitBrandData)) {
                            List<UnitOrderScheduleData> list = new ArrayList<>();
                            for (UnitOrderScheduleData orderSchedule : cloneFromUnitData.getUnitOrderScheduleData()) {
                                UnitOrderScheduleData scheduleData = new UnitOrderScheduleData();
                                scheduleData.setOrderingDay(orderSchedule.getOrderingDay());
                                scheduleData.setIsFunctional(orderSchedule.getIsFunctional());
                                scheduleData.setIsOrderingDay(orderSchedule.getIsOrderingDay());
                                scheduleData.setOrderingDays(orderSchedule.getOrderingDays());
                                scheduleData.setRegularOrderUnitBrandData(regularOrderUnitBrandData);
                                scheduleData = referenceOrderManagementDao.add(scheduleData, false);
                                list.add(scheduleData);
                            }
                            regularOrderUnitBrandData.setUnitOrderScheduleData(list);
                            referenceOrderManagementDao.update(regularOrderUnitBrandData, true);
                        }
                    }
                }
            }
            else {
                throw new SumoException("No Schedule Found ", "Can't Find Schedule for the selected Unit : <b> " + masterDataCache.getUnit(orderScheduleClone.getCloneFromUnit()).getName() + "</b>");
            }
            return true;
        }
        catch (SumoException e) {
            throw e;
        }
        catch (Exception e) {
            LOG.error("Exception Occurred while cloning :: ",e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<Integer> getFountain9Units(Integer unitId, Boolean isForceLookUp) {
        List<Integer> result = new ArrayList<>();
        try {
            masterDataCache.getAllUnits().forEach(e -> {
                if (Objects.nonNull(e.getF9Enabled()) && e.getF9Enabled().equalsIgnoreCase(AppConstants.YES)) {
                    result.add(e.getId());
                }
            });
            LOG.info("Size of f9 units before is : {} and isForceLookup : {}",result.size(),isForceLookUp);
            if (isForceLookUp) {
                if (!result.contains(unitId)) {
                    Boolean f9Enabled = referenceOrderManagementDao.checkFountain9Enabled(unitId);
                    if (f9Enabled) {
                        // update in master cache and DB
                        LOG.info("Fountain 9 is enable for unit : {} updating the master cache and Unit data", masterDataCache.getUnit(unitId).getName());
                        Map<String, Integer> uriVariables = new HashMap<>();
                        uriVariables.put("unitId", unitId);
                        String endpoint = env.getMasterServiceBasePath() + MasterServiceClientEndpoints.UPDATE_UNIT_F9_ENABLED;
                        WebServiceHelper.exchangeWithAuth(endpoint, env.getAuthToken(), HttpMethod.POST, null, null, uriVariables);
                        result.add(unitId);
                    } else {
                        LOG.info("Fountain 9 is not enabled for unit : {}", masterDataCache.getUnit(unitId).getName());
                    }
                }
                else {
                    LOG.info("Current unit : {} is in the cache..!",masterDataCache.getUnit(unitId).getName());
                }
            }
            LOG.info("Size of f9 units after is : {} and isForceLookup : {}",result.size(),isForceLookUp);
        } catch (Exception e) {
            LOG.error("Exception Occurred while getting fountain 9 units list :: ",e);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ExpiryProduct> getNewExpiryProducts(Integer unitId, Date lastDate) {
        List<ExpiryProduct> result = new ArrayList<>();
        try {
            List<Object []> expiryProducts = referenceOrderManagementDao.getNewExpiryProducts(unitId);
            for (Object [] product : expiryProducts){
                ExpiryProduct expiryProduct = new ExpiryProduct();
                expiryProduct.setProductId((Integer) product[0]);
                expiryProduct.setProductName((String) product[1]);
                Date businessDate = SCMUtil.getScmBusinessDate((Date) product[2]);
                expiryProduct.setExpiryDate(SCMUtil.getScmBusinessDate((Date) product[2]));
                if (lastDate.compareTo(businessDate) < 0) {
                    expiryProduct.setExpiryDate(lastDate);
                }
                BigDecimal quantity = (BigDecimal) product[3];
                expiryProduct.setExpiryQuantity(quantity.floatValue());
                result.add(expiryProduct);
            }
        }
        catch (Exception e) {
            LOG.error("Exception Occurred while getting new expiry products :: ",e);
        }
        return result.stream().sorted(Comparator.comparing(ExpiryProduct::getExpiryDate)).collect(Collectors.toList());
    }

    private Integer getUnitId(String cafeId) {
        String arr[] = cafeId.split("_");
        return Integer.parseInt(arr[0]);
    }


    private BigDecimal getStockOutPercentage(ScmProductConsumptionAverage scmProductConsumptionAverage,Map<Integer,Map<Integer,Pair<BigDecimal,BigDecimal>>> aggregatedStockOutMap ,
                                             Integer dayOfWeek){
        if(!aggregatedStockOutMap.containsKey(scmProductConsumptionAverage.getProductId()) || !aggregatedStockOutMap.get(scmProductConsumptionAverage.getProductId()).containsKey(dayOfWeek)){
            return BigDecimal.ZERO;
        }
        Pair<BigDecimal,BigDecimal> downtimeToTotalTime = aggregatedStockOutMap.get(scmProductConsumptionAverage.getProductId()).
                get(dayOfWeek);
        BigDecimal stockOutPercentage =  AppUtils.multiply(AppUtils.divide(downtimeToTotalTime.getKey(),downtimeToTotalTime.getValue()),BigDecimal.valueOf(100));
        scmProductConsumptionAverage.setStockOutPercentage(stockOutPercentage);
        scmProductConsumptionAverage.setStockOutRaw(downtimeToTotalTime.getKey());
        scmProductConsumptionAverage.setCafeTotalHours(downtimeToTotalTime.getValue());
        return stockOutPercentage;

    }

    private BigDecimal getWastagePercentage(ScmProductConsumptionAverage scmProductConsumptionAverage,Map<Integer,Map<Integer,BigDecimal>> aggregatedWastageMap ,
                                             Integer dayOfWeek, BigDecimal totalConsumption){
        if(!aggregatedWastageMap.containsKey(scmProductConsumptionAverage.getProductId()) || !aggregatedWastageMap.get(scmProductConsumptionAverage.getProductId()).
                containsKey(dayOfWeek)){
            return BigDecimal.ZERO;
        }
        BigDecimal totalWastage = aggregatedWastageMap.get(scmProductConsumptionAverage.getProductId()).get(dayOfWeek);
        BigDecimal wastagePercentage =  AppUtils.multiply(AppUtils.divide(totalWastage,totalConsumption),BigDecimal.valueOf(100));
        scmProductConsumptionAverage.setWastagePercentage(wastagePercentage);
        scmProductConsumptionAverage.setWastageRaw(totalWastage);
        return wastagePercentage;

    }

    private String getCommentForAdjustedQuantity(BigDecimal extra , BigDecimal wastage , BigDecimal shortFall , String matrixCode, BigDecimal adjustment){
        // matrix code HIGH_LOW means HIGH stock out low wastage
        if(matrixCode.equalsIgnoreCase("HIGH_HIGH")){
            return "Increased Ordering by " + extra +  " Quantity as both Stock out and Wastage  is high";
        }else if (matrixCode.equalsIgnoreCase("HIGH_LOW")){
            return "Increased Ordering by " + shortFall +  " Quantity(shortFall) as  Stock out is high and Wastage  is low";
        }else if(matrixCode.equalsIgnoreCase("LOW_HIGH")){
            return "Decreased Ordering by " + adjustment +  " Quantity(wastage) as  Stock out is low and Wastage  is high";
        }else if(matrixCode.equalsIgnoreCase("LOW_LOW")){
            if(adjustment.compareTo(BigDecimal.ZERO)!=0){
                return "Increased Ordering by " + adjustment +  " Quantity(shortfall/2) as  Stock out is low and Wastage  is low";
            }else{
                return "";
            }
        }
        return "";
    }

    private String getCommentForZeroConsumptionAdjustment(){
        return "Increased Ordering By Adjusted Quantity As consumption was 0";
    }

    private BigDecimal applyBufferQuantity(BigDecimal a , BigDecimal bufferPercentage){
        if(Objects.isNull(bufferPercentage) || bufferPercentage.compareTo(BigDecimal.ZERO) == 0){
            return a;
        }
        return  AppUtils.multiply(a,bufferPercentage);
    }



    private void processConsumptionDataAccordingToMatrix(ScmProductConsumptionAverage scmProductConsumptionAverage,BigDecimal stockOutPercentage ,
                                                         BigDecimal wastagePercentage){
        BigDecimal stockOutThreshold  = BigDecimal.valueOf(Float.valueOf(env.getThresholdStockOutPercentage()));
        BigDecimal wastageThreshold  = BigDecimal.valueOf(Float.valueOf(env.getThresholdWastagePercentage()));
        BigDecimal shortFall = AppUtils.divide(AppUtils.multiply(stockOutPercentage,
                scmProductConsumptionAverage.getOriginalConsumption()),BigDecimal.valueOf(100));
        BigDecimal wastage = AppUtils.divide(AppUtils.multiply(wastagePercentage,
                scmProductConsumptionAverage.getOriginalConsumption()),BigDecimal.valueOf(100));
        BigDecimal extra = AppUtils.subtract(shortFall,wastage);

        if(BigDecimal.ZERO.compareTo(scmProductConsumptionAverage.getSuggestedQuantity()) == 0){
            scmProductConsumptionAverage.setComments(getCommentForZeroConsumptionAdjustment());
        } else if(stockOutPercentage.compareTo(stockOutThreshold) > 0 && wastagePercentage.compareTo(wastageThreshold) > 0){
            if(extra.compareTo(BigDecimal.ZERO)>0){
                scmProductConsumptionAverage.setAdjustedQuantity(extra);
                scmProductConsumptionAverage.setAverageConsumption(applyBufferQuantity(scmProductConsumptionAverage.getOriginalConsumption().add(extra),
                        scmProductConsumptionAverage.getBufferQuantity()));
                scmProductConsumptionAverage.setOrderingQuantity(applyBufferQuantity(scmProductConsumptionAverage.getOriginalConsumption().add(extra),
                        scmProductConsumptionAverage.getBufferQuantity()));
                scmProductConsumptionAverage.setSuggestedQuantity(applyBufferQuantity(scmProductConsumptionAverage.getOriginalConsumption().add(extra),
                        scmProductConsumptionAverage.getBufferQuantity()));
                scmProductConsumptionAverage.setComments(getCommentForAdjustedQuantity(extra,wastage,shortFall,"HIGH_HIGH", extra));
            }
        }else if(stockOutPercentage.compareTo(stockOutThreshold) > 0 && wastagePercentage.compareTo(wastageThreshold) <= 0){
            scmProductConsumptionAverage.setAdjustedQuantity(shortFall);
            scmProductConsumptionAverage.setAverageConsumption(applyBufferQuantity(scmProductConsumptionAverage.getOriginalConsumption().add(shortFall)
            ,scmProductConsumptionAverage.getBufferQuantity()));
            scmProductConsumptionAverage.setOrderingQuantity(applyBufferQuantity(scmProductConsumptionAverage.getOriginalConsumption().add(shortFall),
                    scmProductConsumptionAverage.getBufferQuantity()));
            scmProductConsumptionAverage.setSuggestedQuantity(applyBufferQuantity(scmProductConsumptionAverage.getOriginalConsumption().add(shortFall)
            ,scmProductConsumptionAverage.getBufferQuantity()));
            scmProductConsumptionAverage.setComments(getCommentForAdjustedQuantity(extra,wastage,shortFall,"HIGH_LOW", shortFall));
        }else if(stockOutPercentage.compareTo(stockOutThreshold) <= 0 && wastagePercentage.compareTo(wastageThreshold) > 0){
            scmProductConsumptionAverage.setAdjustedQuantity(SCMUtil.getMinBigdecimal(scmProductConsumptionAverage.getOriginalConsumption(), wastage).negate());
            scmProductConsumptionAverage.setAverageConsumption(applyBufferQuantity(SCMUtil.getMaxBigdecimal(BigDecimal.ZERO,
                    scmProductConsumptionAverage.getOriginalConsumption().subtract(wastage)),scmProductConsumptionAverage.getBufferQuantity()));
            scmProductConsumptionAverage.setOrderingQuantity(applyBufferQuantity(SCMUtil.getMaxBigdecimal(BigDecimal.ZERO,
                    scmProductConsumptionAverage.getOriginalConsumption().subtract(wastage)),scmProductConsumptionAverage.getBufferQuantity()));
            scmProductConsumptionAverage.setSuggestedQuantity(applyBufferQuantity(SCMUtil.getMaxBigdecimal(BigDecimal.ZERO,
                    scmProductConsumptionAverage.getOriginalConsumption().subtract(wastage)),scmProductConsumptionAverage.getBufferQuantity()));
            scmProductConsumptionAverage.setComments(getCommentForAdjustedQuantity(extra,wastage,shortFall,"LOW_HIGH", SCMUtil.getMinBigdecimal(scmProductConsumptionAverage.getOriginalConsumption(),
                    wastage)));
        }else if(stockOutPercentage.compareTo(stockOutThreshold) <= 0 && wastagePercentage.compareTo(wastageThreshold) <= 0){
            if (wastagePercentage.compareTo((AppUtils.divide(wastageThreshold, BigDecimal.valueOf(2)))) < 0) {
                BigDecimal adjustedQuantity = AppUtils.divide(shortFall,BigDecimal.valueOf(2));
                scmProductConsumptionAverage.setAdjustedQuantity(adjustedQuantity);
                scmProductConsumptionAverage.setAverageConsumption(applyBufferQuantity(scmProductConsumptionAverage.getOriginalConsumption().add(adjustedQuantity),
                        scmProductConsumptionAverage.getBufferQuantity()));
                scmProductConsumptionAverage.setOrderingQuantity(applyBufferQuantity(scmProductConsumptionAverage.getOriginalConsumption().add(adjustedQuantity),
                        scmProductConsumptionAverage.getBufferQuantity()));
                scmProductConsumptionAverage.setSuggestedQuantity(applyBufferQuantity(scmProductConsumptionAverage.getOriginalConsumption().add(adjustedQuantity),
                        scmProductConsumptionAverage.getBufferQuantity()));
                scmProductConsumptionAverage.setComments(getCommentForAdjustedQuantity(extra,wastage,shortFall,"LOW_LOW",adjustedQuantity));
            }
        }

    }
    private void processStockOutAndWastageData(ScmProductConsumptionAverage scmProductConsumptionAverage,Map<Integer,Map<Integer,Pair<BigDecimal,BigDecimal>>> aggregatedStockOutMap ,
                                               Map<Integer,Map<Integer,BigDecimal>> aggregatedWastageMap, Integer dayOfWeek,BigDecimal totalConsumption){
        BigDecimal stockOutPercentage = getStockOutPercentage(scmProductConsumptionAverage,aggregatedStockOutMap,dayOfWeek);
        BigDecimal wastagePercentage = getWastagePercentage(scmProductConsumptionAverage,aggregatedWastageMap,dayOfWeek,totalConsumption);
        processConsumptionDataAccordingToMatrix(scmProductConsumptionAverage,stockOutPercentage,wastagePercentage);


    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<String, List<ScmProductConsumptionAverage>> getScmProductsConsumptionAverage(int unitId, String brandName, List<Date> daysList,
                                                                                            Map<String, BigDecimal> dayWiseOrderingPercentage) throws SumoException {
        Map<String, List<ScmProductConsumptionAverage>> result = new HashMap<>();
        try {
            // I have Business Dates I will get Last 6 weeks Date of Each Day Type, and filter latest 4 after filtering big days.
            Map<Date, List<Date>> last6WeekDatesMap = SCMUtil.getLastNWeekDates(daysList, 6);
            LOG.info("::: Getting Big Days List Before Checking Size Of Last 6 Weeks List is : {} :::", last6WeekDatesMap.size());
            List<Date> bigDaysList = stockManagementDao.getChaayosBigDays( last6WeekDatesMap.values().stream().flatMap(List::stream).toList() );
            List<Date> last4WeekDates = new ArrayList<>();
            for(Map.Entry<Date, List<Date>> last6WeekDates : last6WeekDatesMap.entrySet()) {
                List<Date> dates = last6WeekDates.getValue();
                if( !CollectionUtils.isEmpty(bigDaysList) ) {
                    dates.removeAll(bigDaysList);
                }
                Collections.sort(dates);
                last4WeekDates.addAll(dates.subList(0, Math.min(4, dates.size())));
            }
            LOG.info("::: Before Filtering Big Days Size Of Last 4 Weeks List is : {} :::", last4WeekDates.size());
            if(CollectionUtils.isEmpty(last4WeekDates)) {
                throw new SumoException("Last four week dates are Empty, please recheck the date");
            }
            // getting Kettle Day Close Ids For these Business Dates
            List<SCMDayCloseEventData> kettleDayCloses = stockManagementDao.getKettleDayCloseForBusinessDates(last4WeekDates, unitId);
            // Now getting the Bulk Order Consumption Day Wise
            DayWiseOrderConsumptionRequest finalResult;
            try {
                DayWiseOrderConsumptionRequest bulkOrderExclusionRequest = new DayWiseOrderConsumptionRequest();
                bulkOrderExclusionRequest.setUnitId(unitId);
                bulkOrderExclusionRequest.setDayWiseOrderConsumptionItems(getDayWiseOrderConsumptionItems(kettleDayCloses));
                bulkOrderExclusionRequest.setExcludeOnlyBulkOrders(true);
                bulkOrderExclusionRequest.setBulkOrderMinimLimit(getBulkOrderMinimLimit());

                if(AppUtils.isDev(env.getEnvType())) {
                    finalResult = bulkOrderExclusionRequest;
                    finalResult.setDayCloseEventWiseConsumption(new HashMap<>());
                } else {
                    String endpoint = env.getKettleServiceBasePath() + KettleServiceClientEndpoints.GET_ORDER_CONSUMPTION_DATA;
                    DayWiseOrderConsumptionRequest kettleResult = WebServiceHelper.exchangeWithAuth(endpoint,
                            env.getAuthToken(), HttpMethod.POST, DayWiseOrderConsumptionRequest.class, JSONSerializer.toJSON(bulkOrderExclusionRequest), null);
                    ObjectMapper mapper = new ObjectMapper();
                    finalResult = mapper.convertValue(kettleResult, new TypeReference<>() {});
                    LOG.info("Got Bulk Order Consumption Data From Kettle : for Unit Id : {}", unitId);
                }
            } catch (Exception e) {
                LOG.error(":::: Failed To Get Consumption Of Bulk Orders :: ", e);
                throw new SumoException("Failed To Get Consumption Of Bulk Orders", "Failed  To get Consumption Of Bulk Orders Please Try After Some Time..!");
            }

            // getting the Consumption Which i got from the Kettle Day CLose for these Business Dates
            LOG.info("For Unit Id : {} Got the Last 4 Weeks Kettle Day Closes : {}", unitId, Arrays.toString(last4WeekDates.toArray()));
            // Now Getting the Consumption Avg ProductWise
            Map<Integer, Map<Integer, ScmProductConsumptionDTO>> eventProductWiseConsumption = getEventProductWiseConsumption(finalResult,
                    kettleDayCloses.stream().mapToInt(SCMDayCloseEventData::getEventId).boxed().collect(Collectors.toList()));
            Map<Integer,Map<Integer,Pair<BigDecimal,BigDecimal>>> aggregatedStockOutMap = new HashMap<>();
            Map<Integer,Map<Integer,BigDecimal>> aggregatedWastageMap = new HashMap<>();
            if(SuggestiveOrderingCompanyTypes.GNT.name().equalsIgnoreCase(brandName) || SuggestiveOrderingCompanyTypes.CHAAYOS_AND_GNT.name().equalsIgnoreCase(brandName)){
                aggregatedStockOutMap = stockManagementDao.getAggregatedStockOutData(last4WeekDates,unitId,SuggestiveOrderingCompanyTypes.GNT.getBrandId());
                LOG.info("aggregated StockOut Map : {} ", new Gson().toJson(aggregatedStockOutMap));
                List<Integer> stockTakeDayCloseIds  = stockManagementDao.getStockTakeDailyDayCloseForBusinessDates(last4WeekDates,unitId);
                List<Integer> eligibleProductIds = stockManagementDao.getProductIdsByCategoryLevelAndProductType(SCMServiceConstants.CATEGORY_LEVEL_1,
                        SCMServiceConstants.GNT_PRODUCT_TYPE);
                aggregatedWastageMap = stockManagementDao.getAggregatedWastageData(last4WeekDates,unitId,stockTakeDayCloseIds,eligibleProductIds);
                LOG.info("aggregated Wastage Map : {} ", new Gson().toJson(aggregatedWastageMap));
            }


            Map<Integer, Map<Integer, Map<MeanTypeEnum, Pair<Integer, BigDecimal>>>> productWiseConsumptionCountOf8Weeks = getProductWiseConsumptionStatsOf8Weeks(eventProductWiseConsumption);

//            List<Object[]> scmProductConsumptionAveragesList = stockManagementDao.getScmProductsConsumptionAverage(unitId, brandName,
//                    kettleDayCloses.stream().mapToInt(SCMDayCloseEventData::getEventId).boxed().collect(Collectors.toList()));
            if (!productWiseConsumptionCountOf8Weeks.isEmpty()) {
                for (Map.Entry<Integer, Map<Integer, Map<MeanTypeEnum, Pair<Integer, BigDecimal>>>> productEntry : productWiseConsumptionCountOf8Weeks.entrySet()) {
                    Integer productId = productEntry.getKey();
                    Map<Integer, Map<MeanTypeEnum, Pair<Integer, BigDecimal>>> dayMap = productEntry.getValue();

                    for (Map.Entry<Integer, Map<MeanTypeEnum, Pair<Integer, BigDecimal>>> dayEntry : dayMap.entrySet()) {
                        Integer dayOfWeek = dayEntry.getKey();
                        Map<MeanTypeEnum, Pair<Integer, BigDecimal>> meanMap = dayEntry.getValue();

                        BigDecimal consumption = SCMUtil.multiplyWithScale(BigDecimal.valueOf(0.6), getMean(meanMap, MeanTypeEnum.GEOMETRIC), 6)
                                    .add(SCMUtil.multiplyWithScale(BigDecimal.valueOf(0.4), getMean(meanMap, MeanTypeEnum.UPPER_GEOMETRIC), 6));
                        BigDecimal totalConsumption = meanMap.get(MeanTypeEnum.ARITHMETIC).getValue();

                        if (consumption.compareTo(BigDecimal.ZERO) > 0 && checkProductActiveStatus(productId)) {
                            Date date = getDateFromDayOfWeek(daysList, dayOfWeek);
                            if (Objects.isNull(date)) {
                                LOG.info("This Should not Happen ::: got dat of week is : {} and list of Dates are {}", dayOfWeek, Arrays.toString(daysList.toArray()));
                                throw new SumoException("This should not happen :: ", "Please Contact Sumo Team..!");
                            } else {
                                String dayType = AppUtils.getFormattedTime(date, "yyyy-MM-dd");
                                BigDecimal bufferPercentage = getPercentage(dayWiseOrderingPercentage.getOrDefault(dayType, BigDecimal.valueOf(100)));
                                BigDecimal consumptionAfterApplyingPercentage = getValueAfterPercentage(consumption, bufferPercentage);
                                ProductDefinition productDefinition = scmCache.getProductDefinition(productId);
                                if (brandName.equalsIgnoreCase(SuggestiveOrderingCompanyTypes.CHAAYOS.name())) {
                                    if (ProductType.GNT.value().equalsIgnoreCase(productDefinition.getProductType())) {
                                        continue;
                                    }
                                } else if (brandName.equalsIgnoreCase(SuggestiveOrderingCompanyTypes.GNT.name())) {
                                    if (!ProductType.GNT.value().equalsIgnoreCase(productDefinition.getProductType())) {
                                        continue;
                                    }
                                }
                                SuggestiveOrderingCompanyTypes type = SuggestiveOrderingCompanyTypes.valueOf(brandName);
                                if(!type.equals(SuggestiveOrderingCompanyTypes.CHAAYOS_AND_GNT)){
                                    if( productDefinition.getBrandId() != null && !productDefinition.getBrandId().equals(type.getBrandId()) ) {
                                        continue;
                                    }
                                }

                                List<ScmProductConsumptionAverage> scmProductConsumptionAverages;
                                if (result.containsKey(dayType)) {
                                    scmProductConsumptionAverages = result.get(dayType);
                                } else {
                                    scmProductConsumptionAverages = new ArrayList<>();
                                }
                                ScmProductConsumptionAverage scmProductConsumptionAverage = new ScmProductConsumptionAverage();
                                scmProductConsumptionAverage.setProductId(productId);
                                scmProductConsumptionAverage.setOrderingDay(date);
                                scmProductConsumptionAverage.setAverageConsumption(consumptionAfterApplyingPercentage);
                                scmProductConsumptionAverage.setOrderingQuantity(consumptionAfterApplyingPercentage);
                                scmProductConsumptionAverage.setSuggestedQuantity(consumptionAfterApplyingPercentage);
                                scmProductConsumptionAverages.add(scmProductConsumptionAverage);
                                if((SuggestiveOrderingCompanyTypes.GNT.name().equalsIgnoreCase(brandName) || SuggestiveOrderingCompanyTypes.CHAAYOS_AND_GNT.name().equalsIgnoreCase(brandName)) &&
                                          SCMServiceConstants.GNT_PRODUCT_TYPE.equalsIgnoreCase(productDefinition.getProductType())  &&
                                        SCMServiceConstants.CATEGORY_LEVEL_1.equalsIgnoreCase(productDefinition.getCategoryLevel())
                                        && productDefinition.getFulfillmentType().equals(FulfillmentType.KITCHEN)){
                                         scmProductConsumptionAverage.setOriginalConsumption(consumption);
                                         scmProductConsumptionAverage.setBufferQuantity(bufferPercentage);
                                         scmProductConsumptionAverage.setTotalConsumption(totalConsumption);
                                         processStockOutAndWastageData(scmProductConsumptionAverage,aggregatedStockOutMap,aggregatedWastageMap,dayOfWeek,totalConsumption);
                                }
                                result.put(dayType, scmProductConsumptionAverages);
                            }
                        }
                    }
                }
            }
        } catch (SumoException e) {
            throw e;
        } catch (Exception e) {
            LOG.error("Exception Occurred While Calculating the Consumption Average For Suggestive Ordering ::: ", e);
        }
        return result;
    }

    private BigDecimal calculateMean(StrategyType strategyType, List<BigDecimal> values) {
        return switch (strategyType) {
            case GM, GM_WITH_EXPIRY -> calculateGeometricMean(values);
            default -> throw new IllegalArgumentException("Unsupported StrategyType: " + strategyType);
        };
    }

    private BigDecimal  calculateGeometricMean(List<BigDecimal> values) {
        BigDecimal product = BigDecimal.ONE;
        int count = 0;

        for (BigDecimal val : values) {
            if (val.compareTo(BigDecimal.ZERO) > 0) {
                product = product.multiply(val);
                count++;
            }
        }

        if (count == 0) return BigDecimal.ZERO;

        double gm = Math.pow(product.doubleValue(), 1.0 / count);
        return BigDecimal.valueOf(gm).setScale(4, RoundingMode.HALF_UP);
    }

    private BigDecimal computeWeightedMean(StrategyType strategyType, List<BigDecimal> values, List<Double> weights) {
        return switch (strategyType) {
            case GM, GM_WITH_EXPIRY -> computeWeightedGeometricMean(values, weights);
            default -> throw new IllegalArgumentException("Unsupported StrategyType: " + strategyType);
        };
    }

    private BigDecimal computeWeightedGeometricMean(List<BigDecimal> values, List<Double> weights) {
        double sumWeightedLogs = 0.0;
        double sumWeights = 0.0;

        for (int i = 0; i < values.size(); i++) {
            BigDecimal val = values.get(i);
            double weight = weights.get(i);

            // Skip non-positive values (log is undefined)
            if (val.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            // Weighted sum of logarithms
            sumWeightedLogs += weight * Math.log(val.doubleValue());
            sumWeights += weight;
        }

        if (sumWeights == 0) {
            return BigDecimal.ZERO;
        }

        double weightedGM = Math.exp(sumWeightedLogs / sumWeights);
        return BigDecimal.valueOf(weightedGM).setScale(4, RoundingMode.HALF_UP);
    }

    private int getWeekNumber(LocalDate date, List<LocalDate> sortedDates, int datesPerWeek) {
        int index = sortedDates.indexOf(date);
        if (index == -1) return -1;
        return (index/datesPerWeek) + 1;
    }


    private boolean isDateInRange(LocalDate date, int startWeek, int endWeek, List<LocalDate> sortedDates, int datesPerWeek) {
        if (date == null || startWeek <= 0 || endWeek <= 0 || startWeek > endWeek || sortedDates == null) return false;
        int weekNumber = getWeekNumber(date, sortedDates, datesPerWeek);
        return weekNumber >= startWeek && weekNumber <= endWeek;
    }

    private int getMaxBaisQuantity(List<SalesForecastingInputData> baisSalesForecastingInputDataList, String productDimensionKey) {

        int maxBaisQuantity=0;

        List<SalesForecastingInputData> filteredData = baisSalesForecastingInputDataList.stream().
                filter(data -> Boolean.parseBoolean(String.valueOf((data.getProductId() + "_" + data.getDimension()).equals(productDimensionKey))))
                .toList();

        int currentDaySalesQuantity = filteredData.stream()
                .filter(data -> data.getDateOfOrdering().equals(SCMUtil.dateToLocalDate(AppUtils.getDateAfterDays(AppUtils.getCurrentDate(), -1))))
                .mapToInt(SalesForecastingInputData::getTotalSaleQuantity)
                .sum();

        int previousDaySalesQuantity = filteredData.stream()
                .filter(data -> data.getDateOfOrdering().equals(SCMUtil.dateToLocalDate(AppUtils.getDateAfterDays(AppUtils.getCurrentDate(), -2))))
                .mapToInt(SalesForecastingInputData::getTotalSaleQuantity)
                .sum();

        int previousWeekSalesQuantity = filteredData.stream()
                .filter(data -> data.getDateOfOrdering().equals(SCMUtil.dateToLocalDate(AppUtils.getDateAfterDays(AppUtils.getCurrentDate(), -8))))
                .mapToInt(SalesForecastingInputData::getTotalSaleQuantity)
                .sum();

        int previousWeekSalesQuantity2 = filteredData.stream()
                .filter(data -> data.getDateOfOrdering().equals(SCMUtil.dateToLocalDate(AppUtils.getDateAfterDays(AppUtils.getCurrentDate(), -9))))
                .mapToInt(SalesForecastingInputData::getTotalSaleQuantity)
                .sum();

        maxBaisQuantity = ((currentDaySalesQuantity-previousWeekSalesQuantity)+(previousDaySalesQuantity-previousWeekSalesQuantity2))/2;

        return maxBaisQuantity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<String, List<MenuProductSalesAverageDto>> getMenuProductsConsumptionAverage(int unitId, String brandName, List<LocalDate> daysList) throws SumoException {
        Map<String, List<MenuProductSalesAverageDto>> result = new HashMap<>();
        try {

            SuggestiveOrderingStrategyMetadata strategy = suggestiveOrderingStrategyDao
                    .findByUnitIdAndStatus(unitId, SwitchStatus.ACTIVE);

            if (Objects.isNull(strategy)) {
                throw new SumoException("Suggestive Ordering Strategy is not available for unit :: " + unitId);
            }

            Map<Date, List<Date>> lastNWeekDatesMap = SCMUtil.getLastNWeekDates(daysList.stream().map(SCMUtil::localDateToDate).collect(Collectors.toList()),
                            strategy.getNoOfWeeks() + strategy.getSafetyWeeks()).entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().subList(0, Math.min(strategy.getNoOfWeeks(), e.getValue().size()))));

            List<LocalDate> sortedDates = lastNWeekDatesMap.values().stream()
                    .flatMap(List::stream)
                    .distinct()
                    .map(SCMUtil::dateToLocalDate)
                    .sorted(Comparator.reverseOrder())
                    .toList();

            List<LocalDate> baisCalculatingDays = Stream.of(
                    AppUtils.getDateAfterDays(AppUtils.getCurrentDate(), -1),
                    AppUtils.getDateAfterDays(AppUtils.getCurrentDate(), -2),
                    AppUtils.getDateAfterDays(AppUtils.getCurrentDate(), -8),
                    AppUtils.getDateAfterDays(AppUtils.getCurrentDate(), -9)
            ).map(SCMUtil::dateToLocalDate)
                    .sorted(Comparator.reverseOrder()).toList();

            List<SalesForecastingInputData> baisSalesForecastingInputDataList = salesForecastingInputDataDao.findByUnitIdAndDateIn(unitId, baisCalculatingDays);

            List<SalesForecastingInputData> salesForecastingInputDataList = salesForecastingInputDataDao.findByUnitIdAndDateIn(unitId, sortedDates);

            Map<String, Map<LocalDate, List<BigDecimal>>> productDimensionDateSalesMap = new HashMap<>();
            Map<String, SalesForecastingInputData> productMap = new HashMap<>();

            //Group by productDimension → date → List of sale quantities
            for (SalesForecastingInputData data : salesForecastingInputDataList) {
                LocalDate date = data.getDateOfOrdering();
                String productDimensionKey = data.getProductId() + "_" + data.getDimension();

                if (data.getTotalSaleQuantity() == null) continue;

                productDimensionDateSalesMap
                        .computeIfAbsent(productDimensionKey, k -> new HashMap<>())
                        .computeIfAbsent(date, k -> new ArrayList<>())
                        .add(BigDecimal.valueOf(data.getTotalSaleQuantity()));

                productMap.putIfAbsent(productDimensionKey, data);
            }

            //For each parentDate (forecast date)
            for (Map.Entry<Date, List<Date>> parentEntry : lastNWeekDatesMap.entrySet()) {
                Date parentDate = parentEntry.getKey();
                List<LocalDate> historicalDates = parentEntry.getValue().stream().map(SCMUtil::dateToLocalDate)
                        .sorted(Comparator.reverseOrder()).toList();

                List<MenuProductSalesAverageDto> menuProductSalesAverageList = new ArrayList<>();

                for (String productDimensionKey : productDimensionDateSalesMap.keySet()) {
                    Map<LocalDate, List<BigDecimal>> dateWiseSales = productDimensionDateSalesMap.get(productDimensionKey);

                    // Map of Strategy → List of total sale quantities during its date range
                    Map<StrategyMeanMetaData, List<BigDecimal>> strategyWiseSalesMap = new HashMap<>();

                    for (StrategyMeanMetaData strategyMeta : strategy.getStrategyMeanMetaDataSet()) {
                        if (!SCMServiceConstants.STRATEGY_MEAN_METADATA_STATUS_ACTIVE.equalsIgnoreCase(strategyMeta.getStatus()))
                            continue;

                        // Strategy's applicable date range (weeks)
                        int startWeek = strategyMeta.getStartWeek();
                        int endWeek = strategyMeta.getEndWeek();

                        List<BigDecimal> allSales = new ArrayList<>();

                        for (int i = 0; i < historicalDates.size(); i++) {
                            int weekNum = i + 1;
                            if (weekNum < startWeek || weekNum > endWeek) continue;

                            LocalDate date = historicalDates.get(i);
                            List<BigDecimal> dailySales = dateWiseSales.getOrDefault(date, Collections.emptyList());
                            allSales.addAll(dailySales);
                        }

                        if (!allSales.isEmpty()) {
                            strategyWiseSalesMap.put(strategyMeta, allSales);
                        }
                    }

                    //Calculate final mean
                    BigDecimal finalAverageSalesQuantity = strategyWiseSalesMap.entrySet().stream()
                        .map(entry -> {
                            StrategyMeanMetaData strategyMeta = entry.getKey();
                            List<BigDecimal> sales = entry.getValue();
                            BigDecimal mean = calculateMean(strategy.getStrategyType(), sales); // This can be arithmetic/geometric
                            return mean.multiply(strategyMeta.getPercentage().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP));
                        })
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                    int maxBiasQuantity = getMaxBaisQuantity(baisSalesForecastingInputDataList, productDimensionKey);

                    MenuProductSalesAverageDto dto = new MenuProductSalesAverageDto();
                    dto.setDate(SCMUtil.dateToLocalDate(parentDate));
                    dto.setProductId(Integer.parseInt(productDimensionKey.split("_")[0]));
                    if(Objects.nonNull(productMap.get(productDimensionKey))){
                        dto.setProductName(productMap.get(productDimensionKey).getProductName());
                        dto.setProductCategory(productMap.get(productDimensionKey).getProductCategory());
                    }
                    dto.setDimension(!productDimensionKey.split("_")[1].equalsIgnoreCase("null") ? productDimensionKey.split("_")[1] : "None");
                    dto.setAverageSalesQuantity(finalAverageSalesQuantity.setScale(0, RoundingMode.HALF_UP).intValue());
                    dto.setMaxBiasQuantity(maxBiasQuantity);

                    menuProductSalesAverageList.add(dto);
                }
                result.put(SCMUtil.dateToLocalDate(parentDate).toString(), menuProductSalesAverageList);
            }
            LOG.info("MENU Products Consumption Result is : {}", result);
            return result;
        }
        catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private BigDecimal getValueAfterPercentage(BigDecimal consumption, BigDecimal percentage) {
        return SCMUtil.multiplyWithScale(consumption, percentage, 6);
    }

    private BigDecimal getPercentage(BigDecimal orderingPercentage) {
        return SCMUtil.divideWithScale(orderingPercentage, BigDecimal.valueOf(100), 6);
    }

    private BigDecimal getMean(Map<MeanTypeEnum, Pair<Integer, BigDecimal>> meanMap, MeanTypeEnum type) {
        Pair<Integer, BigDecimal> pair = meanMap.get(type);
        if (pair == null || pair.getKey() == 0) return BigDecimal.ZERO;

        switch (type) {
            case ARITHMETIC:
                return SCMUtil.divideWithScale(pair.getValue(), BigDecimal.valueOf(pair.getKey()), 6);
            case GEOMETRIC:
                return BigDecimal.valueOf(Math.pow(pair.getValue().doubleValue(), 1.0 / pair.getKey()));
            case HARMONIC:
                return SCMUtil.divideWithScale(BigDecimal.valueOf(pair.getKey()), pair.getValue(), 6);
            case UPPER_GEOMETRIC:
            case LOWER_GEOMETRIC:
            case UPPER_HARMONIC:
            case LOWER_HARMONIC:
                return pair.getValue(); // Already precomputed
            default:
                throw new IllegalArgumentException("Unsupported MeanTypeEnum: " + type);
        }
    }



    private Map<Integer, Map<Integer, Map<MeanTypeEnum, Pair<Integer, BigDecimal>>>> getProductWiseConsumptionStatsOf8Weeks(Map<Integer, Map<Integer, ScmProductConsumptionDTO>> eventProductWiseConsumption) {

        Map<Integer, Map<Integer, Map<MeanTypeEnum, Pair<Integer, BigDecimal>>>> result = new HashMap<>();
        Map<String, TreeMap<Date, BigDecimal>> lastTwoDaysMap = new HashMap<>();

        for (Map<Integer, ScmProductConsumptionDTO> productMap : eventProductWiseConsumption.values()) {
            for (ScmProductConsumptionDTO dto : productMap.values()) {
                if (dto == null || dto.getConsumptionQuantity() == null || dto.getConsumptionQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }

                int productId = dto.getProductId();
                int dayOfWeek = dto.getDayOfWeek();
                BigDecimal quantity = dto.getConsumptionQuantity();
                Date date = dto.getBusinessDate();

                result
                        .computeIfAbsent(productId, k -> new HashMap<>())
                        .computeIfAbsent(dayOfWeek, d -> new EnumMap<>(MeanTypeEnum.class));

                Map<MeanTypeEnum, Pair<Integer, BigDecimal>> meanMap = result.get(productId).get(dayOfWeek);

                // Arithmetic Mean (Sum)
                meanMap.compute(MeanTypeEnum.ARITHMETIC, (m, p) -> {
                    if (p == null) return new Pair<>(1, quantity);
                    p.setKey(p.getKey() + 1);
                    p.setValue(SCMUtil.add(p.getValue(), quantity));
                    return p;
                });

                // Geometric Mean (Product)
                meanMap.compute(MeanTypeEnum.GEOMETRIC, (m, p) -> {
                    if (p == null) return new Pair<>(1, quantity);
                    p.setKey(p.getKey() + 1);
                    p.setValue(p.getValue().multiply(quantity));
                    return p;
                });

                // Harmonic Mean (Sum of Reciprocals)
                meanMap.compute(MeanTypeEnum.HARMONIC, (m, p) -> {
                    BigDecimal reciprocal = BigDecimal.ONE.divide(quantity, 10, RoundingMode.HALF_UP);
                    if (p == null) return new Pair<>(1, reciprocal);
                    p.setKey(p.getKey() + 1);
                    p.setValue(p.getValue().add(reciprocal));
                    return p;
                });

                // Track quantities by date per (productId, dayOfWeek)
                String key = productId + "-" + dayOfWeek;
                lastTwoDaysMap
                        .computeIfAbsent(key, k -> new TreeMap<>(Comparator.reverseOrder()))
                        .put(date, quantity);
            }
        }

        // Compute upper and lower geometric & harmonic means
        for (Map.Entry<String, TreeMap<Date, BigDecimal>> entry : lastTwoDaysMap.entrySet()) {
            String[] parts = entry.getKey().split("-");
            int productId = Integer.parseInt(parts[0]);
            int dayOfWeek = Integer.parseInt(parts[1]);

            List<BigDecimal> upperTwo = entry.getValue().values().stream().limit(2).toList();
            List<BigDecimal> lowerTwo = entry.getValue().descendingMap().values().stream().limit(2).toList();

            if (upperTwo.size() == 2) {
                BigDecimal q1 = upperTwo.get(0), q2 = upperTwo.get(1);
                BigDecimal gm = BigDecimal.valueOf(Math.sqrt(q1.multiply(q2).doubleValue()));
                BigDecimal hm = SCMUtil.divideWithScale(BigDecimal.valueOf(2),
                        BigDecimal.ONE.divide(q1, 10, RoundingMode.HALF_UP).add(BigDecimal.ONE.divide(q2, 10, RoundingMode.HALF_UP)), 6);

                result.get(productId).get(dayOfWeek).put(MeanTypeEnum.UPPER_GEOMETRIC, new Pair<>(2, gm));
                result.get(productId).get(dayOfWeek).put(MeanTypeEnum.UPPER_HARMONIC, new Pair<>(2, hm));
            }

            if (lowerTwo.size() == 2) {
                BigDecimal q1 = lowerTwo.get(0), q2 = lowerTwo.get(1);
                BigDecimal gm = BigDecimal.valueOf(Math.sqrt(q1.multiply(q2).doubleValue()));
                BigDecimal hm = SCMUtil.divideWithScale(BigDecimal.valueOf(2),
                        BigDecimal.ONE.divide(q1, 10, RoundingMode.HALF_UP).add(BigDecimal.ONE.divide(q2, 10, RoundingMode.HALF_UP)), 6);

                result.get(productId).get(dayOfWeek).put(MeanTypeEnum.LOWER_GEOMETRIC, new Pair<>(2, gm));
                result.get(productId).get(dayOfWeek).put(MeanTypeEnum.LOWER_HARMONIC, new Pair<>(2, hm));
            }
        }

        return result;
    }


    private Map<Integer, Map<Integer, ScmProductConsumptionDTO>> getEventProductWiseConsumption(DayWiseOrderConsumptionRequest dayWiseOrderConsumptionRequest,
                                                                                                List<Integer> kettleDayCloseIds) {
        //<Day_Close_Event_ID, <Product_Id, Object>
        Map<Integer, Map<Integer, ScmProductConsumptionDTO>> result = new HashMap<>();
        List<ScmProductConsumptionDTO> scmProductConsumptionDataList = stockManagementDao.getScmProductConsumptionDataOfDayCloses(kettleDayCloseIds);

        for (ScmProductConsumptionDTO consumptionDTO : scmProductConsumptionDataList) {
            Calendar calendar = SCMUtil.getCalendar(consumptionDTO.getBusinessDate());
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            consumptionDTO.setDayOfWeek(calendar.get(Calendar.DAY_OF_WEEK));
            Map<Integer, ScmProductConsumptionDTO> innerMap;
            if (result.containsKey(consumptionDTO.getDayCloseEventId())) {
                innerMap = result.get(consumptionDTO.getDayCloseEventId());
            } else {
                 innerMap = new HashMap<>();
            }
            innerMap.put(consumptionDTO.getProductId(), consumptionDTO);
            result.put(consumptionDTO.getDayCloseEventId(), innerMap);
        }
        LOG.info("Started Calculating Consumption After Removing Bulk Orders for Unit Id : {}", dayWiseOrderConsumptionRequest.getUnitId());
        // Now Subtracting the Bulk Orders Consumption From the Actual Consumption Happend On that Day For Every Day Close
        for (Map.Entry<Integer, List<Consumable>> mapEntry : dayWiseOrderConsumptionRequest.getDayCloseEventWiseConsumption().entrySet()) {
            Map<Integer, ScmProductConsumptionDTO> scmProductConsumptionDTOMap = result.get(mapEntry.getKey());
            if (Objects.nonNull(scmProductConsumptionDTOMap)) {
                for (Consumable consumable : mapEntry.getValue()) {
                    ScmProductConsumptionDTO productActualConsumption = scmProductConsumptionDTOMap.get(consumable.getProductId());
                    if (Objects.nonNull(productActualConsumption)) {
                        LOG.info("Updating Consumption For Unit Id : {} For Business Date : {} For Product Id : {} Bulk Consumption is : {}",
                                dayWiseOrderConsumptionRequest.getUnitId(), productActualConsumption.getBusinessDate(), productActualConsumption.getProductId(), consumable.getQuantity());
                        BigDecimal currentConsumptionQuantity = productActualConsumption.getConsumptionQuantity();
                        BigDecimal afterRemovingBulkQuantity = SCMUtil.subtract(currentConsumptionQuantity, consumable.getQuantity());
                        productActualConsumption.setConsumptionQuantity(afterRemovingBulkQuantity);
                        scmProductConsumptionDTOMap.put(productActualConsumption.getProductId(), productActualConsumption);
                    }
                }
            }
            result.put(mapEntry.getKey(), scmProductConsumptionDTOMap);
        }
        LOG.info("Completed Calculating Consumption After Removing Bulk Orders for Unit Id : {}", dayWiseOrderConsumptionRequest.getUnitId());
        return result;
    }

    private BigDecimal getBulkOrderMinimLimit() {
        return new BigDecimal(env.getBulkOrderMinimLimit());
    }

    private List<DayWiseOrderConsumptionItem> getDayWiseOrderConsumptionItems(List<SCMDayCloseEventData> kettleDayCloses) {
        List<DayWiseOrderConsumptionItem> result = new ArrayList<>();
        Map<Integer, SCMDayCloseEventData> dayCloseEventsMap = kettleDayCloses.stream().collect(Collectors.toMap(SCMDayCloseEventData::getEventId, Function.identity()));
        List<SCMDayCloseEventRangeData> scmDayCloseEventRangeDataList = stockManagementDao.getScmDayCloseEventRangeDataList(
                kettleDayCloses.stream().mapToInt(SCMDayCloseEventData::getEventId).boxed().collect(Collectors.toList())
        );
        if (Objects.nonNull(scmDayCloseEventRangeDataList)) {
            for (SCMDayCloseEventRangeData dayCloseEventRangeData : scmDayCloseEventRangeDataList) {
                DayWiseOrderConsumptionItem DayWiseOrderConsumptionItem = new DayWiseOrderConsumptionItem();
                DayWiseOrderConsumptionItem.setSumoDayCloseId(dayCloseEventRangeData.getEventId().getEventId());
                DayWiseOrderConsumptionItem.setKettleDayCloseId(dayCloseEventsMap.get(dayCloseEventRangeData.getEventId().getEventId()).getDayClosureId());
                DayWiseOrderConsumptionItem.setKettleOrderStartId(dayCloseEventRangeData.getStartId());
                DayWiseOrderConsumptionItem.setKettleOrderEndId(dayCloseEventRangeData.getEndId());
                result.add(DayWiseOrderConsumptionItem);
            }
        }
        return result;
    }

    private boolean checkProductActiveStatus(Integer productId) {
        ProductDefinition productDefinition = scmCache.getProductDefinition(productId);
        return productDefinition != null && productDefinition.getProductStatus().equals(ProductStatus.ACTIVE) && productDefinition.isAvailableAtCafe();
    }

    private Date getDateFromDayOfWeek(List<Date> daysList, Integer dayOfWeek) {
        for (Date date : daysList) {
            Calendar calendar = SCMUtil.getCalendar(date);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            if(calendar.get(Calendar.DAY_OF_WEEK) == dayOfWeek) {
                return date;
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ApiResponse getFreezeSuggestedQuantity(Integer loggedInUnit) {
        List<Integer> list = referenceOrderManagementDao.getFreezeSuggestedQuantity(loggedInUnit, AppConstants.ACTIVE);
        if(CollectionUtils.isEmpty(list)) {
            ApiResponse response =  new ApiResponse( null);
            response.setMessage("No Freeze Suggested Quantity Found");
            response.setSuccess(false);
            return response;
        } else {
            return new ApiResponse(list);
        }
    }

    @Override
    public SuggestiveOrderingStrategyMetadata checkForSuggestiveOrderingStrategyAvailable(Integer unitId) {
        try {
            SuggestiveOrderingStrategyMetadata strategy = suggestiveOrderingStrategyDao.findByUnitIdAndStatus(unitId, SwitchStatus.ACTIVE);

            if (Objects.nonNull(strategy) && Objects.nonNull(strategy.getStrategyMeanMetaDataSet())) {
                LOG.info("Strategy found for unit {}: Type={}", unitId, strategy.getStrategyType());
                return strategy;
            } else {
                LOG.info("No active strategy found for unit {}", unitId);
                return null;
            }
        } catch (Exception e) {
            LOG.error("Error fetching strategy for unit {}: {}", unitId, e.getMessage());
            return null;
        }
    }

}



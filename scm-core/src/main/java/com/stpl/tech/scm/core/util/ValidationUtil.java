
package com.stpl.tech.scm.core.util;

import com.stpl.tech.scm.core.exception.ValidationException;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class ValidationUtil {

    private ValidationUtil() {}

    private static boolean validate(boolean condition, String message, boolean shouldThrow) {
        if (!condition) {
            message = checkStringIsNotEmpty(message) ? message : "Validation failed in ValidationUtil" ;
            log.error("Validation failed: {}", message);
            if (shouldThrow) {
                throw new ValidationException(message);
            }
        }
        return condition;
    }

    public static void requireNonNull(Object obj, String msg) {
        validate(obj != null, msg, true);
    }

    public static boolean checkNonNull(Object obj, String msg) {
        return validate(obj != null, msg, false);
    }

    public static boolean checkStringIsNotEmpty(String str) {
        return str != null && !str.trim().isEmpty();
    }

    public static void requireNonBlank(String str, String msg) {
        validate(checkStringIsNotEmpty(str), msg, true);
    }

    public static boolean checkNonBlank(String str, String msg) {
        return validate(checkStringIsNotEmpty(str), msg, false);
    }

    public static void requireNonEmptyCollection(Collection<?> collection, String msg) {
        validate(collection != null && !collection.isEmpty(), msg, true);
    }

    public static boolean checkNonEmptyCollection(Collection<?> collection, String msg) {
        return validate(collection != null && !collection.isEmpty(), msg, false);
    }

    public static boolean checkIsEmptyCollection(Collection<?> collection) {
        return (collection == null || collection.isEmpty());
    }

    public static void requireNonEmptyCollection(Map<?, ?> map, String msg) {
        validate(map != null && !map.isEmpty(), msg, true);
    }

    public static boolean checkNonEmptyCollection(Map<?, ?> map, String msg) {
        return validate(map != null && !map.isEmpty(), msg, false);
    }

    public static boolean checkIsEmptyCollection(Map<?, ?> map) {
        return (map == null || map.isEmpty());
    }

    public static void requirePositive(Number number, String msg) {
        validate(number != null && number.doubleValue() > 0, msg, true);
    }

    public static boolean checkPositive(Number number, String msg) {
        return validate(number != null && number.doubleValue() > 0, msg, false);
    }

    public static void requireEquals(Object a, Object b, String msg) {
        validate(Objects.equals(a, b), msg, true);
    }

    public static boolean checkEquals(Object a, Object b, String msg) {
        return validate(Objects.equals(a, b), msg, false);
    }

    public static void requireNotEquals(Object a, Object b, String msg) {
        validate(!Objects.equals(a, b), msg, true);
    }

    public static boolean checkNotEquals(Object a, Object b, String msg) {
        return validate(!Objects.equals(a, b), msg, false);
    }

    public static void requireTrue(boolean condition, String msg) {
        validate(condition, msg, true);
    }

    public static boolean checkTrue(boolean condition, String msg) {
        return validate(condition, msg, false);
    }


}

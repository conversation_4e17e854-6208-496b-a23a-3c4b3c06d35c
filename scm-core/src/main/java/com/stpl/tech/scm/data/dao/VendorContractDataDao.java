package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.data.model.VendorContractData;
import com.stpl.tech.scm.data.model.WorkOrderData;
import com.stpl.tech.scm.domain.model.VendorContractStatus;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface VendorContractDataDao extends SCMAbstractDao {

    VendorContractData getByVendorIdAndStatusIn(Integer vendorId, List<String> status);

    List<VendorContractData> getContractSByDateAndStatus(Date endDate, String status);

    List<WorkOrderData> getWorkOrdersByDateAndStatus(Date startDate, List<VendorContractStatus> statuses);

    VendorContractData changeStatusOfAContract(Integer vendorId, String contractIn, String setStatus);

    VendorContractData getByVendorIdAndStatusNotIn(Integer vendorId, List<String> status);

    WorkOrderData getRecentWorkOrderForVendor(Integer vendorId);

    WorkOrderData findWoByContractId(Integer contractId);

    VendorContractData findById(Integer contractId);

    List<WorkOrderData> getAllInStatusAndApprover(VendorContractStatus status, Integer approverId);

    WorkOrderData findWoById(Integer workOrderId);

    VendorContractData findPreviousVendorContractItemsFromEachWO(WorkOrderData woData, boolean excludeContractId);
}

package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import org.hibernate.annotations.Filter;

@Entity
@Table(name = "PRICE_UPDATE_ENTRY")
public class PriceUpdateEntryData {

	private Integer id;
	private PriceUpdateEventData eventId;
	private String keyType;
	private int keyId;
	private String keyName;
	private String unitOfMeasure;
	private BigDecimal unitPrice;
	private BigDecimal updatedUnitPrice;
	private BigDecimal editedUnitPrice;
	private BigDecimal approvedUnitPrice;
	private String entryStatus;
	private String hasError;
	private List<PriceUpdateEntryDrillDown> drillDowns = new ArrayList<PriceUpdateEntryDrillDown>();
	private List<PriceUpdateEntryError> errors = new ArrayList<PriceUpdateEntryError>();

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ENTRY_ID", nullable = false, unique = true)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "EVENT_ID", nullable = false)
	public PriceUpdateEventData getEventId() {
		return eventId;
	}

	public void setEventId(PriceUpdateEventData eventId) {
		this.eventId = eventId;
	}

	@Column(name = "KEY_TYPE", nullable = false)
	public String getKeyType() {
		return keyType;
	}

	public void setKeyType(String keyType) {
		this.keyType = keyType;
	}

	@Column(name = "KEY_ID", nullable = false)
	public int getKeyId() {
		return keyId;
	}

	public void setKeyId(int keyId) {
		this.keyId = keyId;
	}

	@Column(name = "KEY_NAME", nullable = false)
	public String getKeyName() {
		return keyName;
	}

	public void setKeyName(String keyName) {
		this.keyName = keyName;
	}

	@Column(name = "UOM", nullable = false)
	public String getUnitOfMeasure() {
		return unitOfMeasure;
	}

	public void setUnitOfMeasure(String unitOfMeasure) {
		this.unitOfMeasure = unitOfMeasure;
	}

	@Column(name = "UNIT_PRICE", nullable = false)
	public BigDecimal getUnitPrice() {
		return unitPrice;
	}

	public void setUnitPrice(BigDecimal unitPrice) {
		this.unitPrice = unitPrice;
	}

	@Column(name = "UPDATED_UNIT_PRICE", nullable = false)
	public BigDecimal getUpdatedUnitPrice() {
		return updatedUnitPrice;
	}

	public void setUpdatedUnitPrice(BigDecimal updatedUnitPrice) {
		this.updatedUnitPrice = updatedUnitPrice;
	}

	@Column(name = "EDITED_UNIT_PRICE", nullable = true)
	public BigDecimal getEditedUnitPrice() {
		return editedUnitPrice;
	}

	public void setEditedUnitPrice(BigDecimal editedUnitPrice) {
		this.editedUnitPrice = editedUnitPrice;
	}

	@Column(name = "APPROVED_UNIT_PRICE", nullable = true)
	public BigDecimal getApprovedUnitPrice() {
		return approvedUnitPrice;
	}

	public void setApprovedUnitPrice(BigDecimal approvedUnitPrice) {
		this.approvedUnitPrice = approvedUnitPrice;
	}

	@Column(name = "ENTRY_STATUS", nullable = false)
	public String getEntryStatus() {
		return entryStatus;
	}

	public void setEntryStatus(String entryStatus) {
		this.entryStatus = entryStatus;
	}

	@Column(name = "HAS_ERROR", nullable = true)
	public String getHasError() {
		return hasError;
	}

	public void setHasError(String errorCode) {
		this.hasError = errorCode;
	}

	/**
	 * @return the drillDowns
	 */
	@OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
	@JoinColumn(name = "ENTRY_ID", nullable = true)
	public List<PriceUpdateEntryDrillDown> getDrillDowns() {
		return drillDowns;
	}

	/**
	 * @param drillDowns the drillDowns to set
	 */
	@OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
	@JoinColumn(name = "ENTRY_ID", nullable = true)
	public void setDrillDowns(List<PriceUpdateEntryDrillDown> drillDowns) {
		this.drillDowns = drillDowns;
	}

	/**
	 * @return the errors
	 */
	@OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
	@JoinColumn(name = "ENTRY_ID", nullable = true)
	@Filter(name="entry")
	public List<PriceUpdateEntryError> getErrors() {
		return errors;
	}

	/**
	 * @param errors the errors to set
	 */
	public void setErrors(List<PriceUpdateEntryError> errors) {
		this.errors = errors;
	}

	
	
}

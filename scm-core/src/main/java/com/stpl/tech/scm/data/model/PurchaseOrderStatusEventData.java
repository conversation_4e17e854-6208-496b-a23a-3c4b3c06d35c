package com.stpl.tech.scm.data.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * Copyright (C) 2017,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 11-05-2017.
 */

@Entity
@Table(name = "PURCHASE_ORDER_STATUS_EVENT")
public class PurchaseOrderStatusEventData {

    private Integer statusEventId;
    private String fromStatus;
    private String toStatus;
    private Integer purchaseOrderId;
    private String transitionStatus;
    private Date updateTime;
    private Integer updatedBy;


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PURCHASE_ORDER_STATUS_EVENT_ID", nullable = false)
    public Integer getStatusEventId() {
        return statusEventId;
    }

    public void setStatusEventId(Integer statusEventId) {
        this.statusEventId = statusEventId;
    }

    @Column(name = "FROM_STATUS", nullable = false)
    public String getFromStatus() {
        return fromStatus;
    }

    public void setFromStatus(String fromStatus) {
        this.fromStatus = fromStatus;
    }

    @Column(name = "TO_STATUS", nullable = false)
    public String getToStatus() {
        return toStatus;
    }

    public void setToStatus(String toStatus) {
        this.toStatus = toStatus;
    }

    @Column(name = "PURCHASE_ORDER_ID", nullable = false)
    public Integer getPurchaseOrderId() {
        return purchaseOrderId;
    }

    public void setPurchaseOrderId(Integer purchaseOrderId) {
        this.purchaseOrderId = purchaseOrderId;
    }

    @Column(name = "TRANSITION_STATUS", nullable = false)
    public String getTransitionStatus() {
        return transitionStatus;
    }

    public void setTransitionStatus(String transitionStatus) {
        this.transitionStatus = transitionStatus;
    }

    @Column(name = "UPDATE_TIME", nullable = false)
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Column(name = "UPDATED_BY",nullable = false)
    public Integer getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }
}

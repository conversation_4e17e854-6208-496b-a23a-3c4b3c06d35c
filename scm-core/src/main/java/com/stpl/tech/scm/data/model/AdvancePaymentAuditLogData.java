package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "ADVANCE_PAYMENT_AUDIT_LOG")
public class AdvancePaymentAuditLogData {

    private Integer advancePaymentAuditLogId;
    private BigDecimal amount;
    private BigDecimal fromAmount;
    private BigDecimal toAmount;
    private Integer prId;
    private AdvancePaymentData advancePaymentId;
    private String status;
    private String loggedBy;
    private Date loggedAt;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ADVANCE_PAYMENT_AUDIT_LOG_ID", nullable = false, unique = true)
    public Integer getAdvancePaymentAuditLogId() {
        return advancePaymentAuditLogId;
    }

    public void setAdvancePaymentAuditLogId(Integer advancePaymentAuditLogId) {
        this.advancePaymentAuditLogId = advancePaymentAuditLogId;
    }

    @Column(name = "AMOUNT", nullable = false)
    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    @Column(name = "FROM_AMOUNT", nullable = false)
    public BigDecimal getFromAmount() {
        return fromAmount;
    }

    public void setFromAmount(BigDecimal fromAmount) {
        this.fromAmount = fromAmount;
    }

    @Column(name = "TO_AMOUNT", nullable = false)
    public BigDecimal getToAmount() {
        return toAmount;
    }

    public void setToAmount(BigDecimal toAmount) {
        this.toAmount = toAmount;
    }

    @Column(name = "PR_ID", nullable = false)
    public Integer getPrId() {
        return prId;
    }

    public void setPrId(Integer prId) {
        this.prId = prId;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ADVANCE_PAYMENT_ID", nullable = false)
    public AdvancePaymentData getAdvancePaymentId() {
        return advancePaymentId;
    }

    public void setAdvancePaymentId(AdvancePaymentData advancePaymentId) {
        this.advancePaymentId = advancePaymentId;
    }

    @Column(name = "STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "LOGGED_BY", nullable = false)
    public String getLoggedBy() {
        return loggedBy;
    }

    public void setLoggedBy(String loggedBy) {
        this.loggedBy = loggedBy;
    }

    @Column(name = "LOGGED_AT", nullable = false)
    public Date getLoggedAt() {
        return loggedAt;
    }

    public void setLoggedAt(Date loggedAt) {
        this.loggedAt = loggedAt;
    }
}

/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.notification.email;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.notification.email.template.VendorComplianceEmailNotificationTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.List;

public class VendorComplianceEmailNotification extends EmailNotification {

    private VendorComplianceEmailNotificationTemplate vendorComplianceEmailNotificationTemplate;
    private EnvType envType;
    private List<String> emails;

    public VendorComplianceEmailNotification(VendorComplianceEmailNotificationTemplate vendorComplianceEmailNotificationTemplate, EnvType envType, List<String> emails) {
        this.vendorComplianceEmailNotificationTemplate = vendorComplianceEmailNotificationTemplate;
        this.envType = envType;
        this.emails = emails;
    }

    @Override
    public String[] getToEmails() {
        String[] simpleArray = new String[emails.size()];
        return SCMUtil.isDev(envType) ? new String[]{"<EMAIL>"} : emails.toArray(simpleArray);
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        String subject = "Vendor Compliance Validation ON " + SCMUtil.getFormattedTime(SCMUtil.getCurrentDateIST(), "EEE dd MMM yyyy");
        if (SCMUtil.isDev(envType)) {
            subject = "Dev : " + subject;
        }
        return subject;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return vendorComplianceEmailNotificationTemplate.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
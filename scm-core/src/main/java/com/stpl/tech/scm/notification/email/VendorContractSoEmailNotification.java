package com.stpl.tech.scm.notification.email;

import com.stpl.tech.scm.core.util.SCMUtil;

import com.stpl.tech.scm.notification.email.template.VendorSOEmailNotificationTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
public class VendorContractSoEmailNotification extends EmailNotification {
    private VendorSOEmailNotificationTemplate vendorSOEmailNotificationTemplate;
    private EnvType envType;
    private String[] vendorMail;
    private String vendorName;


    @Override
    public String[] getToEmails() {
        if (SCMUtil.isDev(envType)) {
            return new String[] { "<EMAIL>" };
        } else {
            return this.vendorMail;
        }
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        String subjectOfEmail = String.format(this.vendorName+" Provisional Work Order Vendor Contract Proposal by STPL");
        if (SCMUtil.isDev(envType)) {
            subjectOfEmail = " [DEV] : " + subjectOfEmail;
        }
        return subjectOfEmail;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return vendorSOEmailNotificationTemplate.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}

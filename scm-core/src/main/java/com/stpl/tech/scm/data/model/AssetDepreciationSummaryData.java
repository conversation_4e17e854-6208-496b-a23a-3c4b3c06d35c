package com.stpl.tech.scm.data.model;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@SuppressWarnings("serial")
@Entity
@Table(name = "DEPRECIATION_SUMMARY")
public class AssetDepreciationSummaryData {

    private Integer assetDepreciationSummaryId;


    private Integer assetId;

    private String assetStatus;

    private Date startDate;

    private Date endDate;

    private BigDecimal currentValue;


    private String isWriteOff;
    private String writeOffType;
    private BigDecimal writeOffAmount;

    public AssetDepreciationSummaryData() {
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "DEPRECIATION_SUMMARY_ID", unique = true, nullable = false)
    public Integer getAssetDepreciationSummaryId() {
        return assetDepreciationSummaryId;
    }

    public void setAssetDepreciationSummaryId(Integer assetDepreciationSummaryId) {
        this.assetDepreciationSummaryId = assetDepreciationSummaryId;
    }

    @Column(name = "ASSET_STATUS", length = 19)
    public String getAssetStatus() {
        return assetStatus;
    }

    public void setAssetStatus(String assetStatus) {
        this.assetStatus = assetStatus;
    }

    @Column(name = "ASSET_ID", nullable = false)
    public Integer getAssetId() {
        return assetId;
    }

    public void setAssetId(Integer assetId) {
        this.assetId = assetId;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "START_DATE", length = 19)
    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "END_DATE", length = 19)
    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    @Column(name = "CURRENT_VALUE", length = 19)
    public BigDecimal getCurrentValue() {
        return currentValue;
    }

    public void setCurrentValue(BigDecimal currentValue) {
        this.currentValue = currentValue;
    }

    @Column(name = "IS_WRITE_OFF", length = 19)
    public String getIsWriteOff() {
        return isWriteOff;
    }

    public void setIsWriteOff(String isWriteOff) {
        this.isWriteOff = isWriteOff;
    }

    @Column(name = "WRITE_OFF_TYPE", length = 19)
    public String getWriteOffType() {
        return writeOffType;
    }

    public void setWriteOffType(String writeOffType) {
        this.writeOffType = writeOffType;
    }

    @Column(name = "WRITE_OFF_AMOUNT", length = 19)
    public BigDecimal getWriteOffAmount() {
        return writeOffAmount;
    }

    public void setWriteOffAmount(BigDecimal writeOffAmount) {
        this.writeOffAmount = writeOffAmount;
    }
}

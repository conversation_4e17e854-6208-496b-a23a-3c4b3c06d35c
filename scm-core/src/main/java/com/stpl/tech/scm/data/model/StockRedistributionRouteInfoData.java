package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;
import java.util.List;

@Entity
@Getter
@Setter
@Table(name = "STOCK_REDISTRIBUTION_ROUTE_INFO_DATA")
@AllArgsConstructor
@NoArgsConstructor
public class StockRedistributionRouteInfoData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "STOCK_REDISTRIBUTION_ROUTE_INFO_DATA_ID")
    private Integer stockRedistributionRouteInfoDataId;

    @Column(name = "ROUTE_NAME", nullable = false)
    private String routeName;

    @Column(name = "ROUTE_STATUS", nullable = false)
    private String routeStatus;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_TIME")
    private Date creationTime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_UPDATE_TIME")
    private Date lastUpdateTime;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "stockRedistributionRouteInfoData")
    private List<StockRedistributionRouteUnitsData> stockRedistributionRouteUnitsDataList;
}


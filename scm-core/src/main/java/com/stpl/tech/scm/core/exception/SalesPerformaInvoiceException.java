package com.stpl.tech.scm.core.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 07-07-2018.
 */
@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR, reason = "Invoice Request failure.")
public class SalesPerformaInvoiceException extends Exception {
    private static final long serialVersionUID = 616315625637405710L;

    private SCMError code;

    public SalesPerformaInvoiceException() {
    }

    public SalesPerformaInvoiceException(String message) {
        super(message);
        this.code = new SCMError("Invoice Request failure.", message, 501);
    }


    public SalesPerformaInvoiceException(SCMError code) {
        super(code.getErrorMsg());
        this.code = code;
    }

    public SCMError getCode() {
        return code;
    }

    public SalesPerformaInvoiceException(Throwable cause) {
        super(cause);
    }

    public SalesPerformaInvoiceException(String message, Throwable cause) {
        super(message, cause);
    }

    public SalesPerformaInvoiceException(String message, Throwable cause, boolean enableSuppression,
                                         boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
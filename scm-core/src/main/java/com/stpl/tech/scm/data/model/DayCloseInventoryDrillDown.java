package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 12-06-2017.
 */
@Entity
@Table(name = "INVENTORY_DRILLDOWN")
public class DayCloseInventoryDrillDown {

    private Integer inventoryId;
    private Integer productId;
    private Integer skuId;
    private String unitOfMeasure;
    private BigDecimal skuPrice;
    private BigDecimal averagePrice;
    private BigDecimal openingStock;
    private BigDecimal transferred;
    private BigDecimal received;
    @Transient
    private BigDecimal receivedWithInitiatedGr;
    private BigDecimal booked;
    private BigDecimal reverseBooked;
    private BigDecimal wasted;
    private BigDecimal expected;
    private BigDecimal actual;
    private BigDecimal variance;
    private BigDecimal varianceCost;
    private BigDecimal consumed;
    private BigDecimal reverseConsumed;
    private SCMDayCloseEventData closureEvent;
    private String varianceReason;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "INVENTORY_ID", nullable = false, unique = true)
    public Integer getInventoryId() {
        return inventoryId;
    }

    public void setInventoryId(Integer inventoryId) {
        this.inventoryId = inventoryId;
    }

    @Column(name = "PRODUCT_ID")
    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    @Column(name = "SKU_ID")
    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    @Column(name = "UOM")
    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }

    @Column(name = "PRICE")
    public BigDecimal getSkuPrice() {
        return skuPrice;
    }

    public void setSkuPrice(BigDecimal skuPrice) {
        this.skuPrice = skuPrice;
    }

    @Column(name = "AVERAGE_PRICE")
    public BigDecimal getAveragePrice() {
        return averagePrice;
    }

    public void setAveragePrice(BigDecimal averagePrice) {
        this.averagePrice = averagePrice;
    }

    @Column(name = "OPENING")
    public BigDecimal getOpeningStock() {
        return openingStock;
    }

    public void setOpeningStock(BigDecimal openingStock) {
        this.openingStock = openingStock;
    }

    @Column(name = "TRANSFERRED")
    public BigDecimal getTransferred() {
        return transferred;
    }

    public void setTransferred(BigDecimal transferred) {
        this.transferred = transferred;
    }

    @Column(name = "RECEIVED")
    public BigDecimal getReceived() {
        return received;
    }

    public void setReceived(BigDecimal received) {
        this.received = received;
    }

    @Column(name = "BOOKED")
    public BigDecimal getBooked() {
        return booked;
    }

    public void setBooked(BigDecimal booked) {
        this.booked = booked;
    }

    @Column(name = "REVERSE_BOOKED")
    public BigDecimal getReverseBooked() {
        return reverseBooked;
    }

    public void setReverseBooked(BigDecimal reverseBooked) {
        this.reverseBooked = reverseBooked;
    }

    @Column(name = "WASTED")
    public BigDecimal getWasted() {
        return wasted;
    }

    public void setWasted(BigDecimal wasted) {
        this.wasted = wasted;
    }

    @Column(name = "EXPECTED_CLOSING")
    public BigDecimal getExpected() {
        return expected;
    }

    public void setExpected(BigDecimal expected) {
        this.expected = expected;
    }

    @Column(name = "ACTUAL_CLOSING")
    public BigDecimal getActual() {
        return actual;
    }

    public void setActual(BigDecimal actual) {
        this.actual = actual;
    }

    @Column(name = "VARIANCE")
    public BigDecimal getVariance() {
        return variance;
    }

    public void setVariance(BigDecimal variance) {
        this.variance = variance;
    }

    @Column(name = "VARIANCE_COST")
    public BigDecimal getVarianceCost() {
        return varianceCost;
    }

    public void setVarianceCost(BigDecimal varianceCost) {
        this.varianceCost = varianceCost;
    }

    @Column(name = "CONSUMPTION")
    public BigDecimal getConsumed() {
        return consumed;
    }

    public void setConsumed(BigDecimal consumed) {
        this.consumed = consumed;
    }

    @Column(name = "REVERSE_CONSUMPTION")
    public BigDecimal getReverseConsumed() {
        return reverseConsumed;
    }

    public void setReverseConsumed(BigDecimal reverseConsumed) {
        this.reverseConsumed = reverseConsumed;
    }


    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CLOSURE_EVENT_ID")
    public SCMDayCloseEventData getClosureEvent() {
        return closureEvent;
    }

    public void setClosureEvent(SCMDayCloseEventData closureEvent) {
        this.closureEvent = closureEvent;
    }

    @Column(name = "VARIANCE_REASON")
    public String getVarianceReason() {
        return varianceReason;
    }

    public void setVarianceReason(String varianceReason) {
        this.varianceReason = varianceReason;
    }

    @Transient
    public BigDecimal getReceivedWithInitiatedGr() {
        return receivedWithInitiatedGr;
    }

    public void setReceivedWithInitiatedGr(BigDecimal receivedWithInitiatedGr) {
        this.receivedWithInitiatedGr = receivedWithInitiatedGr;
    }
}

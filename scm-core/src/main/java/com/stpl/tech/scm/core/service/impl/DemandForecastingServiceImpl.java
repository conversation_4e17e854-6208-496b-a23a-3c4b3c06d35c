/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.mapper.DemandForecastingMapper;
import com.stpl.tech.scm.core.service.DemandForecastingService;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.util.DemandForecastingUtil;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.dao.DemandForecastingDao;
import com.stpl.tech.scm.data.model.DayWiseSlotWiseSalesData;
import com.stpl.tech.scm.data.model.HolidaysListData;
import com.stpl.tech.scm.data.model.UnitWiseOrderingStrategyData;
import com.stpl.tech.scm.domain.model.DaySlotEnum;
import com.stpl.tech.scm.domain.model.DayType;
import com.stpl.tech.scm.domain.model.DayWiseSlotWiseSalesDataDto;
import com.stpl.tech.scm.domain.model.DemandForecastResult;
import com.stpl.tech.scm.domain.model.DemandForecastingRequestDto;
import com.stpl.tech.scm.domain.model.DemandForecastingStrategyMetadataDto;
import com.stpl.tech.scm.domain.model.HolidayTypeEnum;
import com.stpl.tech.scm.domain.model.MeanResult;
import com.stpl.tech.scm.domain.model.ProductDemandForecast;
import com.stpl.tech.scm.domain.model.ProductSaleClusterEnum;
import com.stpl.tech.scm.domain.model.SafetyStockForecastResult;
import com.stpl.tech.scm.domain.model.UnitMappedMenuMappedProductDto;
import com.stpl.tech.scm.domain.model.UnitWiseOrderingStrategyDataDto;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.EnvType;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Log4j2
public class DemandForecastingServiceImpl implements DemandForecastingService {

    @Autowired
    private DemandForecastingDao demandForecastingDao;

    @Autowired
    private DemandForecastingMapper demandForecastingMapper;

    @Autowired
    private EnvProperties envProperties;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public UnitWiseOrderingStrategyDataDto getUnitWiseOrderingStrategyData(Integer unitId) {
        UnitWiseOrderingStrategyData entity = demandForecastingDao.getUnitWiseOrderingStrategyDataByUnitId(unitId);
        
        if (Objects.nonNull(entity)) {
            return demandForecastingMapper.toUnitWiseOrderingStrategyDataDto(entity);
        }
        
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<String, ProductDemandForecast> getDemandForecastForDates(DemandForecastingRequestDto request) throws SumoException {
        // Validate request
        if (request == null) {
            log.error("Request cannot be null");
            throw new SumoException("Request cannot be null");
        }
        
        Integer unitId = request.getUnitId();
        Integer brandId = request.getBrandId();
        
        if (unitId == null || brandId == null) {
            log.error("Unit ID and Brand ID cannot be null");
            throw new SumoException("Unit ID and Brand ID cannot be null");
        }
        
        log.info("Starting demand forecasting for unit: {}, brand: {}", unitId, brandId);
        
        // Get unit-wise ordering strategy data
        UnitWiseOrderingStrategyDataDto unitStrategyData = getUnitWiseOrderingStrategyData(unitId);
        
        if (Objects.nonNull(unitStrategyData) && AppConstants.ACTIVE.equals(unitStrategyData.getStatus())) {
            log.info("Unit strategy is active, proceeding with forecasting for unit: {}", unitId);
            
            // Get strategy metadata
            DemandForecastingStrategyMetadataDto strategy = unitStrategyData.getDemandForecastingStrategyMetadata();
            if (strategy == null) {
                log.error("Strategy metadata not found for unit: {}", unitId);
                throw new SumoException("Strategy metadata not found for unit: " + unitId);
            }
            
            // Get historical start date from request ordering dates (first date - 1)
            List<Date> orderingDates = request.getOrderingDates();
            if (orderingDates == null || orderingDates.isEmpty()) {
                throw new SumoException("Ordering dates cannot be null or empty");
            }
            
            // Sort ordering dates and get the first (lowest) date
            List<Date> sortedOrderingDates = orderingDates.stream()
                    .filter(Objects::nonNull)
                    .sorted().toList();
            
            if (sortedOrderingDates.isEmpty()) {
                throw new SumoException("No valid ordering dates found in request");
            }
            
            Date firstOrderingDate = SCMUtil.getCurrentBusinessDate();
            Date historicalStartDate = SCMUtil.addDays(firstOrderingDate, -1);
            Date historicalEndDate = SCMUtil.addDays(historicalStartDate, -(Objects.nonNull(strategy.getMaxHistoricalDaysLookup()) ? strategy.getMaxHistoricalDaysLookup() : DemandForecastingUtil.DEFAULT_HISTORICAL_DAYS)); // Get last 60 days data
            
            log.info("Fetching historical data from {} to {}", historicalEndDate, historicalStartDate);
            
            // Get historical sales data
            List<DayWiseSlotWiseSalesData> historicalData = demandForecastingDao.getHistoricalSalesData(
                unitId, brandId, historicalEndDate, historicalStartDate);
            
            if (historicalData == null || historicalData.isEmpty()) {
                log.error("No historical data found for unit: {}, brand: {}", unitId, brandId);
                throw new SumoException("No historical data found for unit: " + unitId + ", brand: " + brandId);
            }
            
            log.info("Retrieved {} historical data records", historicalData.size());

            // Get holiday data for the historical period
            List<HolidaysListData> holidayData = demandForecastingDao.getHolidayListOfType(
                historicalEndDate, historicalStartDate, HolidayTypeEnum.getAllHolidayTypeStrings());
            
            log.info("Retrieved {} holiday records for the period", holidayData.size());
            
            // Group holidays by date with holiday type enum as value
            Map<Date, HolidayTypeEnum> holidayMap = groupHolidaysByDate(holidayData);
            log.info("Grouped holidays into {} date entries", holidayMap.size());
            
            // Calculate slot-wise mean
            Map<DayType, Map<String, Map<DaySlotEnum, MeanResult>>> slotWiseMeans = 
                calculateSlotWiseMean(historicalData, strategy, unitStrategyData, holidayMap);
            
            log.info("Slot-wise mean calculation completed for unit: {}", unitId);
            
            // Create business date wise sales map
            Map<Date, List<DayWiseSlotWiseSalesData>> businessDateWiseSalesMap = historicalData.stream()
                    .collect(Collectors.groupingBy(DayWiseSlotWiseSalesData::getBusinessDate));
            
            // Get last N weeks sales data using the historical data
            Map<Date, Map<String, List<DayWiseSlotWiseSalesDataDto>>> lastNWeeksSalesData = 
                getLastNWeeksSalesData(unitId, brandId, strategy, businessDateWiseSalesMap, request, holidayMap);
            
            log.info("Last N weeks sales data retrieved for {} weeks", lastNWeeksSalesData.size());
            
            // Calculate adjusted sales with stockout corrections (modifies original data in place)
            calculateAdjustedSale(slotWiseMeans, lastNWeeksSalesData, strategy);
            
            log.info("Adjusted sales calculation completed");
            
            // Calculate final prediction
            Map<Date, Map<String, DemandForecastResult>> finalPrediction = 
                calculateFinalPrediction(lastNWeeksSalesData, strategy);
            
            log.info("Final prediction calculation completed for {} weeks", finalPrediction.size());
            
            // Calculate safety stock for each product
            Map<String, SafetyStockForecastResult> safetyStockResults = 
                calculateSafetyStock(finalPrediction, strategy);
            
            log.info("Safety stock calculation completed for {} products", safetyStockResults.size());
            
            // Set final predictions combining safety stock and demand forecast
            Map<String, ProductDemandForecast> finalPredictions = setFinalPredictions(safetyStockResults, finalPrediction);

            log.info("Final predictions prepared for {} products", finalPredictions.size());

            // Filter to only include unit and menu mapped products
            return filterUnitMappedMenuMappedProducts(finalPredictions, unitId, envProperties.getEnvType());
        } else {
            log.error("Unit strategy not found or not active for unit: {}", unitId);
            throw new SumoException("Unit strategy not found or not active for unit: " + unitId);
        }
    }

    /**
     * Calculate slot-wise mean for demand forecasting by analyzing historical sales data
     * Filters out holidays and non-operational data, then calculates averages for each time slot
     * (breakfast, lunch, evening, dinner, post-dinner, overnight) separately for weekdays and weekends
     */
    private Map<DayType, Map<String, Map<DaySlotEnum, MeanResult>>> calculateSlotWiseMean(
            List<DayWiseSlotWiseSalesData> salesData, 
            DemandForecastingStrategyMetadataDto strategy,
            UnitWiseOrderingStrategyDataDto unitStrategy,
            Map<Date, HolidayTypeEnum> holidayMap) {
        
        log.info("Starting slot-wise mean calculation for strategy: {}", 
                Objects.nonNull(strategy.getStrategyName()) ? strategy.getStrategyName() : "Unknown");
        
        if (salesData == null || salesData.isEmpty()) {
            log.error("Sales data is null or empty");
            return new HashMap<>();
        }

        // Sort data by business date in descending order (filter out null dates and holiday dates)
        List<DayWiseSlotWiseSalesData> sortedData = salesData.stream()
                .filter(data -> Objects.nonNull(data.getBusinessDate()))
                .filter(data -> !holidayMap.containsKey(data.getBusinessDate()))
                .sorted((a, b) -> b.getBusinessDate().compareTo(a.getBusinessDate())).toList();

        log.info("Total sales data records after filtering holidays: {}", sortedData.size());
        log.info("Holiday dates filtered out: {}", salesData.size() - sortedData.size());

        // Filter operational data up to historical days limit
        List<DayWiseSlotWiseSalesData> operationalData = new ArrayList<>();
        int historicalDays = strategy.getHistoricalDays();
        
        // Validate historical days
        if (historicalDays <= 0) {
            log.error("Invalid historical days: {}", historicalDays);
            return new HashMap<>();
        }
        
        int count = 0;
        Set<Date> traversedDates = new HashSet<>();
        
        for (DayWiseSlotWiseSalesData data : sortedData) {
            if (count >= historicalDays) break;
            
            // Check if operational
            if (DemandForecastingUtil.isOperationalData(data)) {
                operationalData.add(data);
            }

            if (traversedDates.add(data.getBusinessDate())) {
                count++;
            }
        }

        log.info("Operational data records: {}", operationalData.size());
        
        // Check if we have operational data
        if (operationalData.isEmpty()) {
            log.error("No operational data found after filtering");
            return new HashMap<>();
        }

        // Separate weekday and weekend data
        List<DayWiseSlotWiseSalesData> weekdayData = operationalData.stream()
                .filter(data -> Objects.nonNull(data.getBusinessDate()) && !DemandForecastingUtil.isWeekend(data.getBusinessDate()))
                .collect(Collectors.toList());

        List<DayWiseSlotWiseSalesData> weekendData = operationalData.stream()
                .filter(data -> Objects.nonNull(data.getBusinessDate()) && DemandForecastingUtil.isWeekend(data.getBusinessDate()))
                .collect(Collectors.toList());

        log.info("Weekday data records: {}, Weekend data records: {}", weekdayData.size(), weekendData.size());

        // Calculate slot-wise averages
        Map<DayType, Map<String, Map<DaySlotEnum, MeanResult>>> result = new HashMap<>();
        
        // Process weekday data
        Map<String, Map<DaySlotEnum, MeanResult>> weekdayAverages = calculateSlotAverages(weekdayData, DayType.WEEKDAY, strategy);
        if (!weekdayAverages.isEmpty()) {
            result.put(DayType.WEEKDAY, weekdayAverages);
        }

        // Process weekend data
        Map<String, Map<DaySlotEnum, MeanResult>> weekendAverages = calculateSlotAverages(weekendData, DayType.WEEKEND, strategy);
        if (!weekendAverages.isEmpty()) {
            result.put(DayType.WEEKEND, weekendAverages);
        }

        log.info("Slot-wise mean calculation completed");
        return result;
    }

    /**
     * Calculate slot averages for given data by grouping sales data by product and time slot
     * Extracts sales quantities for each slot, filters out stockout periods, and calculates mean values
     * Returns mean results for each product and slot combination
     */
    private Map<String, Map<DaySlotEnum, MeanResult>> calculateSlotAverages(
            List<DayWiseSlotWiseSalesData> data, DayType dayType, DemandForecastingStrategyMetadataDto strategy) {
        
        Map<String, Map<DaySlotEnum, MeanResult>> result = new HashMap<>();
        
        // Group data by product (productId + dimension)
        Map<String, List<DayWiseSlotWiseSalesData>> productGroups = data.stream()
                .collect(Collectors.groupingBy(salesData -> 
                    DemandForecastingUtil.generateProductKey(salesData.getProductId(), salesData.getDimension())));
        
        // Process each product group
        for (Map.Entry<String, List<DayWiseSlotWiseSalesData>> productEntry : productGroups.entrySet()) {
            String productKey = productEntry.getKey();
            List<DayWiseSlotWiseSalesData> productData = productEntry.getValue();
            
            Map<DaySlotEnum, MeanResult> productAverages = new HashMap<>();
            
            // Get stockout threshold from strategy
            Integer stockOutThreshold = strategy.getStockOutThresholdInMinutes() != null ? 
                strategy.getStockOutThresholdInMinutes() : DemandForecastingUtil.DEFAULT_STOCK_OUT_THRESHOLD_IN_MINUTES;
            
            // Extract all slot data at once
            Map<DaySlotEnum, List<Double>> allSlotData = new HashMap<>();
            for (DaySlotEnum slot : DaySlotEnum.values()) {
                List<Double> slotData = extractSlotData(productData, slot, stockOutThreshold);
                if (!slotData.isEmpty()) {
                    allSlotData.put(slot, slotData);
                }
            }
            
            // Calculate mean results for all slots at once
            for (Map.Entry<DaySlotEnum, List<Double>> slotEntry : allSlotData.entrySet()) {
                DaySlotEnum slot = slotEntry.getKey();
                List<Double> slotData = slotEntry.getValue();
                
                // Log data points before calculation
                log.info("Processing {} slot for product {} ({}): data points = {}", 
                         slot.getSlotName(), productKey, dayType, slotData);
                
                MeanResult meanResult = DemandForecastingUtil.forecastDemand(slotData);
                productAverages.put(slot, meanResult);
                
                // Log detailed results including when mean is 0
                if (meanResult.getMean() == 0.0) {
                    log.error("Mean calculated as 0 for {} slot, product {} ({}): data points = {}, method = {}", 
                             slot.getSlotName(), productKey, dayType, slotData, meanResult.getMethod());
                } else {
                    log.info("Calculated {} average for {} ({}): mean={}, method={}, data points = {}", 
                             slot.getSlotName(), productKey, dayType, meanResult.getMean(), 
                             meanResult.getMethod(), slotData);
                }
            }
            
            if (!productAverages.isEmpty()) {
                result.put(productKey, productAverages);
            }
        }
        
        return result;
    }

    /**
     * Extract slot data with minimal stockout (stockout <= threshold minutes)
     * Filters out sales data where stockout time exceeds the threshold to ensure data quality
     * Returns list of sales quantities for the specified time slot
     */
    private List<Double> extractSlotData(List<DayWiseSlotWiseSalesData> data, DaySlotEnum slot, Integer stockOutThreshold) {
        List<Double> slotData = new ArrayList<>();
        
        for (DayWiseSlotWiseSalesData record : data) {
            if (isSlotDataValid(record, slot, stockOutThreshold)) {
                double sales = getSlotSales(record, slot);
                slotData.add(sales);
            }
        }
        
        return slotData;
    }

    /**
     * Check if slot data is valid by verifying stockout minutes are within acceptable threshold
     * Returns true if stockout time is minimal (<= threshold), false otherwise
     */
    private boolean isSlotDataValid(DayWiseSlotWiseSalesData record, DaySlotEnum slot, Integer stockOutThreshold) {
        return switch (slot) {
            case DAY_SLOT_BREAKFAST -> Objects.nonNull(record.getBreakfastStockOutMinutes()) && record.getBreakfastStockOutMinutes() <= stockOutThreshold;
            case DAY_SLOT_LUNCH -> Objects.nonNull(record.getLunchStockOutMinutes()) && record.getLunchStockOutMinutes() <= stockOutThreshold;
            case DAY_SLOT_EVENING -> Objects.nonNull(record.getEveningStockOutMinutes()) && record.getEveningStockOutMinutes() <= stockOutThreshold;
            case DAY_SLOT_DINNER -> Objects.nonNull(record.getDinnerStockOutMinutes()) && record.getDinnerStockOutMinutes() <= stockOutThreshold;
            case DAY_SLOT_POST_DINNER -> Objects.nonNull(record.getPostDinnerStockOutMinutes()) && record.getPostDinnerStockOutMinutes() <= stockOutThreshold;
            case DAY_SLOT_OVERNIGHT -> Objects.nonNull(record.getOvernightStockOutMinutes()) && record.getOvernightStockOutMinutes() <= stockOutThreshold;
        };
    }

    /**
     * Get slot sales value for a specific time slot from sales data record
     * Returns the sales quantity for the specified slot, or 0.0 if null
     */
    private double getSlotSales(DayWiseSlotWiseSalesData record, DaySlotEnum slot) {
        return switch (slot) {
            case DAY_SLOT_BREAKFAST -> Objects.nonNull(record.getBreakfastSales()) ? record.getBreakfastSales().doubleValue() : DemandForecastingUtil.DEFAULT_SALES_VALUE;
            case DAY_SLOT_LUNCH -> Objects.nonNull(record.getLunchSales()) ? record.getLunchSales().doubleValue() : DemandForecastingUtil.DEFAULT_SALES_VALUE;
            case DAY_SLOT_EVENING -> Objects.nonNull(record.getEveningSales()) ? record.getEveningSales().doubleValue() : DemandForecastingUtil.DEFAULT_SALES_VALUE;
            case DAY_SLOT_DINNER -> Objects.nonNull(record.getDinnerSales()) ? record.getDinnerSales().doubleValue() : DemandForecastingUtil.DEFAULT_SALES_VALUE;
            case DAY_SLOT_POST_DINNER -> Objects.nonNull(record.getPostDinnerSales()) ? record.getPostDinnerSales().doubleValue() : DemandForecastingUtil.DEFAULT_SALES_VALUE;
            case DAY_SLOT_OVERNIGHT -> Objects.nonNull(record.getOvernightSales()) ? record.getOvernightSales().doubleValue() : DemandForecastingUtil.DEFAULT_SALES_VALUE;
        };
    }

    /**
     * Get last N weeks sales data grouped by date and product from historical data
     * Excludes holidays and groups sales data by week start date and product key
     * Returns map of week start dates to product-wise sales data lists
     */
    private Map<Date, Map<String, List<DayWiseSlotWiseSalesDataDto>>> getLastNWeeksSalesData(
            Integer unitId, Integer brandId, DemandForecastingStrategyMetadataDto strategy,
            Map<Date, List<DayWiseSlotWiseSalesData>> businessDateWiseSalesMap, DemandForecastingRequestDto request,
            Map<Date, HolidayTypeEnum> holidayMap) {
        
        log.info("Getting last N weeks sales data for unit: {}, brand: {}", unitId, brandId);
        
        if (businessDateWiseSalesMap == null || businessDateWiseSalesMap.isEmpty()) {
            log.error("No business date wise sales data provided");
            return new HashMap<>();
        }
        
        // Extract unique dates and sort them
        List<Date> availableDates = request.getOrderingDates();
        
        log.info("Found {} unique dates in historical data", availableDates.size());
        
        // Get last N weeks dates using SCMUtil, excluding holidays
        Map<Date, List<Date>> lastNWeekDatesMap = SCMUtil.getLastNWeekDatesExcludingHolidays(availableDates,
                strategy.getNoOfPastWeeks() + strategy.getSafetyWeeks(), holidayMap).entrySet().stream()
                .collect(Collectors.toMap(
                    Map.Entry::getKey, 
                    e -> e.getValue().subList(0, Math.min(strategy.getNoOfPastWeeks(), e.getValue().size()))
                ));
        
        log.info("Generated {} week groups for analysis (excluding holidays)", lastNWeekDatesMap.size());
        log.info("Holiday map contains {} holiday dates that will be excluded", holidayMap != null ? holidayMap.size() : 0);
        
        // Group sales data by date and product
        Map<Date, Map<String, List<DayWiseSlotWiseSalesDataDto>>> result = new HashMap<>();
        
        for (Map.Entry<Date, List<Date>> weekEntry : lastNWeekDatesMap.entrySet()) {
            Date weekStartDate = weekEntry.getKey();
            List<Date> weekDates = weekEntry.getValue();
            
            Map<String, List<DayWiseSlotWiseSalesDataDto>> weekSalesData = new HashMap<>();
            
            for (Date date : weekDates) {
                Date businessDate = date;
                
                // Get sales data for this specific date from the map
                List<DayWiseSlotWiseSalesData> dateSalesData = businessDateWiseSalesMap.get(businessDate);
                
                if (Objects.nonNull(dateSalesData)) {
                    // Group by product (productId + dimension)
                    for (DayWiseSlotWiseSalesData salesData : dateSalesData) {
                        String productKey = DemandForecastingUtil.generateProductKey(
                            salesData.getProductId(), salesData.getDimension());
                        
                        // Convert to DTO
                        DayWiseSlotWiseSalesDataDto salesDataDto = demandForecastingMapper.toDayWiseSlotWiseSalesDataDto(salesData);
                        
                        // Add to product group
                        weekSalesData.computeIfAbsent(productKey, k -> new ArrayList<>()).add(salesDataDto);
                    }
                }
            }
            
            if (!weekSalesData.isEmpty()) {
                result.put(weekStartDate, weekSalesData);
            }
        }
        
        log.info("Generated sales data for {} weeks", result.size());
        return result;
    }

    /**
     * Calculate adjusted sales with stockout corrections by estimating lost sales due to stockouts
     * Uses slot-wise mean values to calculate potential lost sales and adds them to original sales
     * Updates the sales data in place with adjusted quantities for each time slot
     */
    private void calculateAdjustedSale(
            Map<DayType, Map<String, Map<DaySlotEnum, MeanResult>>> slotWiseMeans,
            Map<Date, Map<String, List<DayWiseSlotWiseSalesDataDto>>> lastNWeeksSalesData, DemandForecastingStrategyMetadataDto strategy) {
        
        log.info("Starting adjusted sales calculation");
        
        for (Map.Entry<Date, Map<String, List<DayWiseSlotWiseSalesDataDto>>> weekEntry : lastNWeeksSalesData.entrySet()) {
            Map<String, List<DayWiseSlotWiseSalesDataDto>> weekSalesData = weekEntry.getValue();
            
            for (Map.Entry<String, List<DayWiseSlotWiseSalesDataDto>> productEntry : weekSalesData.entrySet()) {
                List<DayWiseSlotWiseSalesDataDto> productSalesData = productEntry.getValue();
                
                for (DayWiseSlotWiseSalesDataDto salesData : productSalesData) {
                    // Determine day type (weekday/weekend) for slot-wise means
                    DayType dayType = DemandForecastingUtil.isWeekend(salesData.getBusinessDate()) ? DayType.WEEKEND : DayType.WEEKDAY;
                    Map<String, Map<DaySlotEnum, MeanResult>> daySlotMeans = slotWiseMeans.get(dayType);
                    
                    if (Objects.nonNull(daySlotMeans)) {
                        // Get product key for this sales data
                        String salesProductKey = DemandForecastingUtil.generateProductKey(salesData.getProductId(), salesData.getDimension());
                        Map<DaySlotEnum, MeanResult> productSlotMeans = daySlotMeans.get(salesProductKey);
                        
                        if (Objects.nonNull(productSlotMeans)) {
                            // Set day slot means for UI display
                            salesData.setDaySlotMeans(productSlotMeans);
                            
                            // Get stockout threshold from strategy
                            Integer stockOutThreshold = strategy.getStockOutThresholdInMinutes() != null ? 
                                strategy.getStockOutThresholdInMinutes() : DemandForecastingUtil.DEFAULT_STOCK_OUT_THRESHOLD_IN_MINUTES;
                            
                            // Process each slot directly on original data
                            for (DaySlotEnum slot : DaySlotEnum.values()) {
                                processSlotAdjustment(salesData, slot, productSlotMeans, stockOutThreshold);
                            }
                        
                            // Calculate total adjusted quantity sold
                            calculateTotalAdjustedQuantitySold(salesData);
                        }
                    }
                }
            }
        }
        
        log.info("Adjusted sales calculation completed");
    }


    /**
     * Process slot adjustment for a specific slot by calculating lost sales due to stockouts
     * Compares stockout minutes with slot time to determine lost sales ratio
     * Sets adjusted sales as original sales plus estimated lost sales
     */
    private void processSlotAdjustment(DayWiseSlotWiseSalesDataDto salesData, DaySlotEnum slot, 
                                     Map<DaySlotEnum, MeanResult> daySlotMeans, Integer stockOutThreshold) {
        
        MeanResult slotMean = daySlotMeans.get(slot);
        if (slotMean == null) {
            return;
        }
        
        // Get slot-specific data
        Integer slotStockOutMinutes = getSlotStockOutMinutes(salesData, slot);
        Integer slotTimeInMinutes = getSlotTimeInMinutes(salesData, slot);
        Integer originalSlotSales = getSlotSales(salesData, slot);
        
        if (Objects.nonNull(slotStockOutMinutes) && Objects.nonNull(slotTimeInMinutes) && slotStockOutMinutes > stockOutThreshold) {
            // Calculate lost sales due to stockout
            double lostSales = calculateLostSaleDueToStockOut(slotMean.getMean(), slotTimeInMinutes, slotStockOutMinutes);
            
            // Calculate adjusted sales (original + lost sales)
            int adjustedSales = originalSlotSales + (int) Math.round(lostSales);
            
            // Set adjusted sales for the specific slot
            setAdjustedSlotSales(salesData, slot, adjustedSales);
            
            log.info("Slot {}: Original={}, Lost={}, Adjusted={}", 
                     slot.getSlotName(), originalSlotSales, lostSales, adjustedSales);
        } else {
            // No adjustment needed, use original sales
            setAdjustedSlotSales(salesData, slot, originalSlotSales);
        }
    }

    /**
     * Calculate lost sales due to stockout using mean sales and stockout ratio
     * Formula: lostSales = meanSales * (stockoutMinutes / slotTimeMinutes)
     * Returns estimated lost sales quantity
     */
    private double calculateLostSaleDueToStockOut(double meanSales, int slotTimeInMinutes, int stockOutMinutes) {
        if (slotTimeInMinutes <= 0) {
            return 0.0;
        }
        
        double stockOutRatio = (double) stockOutMinutes / slotTimeInMinutes;
        return meanSales * stockOutRatio;
    }

    /**
     * Get slot stockout minutes for a specific time slot from sales data
     * Returns the stockout duration in minutes for the specified slot
     */
    private Integer getSlotStockOutMinutes(DayWiseSlotWiseSalesDataDto salesData, DaySlotEnum slot) {
        return switch (slot) {
            case DAY_SLOT_BREAKFAST -> salesData.getBreakfastStockOutMinutes();
            case DAY_SLOT_LUNCH -> salesData.getLunchStockOutMinutes();
            case DAY_SLOT_EVENING -> salesData.getEveningStockOutMinutes();
            case DAY_SLOT_DINNER -> salesData.getDinnerStockOutMinutes();
            case DAY_SLOT_POST_DINNER -> salesData.getPostDinnerStockOutMinutes();
            case DAY_SLOT_OVERNIGHT -> salesData.getOvernightStockOutMinutes();
        };
    }

    /**
     * Get slot time in minutes for a specific time slot from sales data
     * Returns the total duration of the time slot in minutes
     */
    private Integer getSlotTimeInMinutes(DayWiseSlotWiseSalesDataDto salesData, DaySlotEnum slot) {
        return switch (slot) {
            case DAY_SLOT_BREAKFAST -> salesData.getBreakfastTimeInMinutes();
            case DAY_SLOT_LUNCH -> salesData.getLunchTimeInMinutes();
            case DAY_SLOT_EVENING -> salesData.getEveningTimeInMinutes();
            case DAY_SLOT_DINNER -> salesData.getDinnerTimeInMinutes();
            case DAY_SLOT_POST_DINNER -> salesData.getPostDinnerTimeInMinutes();
            case DAY_SLOT_OVERNIGHT -> salesData.getOvernightTimeInMinutes();
        };
    }

    /**
     * Get slot sales quantity for a specific time slot from sales data DTO
     * Returns the sales quantity for the specified slot, or null if not available
     */
    private Integer getSlotSales(DayWiseSlotWiseSalesDataDto salesData, DaySlotEnum slot) {
        return switch (slot) {
            case DAY_SLOT_BREAKFAST -> salesData.getBreakfastSales();
            case DAY_SLOT_LUNCH -> salesData.getLunchSales();
            case DAY_SLOT_EVENING -> salesData.getEveningSales();
            case DAY_SLOT_DINNER -> salesData.getDinnerSales();
            case DAY_SLOT_POST_DINNER -> salesData.getPostDinnerSales();
            case DAY_SLOT_OVERNIGHT -> salesData.getOvernightSales();
        };
    }

    /**
     * Set adjusted slot sales for a specific time slot in sales data DTO
     * Updates the adjusted sales quantity for the specified slot after stockout correction
     */
    private void setAdjustedSlotSales(DayWiseSlotWiseSalesDataDto salesData, DaySlotEnum slot, Integer adjustedSales) {
        switch (slot) {
            case DAY_SLOT_BREAKFAST -> salesData.setAdjustedBreakfastSales(adjustedSales);
            case DAY_SLOT_LUNCH -> salesData.setAdjustedLunchSales(adjustedSales);
            case DAY_SLOT_EVENING -> salesData.setAdjustedEveningSales(adjustedSales);
            case DAY_SLOT_DINNER -> salesData.setAdjustedDinnerSales(adjustedSales);
            case DAY_SLOT_POST_DINNER -> salesData.setAdjustedPostDinnerSales(adjustedSales);
            case DAY_SLOT_OVERNIGHT -> salesData.setAdjustedOvernightSales(adjustedSales);
        }
    }

    /**
     * Calculate total adjusted quantity sold by summing all adjusted slot sales
     * Adds up all time slot sales (breakfast, lunch, evening, dinner, post-dinner, overnight)
     * Sets the total adjusted quantity sold in the sales data DTO
     */
    private void calculateTotalAdjustedQuantitySold(DayWiseSlotWiseSalesDataDto salesData) {
        int totalAdjustedSales = 0;
        
        if (Objects.nonNull(salesData.getAdjustedBreakfastSales())) {
            totalAdjustedSales += salesData.getAdjustedBreakfastSales();
        }
        if (Objects.nonNull(salesData.getAdjustedLunchSales())) {
            totalAdjustedSales += salesData.getAdjustedLunchSales();
        }
        if (Objects.nonNull(salesData.getAdjustedEveningSales())) {
            totalAdjustedSales += salesData.getAdjustedEveningSales();
        }
        if (Objects.nonNull(salesData.getAdjustedDinnerSales())) {
            totalAdjustedSales += salesData.getAdjustedDinnerSales();
        }
        if (Objects.nonNull(salesData.getAdjustedPostDinnerSales())) {
            totalAdjustedSales += salesData.getAdjustedPostDinnerSales();
        }
        if (Objects.nonNull(salesData.getAdjustedOvernightSales())) {
            totalAdjustedSales += salesData.getAdjustedOvernightSales();
        }
        
        salesData.setAdjustedTotalQuantitySold(BigDecimal.valueOf(totalAdjustedSales));
    }

    /**
     * Calculate final prediction for each product across all weeks using adjusted sales data
     * Processes each week's sales data to generate demand forecast results for each product
     * Applies product clustering to categorize products based on predicted quantities
     */
    private Map<Date, Map<String, DemandForecastResult>> calculateFinalPrediction(
            Map<Date, Map<String, List<DayWiseSlotWiseSalesDataDto>>> lastNWeeksSalesData,
            DemandForecastingStrategyMetadataDto strategy) {
        
        log.info("Starting final prediction calculation");
        
        Map<Date, Map<String, DemandForecastResult>> finalPrediction = new HashMap<>();
        
        for (Map.Entry<Date, Map<String, List<DayWiseSlotWiseSalesDataDto>>> weekEntry : lastNWeeksSalesData.entrySet()) {
            Date weekDate = weekEntry.getKey();
            Map<String, List<DayWiseSlotWiseSalesDataDto>> weekSalesData = weekEntry.getValue();
            
            Map<String, DemandForecastResult> weekPrediction = new HashMap<>();
            
            for (Map.Entry<String, List<DayWiseSlotWiseSalesDataDto>> productEntry : weekSalesData.entrySet()) {
                String productKey = productEntry.getKey();
                List<DayWiseSlotWiseSalesDataDto> productSalesData = productEntry.getValue();
                
                // Calculate predicted quantity for this product
                DemandForecastResult predictionResult = calculateProductPrediction(productKey, productSalesData);
                
                if (Objects.nonNull(predictionResult)) {
                    weekPrediction.put(productKey, predictionResult);
                }
            }
            
            if (!weekPrediction.isEmpty()) {
                // Apply clustering to categorize products
                applyProductClustering(weekPrediction, strategy);
                finalPrediction.put(weekDate, weekPrediction);
            }
        }
        
        log.info("Final prediction calculation completed for {} weeks", finalPrediction.size());
        return finalPrediction;
    }

    /**
     * Calculate prediction for a specific product using adjusted sales quantities
     * Extracts adjusted total quantity sold values and calculates mean demand using forecasting utility
     * Creates demand forecast result with product information and predicted quantity
     */
    private DemandForecastResult calculateProductPrediction(String productKey, List<DayWiseSlotWiseSalesDataDto> productSalesData) {
        
        if (productSalesData == null || productSalesData.isEmpty()) {
            log.error("No sales data available for product: {}", productKey);
            return null;
        }
        
        // Extract adjusted total quantity sold values
        List<Double> adjustedQuantities = productSalesData.stream()
                .filter(salesData -> Objects.nonNull(salesData.getAdjustedTotalQuantitySold()))
                .map(salesData -> salesData.getAdjustedTotalQuantitySold().doubleValue())
                .collect(Collectors.toList());
        
        if (adjustedQuantities.isEmpty()) {
            log.error("No valid adjusted quantities found for product: {}", productKey);
            return null;
        }
        
        // Calculate mean result using utility method
        MeanResult meanResult = DemandForecastingUtil.forecastDemand(adjustedQuantities);
        
        // Create prediction result
        DemandForecastResult result = new DemandForecastResult();
        result.setPredictedQuantity(meanResult.getMean());
        result.setMeanResult(meanResult);
        result.setSalesDataList(productSalesData);
        
        // Set product information from the first sales data record
        if (!productSalesData.isEmpty()) {
            DayWiseSlotWiseSalesDataDto firstSalesData = productSalesData.get(0);
            result.setProductId(firstSalesData.getProductId());
            result.setDimension(firstSalesData.getDimension());
            result.setProductName(firstSalesData.getProductName());
            result.setBusinessDate(firstSalesData.getBusinessDate());
            result.setProductDimensionKey(productKey);
        }
        
        log.info("Product {} prediction: quantity={}, method={}, productId={}, dimension={}", 
                 productKey, meanResult.getMean(), meanResult.getMethod(), 
                 result.getProductId(), result.getDimension());
        
        return result;
    }

    /**
     * Apply product clustering based on predicted quantities using quantile logic
     * Categorizes products into HIGH, MEDIUM, LOW clusters based on quantile thresholds
     * Sets editability flags based on cluster type and strategy configuration
     */
    private void applyProductClustering(Map<String, DemandForecastResult> weekPrediction, 
                                      DemandForecastingStrategyMetadataDto strategy) {
        
        if (weekPrediction.isEmpty()) {
            return;
        }
        
        // Extract predicted quantities for clustering
        List<Double> predictedQuantities = weekPrediction.values().stream()
                .map(DemandForecastResult::getPredictedQuantity)
                .filter(Objects::nonNull).toList();
        
        if (predictedQuantities.size() < 3) {
            // Not enough data for meaningful clustering
            log.error("Insufficient data for clustering: {} products", predictedQuantities.size());
            return;
        }
        
        // Sort quantities for quantile calculation
        List<Double> sortedQuantities = predictedQuantities.stream()
                .sorted().toList();
        
        int totalProducts = sortedQuantities.size();
        
        // Calculate quantile thresholds
        double[] thresholds = calculateQuantileThresholds(sortedQuantities, strategy);
        double highThreshold = thresholds[0];
        double lowThreshold = thresholds[1];
        
        log.info("Clustering thresholds - High: {}, Low: {}, Total products: {}", 
                 highThreshold, lowThreshold, totalProducts);
        
        // Apply clustering to each product
        for (DemandForecastResult result : weekPrediction.values()) {
            Double predictedQuantity = result.getPredictedQuantity();
            
            if (Objects.nonNull(predictedQuantity)) {
                ProductSaleClusterEnum cluster = determineProductCluster(predictedQuantity, highThreshold, lowThreshold);
                result.setProductSaleCluster(cluster);
                
                // Set editable flag based on cluster and strategy
                Boolean isEditable = determineDemandForecastEditability(cluster, strategy);
                result.setIsEditable(isEditable);
                
                log.info("Product {} clustered as {} (quantity: {}), isEditable: {}", 
                         result.getProductDimensionKey(), cluster, predictedQuantity, isEditable);
            }
        }
    }

    /**
     * Determine product cluster based on predicted quantity and thresholds
     * Compares predicted quantity against high and low thresholds to assign cluster
     * Returns HIGH if >= high threshold, LOW if <= low threshold, MEDIUM otherwise
     */
    private ProductSaleClusterEnum determineProductCluster(Double predictedQuantity, double highThreshold, double lowThreshold) {
        
        if (predictedQuantity >= highThreshold) {
            return ProductSaleClusterEnum.HIGH;
        } else if (predictedQuantity <= lowThreshold) {
            return ProductSaleClusterEnum.LOW;
        } else {
            return ProductSaleClusterEnum.MEDIUM;
        }
    }

    /**
     * Calculate safety stock for each product using cycle-based matrix analysis
     * Creates n×M matrices for each product where n=dates and M=max sales data points per day
     * Groups sales data by day-of-week and calculates column-wise sums for safety stock calculation
     * Applies clustering and calculates final safety stock quantities
     */
    private Map<String, SafetyStockForecastResult> calculateSafetyStock(
            Map<Date, Map<String, DemandForecastResult>> finalPrediction,
            DemandForecastingStrategyMetadataDto strategy) {
        
        log.info("Starting safety stock calculation using cycle-based grouping");
        
        Map<String, SafetyStockForecastResult> safetyStockResults = new HashMap<>();
        
        // Group demand forecast results by product and calculate max column count
        Map<String, List<DemandForecastResult>> productGroups = new HashMap<>();
        Map<String, Integer> maxCountPerProduct = new HashMap<>();
        
        for (Map.Entry<Date, Map<String, DemandForecastResult>> weekEntry : finalPrediction.entrySet()) {
            Map<String, DemandForecastResult> weekResults = weekEntry.getValue();
            Date businessDate = weekEntry.getKey();
            
            Calendar cal = Calendar.getInstance();
            cal.setTime(businessDate);
            int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);
            
            for (Map.Entry<String, DemandForecastResult> productEntry : weekResults.entrySet()) {
                String productKey = productEntry.getKey();
                DemandForecastResult demandResult = productEntry.getValue();
                
                productGroups.computeIfAbsent(productKey, k -> new ArrayList<>()).add(demandResult);
                
                // Track max count for this product
                int currentCount = demandResult.getSalesDataList().size();
                maxCountPerProduct.merge(productKey, currentCount, Integer::max);
            }
        }
        
        // Create matrices and calculate column sums
        Map<String, List<Double>> productColumnSums = new HashMap<>();
        List<Date> distinctDates = new ArrayList<>(finalPrediction.keySet());
        distinctDates.sort(Date::compareTo);
        
        for (Map.Entry<String, Integer> productEntry : maxCountPerProduct.entrySet()) {
            String productKey = productEntry.getKey();
            int maxCount = productEntry.getValue();
            
            double[][] matrix = new double[distinctDates.size()][maxCount];
            List<Double> columnSums = new ArrayList<>();
            
            // Fill matrix and calculate column sums
            for (int dateIndex = 0; dateIndex < distinctDates.size(); dateIndex++) {
                Date currentDate = distinctDates.get(dateIndex);
                Map<String, DemandForecastResult> dateResults = finalPrediction.get(currentDate);
                
                if (dateResults != null && dateResults.containsKey(productKey)) {
                    DemandForecastResult result = dateResults.get(productKey);
                    
                    Calendar currentCal = Calendar.getInstance();
                    currentCal.setTime(currentDate);
                    int currentDayOfWeek = currentCal.get(Calendar.DAY_OF_WEEK);
                    
                    if (Objects.nonNull(result.getSalesDataList())) {
                        int columnIndex = 0;
                        
                        for (DayWiseSlotWiseSalesDataDto salesData : result.getSalesDataList()) {
                            if (Objects.nonNull(salesData.getBusinessDate()) && columnIndex < maxCount) {
                                Calendar salesCal = Calendar.getInstance();
                                salesCal.setTime(salesData.getBusinessDate());
                                int salesDayOfWeek = salesCal.get(Calendar.DAY_OF_WEEK);
                                
                                if (salesDayOfWeek == currentDayOfWeek) {
                                    double quantity = salesData.getAdjustedTotalQuantitySold() != null ? 
                                        salesData.getAdjustedTotalQuantitySold().doubleValue() :
                                        (salesData.getTotalQuantitySold() != null ? salesData.getTotalQuantitySold().doubleValue() : DemandForecastingUtil.DEFAULT_SALES_VALUE);
                                    
                                    matrix[dateIndex][columnIndex] = quantity;
                                    columnIndex++;
                                }
                            }
                        }
                    }
                }
            }
            
            // Print matrix in readable format
            log.info("Matrix for product {} ({}x{}):", productKey, distinctDates.size(), maxCount);
            for (int i = 0; i < distinctDates.size(); i++) {
                StringBuilder row = new StringBuilder();
                for (int j = 0; j < maxCount; j++) {
                    row.append(String.format("%8.2f ", matrix[i][j]));
                }
                log.info("  Row {} (Date {}): [{}]", i, distinctDates.get(i), row.toString().trim());
            }
            
            // Calculate column sums
            for (int j = 0; j < maxCount; j++) {
                double columnSum = 0.0;
                for (int i = 0; i < distinctDates.size(); i++) {
                    columnSum += matrix[i][j];
                }
                columnSums.add(columnSum);
            }
            
            log.info("Column sums for product {}: {}", productKey, columnSums);
            productColumnSums.put(productKey, columnSums);
        }
        
        // Calculate safety stock for each product
        for (Map.Entry<String, List<DemandForecastResult>> productEntry : productGroups.entrySet()) {
            String productKey = productEntry.getKey();
            List<DemandForecastResult> demandResults = productEntry.getValue();
            List<Double> columnSums = productColumnSums.get(productKey);
            
            if (columnSums != null && !columnSums.isEmpty()) {
                SafetyStockForecastResult safetyStockResult = calculateProductSafetyStockFromColumnSums(
                    productKey, demandResults, columnSums, strategy);
                
                if (Objects.nonNull(safetyStockResult)) {
                    safetyStockResults.put(productKey, safetyStockResult);
                }
            }
        }
        
        log.info("Safety stock calculation completed for {} products", safetyStockResults.size());
        
        applySafetyStockClustering(safetyStockResults, strategy);
        calculateFinalSafetyStockQuantities(safetyStockResults, strategy);
        
        return safetyStockResults;
    }

    /**
     * Calculate safety stock for a specific product using column sums from matrix
     * Uses column-wise sums from the matrix to calculate mean demand and standard deviation
     * Creates safety stock forecast result with statistical measures for further processing
     */
    private SafetyStockForecastResult calculateProductSafetyStockFromColumnSums(
            String productKey, 
            List<DemandForecastResult> demandResults,
            List<Double> columnSums,
            DemandForecastingStrategyMetadataDto strategy) {
        
        if (demandResults == null || demandResults.isEmpty()) {
            log.error("No demand forecast results available for product: {}", productKey);
            return null;
        }
        
        if (columnSums == null || columnSums.isEmpty()) {
            log.error("No column sums available for product: {}", productKey);
            return null;
        }
        
        log.info("Processing safety stock for product {} using {} column sums: {}", 
                 productKey, columnSums.size(), columnSums);
        
        // Calculate mean and standard deviation using column sums
        MeanResult meanResult = DemandForecastingUtil.forecastDemand(columnSums);
        double meanDemand = meanResult.getMean();
        double standardDeviation = meanResult.getStandardDeviation();
        
        // Log detailed results including when mean is 0
        if (meanDemand == 0.0) {
            log.error("Safety stock mean calculated as 0 for product {}: column sums = {}, method = {}", 
                     productKey, columnSums, meanResult.getMethod());
        } else {
            log.info("Safety stock calculation for product {}: mean={}, stdDev={}, method={}, column sums = {}", 
                     productKey, meanDemand, standardDeviation, meanResult.getMethod(), columnSums);
        }
        
        // Create safety stock result
        SafetyStockForecastResult result = new SafetyStockForecastResult();
        result.setProductId(demandResults.get(0).getProductId());
        result.setDimension(demandResults.get(0).getDimension());
        result.setProductName(demandResults.get(0).getProductName());
        result.setProductDimensionKey(productKey);
        result.setDemandForecastResults(demandResults);
        result.setMeanDemand(meanDemand);
        result.setStandardDeviation(standardDeviation);
        result.setMeanResult(meanResult);
        result.setSafetyStockMultiplier(1.0); // Will be updated after clustering
        
        log.info("Product {} column-sum-based grouping: mean={}, stdDev={}", 
                 productKey, meanDemand, standardDeviation);
        
        return result;
    }


    /**
     * Apply K-means clustering to categorize products for safety stock using quantile logic
     * Categorizes products into HIGH, MEDIUM, LOW clusters based on mean demand thresholds
     * Uses quantile-based thresholds to determine cluster boundaries
     */
    private void applySafetyStockClustering(Map<String, SafetyStockForecastResult> safetyStockResults, 
                                          DemandForecastingStrategyMetadataDto strategy) {
        
        if (safetyStockResults.isEmpty()) {
            return;
        }
        
        // Extract mean demand values for clustering
        List<Double> meanDemands = safetyStockResults.values().stream()
                .map(SafetyStockForecastResult::getMeanDemand)
                .filter(Objects::nonNull).toList();
        
        if (meanDemands.size() < 3) {
            log.error("Insufficient data for safety stock clustering: {} products", meanDemands.size());
            return;
        }
        
        // Sort mean demands for quantile calculation
        List<Double> sortedMeanDemands = meanDemands.stream()
                .sorted().toList();
        
        int totalProducts = sortedMeanDemands.size();
        
        // Calculate quantile thresholds
        double[] thresholds = calculateQuantileThresholds(sortedMeanDemands, strategy);
        double highThreshold = thresholds[0];
        double lowThreshold = thresholds[1];
        
        log.info("Safety stock clustering thresholds - High: {}, Low: {}, Total products: {}", 
                 highThreshold, lowThreshold, totalProducts);
        
        // Apply clustering to each product
        for (SafetyStockForecastResult result : safetyStockResults.values()) {
            Double meanDemand = result.getMeanDemand();
            
            if (Objects.nonNull(meanDemand)) {
                ProductSaleClusterEnum cluster = determineSafetyStockCluster(meanDemand, highThreshold, lowThreshold);
                result.setProductSaleCluster(cluster);
                
                log.info("Product {} safety stock clustered as {} (mean demand: {})", 
                         result.getProductDimensionKey(), cluster, meanDemand);
            }
        }
    }

    /**
     * Calculate final safety stock quantities based on clustering results
     * Applies cluster-specific safety multipliers to standard deviation
     * Sets editability flags and final safety stock quantities for each product
     */
    private void calculateFinalSafetyStockQuantities(Map<String, SafetyStockForecastResult> safetyStockResults, 
                                                   DemandForecastingStrategyMetadataDto strategy) {
        
        for (SafetyStockForecastResult result : safetyStockResults.values()) {
            if (Objects.nonNull(result.getProductSaleCluster()) && Objects.nonNull(result.getStandardDeviation())) {
                
                // Get safety value based on cluster
                double safetyValue = getSafetyValueForCluster(result.getProductSaleCluster(), strategy);
                
                // Calculate final safety stock quantity
                double finalSafetyStockQuantity = safetyValue * result.getStandardDeviation();
                
                // Set the calculated values
                result.setPredictedQuantity(finalSafetyStockQuantity);
                result.setSafetyStockQuantity(finalSafetyStockQuantity);
                result.setSafetyStockMultiplier(safetyValue);
                
                // Set editable flag based on cluster and strategy
                Boolean isEditable = determineSafetyStockEditability(result.getProductSaleCluster(), strategy);
                result.setIsEditable(isEditable);
                
                log.info("Product {} final safety stock: quantity={}, safetyValue={}, stdDev={}, isEditable={}", 
                         result.getProductDimensionKey(), finalSafetyStockQuantity, safetyValue, result.getStandardDeviation(), isEditable);
            }
        }
    }

    /**
     * Determine safety stock cluster based on mean demand and thresholds
     * Compares mean demand against high and low thresholds to assign cluster
     * Returns HIGH if >= high threshold, LOW if <= low threshold, MEDIUM otherwise
     */
    private ProductSaleClusterEnum determineSafetyStockCluster(Double meanDemand, double highThreshold, double lowThreshold) {
        
        if (meanDemand >= highThreshold) {
            return ProductSaleClusterEnum.HIGH;
        } else if (meanDemand <= lowThreshold) {
            return ProductSaleClusterEnum.LOW;
        } else {
            return ProductSaleClusterEnum.MEDIUM;
        }
    }

    /**
     * Get safety value for cluster from strategy configuration
     * Returns cluster-specific safety multiplier values (HIGH=1.5, MEDIUM=1.2, LOW=1.0 by default)
     * Uses strategy configuration or default values if not specified
     */
    private double getSafetyValueForCluster(ProductSaleClusterEnum cluster, DemandForecastingStrategyMetadataDto strategy) {
        
        return switch (cluster) {
            case HIGH -> strategy.getHighProductSafetyValue() != null ? 
                strategy.getHighProductSafetyValue().doubleValue() : 1.5;
            case MEDIUM -> strategy.getMediumProductSafetyValue() != null ? 
                strategy.getMediumProductSafetyValue().doubleValue() : 1.2;
            case LOW -> strategy.getLowProductSafetyValue() != null ? 
                strategy.getLowProductSafetyValue().doubleValue() : 1.0;
        };
    }

    /**
     * Determine if safety stock is editable based on cluster and strategy configuration
     * Checks strategy flags for each cluster type to determine editability
     * Returns true if editing is enabled for the specific cluster type
     */
    private Boolean determineSafetyStockEditability(ProductSaleClusterEnum cluster, DemandForecastingStrategyMetadataDto strategy) {
        
        if (cluster == null) {
            return false;
        }
        
        return switch (cluster) {
            case HIGH -> SCMServiceConstants.SCM_CONSTANT_YES.equals(strategy.getEnableHighSafetyStockEdit());
            case MEDIUM -> SCMServiceConstants.SCM_CONSTANT_YES.equals(strategy.getEnableMediumSafetyStockEdit());
            case LOW -> SCMServiceConstants.SCM_CONSTANT_YES.equals(strategy.getEnableLowSafetyStockEdit());
        };
    }

    /**
     * Determine if demand forecast is editable based on cluster and strategy configuration
     * Checks strategy flags for each cluster type to determine editability for day-wise sales
     * Returns true if editing is enabled for the specific cluster type
     */
    private Boolean determineDemandForecastEditability(ProductSaleClusterEnum cluster, DemandForecastingStrategyMetadataDto strategy) {
        
        if (cluster == null) {
            return false;
        }
        
        return switch (cluster) {
            case HIGH -> SCMServiceConstants.SCM_CONSTANT_YES.equals(strategy.getEnableHighDayWiseSaleEdit());
            case MEDIUM -> SCMServiceConstants.SCM_CONSTANT_YES.equals(strategy.getEnableMediumDayWiseSaleEdit());
            case LOW -> SCMServiceConstants.SCM_CONSTANT_YES.equals(strategy.getEnableLowDayWiseSaleEdit());
        };
    }

    /**
     * Set final predictions combining safety stock and demand forecast results
     * Creates ProductDemandForecast objects with safety stock and demand forecast data
     * Rounds quantities to whole numbers and organizes results by product key
     */
    private Map<String, ProductDemandForecast> setFinalPredictions(
            Map<String, SafetyStockForecastResult> safetyStockResults,
            Map<Date, Map<String, DemandForecastResult>> finalPrediction) {
        
        log.info("Setting final predictions for {} safety stock results", safetyStockResults.size());
        
        Map<String, ProductDemandForecast> finalPredictions = new HashMap<>();
        
        // Process each safety stock result
        for (Map.Entry<String, SafetyStockForecastResult> safetyEntry : safetyStockResults.entrySet()) {
            String productKey = safetyEntry.getKey();
            SafetyStockForecastResult safetyStockResult = safetyEntry.getValue();
            
            // Extract demand forecast results for this product across all dates
            Map<Date, DemandForecastResult> demandForecastByDate = new HashMap<>();
            
            for (Map.Entry<Date, Map<String, DemandForecastResult>> weekEntry : finalPrediction.entrySet()) {
                Date date = weekEntry.getKey();
                Map<String, DemandForecastResult> weekResults = weekEntry.getValue();
                
                // Find demand forecast result for this product on this date
                DemandForecastResult demandResult = weekResults.get(productKey);
                if (Objects.nonNull(demandResult)) {
                    demandForecastByDate.put(date, demandResult);
                }
            }
            
            // Round safety stock quantity
            if (Objects.nonNull(safetyStockResult.getSafetyStockQuantity())) {
                double roundedSafetyStock = Math.round(safetyStockResult.getSafetyStockQuantity());
                safetyStockResult.setSafetyStockQuantity(roundedSafetyStock);
            }
            
            // Round predicted quantities in demand forecast results
            for (DemandForecastResult demandResult : demandForecastByDate.values()) {
                if (Objects.nonNull(demandResult.getPredictedQuantity())) {
                    double roundedPredictedQuantity = Math.round(demandResult.getPredictedQuantity());
                    demandResult.setPredictedQuantity(roundedPredictedQuantity);
                }
            }
            
            // Create ProductDemandForecast
            ProductDemandForecast productDemandForecast = new ProductDemandForecast();
            productDemandForecast.setSafetyStockForecastResult(safetyStockResult);
            productDemandForecast.setDemandForecastByDate(demandForecastByDate);
            
            finalPredictions.put(productKey, productDemandForecast);
            
            log.info("Product {} final prediction: safety stock quantity={}, demand forecast dates={}", 
                     productKey, safetyStockResult.getSafetyStockQuantity(), demandForecastByDate.size());
        }
        
        log.info("Final predictions completed for {} products", finalPredictions.size());
        return finalPredictions;
    }

    /**
     * Calculate quantile thresholds for clustering based on strategy configuration
     * Determines high and low threshold indices using quantile values from strategy
     * Returns threshold values for clustering products into HIGH, MEDIUM, LOW categories
     * @param sortedValues List of sorted values to calculate thresholds from
     * @param strategy Strategy configuration containing quantile settings
     * @return Array containing [highThreshold, lowThreshold] values
     */
    private double[] calculateQuantileThresholds(List<Double> sortedValues, DemandForecastingStrategyMetadataDto strategy) {
        int totalProducts = sortedValues.size();
        
        // Calculate quantile thresholds
        BigDecimal highQuantile = Objects.nonNull(strategy.getHighQuantile()) ? 
            strategy.getHighQuantile() : DemandForecastingUtil.DEFAULT_HIGH_QUANTILE;
        BigDecimal lowQuantile = Objects.nonNull(strategy.getLowQuantile()) ? 
            strategy.getLowQuantile() : DemandForecastingUtil.DEFAULT_LOW_QUANTILE;
        
        int highThresholdIndex = (int) Math.ceil(totalProducts * highQuantile.doubleValue()) - 1;
        int lowThresholdIndex = (int) Math.floor(totalProducts * lowQuantile.doubleValue());
        
        // Ensure valid indices
        highThresholdIndex = Math.max(0, Math.min(highThresholdIndex, totalProducts - 1));
        lowThresholdIndex = Math.max(0, Math.min(lowThresholdIndex, totalProducts - 1));
        
        double highThreshold = sortedValues.get(highThresholdIndex);
        double lowThreshold = sortedValues.get(lowThresholdIndex);
        
        return new double[]{highThreshold, lowThreshold};
    }

    /**
     * Groups holidays by date with holiday type enum as value
     * Filters out null dates and types, then creates a map of holiday dates to their types
     * @param holidayData List of holiday data from database
     * @return Map of Date to HolidayTypeEnum for easy lookup during data processing
     */
    private Map<Date, HolidayTypeEnum> groupHolidaysByDate(List<HolidaysListData> holidayData) {
        if (holidayData == null || holidayData.isEmpty()) {
            log.error("No holiday data provided for grouping");
            return new HashMap<>();
        }
        
        return holidayData.stream()
                .filter(holiday -> holiday.getHolidayDate() != null && holiday.getHolidayType() != null)
                .collect(Collectors.toMap(
                        HolidaysListData::getHolidayDate,
                    holiday -> HolidayTypeEnum.valueOf(holiday.getHolidayType()),
                    (existing, replacement) -> existing
                ));
    }

    /**
     * Filter final predictions to only include unit and menu mapped products
     * Gets the list of mapped products from DAO and removes products that are not mapped
     * Logs removed products for tracking and returns filtered predictions
     */
    private Map<String, ProductDemandForecast> filterUnitMappedMenuMappedProducts(
            Map<String, ProductDemandForecast> finalPredictions, Integer unitId, EnvType envType) {
        
        log.info("Starting filtering for unit {} mapped products", unitId);
        
        // Get unit and menu mapped products from DAO
        List<UnitMappedMenuMappedProductDto> mappedProducts = demandForecastingDao.getUnitMappedMenuMappedProducts(unitId, envType);
        
        // Create map of product keys to DTOs for efficient lookup
        Map<String, UnitMappedMenuMappedProductDto> mappedProductMap = mappedProducts.stream()
                .collect(Collectors.toMap(
                    dto -> DemandForecastingUtil.generateProductKey(dto.getProductId(), dto.getDimension()),
                    dto -> dto,
                    (existing, replacement) -> existing
                ));
        
        log.info("Found {} unit and menu mapped products for unit {}", mappedProductMap.size(), unitId);
        
        // Filter final predictions to only include mapped products
        Map<String, ProductDemandForecast> filteredPredictions = new HashMap<>();
        List<String> removedProducts = new ArrayList<>();
        
        for (Map.Entry<String, ProductDemandForecast> entry : finalPredictions.entrySet()) {
            String productKey = entry.getKey();
            ProductDemandForecast prediction = entry.getValue();
            
            if (mappedProductMap.containsKey(productKey)) {
                // Product is mapped, include in filtered results
                filteredPredictions.put(productKey, prediction);
            } else {
                // Product is not mapped, log for removal
                removedProducts.add(productKey);
                log.error("For unit {} - product {} is not unit and menu mapped, removing from predictions",
                    unitId, productKey);
            }
        }
        
        log.info("Filtering completed: {} products retained, {} products removed for unit {}", 
            filteredPredictions.size(), removedProducts.size(), unitId);
        
        return filteredPredictions;
    }

}


package com.stpl.tech.scm.notification.email;

import com.stpl.tech.master.core.external.acl.service.Notification;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.VendorContractStatus;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.EmailNotification;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class UserContractEmailNotification extends EmailNotification implements Notification {
    private static final Logger LOG = LoggerFactory.getLogger(UserContractEmailNotification.class);

    private String subject;
    private VendorContractStatus vendorStatus;
    private Integer workOrderId;
    private String vendor;
    private boolean forApprover;
    private String  approver;
    private String user;
    private String mailId;

    private EnvType envType;

    public UserContractEmailNotification(String subject, VendorContractStatus vendorStatus, Integer workOrderId, String vendor, boolean forApprover, String toName, String mailId, EnvType envType) {
        this.subject = subject;
        this.vendorStatus = vendorStatus;
        this.workOrderId = workOrderId;
        this.vendor = vendor;
        this.forApprover = forApprover;
        if(forApprover){
            approver = toName;
        } else {
            user = toName;
        }
        this.mailId = mailId;
        this.envType = envType;
    }

    @Override
    public String getNotificationMessage() {
        return null;
    }

    @Override
    public String[] getToEmails() {
        return (SCMUtil.isDev(getEnvironmentType()) ? new String[] {SCMServiceConstants.TECHNOLOGY_EMAIL} : new String[] { SCMServiceConstants.TECHNOLOGY_EMAIL, mailId});
    }

    @Override
    public String getFromEmail() {
        return SCMServiceConstants.REPORTING_EMAIL;
    }

    @Override
    public String subject() {
        return (EnvType.STAGE.equals(getEnvironmentType()) ? "[DEV] " : "") + subject;
    }

    @Override
    public String body(){
        StringBuilder body  = new StringBuilder("<html><b>" + subject + "</b><br/>");
        body.append("Price request id : <b>" + workOrderId + "</b>.<br/>" +
                "Vendor : <b>" + vendor + "</b>.<br/>");
        if(VendorContractStatus.CREATED.equals(vendorStatus)) {
            if(forApprover) {
                body.append("You have updated the SKU's for vendor :  <b>" + vendor + "</b>. Details has been sent for verification to <b>" + approver +"</b>.");
            }
            else {
                body.append("You have received one price update request from <b>" + user + "</b>.");
            }
        } else if(VendorContractStatus.PENDING_VENDOR_APPROVAL.equals(vendorStatus)) {
            body.append("SKU's has been APPROVED by <b>" + approver + "</b>. The contract is in Pending Vendor Approval state");
        } else if(VendorContractStatus.PENDING_APPROVER_APPROVAL.equals(vendorStatus)) {
            body.append("SKU's has been APPROVED by <b>" + approver + "</b>.<br/> The Contract has been BY_PASSED. <br/> The contract is in Pending Approver Approval state");
        } else if(VendorContractStatus.PARTIALLY_REJECTED.equals(vendorStatus)) {
            body.append("Few SKU's has been REJECTED by <b>" + approver + "</b>.");
        } else if(VendorContractStatus.VENDOR_REJECTED.equals(vendorStatus)) {
            body.append("Vendor Price has been REJECTED by <b>" + vendor + "</b>.");
        } else if(VendorContractStatus.VENDOR_APPROVED.equals(vendorStatus)) {
            body.append("Vendor Price has been APPROVED by <b>" + vendor + "</b>.");
        } else if(VendorContractStatus.APPROVER_REJECTED.equals(vendorStatus)) {
            body.append("Vendor Price has been REJECTED by <b>" + approver + "</b>.");
        } else if(VendorContractStatus.APPROVER_BY_PASSED.equals(vendorStatus)) {
            body.append("Vendor Price has been APPROVED and byPassed by <b>" + approver + "</b>.");
        } else if(VendorContractStatus.APPLIED.equals(vendorStatus)) {
            body.append("Vendor Price has been APPLIED.");
        } else if(VendorContractStatus.EXPIRED.equals(vendorStatus)) {
            body.append("Vendor Price has been EXPIRED.");
        } else if(VendorContractStatus.DEACTIVATED.equals(vendorStatus)) {
            body.append("Vendor Price has been DEACTIVATED.");
        } else if(VendorContractStatus.CANCELLED.equals(vendorStatus)) {
            body.append("Vendor Price has been CANCELLED").append(approver != null ? ", by <b>" + approver + "</b>." : ".");
        } else {
            LOG.warn("Unknown vendor status : {}", vendorStatus);
        }
        return body.toString();
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }

}

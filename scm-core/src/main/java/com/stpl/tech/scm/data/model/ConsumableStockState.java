package com.stpl.tech.scm.data.model;

import javax.persistence.*;
import java.math.BigDecimal;

@Entity
@Table(name = "CONSUMABLE_STOCK_STATE")
public class ConsumableStockState {
    @Id
    @Column(name = "CONSUMABLE_STOCK_STATE_ID")
    private Integer consumableStockStateId;

    @Column(name = "UNIT_ID")
    private Integer unitId;

    @Column(name = "PRODUCT_ID")
    private Integer productId;

    @Column(name = "SKU_ID")
    private Integer skuId;

    @Column(name = "BAD_STOCK")
    private BigDecimal badStock;

    @Column(name = "GOOD_STOCK")
    private BigDecimal goodStock;

    public Integer getConsumableStockStateId() {
        return this.consumableStockStateId;
    }

    public void setConsumableStockStateId(Integer consumableStockStateId) {
        this.consumableStockStateId = consumableStockStateId;
    }

    public Integer getUnitId() {
        return this.unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getProductId() {
        return this.productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getSkuId() {
        return this.skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    public BigDecimal getBadStock() {
        return this.badStock;
    }

    public void setBadStock(BigDecimal badStock) {
        this.badStock = badStock;
    }

    public BigDecimal getGoodStock() {
        return this.goodStock;
    }

    public void setGoodStock(BigDecimal goodStock) {
        this.goodStock = goodStock;
    }
}

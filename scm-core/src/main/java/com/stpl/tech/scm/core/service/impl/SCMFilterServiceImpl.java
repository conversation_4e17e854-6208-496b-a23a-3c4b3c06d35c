package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.ApiResponse;
import com.stpl.tech.master.domain.model.Company;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.SCMProductManagementService;
import com.stpl.tech.scm.core.service.SCMFilterService;
import com.stpl.tech.scm.core.util.SCMConstant;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.UnitBrandFilterRequest;
import com.stpl.tech.util.domain.RequestContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SCMFilterServiceImpl implements SCMFilterService {

    @Autowired
    private SCMProductManagementService scmProductManagementService;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private EnvProperties env;

    @Override
    public ApiResponse filterProductBasicDetail(UnitBrandFilterRequest filterRequest) {
        Integer companyId = getCompanyIdWithFilterChecks();
        if (Objects.isNull(companyId)) {
            return scmProductManagementService.getProductBasicDetail();
        }
        List<Integer> mappedBrands=getMappedBrands(companyId);

        Boolean autoProduction = filterRequest.getAutoProduction();
        Map<Integer, IdCodeName> productBasicDetail = scmCache.getProductDefinitions().values().stream()
                .filter(p -> mappedBrands.contains(p.getBrandId()))
                .filter(p -> autoProduction == null || autoProduction.equals(p.isAutoProduction()))
                .collect(Collectors.toMap(
                        ProductDefinition::getProductId,
                        p -> new IdCodeName(p.getProductId(), p.getUnitOfMeasure(), p.getProductName())
                ));

        return new ApiResponse(productBasicDetail);
    }

    @Override
    public List<Integer> getMappedBrands(Integer companyId) {
        List<Integer> mappedBrands;
        try {
            mappedBrands = SCMUtil.getMappedBrands(companyId, masterDataCache);
        } catch (Exception e) {
            log.error("Error fetching mapped brands for company: " + companyId, e);
            mappedBrands = Arrays.stream(env.getListOfBrandIds().split(",")).map(Integer::parseInt)
                    .toList();
        }
        return mappedBrands;
    }

    @Override
    public Integer getCompanyIdWithFilterChecks() {
        Integer companyId = RequestContext.getContext().getCompanyId();
        if(Objects.isNull(companyId)) {
            log.info("Company id from request context: " + companyId);
            return null;
        }

        boolean isExternalCompany = isExternalCompany(companyId);

        if(isExternalCompany) {
            return null;
        }
        return companyId;
    }

    @Override
    public boolean isExternalCompany(Integer companyId) {
        if(Objects.isNull(companyId) || companyId == 0) {
            return false;
        }
        Company company = masterDataCache.getCompany(companyId);
        return company != null && SCMConstant.EXTERNAL_COMPANY_TYPE.equals(company.getCompanyType());
    }

}

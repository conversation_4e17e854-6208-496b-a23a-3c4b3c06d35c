package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.data.model.SkuPriceData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface SkuPriceDataDao extends JpaRepository<SkuPriceData, Integer> {

    List<SkuPriceData> findByVendorIdAndSkuIdInAndPackagingIdInAndDeliveryLocationIdInAndDispatchLocationIdIn(Integer vendorId, Set<Integer> skuIds, Set<Integer> skuPackagingIds, Set<Integer> deliveryLocationIds, Set<Integer> dispatchLocationIds);
    List<SkuPriceData> findByVendorId(Integer vendorId);

}

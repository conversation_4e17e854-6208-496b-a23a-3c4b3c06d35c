package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "USER_PRODUCT_CREATION_REQUEST")
public class UserProductCreationRequestData {

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "PRODUCT_CREATION_ID", unique = true, nullable = false)
    private Integer productId;
    @Column(name = "PRODUCT_NAME", nullable = false)
    private String productName;
    @Column(name = "PRODUCT_DESCRIPTION")
    private String productDescription;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CATEGORY_ID", nullable = false)
    private CategoryDefinitionData categoryDefinition;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SUB_CATEGORY_ID")
    private SubCategoryDefinitionData subCategoryDefinition;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PROFILE_ID")
    private ProfileDefinitionData profileDefinitionData;
    @Column(name = "SUPPORTS_LOOSE_ORDERING", nullable = false)
    private String supportsLooseOrdering;
    @Column(name = "CREATION_DATE")
    private Date creationDate;
    @Column(name = "CREATED_BY", nullable = false)
    private int createdBy;
    @Column(name = "HAS_INNER", nullable = false)
    private String hasInner;
    @Column(name = "RECIPE_REQUIRED", nullable = false)
    private String recipeRequired = "N";
    @Column(name = "HAS_CASE", nullable = false)
    private String hasCase;
    @Column(name = "STOCK_KEEPING_FREQUENCY", nullable = false)
    private String stockKeepingFrequency;
    @Column(name = "PRODUCT_CODE")
    private String productCode;
    @Column(name = "SHELF_LIFE_IN_DAYS", nullable = false)
    private int shelfLifeInDays;
    @Column(name = "PRODUCT_STATUS", nullable = false)
    private String productStatus;
    @Column(name = "UNIT_OF_MEASURE", nullable = false)
    private String unitOfMeasure;
    @Column(name = "PRODUCT_IMAGE")
    private String productImage;
    @Column(name = "UNIT_PRICE")
    private BigDecimal unitPrice;
    @Column(name = "NEGOTIATED_UNIT_PRICE")
    private BigDecimal negotiatedUnitPrice;
    @Column(name = "PARTICIPATES_IN_RECIPE", nullable = false)
    private String participatesInRecipe = "N";
    @Column(name = "PARTICIPATES_IN_CAFE_RECIPE", nullable = false)
    private String participatesInCafeRecipe = "N";
    @Column(name = "ASSET_ORDERING", nullable = false)
    private String assetOrdering = "N";
    @Column(name = "VARIANT_LEVEL_ORDERING", nullable = false)
    private String variantLevelOrdering = "N";
    @Column(name = "SUPPORTS_SPECIALIZED_ORDERING", nullable = false)
    private String supportsSpecializedOrdering = "N";
    //private List<ProductFulfillmentTypeData> fulfillmentType = new ArrayList<ProductFulfillmentTypeData>(0);
    @Column(name = "TAX_CATEGORY_CODE", nullable = false)
    private String taxCategoryCode;
    @Column(name = "FULFILLMENT_TYPE", nullable = false)
    private String fulfillmentType;
    @Column(name = "DEFAULT_FULFILLMENT_TYPE")
    private String defaultFulfillmentType;
    @Column(name = "AVAILABLE_AT_CAFE", nullable = false)
    private String availableForCafe;
    @Column(name = "INTER_CAFE_TRANSFER", nullable = false)
    private String interCafeTransfer;
    @Column(name = "AVAILABLE_FOR_CAFE_INVENTORY")
    private String availableForCafeInventory;
    @Column(name = "VARIANCE_TYPE")
    private String varianceType;
    @Column(name = "KITCHEN_VARIANCE_TYPE")
    private String kitchenVarianceType;
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "product")
    private List<DerivedMappingData> derivedMappingDataList = new ArrayList<DerivedMappingData>(0);
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "linkedProduct")
    private List<SkuDefinitionData> skuDefinitionDataList = new ArrayList<>(0);
    @Column(name = "AUTO_PRODUCTION", nullable = false)
    private String autoProduction = "N";
    @Column(name = "PARTICIPATES_IN_PNL", nullable = false)
    private String participatesInPnl = "Y";
    @Column(name = "IS_BULK_GR_ALLOWED", nullable = false)
    private String isBulkGRAllowed = "N";
    @Column(name = "DIVISION_ID")
    private Integer divisionId;
    @Column(name = "DEPARTMENT_ID")
    private Integer departmentId;
    @Column(name = "CLASSIFICATION_ID")
    private Integer classificationId;
    @Column(name = "SUB_CLASSIFICATION_ID")
    private Integer subClassificationId;
    @Column(name = "PRODUCT_TYPE")
    private String productType;
    @Column(name = "HOD_ID")
    private Integer hodId;
    @Column(name = "BRAND_ID")
    private Integer brandId;
    @Column(name = "COMPANY_ID")
    private  Integer companyId;
    @Column(name = "APPROVAL_DOCUMENT_ID")
    private Integer approvalDocumentId;

}

/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.scm.core.service.IdGeneratorServiceScm;
import com.stpl.tech.scm.data.dao.IdGeneratorScmDao;
import com.stpl.tech.scm.data.mongo.IdGeneratorScm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class IdGeneratorServiceScmImpl implements IdGeneratorServiceScm {

    @Autowired
    private IdGeneratorScmDao dao;

    @Override
    public <T> int getNextId(Class<T> context) {
        IdGeneratorScm data = null;
        List<IdGeneratorScm> list = dao.findByName(context.getName());
        if (list == null || list.size() == 0) {
            IdGeneratorScm idGenerator = new IdGeneratorScm();
            idGenerator.setName(context.getName());
            idGenerator.setNextId(1);
            data = dao.save(idGenerator);
        } else {
            data = list.get(0);
        }
        int id = data.getNextId();
        data.setNextId(data.getNextId() + 1);
        dao.save(data);
        return id;
    }

}

package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * Created by <PERSON><PERSON> on 13-06-2016.
 */
@Entity
@Table(name = "SCM_ORDER_PACKAGING")
public class SCMOrderPackagingData {

    private Integer id;
    private PackagingDefinitionData packagingDefinitionData;
    private BigDecimal conversionRatio;
    private BigDecimal numberOfUnitsPacked;
    private BigDecimal numberOfUnitsReceived;
	private BigDecimal numberOfUnitsRejected;
	private String rejectionReason;
    private BigDecimal transferredQuantity;
    private BigDecimal receivedQuantity;
    private TransferOrderItemData transferOrderItemData;
    private GoodsReceivedItemData goodsReceivedItemData;
    private BigDecimal pricePerUnit=BigDecimal.ZERO;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "SCM_ORDER_PACKAGING_ID")
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PACKAGING_ID", nullable = false)
    public PackagingDefinitionData getPackagingDefinitionData() {
        return packagingDefinitionData;
    }

    public void setPackagingDefinitionData(PackagingDefinitionData packagingDefinitionData) {
        this.packagingDefinitionData = packagingDefinitionData;
    }

    @Column(name = "NUMBER_OF_UNITS_PACKED", nullable = false)
    public BigDecimal getNumberOfUnitsPacked() {
        return numberOfUnitsPacked;
    }

    public void setNumberOfUnitsPacked(BigDecimal numberOfUnitsPacked) {
        this.numberOfUnitsPacked = numberOfUnitsPacked;
    }

    @Column(name = "NUMBER_OF_UNITS_RECEIVED", nullable = true)
    public BigDecimal getNumberOfUnitsReceived() {
        return numberOfUnitsReceived;
    }

    public void setNumberOfUnitsReceived(BigDecimal numberOfUnitsReceived) {
        this.numberOfUnitsReceived = numberOfUnitsReceived;
    }

    @Column(name = "TRANSFERRED_QUANTITY", nullable = true)
    public BigDecimal getTransferredQuantity() {
        return transferredQuantity;
    }

    public void setTransferredQuantity(BigDecimal transferredQuantity) {
        this.transferredQuantity = transferredQuantity;
    }

    @Column(name = "RECEIVED_QUANTITY", nullable = true)
    public BigDecimal getReceivedQuantity() {
        return receivedQuantity;
    }

    public void setReceivedQuantity(BigDecimal receivedQuantity) {
        this.receivedQuantity = receivedQuantity;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "TRANSFER_ORDER_ITEM_ID", nullable = true)
    public TransferOrderItemData getTransferOrderItemData() {
        return transferOrderItemData;
    }

    public void setTransferOrderItemData(TransferOrderItemData transferOrderItemData) {
        this.transferOrderItemData = transferOrderItemData;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "GOODS_RECEIVED_ITEM_ID", nullable = true)
    public GoodsReceivedItemData getGoodsReceivedItemData() {
        return goodsReceivedItemData;
    }

    public void setGoodsReceivedItemData(GoodsReceivedItemData goodsReceivedItemData) {
        this.goodsReceivedItemData = goodsReceivedItemData;
    }

    @Column(name = "CONVERSION_RATIO", nullable = false)
    public BigDecimal getConversionRatio() {
        return conversionRatio;
    }

    public void setConversionRatio(BigDecimal conversionRatio) {
        this.conversionRatio = conversionRatio;
    }

	@Column(name = "NUMBER_OF_UNITS_REJECTED", nullable = true)
	public BigDecimal getNumberOfUnitsRejected() {
		return numberOfUnitsRejected;
	}

	public void setNumberOfUnitsRejected(BigDecimal numberOfUnitsRejected) {
		this.numberOfUnitsRejected = numberOfUnitsRejected;
	}

	@Column(name = "REJECTION_REASON", nullable = true)
	public String getRejectionReason() {
		return rejectionReason;
	}

	public void setRejectionReason(String rejectionReason) {
		this.rejectionReason = rejectionReason;
	}

	@Column(name = "PRICE_PER_UNIT", nullable = true)
    public BigDecimal getPricePerUnit() {
        return pricePerUnit;
    }

    public void setPricePerUnit(BigDecimal pricePerUnit) {
        this.pricePerUnit = pricePerUnit;
    }
}

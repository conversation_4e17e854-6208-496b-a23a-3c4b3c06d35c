package com.stpl.tech.scm.data.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Entity
@Table(name = "VENDOR_CONTRACT_SO_INFO")
public class VendorContractSoInfo {

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "VENDOR_CONTRACT_ID", unique = true, nullable = false)
    private Integer vendorContractId;
    @Column(name = "VENDOR_ID", nullable = false)
    private Integer vendorId;

    @Column(name = "SO_ID", nullable = false)
    private Integer soId;
    @Column(name = "UNSIGNED_DOCUMENT_ID")
    private Integer unsignedDocumentId;
    @Column(name = "STATUS", nullable = false)
    private String status;
    @Column(name = "CREATED_BY", nullable = false)
    private Integer createdBy;
    @Column(name = "CONTRACT_REQUESTED_BY", nullable = false)
    private String contractRequestedBy;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_TIME", nullable = false)
    private Date creationTime;
    @Column(name = "IS_MAIL_TRIGGERED")
    private String isMailTriggered;
    @Column(name = "VENDOR_IP_ADDRESS")
    private String ipAddress;
    @Column(name = "VENDOR_LOCATION")
    private String vendorLocation;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_UPDATE_TIME", nullable = false)
    private Date lastUpdateTime;
    @Column(name = "VENDOR_NAME")
    private String vendorName;
    @Column(name = "VENDOR_DESIGNATION")
    private String vendorDesignation;

}

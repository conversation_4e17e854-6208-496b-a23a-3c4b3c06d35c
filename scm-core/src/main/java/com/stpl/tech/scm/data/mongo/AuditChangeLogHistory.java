package com.stpl.tech.scm.data.mongo;


import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.persistence.Entity;

//@Entity
@Document(collection = "AuditChangeLogHistory")
public class AuditChangeLogHistory {
    @Id
    private String id;

    @Field
    private AuditChangeLog auditChangeLog;

    public String get_id() {
        return id;
    }

    public void set_id(String _id) {
        this.id = _id;
    }

    public AuditChangeLog getAuditChangeLog() {
        return auditChangeLog;
    }

    public void setAuditChangeLog(AuditChangeLog auditChangeLog) {
        this.auditChangeLog = auditChangeLog;
    }
}

package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "SERVICE_ORDER_TO_SERVICE_RECEIVED_MAPPING")
public class ServiceOrderServiceReceiveMappingData {

	private Integer id;
	private ServiceOrderData serviceOrderData;
	private ServiceReceivedData serviceReceivedData;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "MAPPING_ID", nullable = false, unique = true)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@ManyToOne(targetEntity = ServiceOrderData.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "SERVICE_ORDER_ID")
	public ServiceOrderData getServiceOrderData() {
		return serviceOrderData;
	}

	public void setServiceOrderData(ServiceOrderData serviceOrderData) {
		this.serviceOrderData = serviceOrderData;
	}

	@ManyToOne(targetEntity = ServiceReceivedData.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "SERVICE_RECEIVED_ID")
	public ServiceReceivedData getServiceReceivedData() {
		return serviceReceivedData;
	}

	public void setServiceReceivedData(ServiceReceivedData serviceReceivedData) {
		this.serviceReceivedData = serviceReceivedData;
	}

}

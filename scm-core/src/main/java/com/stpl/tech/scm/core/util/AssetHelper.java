package com.stpl.tech.scm.core.util;

import com.stpl.tech.scm.data.model.AssetDefinitionData;
import com.stpl.tech.scm.domain.model.AssetStatusType;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AssetHelper {

    private static final Logger LOG = LoggerFactory.getLogger(AssetHelper.class);

    public static final List<AssetStatusType> depreciableStatusList = new ArrayList<>();

    static {
        depreciableStatusList.add(AssetStatusType.READY_FOR_USE);
        depreciableStatusList.add(AssetStatusType.BROKEN);
        depreciableStatusList.add(AssetStatusType.IN_REPAIR);
        depreciableStatusList.add(AssetStatusType.PENDING_LOST);
        depreciableStatusList.add(AssetStatusType.PENDING_RETURN);
        depreciableStatusList.add(AssetStatusType.IN_RENOVATION);
        depreciableStatusList.add(AssetStatusType.IN_USE);
    }

    public static BigDecimal getCurrentValueOfAsset(AssetDefinitionData assetDefinitionData) {
        BigDecimal currentValue = new BigDecimal(0.00f);
        Date today = new Date();
        if (assetDefinitionData.getAssetStatus().equals(AssetStatusType.CREATED.value()) ||
                assetDefinitionData.getAssetStatus().equals(AssetStatusType.INITIATED.value())) {
            currentValue = assetDefinitionData.getGrossBlock();
        } else if (AppUtils.getStatus(assetDefinitionData.getIsWriteOff())) {
            currentValue = BigDecimal.ZERO;
        } else {
            Date expectedEndDate = assetDefinitionData.getExpectedEndDate();
            Date lastDate = today.after(expectedEndDate) ? expectedEndDate : today;
            int absDaysDiff = AppUtils.getAbsDaysDiff(assetDefinitionData.getStartDate(), lastDate);
            currentValue = assetDefinitionData.getGrossBlock().subtract(new BigDecimal(absDaysDiff).multiply(assetDefinitionData.getDailyDepreciationRate()));
        }
        if(currentValue.compareTo(BigDecimal.valueOf(0)) == -1 ){
          return  assetDefinitionData.getGrossBlock().multiply(BigDecimal.valueOf(0.05));
        }
        return currentValue;
    }


}

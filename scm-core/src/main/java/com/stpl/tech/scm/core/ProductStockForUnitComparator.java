package com.stpl.tech.scm.core;

import java.util.Comparator;
import java.util.Map;

import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductStockForUnit;

public class ProductStockForUnitComparator implements Comparator<ProductStockForUnit> {

	private Map<Integer, ProductDefinition> productData;

	public ProductStockForUnitComparator(Map<Integer, ProductDefinition> productData) {
		super();
		this.productData = productData;
	}

	@Override
	public int compare(ProductStockForUnit o1, ProductStockForUnit o2) {
		ProductDefinition product1 = productData.get(o1.getProductId());
		IdCodeName category1 = product1.getCategoryDefinition();
		IdCodeName subCategroy1 = product1.getSubCategoryDefinition();

		ProductDefinition product2 = productData.get(o2.getProductId());
		IdCodeName category2 = product2.getCategoryDefinition();
		IdCodeName subCategroy2 = product2.getSubCategoryDefinition();

		if (category1.getId().equals(category2.getId())) {
			if (subCategroy1.getId().equals(subCategroy2.getId())) {
				return product1.getProductName().compareTo(product2.getProductName());
			}
			return subCategroy1.getId().compareTo(subCategroy2.getId());
		} else {
			return category1.getId().compareTo(category2.getId());
		}
	}

}

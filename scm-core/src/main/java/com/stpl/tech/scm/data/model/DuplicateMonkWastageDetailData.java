package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.EnumType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import com.stpl.tech.scm.domain.model.MonkWastageProcessingEnum;

import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 25-01-2019.
 * 
 * Entity to store duplicate monk wastage detail data that would have been filtered out
 * from the main MonkWastageDetailData table due to duplicate remake events.
 */
@Entity
@Table(name = "DUPLICATE_MONK_WASTAGE_DETAIL_DATA")
public class DuplicateMonkWastageDetailData {

    private Integer id;
    private Integer wastageData;
    private Integer taskId;
    private Integer orderId;
    private BigDecimal quantity;
    private BigDecimal originalQuantity;
    private String monkEvent;
    private String chaiMonk;
    private Integer errorCode;
    private String remakeReason;
    private BigDecimal expectedMilkQuantity;
    private String recipeString;
    private Integer milkProductId;
    private Integer unitId;

    private String isClubbed;
    private Integer clubbedWithTask;
    private String isSplit;
    private Integer linkedTaskId;

    private String isManualTask;
    private MonkWastageProcessingEnum isProcessed;
    private Date logAddTime;
    private Date logAddTimeAtServer;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "DUPLICATE_MONK_WASTAGE_DETAIL_DATA_ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "WASTAGE_ITEM_ID")
    public Integer getWastageData() {
        return wastageData;
    }

    public void setWastageData(Integer wastageData) {
        this.wastageData = wastageData;
    }

    @Column(name = "TASK_ID")
    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    @Column(name = "ORDER_ID")
    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    @Column(name = "QUANTITY")
    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    @Column(name = "ORIGINAL_QUANTITY")
    public BigDecimal getOriginalQuantity() {
        return originalQuantity;
    }

    public void setOriginalQuantity(BigDecimal originalQuantity) {
        this.originalQuantity = originalQuantity;
    }

    @Column(name = "MONK_EVENT")
    public String getMonkEvent() {
        return monkEvent;
    }

    public void setMonkEvent(String monkEvent) {
        this.monkEvent = monkEvent;
    }

    @Column(name = "CHAI_MONK")
    public String getChaiMonk() {
        return chaiMonk;
    }

    public void setChaiMonk(String chaiMonk) {
        this.chaiMonk = chaiMonk;
    }

    @Column(name = "ERROR_CODE")
    public Integer getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }

    @Column(name = "REMAKE_REASON")
    public String getRemakeReason() {
        return remakeReason;
    }

    public void setRemakeReason(String remakeReason) {
        this.remakeReason = remakeReason;
    }

    @Column(name = "EXPECTED_MILK_QUANTITY")
    public BigDecimal getExpectedMilkQuantity() {
        return expectedMilkQuantity;
    }

    public void setExpectedMilkQuantity(BigDecimal expectedMilkQuantity) {
        this.expectedMilkQuantity = expectedMilkQuantity;
    }

    @Column(name = "RECIPE_STRING")
    public String getRecipeString() {
        return recipeString;
    }

    public void setRecipeString(String recipeString) {
        this.recipeString = recipeString;
    }

    @Column(name = "MILK_PRODUCT_ID")
    public Integer getMilkProductId() {
        return milkProductId;
    }

    public void setMilkProductId(Integer milkProductId) {
        this.milkProductId = milkProductId;
    }

    @Column(name = "UNIT_ID")
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "IS_CLUBBED")
    public String getIsClubbed() {
        return isClubbed;
    }

    public void setIsClubbed(String isClubbed) {
        this.isClubbed = isClubbed;
    }

    @Column(name = "CLUBBED_WITH_TASK")
    public Integer getClubbedWithTask() {
        return clubbedWithTask;
    }

    public void setClubbedWithTask(Integer clubbedWithTask) {
        this.clubbedWithTask = clubbedWithTask;
    }

    @Column(name = "IS_SPLIT")
    public String getIsSplit() {
        return isSplit;
    }

    public void setIsSplit(String isSplit) {
        this.isSplit = isSplit;
    }

    @Column(name = "LINKED_TASK_ID")
    public Integer getLinkedTaskId() {
        return linkedTaskId;
    }

    public void setLinkedTaskId(Integer linkedTaskId) {
        this.linkedTaskId = linkedTaskId;
    }

    @Column(name = "IS_MANUAL_TASK")
    public String getIsManualTask() {
        return isManualTask;
    }

    public void setIsManualTask(String isManualTask) {
        this.isManualTask = isManualTask;
    }

    @Enumerated(EnumType.STRING)
    @Column(name = "IS_PROCESSED")
    public MonkWastageProcessingEnum getIsProcessed() {
        return isProcessed;
    }

    public void setIsProcessed(MonkWastageProcessingEnum isProcessed) {
        this.isProcessed = isProcessed;
    }

    @Column(name = "LOG_ADD_TIME")
    public Date getLogAddTime() {
        return logAddTime;
    }

    public void setLogAddTime(Date logAddTime) {
        this.logAddTime = logAddTime;
    }

    @Column(name = "LOG_ADD_TIME_AT_SERVER")
    public Date getLogAddTimeAtServer() {
        return logAddTimeAtServer;
    }

    public void setLogAddTimeAtServer(Date logAddTimeAtServer) {
        this.logAddTimeAtServer = logAddTimeAtServer;
    }
}

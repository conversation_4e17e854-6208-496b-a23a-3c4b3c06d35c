/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;

@Entity
@Table(name = "PAYMENT_REQUEST_QUERY")
public class PaymentRequestQueryData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PR_QUERY_ID")
    private Integer prQueryId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PR_ID", nullable = true)
    private PaymentRequestData prId;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PAYMENT_DEVIATION_ID", nullable = false)
    private PaymentDeviationData paymentDeviationData;

    @Column(name = "QUERY_RAISED_BY")
    private Integer queryRaisedBy;

    @Column(name = "QUERY_RESOLVED_BY")
    private Integer queryResolvedBy;

    @Column(name = "RAISED_BY_COMMENT")
    private String raisedByComment;

    @Column(name = "RESOLVED_BY_COMMENT")
    private String resolvedByComment;

    @Column(name = "UPLOADED_DOCUMENT_ID")
    private Integer uploadedDocumentId;

    @Column(name = "QUERIED_FOR_PR_ID")
    private Integer queriedForPrId;

    public Integer getPrQueryId() {
        return this.prQueryId;
    }

    public void setPrQueryId(Integer prQueryId) {
        this.prQueryId = prQueryId;
    }

    public PaymentRequestData getPrId() {
        return prId;
    }

    public void setPrId(PaymentRequestData prId) {
        this.prId = prId;
    }

    public PaymentDeviationData getPaymentDeviationData() {
        return paymentDeviationData;
    }

    public void setPaymentDeviationData(PaymentDeviationData paymentDeviationData) {
        this.paymentDeviationData = paymentDeviationData;
    }

    public Integer getQueryRaisedBy() {
        return this.queryRaisedBy;
    }

    public void setQueryRaisedBy(Integer queryRaisedBy) {
        this.queryRaisedBy = queryRaisedBy;
    }

    public Integer getQueryResolvedBy() {
        return this.queryResolvedBy;
    }

    public void setQueryResolvedBy(Integer queryResolvedBy) {
        this.queryResolvedBy = queryResolvedBy;
    }

    public String getRaisedByComment() {
        return this.raisedByComment;
    }

    public void setRaisedByComment(String raisedByComment) {
        this.raisedByComment = raisedByComment;
    }

    public String getResolvedByComment() {
        return this.resolvedByComment;
    }

    public void setResolvedByComment(String resolvedByComment) {
        this.resolvedByComment = resolvedByComment;
    }

    public Integer getUploadedDocumentId() {
        return this.uploadedDocumentId;
    }

    public void setUploadedDocumentId(Integer uploadedDocumentId) {
        this.uploadedDocumentId = uploadedDocumentId;
    }

    public Integer getQueriedForPrId() {
        return queriedForPrId;
    }

    public void setQueriedForPrId(Integer queriedForPrId) {
        this.queriedForPrId = queriedForPrId;
    }
}

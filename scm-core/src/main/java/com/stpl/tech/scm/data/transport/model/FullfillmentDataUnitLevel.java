package com.stpl.tech.scm.data.transport.model;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FullfillmentDataUnitLevel {

    String transferringUnit;
    Long transferringUnitId;
    String requestingUnit;
    Long requestingUnitId;

    Double fPerWeightedAvg;
    Double onTimeFPerWeightedAvg;
    Double onDateFPerWeightedAvg;

    Double totalRoRaisedOnTimePer;
    Double totalRoRaisedOnDatePer;
    Double totalDelayRoRaisedPer;

    Double imFPerWeightedAvg;

    Double bakeryProductFPAvg;
    Double bakeryProductOnTimeFFAvg;
    Double bakeryProductOnDateFFAvg;
    Boolean isBakeryProduct;

    Double bakeryRoRaisedOnTimePer;
    Double bakeryRoRaisedOnDatePer;
    Double bakeryDelayRoRaisedPer;

}

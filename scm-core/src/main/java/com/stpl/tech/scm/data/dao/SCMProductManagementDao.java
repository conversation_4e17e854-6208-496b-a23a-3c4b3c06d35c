package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.data.model.DerivedMappingData;
import com.stpl.tech.scm.data.model.PackagingDefinitionData;
import com.stpl.tech.scm.data.model.ProductDefinitionData;
import com.stpl.tech.scm.data.model.ProductFulfillmentTypeData;
import com.stpl.tech.scm.data.model.ProductPackagingMappingData;
import com.stpl.tech.scm.data.model.RegionProductPackagingMappingData;
import com.stpl.tech.scm.data.model.SkuAttributeValueData;
import com.stpl.tech.scm.data.model.SkuDefinitionData;
import com.stpl.tech.scm.data.model.SkuPackagingMappingData;
import com.stpl.tech.scm.data.model.UnitSkuVendorMapping;
import com.stpl.tech.scm.data.model.UserProductCreationRequestData;
import com.stpl.tech.scm.domain.model.PackagingType;
import com.stpl.tech.scm.domain.model.RPPMappingRequest;
import com.stpl.tech.scm.domain.model.RequestedSkuDetails;
import com.stpl.tech.scm.domain.model.UnitProductPackagingMapping;
import com.stpl.tech.scm.domain.model.UserRequestDto;

import java.util.List;
import java.util.Map;

/**
 * Created by Rahul Singh on 07-05-2016.
 */
public interface SCMProductManagementDao extends SCMAbstractDao {

    public List<ProductFulfillmentTypeData> getProductFulfillmentTypes(int productId);

    public List<ProductPackagingMappingData> getPackagingMappingsForProduct(int productId);

    public List<SkuDefinitionData> getSkuAgainstProduct(int productId);

    public List<SkuPackagingMappingData> getPackagingMappingsForSku(int skuId);

	public SkuAttributeValueData fetchSkuAttributeByType(int skuId, int attributeId);

    public List<UnitSkuVendorMapping> getUnitSkuVendorMappingsByUnitId(int unitId);

    public void updateIsDefaultFlag(Integer skuId, Integer productId);

    PackagingDefinitionData findByPackagingTypeAndPackagingCode(PackagingType packagingType, String packagingCode);

    public List<UnitProductPackagingMapping> findPackagingByUnitId(int unitId);

    public List<Object[]> getUnitSkuPackagingMappings(int unitId);

    public List<UserProductCreationRequestData> findAllProductsInStatus(UserRequestDto productRequest);

    public void isDuplicateProduct(String productName, Integer productId) throws SumoException;
    public void isDuplicateProduct2(String productName, Integer productId) throws SumoException;

    public List<RequestedSkuDetails> findAllSkusInStatus(UserRequestDto skuRequest) throws Exception;

    void isSkuNameAlreadyExistsInUserSkuCreation(String skuName, Integer userCreationSkuId) throws SumoException;

    void isSkuNameAlreadyExistsInSkuDefinition(String skuName, Integer skuId) throws SumoException;

    Map<Integer, DerivedMappingData> getDerivedProductsMapping(Integer id, boolean isUnitId);

    Map<Integer, DerivedMappingData> getDerivedMappingsByIds(List<Integer> derivedMappingIds);

    Map<Integer, ProductDefinitionData> getProductsByIds(List<Integer> productIds);

    List<RegionProductPackagingMappingData> getRegionProductPackagings(RPPMappingRequest request) throws SumoException;

    List<RegionProductPackagingMappingData> findByMappingIds(List<Integer> mappingIds);
}

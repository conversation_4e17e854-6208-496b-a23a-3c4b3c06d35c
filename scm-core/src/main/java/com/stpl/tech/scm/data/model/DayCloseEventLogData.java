package com.stpl.tech.scm.data.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 13-06-2017.
 */
@Entity
@Table(name = "DAY_CLOSE_EVENT_LOG")
public class DayCloseEventLogData {

    private Integer id;
    private SCMDayCloseEventData dayCloseEventData;
    private String eventCompleted;
    private String inventoryList;
    private Integer createdBy;
    private Date createdAt;
    private String status;

    private List<WriteOffItemData> writeOffItemDatas = new ArrayList<>(0);


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "EVENT_LOG_ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CLOSURE_EVENT_ID", nullable = false)
    public SCMDayCloseEventData getDayCloseEventData() {
        return dayCloseEventData;
    }

    public void setDayCloseEventData(SCMDayCloseEventData dayCloseEventData) {
        this.dayCloseEventData = dayCloseEventData;
    }

    @Column(name = "INVENTORY_LIST",nullable = true)
    public String getInventoryList() {
        return inventoryList;
    }

    public void setInventoryList(String inventoryList) {
        this.inventoryList = inventoryList;
    }

    @Column(name = "EVENT_TYPE", nullable = true)
    public String getEventCompleted() {
        return eventCompleted;
    }

    public void setEventCompleted(String eventCompleted) {
        this.eventCompleted = eventCompleted;
    }

    @Column(name = "EVENT_STATUS", nullable = true)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "CREATED_BY", nullable = true)
    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "CREATED_AT", nullable = true)
    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @OneToMany(mappedBy = "eventLogData")
    public List<WriteOffItemData> getWriteOffItemDatas() {
        return writeOffItemDatas;
    }

    public void setWriteOffItemDatas(List<WriteOffItemData> writeOffItemDatas) {
        this.writeOffItemDatas = writeOffItemDatas;
    }
}

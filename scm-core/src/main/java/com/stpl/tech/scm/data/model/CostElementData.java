package com.stpl.tech.scm.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;

@Entity
@Table(name = "COST_ELEMENT_DATA")
public class CostElementData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2565093697355543375L;

	private Integer costElementId;
	private String costElementName;
	private String Description;
	private String ascCode;
	private String costElementStatus;
	private BigDecimal taxRate;
	private ListDetailData category;
	private ListDetailData department;
	private ListDetailData division;
	private ListTypeData subCategory;
	private ListDatas subSubCategory;
	private String capex;
	private String uom;
	private String isPriceUpdate;
	private BigDecimal lowerPriceRange;
	private BigDecimal upperPriceRange;

	private ListDetailData capexCategory;

	private ListTypeData capexSubCategory;


	private List<CostElementPackagingMapping> packagingList = new ArrayList<CostElementPackagingMapping>();

	public CostElementData() {
		// TODO Auto-generated constructor stub
	}

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "COST_ELEMENT_ID")
	public Integer getCostElementId() {
		return costElementId;
	}

	public void setCostElementId(Integer costElementId) {
		this.costElementId = costElementId;
	}

	@Column(name = "COST_ELEMENT_NAME")
	public String getCostElementName() {
		return costElementName;
	}

	public void setCostElementName(String costElementName) {
		this.costElementName = costElementName;
	}

	@Column(name = "COST_ELEMENT_DESCRIPTION")
	public String getDescription() {
		return Description;
	}

	public void setDescription(String description) {
		Description = description;
	}

	@Column(name = "ASC_CODE")
	public String getAscCode() {
		return ascCode;
	}

	public void setAscCode(String shortCode) {
		this.ascCode = shortCode;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "costElementDataId")
	public List<CostElementPackagingMapping> getPackagingList() {
		return packagingList;
	}

	public void setPackagingList(List<CostElementPackagingMapping> packagingList) {
		this.packagingList = packagingList;
	}

	@Column(name = "COST_ELEMENT_STATUS")
	public String getCostElementStatus() {
		return costElementStatus;
	}

	public void setCostElementStatus(String costElementStatus) {
		this.costElementStatus = costElementStatus;
	}

	@Column(name = "TAX_RATE")
	public BigDecimal getTaxRate() {
		return taxRate;
	}

	public void setTaxRate(BigDecimal taxRate) {
		this.taxRate = taxRate;
	}

	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CATEGORY_ID", nullable = true)
	public ListDetailData getCategory() {
		return category;
	}

	public void setCategory(ListDetailData category) {
		this.category = category;
	}

	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "DEPARTMENT_ID", nullable = true)
	public ListDetailData getDepartment() {
		return department;
	}

	public void setDepartment(ListDetailData department) {
		this.department = department;
	}

	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "DIVISION_ID", nullable = true)
	public ListDetailData getDivision() {
		return division;
	}

	public void setDivision(ListDetailData division) {
		this.division = division;
	}

	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SUB_CATEGORY", nullable = true)
	public ListTypeData getSubCategory() {
		return subCategory;
	}
	
	public void setSubCategory(ListTypeData subCategory) {
		this.subCategory = subCategory;
	}

	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SUB_SUB_CATEGORY", nullable = true)
	public ListDatas getSubSubCategory() {
		return subSubCategory;
	}

	public void setSubSubCategory(ListDatas subSubCategory) {
		this.subSubCategory = subSubCategory;
	}

	@Column(name = "CAPEX")
	public String getCapex() {
		return capex;
	}

	public void setCapex(String capex) {
		this.capex = capex;
	}

	@Column(name = "UOM")
	public String getUom() {
		return uom;
	}

	public void setUom(String uom) {
		this.uom = uom;
	}

	@Column(name = "ISPRICEUPDATE")
	public String getIsPriceUpdate() {
		return isPriceUpdate;
	}

	public void setIsPriceUpdate(String isPriceUpdate) {
		this.isPriceUpdate = isPriceUpdate;
	}
	@Column(name = "LOWER_PRICE_RANGE")
	public BigDecimal getLowerPriceRange() {
		return lowerPriceRange;
	}
	public void setLowerPriceRange(BigDecimal lowerPriceRange) {
		this.lowerPriceRange = lowerPriceRange;
	}
	@Column(name = "UPPER_PRICE_RANGE")
	public BigDecimal getUpperPriceRange() {
		return upperPriceRange;
	}
	public void setUpperPriceRange(BigDecimal upperPriceRange) {
		this.upperPriceRange = upperPriceRange;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "CAPEX_CATEGORY_ID", nullable = true)
	public ListDetailData getCapexCategory() {
		return capexCategory;
	}
	public void setCapexCategory(ListDetailData capexCategory) {
		this.capexCategory = capexCategory;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "CAPEX_SUB_CATEGORY", nullable = true)
	public ListTypeData getCapexSubCategory() {
		return capexSubCategory;
	}
	public void setCapexSubCategory(ListTypeData capexSubCategory) {
		this.capexSubCategory = capexSubCategory;
	}
}

package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.math.BigDecimal;

@Entity
@Table(name = "ORDERING_MULTIPLIER_DATA")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OrderingMultiplierData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "MULTIPLIER_NAME", nullable = false, length = 50)
    private String multiplierName;

    @Column(name = "BIG_DAY_MULTIPLIER", precision = 18, scale = 4)
    private BigDecimal bigDayMultiplier;

    @Column(name = "FESTIVE_DAY_MULTIPLIER", precision = 18, scale = 4)
    private BigDecimal festiveDayMultiplier;
}

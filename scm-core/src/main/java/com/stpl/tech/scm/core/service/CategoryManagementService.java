package com.stpl.tech.scm.core.service;

import java.util.List;
import java.util.Map;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.domain.model.CategoryAttributeMapping;
import com.stpl.tech.scm.domain.model.CategoryAttributeValue;
import com.stpl.tech.scm.domain.model.CategoryDefinition;
import com.stpl.tech.scm.domain.model.SubCategoryDefinition;

/**
 * Created by <PERSON><PERSON> on 11-05-2016.
 */
public interface CategoryManagementService {

    public CategoryDefinition viewCategory(int categoryId);

    public List<CategoryDefinition> viewAllCategories();

    public boolean addNewCategory(CategoryDefinition categoryDefinition) throws SumoException;

    public boolean updateCategory(CategoryDefinition categoryDefinition);

    public boolean deactivateCategory(int categoryId);

    public boolean activateCategory(int categoryId);

    public Map<Integer, List<CategoryAttributeMapping>> getAllCategoryAttributeMappings();

    public CategoryAttributeMapping getCategoryAttributeMapping(int categoryId);

    public boolean addCategoryAttributeMapping(List<CategoryAttributeMapping> categoryAttributeMappings) throws SumoException;

    public boolean updateCategoryAttributeMapping(List<CategoryAttributeMapping> categoryAttributeMappings);

    public boolean deactivateCategoryAttributeMapping(int categoryAttributeMappingId);

    public boolean activateCategoryAttributeMapping(int categoryAttributeMappingId);

    public Map<Integer, List<CategoryAttributeValue>> getAllCategoryAttributeValues();

    public Map<Integer, List<CategoryAttributeValue>> getAllCategoryAttributeValuesByCategory();

    public CategoryAttributeValue getCategoryAttributeValue(int categoryAttributeValueId);

    public boolean addCategoryAttributeValue(List<CategoryAttributeValue> categoryAttributeValues) throws SumoException;

    public boolean updateCategoryAttributeValue(List<CategoryAttributeValue> categoryAttributeValues);

    public boolean deactivateCategoryAttributeValue(int categoryAttributeValueId);

    public boolean activateCategoryAttributeValue(int categoryAttributeValueId);

    public Map<Integer, SubCategoryDefinition> getAllSubCategoryDefinitions();
}

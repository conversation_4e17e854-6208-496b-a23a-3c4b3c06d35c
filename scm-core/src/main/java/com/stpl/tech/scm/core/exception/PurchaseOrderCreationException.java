package com.stpl.tech.scm.core.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 17-05-2017.
 */
@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR, reason = "Negative Stock Entered")
public class PurchaseOrderCreationException extends SumoException{

    private static final long serialVersionUID = 616315625637405710L;

    public PurchaseOrderCreationException() {
    }

    public PurchaseOrderCreationException(String message) {
        super(message);
    }

    public PurchaseOrderCreationException(Throwable cause) {
        super(cause);
    }

    public PurchaseOrderCreationException(String message, Throwable cause) {
        super(message, cause);
    }

    public PurchaseOrderCreationException(String message, Throwable cause, boolean enableSuppression,
                                          boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

}

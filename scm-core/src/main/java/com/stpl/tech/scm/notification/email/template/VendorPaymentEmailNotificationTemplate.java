package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.master.domain.model.Company;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.config.ScmConfig;
import com.stpl.tech.scm.data.model.PaymentInvoiceData;
import com.stpl.tech.scm.domain.model.PRPaymentDetail;
import com.stpl.tech.scm.domain.model.PaymentRequest;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by Chaayos on 05-09-2016.
 */
public class VendorPaymentEmailNotificationTemplate extends AbstractVelocityTemplate {

	private final static Logger LOG = LoggerFactory.getLogger(VendorPaymentEmailNotificationTemplate.class);

	private VendorDetail vendorDetail;
	private PaymentRequest paymentRequest;
	private PRPaymentDetail paymentDetail;
	private Company company;
	private String basePath;
	private String paymentDate;
	private String selectedCompany;
	private String actualDate;
	private PaymentInvoiceData paymentInvoiceData;

	public VendorPaymentEmailNotificationTemplate() {

	}

	public VendorPaymentEmailNotificationTemplate(VendorDetail vendorDetail, PaymentRequest paymentRequest,
			PRPaymentDetail paymentDetail, String basePath, String paymentDate, Company company, String selectedCompany, String actualDate,PaymentInvoiceData paymentInvoiceData) {
		this.vendorDetail = vendorDetail;
		this.basePath = basePath;
		this.paymentRequest = paymentRequest;
		this.paymentDetail = paymentDetail;
		this.paymentDate = paymentDate;
		this.company = company;
		this.selectedCompany = selectedCompany;
		this.actualDate = actualDate;
		this.paymentInvoiceData = paymentInvoiceData;
	}

	@Override
	public String getTemplatePath() {
		return "templates/VendorPaymentEmailTemplate.html";
	}

	@Override
	public String getFilepath() {
		return basePath + "/vendor/paymentAdvice/" + vendorDetail.getVendorId() + "/"
				+ paymentRequest.getPaymentRequestId() + ".html";
	}

	@Override
	public Map<String, Object> getData() {
		Map<String, Object> stringObjectMap = new HashMap<>();
		stringObjectMap.put("vendorDetail", vendorDetail);
		stringObjectMap.put("paymentRequest", paymentRequest);
		stringObjectMap.put("paymentDetail", paymentDetail);
		stringObjectMap.put("paymentDate", paymentDate);
		stringObjectMap.put("actualDate", actualDate);
		stringObjectMap.put("paymentInvoiceData", paymentInvoiceData);
		stringObjectMap.put("companyName",
				selectedCompany != null ? selectedCompany.toUpperCase()
						: SCMServiceConstants.SUNSHINE_TEAHOUSE);
		return stringObjectMap;
	}

	public VendorDetail getVendorDetail() {
		return vendorDetail;
	}

	public PaymentRequest getPaymentRequest() {
		return paymentRequest;
	}
}

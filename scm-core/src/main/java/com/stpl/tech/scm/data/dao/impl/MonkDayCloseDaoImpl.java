package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.scm.data.dao.MonkDayCloseDao;
import com.stpl.tech.scm.data.model.MonkDayCloseEventStatusData;
import com.stpl.tech.scm.data.model.MonkStatusDayCloseData;
import com.stpl.tech.scm.data.model.MonkStatusDayCloseHistoryData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Repository
public class MonkDayCloseDaoImpl extends SCMAbstractDaoImpl implements MonkDayCloseDao {
    
    private static final Logger LOG = LoggerFactory.getLogger(MonkDayCloseDaoImpl.class);
    
    @Autowired
    private MasterDataCache masterDataCache;
    
    @Override
    public MonkDayCloseEventStatusData createMonkDayCloseEvent(Integer unitId, Date businessDate, String eventType, String eventStatus) {
        MonkDayCloseEventStatusData eventData = MonkDayCloseEventStatusData.builder()
                .unitId(unitId)
                .businessDate(businessDate)
                .eventType(eventType)
                .eventStatus(eventStatus)
                .build();
        
        return add(eventData);
    }
    
    @Override
    public MonkDayCloseEventStatusData getMonkDayCloseEvent(Integer unitId, Date businessDate) {
        String hql = "FROM MonkDayCloseEventStatusData WHERE unitId = :unitId AND businessDate = :businessDate";
        Query query = manager.createQuery(hql, MonkDayCloseEventStatusData.class);
        query.setParameter("unitId", unitId);
        query.setParameter("businessDate", businessDate);
        
        try {
            return (MonkDayCloseEventStatusData) query.getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public List<MonkStatusDayCloseData> getMonkStatusesByEventId(Long eventStatusId) {
        String hql = "FROM MonkStatusDayCloseData WHERE eventStatusId = :eventStatusId ORDER BY monkName";
        Query query = manager.createQuery(hql);
        query.setParameter("eventStatusId", eventStatusId);
        return query.getResultList();
    }
    
    @Override
    public MonkStatusDayCloseData createMonkStatus(Long eventStatusId, String monkName, String monkStatus, Integer updatedBy) {
        MonkStatusDayCloseData statusData = MonkStatusDayCloseData.builder()
                .eventStatusId(eventStatusId)
                .monkName(monkName)
                .monkStatus(monkStatus)
                .build();
        
        MonkStatusDayCloseData savedStatusData = add(statusData);
        
        // Create initial history record for the newly created monk status
        if (savedStatusData != null && savedStatusData.getMonkStatusDayCloseId() != null) {
            createMonkStatusHistory(savedStatusData.getMonkStatusDayCloseId(), monkStatus, "Initial status on creation",updatedBy);
            LOG.info("Created initial history record for new monk status: {} with status: {} for monk: {}", 
                    savedStatusData.getMonkStatusDayCloseId(), monkStatus, monkName);
        }
        
        return savedStatusData;
    }
    
    @Override
    public MonkStatusDayCloseData updateMonkStatus(Long monkStatusDayCloseId, String monkStatus, Integer updatedBy) {
        MonkStatusDayCloseData statusData = find(MonkStatusDayCloseData.class, monkStatusDayCloseId);
        if (statusData != null) {
            // Create history record with the new status (if different)
            if (!monkStatus.equals(statusData.getMonkStatus())) {
                createMonkStatusHistory(monkStatusDayCloseId, monkStatus, null, updatedBy);
                LOG.info("Created history record for monk status change from {} to {} for ID: {}", 
                        statusData.getMonkStatus(), monkStatus, monkStatusDayCloseId);
            }
            
            // Update the current status
            statusData.setMonkStatus(monkStatus);
            statusData.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
            return update(statusData);
        }
        return null;
    }
    
    @Override
    public MonkDayCloseEventStatusData updateEventStatus(Long monkDayCloseEventStatusId, String eventStatus) {
        MonkDayCloseEventStatusData eventData = find(MonkDayCloseEventStatusData.class, monkDayCloseEventStatusId);
        if (eventData != null) {
            eventData.setEventStatus(eventStatus);
            eventData.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
            return update(eventData);
        }
        return null;
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public List<MonkDayCloseEventStatusData> getPendingMonkDayCloseEvents() {
        String hql = "FROM MonkDayCloseEventStatusData WHERE eventStatus = 'INITIATED' ORDER BY businessDate DESC";
        Query query = manager.createQuery(hql);
        return query.getResultList();
    }
    
    @Override
    public MonkDayCloseEventStatusData getLatestMonkDayCloseEventByUnit(Integer unitId) {
        String hql = "FROM MonkDayCloseEventStatusData WHERE unitId = :unitId ORDER BY businessDate DESC, createdAt DESC";
        Query query = manager.createQuery(hql, MonkDayCloseEventStatusData.class);
        query.setParameter("unitId", unitId);
        query.setMaxResults(1);
        
        try {
            return (MonkDayCloseEventStatusData) query.getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public List<MonkStatusDayCloseData> getLatestMonkStatusesByUnit(Integer unitId, Integer limit) {
        // Get the latest monk statuses across all events for this unit
        // Order by most recent business date and creation time, then limit by unique monk names
        String hql = "SELECT ms FROM MonkStatusDayCloseData ms " +
                    "JOIN MonkDayCloseEventStatusData es ON ms.eventStatusId = es.monkDayCloseEventStatusId " +
                    "WHERE es.unitId = :unitId " +
                    "ORDER BY es.businessDate DESC, es.createdAt DESC";
        
        Query query = manager.createQuery(hql);
        query.setParameter("unitId", unitId);
        
        List<MonkStatusDayCloseData> allStatuses = query.getResultList();
        
        // Filter to get unique monk names (latest status for each monk)
        Map<String, MonkStatusDayCloseData> uniqueMonkStatuses = new LinkedHashMap<>();
        for (MonkStatusDayCloseData status : allStatuses) {
            if (!uniqueMonkStatuses.containsKey(status.getMonkName())) {
                uniqueMonkStatuses.put(status.getMonkName(), status);
//                if (uniqueMonkStatuses.size() >= limit) {
//                    break;
//                }
            }
        }
        
        return new ArrayList<>(uniqueMonkStatuses.values());
    }
    
    @Override
    public Integer getRequiredMonksCount(Integer unitId) {
        try {
            UnitBasicDetail unitBasicDetail = masterDataCache.getUnitBasicDetail(unitId.intValue());
            if (unitBasicDetail != null) {
                Integer monksNeeded = unitBasicDetail.getNoOfMonksNeeded();
                return monksNeeded != null && monksNeeded > 0 ? monksNeeded : 0;
            }
            return 0; // Default fallback
        } catch (Exception e) {
            return 0; // Default fallback
        }
    }
    
    @Override
    public void linkKettleDayClose(Long eventId, Integer kettleDayCloseId) {
        String updateQuery = "UPDATE MONK_DAY_CLOSE_EVENT_STATUS SET KETTLE_DAY_CLOSE = :kettleDayCloseId, UPDATED_AT = :updatedAt " +
                "WHERE MONK_DAY_CLOSE_EVENT_STATUS_ID = :eventId";
        
        Query query = manager.createNativeQuery(updateQuery);
        query.setParameter("kettleDayCloseId", kettleDayCloseId);
        query.setParameter("updatedAt", new Timestamp(System.currentTimeMillis()));
        query.setParameter("eventId", eventId);
        
        int updatedRows = query.executeUpdate();
        LOG.info("Updated {} rows linking kettle day close ID: {} to event: {}", updatedRows, kettleDayCloseId, eventId);
    }
    
    @Override
    public void linkSumoDayClose(Long eventId, Integer sumoDayCloseId) {
        String updateQuery = "UPDATE MONK_DAY_CLOSE_EVENT_STATUS SET SUMO_DAY_CLOSE = :sumoDayCloseId, UPDATED_AT = :updatedAt " +
                "WHERE MONK_DAY_CLOSE_EVENT_STATUS_ID = :eventId";
        
        Query query = manager.createNativeQuery(updateQuery);
        query.setParameter("sumoDayCloseId", sumoDayCloseId);
        query.setParameter("updatedAt", new Timestamp(System.currentTimeMillis()));
        query.setParameter("eventId", eventId);
        
        int updatedRows = query.executeUpdate();
        LOG.info("Updated {} rows linking sumo day close ID: {} to event: {}", updatedRows, sumoDayCloseId, eventId);
    }
    
    @Override
    public MonkStatusDayCloseHistoryData createMonkStatusHistory(Long monkStatusDayCloseId, String monkStatus, String comment,Integer updatedBy) {
        MonkStatusDayCloseHistoryData historyData = MonkStatusDayCloseHistoryData.builder()
                .monkStatusDayCloseId(monkStatusDayCloseId)
                .monkStatus(monkStatus)
                .comment(comment)
                .updatedBy(updatedBy)
                .build();
        
        return add(historyData);
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public List<MonkStatusDayCloseHistoryData> getMonkStatusHistory(Long monkStatusDayCloseId) {
        String hql = "FROM MonkStatusDayCloseHistoryData WHERE monkStatusDayCloseId = :monkStatusDayCloseId ORDER BY createdAt DESC";
        Query query = manager.createQuery(hql);
        query.setParameter("monkStatusDayCloseId", monkStatusDayCloseId);
        return query.getResultList();
    }
    
    @Override
    public MonkStatusDayCloseData updateMonkStatusWithHistory(Long monkStatusDayCloseId, String monkStatus, String comment, Integer updatedBy) {
        // Get the current monk status
        MonkStatusDayCloseData currentStatus = find(MonkStatusDayCloseData.class, monkStatusDayCloseId);
        if (currentStatus == null) {
            LOG.error("MonkStatusDayCloseData not found with ID: {}", monkStatusDayCloseId);
            return null;
        }
        
        // Create history record with the new status (if different)
        if (!monkStatus.equals(currentStatus.getMonkStatus())) {
            createMonkStatusHistory(monkStatusDayCloseId, monkStatus, comment, updatedBy);
            LOG.info("Created history record for monk status change from {} to {} for ID: {} with comment: {}", 
                    currentStatus.getMonkStatus(), monkStatus, monkStatusDayCloseId, comment);
        }
        
        // Update the current status
        currentStatus.setMonkStatus(monkStatus);
        currentStatus.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
        
        return update(currentStatus);
    }
}

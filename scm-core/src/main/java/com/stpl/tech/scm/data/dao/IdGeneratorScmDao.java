/*
 * Created By Shanmu<PERSON>
 */

package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.data.mongo.IdGeneratorScm;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IdGeneratorScmDao extends MongoRepository<IdGeneratorScm, String> {

    public List<IdGeneratorScm> findByName(String name);
}
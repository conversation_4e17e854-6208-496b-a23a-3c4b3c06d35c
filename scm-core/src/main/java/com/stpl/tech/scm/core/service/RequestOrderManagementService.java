package com.stpl.tech.scm.core.service;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.core.exception.DayCloseInitiatedException;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.TransferOrderCreationException;
import com.stpl.tech.scm.data.model.ReferenceOrderData;
import com.stpl.tech.scm.data.model.RequestOrderData;
import com.stpl.tech.scm.domain.model.BudgetAuditActions;
import com.stpl.tech.scm.domain.model.GoodsReceived;
import com.stpl.tech.scm.domain.model.GoodsReceivedItem;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.NotificationType;
import com.stpl.tech.scm.domain.model.OrdersDetailsShort;
import com.stpl.tech.scm.domain.model.PlanOrderItem;
import com.stpl.tech.scm.domain.model.PlanOrderItemPrep;
import com.stpl.tech.scm.domain.model.ProductionPlanEvent;
import com.stpl.tech.scm.domain.model.ProductionPlanningSummary;
import com.stpl.tech.scm.domain.model.RegularOrderEvent;
import com.stpl.tech.scm.domain.model.RequestOrder;
import com.stpl.tech.scm.domain.model.RequestOrderItem;
import com.stpl.tech.scm.domain.model.RequestOrderResponse;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.TransferOrder;
import com.stpl.tech.scm.domain.model.UnitPlanItemRequest;
import com.stpl.tech.scm.domain.model.UnitWiseSummary;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by Rahul Singh on 13-06-2016.
 */
public interface RequestOrderManagementService {

    public List<RequestOrder> getRequestOrders(Integer fulfillingUnitId, Integer requestingUnitId, Date startDate, Date endDate, SCMOrderStatus status, Integer requestOrderId, String searchTag);

    public List<RequestOrder> getRequestOrdersFromReferenceOrder(int referenceOrderId);

    public RequestOrder getRequestOrder(int requestOrderId);

    public List<RequestOrder> createRequestOrdersFromReferenceOrder(ReferenceOrderData referenceOrderData, RegularOrderEvent orderEvent) throws InventoryUpdateException, SumoException;

    CellStyle getHeaderStyle(XSSFWorkbook workbook);

    public List<RequestOrder> getPendingRequestOrders(Integer unitId, Date fulfilmentDate);

    public List<OrdersDetailsShort> getPendingRoShort(Integer unitId, Date fulfilmentDate);

    public List<RequestOrderResponse> createRequestOrder(RequestOrder requestOrder) throws SumoException, TransferOrderCreationException, DayCloseInitiatedException;

    public List<RequestOrderResponse> createMultipleRequestOrder(RequestOrder requestOrder) throws SumoException;

    public List<RequestOrder> getPendingRequestOrders(String date, int fulfilmentUnit);

    public List<OrdersDetailsShort> getPendingRequestOrdersShort(String date, int fulfilmentUnit, boolean fetcgAcknowledged);

    public boolean acknowledgeOrders(List<RequestOrder> requestOrders, IdCodeName updatedBy);

    public Integer cancelRequestOrder(int requestOrderId, Integer updatedBy) throws SumoException;

    public List<RequestOrder> getSpecializedROForFulfillmentDate(Date fulfillmentDate, boolean isReceiving);

    public List<RequestOrder> getSpecializedROForNotification(NotificationType notificationType, Date notificationStartTime, Date notificationEndTime);

    public Map<Integer, List<RequestOrder>> getSpecialOrdersForDate(Date date);

    public RequestOrderData clubRequestOrders(List<RequestOrderData> requestOrderDataList, IdCodeName generationUnitId, IdCodeName generatedForUnitId,
                                              IdCodeName generatedBy, Date fulfilmentDate, Integer requestCompanyId, Integer fulfillmentCompanyId) throws SumoException;

	public ProductionPlanEvent startPlanning(Date fulfillmentDate, List<Integer> list, IdCodeName lastUpdatedBy, int unitId) throws Exception;

    public boolean updateCalculatedProductionItemsData(int eventId, List<PlanOrderItem> list) throws Exception;

    public PlanOrderItemPrep getPlanItemsForSemiFinishedProduct(RequestOrderItem item , Boolean isSkuRequired) throws SumoException, DataNotFoundException;

    public PlanOrderItemPrep submitPlanOrderItemPreparation(PlanOrderItemPrep item, int unitId) throws SumoException, DataNotFoundException;

    public List<PlanOrderItemPrep> getPlanOrderItemPreparations(Integer itemId, Integer unitId) throws SumoException, DataNotFoundException;

	public boolean acknowledgeEventOrders(int eventId);

	public List<ProductionPlanEvent> getPlansByFulfilmentDate(Date start, Date end, int fulfillmentUnit);

	public ProductionPlanEvent getPlanningEvent(int eventId);

    List<ProductionPlanEvent> getPlanningEventBulk(List<Integer> planIds);

    public List<RequestOrder> getRequestOrdersForInvoice(Integer sendingUnit, Integer receivingUnit);

    public Boolean updateTag(int roId, String tag);

	public void markRequestOrderNotified(List<Integer> requestOrdersNotified);

    public ProductionPlanningSummary getProductionPlanningSummary(String date, String region, Integer fulfillmentUnit);

    public Boolean excessPlanning(List<PlanOrderItem> list, Integer planId) throws IOException, SumoException;

    public List<UnitWiseSummary> getUnitWiseGntAndChaayos( String date, int fulfilmentUnit,ProductionPlanningSummary productionPlanningSummary, String region);

	public boolean isDifferentCompany(Integer requestOrderId);

    public List<TransferOrder> getTransferOrdersFromRequestOrders(List<Integer> clubRoIds , Map<Integer, Map<Integer, GoodsReceivedItem>> skuToPackagingMapping , Date fulfilmentDate , IdCodeName generationUnit , IdCodeName generatedBy ) throws SumoException;

    public List<UnitPlanItemRequest> getRoItemsByPlanId(Integer planId);

    public Boolean updateAdjustedQuantities(List<UnitPlanItemRequest> list, String updatedBy);

    public  List<RequestOrder> getRequestOrdersByIds(List<Integer> roIds);

    public Map<Integer , Integer> getProductionBookingProductSkuMap(Integer productId , Integer unitId);

    public Map<Integer, Pair<BigDecimal,BigDecimal>> getCurrentPriceAtFulfillingUnit(Map<Integer,Integer> productIdsToSkuSelectedMap, Integer fulfullingUnitId ,
                                                                                     Integer requestingUnitid , Boolean isFA) throws SumoException;
    public Boolean updateBudgetDetails(Integer requestUnitId , BigDecimal totalAmount , Integer id  , BudgetAuditActions actionType,
                                       String isAssetOrder , Integer generatedBy, Boolean isCancelled);

    public Map<Integer,BigDecimal> getLastWeekAverageQty(List<Integer> productIds , Integer unitId ,Boolean isSpecial);

    public Boolean autoAcknowledgeAndTransferSpecializedROs(List<Integer> roIds) throws TransferOrderCreationException,
            InventoryUpdateException, SumoException;

    public RequestOrderData createROFromGR(GoodsReceived goodsReceived , Integer vendorId) throws SumoException;


    public Map<Integer,Boolean> getProductDiscontinuedStatus(List<Integer> productIds, Integer fullfilmentUnitId, Date fulfilmentDate);
}

package com.stpl.tech.scm.core.util.model;

import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.domain.model.IdCodeName;

import java.math.BigDecimal;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by s<PERSON>khar on 22-10-2018.
 */
public class ConsumptionView extends IdCodeName {

    private String date;
    private BigDecimal qty;

    public ConsumptionView(Object id,Object date, Object qty, SCMCache scmCache) {
        this.id = (int) id;
        this.date = date.toString();
        this.qty = (BigDecimal) qty;
        this.name = scmCache.getSkuDefinition(this.id) != null
                        ? scmCache.getSkuDefinition(this.id).getSkuName() : "No Name";
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }
}

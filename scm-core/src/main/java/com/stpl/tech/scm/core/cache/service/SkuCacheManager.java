package com.stpl.tech.scm.core.cache.service;

import com.stpl.tech.scm.core.cache.base.BaseCacheManager;
import com.stpl.tech.scm.data.model.SkuDefinitionData;
import com.stpl.tech.scm.data.redis.service.CacheRefreshService;
import com.stpl.tech.scm.data.redis.service.RedisHashManager;
import com.stpl.tech.scm.data.redis.service.RedisHashMap;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class SkuCacheManager extends BaseCacheManager<Integer, SkuDefinition> {

    public SkuCacheManager(RedisHashManager redisHashManager,
                           CacheRefreshService cacheRefreshService,
                           @Qualifier("taskExecutor") ThreadPoolTaskExecutor taskExecutor) {
        super(redisHashManager, cacheRefreshService, taskExecutor);
    }

    @Override
    protected RedisHashMap<Integer, SkuDefinition> getRedisHash() {
        return redisHashManager.skuDefinition();
    }

    @Override
    protected Map<Integer, SkuDefinition> loadFromSource() {
        return cacheRefreshService.reloadSkuCache();
    }

    @Override
    protected Integer extractKey(SkuDefinition sku) {
        return sku.getSkuId();
    }


    @Getter
    @AllArgsConstructor
    public static class SkuWithProduct {
        private SkuDefinitionData skuData;
        private IdCodeName product;
    }


}

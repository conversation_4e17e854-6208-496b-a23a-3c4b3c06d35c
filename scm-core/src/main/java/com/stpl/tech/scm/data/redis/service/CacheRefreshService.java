package com.stpl.tech.scm.data.redis.service;

import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.VendorDetail;

import java.util.Map;

public interface CacheRefreshService {

    // Product Cache Methods
    Map<Integer, ProductDefinition> reloadProductCache();

    // Sku Cache Methods
    Map<Integer, SkuDefinition> reloadSkuCache();

    // Vendor Cache Methods
    Map<Integer, VendorDetail> reloadVendorCache();

}

package com.stpl.tech.scm.core.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import com.stpl.tech.scm.core.exception.SumoException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.service.CategoryManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.CategoryManagementDao;
import com.stpl.tech.scm.data.model.AttributeDefinitionData;
import com.stpl.tech.scm.data.model.AttributeValueData;
import com.stpl.tech.scm.data.model.CategoryAttributeMappingData;
import com.stpl.tech.scm.data.model.CategoryAttributeValueData;
import com.stpl.tech.scm.data.model.CategoryDefinitionData;
import com.stpl.tech.scm.domain.model.CategoryAttributeMapping;
import com.stpl.tech.scm.domain.model.CategoryAttributeValue;
import com.stpl.tech.scm.domain.model.CategoryDefinition;
import com.stpl.tech.scm.domain.model.SubCategoryDefinition;
import com.stpl.tech.scm.domain.model.SwitchStatus;

/**
 * Created by Rahul Singh on 11-05-2016.
 */

@Service
public class CategoryManagementServiceImpl implements CategoryManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(CategoryManagementServiceImpl.class);

    @Autowired
    private CategoryManagementDao categoryManagementDao;

    @Autowired
    private SCMCache scmCache;


    // Category Definition Resources


    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public CategoryDefinition viewCategory(int categoryId) {
        return scmCache.getCategoryDefinitions().get(categoryId);
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<CategoryDefinition> viewAllCategories() {
        List<CategoryDefinition> cd = new ArrayList<CategoryDefinition>(scmCache.getCategoryDefinitions().values());
        return cd;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addNewCategory(CategoryDefinition categoryDefinition) throws SumoException {
        CategoryDefinitionData categoryDefinitionData = categoryManagementDao.add(SCMDataConverter.convert(categoryDefinition),true);
        return updateCategoryDefinitionCache(categoryDefinitionData);
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateCategory(CategoryDefinition categoryDefinition) {
        CategoryDefinitionData categoryDefinitionData = categoryManagementDao.find(CategoryDefinitionData.class, categoryDefinition.getCategoryId());
        if (categoryDefinitionData != null) {
            categoryDefinitionData.setCategoryCode(categoryDefinition.getCategoryCode());
            categoryDefinitionData.setCategoryDescription(categoryDefinition.getCategoryDescription());
            categoryDefinitionData.setCategoryId(categoryDefinition.getCategoryId());
            categoryDefinitionData.setCategoryName(categoryDefinition.getCategoryName());
            categoryDefinitionData.setCategoryStatus(categoryDefinition.getCategoryStatus().value());
            categoryDefinitionData = categoryManagementDao.update(categoryDefinitionData,true);
            if (categoryDefinitionData != null) {
                return updateCategoryDefinitionCache(categoryDefinitionData);
            }
        }
        LOG.info("Category you are trying to update doesn't exist. Category productId: {}", categoryDefinition.getCategoryId());
        return false;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean deactivateCategory(int categoryId) {
        CategoryDefinitionData categoryDefinitionData = categoryManagementDao.find(CategoryDefinitionData.class, categoryId);
        if (categoryDefinitionData != null) {
            categoryDefinitionData.setCategoryStatus(SwitchStatus.IN_ACTIVE.value());
            categoryDefinitionData = (CategoryDefinitionData) categoryManagementDao.update(categoryDefinitionData,true);
            return updateCategoryDefinitionCache(categoryDefinitionData);
        }
        LOG.info("Category you are trying to deactivate doesn't exist. Category productId: {}", categoryId);
        return false;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean activateCategory(int categoryId) {
        CategoryDefinitionData categoryDefinitionData = categoryManagementDao.find(CategoryDefinitionData.class, categoryId);
        if (categoryDefinitionData != null) {
            categoryDefinitionData.setCategoryStatus(SwitchStatus.ACTIVE.value());
            categoryDefinitionData = categoryManagementDao.update(categoryDefinitionData,true);
            return updateCategoryDefinitionCache(categoryDefinitionData);
        }
        LOG.info("Category you are trying to deactivate doesn't exist. Category productId: {}", categoryId);
        return false;
    }


    // Category Attribute Mapping Resources


    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map<Integer, List<CategoryAttributeMapping>> getAllCategoryAttributeMappings() {
        Map<Integer, List<CategoryAttributeMapping>> categoryDefinitionListMap = new TreeMap<Integer, List<CategoryAttributeMapping>>();
        for (CategoryAttributeMapping cam : scmCache.getCategoryAttributeMappings().values()) {
            List<CategoryAttributeMapping> categoryAttributeMappings = categoryDefinitionListMap.get(cam.getCategoryDefinition().getId());
            if (categoryAttributeMappings == null) {
                categoryAttributeMappings = new ArrayList<CategoryAttributeMapping>();
            }
            if (!categoryAttributeMappings.contains(cam)) {
                categoryAttributeMappings.add(cam);
                categoryDefinitionListMap.put(cam.getCategoryDefinition().getId(), categoryAttributeMappings);
            }
        }
        return categoryDefinitionListMap;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CategoryAttributeMapping getCategoryAttributeMapping(int categoryAttributeMappingId) {
        CategoryAttributeMapping categoryAttributeMapping = scmCache.getCategoryAttributeMappings().get(categoryAttributeMappingId);
        if (categoryAttributeMapping == null) {
            LOG.info("No mapping found for category attribute mapping productId: {}", categoryAttributeMappingId);
        }
        return categoryAttributeMapping;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addCategoryAttributeMapping(List<CategoryAttributeMapping> categoryAttributeMappings) throws SumoException {
        List<CategoryAttributeMappingData> categoryAttributeMappingDataList = getCategoryAttributeMappingDataObject(categoryAttributeMappings);
        if (categoryAttributeMappingDataList != null) {
            for (CategoryAttributeMappingData categoryAttributeMappingData : categoryManagementDao.addCategoryAttributeMapping(categoryAttributeMappingDataList)) {
                updateCategoryAttributeMappingCache(categoryAttributeMappingData);
            }
            return true;
        }
        LOG.error("Error adding category attribute mappings.");
        return false;

    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateCategoryAttributeMapping(List<CategoryAttributeMapping> categoryAttributeMappings) {
        List<CategoryAttributeMappingData> categoryAttributeMappingDataList = new ArrayList<CategoryAttributeMappingData>();
        for (CategoryAttributeMapping categoryAttributeMapping : categoryAttributeMappings) {
            if (categoryAttributeMapping.getCategoryAttributeMappingId() != null) {
                CategoryAttributeMappingData categoryAttributeMappingData = categoryManagementDao.find(CategoryAttributeMappingData.class, categoryAttributeMapping.getCategoryAttributeMappingId());
                categoryAttributeMappingData.setCategoryAttributeMappingId(categoryAttributeMapping.getCategoryAttributeMappingId());
                categoryAttributeMappingData.setMappingOrder(categoryAttributeMapping.getMappingOrder());
                categoryAttributeMappingData.setIsUsedInNaming(SCMUtil.setStatus(categoryAttributeMapping.isUsedInNaming()));
                categoryAttributeMappingData.setIsMandatory(SCMUtil.setStatus(categoryAttributeMapping.isMandatory()));
                categoryAttributeMappingData.setAttributeDefinition(categoryManagementDao.find(AttributeDefinitionData.class, categoryAttributeMapping.getAttributeDefinition().getId()));
                categoryAttributeMappingData.setCategoryDefinition(categoryManagementDao.find(CategoryDefinitionData.class, categoryAttributeMapping.getCategoryDefinition().getId()));
                categoryAttributeMappingData.setMappingStatus(categoryAttributeMapping.getMappingStatus().value());
                categoryAttributeMappingData = categoryManagementDao.update(categoryAttributeMappingData,false);
                categoryAttributeMappingDataList.add(categoryAttributeMappingData);
            }
        }
        categoryManagementDao.flush();
        if (categoryAttributeMappingDataList != null) {
            for (CategoryAttributeMappingData categoryAttributeMappingData : categoryAttributeMappingDataList) {
                updateCategoryAttributeMappingCache(categoryAttributeMappingData);
            }
            return true;
        }
        LOG.error("Error updating category attribute mappings.");
        return false;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean deactivateCategoryAttributeMapping(int categoryAttributeMappingId) {
        CategoryAttributeMappingData categoryAttributeMappingData = categoryManagementDao.find(CategoryAttributeMappingData.class, categoryAttributeMappingId);
        if (categoryAttributeMappingData != null) {
            categoryAttributeMappingData.setMappingStatus(SwitchStatus.IN_ACTIVE.value());
            categoryAttributeMappingData = (CategoryAttributeMappingData) categoryManagementDao.update(categoryAttributeMappingData,true);
            return updateCategoryAttributeMappingCache(categoryAttributeMappingData);
        }
        LOG.info("Category attribute mapping productId: {} you are trying to deactivate doesn't exist!", categoryAttributeMappingId);
        return false;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean activateCategoryAttributeMapping(int categoryAttributeMappingId) {
        CategoryAttributeMappingData categoryAttributeMappingData = categoryManagementDao.find(CategoryAttributeMappingData.class, categoryAttributeMappingId);
        if (categoryAttributeMappingData != null) {
            categoryAttributeMappingData.setMappingStatus(SwitchStatus.ACTIVE.value());
            categoryAttributeMappingData = (CategoryAttributeMappingData) categoryManagementDao.update(categoryAttributeMappingData,true);
            return updateCategoryAttributeMappingCache(categoryAttributeMappingData);
        }
        LOG.info("Category attribute mapping productId: {} you are trying to activate doesn't exist!", categoryAttributeMappingId);
        return false;
    }


    // Category Attribute Value Resources


    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, List<CategoryAttributeValue>> getAllCategoryAttributeValues() {
        Map<Integer, List<CategoryAttributeValue>> categoryAttributeMappingListMap = new TreeMap<Integer, List<CategoryAttributeValue>>();
        for (CategoryAttributeValue categoryAttributeValue : scmCache.getCategoryAttributeValues().values()) {
            List<CategoryAttributeValue> categoryAttributeValues = categoryAttributeMappingListMap.get(categoryAttributeValue.getCategoryAttributeMappingId());
            if (categoryAttributeValues == null) {
                categoryAttributeValues = new ArrayList<CategoryAttributeValue>();
            }
            if (!categoryAttributeValues.contains(categoryAttributeValue)) {
                categoryAttributeValues.add(categoryAttributeValue);
                categoryAttributeMappingListMap.put(categoryAttributeValue.getCategoryAttributeMappingId(), categoryAttributeValues);
            }
        }
        return categoryAttributeMappingListMap;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CategoryAttributeValue getCategoryAttributeValue(int categoryAttributeValueId) {
        return scmCache.getCategoryAttributeValues().get(categoryAttributeValueId);
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addCategoryAttributeValue(List<CategoryAttributeValue> categoryAttributeValues) throws SumoException {
        List<CategoryAttributeValueData> categoryAttributeValueDataList = getCategoryAttributeValueDataObject(categoryAttributeValues);
        for (CategoryAttributeValueData categoryAttributeValueData : categoryManagementDao.addCategoryAttributeValue(categoryAttributeValueDataList)) {
            updateCategoryAttributeValueCache(categoryAttributeValueData);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateCategoryAttributeValue(List<CategoryAttributeValue> categoryAttributeValues) {
        List<CategoryAttributeValueData> categoryAttributeValueDataList = new ArrayList<CategoryAttributeValueData>();
        for (CategoryAttributeValue categoryAttributeValue : categoryAttributeValues) {
            CategoryAttributeValueData categoryAttributeValueData = categoryManagementDao.find(CategoryAttributeValueData.class, categoryAttributeValue.getCategoryAttributeValueId());
            if (categoryAttributeValueData != null) {
                categoryAttributeValueData.setAttributeValue(categoryManagementDao.find(AttributeValueData.class, categoryAttributeValue.getAttributeValue().getId()));
                categoryAttributeValueData.setCategoryAttributeMapping(categoryManagementDao.find(CategoryAttributeMappingData.class, categoryAttributeValue.getCategoryAttributeMappingId()));
                categoryAttributeValueData.setMappingStatus(categoryAttributeValue.getMappingStatus().value());
                categoryAttributeValueData.setCategoryAttributeValueId(categoryAttributeValue.getCategoryAttributeValueId());
                categoryAttributeValueData = categoryManagementDao.update(categoryAttributeValueData,true);
                categoryAttributeValueDataList.add(categoryAttributeValueData);
            } else {
                LOG.info("Category attribute value productId: you are trying to update doesn't exist!");
            }
        }
        for (CategoryAttributeValueData categoryAttributeValueData : categoryAttributeValueDataList) {
            updateCategoryAttributeValueCache(categoryAttributeValueData);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean deactivateCategoryAttributeValue(int categoryAttributeValueId) {
        CategoryAttributeValueData categoryAttributeValueData = categoryManagementDao.find(CategoryAttributeValueData.class, categoryAttributeValueId);
        if (categoryAttributeValueData != null) {
            categoryAttributeValueData.setMappingStatus(SwitchStatus.IN_ACTIVE.value());
            categoryAttributeValueData = categoryManagementDao.update(categoryAttributeValueData,true);
            if (categoryAttributeValueData != null) {
                return updateCategoryAttributeValueCache(categoryAttributeValueData);
            }
        }
        LOG.info("Category attribute value productId: {} you are trying to deactivate doesn't exist!", categoryAttributeValueId);
        return false;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean activateCategoryAttributeValue(int categoryAttributeValueId) {
        CategoryAttributeValueData categoryAttributeValueData = categoryManagementDao.find(CategoryAttributeValueData.class, categoryAttributeValueId);
        if (categoryAttributeValueData != null) {
            categoryAttributeValueData.setMappingStatus(SwitchStatus.ACTIVE.value());
            categoryAttributeValueData = categoryManagementDao.update(categoryAttributeValueData,true);
            if (categoryAttributeValueData != null) {
                return updateCategoryAttributeValueCache(categoryAttributeValueData);
            }
        }
        LOG.info("Category attribute value productId: {} you are trying to activate doesn't exist!", categoryAttributeValueId);
        return false;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, List<CategoryAttributeValue>> getAllCategoryAttributeValuesByCategory() {
        Map<Integer, List<CategoryAttributeValue>> categoryAttributeValuesByCategoryMap = new TreeMap<>();
        for (CategoryAttributeValue categoryAttributeValue : scmCache.getCategoryAttributeValues().values()) {
            CategoryAttributeMapping cam = scmCache.getCategoryAttributeMappings().get(categoryAttributeValue.getCategoryAttributeMappingId());
            List<CategoryAttributeValue> categoryAttributeValues = categoryAttributeValuesByCategoryMap.get(cam.getCategoryDefinition().getId());
            if (categoryAttributeValues == null) {
                categoryAttributeValues = new ArrayList<>();
            }
            if (!categoryAttributeValues.contains(categoryAttributeValue)) {
                categoryAttributeValues.add(categoryAttributeValue);
                categoryAttributeValuesByCategoryMap.put(cam.getCategoryDefinition().getId(), categoryAttributeValues);
            }
        }
        return categoryAttributeValuesByCategoryMap;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, SubCategoryDefinition> getAllSubCategoryDefinitions() {
        return scmCache.getSubCategoryDefinitions();
    }

    private List<CategoryAttributeMappingData> getCategoryAttributeMappingDataObject(List<CategoryAttributeMapping> categoryAttributeMappings) {
        List<CategoryAttributeMappingData> categoryAttributeMappingDataList = new ArrayList<CategoryAttributeMappingData>();
        for (CategoryAttributeMapping categoryAttributeMapping : categoryAttributeMappings) {
            CategoryAttributeMappingData categoryAttributeMappingData = new CategoryAttributeMappingData();
            categoryAttributeMappingData.setMappingStatus(categoryAttributeMapping.getMappingStatus().value());
            categoryAttributeMappingData.setCategoryDefinition(categoryManagementDao.find(CategoryDefinitionData.class, categoryAttributeMapping.getCategoryDefinition().getId()));
            categoryAttributeMappingData.setCategoryAttributeMappingId(categoryAttributeMapping.getCategoryAttributeMappingId());
            categoryAttributeMappingData.setAttributeDefinition(categoryManagementDao.find(AttributeDefinitionData.class, categoryAttributeMapping.getAttributeDefinition().getId()));
            categoryAttributeMappingData.setIsMandatory(SCMUtil.setStatus(categoryAttributeMapping.isMandatory()));
            categoryAttributeMappingData.setIsUsedInNaming(SCMUtil.setStatus(categoryAttributeMapping.isUsedInNaming()));
            categoryAttributeMappingData.setMappingOrder(categoryAttributeMapping.getMappingOrder());
            categoryAttributeMappingDataList.add(categoryAttributeMappingData);
        }
        return categoryAttributeMappingDataList;
    }

    private List<CategoryAttributeValueData> getCategoryAttributeValueDataObject(List<CategoryAttributeValue> categoryAttributeValues) {
        List<CategoryAttributeValueData> categoryAttributeValueDataList = new ArrayList<CategoryAttributeValueData>();
        for (CategoryAttributeValue categoryAttributeValue : categoryAttributeValues) {
            CategoryAttributeValueData categoryAttributeValueData = new CategoryAttributeValueData();
            categoryAttributeValueData.setCategoryAttributeMapping(categoryManagementDao.find(CategoryAttributeMappingData.class, categoryAttributeValue.getCategoryAttributeMappingId()));
            categoryAttributeValueData.setMappingStatus(categoryAttributeValue.getMappingStatus().value());
            categoryAttributeValueData.setCategoryAttributeValueId(categoryAttributeValue.getCategoryAttributeValueId());
            categoryAttributeValueData.setAttributeValue(categoryManagementDao.find(AttributeValueData.class, categoryAttributeValue.getAttributeValue().getId()));
            categoryAttributeValueDataList.add(categoryAttributeValueData);
        }
        return categoryAttributeValueDataList;
    }

    private boolean updateCategoryDefinitionCache(CategoryDefinitionData categoryDefinitionData) {
        if (categoryDefinitionData != null) {
            scmCache.getCategoryDefinitions().put(categoryDefinitionData.getCategoryId(), SCMDataConverter.convert(categoryDefinitionData));
            return true;
        }
        return false;
    }

    private boolean updateCategoryAttributeMappingCache(CategoryAttributeMappingData categoryAttributeMappingData) {
        if (categoryAttributeMappingData != null) {
            scmCache.getCategoryAttributeMappings().put(categoryAttributeMappingData.getCategoryAttributeMappingId(), SCMDataConverter.convert(categoryAttributeMappingData));
            return true;
        }
        return false;
    }

    private boolean updateCategoryAttributeValueCache(CategoryAttributeValueData categoryAttributeValueData) {
        if (categoryAttributeValueData != null) {
            scmCache.getCategoryAttributeValues().put(categoryAttributeValueData.getCategoryAttributeValueId(), SCMDataConverter.convert(categoryAttributeValueData));
            return true;
        }
        return false;
    }
}

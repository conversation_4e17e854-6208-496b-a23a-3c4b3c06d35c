/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;


import javax.persistence.*;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;


@SuppressWarnings("serial")
@Entity
@Table(name = "ENTITY_ATTRIBUTE_VALUE_MAPPING",
        uniqueConstraints = @UniqueConstraint(
                columnNames = {
                        "PROFILE_ID",
                        "ATTRIBUTE_ID",
                        "PROFILE_ATTRIBUTE_MAPPING_ID",
                        "ENTITY_TYPE"
                }))
public class EntityAttributeValueMappingData implements java.io.Serializable {

    private Integer entityAttributeValueMappingId;
    private Integer profileId;
    private Integer attributeId;
    private Integer attributeValueId;
    private Integer profileAttributeMappingId;
    private String entityType;
    private Integer entityId;
    private Date creationDate;
    private int createdBy;
    private String status;

    public EntityAttributeValueMappingData() {
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ENTITY_ATTRIBUTE_VALUE_MAPPING_ID", unique = true, nullable = false)
    public Integer getEntityAttributeValueMappingId() {
        return entityAttributeValueMappingId;
    }

    public void setEntityAttributeValueMappingId(Integer entityAttributeValueMappingId) {
        this.entityAttributeValueMappingId = entityAttributeValueMappingId;
    }

    @Column(name = "PROFILE_ID", nullable = false)
    public Integer getProfileId() {
        return profileId;
    }

    public void setProfileId(Integer profileId) {
        this.profileId = profileId;
    }

    @Column(name = "ATTRIBUTE_ID", nullable = false)
    public Integer getAttributeId() {
        return attributeId;
    }

    public void setAttributeId(Integer attributeId) {
        this.attributeId = attributeId;
    }

    @Column(name = "ATTRIBUTE_VALUE_ID", nullable = false)
    public Integer getAttributeValueId() {
        return attributeValueId;
    }

    public void setAttributeValueId(Integer attributeValueId) {
        this.attributeValueId = attributeValueId;
    }

    @Column(name = "PROFILE_ATTRIBUTE_MAPPING_ID", nullable = false)
    public Integer getProfileAttributeMappingId() {
        return profileAttributeMappingId;
    }

    public void setProfileAttributeMappingId(Integer profileAttributeMappingId) {
        this.profileAttributeMappingId = profileAttributeMappingId;
    }

    @Column(name = "ENTITY_TYPE", nullable = false)
    public String getEntityType() {
        return entityType;
    }

    public void setEntityType(String entityType) {
        this.entityType = entityType;
    }

    @Column(name = "ENTITY_ID", nullable = false)
    public Integer getEntityId() {
        return entityId;
    }

    public void setEntityId(Integer entityId) {
        this.entityId = entityId;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_DATE", length = 19)
    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    @Column(name = "CREATED_BY", nullable = false)
    public int getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(int createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}

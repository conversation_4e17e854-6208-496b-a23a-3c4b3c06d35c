package com.stpl.tech.scm.data.dao;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import com.stpl.tech.scm.core.exception.SumoException;
import org.springframework.web.servlet.View;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.recipe.model.RecipeCostData;
import com.stpl.tech.scm.data.model.CostDetailData;
import com.stpl.tech.scm.domain.model.PriceData;
import com.stpl.tech.scm.domain.model.PriceUpdateEntry;
import com.stpl.tech.scm.domain.model.PriceUpdateEvent;
import com.stpl.tech.scm.domain.model.PriceUpdateEventStatus;
import com.stpl.tech.scm.domain.model.ProductPriceData;

public interface ProductPriceManagementDao extends SCMAbstractDao {

	public List<PriceUpdateEvent> getSkuPriceUpdateEvents();

	public Integer addSkuPriceUpdateEvent(PriceUpdateEvent event) throws DataUpdationException, SumoException;

	public boolean updateSkuPriceEvent(int eventId, int userId, String username, PriceUpdateEventStatus status) throws SumoException;

	public View downloadSkuPriceUpdateFile();

	public void approveProductPrice(int eventId) throws IOException, SumoException;

	public Integer updateSkuPriceEvent(PriceUpdateEvent event, Integer userId, String userName) throws SumoException;

	public boolean callProductPriceUpdate(int eventId);

	public PriceUpdateEvent getSkuPriceUpdateEventData(int eventId);

	public void addPriceUpdateEntries(List<PriceUpdateEntry> recipeEntries, PriceUpdateEvent updatedEvent);

	/**
	 * @param id
	 * @return
	 */
	public PriceUpdateEvent getPriceUpdateEvent(int id);

	public void failEvent(Integer updatedEventId);

	public Map<Integer, PriceData> getCurrentProductPrice(int unitId, List<Integer> products);

	void sendCostDataToKettle(List<RecipeCostData> costData) throws IOException;

	public Map<Integer, Map<Integer, ProductPriceData>> getAllCurrentProductPriceMap();

	public void saveRecipeCosts(List<RecipeCostData> recipeCostList) throws SumoException;

	public List<CostDetailData> fetchUnitInventroy(int unitId);

	public List<CostDetailData> getCostDetailForIds(int unitId, List<Integer> ids);

	/**
	 * @param unitId
	 * @param triggerId
	 * @param trigger
	 * @param keyType
	 * @param prices
	 * @param updatePrice
	 *//*
	void updateCurrentPriceData(int unitId, int triggerId, String trigger, String keyType, Collection<PriceData> prices,
			boolean updatePrice);*/


}

package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Version;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 12-05-2017.
 */
@Entity
@Table(name = "VENDOR_GOODS_RECEIVED_DATA")
public class VendorGoodsReceivedData {

    private Integer goodsReceivedId;
    private Integer deliveryUnitId;
    private Integer generatedForVendor;
    private Integer dispatchId;
    private Integer approvedBy;
    private Integer updatedBy;
    private Integer createdBy;
    private String docType;
    private String docNumber;
    private BigDecimal totalAmount;
    private BigDecimal totalTaxes;
    private BigDecimal totalPrice;
    private BigDecimal extraCharges;
    private Date updatedAt;
    private Date createdAt;
    private String grStatus;
    private String creationType;
    private DocumentDetailData grDocument;
    private String paymentStatus;
    private PaymentRequestData paymentRequestData;
    private List<VendorGoodsReceivedItemData> grItemList = new ArrayList<>(0);
    private List<PurchaseOrderVendorGRMappingData> poMappingList = new ArrayList<>(0);

    private Integer closureEventId;
    private String amountMatched;
    private Date documentDate;
    private String invalidGR = "N";
    private Integer companyId;
    private String toBePaid = "Y";
    private String vendorGRType;
    private BigDecimal extraGrAmount;
    private String type;
    private String comment;
    private String vendorInvoiceDocId;
    private Integer rowVersion;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "GOODS_RECEIVED_ID", nullable = false, unique = true)
    public Integer getGoodsReceivedId() {
        return goodsReceivedId;
    }

    public void setGoodsReceivedId(Integer goodsReceivedId) {
        this.goodsReceivedId = goodsReceivedId;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "vendorGoodsReceivedData")
    public List<PurchaseOrderVendorGRMappingData> getPoMappingList() {
        return poMappingList;
    }


    public void setPoMappingList(List<PurchaseOrderVendorGRMappingData> poMappingList) {
        this.poMappingList = poMappingList;
    }


    @OneToMany(fetch = FetchType.LAZY, mappedBy = "goodsReceivedData")
    public List<VendorGoodsReceivedItemData> getGrItemList() {
        return grItemList;
    }

    public void setGrItemList(List<VendorGoodsReceivedItemData> grItemList) {
        this.grItemList = grItemList;
    }

    @Column(name = "GOODS_RECEIVED_STATUS", nullable = false)
    public String getGrStatus() {
        return grStatus;
    }

    public void setGrStatus(String grStatus) {
        this.grStatus = grStatus;
    }

    @Column(name = "UPDATED_BY", nullable = true)
    public Integer getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Column(name = "CREATED_BY", nullable = false)
    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "UPDATED_AT", nullable = true)
    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Column(name = "CREATED_AT", nullable = false)
    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @Column(name = "DELIVERY_UNIT_ID", nullable = false)
    public Integer getDeliveryUnitId() {
        return deliveryUnitId;
    }

    public void setDeliveryUnitId(Integer deliveryUnitId) {
        this.deliveryUnitId = deliveryUnitId;
    }

    @Column(name = "VENDOR_ID", nullable = false)
    public Integer getGeneratedForVendor() {
        return generatedForVendor;
    }

    public void setGeneratedForVendor(Integer generatedForVendor) {
        this.generatedForVendor = generatedForVendor;
    }

    @Column(name = "DISPATCH_ID", nullable = false)
    public Integer getDispatchId() {
        return dispatchId;
    }

    public void setDispatchId(Integer dispatchId) {
        this.dispatchId = dispatchId;
    }

    @Column(name = "DOCUMENT_UPLOADED", nullable = false)
    public String getDocType() {
        return docType;
    }

    public void setDocType(String docType) {
        this.docType = docType;
    }

    @Column(name = "DOCUMENT_NUMBER", nullable = false)
    public String getDocNumber() {
        return docNumber;
    }

    public void setDocNumber(String docNumber) {
        this.docNumber = docNumber;
    }

    @Column(name = "DOCUMENT_DATE", nullable = false)
    public Date getDocumentDate() {
        return this.documentDate;
    }

    public void setDocumentDate(Date date) {
        this.documentDate = date;
    }

    @Column(name = "TOTAL_AMOUNT", nullable = false)
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    @Column(name = "TOTAL_TAX", nullable = false)
    public BigDecimal getTotalTaxes() {
        return totalTaxes;
    }

    public void setTotalTaxes(BigDecimal totalTaxes) {
        this.totalTaxes = totalTaxes;
    }

    @Column(name = "TOTAL_PRICE", nullable = false)
    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    @Column(name = "EXTRA_CHARGES", nullable = true)
    public BigDecimal getExtraCharges() {
        return extraCharges;
    }

    public void setExtraCharges(BigDecimal extraCharges) {
        this.extraCharges = extraCharges;
    }

    @Column(name = "CREATION_TYPE", nullable = false)
    public String getCreationType() {
        return creationType;
    }

    public void setCreationType(String creationType) {
        this.creationType = creationType;
    }


    @Column(name="CLOSURE_EVENT_ID",nullable = true)
    public Integer getClosureEventId() {
        return closureEventId;
    }

    public void setClosureEventId(Integer closureEventId) {
        this.closureEventId = closureEventId;
    }

    @OneToOne
    @JoinColumn(name="GR_DOCUMENT_ID",nullable = true)
    public DocumentDetailData getGrDocument() {
        return grDocument;
    }

    public void setGrDocument(DocumentDetailData grDocument) {
        this.grDocument = grDocument;
    }

    @Column(name="AMOUNT_MATCHED",nullable = true)
    public String getAmountMatched() {
        return amountMatched;
    }

    public void setAmountMatched(String amountMatched) {
        this.amountMatched = amountMatched;
    }

    @Column(name="INVALID_GR",nullable = true)
    public String getInvalidGR() {
        return this.invalidGR;
    }

    public void setInvalidGR(String invalidGR) {
        this.invalidGR = invalidGR;
    }

    @Column(name = "PAYMENT_STATUS", length = 100)
    public String getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(String paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PAYMENT_REQUEST_ID")
    public PaymentRequestData getPaymentRequestData() {
        return paymentRequestData;
    }

    public void setPaymentRequestData(PaymentRequestData paymentRequestData) {
        this.paymentRequestData = paymentRequestData;
    }

    @Column(name = "COMPANY_ID")
  	public Integer getCompanyId() {
  		return companyId;
  	}

  	public void setCompanyId(Integer companyId) {
  		this.companyId = companyId;
  	}

    @Column(name = "TO_BE_PAID", length = 1, nullable = false)
    public String getToBePaid() {
        return toBePaid;
    }

    public void setToBePaid(String toBePaid) {
        this.toBePaid = toBePaid;
    }

    @Column(name = "VENDOR_GR_TYPE", nullable = false)
    public String getVendorGRType() {
        return vendorGRType;
    }

    public void setVendorGRType(String vendorGRType) {
        this.vendorGRType = vendorGRType;
    }

    @Column(name = "APPROVED_BY", nullable = true)
    public Integer getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(Integer approvedBy) {
        this.approvedBy = approvedBy;
    }

    @Column(name = "EXTRA_GR_AMOUNT", nullable = true)
    public BigDecimal getExtraGrAmount() {
        return extraGrAmount;
    }

    public void setExtraGrAmount(BigDecimal extraGrAmount) {
        this.extraGrAmount = extraGrAmount;
    }

    @Column(name = "TYPE", nullable = true)
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Column(name = "COMMENT", nullable = true)
    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    @Column(name = "VENDOR_INVOICE_DOC_ID", nullable = true)
    public String getVendorInvoiceDocId() {
        return vendorInvoiceDocId;
    }

    public void setVendorInvoiceDocId(String vendorInvoiceDocId) {
        this.vendorInvoiceDocId = vendorInvoiceDocId;
    }

    @Version
    @Column(name="ROW_VERSION")
    public Integer getRowVersion() {
        return rowVersion;
    }

    public void setRowVersion(Integer rowVersion) {
        this.rowVersion = rowVersion;
    }
}


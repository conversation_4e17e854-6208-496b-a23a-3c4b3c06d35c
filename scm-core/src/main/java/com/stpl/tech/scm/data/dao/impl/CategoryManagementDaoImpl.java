package com.stpl.tech.scm.data.dao.impl;

import java.util.ArrayList;
import java.util.List;

import com.stpl.tech.scm.core.exception.SumoException;
import org.springframework.stereotype.Repository;

import com.stpl.tech.scm.data.dao.CategoryManagementDao;
import com.stpl.tech.scm.data.model.CategoryAttributeMappingData;
import com.stpl.tech.scm.data.model.CategoryAttributeValueData;

/**
 * Created by Chaayos on 11-05-2016.
 */

@Repository
public class CategoryManagementDaoImpl extends SCMAbstractDaoImpl implements CategoryManagementDao {

    @Override
    public List<CategoryAttributeMappingData> addCategoryAttributeMapping(List<CategoryAttributeMappingData> categoryAttributeMappingDataList) throws SumoException {
        List<CategoryAttributeMappingData> categoryAttributeMappingDatas = new ArrayList<CategoryAttributeMappingData>();
        for (CategoryAttributeMappingData camd : categoryAttributeMappingDataList) {
            categoryAttributeMappingDatas.add(add(camd,false));
        }
        flush();
        return categoryAttributeMappingDatas;
    }

    @Override
    public List<CategoryAttributeValueData> addCategoryAttributeValue(List<CategoryAttributeValueData> categoryAttributeValueDataList) throws SumoException {
        List<CategoryAttributeValueData> categoryAttributeValueDatas = new ArrayList<CategoryAttributeValueData>();
        for (CategoryAttributeValueData categoryAttributeValueData : categoryAttributeValueDataList) {
            categoryAttributeValueDatas.add(add(categoryAttributeValueData,false));
        }
        flush();
        return categoryAttributeValueDatas;
    }
}

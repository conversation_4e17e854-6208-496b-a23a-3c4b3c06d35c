package com.stpl.tech.scm.notification.email.template;

import java.util.HashMap;
import java.util.Map;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

/**
 * Created by Chaayos on 05-09-2016.
 */
public class VendorEmailNotificationFailureTemplate extends AbstractVelocityTemplate {

    private String basePath;
    private boolean isReceiving;
    private VendorDetail vendorDetail;
    private String e;

    public VendorEmailNotificationFailureTemplate(){

    }

    public VendorEmailNotificationFailureTemplate(VendorDetail vendorDetail, String basePath, boolean isReceiving, String e){
        this.vendorDetail = vendorDetail;
        this.basePath = basePath;
        this.isReceiving = isReceiving;
        this.e = e;
    }

    @Override
    public String getTemplatePath() {
        return "templates/VendorEmailFailureTemplate.html";
    }

    @Override
    public String getFilepath() {
        if(isReceiving){
            return basePath + "/vendor/failure/receiving/"+ vendorDetail.getVendorId()+ "/" +SCMUtil.getDateString(SCMUtil.getCurrentDateIST())+".html";
        }else{
            return basePath + "/vendor/failure/request/"+ vendorDetail.getVendorId()+ "/" +SCMUtil.getDateString(SCMUtil.getCurrentDateIST())+".html";
        }

    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("vendorDetail",vendorDetail);
        stringObjectMap.put("isReceiving", isReceiving);
        stringObjectMap.put("exception", e);
        return stringObjectMap;
    }

    public VendorDetail getVendorDetail() {
        return vendorDetail;
    }

    public boolean isReceiving() {
        return isReceiving;
    }
}

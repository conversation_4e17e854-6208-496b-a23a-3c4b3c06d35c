package com.stpl.tech.scm.notification.email;


import com.stpl.tech.scm.notification.email.template.SwitchAssetEmailTemplate;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.ArrayList;

public class SwitchAssetEmailNotification extends EmailNotification {

    SwitchAssetEmailTemplate switchAssetEmailTemplate;

    EnvType envType;
    ArrayList<String> destinationMails;

    SwitchAssetEmailNotification(){

    }
    public SwitchAssetEmailNotification(SwitchAssetEmailTemplate switchAssetEmailTemplate, EnvType envType, ArrayList<String> destinationMails) {
        this.switchAssetEmailTemplate =switchAssetEmailTemplate;
        this.envType = envType;
        this.destinationMails = destinationMails;
    }

    @Override
    public String[] getToEmails() {
        if(envType==EnvType.PROD || envType == EnvType.SPROD){
          return  destinationMails.toArray(new String[0]);
        }else{
            String[] emails = new String[1];
            emails[0]="<EMAIL>";
            return emails;
        }

    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        if(envType==EnvType.PROD || envType==EnvType.SPROD) {
            return "Switch Asset Information" + AppUtils.getCurrentDateISTFormatted();
        }else{
            return "[DEV] Switch Asset Information " + AppUtils.getCurrentDateISTFormatted();
        }
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return switchAssetEmailTemplate.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}

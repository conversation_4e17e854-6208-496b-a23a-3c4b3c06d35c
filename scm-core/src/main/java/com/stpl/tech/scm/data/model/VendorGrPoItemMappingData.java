package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 29-06-2017.
 */
@Entity
@Table(name = "VENDOR_GR_PO_ITEM_MAPPING_DATA")
public class VendorGrPoItemMappingData {

    private Integer id;
    private VendorGoodsReceivedItemData grItem;
    private PurchaseOrderItemData poItem;
    private BigDecimal packagingQty;
    private BigDecimal acceptedQty;
    private Date lastUpdateTime;
    private Integer lastUpdatedBy;
    private String updationreason;
    private String description;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MAPPING_ID", nullable = false, unique = true)
    public Integer getId() {
        return id;
    }

    public void setId(Integer mapping) {
        this.id = mapping;
    }

    @ManyToOne(targetEntity = PurchaseOrderItemData.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "PURCHASE_ORDER_ITEM_ID", nullable = false)
    public PurchaseOrderItemData getPoItem() {
        return poItem;
    }

    public void setPoItem(PurchaseOrderItemData itemData) {
        this.poItem = itemData;
    }

    @ManyToOne(targetEntity = VendorGoodsReceivedItemData.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "VENDOR_GOODS_RECEIVED_ITEM_ID", nullable = false)
    public VendorGoodsReceivedItemData getGrItem() {
        return grItem;
    }

    public void setGrItem(VendorGoodsReceivedItemData itemData) {
        this.grItem = itemData;
    }

    @Column(name = "PACKAGING_QUANTITY", nullable = false)
    public BigDecimal getPackagingQty() {
        return packagingQty;
    }

    public void setPackagingQty(BigDecimal packagingQty) {
        this.packagingQty = packagingQty;
    }

    @Column(name = "ACCEPTED_QUANTITY", nullable = false)
    public BigDecimal getAcceptedQty() {
        return acceptedQty;
    }

    public void setAcceptedQty(BigDecimal acceptedQty) {
        this.acceptedQty = acceptedQty;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "UPDATED_DATE", nullable = false)
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    @Column(name = "LAST_UPDATED_BY", nullable = false)
    public Integer getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(Integer lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }


    @Column(name = "UPDATION_REASON", nullable = true)
    public String getUpdationreason() {
        return updationreason;
    }

    public void setUpdationreason(String updationreason) {
        this.updationreason = updationreason;
    }

    @Column(name = "DESCRIPTION", nullable = true)
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}


package com.stpl.tech.scm.core.service.impl;


import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.external.notification.service.SMSClientProviderService;
import com.stpl.tech.master.core.notification.sms.SMSType;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.OtpType;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.GoodsReceiveManagementService;
import com.stpl.tech.scm.core.service.SCMAssetManagementService;
import com.stpl.tech.scm.core.service.SwitchAssetService;
import com.stpl.tech.scm.core.service.TransferOrderManagementService;
import com.stpl.tech.scm.core.util.MultiPartFileHelper;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.SCMAssetManagementDao;
import com.stpl.tech.scm.data.dao.SwitchAssetDataDao;
import com.stpl.tech.scm.data.model.AssetDefinitionData;
import com.stpl.tech.scm.data.model.DocumentDetailData;
import com.stpl.tech.scm.data.model.GoodsReceivedData;
import com.stpl.tech.scm.data.model.SwitchAssetData;
import com.stpl.tech.scm.data.transport.model.SwitchAssetBody;
import com.stpl.tech.scm.data.transport.model.SwitchAssetEventData;
import com.stpl.tech.scm.domain.model.AssetDefinition;
import com.stpl.tech.scm.domain.model.AssetStatusType;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.GoodsReceived;
import com.stpl.tech.scm.domain.model.GoodsReceivedItem;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.SCMOrderPackaging;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.SwitchAsset;
import com.stpl.tech.scm.domain.model.TransferOrder;
import com.stpl.tech.scm.domain.model.TransferOrderType;
import com.stpl.tech.scm.notification.email.SwitchAssetEmailNotification;
import com.stpl.tech.scm.notification.email.template.SwitchAssetEmailTemplate;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppUtils;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.Path;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;


@Service
@Log4j2
@Configuration
public class SwitchAssetServiceImpl implements SwitchAssetService {

    @Autowired
    private FileArchiveService fileArchiveService;
    @Autowired
    private EnvProperties envProperties;
    @Autowired
    private MasterDataCache masterDataCache;
    @Autowired
    private SCMCache scmCache;

    @Autowired
    private NotificationService notificationService;
    @Autowired
    private SMSClientProviderService providerService;

    @Autowired
    private SCMAssetManagementDao scmAssetManagementDao;

    @Autowired
    private SCMAssetManagementService scmAssetManagementService;

    @Autowired
    private TransferOrderManagementService transferOrderManagementService;
    @Autowired
    private GoodsReceiveManagementService goodsReceiveManagementService;
    @Autowired
    private SwitchAssetDataDao switchAssetDataDao;

    @Value("${switch.asset.doc.dir}")
    String docBasePath;

    private static final ExecutorService taskExecutor = Executors.newFixedThreadPool(1);
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Integer uploadAssetImage(FileType type, MimeType mimeType, DocUploadType docType, MultipartFile file, Integer userId, Boolean isNewAsset) throws SumoException {
        try {
            String fileName = MultiPartFileHelper.getSwitchAssetUploadFileName(type, userId, mimeType, isNewAsset);
            FileDetail fileDetail = fileArchiveService.saveFileToS3(envProperties.getS3ProductBucket(), "scm-service/excess_asset_image", fileName, file);

            DocumentDetail documentDetail = new DocumentDetail();
            documentDetail.setMimeType(mimeType);
            documentDetail.setUploadType(docType);
            documentDetail.setFileType(type);
            documentDetail.setDocumentLink(fileName);
            documentDetail.setS3Key(fileDetail.getKey());
            documentDetail.setS3Bucket(fileDetail.getBucket());
            documentDetail.setFileUrl(envProperties.getAssetImageUrl()+fileDetail.getKey().split("/")[2]);
            documentDetail.setUpdatedBy(SCMUtil.generateIdCodeName(userId, "", masterDataCache.getEmployee(userId)));
            documentDetail.setUpdateTime(SCMUtil.getCurrentTimestamp());
            DocumentDetailData fileData = scmAssetManagementDao.add(SCMDataConverter.convert(documentDetail), true);
            return fileData.getDocumentId();
        } catch (Exception e) {
            log.error("################## Error in [uploadAssetImage] ################ , {}" + e.getMessage());
            log.error(e.getStackTrace());
            throw new SumoException("SWITCH_EXCEPTION", "Switch Asset Image Document Upload Failed");
        }
    }

    @Override
    public SwitchAsset getValidAssetFromUnitAndTag(Integer unitId, String tagValue) throws SumoException {
        try {
            AssetDefinitionData asset = scmAssetManagementDao.getAssetFromUnitAndTagValue(unitId, tagValue);
            if (asset == null) {
                throw new SumoException("SWITCH_EXCEPTION","Asset Not Found With Asset Tag : " + tagValue);
            }
            boolean isValid = checkForTransferableAsset(asset);
            if(isValid){
                AssetDefinition assetDefinition = scmAssetManagementService.convertAssetDefinitionDataToAssetDefinition(asset, true);
                SkuDefinition skuDefinition = scmCache.getSkuDefinition(assetDefinition.getSkuId());
                ProductDefinition productDefinition = scmCache.getProductDefinition(assetDefinition.getProductId());
                return SwitchAsset.builder().assetId(assetDefinition.getAssetId())
                        .assetTag(assetDefinition.getTagValue())
                        .assetName(assetDefinition.getAssetName())
                        .subCategoryId(assetDefinition.getSubCategoryDefinition().getId())
                        .subcategoryName(assetDefinition.getSubCategoryDefinition().getName())
                        .skuId(assetDefinition.getSkuId())
                        .skuName(skuDefinition.getSkuName())
                        .productId(productDefinition.getProductId())
                        .productName(productDefinition.getProductName()).build();
            }else {
                throw new SumoException("SWITCH_EXCEPTION","Asset is not valid for transfer");
            }
        } catch (SumoException e) {
            throw e;
        } catch (Exception e) {
            log.error("############### Error in [getValidAssetFromUnitAndTag] ################, {} " + e.getMessage());
            log.error(e.getStackTrace());
            throw new SumoException("SWITCH_EXCEPTION", "Error while getting asset from unit id and tag value");
        }

    }

    private Boolean checkForTransferableAsset(AssetDefinitionData asset){
        Boolean notInTransit = Objects.isNull(asset.getIsInTransit()) ||
                asset.getIsInTransit().equalsIgnoreCase(SCMUtil.NO);
        if (notInTransit &&
                (asset.getAssetStatus().equals(AssetStatusType.BROKEN.value())
                        || asset.getAssetStatus().equals(AssetStatusType.CREATED.value())
                        || asset.getAssetStatus().equals(AssetStatusType.IN_USE.value())
                        || asset.getAssetStatus().equals(AssetStatusType.READY_FOR_USE.value())
                        || asset.getAssetStatus().equals(AssetStatusType.IN_RENOVATION.value())
                        || asset.getAssetStatus().equals(AssetStatusType.IN_REPAIR.value()))
                || asset.getAssetStatus().equals(AssetStatusType.LOST_ADJUSTED.value()) && (asset.getPrice().compareTo(BigDecimal.ZERO) > 0)) {
                return true;
        }
        return false;
    }

    @Override
    public Boolean sendOtp(Integer userId) throws SumoException {
        String contactNumber = masterDataCache.getEmployeeBasicDetail(userId).getContactNumber();
        try {
            String otp = notificationService.getOTPMapperInstance().generateOTP(false, OtpType.SWITCH_ASSET, contactNumber, envProperties.getEnvType());
            log.info(otp);
            String message = String.format("OTP for Switch Asset Process : %s, for User Id : %s   - Chaayos -CHAAYOS" , otp, userId);
            boolean sentOtp = notificationService.sendNotification("SWITCH_ASSET_HANDSHAKE", message, contactNumber, providerService.getSMSClient(SMSType.OTP, ApplicationName.SCM_SERVICE), true, null);
            return sentOtp;
        } catch (Exception e) {
            log.error("##### Error while sending otp ##########, {}", e.getMessage());
            e.printStackTrace();
            throw new SumoException("SWITCH_EXCEPTION", e.getMessage());
        }

    }

    public Boolean verifyOtp(String otp, String contactNumber) throws SumoException {
        if (!StringUtils.isEmpty(contactNumber) && !StringUtils.isEmpty(otp)) {
            boolean result = otp
                    .equals(notificationService.getOTPMapperInstance().getOTP(OtpType.SWITCH_ASSET, contactNumber));
            if (result) {
                notificationService.getOTPMapperInstance().removeOTP(OtpType.SWITCH_ASSET, contactNumber);
                return true;
            }
            throw new SumoException("SWITCH_EXCEPTION","Otp Verification  Failed, Please enter valid opt.");
        } else {
            throw new SumoException("SWITCH_EXCEPTION","Contact number or otp is empty !");
        }
    }
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean startProcess(SwitchAssetBody switchAssetBody) throws SumoException {

        try {
            String contactNumber = masterDataCache.getEmployeeBasicDetail(switchAssetBody.getRequestedBy()).getContactNumber();
            verifyOtp(switchAssetBody.getOtpValue(),contactNumber);

            Path fileStorageLocation = Paths.get(docBasePath).normalize();
            if(!Files.isDirectory(fileStorageLocation)) {Files.createDirectories(fileStorageLocation);}

            AssetDefinitionData newAssetDefinitionData = scmAssetManagementDao.getAssetDefinitionDataByAssetId(switchAssetBody.getNewAssetId());
            AssetDefinitionData oldAssetDefinitionData = scmAssetManagementDao.getAssetDefinitionDataByAssetId(switchAssetBody.getOldAssetId());

            if (!Objects.equals(newAssetDefinitionData.getProduct().getProductId(), oldAssetDefinitionData.getProduct().getProductId())) {
                throw new SumoException("SWITCH_EXCEPTION","Switch Assets product mismatch");
            }
            if(!Objects.equals(newAssetDefinitionData.getUnitId(), switchAssetBody.getCreatedByUnit())){
                throw new SumoException("SWITCH_EXCEPTION","New Asset is not present in unit id :"+switchAssetBody.getCreatedByUnit());
            }
            if(!Objects.equals(oldAssetDefinitionData.getUnitId(), switchAssetBody.getRequestingUnit())){
                throw new SumoException("SWITCH_EXCEPTION","Old Asset is not present in unit id :"+switchAssetBody.getCreatedByUnit());
            }
            boolean isNewAssetValid = checkForTransferableAsset(newAssetDefinitionData);
            if(!isNewAssetValid){
                throw new SumoException("SWITCH_EXCEPTION","New Asset is not valid for transfer, assetId :"+switchAssetBody.getNewAssetId());
            }
            boolean isOldAssetValid = checkForTransferableAsset(oldAssetDefinitionData);
            if(!isOldAssetValid){
                throw new SumoException("SWITCH_EXCEPTION","Old Asset is not valid for transfer, assetId :"+switchAssetBody.getOldAssetId());
            }

            // Auto TO and GR for newAsset
            List<AssetDefinitionData> newAsset = new ArrayList<>();
            newAsset.add(newAssetDefinitionData);
            Integer newAssetToId = scmAssetManagementService.transferAssets(newAsset, switchAssetBody.getCreatedByUnit(), switchAssetBody.getCreatedBy(), switchAssetBody.getRequestingUnit());
            TransferOrder newTo = transferOrderManagementService.getTransferOrderDetail(newAssetToId);
            if (Float.compare(newTo.getTotalAmount(), 50000) > 0) {
                throw new SumoException("SWITCH_EXCEPTION", "Asset transfer require Eway bill");
            }
            Integer newAssetGrId = settleSwitchAssetGR(newAssetToId, switchAssetBody.getCreatedByUnit(),switchAssetBody.getCreatedBy());

            // Auto TO and GR for oldAsset
            List<AssetDefinitionData> oldAsset = new ArrayList<>();
            oldAsset.add(oldAssetDefinitionData);
            Integer oldAssetToId = scmAssetManagementService.transferAssets(oldAsset, switchAssetBody.getRequestingUnit(), switchAssetBody.getCreatedBy(), switchAssetBody.getCreatedByUnit());
            TransferOrder oldTo = transferOrderManagementService.getTransferOrderDetail(oldAssetToId);
            if (Float.compare(oldTo.getTotalAmount(), 50000) > 0) {
                throw new SumoException("SWITCH_EXCEPTION", "Asset transfer require Eway bill");
            }
            Integer oldAssetGrId = settleSwitchAssetGR(oldAssetToId, switchAssetBody.getRequestingUnit(),switchAssetBody.getCreatedBy());

            Long resId = saveSwitchAssetInfo(switchAssetBody, newAssetToId, newAssetGrId, oldAssetToId, oldAssetGrId);
            startUploadAndNotificationTask(switchAssetBody,resId,newAssetDefinitionData,oldAssetDefinitionData);
            return true;
        } catch (Exception e) {
            log.info("######### Error in start switch process ###########, {}", e.getMessage());
            log.error(e.getStackTrace());
            throw new SumoException("SWITCH_EXCEPTION",e.getMessage());
        }
    }

    private ArrayList<String> getDestinationMails(SwitchAssetBody switchAssetBody){
        String requestedByEmail = masterDataCache.getEmployeeBasicDetail(switchAssetBody.getRequestedBy()).getEmailId();
        String createdByEmail = masterDataCache.getEmployeeBasicDetail(switchAssetBody.getCreatedBy()).getEmailId();
        String requestedByUnitEmail = masterDataCache.getUnit(switchAssetBody.getRequestingUnit()).getUnitEmail();
        String createdByUnitEmail = masterDataCache.getUnit(switchAssetBody.getCreatedByUnit()).getUnitEmail();

        ArrayList<String> mails = new ArrayList<>();
        if(!requestedByEmail.isEmpty()) mails.add(requestedByEmail);
        if(!requestedByUnitEmail.isEmpty()) mails.add(requestedByUnitEmail);
        if(!createdByEmail.isEmpty()) mails.add(createdByEmail);
        if(!createdByUnitEmail.isEmpty()) mails.add(createdByUnitEmail);
        mails.add("<EMAIL>");
        mails.add("<EMAIL>");
        mails.add("<EMAIL>");
        return mails;
    }
    private void startUploadAndNotificationTask(SwitchAssetBody switchAssetBody,Long resId,AssetDefinitionData newAssetDefinitionData,AssetDefinitionData oldAssetDefinitionData){
        taskExecutor.execute(() -> {
            try {
                FileDetail uploadedZip = uploadAndZipAssetImages(switchAssetBody, resId);
                SwitchAssetData switchAssetData = switchAssetDataDao.findById(resId).orElse(null);
                if(switchAssetData!=null && uploadedZip!=null){
                    switchAssetData.setAwsBucket(uploadedZip.getBucket());
                    switchAssetData.setAwsKey(uploadedZip.getKey());
                    switchAssetDataDao.saveAndFlush(switchAssetData);
                }
                SwitchAssetEventData s = getSwitchEventData(switchAssetBody, newAssetDefinitionData, oldAssetDefinitionData, resId, uploadedZip == null ? "NA" : envProperties.getAssetImageUrl()+uploadedZip.getKey().split("/")[2]);
                SwitchAssetEmailTemplate switchAssetEmailTemplate = new SwitchAssetEmailTemplate(s, envProperties.getBasePath());
                new SwitchAssetEmailNotification(switchAssetEmailTemplate, envProperties.getEnvType(),getDestinationMails(switchAssetBody)).sendEmail();
                if(switchAssetData!=null && uploadedZip!=null){
                    switchAssetData.setIsEmailSent("Y");
                    switchAssetDataDao.saveAndFlush(switchAssetData);
                }
            }catch (Exception e){
                log.error("############ Error in upload and notification task ###############, {}",e.getMessage());
                e.printStackTrace();
                throw new RuntimeException(e);
            }
        });
    }
    private SwitchAssetEventData getSwitchEventData(SwitchAssetBody switchAssetBody,AssetDefinitionData newAsset,AssetDefinitionData oldAsset,Long resId,String url){

        DateFormat dateFormat = new SimpleDateFormat("yyyy-mm-dd hh:mm:ss");
        Date newAssetWarrantyDate = newAsset.getWarrantyLastDate();
        Date newAssetInsuranceLastDate = newAsset.getInsuranceLastDate();
        Date oldAssetWarrantyDate = oldAsset.getWarrantyLastDate();
        Date oldAssetInsuranceLastDate = oldAsset.getInsuranceLastDate();

        return SwitchAssetEventData.builder()
                .switchId(String.valueOf(resId)).newAssetIdAndName(newAsset.getAssetName()+"("+newAsset.getAssetId()+")")
                .newAssetProcurementCost(newAsset.getProcurementCost().toString()).newAssetWarranty(newAsset.getHasWarranty())
                .newAssetWarrantyLastDate(newAssetWarrantyDate==null?"NA": dateFormat.format(newAssetWarrantyDate)).newAssetInsurance(newAsset.getHasInsurance())
                .newAssetInsuranceLastDate(newAssetInsuranceLastDate==null?"NA":dateFormat.format(newAssetInsuranceLastDate)).newAssetComment(switchAssetBody.getNewAssetComments())
                .oldAssetIdAndName(oldAsset.getAssetName()+"("+oldAsset.getAssetId()+")")
                .oldAssetProcurementCost(oldAsset.getProcurementCost().toString()).oldAssetWarranty(oldAsset.getHasWarranty())
                .oldAssetWarrantyLastDate(oldAssetWarrantyDate==null?"NA"  : dateFormat.format(oldAssetWarrantyDate)).oldAssetInsurance(oldAsset.getHasInsurance())
                .oldAssetInsuranceLastDate(oldAssetInsuranceLastDate==null? "NA" : dateFormat.format(oldAssetInsuranceLastDate)).oldAssetComment(switchAssetBody.getOldAssetComments())
                .requestedByUser(switchAssetBody.getRequestedBy().toString()).createdByUser(switchAssetBody.getCreatedBy().toString()).fromUnit(switchAssetBody.getCreatedByUnit().toString()).toUnit(switchAssetBody.getRequestingUnit().toString())
                .url(url).reason(switchAssetBody.getSwitchReason())
                .build();
    }

    private Long saveSwitchAssetInfo(SwitchAssetBody switchAssetBody, Integer newAssetToId, Integer newAssetGrId, Integer oldAssetToId, Integer oldAssetGrId) throws SumoException {
        try {
            SwitchAssetData switchAssetData = new SwitchAssetData();
            switchAssetData.setNewAssetId(switchAssetBody.getNewAssetId());
            switchAssetData.setNewAssetToId(newAssetToId);
            switchAssetData.setNewAssetGrId(newAssetGrId);
            switchAssetData.setNewAssetComments(switchAssetBody.getNewAssetComments());

            switchAssetData.setOldAssetId(switchAssetBody.getOldAssetId());
            switchAssetData.setOldAssetToId(oldAssetToId);
            switchAssetData.setOldAssetGrId(oldAssetGrId);
            switchAssetData.setOldAssetComments(switchAssetBody.getOldAssetComments());

            switchAssetData.setSwitchReason(switchAssetBody.getSwitchReason());
            switchAssetData.setCreatedBy(switchAssetBody.getCreatedBy());
            switchAssetData.setRequestedBy(switchAssetBody.getRequestedBy());
            switchAssetData.setCreatedUnit(switchAssetBody.getCreatedByUnit());
            switchAssetData.setRequestingUnit(switchAssetBody.getRequestingUnit());
            switchAssetData.setTicketId(switchAssetBody.getTicketId());
            switchAssetData.setStatus("SUCCESS");
            switchAssetData.setIsEmailSent("N");
            SwitchAssetData sad = switchAssetDataDao.saveAndFlush(switchAssetData);
            return sad.getId();
        } catch (Exception e) {
            log.error("######### Error while saving switch asset info ############, {}", e.getMessage());
            e.printStackTrace();
            throw new SumoException("SWITCH_EXCEPTION","Error while storing switch asset information.");
        }

    }

    private Integer settleSwitchAssetGR(Integer toId, Integer generatedUnitId,Integer createdBy) throws SumoException {
        try {

            GoodsReceivedData grData = transferOrderManagementService.getGrByTO(toId);

            grData.setAutoGenerated(AppUtils.setStatus(true));

            scmAssetManagementDao.update(grData, true);

            GoodsReceived gr = SCMDataConverter.convert(grData, new IdCodeName(createdBy, "", ""),
                    new IdCodeName(createdBy, "", ""), null,
                    new IdCodeName(generatedUnitId, "", ""), new IdCodeName(grData.getGeneratedForUnitId(), "", ""),
                    true, scmCache, masterDataCache);
            gr.setTransferOrderType(TransferOrderType.FIXED_ASSET_TRANSFER);
            gr.setAutoGenerated(true);
            for (GoodsReceivedItem item : gr.getGoodsReceivedItems()) {
                item.setReceivedQuantity(item.getTransferredQuantity());
                for (SCMOrderPackaging packaging : item.getPackagingDetails()) {
                    packaging.setReceivedQuantity(packaging.getTransferredQuantity());
                }
            }
            goodsReceiveManagementService.settleGoodsReceivedDetail(gr);
            return gr.getId();
        } catch (Exception e) {
            log.error("######## Error in settleSwitchAssetGr ##############, {}", e.getMessage());
            e.printStackTrace();
            throw new SumoException("SWITCH_EXCEPTION","Error while doing auto GR");
        }
    }




    private FileDetail uploadAndZipAssetImages(SwitchAssetBody switchAssetBody,Long resId) {
        List<DocumentDetailData> newAssetDocs = new ArrayList<>();
        List<DocumentDetailData> oldAssetDocs = new ArrayList<>();
        if (!switchAssetBody.getNewAssetDocIds().isEmpty()) {
            newAssetDocs = scmAssetManagementDao.getSwitchAssetDocs(switchAssetBody.getNewAssetDocIds());
        }
        if (!switchAssetBody.getOldAssetDocIds().isEmpty()) {
            oldAssetDocs = scmAssetManagementDao.getSwitchAssetDocs(switchAssetBody.getOldAssetDocIds());
        }

         if(oldAssetDocs.isEmpty() && newAssetDocs.isEmpty()) return null;

        try {
            File zipFile = new File(docBasePath + "/switchAssetImages_"+resId+".zip");
            ZipOutputStream zipOutputStream = new ZipOutputStream(Files.newOutputStream(zipFile.toPath()));
            BufferedInputStream bufferedInputStream = null;

            try {
               if(!newAssetDocs.isEmpty())  storeAssetImagesInZipFile(newAssetDocs, bufferedInputStream, zipOutputStream);
               if (!oldAssetDocs.isEmpty()) storeAssetImagesInZipFile(oldAssetDocs, bufferedInputStream, zipOutputStream);
            }catch (Exception e){
                log.error("#### Error while storing new and old asset images in zip #########, {}",e.getMessage());
                throw new RuntimeException(e);
            }finally {
             if(bufferedInputStream!=null) bufferedInputStream.close();
             zipOutputStream.close();
            }

            InputStream stream = Files.newInputStream(zipFile.toPath());
            MultipartFile zipMultipartFile = new MockMultipartFile("file", zipFile.getName(), MediaType.APPLICATION_OCTET_STREAM_VALUE, stream);

            return fileArchiveService.saveFileToS3(envProperties.getS3ProductBucket(),"scm-service/excess_asset_image",zipFile.getName(),zipMultipartFile);
        }catch (Exception e) {
                throw new RuntimeException(e);
        }
    }

    private void storeAssetImagesInZipFile(List<DocumentDetailData> assetDocs,BufferedInputStream bufferedInputStream,ZipOutputStream zipOutputStream){
        try{
            final int BUFFER = 1024;
            for (DocumentDetailData d : assetDocs) {
                bufferedInputStream = null;
                FileDetail fd = new FileDetail(d.getS3Bucket(), d.getS3Key(), null);
                File f = fileArchiveService.getFileFromS3(docBasePath+"/old_new_asset", fd);
                FileInputStream fis = new FileInputStream(f);
                bufferedInputStream = new BufferedInputStream(fis, BUFFER);
                ZipEntry zipEntry = new ZipEntry(f.getName());
                zipOutputStream.putNextEntry(zipEntry);
                byte data[] = new byte[BUFFER];
                int count;
                while ((count = bufferedInputStream.read(data, 0, BUFFER)) != -1) {
                    zipOutputStream.write(data, 0, count);
                }
                zipOutputStream.closeEntry();
            }
        }catch (Exception e){
                log.error("######### Error while storing asset images in zip file ##############");
                throw new RuntimeException(e);
        }
    }
    @Override
    public void deleteTempFiles() {
        try {
            FileUtils.cleanDirectory(new File(docBasePath));
        } catch (IOException e) {
            log.error("######## Error while cleaning maintenance temp files #########");
        }
    }

}

package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.AssetRecoveryDefinition;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ScmMissingPricesNotificationTemplate  extends AbstractVelocityTemplate {


    private Map<String, Pair<String,String>> regionToUnitProductMap;
    private String basePath;

    public ScmMissingPricesNotificationTemplate(Map<String,Pair<String,String>> regionToUnitProductMap, String basePath){
        this.regionToUnitProductMap = regionToUnitProductMap;
        this.basePath=  basePath;

    }

    @Override
    public String getTemplatePath() {
        return "templates/MissingPrices.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/MissingPrices/" + "_" + SCMUtil.getDateString(SCMUtil.getCurrentDateIST()) + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("regionToUnitProductMap", regionToUnitProductMap);
        stringObjectMap.put("keys",regionToUnitProductMap.keySet());
        return stringObjectMap;
    }
}

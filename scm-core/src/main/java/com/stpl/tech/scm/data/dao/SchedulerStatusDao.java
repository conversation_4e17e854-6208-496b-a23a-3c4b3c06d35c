package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.data.enums.SchedulerStatus;
import com.stpl.tech.scm.data.enums.SchedulerType;
import com.stpl.tech.scm.data.model.SchedulerStatusData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;

@Repository
public interface SchedulerStatusDao extends JpaRepository<SchedulerStatusData,Long> {

    boolean existsBySchedulerTypeAndStatus(SchedulerType schedulerType, SchedulerStatus schedulerStatus);

    @Query("SELECT COUNT(e) > 0 FROM SchedulerStatusData e " +
            "WHERE e.schedulerType = :type AND e.status = :status " +
            "AND FUNCTION('DATE', e.timeStamp) = :date")
    boolean existsByTypeStatusAndExactDate(
            @Param("type") SchedulerType type,
            @Param("status") SchedulerStatus status,
            @Param("date") Date dateOnly
    );


}

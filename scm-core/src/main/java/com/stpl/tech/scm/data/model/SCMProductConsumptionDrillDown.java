/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;
import java.math.RoundingMode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.stpl.tech.scm.core.util.SCMUtil;

@Entity
@Table(name = "PRODUCT_CONSUMPTION_DRILLDOWN")
public class SCMProductConsumptionDrillDown {
	private Integer consumptionId;
	private int productId;
	private int unitId;
	private BigDecimal consumption;
	private BigDecimal taxableConsumption;
	private String unitOfMeasure;
	private String consumptionType;
	private SCMDayCloseEventData eventId;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "CONSUMPTION_ID", unique = true, nullable = false)
	public Integer getConsumptionId() {
		return consumptionId;
	}

	public void setConsumptionId(Integer consumptionId) {
		this.consumptionId = consumptionId;
	}

	@Column(name = "PRODUCT_ID", nullable = false)
	public int getProductId() {
		return productId;
	}

	public void setProductId(int productId) {
		this.productId = productId;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "CONSUMPTION")
	public BigDecimal getConsumption() {
		return consumption;
	}

	public void setConsumption(BigDecimal consumption) {
		this.consumption = SCMUtil.convertToBigDecimal(consumption).setScale(6,RoundingMode.HALF_UP);
	}


	@Column(name = "UOM", nullable = false)
	public String getUnitOfMeasure() {
		return unitOfMeasure;
	}

	public void setUnitOfMeasure(String unitOfMeasure) {
		this.unitOfMeasure = unitOfMeasure;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "EVENT_ID", nullable = false)
	public SCMDayCloseEventData getEventId() {
		return eventId;
	}

	public void setEventId(SCMDayCloseEventData eventId) {
		this.eventId = eventId;
	}

	@Column(name = "CONSUMPTION_TYPE")
	public String getConsumptionType() {
		return consumptionType;
	}

	public void setConsumptionType(String consumptionType) {
		this.consumptionType = consumptionType;
	}

	@Column(name = "TAXABLE_CONSUMPTION")
	public BigDecimal getTaxableConsumption() {
		return taxableConsumption;
	}

	public void setTaxableConsumption(BigDecimal taxableConsumption) {
		this.taxableConsumption = SCMUtil.convertToBigDecimal(taxableConsumption).setScale(6,RoundingMode.HALF_UP);
	}
}

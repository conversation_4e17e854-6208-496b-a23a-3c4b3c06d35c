package com.stpl.tech.scm.notification.pdf.template;

import com.stpl.tech.scm.domain.model.SalesPerformaInvoice;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by Chaayos on 05-09-2016.
 */
public class B2BInvoiceTemplate extends AbstractVelocityTemplate {

    private SalesPerformaInvoice invoice;
    private String basePath;

    public B2BInvoiceTemplate() {

    }

    public B2BInvoiceTemplate(SalesPerformaInvoice invoice) {
        this.invoice = invoice;
    }

    @Override
    public String getTemplatePath() {
        return "templates/B2BInvoiceTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/Invoice/" + invoice.getId() + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("invoice", invoice);
        return stringObjectMap;
    }

}

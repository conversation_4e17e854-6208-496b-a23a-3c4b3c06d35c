package com.stpl.tech.scm.core.service;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.data.model.ProductionBookingData;
import com.stpl.tech.scm.data.model.ProductionBookingMappingData;
import com.stpl.tech.scm.data.model.TransferOrderItemData;
import com.stpl.tech.scm.domain.model.ProductionBooking;
import com.stpl.tech.scm.domain.model.SCMProductItem;
import org.springframework.web.multipart.MultipartFile;

public interface ProductionBookingService {

	public ProductionBooking calculateConsumption(int unitId, int productId, BigDecimal quantity) throws SumoException, DataNotFoundException;

	public boolean addBooking(ProductionBooking booking) throws InventoryUpdateException, SumoException, DataNotFoundException, DataUpdationException;

	public List<ProductionBooking> getBookings(Integer unitId, Date startDate, Date endDate, boolean isReverse);

	public boolean cancelBookings(Integer id, Integer empId) throws InventoryUpdateException, DataNotFoundException;

	public ProductionBooking getLastBooking(Integer unitId, boolean isReverse) throws InventoryUpdateException, DataNotFoundException;

	public boolean updateMapping(ProductionBooking booking, Boolean updateMapping)throws SumoException;

	public ProductionBookingMappingData productionUnitMappingItems(int productId, int unitId, int skuId, String profile) throws SumoException;

	public boolean inactiveProductFromProductionBooking(Integer productId, String profile);

	public boolean updateBookings(ProductionBookingData productionBooking , TransferOrderItemData transferOrderItem , Integer empId  , SCMCache scmCache) throws InventoryUpdateException, DataNotFoundException, SumoException;

	boolean addReverseBooking(ProductionBooking booking) throws InventoryUpdateException, SumoException, DataNotFoundException, DataUpdationException;

	boolean updateReverseMapping(ProductionBooking booking, Boolean updateMapping) throws SumoException;

    boolean cancelReverseBookings(Integer id, Integer empId) throws InventoryUpdateException, DataNotFoundException;

	public List<SCMProductItem> readUploadFile(MultipartFile file)  throws IOException,SumoException;
}

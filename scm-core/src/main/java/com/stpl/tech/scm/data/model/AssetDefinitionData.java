/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;


import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;


@Entity
@Table(name = "ASSET_DEFINITION")
public class AssetDefinitionData implements java.io.Serializable {


    /**
	 *
	 */
	private static final long serialVersionUID = 6741880858131328971L;

	private Integer assetId;

    private String assetName;

    private String assetImageUrl;

    /*
        UnitId signifies Location of Asset
     */
    private Integer unitId;


    private String unitType;


    private String assetStatus;


    private Integer SKUId;


    private ProductDefinitionData product;

    //private Integer productId;


    private Integer profileId;


    private Integer grId;

    private Integer grItemId;


    private Integer vendorId;

    private String vendorName;


    private String ownerType;

    private Integer ownerId;


    private String firstOwnerType;

    private Integer firstOwnerId;


    private String tagType;

    private String tagValue;
    private int tagPrintCount;

    private Date lastTagPrintDate;

    private Integer lastTagPrintedBy;

    private BigDecimal price;
    private BigDecimal tax;
    private BigDecimal procurementCost;

    private int quantity;


    private String lifeTimeType;
    private BigDecimal lifeTimeValue;
    private Integer lifeTimeInDays;


    private Date inventoryDate;


    private Date startDate;


    private Date expectedEndDate;


    private Date actualEndDate;

    private String depreciationStrategy;
    private BigDecimal depreciationRatePa;
    private BigDecimal dailyDepreciationRate;
    private BigDecimal depreciationResidue;
    private BigDecimal realizedDepreciation;

    private Date realizedDepreciationDate;

    private String lastTransferType;
    private Integer lastTransferId;

    private Date lastTransferDate;

    private Integer lastTransferedBy;

    private String hasWarranty;

    private Date warrantyLastDate;

    private String hasAMC;

    private Date amcLastDate;

    private String hasInsurance;

    private Date insuranceLastDate;

    private Date creationDate;

    private Integer createdBy;

    private BigDecimal grossBlock;

    private BigDecimal fixedValue;

    private String recoveryType;

    private BigDecimal recoveryAmount;

    private String recoveryStatus;

    private String writeOffType;

    private BigDecimal writeOffAmount;

    private String isWriteOff;

    private BigDecimal taxPercentage;

    private String uniqueFieldName;

    private String uniqueFieldValue;

    private String isInTransit;

    private BigDecimal totalRecoverAmount;


    public AssetDefinitionData() {
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ASSET_ID", unique = true, nullable = false)
    public Integer getAssetId() {
        return assetId;
    }

    public void setAssetId(Integer assetId) {
        this.assetId = assetId;
    }

    @Column(name = "ASSET_NAME", nullable = false, length = 255)
    public String getAssetName() {
        return assetName;
    }


    public void setAssetName(String assetName) {
        this.assetName = assetName;
    }

    @Column(name = "ASSET_IMAGE_URL", nullable = true, length = 255)
    public String getAssetImageUrl() {
        return assetImageUrl;
    }

    public void setAssetImageUrl(String assetImageUrl) {
        this.assetImageUrl = assetImageUrl;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public Integer getUnitId() {
        return unitId;
    }


    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "UNIT_TYPE", nullable = false, length = 50)
    public String getUnitType() {
        return unitType;
    }

    public void setUnitType(String unitType) {
        this.unitType = unitType;
    }

    @Column(name = "ASSET_STATUS", nullable = false, length = 50)
    public String getAssetStatus() {
        return assetStatus;
    }

    public void setAssetStatus(String assetStatus) {
        this.assetStatus = assetStatus;
    }

    @Column(name = "SKU_ID", nullable = false)
    public Integer getSKUId() {
        return SKUId;
    }

    public void setSKUId(Integer SKUId) {
        this.SKUId = SKUId;
    }


	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PRODUCT_ID", nullable = false)
    public ProductDefinitionData getProduct() {
		return product;
	}

	public void setProduct(ProductDefinitionData product) {
		this.product = product;
	}


    @Column(name = "PROFILE_ID", nullable = false)
    public Integer getProfileId() {
        return profileId;
    }

    public void setProfileId(Integer profileId) {
        this.profileId = profileId;
    }

    @Column(name = "GR_ID", nullable = false)
    public Integer getGrId() {
        return grId;
    }

    public void setGrId(Integer grId) {
        this.grId = grId;
    }

    @Column(name = "GR_ITEM_ID", nullable = false)
    public Integer getGrItemId() {
        return grItemId;
    }

    public void setGrItemId(Integer grItemId) {
        this.grItemId = grItemId;
    }

    @Column(name = "VENDOR_ID", nullable = false)
    public Integer getVendorId() {
        return vendorId;
    }

    public void setVendorId(Integer vendorId) {
        this.vendorId = vendorId;
    }

    @Column(name = "VENDOR_NAME", nullable = false, length = 255)
    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    @Column(name = "OWNER_TYPE", nullable = false, length = 50)
    public String getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(String ownerType) {
        this.ownerType = ownerType;
    }

    @Column(name = "OWNER_ID", nullable = false)
    public Integer getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }

    @Column(name = "FIRST_OWNER_TYPE", nullable = false, length = 50)
    public String getFirstOwnerType() {
        return firstOwnerType;
    }

    public void setFirstOwnerType(String firstOwnerType) {
        this.firstOwnerType = firstOwnerType;
    }

    @Column(name = "FIRST_OWNER_ID", nullable = false)
    public Integer getFirstOwnerId() {
        return firstOwnerId;
    }

    public void setFirstOwnerId(Integer firstOwnerId) {
        this.firstOwnerId = firstOwnerId;
    }

    @Column(name = "TAG_TYPE", nullable = true, length = 50)
    public String getTagType() {
        return tagType;
    }

    public void setTagType(String tagType) {
        this.tagType = tagType;
    }

    @Column(name = "TAG_VALUE", nullable = true, length = 50)
    public String getTagValue() {
        return tagValue;
    }

    public void setTagValue(String tagValue) {
        this.tagValue = tagValue;
    }

    @Column(name = "TAG_PRINT_COUNT", nullable = true)
    public int getTagPrintCount() {
        return tagPrintCount;
    }

    public void setTagPrintCount(int tagPrintCount) {
        this.tagPrintCount = tagPrintCount;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_TAG_PRINT_DATE", length = 19)
    public Date getLastTagPrintDate() {
        return lastTagPrintDate;
    }

    public void setLastTagPrintDate(Date lastTagPrintDate) {
        this.lastTagPrintDate = lastTagPrintDate;
    }

    @Column(name = "LAST_TAG_PRINTED_BY", nullable = true)
    public Integer getLastTagPrintedBy() {
        return lastTagPrintedBy;
    }

    public void setLastTagPrintedBy(Integer lastTagPrintedBy) {
        this.lastTagPrintedBy = lastTagPrintedBy;
    }

    @Column(name = "PRICE", nullable = false)
    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Column(name = "TAX", nullable = false)
    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    @Column(name = "PROCUREMENT_COST", nullable = false)
    public BigDecimal getProcurementCost() {
        return procurementCost;
    }

    public void setProcurementCost(BigDecimal procurementCost) {
        this.procurementCost = procurementCost;
    }

    @Column(name = "QUANTITY", nullable = false)
    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    @Column(name = "LIFE_TIME_TYPE", nullable = true, length = 50)
    public String getLifeTimeType() {
        return lifeTimeType;
    }

    public void setLifeTimeType(String lifeTimeType) {
        this.lifeTimeType = lifeTimeType;
    }

    @Column(name = "LIFE_TIME_VALUE", nullable = false)
    public BigDecimal getLifeTimeValue() {
        return lifeTimeValue;
    }

    public void setLifeTimeValue(BigDecimal lifeTimeValue) {
        this.lifeTimeValue = lifeTimeValue;
    }

    @Column(name = "LIFE_TIME_IN_DAYS", nullable = true)
    public Integer getLifeTimeInDays() {
        return lifeTimeInDays;
    }

    public void setLifeTimeInDays(Integer lifeTimeInDays) {
        this.lifeTimeInDays = lifeTimeInDays;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "INVENTORY_DATE", length = 19)
    public Date getInventoryDate() {
        return inventoryDate;
    }

    public void setInventoryDate(Date inventoryDate) {
        this.inventoryDate = inventoryDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "START_DATE", length = 19)
    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "EXPECTED_END_DATE", length = 19)
    public Date getExpectedEndDate() {
        return expectedEndDate;
    }

    public void setExpectedEndDate(Date expectedEndDate) {
        this.expectedEndDate = expectedEndDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "ACTUAL_END_DATE", length = 19)
    public Date getActualEndDate() {
        return actualEndDate;
    }

    public void setActualEndDate(Date actualEndDate) {
        this.actualEndDate = actualEndDate;
    }

    @Column(name = "DEPRECIATION_STRATEGY", nullable = false, length = 50)
    public String getDepreciationStrategy() {
        return depreciationStrategy;
    }

    public void setDepreciationStrategy(String depreciationStrategy) {
        this.depreciationStrategy = depreciationStrategy;
    }

    @Column(name = "DEPRECIATION_RATE_PA", nullable = true)
    public BigDecimal getDepreciationRatePa() {
        return depreciationRatePa;
    }

    public void setDepreciationRatePa(BigDecimal depreciationRatePa) {
        this.depreciationRatePa = depreciationRatePa;
    }

    @Column(name = "DAILY_DEPRECIATION_RATE", nullable = true)
    public BigDecimal getDailyDepreciationRate() {
        return dailyDepreciationRate;
    }

    public void setDailyDepreciationRate(BigDecimal dailyDepreciationRate) {
        this.dailyDepreciationRate = dailyDepreciationRate;
    }

    @Column(name = "DEPRECIATION_RESIDUE", nullable = true)
    public BigDecimal getDepreciationResidue() {
        return depreciationResidue;
    }

    public void setDepreciationResidue(BigDecimal depreciationResidue) {
        this.depreciationResidue = depreciationResidue;
    }

    @Column(name = "REALIZED_DEPRECIATION", nullable = true)
    public BigDecimal getRealizedDepreciation() {
        return realizedDepreciation;
    }

    public void setRealizedDepreciation(BigDecimal realizedDepreciation) {
        this.realizedDepreciation = realizedDepreciation;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "REALIZED_DEPRECIATION_DATE", length = 19)
    public Date getRealizedDepreciationDate() {
        return realizedDepreciationDate;
    }

    public void setRealizedDepreciationDate(Date realizedDepreciationDate) {
        this.realizedDepreciationDate = realizedDepreciationDate;
    }

    @Column(name = "LAST_TRANSFER_TYPE", nullable = true, length = 50)
    public String getLastTransferType() {
        return lastTransferType;
    }

    public void setLastTransferType(String lastTransferType) {
        this.lastTransferType = lastTransferType;
    }

    @Column(name = "LAST_TRANSFER_ID", nullable = true)
    public Integer getLastTransferId() {
        return lastTransferId;
    }

    public void setLastTransferId(Integer lastTransferId) {
        this.lastTransferId = lastTransferId;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_TRANSFER_DATE", length = 19)
    public Date getLastTransferDate() {
        return lastTransferDate;
    }


    public void setLastTransferDate(Date lastTransferDate) {
        this.lastTransferDate = lastTransferDate;
    }

    @Column(name = "LAST_TRANSFER_BY", nullable = true)
    public Integer getLastTransferedBy() {
        return lastTransferedBy;
    }

    public void setLastTransferedBy(Integer lastTransferedBy) {
        this.lastTransferedBy = lastTransferedBy;
    }

    @Column(name = "HAS_WARRANTY", nullable = true, length = 50)
    public String getHasWarranty() {
        return hasWarranty;
    }

    public void setHasWarranty(String hasWarranty) {
        this.hasWarranty = hasWarranty;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "WARRANTY_LAST_DATE", length = 19)
    public Date getWarrantyLastDate() {
        return warrantyLastDate;
    }

    public void setWarrantyLastDate(Date warrantyLastDate) {
        this.warrantyLastDate = warrantyLastDate;
    }

    @Column(name = "HAS_AMC", nullable = true, length = 50)
    public String getHasAMC() {
        return hasAMC;
    }

    public void setHasAMC(String hasAMC) {
        this.hasAMC = hasAMC;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "AMC_LAST_DATE", length = 19)
    public Date getAmcLastDate() {
        return amcLastDate;
    }

    public void setAmcLastDate(Date amcLastDate) {
        this.amcLastDate = amcLastDate;
    }

    @Column(name = "HAS_INSURANCE", nullable = true, length = 50)
    public String getHasInsurance() {
        return hasInsurance;
    }

    public void setHasInsurance(String hasInsurance) {
        this.hasInsurance = hasInsurance;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "INSURANCE_LAST_DATE", length = 19)
    public Date getInsuranceLastDate() {
        return insuranceLastDate;
    }

    public void setInsuranceLastDate(Date insuranceLastDate) {
        this.insuranceLastDate = insuranceLastDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_DATE", length = 19)
    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    @Column(name = "CREATED_BY", nullable = false)
    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    ////////////////////////////////////////////////////

    @Column(name = "GROSS_BLOCK", nullable = false)
    public BigDecimal getGrossBlock() {
        return grossBlock;
    }

    public void setGrossBlock(BigDecimal grossBlock) {
        this.grossBlock = grossBlock;
    }

    @Column(name = "FIXED_VALUE", nullable = false)
    public BigDecimal getFixedValue() {
        return fixedValue;
    }

    public void setFixedValue(BigDecimal fixedValue) {
        this.fixedValue = fixedValue;
    }

    @Column(name = "RECOVERY_TYPE")
    public String getRecoveryType() {
        return recoveryType;
    }

    public void setRecoveryType(String recoveryType) {
        this.recoveryType = recoveryType;
    }

    @Column(name = "RECOVERY_AMOUNT")
    public BigDecimal getRecoveryAmount() {
        return recoveryAmount;
    }

    public void setRecoveryAmount(BigDecimal recoveryAmount) {
        this.recoveryAmount = recoveryAmount;
    }

    @Column(name = "RECOVERY_STATUS")
    public String getRecoveryStatus() {
        return recoveryStatus;
    }

    public void setRecoveryStatus(String recoveryStatus) {
        this.recoveryStatus = recoveryStatus;
    }

    @Column(name = "WRITE_OFF_TYPE")
    public String getWriteOffType() {
        return writeOffType;
    }

    public void setWriteOffType(String writeOffType) {
        this.writeOffType = writeOffType;
    }

    @Column(name = "WRITE_OFF_AMOUNT")
    public BigDecimal getWriteOffAmount() {
        return writeOffAmount;
    }

    public void setWriteOffAmount(BigDecimal writeOffAmount) {
        this.writeOffAmount = writeOffAmount;
    }

    @Column(name = "IS_WRITE_OFF")
    public String getIsWriteOff() {
        return isWriteOff;
    }

    public void setIsWriteOff(String isWriteOff) {
        this.isWriteOff = isWriteOff;
    }

    @Column(name = "TAX_PERCENTAGE")
    public BigDecimal getTaxPercentage() {
        return taxPercentage;
    }

    public void setTaxPercentage(BigDecimal taxPercentage) {
        this.taxPercentage = taxPercentage;
    }

    @Column(name = "UNIQUE_FIELD_NAME")
    public String getUniqueFieldName() {
        return uniqueFieldName;
    }

    public void setUniqueFieldName(String uniqueFieldName) {
        this.uniqueFieldName = uniqueFieldName;
    }

    @Column(name = "UNIQUE_FIELD_VALUE")
    public String getUniqueFieldValue() {
        return uniqueFieldValue;
    }

    public void setUniqueFieldValue(String uniqueFieldValue) {
        this.uniqueFieldValue = uniqueFieldValue;
    }

    @Column(name = "IS_IN_TRANSIT")
    public String getIsInTransit() {
        return isInTransit;
    }

    public void setIsInTransit(String isInTransit) {
        this.isInTransit = isInTransit;
    }

    @Column(name = "TOTAL_RECOVER_AMOUNT")
    public BigDecimal getTotalRecoverAmount() {
        return totalRecoverAmount;
    }

    public void setTotalRecoverAmount(BigDecimal totalRecoverAmount) {
        this.totalRecoverAmount = totalRecoverAmount;
    }
}

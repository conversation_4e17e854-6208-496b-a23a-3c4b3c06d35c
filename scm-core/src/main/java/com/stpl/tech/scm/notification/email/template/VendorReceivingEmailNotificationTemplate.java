package com.stpl.tech.scm.notification.email.template;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.RequestOrder;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

/**
 * Created by Chaayos on 05-09-2016.
 */
public class VendorReceivingEmailNotificationTemplate extends AbstractVelocityTemplate {

    private Map<Integer, List<RequestOrder>> requestOrderMap;
    private Map<Integer, Unit> unitMap;
    private VendorDetail vendorDetail;
    private String basePath;

    public VendorReceivingEmailNotificationTemplate(){

    }

    public VendorReceivingEmailNotificationTemplate(Map<Integer, List<RequestOrder>> requestOrderMap, Map<Integer, Unit> unitMap, VendorDetail vendorDetail, String basePath){
        this.requestOrderMap = requestOrderMap;
        this.unitMap = unitMap;
        this.vendorDetail = vendorDetail;
        this.basePath = basePath;
    }

    @Override
    public String getTemplatePath() {
        return "templates/VendorReceivingEmailTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/vendor/receiving/"+ vendorDetail.getVendorId()+ "/" +SCMUtil.getDateString(SCMUtil.getCurrentDateIST())+".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("requestOrderMap", requestOrderMap);
        stringObjectMap.put("unitMap", unitMap);
        stringObjectMap.put("vendorDetail",vendorDetail);
        return stringObjectMap;
    }

    public VendorDetail getVendorDetail() {
        return vendorDetail;
    }
}

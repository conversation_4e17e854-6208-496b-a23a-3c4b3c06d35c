package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

@Entity
@Table(name = "BOOKING_CONSUMPTION")
public class BookingConsumptionData {

	private Integer id;
	private int skuId;
	private String skuName;
	private String unitOfMeasure;
	private BigDecimal calculatedQuantity;
	private BigDecimal unitPrice;
	private BigDecimal totalCost;
	private ProductionBookingData booking;
	private List<BookingConsumptionItemDrilldown> consumption = new ArrayList<BookingConsumptionItemDrilldown>(0);

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "BOOKING_CONSUMPTION_ID", nullable = false, unique = true)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "SKU_ID", nullable = false)
	public int getSkuId() {
		return skuId;
	}

	public void setSkuId(int skuId) {
		this.skuId = skuId;
	}

	@Column(name = "SKU_NAME", nullable = false)
	public String getSkuName() {
		return skuName;
	}

	public void setSkuName(String skuName) {
		this.skuName = skuName;
	}

	@Column(name = "UOM", nullable = false)
	public String getUnitOfMeasure() {
		return unitOfMeasure;
	}

	public void setUnitOfMeasure(String unitOfMeasure) {
		this.unitOfMeasure = unitOfMeasure;
	}

	@Column(name = "CALCULATED_QUANTITY", nullable = false)
	public BigDecimal getCalculatedQuantity() {
		return calculatedQuantity;
	}

	public void setCalculatedQuantity(BigDecimal calculatedQuantity) {
		this.calculatedQuantity = calculatedQuantity;
	}

	@Column(name = "UNIT_PRICE", nullable = true)
	public BigDecimal getUnitPrice() {
		return unitPrice;
	}

	public void setUnitPrice(BigDecimal unitPrice) {
		this.unitPrice = unitPrice;
	}

	@Column(name = "TOTAL_COST", nullable = true)
	public BigDecimal getTotalCost() {
		return totalCost;
	}

	public void setTotalCost(BigDecimal totalCost) {
		this.totalCost = totalCost;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PRODUCTION_BOOK_ID", nullable = false)
	public ProductionBookingData getBooking() {
		return booking;
	}

	public void setBooking(ProductionBookingData booking) {
		this.booking = booking;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "consumptionData")
	public List<BookingConsumptionItemDrilldown> getConsumption() {
		return consumption;
	}

	public void setConsumption(List<BookingConsumptionItemDrilldown> consumption) {
		this.consumption = consumption;
	}
	
}

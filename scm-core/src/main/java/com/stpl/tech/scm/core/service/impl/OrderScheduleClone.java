package com.stpl.tech.scm.core.service.impl;

import java.util.ArrayList;
import java.util.List;

public class OrderScheduleClone {

    private List<Integer> unitsWithSchedules = new ArrayList<>();
    private List<Integer> unitsWithNoSchedules = new ArrayList<>();
    private Integer cloneFromUnit;
    private List<Integer> selectedUnitsForCloning;

    public List<Integer> getUnitsWithSchedules() {
        return unitsWithSchedules;
    }

    public void setUnitsWithSchedules(List<Integer> unitsWithSchedules) {
        this.unitsWithSchedules = unitsWithSchedules;
    }

    public List<Integer> getUnitsWithNoSchedules() {
        return unitsWithNoSchedules;
    }

    public void setUnitsWithNoSchedules(List<Integer> unitsWithNoSchedules) {
        this.unitsWithNoSchedules = unitsWithNoSchedules;
    }

    public Integer getCloneFromUnit() {
        return cloneFromUnit;
    }

    public void setCloneFromUnit(Integer cloneFromUnit) {
        this.cloneFromUnit = cloneFromUnit;
    }

    public List<Integer> getSelectedUnitsForCloning() {
        return selectedUnitsForCloning;
    }

    public void setSelectedUnitsForCloning(List<Integer> selectedUnitsForCloning) {
        this.selectedUnitsForCloning = selectedUnitsForCloning;
    }
}

package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.sql.Time;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * Entity for KETTLE_STOCK_OUT_PERCENTAGE_DATA table
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "KETTLE_STOCK_OUT_PERCENTAGE_DATA")
public class KettleStockOutPercentageData {

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    private Integer id;

    @Column(name = "UNIT_ID", nullable = false)
    private Integer unitId;

    @Temporal(TemporalType.DATE)
    @Column(name = "BUSINESS_DATE", nullable = false)
    private Date businessDate;

    @Column(name = "BRAND_ID")
    private Integer brandId;

    @Column(name = "INVENTORY_TRACK_LEVEL")
    private String inventoryTrackLevel;

    @Column(name = "PRODUCT_COUNT")
    private Integer productCount;

    @Column(name = "CAFE_OPENING")
    private Date cafeOpening;

    @Column(name = "CAFE_CLOSING")
    private Date cafeClosing;

    @Column(name = "CAFE_OPERATIONAL")
    private String cafeOperational;

    @Column(name = "UNIT_STATUS")
    private String unitStatus;

    @Column(name = "IS_LIVE")
    private String isLive;

    @Column(name = "IS_LIVE_INVENTORY_ENABLED")
    private String isLiveInventoryEnabled;

    @Column(name = "PRODUCT_OPERATIONAL_TIME_IN_MIN")
    private Integer productOperationalTimeInMin;

    @Column(name = "TOTAL_OPERATION_TIME_IN_MIN")
    private Integer totalOperationTimeInMin;

    @Column(name = "TOTAL_DOWN_TIME_IN_MIN")
    private Integer totalDownTimeInMin = 0;

    @Column(name = "STOCK_OUT_PERCENTAGE", precision = 19, scale = 4)
    private BigDecimal stockOutPercentage = BigDecimal.ZERO;

    @OneToMany(mappedBy = "kettleStockOutPercentageData", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<KettleStockOutProductWiseData> kettleStockOutProductWiseDataSet;


    public void addProductDimension(KettleStockOutProductWiseData kettleStockOutProductWiseData) {
        if (kettleStockOutProductWiseDataSet == null) {
            kettleStockOutProductWiseDataSet = new HashSet<>();
        }
        kettleStockOutProductWiseDataSet.add(kettleStockOutProductWiseData);
        kettleStockOutProductWiseData.setKettleStockOutPercentageData(this);
    }


}

package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.data.transport.model.FullfillmentData;
import com.stpl.tech.scm.data.transport.model.MenuToScmData;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Set;

public interface FullfillmentReportDao {
    List<FullfillmentData> getFullfillmentData(int numberOfDays) throws SumoException;
    void getMenuToScmData(Long unitId, HashMap<Long, Set<Long>> scmProductToMenuProduct, HashMap<Long, ArrayList<MenuToScmData>> menuProductToScm ) throws SumoException;
}

/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.scm.data.dao.DemandForecastingDao;
import com.stpl.tech.scm.data.model.DayWiseSlotWiseSalesData;
import com.stpl.tech.scm.data.model.HolidaysListData;
import com.stpl.tech.scm.data.model.UnitWiseOrderingStrategyData;
import com.stpl.tech.scm.domain.model.UnitMappedMenuMappedProductDto;
import com.stpl.tech.util.EnvType;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.List;
import java.util.Date;

@Repository
public class DemandForecastingDaoImpl extends SCMAbstractDaoImpl implements DemandForecastingDao {

    @Override
    public UnitWiseOrderingStrategyData getUnitWiseOrderingStrategyDataByUnitId(Integer unitId) {
        String hql = "FROM UnitWiseOrderingStrategyData u WHERE u.unitId = :unitId";
        Query query = manager.createQuery(hql);
        query.setParameter("unitId", unitId);
        
        try {
            return (UnitWiseOrderingStrategyData) query.getSingleResult();
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<DayWiseSlotWiseSalesData> getHistoricalSalesData(Integer unitId, Integer brandId, Date startDate, Date endDate) {
        String hql = "FROM DayWiseSlotWiseSalesData d WHERE d.unitId = :unitId AND d.brandId = :brandId " +
                    "AND d.businessDate >= :startDate AND d.businessDate <= :endDate " +
                    "ORDER BY d.businessDate DESC";
        Query query = manager.createQuery(hql);
        query.setParameter("unitId", unitId);
        query.setParameter("brandId", brandId);
        query.setParameter("startDate", startDate);
        query.setParameter("endDate", endDate);
        
        @SuppressWarnings("unchecked")
        List<DayWiseSlotWiseSalesData> result = query.getResultList();
        return result;
    }

    @Override
    public List<HolidaysListData> getHolidayListOfType(Date startDate, Date endDate, List<String> holidayTypes) {
        String hql = "FROM HolidaysListData h WHERE h.holidayDate >= :startDate AND h.holidayDate <= :endDate " +
                    "AND h.status = 'ACTIVE' AND h.holidayType IN :holidayTypes ORDER BY h.holidayDate ASC";
        Query query = manager.createQuery(hql);
        query.setParameter("startDate", startDate);
        query.setParameter("endDate", endDate);
        query.setParameter("holidayTypes", holidayTypes);
        
        @SuppressWarnings("unchecked")
        List<HolidaysListData> result = query.getResultList();
        return result;
    }

    @Override
    public List<UnitMappedMenuMappedProductDto> getUnitMappedMenuMappedProducts(Integer unitId, EnvType envType) {
        String schemaName = getSchemaName(envType);
        
        String nativeQuery = "WITH UNIT_MAPPED_PRICES AS (" +
            "SELECT " +
            "UD.UNIT_ID, " +
            "PD.PRODUCT_ID, " +
            "RL.RL_ID, " +
            "RL.RL_NAME AS DIMENSION, " +
            "PD.PRODUCT_CLASSIFICATION, " +
            "PD.PRODUCT_TYPE, " +
            "PD.PRODUCT_NAME, " +
            "PD.BRAND_ID, " +
            "PD.IS_INVENTORY_TRACKED, " +
            "PD.INVENTORY_TRACK_LEVEL " +
            "FROM " + schemaName + ".UNIT_PRICE_PROFILE_MAPPING UPPM " +
            "INNER JOIN " + schemaName + ".UNIT_DETAIL UD ON UPPM.UNIT_ID = UD.UNIT_ID AND UD.UNIT_CATEGORY = 'CAFE' " +
            "AND UD.UNIT_STATUS = 'ACTIVE' AND UD.IS_LIVE = 'Y' " +
            "INNER JOIN " + schemaName + ".PRICE_PROFILE_VERSION PPV ON UPPM.PRICE_PROFILE_ID = PPV.PRICE_PROFILE_ID " +
            "AND PPV.STATUS = 'ACTIVE' " +
            "AND UPPM.MAPPING_STATUS = 'ACTIVE' " +
            "INNER JOIN " + schemaName + ".PRICE_PROFILE_PRODUCT_MAPPING PPPM ON UPPM.PRICE_PROFILE_ID = PPPM.PRICE_PROFILE_ID " +
            "AND PPPM.STATUS = 'ACTIVE' " +
            "AND UPPM.PRICE_PROFILE_VERSION = PPPM.VERSION " +
            "STRAIGHT_JOIN " + schemaName + ".PRODUCT_DETAIL PD ON PPPM.PRODUCT_ID = PD.PRODUCT_ID AND PD.PRODUCT_STATUS = 'ACTIVE' AND " +
            "PD.PRODUCT_CLASSIFICATION IN ('MENU', 'PAID_ADDON') AND PD.IS_INVENTORY_TRACKED = 'Y' " +
            "STRAIGHT_JOIN " + schemaName + ".REF_LOOKUP RL ON PPPM.DIMENSION_CODE = RL.RL_ID " +
            "INNER JOIN " + schemaName + ".UNIT_PRODUCT_MAPPING UPM ON PPPM.PRODUCT_ID = UPM.PRODUCT_ID AND UPM.UNIT_ID = UPPM.UNIT_ID " +
            "AND UPM.PRODUCT_STATUS = 'ACTIVE' " +
            "INNER JOIN " + schemaName + ".UNIT_PRODUCT_PRICING UPP ON UPM.UNIT_PROD_REF_ID = UPP.UNIT_PROD_REF_ID " +
            "AND UPP.PRICING_STATUS = 'ACTIVE' " +
            "WHERE UD.UNIT_ID = :unitId " +
            "GROUP BY UD.UNIT_ID, PD.PRODUCT_ID, RL.RL_NAME " +
            "), MENU_MAPPED_PRODUCTS AS (" +
            "SELECT " +
            "UCPM.UNIT_ID, " +
            "UD.UNIT_NAME, " +
            "UD.UNIT_REGION, " +
            "PS.PRODUCT_ID, " +
            "PD.PRODUCT_CLASSIFICATION, " +
            "PD.PRODUCT_TYPE, " +
            "UCPM.CHANNEL_PARTNER_ID, " +
            "UPBM.BRAND_ID, " +
            "MSD.MENU_TYPE, " +
            "MSMD.MENU_SEQUENCE_ID, " +
            "MSD.MENU_SEQUENCE_NAME " +
            "FROM " + schemaName + ".UNIT_CHANNEL_PARTNER_MAPPING UCPM " +
            "INNER JOIN " + schemaName + ".UNIT_PARTNER_BRAND_MAPPING UPBM ON UCPM.UNIT_ID = UPBM.UNIT_ID " +
            "AND UCPM.STATUS = 'ACTIVE' " +
            "AND UPBM.MAPPING_STATUS = 'ACTIVE' " +
            "AND UCPM.CHANNEL_PARTNER_ID = UPBM.PARTNER_ID " +
            "INNER JOIN " + schemaName + ".UNIT_CHANNEL_PARTNER_MENU_MAPPING UCPMM ON UCPM.ID = UCPMM.UNIT_CHANNEL_PARTNER_ID " +
            "AND UPBM.BRAND_ID = UCPMM.BRAND_ID " +
            "INNER JOIN " + schemaName + ".CHANNEL_PARTNER CP ON UPBM.PARTNER_ID = CP.PARTNER_ID " +
            "AND CP.PARTNER_STATUS = 'ACTIVE' " +
            "INNER JOIN " + schemaName + ".MENU_SEQUENCE_MAPPING_DATA MSMD ON UCPMM.MENU_SEQUENCE_ID = MSMD.MENU_SEQUENCE_ID " +
            "AND MSMD.STATUS = 'ACTIVE' " +
            "INNER JOIN " + schemaName + ".MENU_SEQUENCE_DATA MSD ON MSMD.MENU_SEQUENCE_ID = MSD.MENU_SEQUENCE_ID " +
            "AND MSD.MENU_STATUS = 'ACTIVE' " +
            "INNER JOIN " + schemaName + ".PRODUCT_SEQUENCE PS ON PS.PRODUCT_GROUP_ID = MSMD.PRODUCT_GROUP_ID " +
            "AND PS.STATUS = 'ACTIVE' " +
            "AND MSMD.PRODUCT_GROUP_PARENT_ID IS NOT NULL " +
            "INNER JOIN " + schemaName + ".PRODUCT_DETAIL PD ON PS.PRODUCT_ID = PD.PRODUCT_ID " +
            "AND PD.PRODUCT_STATUS = 'ACTIVE' " +
            "AND PD.PRODUCT_CLASSIFICATION IN ('MENU', 'PAID_ADDON') " +
            "AND PD.IS_INVENTORY_TRACKED = 'Y' " +
            "INNER JOIN " + schemaName + ".UNIT_DETAIL UD ON UCPM.UNIT_ID = UD.UNIT_ID " +
            "AND UD.UNIT_STATUS = 'ACTIVE' " +
            "AND UD.IS_LIVE = 'Y' " +
            "AND UD.UNIT_CATEGORY = 'CAFE' " +
            "WHERE UCPM.UNIT_ID = :unitId " +
            "GROUP BY UCPM.UNIT_ID, PD.PRODUCT_ID " +
            "), " +
            "UNIT_MAPPED_MENU_UNMAPPED_PRODUCTS AS (" +
            "SELECT " +
            "UMP.* " +
            "FROM " +
            "UNIT_MAPPED_PRICES UMP " +
            "INNER JOIN " +
            "MENU_MAPPED_PRODUCTS MMP ON UMP.UNIT_ID = MMP.UNIT_ID " +
            "AND UMP.PRODUCT_ID = MMP.PRODUCT_ID " +
            ") " +
            "SELECT " +
            "A.UNIT_ID, " +
            "A.PRODUCT_ID, " +
            "A.RL_ID, " +
            "A.DIMENSION, " +
            "A.PRODUCT_CLASSIFICATION, " +
            "A.PRODUCT_TYPE, " +
            "A.PRODUCT_NAME, " +
            "A.BRAND_ID, " +
            "A.IS_INVENTORY_TRACKED, " +
            "A.INVENTORY_TRACK_LEVEL " +
            "FROM " +
            "UNIT_MAPPED_MENU_UNMAPPED_PRODUCTS A";

        Query query = manager.createNativeQuery(nativeQuery);
        query.setParameter("unitId", unitId);

        @SuppressWarnings("unchecked")
        List<Object[]> results = query.getResultList();
        
        return results.stream()
                .map(this::mapToUnitMappedMenuMappedProductDto)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * Get schema name based on environment type
     * For production (PROD/SPROD): KETTLE_MASTER
     * For other environments: KETTLE_MASTER_DUMP
     */
    private String getSchemaName(EnvType envType) {
        return (EnvType.PROD.equals(envType) || EnvType.SPROD.equals(envType)) ? "KETTLE_MASTER" : "KETTLE_MASTER_STAGE";
    }

    /**
     * Map query result to DTO
     */
    private UnitMappedMenuMappedProductDto mapToUnitMappedMenuMappedProductDto(Object[] row) {
        UnitMappedMenuMappedProductDto dto = new UnitMappedMenuMappedProductDto();
        dto.setUnitId((Integer) row[0]);
        dto.setProductId((Integer) row[1]);
        dto.setRlId((Integer) row[2]);
        dto.setDimension((String) row[3]);
        dto.setProductClassification((String) row[4]);
        dto.setProductType((Integer) row[5]);
        dto.setProductName((String) row[6]);
        dto.setBrandId((Integer) row[7]);
        dto.setIsInventoryTracked((String) row[8]);
        dto.setInventoryTrackLevel((String) row[9]);
        return dto;
    }

}

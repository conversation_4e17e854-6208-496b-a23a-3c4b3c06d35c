package com.stpl.tech.scm.data.dao.impl;

import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import com.stpl.tech.scm.data.dao.AttributeManagementDao;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 05-05-2016.
 */
@Repository
@Log4j2
public class AttributeManagementDaoImpl extends SCMAbstractDaoImpl implements AttributeManagementDao {

    @PersistenceContext(unitName = "SCMDataSourcePUName")
    @Qualifier(value = "SCMDataSourceEMFactory")
    protected EntityManager manager;
    @Override
    public List validateState(String query, Integer unitId) {
        Query query1 = manager.createQuery(query);
        query1.setParameter("unitId",unitId);
        return query1.getResultList();
    }
}

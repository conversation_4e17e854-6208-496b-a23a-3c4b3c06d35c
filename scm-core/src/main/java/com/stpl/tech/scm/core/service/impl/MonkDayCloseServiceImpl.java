package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.scm.core.service.MonkDayCloseService;
import com.stpl.tech.scm.core.service.StockManagementService;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.dao.MonkDayCloseDao;
import com.stpl.tech.scm.data.model.MonkDayCloseEventStatusData;
import com.stpl.tech.scm.data.model.MonkStatusDayCloseData;
import com.stpl.tech.scm.data.model.MonkStatusDayCloseHistoryData;
import com.stpl.tech.scm.data.model.SCMDayCloseEventData;
import com.stpl.tech.scm.domain.model.MonkDayCloseEvent;
import com.stpl.tech.scm.domain.model.MonkStatusDayClose;
import com.stpl.tech.scm.domain.model.MonkStatusDayCloseHistory;
import com.stpl.tech.scm.domain.model.MonkStatusDTO;
import com.stpl.tech.util.domain.RequestContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional("SCMDataSourceTM")
public class MonkDayCloseServiceImpl implements MonkDayCloseService {
    
    private static final Logger LOG = LoggerFactory.getLogger(MonkDayCloseServiceImpl.class);
    
    @Autowired
    private MonkDayCloseDao monkDayCloseDao;
    
    @Autowired
    private StockManagementService stockManagementService;
    
    @Autowired
    private MasterDataCache masterDataCache;
    
    @Override
    public MonkDayCloseEvent initializeMonkDayCloseEvent(Integer unitId, Date businessDate, Integer updatedBy) {
        LOG.info("Initializing monk day close event for unit: {} on date: {}", unitId, businessDate);
        
        // Check if event already exists
        MonkDayCloseEventStatusData existingEvent = monkDayCloseDao.getMonkDayCloseEvent(unitId, businessDate);
        if (existingEvent != null) {
            return convertToMonkDayCloseEvent(existingEvent);
        }
        
        // Check if kettle day close exists for this unit (should be for current business date)
        Integer kettleDayCloseId = null;
        try {
            SCMDayCloseEventData kettleDayClose = stockManagementService.getLatestKettleDayClose(unitId);
            if (kettleDayClose != null && kettleDayClose.getBusinessDate().equals(businessDate)) {
                kettleDayCloseId = kettleDayClose.getEventId();
                LOG.info("Found existing kettle day close with event ID: {} for unit: {}", 
                        kettleDayCloseId, unitId);
            } else {
                LOG.info("No kettle day close found for unit: {} for business date: {}", unitId, businessDate);
            }
        } catch (Exception e) {
            LOG.warn("Could not retrieve kettle day close for unit: {} on date: {}", unitId, businessDate, e);
        }
        
        // Create new event
        MonkDayCloseEventStatusData eventData = monkDayCloseDao.createMonkDayCloseEvent(
                unitId, businessDate, 
                MonkDayCloseEvent.EventType.MINI_DIAGNOSIS.name(),
                MonkDayCloseEvent.EventStatus.INITIATED.name()
        );
        
        // Link kettle day close if found
        if (kettleDayCloseId != null) {
            try {
                monkDayCloseDao.linkKettleDayClose(eventData.getMonkDayCloseEventStatusId(), kettleDayCloseId);
                LOG.info("Successfully linked kettle day close event ID: {} to monk event: {} for unit: {}", 
                        kettleDayCloseId, eventData.getMonkDayCloseEventStatusId(), unitId);
            } catch (Exception e) {
                LOG.error("Failed to link kettle day close event ID: {} to monk event: {} for unit: {}", 
                        kettleDayCloseId, eventData.getMonkDayCloseEventStatusId(), unitId, e);
            }
        }
        
        // Initialize monk statuses based on required monks count
        Integer requiredMonks = monkDayCloseDao.getRequiredMonksCount(unitId);
        for (int i = 1; i <= requiredMonks; i++) {
            String monkName = "CHAI_MONK" + i;
            monkDayCloseDao.createMonkStatus(eventData.getMonkDayCloseEventStatusId(), monkName, 
                    MonkStatusDayClose.MonkStatus.INITIATED.name(), RequestContext.getContext().getLoggedInUserId());
        }
        
        return convertToMonkDayCloseEvent(eventData);
    }
    
    @Override
    public MonkDayCloseEvent getMonkDayCloseEvent(Integer unitId, Date businessDate) {
        MonkDayCloseEventStatusData eventData = monkDayCloseDao.getMonkDayCloseEvent(unitId, businessDate);
        return eventData != null ? convertToMonkDayCloseEvent(eventData) : null;
    }
    
    @Override
    public MonkStatusDayClose updateMonkStatus(Long monkStatusDayCloseId, String monkStatus, Integer updatedBy) {
        LOG.info("Updating monk status: {} to {}", monkStatusDayCloseId, monkStatus);
        
        MonkStatusDayCloseData statusData = monkDayCloseDao.updateMonkStatus(monkStatusDayCloseId, monkStatus, updatedBy);
        if (statusData != null) {
            // Check if all monks are completed and update event status
            checkAndUpdateEventStatus(statusData.getEventStatusId());
            return convertToMonkStatusDayClose(statusData);
        }
        return null;
    }
    
    @Override
    public MonkStatusDayClose createMonkStatus(Long eventStatusId, String monkName, String monkStatus, Integer updatedBy) {
        LOG.info("Creating new monk status for event: {}, monk: {}, status: {}", eventStatusId, monkName, monkStatus);
        
        MonkStatusDayCloseData statusData = monkDayCloseDao.createMonkStatus(eventStatusId, monkName, monkStatus, updatedBy);
        if (statusData != null) {
            return convertToMonkStatusDayClose(statusData);
        }
        return null;
    }
    
    @Override
    public List<MonkStatusDTO> getAvailableMonkStatuses() {
        // Only return actual business statuses - UI placeholders should be handled in frontend
        List<MonkStatusDTO> statuses = Arrays.asList(MonkStatusDTO.fromMonkStatus(MonkStatusDayClose.MonkStatus.MONK_DOWN));
        LOG.info("Returning {} monk statuses: {}", statuses.size(), statuses);
        return statuses;
    }
    
    @Override
    public boolean checkAndUpdateEventStatus(Long eventId) {
        List<MonkStatusDayCloseData> monkStatuses = monkDayCloseDao.getMonkStatusesByEventId(eventId);
        
        // Check if all monks have status other than INITIATED
        boolean allMonksProcessed = monkStatuses.stream()
                .allMatch(status -> !MonkStatusDayClose.MonkStatus.INITIATED.name().equals(status.getMonkStatus()));
        
        if (allMonksProcessed && !monkStatuses.isEmpty()) {
            monkDayCloseDao.updateEventStatus(eventId, MonkDayCloseEvent.EventStatus.COMPLETED.name());
            LOG.info("Updated event status to COMPLETED for eventId: {}", eventId);
            return true;
        }
        
        return false;
    }
    
    @Override
    public List<MonkDayCloseEvent> getPendingMonkDayCloseEvents() {
        List<MonkDayCloseEventStatusData> pendingEvents = monkDayCloseDao.getPendingMonkDayCloseEvents();
        return pendingEvents.stream()
                .map(this::convertToMonkDayCloseEvent)
                .collect(Collectors.toList());
    }
    
    private MonkDayCloseEvent convertToMonkDayCloseEvent(MonkDayCloseEventStatusData eventData) {
        List<MonkStatusDayCloseData> monkStatusData = monkDayCloseDao.getMonkStatusesByEventId(eventData.getMonkDayCloseEventStatusId());
        List<MonkStatusDayClose> monkStatuses = monkStatusData.stream()
                .map(this::convertToMonkStatusDayClose)
                .collect(Collectors.toList());
        
        return MonkDayCloseEvent.builder()
                .monkDayCloseEventStatusId(eventData.getMonkDayCloseEventStatusId())
                .unitId(eventData.getUnitId())
                .businessDate(eventData.getBusinessDate())
                .eventType(eventData.getEventType())
                .eventStatus(eventData.getEventStatus())
                .kettleDayCloseId(eventData.getKettleDayCloseId())
                .sumoDayCloseId(eventData.getSumoDayCloseId())
                .monkStatuses(monkStatuses)
                .build();
    }
    
    @Override
    public MonkDayCloseEvent getLatestMonkDayCloseEventByUnit(Integer unitId) {
        MonkDayCloseEventStatusData eventData = monkDayCloseDao.getLatestMonkDayCloseEventByUnit(unitId);
        if (eventData != null) {
            return convertToMonkDayCloseEvent(eventData);
        }
        return null;
    }
    
    @Override
    public List<MonkStatusDayClose> getLatestMonkStatusesByUnit(Integer unitId) {
        // Get the required number of monks for this unit
        Integer requiredMonksCount = monkDayCloseDao.getRequiredMonksCount(unitId);
        if (requiredMonksCount == null || requiredMonksCount <= 0) {
            return new ArrayList<>();
        }
        
        // Get the latest monk statuses limited to the required count
        List<MonkStatusDayCloseData> statusDataList = monkDayCloseDao.getLatestMonkStatusesByUnit(unitId, requiredMonksCount);
        
        // If no monk statuses found, check if monkDayCloseEnabled is true for this unit
        if (statusDataList.isEmpty()) {
            try {
                UnitBasicDetail unitBasicDetail = masterDataCache.getUnitBasicDetail(unitId);
                if (unitBasicDetail != null && unitBasicDetail.getMonkDayCloseEnabled()) {
                    // Check if there are any previous events at all for this unit
                    MonkDayCloseEventStatusData latestEvent = monkDayCloseDao.getLatestMonkDayCloseEventByUnit(unitId);
                    if (latestEvent == null) {
                        // No previous events at all - return default monks with COMPLETED status
                        LOG.info("No monk events found for unit: {}, returning default monks with COMPLETED status", unitId);
                        return createDefaultMonkStatusesWithCompletedStatus(unitId, requiredMonksCount);
                    }
                }
            } catch (Exception e) {
                LOG.warn("Error checking unit basic details for unit: {}", unitId, e);
            }
        }
        
        return statusDataList.stream()
                .map(this::convertToMonkStatusDayClose)
                .collect(Collectors.toList());
    }
    
    private MonkStatusDayClose convertToMonkStatusDayClose(MonkStatusDayCloseData statusData) {
        return MonkStatusDayClose.builder()
                .monkStatusDayCloseId(statusData.getMonkStatusDayCloseId())
                .eventStatusId(statusData.getEventStatusId())
                .monkName(statusData.getMonkName())
                .monkStatus(statusData.getMonkStatus())
                .build();
    }
    
    @Override
    public void linkKettleDayClose(Long eventId, Integer kettleDayCloseId) {
        LOG.info("Linking kettle day close ID: {} to monk event: {}", kettleDayCloseId, eventId);
        monkDayCloseDao.linkKettleDayClose(eventId, kettleDayCloseId);
    }
    
    @Override
    public void linkSumoDayClose(Long eventId, Integer sumoDayCloseId) {
        LOG.info("Linking sumo day close ID: {} to monk event: {}", sumoDayCloseId, eventId);
        monkDayCloseDao.linkSumoDayClose(eventId, sumoDayCloseId);
    }
    
    @Override
    public MonkStatusDayClose updateMonkStatusWithHistory(Long monkStatusDayCloseId, String monkStatus, String comment, Integer updatedBy) {
        LOG.info("Updating monk status with history for ID: {} to status: {} with comment: {}", 
                monkStatusDayCloseId, monkStatus, comment);
        
        MonkStatusDayCloseData updatedData = monkDayCloseDao.updateMonkStatusWithHistory(monkStatusDayCloseId, monkStatus, comment, updatedBy);
        if (updatedData == null) {
            return null;
        }
        
        return convertToMonkStatusDayClose(updatedData);
    }
    
    @Override
    public List<MonkStatusDayCloseHistory> getMonkStatusHistory(Long monkStatusDayCloseId) {
        LOG.info("Retrieving monk status history for ID: {}", monkStatusDayCloseId);
        
        List<MonkStatusDayCloseHistoryData> historyDataList = monkDayCloseDao.getMonkStatusHistory(monkStatusDayCloseId);
        
        return historyDataList.stream()
                .map(this::convertToMonkStatusDayCloseHistory)
                .collect(Collectors.toList());
    }
    
    private MonkStatusDayCloseHistory convertToMonkStatusDayCloseHistory(MonkStatusDayCloseHistoryData historyData) {
        return MonkStatusDayCloseHistory.builder()
                .monkStatusDayCloseHistoryId(historyData.getMonkStatusDayCloseHistoryId())
                .createdAt(historyData.getCreatedAt())
                .monkStatusDayCloseId(historyData.getMonkStatusDayCloseId())
                .monkStatus(historyData.getMonkStatus())
                .comment(historyData.getComment())
                .build();
    }
    
    private List<MonkStatusDayClose> createDefaultMonkStatusesWithCompletedStatus(Integer unitId, Integer requiredMonksCount) {
        try {
            // Create mock monk statuses with COMPLETED status
            List<MonkStatusDayClose> monkStatuses = new ArrayList<>();
            for (int i = 1; i <= requiredMonksCount; i++) {
                String monkName = "CHAI_MONK" + i;
                MonkStatusDayClose monkStatus = MonkStatusDayClose.builder()
                        .monkStatusDayCloseId(0L) // Mock ID (0 indicates no actual record in DB)
                        .eventStatusId(0L) // Mock ID (0 indicates no actual record in DB)
                        .monkName(monkName)
                        .monkStatus(MonkStatusDayClose.MonkStatus.COMPLETED.name())
                        .build();
                monkStatuses.add(monkStatus);
            }
            
            LOG.info("Created {} default monk statuses with COMPLETED status for unit: {}", requiredMonksCount, unitId);
            return monkStatuses;
                    
        } catch (Exception e) {
            LOG.error("Error creating default monk statuses for unit: {}", unitId, e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public void autoCompleteMonkStatuses() {
        try {
            LOG.info("Starting auto-completion of monk statuses (scheduled for 5:55 AM)");
            
            // Get current business date for filtering
            Date currentBusinessDate = SCMUtil.getCurrentBusinessDate();
            LOG.info("Auto-completing monks for current business date: {}", SCMUtil.formatDate(currentBusinessDate, "yyyy-MM-dd"));
            
            // Get all pending monk day close events with INITIATED status
            List<MonkDayCloseEvent> pendingEvents = getPendingMonkDayCloseEvents();
            
            for (MonkDayCloseEvent event : pendingEvents) {
                try {
                    // Check if event is for current business date
                    if (!SCMUtil.isSameDate(event.getBusinessDate(), currentBusinessDate)) {
                        LOG.debug("Skipping monk event for unit: {} - not current business date. Event date: {}, Current date: {}", 
                                event.getUnitId(), 
                                SCMUtil.formatDate(event.getBusinessDate(), "yyyy-MM-dd"),
                                SCMUtil.formatDate(currentBusinessDate, "yyyy-MM-dd"));
                        continue;
                    }
                    
                    // Check if unit has monk day close enabled
                    UnitBasicDetail unitBasicDetail = masterDataCache.getUnitBasicDetail(event.getUnitId());
                    if (unitBasicDetail != null && unitBasicDetail.getMonkDayCloseEnabled()) {
                        // Check if event status is INITIATED
                        if (MonkDayCloseEvent.EventStatus.INITIATED.name().equals(event.getEventStatus())) {
                            LOG.info("Auto-completing monk statuses for unit: {} with event ID: {}", 
                                    event.getUnitId(), event.getMonkDayCloseEventStatusId());
                            
                            // Update only INITIATED monk statuses to COMPLETED with auto-complete comment
                            boolean anyMonkUpdated = false;
                            if (event.getMonkStatuses() != null) {
                                for (MonkStatusDayClose monkStatus : event.getMonkStatuses()) {
                                    // Only auto-complete monks with INITIATED status
                                    if (MonkStatusDayClose.MonkStatus.INITIATED.name().equals(monkStatus.getMonkStatus())) {
                                        updateMonkStatusWithHistory(
                                                monkStatus.getMonkStatusDayCloseId(), 
                                                MonkStatusDayClose.MonkStatus.COMPLETED.name(), 
                                                "autoComplete",
                                                null
                                        );
                                        LOG.info("Auto-completed monk status for monk: {} from INITIATED to COMPLETED in unit: {}", 
                                                monkStatus.getMonkName(), event.getUnitId());
                                        anyMonkUpdated = true;
                                    } else {
                                        LOG.debug("Skipping auto-complete for monk: {} with status: {} in unit: {}", 
                                                monkStatus.getMonkName(), monkStatus.getMonkStatus(), event.getUnitId());
                                    }
                                }
                            }
                            
                            // Check and update the overall event status if any monks were updated
                            if (anyMonkUpdated) {
                                boolean eventUpdated = checkAndUpdateEventStatus(event.getMonkDayCloseEventStatusId());
                                if (eventUpdated) {
                                    LOG.info("Updated MonkDayCloseEvent status to COMPLETED for unit: {} after auto-completing monks", 
                                            event.getUnitId());
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    LOG.error("Error auto-completing monk statuses for unit: {} with event ID: {}", 
                            event.getUnitId(), event.getMonkDayCloseEventStatusId(), e);
                }
            }
            
            LOG.info("Completed auto-completion of monk statuses");
        } catch (Exception e) {
            LOG.error("Error during auto-completion of monk statuses", e);
        }
    }
}

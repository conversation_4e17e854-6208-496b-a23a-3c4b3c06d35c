package com.stpl.tech.scm.core.service.impl;

import com.google.common.base.Stopwatch;
import com.stpl.tech.kettle.core.service.impl.QRGenerationServiceImpl;
import com.stpl.tech.master.core.UnitClosureStateEnum;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.TaxDataCache;
import com.stpl.tech.master.core.external.inventory.service.InventoryService;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.domain.model.*;
import com.stpl.tech.master.inventory.model.InventoryAction;
import com.stpl.tech.master.inventory.model.InventorySource;
import com.stpl.tech.master.inventory.model.ProductQuantityData;
import com.stpl.tech.master.inventory.model.QuantityResponseData;
import com.stpl.tech.master.tax.model.AdditionalTax;
import com.stpl.tech.master.tax.model.TaxData;
import com.stpl.tech.master.tax.model.TaxationDetailDao;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SCMError;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.TransferOrderCreationException;
import com.stpl.tech.scm.core.mapper.DomainDataMapper;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.FetchTransferOrderService;
import com.stpl.tech.scm.core.service.GoodsReceiveManagementService;
import com.stpl.tech.scm.core.service.PriceManagementService;
import com.stpl.tech.scm.core.service.ProductionBookingService;
import com.stpl.tech.scm.core.service.PurchaseOrderManagementService;
import com.stpl.tech.scm.core.service.RequestOrderManagementService;
import com.stpl.tech.scm.core.service.SCMAssetManagementService;
import com.stpl.tech.scm.core.service.SCMProductManagementService;
import com.stpl.tech.scm.core.service.StockManagementService;
import com.stpl.tech.scm.core.service.SkuMappingService;
import com.stpl.tech.scm.core.service.TransferOrderManagementService;
import com.stpl.tech.scm.core.util.EWayHelper;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.PriceManagementDao;
import com.stpl.tech.scm.data.dao.RequestOrderManagementDao;
import com.stpl.tech.scm.data.dao.SCMAssetManagementDao;
import com.stpl.tech.scm.data.dao.ServiceOrderManagementDao;
import com.stpl.tech.scm.data.dao.SkuMappingDao;
import com.stpl.tech.scm.data.dao.StockManagementDao;
import com.stpl.tech.scm.data.dao.TransferOrderManagementDao;
import com.stpl.tech.scm.data.model.*;
import com.stpl.tech.scm.domain.model.*;
import com.stpl.tech.scm.data.transport.model.EPortalWrapper;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.InventoryItemDrilldown;
import com.stpl.tech.scm.domain.model.ItemExpiryData;
import com.stpl.tech.scm.domain.model.ItemExpiryType;
import com.stpl.tech.scm.domain.model.OrderTransferType;
import com.stpl.tech.scm.domain.model.PackagingDefinition;
import com.stpl.tech.scm.domain.model.PackagingType;
import com.stpl.tech.scm.domain.model.PriceUpdateEntryType;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductionBooking;
import com.stpl.tech.scm.domain.model.RequestOrderItem;
import com.stpl.tech.scm.domain.model.SCMOrderPackaging;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.SkuPackagingKey;
import com.stpl.tech.scm.domain.model.SkuPriceDetail;
import com.stpl.tech.scm.domain.model.SkuToProductResponse;
import com.stpl.tech.scm.domain.model.StockEventType;
import com.stpl.tech.scm.domain.model.TaxDetail;
import com.stpl.tech.scm.domain.model.TransferOrder;
import com.stpl.tech.scm.domain.model.TransferOrderItem;
import com.stpl.tech.scm.domain.model.TransferOrderType;
import com.stpl.tech.scm.domain.model.TransferredAsset;
import com.stpl.tech.scm.domain.model.UnitDetail;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorDispatchLocation;
import com.stpl.tech.scm.notification.email.TransferBlockedNotification;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.entity.ContentType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.DateTimeConstants;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by Rahul Singh on 22-06-2016.
 */
@Service
public class TransferOrderManagementServiceImpl implements TransferOrderManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(TransferOrderManagementServiceImpl.class);

    @Autowired
    private TransferOrderManagementDao transferOrderManagementDao;

    @Autowired
    private PriceManagementDao priceDao;

    @Autowired
    private GoodsReceiveManagementService goodsReceiveManagementService;

    @Autowired
    private RequestOrderManagementService requestOrderManagementService;

    @Autowired
    private StockManagementService stockManagementService;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private ProductionBookingService productionBookingService;

    @Autowired
    private TaxDataCache taxCache;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    EnvProperties props;

    @Autowired
    private InventoryService inventoryService;

    @Autowired
    private SkuMappingService skuMappingService;

    @Autowired
    private FetchTransferOrderService fetchTransferOrderService;


    @Autowired
    private ServiceOrderManagementDao serviceOrderManagementDao;

    @Autowired
    private PurchaseOrderManagementService purchaseOrderService;

    @Autowired
    private RequestOrderManagementDao requestOrderManagementDao;

    @Autowired
    private SCMProductManagementService scmProductManagementService;

    @Autowired
    private SCMAssetManagementService scmAssetManagementService;

    @Autowired
    private SCMAssetManagementDao scmAssetManagementDao;

    @Autowired
    private StockManagementDao stockManagementDao;

    @Autowired
    private FileArchiveService fileArchiveService;

    @Autowired
    private SkuMappingDao skuMappingDao;

    @Autowired
    private PriceManagementService priceManagementService;


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Integer createTransferOrder(TransferOrder transferOrder, boolean external, String invoiceId)
            throws TransferOrderCreationException, InventoryUpdateException, SumoException, DataNotFoundException {
        preTransferOrderCreation(transferOrder);
        TransferOrderData transferOrderData = createTransfer(transferOrder, external, invoiceId);
        postTransferOrderCreation(transferOrderData,transferOrder);
        return transferOrderData.getId() != null ? transferOrderData.getId() : 1;
    }

    private TransferOrder preTransferOrderCreation(TransferOrder transferOrder) throws SumoException {
        UnitCategory unitCategory = masterDataCache.getUnitBasicDetail(transferOrder.getGeneratedForUnitId().getId()).getCategory();
        if(unitCategory!=UnitCategory.WAREHOUSE && unitCategory!=UnitCategory.KITCHEN){
            Unit unit = masterDataCache.getUnit(transferOrder.getGenerationUnitId().getId());
            if(unit.getClosure()!=null && unit.getClosure().equals(UnitClosureStateEnum.PROCESSING.name())){
                throw new SumoException("Unit closure in processing  "+unit.getClosure());
            }
        }

        boolean isCafe = SCMUtil.isCafe(masterDataCache.getUnitBasicDetail(transferOrder.getGenerationUnitId().getId()).getCategory());
        boolean isWareHouse = SCMUtil.isWareHouse(masterDataCache.getUnitBasicDetail(transferOrder.getGeneratedForUnitId().getId()).getCategory());
        boolean bypassCheckForMerchandise = checkForBypassCheckForMerchandise();
        if (isCafe && isWareHouse && !bypassCheckForMerchandise) {
            List<String> merchandiseSkus = checkForMerchandiseSkus(transferOrder);
            if (!merchandiseSkus.isEmpty()) {
                throw new SumoException("Can Not Transfer These Skus Out..!", "You Can not Transfer Out These Sku's ..!<b> " + Arrays.toString(merchandiseSkus.toArray()));
            }
        }
        StockEventDefinitionData latestNSOEvent = scmAssetManagementDao.getLatestNSOEventByUnit(transferOrder.getGenerationUnitId().getId(), StockEventStatusType.COMPLETED.value(), StockTakeSubType.NSO.value());
        if(Objects.nonNull(latestNSOEvent) && masterDataCache.getUnitBasicDetail(transferOrder.getGenerationUnitId().getId()).getHandOverDate() == null
                && SCMUtil.getDateDifferenceInHours(latestNSOEvent.getLastUpdationTime(),SCMUtil.getCurrentBusinessDate()) > 24 ){
            throw new SumoException("NSO Stock Take Event In Process. Please Go to Kettle Admin and put Handover Date to continue creating Transfer Orders");
        }

        Boolean isAssetOrder = transferOrder.getToType().name().equals(TransferOrderType.BROKEN_ASSET_TRANSFER.value())
                || transferOrder.getToType().name().equals(TransferOrderType.FIXED_ASSET_TRANSFER.value())
                || transferOrder.getToType().name().equals(TransferOrderType.RENOVATION_ASSET_TRANSFER.value());
        if (Boolean.TRUE.equals(isAssetOrder)) {
            List<Integer> assetIds = transferOrder.getTransferOrderItems().stream().map(TransferOrderItem::getAssociatedAssetId).collect(Collectors.toList());
            List<Integer> inTransitAssets = scmAssetManagementDao.checkIfTransferInProgress(assetIds);
            if (!inTransitAssets.isEmpty()) {
                String msg = "This Assets : " + inTransitAssets + " in This Transfer is already sent in other Transfer , Please Refresh Page. ";
                throw new SumoException(msg, "if Not Resolved By Refreshing Then Contact SCM Team");
            }
        }

        if (Boolean.FALSE.equals(checkForReceivingUnitDayClose(transferOrder.getGeneratedForUnitId().getId()))) {
            String msg = scmCache.getUnitDetail(transferOrder.getGeneratedForUnitId().getId()).getUnitName() + " Hasn't Done Day Close In Suitable Time.. ";
            Unit recevingUnit = masterDataCache.getUnit(transferOrder.getGeneratedForUnitId().getId());
            Unit transferrigUnit = masterDataCache.getUnit(transferOrder.getGenerationUnitId().getId());
            try {
                String errorMsg = "::::::::::TRANSFER BLOCKED::::::::::::: \n" +
                        "Transferring Unit :::::::::::: " + transferrigUnit.getName() + "(" + transferrigUnit.getId() + ")\n" +
                        "Receiving Unit ::::::::::::::: " + recevingUnit.getName() + "(" + recevingUnit.getId() + ")\n"
                        + "Please Do The Day Close To Avoid Future Transfers Getting BLocked !!";

                SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "SUMO",
                        SlackNotification.SUPPLY_CHAIN, errorMsg);
            } catch (Exception e) {
                LOG.error("Error While Sending Slack Notification For Blocked Transfer for Receiving Unit : {} , Transferring Unit : {}   ::::::::::", recevingUnit.getName()
                        , transferrigUnit.getName(), e);
            }

            try {
                List<String> toEmails = new ArrayList<>(Arrays.asList(recevingUnit.getUnitEmail()));
                TransferBlockedNotification notification = new TransferBlockedNotification(masterDataCache.getUnit(transferOrder.getGenerationUnitId().getId()),
                        toEmails, props.getEnvType(), masterDataCache.getUnit(transferOrder.getGeneratedForUnitId().getId()));
                notification.sendEmail();
            } catch (Exception e) {
                LOG.error("Error While Sending Transer Blocked Notification To Unit : {} ::::::::::", transferOrder.getGeneratedForUnitId().getId(), e);
            }

            throw new SumoException("Transfer BLocked !!", msg);
        }
        return transferOrder;
    }

    private boolean checkForBypassCheckForMerchandise(IdCodeName generatedBy) {
        if (Objects.nonNull(generatedBy) && Objects.nonNull(generatedBy.getId())) {
            String bypassUser = props.getByPassUsersForMerchandise();
            if (Objects.isNull(bypassUser) || bypassUser.equals("")) {
                return false;
            } else {
                return generatedBy.getId().equals(Integer.parseInt(bypassUser));
            }
        }
        return false;
    }

    private boolean checkForBypassCheckForMerchandise() {
        return props.getByPassFlagForMerchandise();
    }

    private TransferOrderData postTransferOrderCreation(TransferOrderData transferOrderData,TransferOrder transferOrder) throws SumoException {
        if (Objects.nonNull(transferOrderData.getRequestOrderData())) {
            RequestOrderData requestOrderData = requestOrderManagementDao.find(RequestOrderData.class, transferOrder.getRequestOrderId());
            if (Objects.nonNull(requestOrderData.getType()) && requestOrderData.getType().equals(CapexStatus.CAPEX.value())) {
                BigDecimal tax = Objects.isNull(transferOrderData.getTaxAmount()) ? BigDecimal.ZERO : transferOrderData.getTaxAmount();
                BigDecimal toTotalAmount = transferOrderData.getTotalAmount().setScale(2, RoundingMode.HALF_EVEN).add(tax.setScale(2, RoundingMode.HALF_EVEN));
                BigDecimal roTotalAmout = requestOrderData.getTotalAmount().setScale(2, RoundingMode.HALF_EVEN);
                BigDecimal roTODiff = toTotalAmount.subtract(roTotalAmout);
                if (roTODiff.compareTo(BigDecimal.ZERO) != 0) {
                    if (checkForBudget(requestOrderData, roTODiff).equals(Boolean.FALSE)) {
                        throw new SumoException("Budget Not Avaialable At Receiving Unit For This RO !!");
                    }
                    requestOrderManagementService.updateBudgetDetails(requestOrderData.getRequestUnitId(), roTODiff, transferOrderData.getId(),
                            BudgetAuditActions.TO_ID, requestOrderData.getAssetOrder(), transferOrderData.getGeneratedBy(), false);

                }

            }
        }


        if (transferOrderData.getToType().equals(TransferOrderType.BROKEN_ASSET_TRANSFER.value())
                || transferOrderData.getToType().equals(TransferOrderType.FIXED_ASSET_TRANSFER.value())
                || transferOrderData.getToType().equals(TransferOrderType.RENOVATION_ASSET_TRANSFER.value())) {
            if (transferOrderData.getType() != null && transferOrderData.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())) {
                if (transferOrder.getTotalCost() != null) {
                    BigDecimal total = new BigDecimal(transferOrder.getTotalCost().toString());
                    LOG.info("Total Amount for this TO is {}", total);
                    if (total.compareTo(BigDecimal.ZERO) > 0) {
                        LOG.info("Updating budget for FIXED ASSETS Create Transfer");
                        if (!updateBudgetForAssetTransfer(transferOrderData, total, true)) {
                            throw new SumoException("Error in Budget Updation", "Cannot Update Budget for <b>FIXED_ASSETS(FA_Equipment)</b> while Creating Transfer..!");
                        }
                    }
                }
            }
        }

        updateCounterForMonkConsumableStockTransferred(transferOrder,transferOrderData.getId(),false);



        return transferOrderData;

    }


    private void updateCounterForMonkConsumableStockTransferred(TransferOrder transferOrder, Integer toId,Boolean isCancelled) {
        try {
            Map<Integer, Float> monkConsumableStockMap = new HashMap<>();
            if (transferOrder.getGeneratedForUnitId().getId() != transferOrder.getGenerationUnitId().getId()
                    && Boolean.TRUE.equals(props.isMaintenanceUnit(transferOrder.getGenerationUnitId().getId()))) {
                monkConsumableStockMap = transferOrder.getTransferOrderItems().stream().filter(
                        transferOrderItem -> props.isMonkConsumableItem(scmCache.getSkuDefinition(transferOrderItem.getSkuId()).getLinkedProduct().getId())
                ).collect(Collectors.toMap(TransferOrderItem::getSkuId, TransferOrderItem::getTransferredQuantity));
                if (!monkConsumableStockMap.isEmpty()) {
                    updateCounterForMonkConsumable(monkConsumableStockMap, transferOrder.getGenerationUnitId().getId(),
                            transferOrder.getGeneratedForUnitId().getId(),isCancelled);
                }
            }
        } catch (Exception e) {
            LOG.error("Couldn't Update Consumable Stock  Counter For  Transfer Order Id :::: {} ", toId);
        }
    }



    private void updateCounterForMonkConsumable(Map<Integer,Float> monkConsumableStockMap , Integer unitId , Integer receivingUnitId,Boolean isCancelled){
         List<ConsumableStockState> monkConsumableState = stockManagementDao.getConsumableStockState(monkConsumableStockMap.
                 keySet().stream().toList(),unitId);
         Boolean isCafeReceiving = SCMUtil.isCafe(masterDataCache.getUnitBasicDetail(receivingUnitId).getCategory());
         for(ConsumableStockState consumableStockState : monkConsumableState){
             if(Boolean.TRUE.equals(isCafeReceiving)){
                 if (Boolean.FALSE.equals(isCancelled)) {
                     consumableStockState.setGoodStock(AppUtils.subtract(consumableStockState.getGoodStock(),
                             BigDecimal.valueOf(monkConsumableStockMap.get(consumableStockState.getSkuId()))));
                 } else {
                     consumableStockState.setGoodStock(AppUtils.add(consumableStockState.getGoodStock(),
                             BigDecimal.valueOf(monkConsumableStockMap.get(consumableStockState.getSkuId()))));
                 }

             }else{
                 if(Boolean.FALSE.equals(isCancelled)){
                     consumableStockState.setBadStock(AppUtils.subtract(consumableStockState.getBadStock(),
                             BigDecimal.valueOf(monkConsumableStockMap.get(consumableStockState.getSkuId()))));
                 }else{
                     consumableStockState.setBadStock(AppUtils.add(consumableStockState.getBadStock(),
                             BigDecimal.valueOf(monkConsumableStockMap.get(consumableStockState.getSkuId()))));
                 }
             }

         }
         transferOrderManagementDao.flush();
    }



    private List<String> checkForMerchandiseSkus(TransferOrder transferOrder) {
        List<String> result = new ArrayList<>();
        List<Integer> skuIds = Arrays.asList(218, 902, 1192, 1504, 1505, 1502, 1503, 1477, 1476, 1587, 1957, 1960, 2226, 2345, 2346,
                2437, 2792, 2726, 2794, 2620, 2242, 2445, 3139, 3140, 3141, 3142, 3014, 2386, 2385, 3068, 3065, 2919, 2920, 2921, 3589, 3588, 3587,
                3407, 3605, 3457, 3456, 2580, 2581, 3607, 3608, 3455, 2243, 3672, 3673, 3669, 3671, 3575, 3818);
        for (TransferOrderItem transferOrderItem : transferOrder.getTransferOrderItems()) {
            if (Objects.nonNull(transferOrderItem.getSkuId()) && skuIds.contains(transferOrderItem.getSkuId())) {
                result.add(scmCache.getSkuDefinition(transferOrderItem.getSkuId()).getSkuName());
            }
        }
        return result;
    }


    private Boolean checkForBudget(RequestOrderData requestOrderData, BigDecimal totalDiffAmount) {
        CapexAuditDetailData capexAuditDetail = serviceOrderManagementDao.findCapexAuditData(requestOrderData.getId());
        String departmentName = Boolean.valueOf(AppUtils.getStatus(requestOrderData.getAssetOrder())).equals(Boolean.TRUE) ? AppConstants.FIXED_ASSETS : AppConstants.GOODS;
        CapexBudgetDetailData capexBudgetData = serviceOrderManagementDao.findBudgetUnit(requestOrderData.getRequestUnitId(), departmentName);
        BigDecimal remainingAmount = capexBudgetData.getRemainingAmount();
        if (remainingAmount.compareTo(totalDiffAmount) < 0) {
            return false;
        }
        return true;
    }


    private Boolean checkForReceivingUnitDayClose(Integer rUnit) {
        if (checkIfUnitIsExcludedForDayCloseCheck(rUnit).equals(Boolean.TRUE)) {
            return true;
        }
        Unit unit = masterDataCache.getUnit(rUnit);
        if (SCMUtil.isKitchen(scmCache.getUnitDetail(rUnit)) || isFullfilmentWH(rUnit)) {
            SCMDayCloseEventData dayCloseEventData = stockManagementDao.getLastDayCloseEvent(rUnit, StockEventType.WH_CLOSING, true);
            if (Objects.isNull(dayCloseEventData) || validateDaysDiff(dayCloseEventData, 3)) {
                return true;
            } else {
                return false;
            }
        } else if (isOnceInAWeekDayCloseUnit(unit)) {
            //HardCoded For Training Unit
            StockEventType eventType = (rUnit == 26003 || SCMUtil.isOffice(unit.getFamily()) || unit.getFamily().equals(UnitCategory.CHAI_MONK)) ?
                    StockEventType.OPENING : StockEventType.WH_CLOSING;
            SCMDayCloseEventData dayCloseEventData = stockManagementDao.getLastDayCloseEvent(rUnit, eventType, true);
            if (Objects.isNull(dayCloseEventData) || Objects.isNull(validateIfDayCloseDoneInWeek(dayCloseEventData).getBlocking()) || validateIfDayCloseDoneInWeek(dayCloseEventData).getBlocking().equals(Boolean.FALSE) ) {
                return true;
            } else {
                return false;
            }
        } else {
            return true;
        }
    }

    private Boolean isOnceInAWeekDayCloseUnit(Unit unit) {
        return SCMUtil.isOffice(unit.getFamily()) || SCMUtil.isWareHouse(unit.getFamily()) || unit.getFamily().equals(UnitCategory.CHAI_MONK);
    }

    @Override
    public Boolean validateIfDayCloseDoneInMonth(SCMDayCloseEventData eventData, Boolean isWarning) {
        Integer blockerDay = isWarning.equals(Boolean.TRUE) ? 23 : 25;
        Date currentDay = SCMUtil.getCurrentTimestamp();
        Integer currentMonth = currentDay.getMonth();
        Date lastDayCloseDay = eventData.getUpdatedAt();
        Integer dayCloseMonth = lastDayCloseDay.getMonth();
        Integer currentDayOfMonth = currentDay.getDate();
        LOG.info("Current Day :::::: {} , Last Day Close Day :::::: {}", currentDay, lastDayCloseDay);
        if (currentMonth.equals(dayCloseMonth)) {
            return true;
        }
        if ((Math.abs(currentMonth - dayCloseMonth) % 10) == 1) {
            if (currentDayOfMonth < blockerDay) {
                return true;
            } else {
                return false;
            }
        }
        return false;
    }
    @Override
    public Disclaimer validateIfDayCloseDoneInWeek(SCMDayCloseEventData eventData){
        Disclaimer res = new Disclaimer();
        Date currentDay = SCMUtil.getCurrentTimestamp();
        Date lastDayCloseDay = eventData.getUpdatedAt();
        Integer lastDayCloseDays = SCMUtil.getAbsDaysDiff(lastDayCloseDay, currentDay);
        if(SCMUtil.isBefore(currentDay,SCMUtil.getDate("2023-08-23","yyyy-MM-dd"))){
            res.setWarning(false);
            res.setBlocking(false);
            return res;
        }
        Integer dayNumber = currentDay.getDay();

        LocalDate currentDate = new LocalDate();
        Integer wednesdayDays = Days.daysBetween(currentDate.withDayOfWeek(DateTimeConstants.WEDNESDAY), currentDate).getDays();

        if (wednesdayDays < 0) {
            wednesdayDays = wednesdayDays + 7;
        }
        Integer previousWednesdayDays = wednesdayDays + 7;

        if (lastDayCloseDays <= wednesdayDays) {
            res.setWarning(false);
            res.setBlocking(false);
            return res;
        } else {
            if (dayNumber == 3 || dayNumber == 4) {//wednesday and thursday
                if (lastDayCloseDays <= previousWednesdayDays) {
                    res.setWarning(true);
                    res.setBlocking(false);
                    return res;
                } else {
                    res.setWarning(false);
                    res.setBlocking(true);
                    return res;
                }
            } else {
                res.setWarning(false);
                res.setBlocking(true);
                return res;
            }
        }
    }


    @Override
    public Boolean isFullfilmentWH(Integer unitId) {
        Set<String> regions = masterDataCache.getAllRegions();
        List<Integer> fullfilmentWhs = new ArrayList<>();
        for (String region : regions) {
            String key = region + "-" + "WAREHOUSE";
            fullfilmentWhs.add(scmCache.getRegionFulfillmentMapping(key));
        }

        return fullfilmentWhs.contains(unitId);
    }

    @Override
    public Boolean checkIfUnitIsExcludedForDayCloseCheck(Integer unitId) {
        return transferOrderManagementDao.checkIfUnitIsExcludedForDayCloseCheck(unitId);
    }

    private Boolean validateDaysDiff(SCMDayCloseEventData eventData, Integer acceptableDiff) {
        Date currentDay = SCMUtil.getCurrentTimestamp();
        Date lastDayCloseDay = eventData.getUpdatedAt();
        long diffInMillies = Math.abs(currentDay.getTime() - lastDayCloseDay.getTime());
        long diff = TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS);
        if (diff >= acceptableDiff) {
            return false;
        }
        return true;
    }

    private boolean updateBudgetForAssetTransfer(TransferOrderData transferOrderData, BigDecimal total, Boolean isCreatedOrCancelled) {
        LOG.info("Updating Budget Audit Details For Fixed Asset Transfer");
        try {
            CapexAuditDetailData capexAuditDetail = serviceOrderManagementDao.findCapexAuditData(transferOrderData.getGeneratedForUnitId());
            CapexBudgetDetailData capexBudgetData = serviceOrderManagementDao.findBudgetUnit(transferOrderData.getGeneratedForUnitId(), AppConstants.FIXED_ASSETS);
            List<String> actions = Arrays.asList(BudgetAuditActions.REMAINING_AMOUNT.value(), BudgetAuditActions.RUNNING_AMOUNT.value());
            for (String action : actions) {
                BudgetAuditDetailData budgetAuditDetail = new BudgetAuditDetailData();
                budgetAuditDetail.setCapexAuditId(capexAuditDetail.getId());
                budgetAuditDetail.setCapexBudgetDetailId(capexBudgetData.getId());
                budgetAuditDetail.setActionBy(transferOrderData.getGeneratedBy());
                if (isCreatedOrCancelled) {
                    budgetAuditDetail.setAction(BudgetAuditActions.CREATED.value());
                } else {
                    budgetAuditDetail.setAction(BudgetAuditActions.CANCELLED.value());
                }
                budgetAuditDetail.setKeyType(BudgetAuditActions.TO_ID.value());
                budgetAuditDetail.setKeyValue(transferOrderData.getId());
                budgetAuditDetail.setActionTime(SCMUtil.getCurrentTimestamp());
                if (action.equalsIgnoreCase(BudgetAuditActions.REMAINING_AMOUNT.value())) {
                    budgetAuditDetail.setAmountType(BudgetAuditActions.REMAINING_AMOUNT.value());
                    budgetAuditDetail.setPreviousValue(capexBudgetData.getRemainingAmount());
                    if (isCreatedOrCancelled) {
                        budgetAuditDetail.setFinalValue(capexBudgetData.getRemainingAmount().subtract(total));
                        budgetAuditDetail.setActionType(BudgetAuditActions.REDUCTION.value());
                    } else {
                        budgetAuditDetail.setFinalValue(capexBudgetData.getRemainingAmount().add(total));
                        budgetAuditDetail.setActionType(BudgetAuditActions.ADDITION.value());
                    }
                } else {
                    budgetAuditDetail.setAmountType(BudgetAuditActions.RUNNING_AMOUNT.value());
                    budgetAuditDetail.setPreviousValue(capexBudgetData.getRunningAmount());
                    if (isCreatedOrCancelled) {
                        budgetAuditDetail.setFinalValue(capexBudgetData.getRunningAmount().add(total));
                        budgetAuditDetail.setActionType(BudgetAuditActions.ADDITION.value());
                    } else {
                        budgetAuditDetail.setFinalValue(capexBudgetData.getRunningAmount().subtract(total));
                        budgetAuditDetail.setActionType(BudgetAuditActions.REDUCTION.value());
                    }
                }
                serviceOrderManagementDao.add(budgetAuditDetail, true);
            }
            if (isCreatedOrCancelled) {
                capexBudgetData.setRemainingAmount(capexBudgetData.getRemainingAmount().subtract(total));
                capexBudgetData.setRunningAmount(capexBudgetData.getRunningAmount().add(total));
            } else {
                capexBudgetData.setRemainingAmount(capexBudgetData.getRemainingAmount().add(total));
                capexBudgetData.setRunningAmount(capexBudgetData.getRunningAmount().subtract(total));
            }
            CapexBudgetDetailData finalData = serviceOrderManagementDao.update(capexBudgetData, true);
            if (!purchaseOrderService.validateBudgetAmounts(finalData)) {
                LOG.info("Budget Values went into negative...! Please Check");
                return false;
            }
            return true;
        } catch (Exception e) {
            LOG.error("Exception Occurred while updating budget for Fixed Assets Settle ::: ", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<ProductionBooking> bulkProductionBooking(Integer unitId, Map<Integer, GoodsReceivedItem> productToQuantityMap, IdCodeName generatedBy, Map<Integer, ProductionBooking> skuToProductionBooking) throws DataNotFoundException, SumoException {
        List<ProductionBooking> productionBookingList = new ArrayList<>();
        List<String> errors = new ArrayList<>();
        Unit unit = masterDataCache.getUnit(unitId);
        LOG.info("Checking for the family type {}", unit.getFamily());
        if (SCMUtil.isKitchen(unit.getFamily())) {
            LOG.info("START CALCULATING CONSUMPTION FOR REQUESTED RECIPE ITEMS");
            long startCalculateConsumption = System.currentTimeMillis();
            for (Integer productId : productToQuantityMap.keySet()) {
                HashMap<Integer, IdCodeName> map = new HashMap<>();
                ProductDefinition product = scmCache.getProductDefinition(productId);
                GoodsReceivedItem item = productToQuantityMap.get(productId);
                if (product == null) {
                    SkuDefinition skuDefinition = scmCache.getSkuDefinition(item.getSkuId());
                    product = scmCache.getProductDefinition(skuDefinition.getLinkedProduct().getId());
                }
                if (product.isRecipeRequired()) {
                    productionBookingList = calculateConsumptions(unitId, product, item.getTransferredQuantity(),
                            productionBookingList, map, errors, item.getExpiryDate(), generatedBy);
                }
            }

            long endCalculateConsumption = System.currentTimeMillis();
            LOG.info("Calculating Consumption Time   is {} ms", endCalculateConsumption - startCalculateConsumption);

            LOG.info("CALCULATION OF CONSUMPTION FOR REQUESTED RECIPE ITEMS IS DONE");
            if (errors != null && errors.size() > 0) {
                String message = "Error while Creating Transfer Order Please Update Mapping For the Requested Products For Production Booking" + "\n"
                        + Arrays.toString(errors.toArray());
                SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "SUMO",
                        SlackNotification.SUPPLY_CHAIN, message);
                throw new SumoException(message);
            }

            long startProductionBooking = System.currentTimeMillis();
            LOG.info("START PRODUCTION BOOKING FOR REQUESTED RECIPES");
            if (productionBookingList.size() > 0) {
                List<String> productionBookingErrors = new ArrayList<>();
                LOG.info("Production Booking List Size  is {}", productionBookingList.size());
                for (ProductionBooking productionBooking : productionBookingList) {
                    try {
                        long startProductionBookingAddBooking = System.currentTimeMillis();
                        productionBookingService.addBooking(productionBooking);
                        skuToProductionBooking.put(productionBooking.getSkuId(), productionBooking);
                        LOG.info("BOOKING SUCCESSFULLY DONE FOR PRODUCTS!");
                        long endProductionBookingAddBooking = System.currentTimeMillis();
                        LOG.info("Production Booking Time for  item : {} is {} ms ", productionBooking.getProductName(), endProductionBookingAddBooking - startProductionBookingAddBooking);

                    } catch (Exception e) {
                        LOG.info("PRODUCTION BOOKING CANNOT BE DONE!");
                        productionBookingErrors.add(e.getMessage());
                        //throw new SumoException(e.getMessage());
                    }

                }
                if (productionBookingErrors != null && productionBookingErrors.size() > 0) {
                    String message = "Error while Creating Transfer Order Please Update Price For the Requested Products For Production Booking" + "\n"
                            + Arrays.toString(productionBookingErrors.toArray());
                    throw new SumoException(message);
                }
            }
            long endProductionBooking = System.currentTimeMillis();
            LOG.info("Production Booking  is done in {} ms", endProductionBooking - startProductionBooking);
            LOG.info("PRODUCTION BOOKING FOR REQUESTED RECIPE ITEMS IS DONE");
        }
        return productionBookingList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public TransferOrderData createTransferOrder(TransferOrder transferOrder, boolean external, String invoiceId, List<ProductionBooking> productionBookingList) throws SumoException, DataNotFoundException, InventoryUpdateException, TransferOrderCreationException {

        UnitCategory unitCategory = masterDataCache.getUnitBasicDetail(transferOrder.getGeneratedForUnitId().getId()).getCategory();
        if(unitCategory!=UnitCategory.WAREHOUSE && unitCategory!=UnitCategory.KITCHEN){
            Unit unit = masterDataCache.getUnit(transferOrder.getGenerationUnitId().getId());
            if(unit.getClosure()!=null && unit.getClosure().equals(UnitClosureStateEnum.PROCESSING.name())){
                throw new SumoException("Unit closure in processing (Only transfer to WH and Kitchen allowed), unit id :  "+unit.getId());
            }
        }

        long startCreateTransfer = System.currentTimeMillis();
        long orderStart = System.currentTimeMillis();
        long stepStart = System.currentTimeMillis();
        LOG.info("##### TRANSFER_ORDER - Start for : {}", transferOrder.getId());
        Date currentTime = SCMUtil.getCurrentTimestamp();
        transferOrder.setGenerationTime(currentTime);
        transferOrder.setInitiationTime(currentTime);
        transferOrder.setLastUpdateTime(currentTime);
        RequestOrderData requestOrderData = null;
        if (transferOrder.getRequestOrderId() != null) {
            requestOrderData = transferOrderManagementDao.find(RequestOrderData.class,
                    transferOrder.getRequestOrderId());
        }

        Unit unit = masterDataCache.getUnit(transferOrder.getGenerationUnitId().getId());
        LOG.info("Checking for the family type {}", unit.getFamily());
        if (SCMUtil.isKitchen(unit.getFamily())) {
            if (requestOrderData != null) {
                if (OrderTransferType.valueOf(requestOrderData.getTransferType()).equals(OrderTransferType.INVOICE)) {
                    requestOrderData.setStatus(SCMOrderStatus.SETTLED.value());
                    transferOrderManagementDao.update(requestOrderData, false);
                    TransferOrderData transferOrderData = SCMDataConverter.convert(transferOrder, requestOrderData, false, masterDataCache);
                    return transferOrderData;
                }
            }
        }


        validateTransferItems(transferOrder);
        if (validateRequestForTransfer(requestOrderData)) {
            stepStart = System.currentTimeMillis();
            TransferOrderData transferOrderData = transferOrderManagementDao
                    .add(SCMDataConverter.convert(transferOrder, requestOrderData, false, masterDataCache), true);
            if (transferOrderData != null) {
                List<TransferOrderItemData> transferOrderItemDatas = new ArrayList<TransferOrderItemData>();
                List<Integer> assetIds = new ArrayList<>();
                for (TransferOrderItem transferOrderItem : transferOrder.getTransferOrderItems()) {
                    RequestOrderItemData requestOrderItemData = null;
                    if (requestOrderData != null) {
                        requestOrderItemData = transferOrderManagementDao.find(RequestOrderItemData.class,
                                transferOrderItem.getRequestOrderItemId());
                        requestOrderItemData.setTransferredQuantity(
                                SCMUtil.convertToBigDecimal(transferOrderItem.getTransferredQuantity()));
                        requestOrderItemData = transferOrderManagementDao.update(requestOrderItemData, false);
                    }
                    TransferOrderItemData transferOrderItemData = SCMDataConverter.convert(transferOrderItem,
                            requestOrderItemData, transferOrderData, false);

                    if (external) {
                        transferOrderItemData.setNegotiatedUnitPrice(
                                SCMUtil.convertToBigDecimal(transferOrderItem.getNegotiatedUnitPrice()));
                        transferOrderItemData
                                .setUnitPrice(SCMUtil.convertToBigDecimal(transferOrderItem.getUnitPrice()));
                    }
					/*
						Set current value of asset as Price
					 */
                    if (transferOrder.isAssetOrder()) {
                        setPriceForAsset(transferOrderItemData, transferOrderItem);
                        assetIds.add(transferOrderItemData.getAssociatedAssetId());

                    }

                    transferOrderItemData = transferOrderManagementDao.add(transferOrderItemData, false);

                    List<SCMOrderPackagingData> scmOrderPackagingDatas = new ArrayList<SCMOrderPackagingData>();


                    for (SCMOrderPackaging scmOrderPackaging : transferOrderItem.getPackagingDetails()) {
                        SCMOrderPackagingData scmOrderPackagingData = SCMDataConverter.convert(scmOrderPackaging, null,
                                transferOrderItemData);
                        scmOrderPackagingData = transferOrderManagementDao.add(scmOrderPackagingData, false);
                        scmOrderPackagingDatas.add(scmOrderPackagingData);
                    }
                    transferOrderItemData.setPackagingDetails(scmOrderPackagingDatas);
                    transferOrderItemDatas.add(transferOrderItemData);
                }
                if (assetIds.size() > 0) {
                    scmAssetManagementService.setInTransit(assetIds, true);
                }
                transferOrderData.setTransferOrderItemDatas(transferOrderItemDatas);
                TransferOrder rec = fetchTransferOrderService.getTransferOrderDetail(transferOrderData.getId());
                fillDrillDowns(rec, transferOrder);
                if (transferOrderData.getGeneratedForUnitId() != transferOrderData.getGenerationUnitId() && !external) {
                    TransferOrder finalOutput = priceDao.reduceConsumable(rec, false);
                    setPricesAndTaxes(transferOrderData, finalOutput, invoiceId);
                    saveTransferOrderItemDrillDowns(finalOutput, transferOrderItemDatas);
                } else {
                    setPricesAndTaxes(transferOrderData, rec, invoiceId);
                    saveTransferOrderItemDrillDowns(rec, transferOrderItemDatas);
                }
                if (!external) {
                    GoodsReceivedData goodsReceivedData = goodsReceiveManagementService
                            .createGoodReceiveFromTransferOrder(transferOrderData, requestOrderData,
                                    transferOrderItemDatas, null);
                    if (goodsReceivedData != null) {
                        if (requestOrderData != null) {
                            requestOrderData.setStatus(SCMOrderStatus.TRANSFERRED.value());
                            requestOrderData.setLastUpdateTime(currentTime);
                            transferOrderManagementDao.update(requestOrderData, false);
                        }
                    }
                }
                transferOrderManagementDao.flush();

                if (transferOrderData.getGeneratedForUnitId() != transferOrderData.getGenerationUnitId() && !external) {
                    publishTransferOrder(transferOrderData, false);
                }
                if (transferOrderData.getRequestOrderData() != null) {
                    sendZeroTransferNotification(transferOrderData);
                }
                LOG.info("##### TRANSFER_ORDER - End for : {}, took {}", transferOrder.getId(), System.currentTimeMillis() - orderStart);
                return transferOrderData;
            }
            LOG.info("##### TRANSFER_ORDER - End for : {}, took {}", transferOrder.getId(), System.currentTimeMillis() - orderStart);


        } else {
            throw new SumoException("Request order is in " + requestOrderData.getStatus() + " state.");
        }
        LOG.info("##### TRANSFER_ORDER - End for : {}, took {}", transferOrder.getId(), System.currentTimeMillis() - orderStart);
        return null;
    }

    @Override
    public List<ProductionBooking> calculateConsumptions(Integer generationUnitId, ProductDefinition pd, Float quantity,
                                                         List<ProductionBooking> productionBookingList, HashMap<Integer, IdCodeName> map,
                                                         List<String> errors, Date expiryDate, IdCodeName generatedBy) throws DataNotFoundException, SumoException {
        long startCalculateConsumptionItem = System.currentTimeMillis();
        LOG.info("CALCULATING CONSUMPTION FOR REQUESTED RECIPE {}", pd.getProductName());
        ProductionBooking productionBooking = productionBookingService.calculateConsumption(generationUnitId, pd.getProductId(), SCMUtil.convertToBigDecimal(quantity));
        List<BookingConsumption> bookingConsumptions = productionBooking.getBookingConsumption();
        setProductMap(bookingConsumptions, map, productionBooking, generationUnitId, errors);
        LOG.info("CONSUMPTION FOR REQUESTED RECIPE {} IS CALCULATED SUCCESSFULLY", pd.getProductName());
        if (errors.size() == 0) {
            setProductionBooking(bookingConsumptions, map);
            productionBooking.setGeneratedBy(generatedBy);
            if (Objects.nonNull(expiryDate)) {
                productionBooking.setExpiryDate(expiryDate);
            } else {
                productionBooking.setExpiryDate(AppUtils.createExpiryDate(AppUtils.getCurrentTimestamp(), pd.getShelfLifeInDays()));
            }
            productionBooking.setAutoBooking(SCMServiceConstants.SCM_CONSTANT_YES);
            productionBookingList.add(productionBooking);
            LOG.info("SUCCESSFULLY ADDED REQUESTED RECIPE {} IN PRODUCTION LIST FOR TO", pd.getProductName());
            long endCalculateConsumptionItem = System.currentTimeMillis();
            LOG.info("Calculating Consumption Time for Product : {}  is {} ms", pd.getProductName(), endCalculateConsumptionItem - startCalculateConsumptionItem);
        }
        return productionBookingList;
    }

    private TransferOrderData createTransfer(TransferOrder transferOrder, boolean external, String invoiceId)
            throws TransferOrderCreationException, InventoryUpdateException, SumoException, DataNotFoundException {
        List<ProductionBooking> productionBookingList = new ArrayList<>();
        List<String> errors = new ArrayList<>();
        Unit unit = masterDataCache.getUnit(transferOrder.getGenerationUnitId().getId());
        LOG.info("Checking for the family type {}", unit.getFamily());
        if (SCMUtil.isKitchen(unit.getFamily())) {
            LOG.info("START CALCULATING CONSUMPTION FOR REQUESTED RECIPE ITEMS");
            long startCalculateConsumption = System.currentTimeMillis();
            LOG.info("TransferOrderItems Size : {} for requestOrderID : {}", transferOrder.getTransferOrderItems().size(), transferOrder.getRequestOrderId());
            for (TransferOrderItem item : transferOrder.getTransferOrderItems()) {
                HashMap<Integer, IdCodeName> map = new HashMap<>();
                ProductDefinition pd = scmCache.getProductDefinition(item.getProductId());
                if (pd == null) {
                    SkuDefinition skuDefinition = scmCache.getSkuDefinition(item.getSkuId());
                    pd = scmCache.getProductDefinition(skuDefinition.getLinkedProduct().getId());
                }
                if (pd.isRecipeRequired()) {
                    productionBookingList = calculateConsumptions(transferOrder.getGenerationUnitId().getId(), pd, item.getTransferredQuantity()
                            , productionBookingList, map, errors, item.getExpiryDate(), transferOrder.getGeneratedBy());
                }
            }
            long endCalculateConsumption = System.currentTimeMillis();
            LOG.info("Calculating Consumption Time  for RequestOrderId {} is {} ms", transferOrder.getRequestOrderId(), endCalculateConsumption - startCalculateConsumption);

            LOG.info("CALCULATION OF CONSUMPTION FOR REQUESTED RECIPE ITEMS IS DONE");
            if (errors != null && errors.size() > 0) {
                String message = "Error while Creating Transfer Order Please Update Mapping For the Requested Products For Production Booking" + "\n"
                        + Arrays.toString(errors.toArray());
                SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "SUMO",
                        SlackNotification.SUPPLY_CHAIN, message);
                throw new SumoException(message);
            }

            long startProductionBooking = System.currentTimeMillis();
            LOG.info("START PRODUCTION BOOKING FOR REQUESTED RECIPES");
            if (productionBookingList.size() > 0) {
                List<String> productionBookingErrors = new ArrayList<>();
                LOG.info("Production Booking List Size for Request Order ID {} is {}", transferOrder.getRequestOrderId(), productionBookingList.size());
                for (ProductionBooking productionBooking : productionBookingList) {
                    try {
                        long startProductionBookingAddBooking = System.currentTimeMillis();
                        productionBookingService.addBooking(productionBooking);
                        for (TransferOrderItem item : transferOrder.getTransferOrderItems()) {
                            if (item.getRequestOrderItemId() == null) {
                                SkuDefinition skuDefinition = scmCache.getSkuDefinition(item.getSkuId());
                                ProductDefinition pd = scmCache.getProductDefinition(skuDefinition.getLinkedProduct().getId());
                                item.setProductId(pd.getProductId());
                            }
                            if (item.getProductId() == productionBooking.getProductId()) {
                                item.setProductionId(productionBooking.getBookingId());
                                if (Objects.nonNull(item.getRequestOrderItemId())) {
                                    //updates production booking done flag and production quantity on ro item level
                                    RequestOrderItemData requestOrderItemData = requestOrderManagementDao.find(RequestOrderItemData.class, item.getRequestOrderItemId());
                                    requestOrderItemData.setProductionBookingCompleted(AppUtils.setStatus(true));
                                    requestOrderItemData.setProductionBookingQuantity(productionBooking.getQuantity());
                                    requestOrderManagementDao.update(requestOrderItemData, false);
                                }

                            }


                        }

                        LOG.info("BOOKING SUCCESSFULLY DONE FOR PRODUCTS!");
                        long endProductionBookingAddBooking = System.currentTimeMillis();
                        LOG.info("Production Booking Time for  item : {} for request order ID : {} is {} ms ", productionBooking.getProductName(), transferOrder.getRequestOrderId(), endProductionBookingAddBooking - startProductionBookingAddBooking);

                    } catch (Exception e) {
                        LOG.info("PRODUCTION BOOKING CANNOT BE DONE!");
                        productionBookingErrors.add(e.getMessage());
                        //throw new SumoException(e.getMessage());
                    }

                }
                if (productionBookingErrors != null && productionBookingErrors.size() > 0) {
                    String message = "Error while Creating Transfer Order Please Update Price For the Requested Products For Production Booking" + "\n"
                            + Arrays.toString(productionBookingErrors.toArray());
                    throw new SumoException(message);
                }
            }
            long endProductionBooking = System.currentTimeMillis();
            LOG.info("Production Booking For RequestOrderId {} is done in {} ms", transferOrder.getRequestOrderId(), endProductionBooking - startProductionBooking);
            LOG.info("PRODUCTION BOOKING FOR REQUESTED RECIPE ITEMS IS DONE");

        }
        return createTransferOrder(transferOrder, external, invoiceId, productionBookingList);

    }

    private void setPriceForAsset(TransferOrderItemData transferOrderItemData, TransferOrderItem transferOrderItem) {
        LOG.info("setting asset Prices");
        try {
            if (transferOrderItem.getNegotiatedUnitPrice() != null) {
                transferOrderItemData.setNegotiatedUnitPrice(new BigDecimal(transferOrderItem.getNegotiatedUnitPrice().toString()));
            }
            if (transferOrderItem.getUnitPrice() != null) {
                transferOrderItemData.setUnitPrice(new BigDecimal(transferOrderItem.getUnitPrice().toString()));
            }
            if (transferOrderItem.getItemTax() != null) {
                transferOrderItemData.setTaxAmount(new BigDecimal(transferOrderItem.getItemTax().toString()));
            }
        } catch (Exception e) {
            LOG.error("Error Occurred While Setting unit price and tax amount for assets :: ", e);
        }
    }


    public void setProductMap(List<BookingConsumption> consumptions, HashMap<Integer, IdCodeName> map, ProductionBooking booking, int unitId, List<String> errors) throws SumoException {
        boolean flag;
        for (BookingConsumption bookingConsumption : consumptions) {
            if (!map.containsKey(bookingConsumption.getProductId())) {
                flag = false;
                for (IdCodeName skuList : bookingConsumption.getAvailableSkuList()) {
                    ProductionBookingMappingData mappingData = productionBookingService.productionUnitMappingItems(booking.getProductId(), unitId, skuList.getId(), booking.getProfile());
                    if (mappingData != null) {
                        flag = true;
                        map.put(bookingConsumption.getProductId(), SCMUtil.generateIdCodeName(mappingData.getLinkedSkuId(), mappingData.getLinkedUnitOfMeasure(), mappingData.getLinkedSkuName()));
                        break;
                    }
                }
            } else {
                flag = true;
            }
            if (!flag) {
                String message = "Could Not Find Mapping For Product " + booking.getProductName() + "\n";
                errors.add(message);
                break;
            }
            if (bookingConsumption.getBookingConsumption().size() > 0 && (bookingConsumption.isAutoProduction() || bookingConsumption.isMappedAutoProduction())) {
                setProductMap(bookingConsumption.getBookingConsumption(), map, booking, unitId, errors);
            }
        }
    }

    public void setProductionBooking(List<BookingConsumption> booking, Map<Integer, IdCodeName> map) {
        for (BookingConsumption bookingConsumption : booking) {
            IdCodeName skuData = map.get(bookingConsumption.getProductId());
            if (skuData != null) {
                bookingConsumption.setSkuId(skuData.getId());
                bookingConsumption.setSkuName(skuData.getName());
                bookingConsumption.setUnitOfMeasure(skuData.getCode());
            }
            if (bookingConsumption.getBookingConsumption().size() > 0 && (bookingConsumption.isAutoProduction() || bookingConsumption.isMappedAutoProduction())) {
                setProductionBooking(bookingConsumption.getBookingConsumption(), map);
            }
        }
    }

    private void validateTransferItems(TransferOrder transfer) throws TransferOrderCreationException {
        IdCodeName transferringUnit = transfer.getGenerationUnitId();
        IdCodeName receivingUnit = transfer.getGeneratedForUnitId();
        boolean isCafe = SCMUtil.isCafe(masterDataCache.getUnitBasicDetail(transferringUnit.getId()).getCategory());
        if (isCafe && !transferringUnit.getId().equals(receivingUnit.getId())) {
            transfer.getTransferOrderItems().forEach(o -> {
                o.setProductId(scmCache.getSkuDefinition(o.getSkuId()).getLinkedProduct().getId());
            });
            if (transfer.isAssetOrder()) {
                return;
            }
            List<Integer> keyIds = transfer.getTransferOrderItems().stream()
                    .map(TransferOrderItem::getProductId).distinct()
                    .collect(Collectors.toList());

            List<CostDetailData> inventory = priceDao.getCurrentPrices(PriceUpdateEntryType.PRODUCT,
                    transferringUnit.getId(), keyIds, false);
            List<String> unavailableInventory = new ArrayList<>();
            if (inventory != null) {
                Map<Integer, BigDecimal> inventoryLookup = inventory.stream()
                        .collect(Collectors.groupingBy(CostDetailData::getKeyId,
                                Collectors.reducing(BigDecimal.ZERO, CostDetailData::getQuantity, SCMUtil::add)
                        ));
                for (TransferOrderItem item : transfer.getTransferOrderItems()) {
                    BigDecimal quantity = inventoryLookup.get(item.getKeyId());
                    if (quantity == null || quantity.compareTo(item.getQuantity()) < 0) {
                        StringBuffer itemStr = new StringBuffer(item.getSkuName());
                        itemStr.append(" [")
                                .append("<b>").append(item.getQuantity()).append("</b>").append(" ")
                                .append("(Avl*: ")
                                .append(quantity != null ? quantity.setScale(2, BigDecimal.ROUND_HALF_UP) : 0).append(")")
                                .append(" ").append(item.getUnitOfMeasure())
                                .append("]");
                        unavailableInventory.add(itemStr.toString());
                    }
                }
            }
            if (unavailableInventory.size() > 0) {
                StringBuffer products = new StringBuffer("Not Enough Stock found for following products <br/>");
                products.append(StringUtils.join(unavailableInventory, ", ")).append("<br/>")
                        .append("Please ensure you have stock available to transfer.");
                SCMError error = new SCMError("Stock Not Available!!", products.toString(), 701);
                throw new TransferOrderCreationException(error);
            }
        }
    }

    private void fillDrillDowns(TransferOrder rec, TransferOrder transferOrder) {
        Set<Integer> indexUsed = new HashSet<Integer>();
        // this will fuck us one day
        for (TransferOrderItem i1 : transferOrder.getTransferOrderItems()) {
            for (int i = 0; i < rec.getTransferOrderItems().size(); i++) {
                TransferOrderItem i2 = rec.getTransferOrderItems().get(i);
                if (getItemKey(i1).equals(getItemKey(i2)) && !indexUsed.contains(i)) {
                    i2.setDrillDowns(i1.getDrillDowns());
                    indexUsed.add(i);
                    break;
                }
            }
        }
    }

    private String getItemKey(TransferOrderItem i) {
        return "" + i.getSkuId() + i.getUom() + i.getTransferredQuantity();
    }

    private boolean validateRequestForTransfer(RequestOrderData requestOrderData) {
        List<SCMOrderStatus> orderStatus = Arrays.asList(SCMOrderStatus.CREATED, SCMOrderStatus.ACKNOWLEDGED);
        return requestOrderData == null || orderStatus.contains(SCMOrderStatus.valueOf(requestOrderData.getStatus()));
    }

    private void saveTransferOrderItemDrillDowns(TransferOrder to, List<TransferOrderItemData> orderData) throws SumoException {
        HashMap<Integer, TransferOrderItemData> itemMap = new HashMap<>();
        for (TransferOrderItemData item : orderData) {
            itemMap.put(item.getId(), item);
        }

        UnitDetail unitDetail = scmCache.getUnitDetail(to.getGenerationUnitId().getId());
        boolean isKWHSendingUnit = SCMUtil.isWareHouseOrKitchen(unitDetail);
        for (TransferOrderItem item : to.getTransferOrderItems()) {
            List<TransferOrderItemDrilldown> drilldownList = new ArrayList<>();
            TransferOrderItemData orderItemData = itemMap.get(item.getId());
            SkuDefinition skuDefinition = scmCache.getSkuDefinition( orderItemData.getSkuId());
            ProductDefinition productDefinition = scmCache.getProductDefinition(skuDefinition.getLinkedProduct().getId());
            if (item.getDrillDowns() != null && !item.getDrillDowns().isEmpty()) {
                for (InventoryItemDrilldown drilldown : item.getDrillDowns()) {
                    TransferOrderItemDrilldown toDrilldown = new TransferOrderItemDrilldown();
                    toDrilldown.setOrderItem(orderItemData);
                    toDrilldown.setQuantity(drilldown.getQuantity());
                    toDrilldown.setPrice(drilldown.getPrice());
                    toDrilldown.setAddTime(AppUtils.getCurrentTimestamp());
                    toDrilldown.setExpiryDate(drilldown.getExpiryDate());
                    toDrilldown = transferOrderManagementDao.add(toDrilldown, true);
                    checkAndUpdateExpiryDate(toDrilldown, productDefinition, to, orderItemData, isKWHSendingUnit);
                    drilldownList.add(toDrilldown);
                }
            } else {
                TransferOrderItemDrilldown toDrilldown = new TransferOrderItemDrilldown();
                toDrilldown.setOrderItem(orderItemData);
                toDrilldown.setQuantity(orderItemData.getTransferredQuantity());
                toDrilldown.setPrice(Objects.isNull(orderItemData.getUnitPrice()) ? BigDecimal.ZERO : orderItemData.getUnitPrice());
                toDrilldown.setAddTime(AppUtils.getCurrentTimestamp());
                toDrilldown.setExpiryDate(SCMUtil.createExpiryDate(AppUtils.getCurrentTimestamp(), productDefinition.getShelfLifeInDays()));
                transferOrderManagementDao.add(toDrilldown, true);
                drilldownList.add(toDrilldown);
            }
            orderItemData.setItemDrilldowns(drilldownList);
        }
        transferOrderManagementDao.flush();
    }

    private void checkAndUpdateExpiryDate(TransferOrderItemDrilldown toDrilldown,
                                                     ProductDefinition productDefinition, TransferOrder to, TransferOrderItemData orderItemData, boolean isKWHSendingUnit) {
        try {
            Date nextExpiryDate;
            if (isKWHSendingUnit) {
                nextExpiryDate = productDefinition.getCategoryDefinition().getId() == SCMServiceConstants.CATEGORY_SEMI_FINISHED ?
                        AppUtils.createExpiryDate(AppUtils.getCurrentTimestamp(), productDefinition.getShelfLifeInDays()) : SCMUtil.getNextExpiryDate(toDrilldown.getExpiryDate());
            } else {
                nextExpiryDate = SCMUtil.getNextExpiryDate(toDrilldown.getExpiryDate());
            }
            if (SCMUtil.formatExpiryDate(toDrilldown.getExpiryDate()).compareTo(nextExpiryDate) < 0) {
                priceManagementService.addExpiryDateCorrection(ExpiryDataCorrectionTypeEnum.TO_ITEM_DRILL_DOWN_RESET.name(),
                        orderItemData.getSkuId(), nextExpiryDate, toDrilldown.getExpiryDate(), to.getGeneratedBy().getId(), to.getUnitId(),
                        PriceUpdateEntryType.SKU.name(), toDrilldown.getTransferOrderItemDrilldownId());
                toDrilldown.setExpiryDate(nextExpiryDate);
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred while updating Expiry for Semi finished for Product Id  : {}",productDefinition.getProductId(), e);
        }
    }

    private void setPricesAndTaxes(TransferOrderData transferOrderData, TransferOrder transferOrder, String invoiceId)
            throws TransferOrderCreationException {
        long orderStart = System.currentTimeMillis();
        long stepStart = System.currentTimeMillis();
        LOG.info("##### SET_PRICES_AND_TAXES - Start for : {}", transferOrderData.getId());
        Boolean isSpecialOrder = false;
        if (transferOrder.getRequestOrderId() != null) {
            RequestOrderData requestOrder = transferOrderManagementDao.find(RequestOrderData.class,
                    transferOrder.getRequestOrderId());
            if (SCMUtil.getStatus(requestOrder.getIsSpecialOrder())) {
                isSpecialOrder = true;
                setSpecialOrderPrices(transferOrder);
            }
        }
        LOG.info("##### SET_PRICES_AND_TAXES - Step 1 - Fetch Request Order for : {},{}", transferOrderData.getId(), System.currentTimeMillis() - stepStart);
        stepStart = System.currentTimeMillis();
        transferOrder.setTotalAmount(getTotalTransferOrderAmount(transferOrder));
        State fromState = masterDataCache.getUnit(transferOrder.getGenerationUnitId().getId()).getLocation().getState();
        State toState = masterDataCache.getUnit(transferOrder.getGeneratedForUnitId().getId()).getLocation().getState();
        boolean applyTaxes = true;
        boolean isInvoice = !transferOrder.getSourceCompany().getId().equals(transferOrder.getReceivingCompany().getId())
                || fromState.getId() != toState.getId();
        OrderTransferType trType = isInvoice ? OrderTransferType.INVOICE : OrderTransferType.TRANSFER;

        if (invoiceId.equals("0")) {
            //do Nothing Invoice Will Be Set after TO Creations in case of  Bulk Transfers
        } else if (invoiceId != null) {
            transferOrder.setInvoiceId(invoiceId);
        } else {
            transferOrder.setInvoiceId(getInvoiceId(transferOrder.getGenerationUnitId().getId(),
                    transferOrder.getGeneratedForUnitId().getId(),
                    !transferOrder.getSourceCompany().getId().equals(transferOrder.getReceivingCompany().getId())));
        }
        transferOrder.setFromState(fromState);
        transferOrder.setToState(toState);
        calculateTaxes(applyTaxes, fromState.getId(), toState.getId(), transferOrder, isSpecialOrder);
        transferOrderData.setTransferType(trType.name());
        transferOrderData.setTotalAmount(SCMUtil.convertToBigDecimal(transferOrder.getTotalAmount()));
        transferOrderData.setTaxAmount(transferOrder.getTax());
        transferOrderData.setGeneratedInvoiceId(transferOrder.getInvoiceId());
        LOG.info("##### SET_PRICES_AND_TAXES - Step 2 - Calculate Taxes at order for : {},{}", transferOrderData.getId(), System.currentTimeMillis() - stepStart);
        stepStart = System.currentTimeMillis();
        transferOrderManagementDao.setTaxDetail(applyTaxes, transferOrderData, transferOrder.getTaxes());
        LOG.info("##### SET_PRICES_AND_TAXES - Step 3 - Set taxes at order in db for : {},{}", transferOrderData.getId(), System.currentTimeMillis() - stepStart);
        Map<String, TransferOrderItemInvoice> invoices = new HashMap<>();
        stepStart = System.currentTimeMillis();
        long itemStep = System.currentTimeMillis();
        Map<Integer, TransferOrderItem> map = transferOrder.getTransferOrderItems().stream().collect(Collectors.toMap(TransferOrderItem::getId, Function.identity()));
        List<TransferOrderItemData> transferOrderItemDatas = transferOrderManagementDao.findByTransferId(transferOrderData.getId());
        LOG.info("##### ITEMS_SET_PRICES_AND_TAXES - Step 4.a - Fetch Items for TO-ID : {},{}", transferOrderData.getId(), System.currentTimeMillis() - itemStep);
        for (TransferOrderItemData transferOrderItemData : transferOrderItemDatas) {
            itemStep = System.currentTimeMillis();
            TransferOrderItem transferOrderItem = map.get(transferOrderItemData.getId());
            transferOrderItemData.setTaxCode(transferOrderItem.getCode());
            if (transferOrderItemData.getTaxAmount() == null) {
                LOG.info("Not a Stand Alone Asset Transfer Setting the unitPrice and Tax");
                transferOrderItemData.setTaxAmount(transferOrderItem.getTax());
                transferOrderItemData.setNegotiatedUnitPrice(transferOrderItem.getPrice());
                transferOrderItemData.setUnitPrice(transferOrderItem.getPrice());
            }
            if (transferOrderItemData.getNegotiatedUnitPrice() != null
                    && transferOrderItemData.getTransferredQuantity() != null) {
                transferOrderItemData
                        .setCalculatedAmount(SCMUtil.multiplyWithScale10(transferOrderItemData.getNegotiatedUnitPrice(),
                                transferOrderItemData.getTransferredQuantity()));
            }
            transferOrderManagementDao.update(transferOrderItemData, false);
            LOG.info("##### ITEMS_SET_PRICES_AND_TAXES - Step 4.b - Set taxes at items in db for : {},{}", transferOrderData.getId(), System.currentTimeMillis() - itemStep);
            itemStep = System.currentTimeMillis();
            List<TransferOrderItemTaxDetail> taxes = setTaxDetail(applyTaxes, transferOrderData.getId(), fromState.getId(),
                    transferOrderItemData, transferOrderItem, invoices);
            LOG.info("##### ITEMS_SET_PRICES_AND_TAXES - Step 4.c - Set taxes at items in db for : {},{}", transferOrderData.getId(), System.currentTimeMillis() - itemStep);
            itemStep = System.currentTimeMillis();
            transferOrderManagementDao.addAll(taxes);
            transferOrderManagementDao.addAll(invoices.values().stream().toList());
            LOG.info("##### ITEMS_SET_PRICES_AND_TAXES - Step 4.d - Persist Set taxes at items in db for : {},{}", transferOrderData.getId(), System.currentTimeMillis() - itemStep);
        }
        LOG.info("##### SET_PRICES_AND_TAXES - Step 5 - Set taxes at items in db for : {} - {},{}", transferOrderData.getId(), transferOrder.getTransferOrderItems().size(), System.currentTimeMillis() - stepStart);
        stepStart = System.currentTimeMillis();
        transferOrderManagementDao.flush();
        LOG.info("##### SET_PRICES_AND_TAXES - Step 6 - Flush DB db for : {},{}", transferOrderData.getId(), System.currentTimeMillis() - stepStart);
        LOG.info("##### SET_PRICES_AND_TAXES - End for : {},{}", transferOrderData.getId(), System.currentTimeMillis() - orderStart);

    }

    public List<TransferOrderItemTaxDetail> setTaxDetail(boolean applyTaxes, int orderId, int stateId, TransferOrderItemData detail, TransferOrderItem item,
                                                         Map<String, TransferOrderItemInvoice> invoices) {
        if (!applyTaxes || item.getTaxes() == null || item.getTaxes().isEmpty()) {
            return null;
        }
        List<TransferOrderItemTaxDetail> taxes = new ArrayList<>();
        for (TaxDetail tax : item.getTaxes()) {
            TransferOrderItemTaxDetail taxDetail = new TransferOrderItemTaxDetail();
            setTaxInfo(taxDetail, tax);
            taxDetail.setOrderItem(detail);
            taxes.add(taxDetail);
        }
        setTaxInvoices(orderId, stateId, detail, item, invoices);
        return taxes;
    }

    private void setTaxInfo(TaxationDetailDao taxDetail, TaxDetail tax) {
        taxDetail.setTaxCode(tax.getCode());
        taxDetail.setTaxType(tax.getType());
        taxDetail.setTotalTax(tax.getValue());
        taxDetail.setTaxPercentage(tax.getPercentage());
        taxDetail.setTotalAmount(tax.getTotal());
        taxDetail.setTaxableAmount(tax.getTaxable());
    }

    private void setTaxInvoices(int orderId, int stateId, TransferOrderItemData detail, TransferOrderItem item,
                                Map<String, TransferOrderItemInvoice> invoices) {
        if (item.getTaxes() == null || item.getTaxes().isEmpty()) {
            return;
        }
        if (!invoices.containsKey(detail.getTaxCode())) {
            TransferOrderItemInvoice invoice = new TransferOrderItemInvoice();
            invoice.setStateId(stateId);
            invoice.setOrderId(orderId);
            //REmoved this as this is not used anywhere. We also checked for duplicate invoice ids for the state and we found alot of items with duplicate ids
            //and even the setter was not used anywhere.
            // invoice.setStateInvoiceId(getNextStateInvoiceId(stateId, "STATE_INVOICE_ID"));
            invoice.setTaxableAmount(BigDecimal.ZERO);
            invoice.setTaxAmount(BigDecimal.ZERO);
            invoice.setTaxCategory(detail.getTaxCode());
            invoice.setTotalAmount(BigDecimal.ZERO);
            invoices.put(detail.getTaxCode(), invoice);
        }
        TransferOrderItemInvoice data = invoices.get(detail.getTaxCode());
        data.setTotalAmount(data.getTotalAmount().add(item.getTotal()));
        data.setTaxableAmount(data.getTaxableAmount().add(item.getTotal()));
        data.setTaxAmount(data.getTaxAmount().add(item.getTax()));
        for (TaxDetail tax : item.getTaxes()) {
            boolean found = false;
            List<TransferOrderItemInvoiceTaxDetail> taxInvoices = data.getOrderItemInvoiceTaxes();
            for (TransferOrderItemInvoiceTaxDetail taxInvoice : taxInvoices) {
                if (taxInvoice.getTaxCode().equals(tax.getCode()) && taxInvoice.getTaxType().equals(tax.getType())
                        && taxInvoice.getTaxPercentage().equals(tax.getPercentage())) {
                    found = true;
                    taxInvoice.setTotalAmount(taxInvoice.getTotalAmount().add(tax.getTotal()));
                    taxInvoice.setTaxableAmount(taxInvoice.getTaxableAmount().add(tax.getTaxable()));
                    taxInvoice.setTotalTax(taxInvoice.getTotalTax().add(tax.getValue()));
                    break;
                }

            }
            if (!found) {
                TransferOrderItemInvoiceTaxDetail newTaxInvoice = new TransferOrderItemInvoiceTaxDetail();
                newTaxInvoice.setOrderItemInvoice(data);
                newTaxInvoice.setTaxableAmount(tax.getTaxable());
                newTaxInvoice.setTaxCode(tax.getCode());
                newTaxInvoice.setTaxPercentage(tax.getPercentage());
                newTaxInvoice.setTaxType(tax.getType());
                newTaxInvoice.setTotalAmount(tax.getTotal());
                newTaxInvoice.setTotalTax(tax.getValue());
                data.getOrderItemInvoiceTaxes().add(newTaxInvoice);
            }
        }
    }

    private void setSpecialOrderPrices(TransferOrder transferOrder) throws TransferOrderCreationException {
        UnitBasicDetail unitBasicDetail = masterDataCache.getUnitBasicDetail(transferOrder.getGenerationUnitId().getId());
        if (unitBasicDetail != null) {
            Map<Integer, List<TransferOrderItem>> vendorTransferItemsMap = new HashMap<>();
            List<TransferOrderItem> cashPurchaseItems = new ArrayList<>();
            for (TransferOrderItem transferOrderItem : transferOrder.getTransferOrderItems()) {
                Integer vendorId = transferOrderManagementDao
                        .find(RequestOrderItemData.class, transferOrderItem.getRequestOrderItemId()).getVendorId();
                if (vendorId != null) {
                    List<TransferOrderItem> transferOrderItems = vendorTransferItemsMap.get(vendorId);
                    if (transferOrderItems == null) {
                        transferOrderItems = new ArrayList<>();
                    }
                    transferOrderItems.add(transferOrderItem);
                    vendorTransferItemsMap.put(vendorId, transferOrderItems);
                } else {
                    throw new TransferOrderCreationException(
                            "No vendor selected for SKU: " + transferOrderItem.getSkuName());
                }
            }
            if (vendorTransferItemsMap.keySet().size() > 0) {
                Integer deliveryLocationId = masterDataCache.getAllLocations().get( unitBasicDetail.getLocationCode() ).getId();
                List<SkuPriceDetail> skuPriceDetails = skuMappingService.getSkuPackagingPriceForVendorsForUnit(
                        unitBasicDetail.getId(), vendorTransferItemsMap.keySet(), deliveryLocationId);
                Map<Integer, Map<SkuPackagingKey, Map<String, BigDecimal>>> vendorSkuPackagingDispatchPriceMap = new HashMap<>();
                if (skuPriceDetails != null) {
                    createVendorSkuPackagingDispatchPriceMap(skuPriceDetails, vendorSkuPackagingDispatchPriceMap);
                }
                List<SkuPriceDetail> allSkuPriceDetails = skuMappingService
                        .getSkuPackagingPriceForUnit(unitBasicDetail.getId(), deliveryLocationId);
                Map<Integer, Map<SkuPackagingKey, Map<String, BigDecimal>>> allVendorSkuPackagingDeliveryPriceMap = new HashMap<>();
                if (allSkuPriceDetails != null) {
                    createVendorSkuPackagingDeliveryPriceMap(allSkuPriceDetails, allVendorSkuPackagingDeliveryPriceMap);
                }
                for (Integer vendorId : vendorTransferItemsMap.keySet()) {
                    VendorDetail vendor = scmCache.getVendorDetail(vendorId);
                    List<TransferOrderItem> transferOrderItems = vendorTransferItemsMap.get(vendorId);
                    Map<SkuPackagingKey, Map<String, BigDecimal>> map = vendorSkuPackagingDispatchPriceMap
                            .get(vendorId);
                    if (map != null || vendorId == SCMServiceConstants.CASH_PURCHASE_VENDOR_ID) {
                        for (TransferOrderItem transferOrderItem : transferOrderItems) {
                            BigDecimal amount = BigDecimal.ZERO;
                            BigDecimal totalUnitsPacked = BigDecimal.ZERO;
                            for (SCMOrderPackaging scmOrderPackaging : transferOrderItem.getPackagingDetails()) {
                                Map<String, BigDecimal> disPatchPriceMap = map != null
                                        ? map.get(new SkuPackagingKey(transferOrderItem.getSkuId(),
                                        scmOrderPackaging.getPackagingDefinitionData().getPackagingId()))
                                        : null;
                                if (vendorId != SCMServiceConstants.CASH_PURCHASE_VENDOR_ID) {
                                    if (disPatchPriceMap != null && (disPatchPriceMap.size() == 1
                                            || disPatchPriceMap.containsKey(unitBasicDetail.getLocationCode()))) {
                                        if (disPatchPriceMap.size() == 1) {
                                            String loc = disPatchPriceMap.keySet().stream().collect(Collectors.toList())
                                                    .get(0);
                                            scmOrderPackaging.setPricePerUnit(disPatchPriceMap.get(loc).floatValue());
                                        } else {
                                            scmOrderPackaging.setPricePerUnit(disPatchPriceMap
                                                    .get(unitBasicDetail.getLocationCode()).floatValue());
                                        }
                                        amount = amount.add(new BigDecimal(scmOrderPackaging.getPricePerUnit())
                                                .multiply(new BigDecimal(scmOrderPackaging.getNumberOfUnitsPacked())));
                                        totalUnitsPacked = totalUnitsPacked
                                                .add(new BigDecimal(scmOrderPackaging.getNumberOfUnitsPacked())
                                                        .multiply(new BigDecimal(scmOrderPackaging
                                                                .getPackagingDefinitionData().getConversionRatio())));
                                    } else {
                                        throw new TransferOrderCreationException(
                                                "Sku Packaging price not found for unit: " + unitBasicDetail.getName()
                                                        + " vendor: " + vendor.getEntityName() + " SKU: "
                                                        + transferOrderItem.getSkuName() + " packaging: "
                                                        + scmOrderPackaging.getPackagingDefinitionData()
                                                        .getPackagingName());
                                    }
                                } else {
                                    if (disPatchPriceMap != null
                                            && disPatchPriceMap.containsKey(unitBasicDetail.getLocationCode())) {
                                        scmOrderPackaging.setPricePerUnit(
                                                disPatchPriceMap.get(unitBasicDetail.getLocationCode()).floatValue());
                                        amount = amount.add(new BigDecimal(scmOrderPackaging.getPricePerUnit())
                                                .multiply(new BigDecimal(scmOrderPackaging.getNumberOfUnitsPacked())));
                                        totalUnitsPacked = totalUnitsPacked
                                                .add(new BigDecimal(scmOrderPackaging.getNumberOfUnitsPacked())
                                                        .multiply(new BigDecimal(scmOrderPackaging
                                                                .getPackagingDefinitionData().getConversionRatio())));
                                    } else {
                                        boolean priceFound = false;
                                        for (Integer vendorIdentifier : allVendorSkuPackagingDeliveryPriceMap.keySet()) {
                                            Map<SkuPackagingKey, Map<String, BigDecimal>> map2 = allVendorSkuPackagingDeliveryPriceMap
                                                    .get(vendorIdentifier);
                                            Map<String, BigDecimal> deliveryPriceMap = map2.get(new SkuPackagingKey(
                                                    transferOrderItem.getSkuId(),
                                                    scmOrderPackaging.getPackagingDefinitionData().getPackagingId()));
                                            if (deliveryPriceMap != null && deliveryPriceMap
                                                    .containsKey(unitBasicDetail.getLocationCode())) {
                                                scmOrderPackaging.setPricePerUnit(deliveryPriceMap
                                                        .get(unitBasicDetail.getLocationCode()).floatValue());
                                                amount = amount.add(new BigDecimal(scmOrderPackaging.getPricePerUnit())
                                                        .multiply(new BigDecimal(
                                                                scmOrderPackaging.getNumberOfUnitsPacked())));
                                                totalUnitsPacked = totalUnitsPacked
                                                        .add(new BigDecimal(scmOrderPackaging.getNumberOfUnitsPacked())
                                                                .multiply(new BigDecimal(
                                                                        scmOrderPackaging.getPackagingDefinitionData()
                                                                                .getConversionRatio())));
                                                priceFound = true;
                                            }
                                        }
                                        if (!priceFound) {
                                            throw new TransferOrderCreationException(
                                                    "Sku Packaging price not found for unit: "
                                                            + unitBasicDetail.getName() + " vendor: CASH_PURCHASE"
                                                            + " SKU: " + transferOrderItem.getSkuName() + " packaging: "
                                                            + scmOrderPackaging.getPackagingDefinitionData()
                                                            .getPackagingName());
                                        }
                                    }
                                }

                            }
                            transferOrderItem.setPrice(SCMUtil.divideWithScale10(amount, totalUnitsPacked));
                            transferOrderItem.setUnitPrice(transferOrderItem.getPrice().doubleValue());
                            transferOrderItem.setNegotiatedUnitPrice(transferOrderItem.getUnitPrice());
                        }
                    } else {
                        throw new TransferOrderCreationException("Sku Packaging price not found for unit: "
                                + unitBasicDetail.getName() + " vendor: " + vendor.getEntityName());
                    }
                }
                transferOrder.getTransferOrderItems().clear();
                for (List<TransferOrderItem> transferOrderItems : vendorTransferItemsMap.values()) {
                    transferOrder.getTransferOrderItems().addAll(transferOrderItems);
                }
                transferOrder.getTransferOrderItems().addAll(cashPurchaseItems);
                List<TransferOrderItem> withoutPrices = transferOrder.getTransferOrderItems().stream()
                        .filter(transferOrderItem -> transferOrderItem.getPrice() == null
                                || transferOrderItem.getPrice().floatValue() == 0.00f)
                        .collect(Collectors.toList());
                if (withoutPrices.size() > 0) {
                    throw new TransferOrderCreationException("Sku price not found for Transfer items");
                }
            }
        }
    }

    private void createVendorSkuPackagingDispatchPriceMap(List<SkuPriceDetail> skuPriceDetails,
                                                          Map<Integer, Map<SkuPackagingKey, Map<String, BigDecimal>>> vendorSkuPackagingDispatchPriceMap) {
        for (SkuPriceDetail skuPriceDetail : skuPriceDetails) {
            Map<SkuPackagingKey, Map<String, BigDecimal>> map = vendorSkuPackagingDispatchPriceMap
                    .get(skuPriceDetail.getVendor().getId());
            if (map == null) {
                map = new HashMap<>();
            }
            SkuPackagingKey skuPackagingKey = new SkuPackagingKey(skuPriceDetail.getSku().getId(),
                    skuPriceDetail.getPkg().getId());
            Map<String, BigDecimal> dispatchPriceMap = map.get(skuPackagingKey);
            if (dispatchPriceMap == null) {
                dispatchPriceMap = new HashMap<>();
            }
            dispatchPriceMap.put(skuPriceDetail.getDispatch().getCode(), skuPriceDetail.getCurrent().getValue());
            map.put(skuPackagingKey, dispatchPriceMap);
            vendorSkuPackagingDispatchPriceMap.put(skuPriceDetail.getVendor().getId(), map);
        }
    }

    private void createVendorSkuPackagingDeliveryPriceMap(List<SkuPriceDetail> skuPriceDetails,
                                                          Map<Integer, Map<SkuPackagingKey, Map<String, BigDecimal>>> allVendorSkuPackagingDeliveryPriceMap) {
        for (SkuPriceDetail skuPriceDetail : skuPriceDetails) {
            Map<SkuPackagingKey, Map<String, BigDecimal>> map = allVendorSkuPackagingDeliveryPriceMap
                    .get(skuPriceDetail.getVendor().getId());
            if (map == null) {
                map = new HashMap<>();
            }
            SkuPackagingKey skuPackagingKey = new SkuPackagingKey(skuPriceDetail.getSku().getId(),
                    skuPriceDetail.getPkg().getId());
            Map<String, BigDecimal> deliveryPriceMap = map.get(skuPackagingKey);
            if (deliveryPriceMap == null) {
                deliveryPriceMap = new HashMap<>();
            }
            deliveryPriceMap.put(skuPriceDetail.getDelivery().getCode(), skuPriceDetail.getCurrent().getValue());
            map.put(skuPackagingKey, deliveryPriceMap);
            allVendorSkuPackagingDeliveryPriceMap.put(skuPriceDetail.getVendor().getId(), map);
        }
    }

    private void calculateTaxes(boolean applyTaxes, int fromStateId, int toStateId, TransferOrder to, Boolean isSpecialOrder) {
        if (!applyTaxes || to.getTransferOrderItems() == null || to.getTransferOrderItems().size() == 0) {
            return;
        }
        for (TransferOrderItem item : to.getTransferOrderItems()) {
            String code = "";
            if (Boolean.TRUE.equals(isSpecialOrder)) {
                code = skuMappingService.getHsnCodeFromUnitSkuMapping(scmCache.getSkuDefinition(item.getSkuId()), to.getGenerationUnitId().getId());
            } else {
                code = scmCache
                        .getProductDefinition(scmCache.getSkuDefinition(item.getSkuId()).getLinkedProduct().getId())
                        .getTaxCode();
            }
            item.setCode(code);
            if (item.getCode() == null) {
                continue;
            }
            TaxData taxData = taxCache.getTaxData(fromStateId, item.getCode());
            if (fromStateId == toStateId) {
                if (taxData.getState() != null && taxData.getState().getSgst().compareTo(BigDecimal.ZERO) > 0) {
                    setTaxes(to, item, taxData, "SGST");
                }
                if (taxData.getState() != null && taxData.getState().getCgst().compareTo(BigDecimal.ZERO) > 0) {
                    setTaxes(to, item, taxData, "CGST");
                }
            } else {
                if (taxData.getState() != null && taxData.getState().getIgst().compareTo(BigDecimal.ZERO) > 0) {
                    setTaxes(to, item, taxData, "IGST");
                }
            }
        }
    }

    /**
     * @param to
     * @param item
     * @param taxData
     */

    private void setTaxes(TransferOrder to, TransferOrderItem item, TaxData taxData, String taxType) {
        item.setTotal(AppUtils.multiplyWithScale10(item.getPrice(), new BigDecimal(item.getTransferredQuantity())));
        BigDecimal taxPercentage = getTaxPercentage(taxType, taxData);
        item.getTaxes().add(convert(to, item, taxPercentage, taxType, "GST"));
        if (taxData.getOthers() != null && taxData.getOthers().size() > 0) {
            for (AdditionalTax tax : taxData.getOthers()) {
                if (tax.getTax().compareTo(BigDecimal.ZERO) > 0) {
                    item.getTaxes().add(convert(to, item, tax.getTax(), tax.getType(), tax.getType()));
                }
            }
        }
    }

    private BigDecimal getTaxPercentage(String taxType, TaxData taxData) {
        BigDecimal percentage = BigDecimal.ZERO;
        switch (taxType) {
            case "IGST":
                percentage = percentage.add(taxData.getState().getIgst());
                break;
            case "SGST":
                percentage = percentage.add(taxData.getState().getSgst());
                break;
            case "CGST":
                percentage = percentage.add(taxData.getState().getCgst());
                break;
        }
        return percentage;
    }

    private TaxDetail convert(TransferOrder to, TransferOrderItem item, BigDecimal percentage, String code,
                              String type) {
        TaxDetail d = new TaxDetail();
        d.setCode(code);
        d.setPercentage(percentage);
        BigDecimal tax = AppUtils.multiplyWithScale10(item.getTotal(),
                AppUtils.divideWithScale10(percentage, new BigDecimal(100)));
        d.setTaxable(item.getTotal());
        d.setTotal(item.getTotal());
        d.setType(type);
        d.setValue(tax);
        item.setTax(AppUtils.add(item.getTax(), tax));
        setTaxes(to, d);
        return d;
    }

    /**
     * @param to
     * @param d
     */
    private void setTaxes(TransferOrder to, TaxDetail d) {
        TaxDetail detail = null;
        for (TaxDetail tax : to.getTaxes()) {
            if (tax.getCode().equals(d.getCode()) && tax.getPercentage().equals(d.getPercentage())
                    && tax.getType().equals(d.getType())) {
                detail = tax;
                break;
            }
        }
        if (detail == null) {
            detail = new TaxDetail();
            detail.setCode(d.getCode());
            detail.setPercentage(d.getPercentage());
            detail.setValue(d.getValue());
            detail.setTaxable(d.getTaxable());
            detail.setType(d.getType());
            detail.setTotal(d.getTotal());
            to.setTax(AppUtils.add(to.getTax(), d.getValue()));
            to.getTaxes().add(detail);
        } else {
            to.setTax(AppUtils.add(to.getTax(), d.getValue()));
            detail.setValue(AppUtils.add(detail.getValue(), d.getValue()));
            detail.setTaxable(AppUtils.add(detail.getTaxable(), d.getTaxable()));
            detail.setTotal(AppUtils.add(detail.getTotal(), d.getTotal()));
        }
    }

    private void publishTransferOrder(TransferOrderData transferOrderData, boolean isCancellation) {
        try {
            List<ProductQuantityData> details = new ArrayList<>();
            for (TransferOrderItemData data : transferOrderData.getTransferOrderItemDatas()) {
                // Request Order will be null for Stand alone TO
                Integer productId = scmCache.getSkuDefinition(data.getSkuId()).getLinkedProduct().getId();
                details.add(new ProductQuantityData(productId, data.getTransferredQuantity(), data.getUnitOfMeasure()));
            }
            if (details.size() > 0) {
                QuantityResponseData response = new QuantityResponseData(transferOrderData.getGenerationUnitId(),
                        details, isCancellation ? InventoryAction.ADD : InventoryAction.REMOVE,
                        isCancellation ? InventorySource.TRANSFER_ORDER_CANCELLATION : InventorySource.TRANSFER_ORDER,
                        transferOrderData.getId(), SCMUtil.getCurrentTimestamp());
                inventoryService.publishInventorySQSFifo(props.getInventoryQueuePrefix(), response);
            }
        } catch (Exception e) {
            LOG.error("Error while publishing inventory for TR with id " + transferOrderData.getId(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<TransferOrder> findTransferOrders(Integer generationUnitId, Integer generatedForUnitId, Date startDate,
                                                  Date endDate, SCMOrderStatus status, Integer transferOrderId) {
        List<TransferOrderData> transferOrderDatas = transferOrderManagementDao.findTransferOrders(generationUnitId,
                generatedForUnitId, startDate, endDate, status, transferOrderId);
        List<TransferOrder> transferOrders = new ArrayList<TransferOrder>();
        for (TransferOrderData transferOrderData : transferOrderDatas) {
            TransferOrder transfer = SCMDataConverter.convert(transferOrderData, true, scmCache, masterDataCache);
            transferOrders.add(transfer);
        }
        return transferOrders;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<TransferOrder> incrementalTransferOrders(Integer generationUnitId, Integer generatedForUnitId, Date startDate, Date endDate, SCMOrderStatus status, Integer transferOrderId, String ids) {
        List<TransferOrderData> transferOrderDatas = transferOrderManagementDao.incrementalTransferOrders(generationUnitId,
                generatedForUnitId, startDate, endDate, status, transferOrderId, ids);
        List<TransferOrder> transferOrders = new ArrayList<TransferOrder>();
        for (TransferOrderData transferOrderData : transferOrderDatas) {
            TransferOrder transfer = SCMDataConverter.convert(transferOrderData, true, scmCache, masterDataCache);
            transferOrders.add(transfer);
        }
        return transferOrders;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<TransferOrder> getTorqusTransferOrderList(Integer generationUnitId, Date startDate, Date endDate) {
        List<TransferOrderData> transferOrderDatas = transferOrderManagementDao.findTransferOrders(generationUnitId,
                null, startDate, endDate, null, null);
        List<TransferOrder> transferOrders = new ArrayList<TransferOrder>();
        for (TransferOrderData transferOrderData : transferOrderDatas) {
            IdCodeName generatedBy = SCMUtil.generateIdCodeName(transferOrderData.getGeneratedBy(), "",
                    masterDataCache.getEmployees().get(transferOrderData.getGeneratedBy()));
            IdCodeName lastUpdatedBy = null;
            if (transferOrderData.getLastUpdatedBy() != null) {
                lastUpdatedBy = SCMUtil.generateIdCodeName(transferOrderData.getLastUpdatedBy(), "",
                        masterDataCache.getEmployees().get(transferOrderData.getLastUpdatedBy()));
            }
            Unit generatedForUnit = masterDataCache.getUnit(transferOrderData.getGeneratedForUnitId());
            Unit generationUnit = masterDataCache.getUnit(transferOrderData.getGenerationUnitId());
            transferOrders.add(SCMDataConverter.convert(transferOrderData, generatedBy, lastUpdatedBy, generatedForUnit,
                    generationUnit, null, true, scmCache, masterDataCache));
        }
        return transferOrders;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public TransferOrder getTransferOrderDetail(int transferOrderId) {
        TransferOrderData transferOrderData = transferOrderManagementDao.find(TransferOrderData.class, transferOrderId);
        LOG.info("Good received for transfer order Id {}", transferOrderId);
        if (transferOrderData != null) {
            IdCodeName generatedBy = SCMUtil.generateIdCodeName(transferOrderData.getGeneratedBy(), "",
                    masterDataCache.getEmployees().get(transferOrderData.getGeneratedBy()));
            IdCodeName lastUpdatedBy = null;
            if (transferOrderData.getLastUpdatedBy() != null) {
                lastUpdatedBy = SCMUtil.generateIdCodeName(transferOrderData.getLastUpdatedBy(), "",
                        masterDataCache.getEmployees().get(transferOrderData.getLastUpdatedBy()));
            }
            Unit generatedForUnit = masterDataCache.getUnit(transferOrderData.getGeneratedForUnitId());
            Unit generationUnit = masterDataCache.getUnit(transferOrderData.getGenerationUnitId());
            GoodsReceivedData goodsReceivedData = transferOrderManagementDao
                    .getGRFromTransferOrder(transferOrderData.getId());
            TransferOrder transferOrder = SCMDataConverter.convert(transferOrderData, generatedBy, lastUpdatedBy,
                    generatedForUnit, generationUnit, goodsReceivedData, true, scmCache, masterDataCache);
            return transferOrder;
        }
        LOG.info("Transfer order with id {} not found for get detail.", transferOrderId);
        return null;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Integer cancelTransferOrder(Integer transferOrderId, Integer updatedBy) throws SumoException {
        TransferOrderData transferOrderData = transferOrderManagementDao.find(TransferOrderData.class, transferOrderId);
        if (transferOrderData != null && !transferOrderData.getStatus().equals(SCMOrderStatus.SETTLED.value()) && !transferOrderData.getStatus().equals(SCMOrderStatus.CANCELLED.value())
                && transferOrderData.getClosureEventId() == null) {
            Date currentTime = SCMUtil.getCurrentTimestamp();
            transferOrderData.setLastUpdateTime(currentTime);
            transferOrderData.setStatus(SCMOrderStatus.CANCELLED.value());
            transferOrderData.setLastUpdatedBy(updatedBy);
            if (transferOrderData.getRequestOrderData() != null) {
                transferOrderData.getRequestOrderData().setStatus(SCMOrderStatus.ACKNOWLEDGED.value());
            }
            if (Objects.nonNull(transferOrderData.getRequestOrderData())) {
                RequestOrderData requestOrderData = transferOrderData.getRequestOrderData();
                if (Objects.nonNull(requestOrderData.getType()) && requestOrderData.getType().equals(CapexStatus.CAPEX.value())) {
                    BigDecimal tax = Objects.isNull(transferOrderData.getTaxAmount()) ? BigDecimal.ZERO : transferOrderData.getTaxAmount();
                    BigDecimal toTotalAmount = transferOrderData.getTotalAmount().setScale(2, RoundingMode.HALF_EVEN).add(tax.setScale(2, RoundingMode.HALF_EVEN));
                    BigDecimal roTotalAmout = requestOrderData.getTotalAmount().setScale(2, RoundingMode.HALF_EVEN);
                    BigDecimal roTODiff = toTotalAmount.subtract(roTotalAmout);
                    if (roTODiff.compareTo(BigDecimal.ZERO) != 0) {
                        requestOrderManagementService.updateBudgetDetails(transferOrderData.getGeneratedForUnitId(), roTODiff, transferOrderId, BudgetAuditActions.TO_ID,
                                requestOrderData.getAssetOrder(), updatedBy, true);
                    }

                }
            }


            if (transferOrderData.getToType().equals(TransferOrderType.BROKEN_ASSET_TRANSFER.value())
                    || transferOrderData.getToType().equals(TransferOrderType.FIXED_ASSET_TRANSFER.value())
                    || transferOrderData.getToType().equals(TransferOrderType.RENOVATION_ASSET_TRANSFER.value())) {
                List<Integer> assetIds = transferOrderData.getTransferOrderItemDatas().stream().map(toItem -> toItem.getAssociatedAssetId()).
                        collect(Collectors.toList());
                scmAssetManagementService.setInTransit(assetIds, false);
                if (transferOrderData.getType() != null && transferOrderData.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())) {
                    BigDecimal total = BigDecimal.ZERO;
                    for (TransferOrderItemData data : transferOrderData.getTransferOrderItemDatas()) {
                        BigDecimal tax = data.getTaxAmount() != null ? data.getTaxAmount() : BigDecimal.ZERO;
                        BigDecimal totalAmount = data.getUnitPrice() != null ? data.getUnitPrice() : BigDecimal.ZERO;
                        total = total.add(totalAmount.add(tax));
                    }
                    LOG.info("Total Amount got from the Item Data is : {}", total);
                    if (total.compareTo(BigDecimal.ZERO) > 0) {
                        LOG.info("Updating budget for FIXED ASSETS Cancel Transfer");
                        if (!updateBudgetForAssetTransfer(transferOrderData, total, false)) {
                            throw new SumoException("Error in Budget Updation", "Cannot Update Budget for <b>FIXED_ASSETS(FA_Equipment)</b> while Cancelling Transfer..!");
                        }
                    }
                }
            }

            try {
                transferOrderData = transferOrderManagementDao.update(transferOrderData, true);
                if (transferOrderData != null) {
                    GoodsReceivedData goodsReceivedData = transferOrderManagementDao
                            .getGRFromTransferOrder(transferOrderData.getId());
                    goodsReceivedData.setStatus(SCMOrderStatus.CANCELLED.value());
                    goodsReceivedData.setCancelledBy(updatedBy);
                    goodsReceivedData.setLastUpdateTime(currentTime);
                    goodsReceiveManagementService.cancelGrEvent(goodsReceivedData.getId());
                    goodsReceivedData = transferOrderManagementDao.update(goodsReceivedData, true);
                    if (goodsReceivedData != null) {
                        TransferOrder transferOrder = fetchTransferOrderService.getTransferOrderDetail(transferOrderData.getId());
                        if (transferOrder.getGeneratedForUnitId().getId() != transferOrder.getGenerationUnitId()
                                .getId()) {
                            updateTOItemDrillDowns(transferOrderData, transferOrder);
                            priceDao.addReceiving(transferOrder, true);
                            publishTransferOrder(transferOrderData, true);
                        }
                        updateCounterForMonkConsumableStockTransferred(transferOrder,transferOrderData.getId(),true);
                        for (TransferOrderItemData itemData : transferOrderData.getTransferOrderItemDatas()) {
                            if (itemData.getProductionId() != null) {
                                if (Objects.isNull(transferOrderData.getBulkTransferEventId())) {
                                    productionBookingService.cancelBookings(itemData.getProductionId(), updatedBy);
                                } else {
                                    ProductionBookingData bookingData = transferOrderManagementDao.find(ProductionBookingData.class, itemData.getProductionId());
                                    if (bookingData.getQuantity().compareTo(itemData.getTransferredQuantity()) < 0) {
                                        throw new SumoException("Can't cancel Booking as TO Item qty is greater than booking qty");
                                    }
                                    if (bookingData.getQuantity().equals(itemData.getTransferredQuantity())) {
                                        productionBookingService.cancelBookings(itemData.getProductionId(), updatedBy);
                                    } else {
                                        productionBookingService.updateBookings(bookingData, itemData, updatedBy, scmCache);
                                    }
                                }
                            }
                        }
                        return transferOrderData.getId();
                    }
                }

            } catch (Exception e) {
                e.printStackTrace();
                LOG.error("Error cancelling transfer order with id {}", transferOrderId);
                transferOrderData.setStatus(SCMOrderStatus.CREATED.value());
                transferOrderManagementDao.update(transferOrderData, true);
                throw new SumoException("Error Cancelling transfer Order ");
            }
        } else {
            LOG.info("Transfer order with id {} not valid for cancellation!", transferOrderId);
        }
        return null;
    }

    private boolean updateTOItemDrillDowns(TransferOrderData transferOrderData, TransferOrder transferOrder) {
        LOG.info("updateTOItemDrillDowns : transfer order id : " + transferOrderData.getId());
        HashMap<Integer, TransferOrderItem> itemMap = new HashMap<>();
        for (TransferOrderItem item : transferOrder.getTransferOrderItems()) {
            itemMap.put(item.getId(), item);
        }
        for (TransferOrderItemData itemData : transferOrderData.getTransferOrderItemDatas()) {
            List<InventoryItemDrilldown> ddList = new ArrayList<>();
            for (TransferOrderItemDrilldown dd : itemData.getItemDrilldowns()) {
                ddList.add(SCMDataConverter.convert(dd));
            }
            if (ddList.size() == 0) {
                return false;
            }
            itemMap.get(itemData.getId()).getDrillDowns().addAll(ddList);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Integer createClubbedTransferOrder(List<Integer> roIds, TransferOrder transferOrder, Date fulfilmentDate,
                                              IdCodeName fulfillmentCompany, IdCodeName requestCompany, String invoiceId)
            throws TransferOrderCreationException, InventoryUpdateException, SumoException, DataNotFoundException {
        List<RequestOrderData> requestOrderDataList = requestOrderManagementDao.getRequestOrdersByIds(null, null, null, null, roIds);
        RequestOrderData requestOrder = requestOrderManagementService.clubRequestOrders(requestOrderDataList,
                transferOrder.getGeneratedForUnitId(), transferOrder.getGenerationUnitId(),
                transferOrder.getGeneratedBy(), fulfilmentDate, requestCompany.getId(), fulfillmentCompany.getId());

        // update the clubbed RO id into Transfer Order request
        transferOrder.setRequestOrderId(requestOrder.getId());
        //update clubbed ro items id into transfer Order Item request
        for (RequestOrderItemData requestOrderItemData : requestOrder.getRequestOrderItemDatas()) {
            for (TransferOrderItem transferOrderItem : transferOrder.getTransferOrderItems()) {
                if (transferOrderItem.getProductId() == requestOrderItemData.getProductId()) {
                    transferOrderItem.setRequestOrderItemId(requestOrderItemData.getId());
                    break;
                }
            }
        }
        return createTransferOrder(transferOrder, false, invoiceId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean generateInvoiceForTO(List<TransferOrderData> transferOrderDataList, Integer eventId) {
        for (TransferOrderData transferOrderData : transferOrderDataList) {
            if (transferOrderData.getStatus().equals(SCMOrderStatus.CANCELLED.value())) {
                continue;
            }
            String invoiceId = getInvoiceId(transferOrderData.getGenerationUnitId(), transferOrderData.getGeneratedForUnitId(),
                    transferOrderData.getSourceCompanyId() != transferOrderData
                            .getReceivingCompanyId());
            transferOrderData.setGeneratedInvoiceId(invoiceId);
        }
        transferOrderManagementDao.update(transferOrderDataList, true);
        if (Objects.nonNull(eventId)) {
            BulkTransferEventData bulkTransferEventData = transferOrderManagementDao.find(BulkTransferEventData.class, eventId);
            bulkTransferEventData.setIsInvoiceSet(SCMUtil.setStatus(true));
            transferOrderManagementDao.update(bulkTransferEventData, true);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<TransferredAsset> findAssetTransfersByUnitId(Integer unitId) throws SumoException {
        Map<String, List<TransferredAsset>> transfers = transferOrderManagementDao.findAssetTransfersByUnitId(unitId);
        List<TransferredAsset> assetTransfers = transfers.get("L1");
        List<TransferredAsset> l2Transfers = transfers.get("L2");
        LOG.info("size of l1 is {}", assetTransfers.size());
        LOG.info("size of l2 is {}", l2Transfers.size());
        Map<String, List<TransferredAsset>> transferredAssetMap = new HashMap<>();
        for (TransferredAsset asset : assetTransfers) {
            TransferredAsset l2SentOut = l2Transfers.stream().filter(e -> e.getAssetTag().equalsIgnoreCase(asset.getAssetTag())).findFirst().orElse(null);
            if (transferredAssetMap.containsKey(asset.getAssetTag())) {
                if (Objects.isNull(l2SentOut)) {
                    asset.setHasChild(false);
                } else {
                    if (l2SentOut.getGeneratedUnitId().equals(asset.getGeneratedForUnitId())) {
                        asset.setHasChild(true);
                    } else {
                        asset.setHasChild(false);
                    }
                }
                transferredAssetMap.get(asset.getAssetTag()).add(asset);
            } else {
                List<TransferredAsset> transferredAssetList = new ArrayList<>();
                if (Objects.isNull(l2SentOut)) {
                    asset.setHasChild(false);
                } else {
//                    asset.setHasChild(true);
                    if (l2SentOut.getGeneratedUnitId().equals(asset.getGeneratedForUnitId())) {
                        asset.setHasChild(true);
                    } else {
                        asset.setHasChild(false);
                    }
                }
                transferredAssetList.add(asset);
                transferredAssetMap.put(asset.getAssetTag(), transferredAssetList);
            }
        }
        List<TransferredAsset> result = new ArrayList<>();
        List<Integer> toItemIds = new ArrayList<>();
        List<Integer> toIds = new ArrayList<>();
        for (Map.Entry<String, List<TransferredAsset>> entry : transferredAssetMap.entrySet()) {
            List<TransferredAsset> list = entry.getValue();
            if (list.size() > 1) {
                for (TransferredAsset data : list) {
                    data.setAssetStatus("DUPLICATE");
                    setProductName(data);
                    toItemIds.add(data.getToItemId());
                    toIds.add(data.getToId());
                }
            } else {
                for (TransferredAsset data : list) {
                    data.setAssetStatus("UNIQUE");
                    setProductName(data);
                    toItemIds.add(data.getToItemId());
                    toIds.add(data.getToId());
                }
            }
            result.addAll(list);
        }
        List<TransferOrderAssetCorrectionData> assetCorrectionData = transferOrderManagementDao.findAssetCorrectionDataByIds(toItemIds);
        LOG.info("size of Asset correction data is : {}", assetCorrectionData.size());
        if (assetCorrectionData.size() > 0) {
            Map<Integer, TransferOrderAssetCorrectionData> correctionMap = new HashMap<>();
            for (TransferOrderAssetCorrectionData data : assetCorrectionData) {
                if (!correctionMap.containsKey(data.getTransferOrderItemId())) {
                    correctionMap.put(data.getTransferOrderItemId(), data);
                }
            }

            for (TransferredAsset transferredAsset : result) {
                if (correctionMap.containsKey(transferredAsset.getToItemId())) {
                    transferredAsset.setAssetStatus(correctionMap.get(transferredAsset.getToItemId()).getStatus());
                    transferredAsset.setPreviousAssetTag(correctionMap.get(transferredAsset.getToItemId()).getPreviousAssetTag());
                }
            }
        }

        //Gr level Check 1st step taking all the GR data with to id's
        List<GoodsReceivedData> goodsReceivedDataList = transferOrderManagementDao.findGRDataByToIds(toIds);
        LOG.info("size of GR data is : {}", goodsReceivedDataList.size());
        //filtering Gr by checking any GR id in parent Gr
        List<Integer> grIds = goodsReceivedDataList.stream().map(GoodsReceivedData::getId).collect(Collectors.toList());
        List<Integer> filteredGrIds = transferOrderManagementDao.filterGrData(grIds);
        LOG.info("size of filtered GR id's {}", filteredGrIds.size());
        Map<Integer, Integer> toGrMap = getToGrMap(goodsReceivedDataList);
        Map<Integer, Integer> toItemGrItemMap = getToItemGrItemMap(goodsReceivedDataList);
        List<GoodsReceivedData> filteredGrDataList = new ArrayList<>();
        for (GoodsReceivedData goodsReceivedData : goodsReceivedDataList) {
            if (!filteredGrIds.contains(goodsReceivedData.getId())) {
                filteredGrDataList.add(goodsReceivedData);
            }
        }
        for (TransferredAsset asset : result) {
            GoodsReceivedData goodsReceivedData = filteredGrDataList.stream().filter(e -> e.getTransferOrderData().getId().equals(asset.getToId())).findFirst().orElse(null);
            if (Objects.isNull(goodsReceivedData)) {
                asset.setInventoryCheck(false);
            } else {
                LOG.info("inventory found for : {}", asset.getToId());
                asset.setInventoryCheck(true);
            }
            asset.setGrId(toGrMap.get(asset.getToId()));
            asset.setGrItemId(toItemGrItemMap.get(asset.getToItemId()));
        }
        LOG.info("Completed the get Transfers Request at {}", AppUtils.getCurrentTimestamp());
        return result;
    }

    private Map<Integer, Integer> getToItemGrItemMap(List<GoodsReceivedData> goodsReceivedDataList) {
        Map<Integer, Integer> result = new HashMap<>();
        for (GoodsReceivedData data : goodsReceivedDataList) {
            for (GoodsReceivedItemData itemData : data.getGoodsReceivedItemDatas()) {
                result.put(itemData.getTransferOrderItemData().getId(), itemData.getId());
            }
        }
        return result;
    }

    private Map<Integer, Integer> getToGrMap(List<GoodsReceivedData> goodsReceivedDataList) {
        Map<Integer, Integer> result = new HashMap<>();
        for (GoodsReceivedData data : goodsReceivedDataList) {
            result.put(data.getTransferOrderData().getId(), data.getId());
        }
        return result;
    }

    private void setProductName(TransferredAsset data) throws SumoException {
        SkuDefinition skuDefinition = scmCache.getSkuDefinition(data.getSkuId());
        if (Objects.nonNull(skuDefinition)) {
            ProductDefinition productDefinition = scmCache.getProductDefinition(skuDefinition.getLinkedProduct().getId());
            if (Objects.nonNull(productDefinition)) {
                data.setProductId(productDefinition.getProductId());
                data.setProductName(productDefinition.getProductName());
            } else {
                throw new SumoException("Missing Product Definition", "No Linked Product Definition found for SKU Id : " + data.getSkuId() + " Sku Name : " + data.getSkuName());
            }
        } else {
            throw new SumoException("Missing SKU Definition", "No SKU Definition found for SKU Id : " + data.getSkuId() + " Sku Name : " + data.getSkuName());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean bulkUpdateAssetStatus(List<TransferredAsset> assetList, Boolean isOriginal, Integer userId) throws SumoException {
        for (TransferredAsset asset : assetList) {
            updateAssetStatus(asset, isOriginal, userId);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean updateAssetStatus(TransferredAsset asset, Boolean isOriginal, Integer userId) throws SumoException {
        try {
            TransferOrderAssetCorrectionData entry = new TransferOrderAssetCorrectionData();
            entry.setTransferOrderId(asset.getToId());
            entry.setTransferOrderItemId(asset.getToItemId());
            entry.setAssetId(asset.getAssetId());
            entry.setUpdatedBy(asset.getUpdatedBy());
            entry.setLastUpdatedTime(AppUtils.getCurrentTimestamp());
            if (isOriginal) {
                entry.setPreviousAssetTag(asset.getAssetTag());
                entry.setUpdatedAssetTag(asset.getAssetTag());
                entry.setStatus("CORRECTED(ORIGINAL)");
            } else {
                entry.setPreviousAssetTag(asset.getAssetTag());
                entry.setUpdatedAssetTag(asset.getUpdatedAssetTag());
                entry.setStatus("CORRECTED(UPDATED)");
            }
            transferOrderManagementDao.add(entry, true);
            try {
                TransferOrderItemData transferOrderItemData = transferOrderManagementDao.find(TransferOrderItemData.class, asset.getToItemId());
                transferOrderItemData.setAssociatedAssetId(isOriginal ? asset.getAssetId() : asset.getUpdatedAssetId());
                transferOrderItemData.setAssociatedAssetTagValue(isOriginal ? asset.getAssetTag() : asset.getUpdatedAssetTag());
                transferOrderManagementDao.update(transferOrderItemData, true);
                try {
                    AssetDefinitionData assetDefinitionData = transferOrderManagementDao.find(AssetDefinitionData.class, isOriginal ? asset.getAssetId() : asset.getUpdatedAssetId());
//                    AssetDefinitionDataLog assetDefinitionDataLog = scmAssetManagementService.logCurrentStatus(assetDefinitionData);

                    TransferOrderData transferOrderData = transferOrderManagementDao.find(TransferOrderData.class, asset.getToId());
                    TransferOrderType transferOrderType = TransferOrderType.fromValue(transferOrderData.getToType());
                    AssetStatusType nextStatus = AssetStatusType.valueOf(assetDefinitionData.getAssetStatus());
                    UnitDetailData unitDetailData = transferOrderManagementDao.find(UnitDetailData.class, asset.getGeneratedForUnitId());
                    if (transferOrderType.equals(TransferOrderType.RENOVATION_ASSET_TRANSFER)) {
                        nextStatus = AssetStatusType.IN_RENOVATION;
                    } else if (transferOrderType.equals(TransferOrderType.BROKEN_ASSET_TRANSFER)) {

                    } else if (transferOrderType.equals(TransferOrderType.FIXED_ASSET_TRANSFER)) {
                        if (assetDefinitionData.getAssetStatus().equals(AssetStatusType.CREATED.value())
                                || assetDefinitionData.getAssetStatus().equals(AssetStatusType.IN_RENOVATION.value())
                                || assetDefinitionData.getAssetStatus().equals(AssetStatusType.READY_FOR_USE.value())) {
                            nextStatus = AssetStatusType.IN_USE;
                        }
                        if (unitDetailData.getUnitCategory().getCode().equals(UnitCategory.WAREHOUSE.value())) {
                            if (assetDefinitionData.getAssetStatus().equals(AssetStatusType.IN_USE.value())) {
                                nextStatus = AssetStatusType.READY_FOR_USE;
                            } else if (assetDefinitionData.getAssetStatus().equals(AssetStatusType.CREATED.value())) {
                                nextStatus = AssetStatusType.READY_FOR_USE;
                            }
                        }
                        assetDefinitionData.setUnitId(unitDetailData.getUnitId());
                        assetDefinitionData.setUnitType(unitDetailData.getUnitCategory().getCode());
                        assetDefinitionData.setAssetStatus(nextStatus.value());
                        assetDefinitionData.setOwnerType(unitDetailData.getUnitCategory().getCode());
                        assetDefinitionData.setOwnerId(unitDetailData.getUnitId());
                        assetDefinitionData.setLastTransferedBy(userId);
                        assetDefinitionData.setLastTransferDate(AppUtils.getCurrentTimestamp());
                        assetDefinitionData = scmAssetManagementService.updateOwnerAndUnitAndTransferData(assetDefinitionData,
                                unitDetailData.getUnitCategory().getCode(), unitDetailData.getUnitId(), true, nextStatus);
                        assetDefinitionData = transferOrderManagementDao.update(assetDefinitionData, true);
                        scmCache.updateAssetToCache(scmAssetManagementService.convertAssetDefinitionDataToAssetDefinition(assetDefinitionData,false));
                    } else {
                        throw new SumoException("Unexpected transfer order recieved",
                                "Error while doing GR. Please contact support.");
                    }
                    if (!isOriginal) {
                        try {
                            GoodsReceivedItemData goodsReceivedItemData = transferOrderManagementDao.find(GoodsReceivedItemData.class, asset.getGrItemId());
                            goodsReceivedItemData.setAssociatedAssetId(asset.getUpdatedAssetId());
                            goodsReceivedItemData.setAssociatedAssetTagValue(asset.getUpdatedAssetTag());
                            transferOrderManagementDao.update(goodsReceivedItemData, true);
                        } catch (Exception e) {
                            LOG.error("Exception Occurred While Updating Goods Received Item Table ::: ", e);
                            return false;
                        }
                    }
                } catch (Exception e) {
                    LOG.error("Exception Occurred While Updating Asset Definition Table ::: ", e);
                    return false;
                }
            } catch (Exception e) {
                LOG.error("Exception Occurred While Updating Transfer Order Item Data ::: ", e);
                return false;
            }
            LOG.info("Completed the Update Request at {}", AppUtils.getCurrentTimestamp());
            return true;
        } catch (Exception e) {
            LOG.error("Exception Occurred While Updating the Asset Status ::: ", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<TransferOrderData> createBulkTransferOrders(ClubbedTORequest clubbedTORequest, BulkTransferEvent bulkTransferEvent) throws SumoException, DataNotFoundException, TransferOrderCreationException, InventoryUpdateException {
        List<TransferOrder> transferOrderList = requestOrderManagementService.getTransferOrdersFromRequestOrders(clubbedTORequest.getClubRoIds(),
                clubbedTORequest.getSkuToPackagingMapping(), SCMUtil.getDate(clubbedTORequest.getFulfilmentDate()), clubbedTORequest.getGenerationUnit(), clubbedTORequest.getGeneratedBy());
        Map<Integer, GoodsReceivedItem> productItemMap = createproductItemMap(clubbedTORequest.getSkuToPackagingMapping());
        Map<Integer, ProductionBooking> skuToProductionBooking = new HashMap<>();
        List<ProductionBooking> productionBookingList = bulkProductionBooking(clubbedTORequest.getGenerationUnit().getId(), productItemMap, clubbedTORequest.getGeneratedBy(), skuToProductionBooking);
        List<TransferOrderData> transfers = new ArrayList<>();
        for (TransferOrder transferOrder : transferOrderList) {
            transferOrder.setBulkTransferEventId(bulkTransferEvent.getBulkTransferEventId());
            updateProductionBookingFlag(transferOrder.getTransferOrderItems(), skuToProductionBooking);
            transfers.add(createBulkTransfer(transferOrder, false, "0", productionBookingList));
        }
        return transfers;
    }

    private void updateProductionBookingFlag(List<TransferOrderItem> transferOrderItemList, Map<Integer, ProductionBooking> skuToProductionBooking) {
        LOG.info("updating production booking done flag ");
        for (TransferOrderItem transferOrderItem : transferOrderItemList) {
            if (skuToProductionBooking.containsKey(transferOrderItem.getSkuId())) {
                //updation of production booking flag and booking quantity at RO Item Level
                if (Objects.nonNull(transferOrderItem.getRequestOrderItemId())) {
                    RequestOrderItemData requestOrderItemData = requestOrderManagementDao.find(RequestOrderItemData.class, transferOrderItem.getRequestOrderItemId());
                    requestOrderItemData.setProductionBookingCompleted(AppUtils.setStatus(true));
                    requestOrderItemData.setProductionBookingQuantity(requestOrderItemData.getRequestedQuantity());
                    requestOrderManagementDao.update(requestOrderItemData, false);
                }


                //sets productionId at TO Item level
                transferOrderItem.setProductionId(skuToProductionBooking.get(transferOrderItem.getSkuId()).getBookingId());
            }
        }
        requestOrderManagementDao.flush();
        LOG.info("Succesfully Updated Production Booking Done Flag ");
    }

    private TransferOrderData createBulkTransfer(TransferOrder transferOrder, boolean external, String invoiceId, List<ProductionBooking> productionBookingList
    ) throws SumoException, DataNotFoundException, TransferOrderCreationException, InventoryUpdateException {
        TransferOrderData transferOrderData = createTransferOrder(transferOrder, external, invoiceId, productionBookingList);
        if (transferOrderData.getToType().equals(TransferOrderType.BROKEN_ASSET_TRANSFER.value())
                || transferOrderData.getToType().equals(TransferOrderType.FIXED_ASSET_TRANSFER.value())
                || transferOrderData.getToType().equals(TransferOrderType.RENOVATION_ASSET_TRANSFER.value())) {
            if (transferOrderData.getType() != null && transferOrderData.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())) {
                if (transferOrder.getTotalCost() != null) {
                    BigDecimal total = new BigDecimal(transferOrder.getTotalCost().toString());
                    LOG.info("Total Amount for this TO is {}", total);
                    if (total.compareTo(BigDecimal.ZERO) > 0) {
                        LOG.info("Updating budget for FIXED ASSETS Create Transfer");
                        if (!updateBudgetForAssetTransfer(transferOrderData, total, true)) {
                            throw new SumoException("Error in Budget Updation", "Cannot Update Budget for <b>FIXED_ASSETS(FA_Equipment)</b> while Creating Transfer..!");
                        }
                    }
                }
            }
        }
        return transferOrderData;
        // return transferOrderData.getId() != null? transferOrderData.getId() : 1;
    }

    private Map<Integer, GoodsReceivedItem> createproductItemMap(Map<Integer, Map<Integer, GoodsReceivedItem>> skuToPackagingMapping) {
        Map<Integer, GoodsReceivedItem> productItemMap = new HashMap<>();
        for (Integer unitId : skuToPackagingMapping.keySet()) {
            Map<Integer, GoodsReceivedItem> tempMap = skuToPackagingMapping.get(unitId);
            if (Objects.isNull(tempMap) || tempMap.isEmpty()) {
                continue;
            }
            for (Integer productId : tempMap.keySet()) {
                if (tempMap.get(productId).getTransferredQuantity().equals(Float.valueOf(0))) {
                    continue;
                }
                if (!productItemMap.containsKey(productId)) {
                    productItemMap.put(productId, tempMap.get(productId));
                    productItemMap.get(productId).setPackagingDetails(null);
                } else {
                    GoodsReceivedItem productItem = productItemMap.get(productId);
                    productItem.setTransferredQuantity(BigDecimal.valueOf(productItem.getTransferredQuantity()).add(BigDecimal.valueOf(tempMap.get(productId).getTransferredQuantity())).floatValue());
                }
            }
        }
        return productItemMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Integer createExternalTransferOrder(TransferOrder transferOrder, String invoiceId)
            throws TransferOrderCreationException, InventoryUpdateException, SumoException, DataNotFoundException {
        Optional<VendorDetail> vendorDetail = Optional
                .ofNullable(scmCache.getVendorDetail(transferOrder.getVendorId()));

        if (!vendorDetail.isPresent()) {
            throw new TransferOrderCreationException("Transfer cannot be created since vendor is not present");
        }

        Optional<VendorDispatchLocation> dispatchLocation = SCMUtil.getDispatchLocation(vendorDetail.get(),
                transferOrder.getDispatchId());
        if (!dispatchLocation.isPresent()) {
            throw new TransferOrderCreationException(
                    "Transfer cannot be created since dispatch location is not present");
        }

        UnitBasicDetail unitBasicDetail = masterDataCache
                .getUnitBasicDetail(transferOrder.getGenerationUnitId().getId());
        String unitRegion = unitBasicDetail.getRegion();
        Integer scrapUnitId = props.getScrapUnitId(unitRegion);
        UnitDetail generatedFor = scmCache.getUnitDetail(scrapUnitId);
        IdCodeName generatedForUnit = SCMUtil.generateIdCodeName(generatedFor.getUnitId(), "SCRAP",
                generatedFor.getUnitName());

        transferOrder.setGeneratedForUnitId(generatedForUnit);
        transferOrder.setStatus(SCMOrderStatus.SETTLED);
        transferOrder.getTransferOrderItems().forEach(
                transferOrderItem -> transferOrderItem.setNegotiatedUnitPrice(transferOrderItem.getUnitPrice()));

        TransferOrderData transferOrderData = createTransfer(transferOrder, true, invoiceId);
        try {
            ExternalTransferDetailData externalDetails = new ExternalTransferDetailData();
            externalDetails.setVendorId(transferOrder.getVendorId());
            externalDetails.setDispatchId(transferOrder.getDispatchId());
            externalDetails.setTransferOrderData(transferOrderData);
            externalDetails.setApprovalStatus(SCMOrderStatus.INITIATED.name());
            externalDetails.setLastUpdatedBy(transferOrder.getGeneratedBy().getId());
            externalDetails.setLastUpdateTime(transferOrderData.getLastUpdateTime());
            externalDetails.setLocationName(dispatchLocation.get().getLocationId());
            externalDetails.setVendorName(vendorDetail.get().getEntityName());

            transferOrderManagementDao.add(externalDetails, true);
        } catch (Exception e) {
            LOG.error("Encountered error while storing external details of transfer order:::: {}",
                    transferOrderData.getId(), e);
            throw e;
        }

        return transferOrderData.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean approveExternalTransferOrder(Integer transferOrderId, Integer userId)
            throws DataNotFoundException, DataUpdationException {
        boolean flag = false;

        TransferOrderData transferOrderData = transferOrderManagementDao.find(TransferOrderData.class, transferOrderId);
        if (transferOrderData != null) {
            if (SCMUtil.getStatus(transferOrderData.getExternal())) {
                ExternalTransferDetailData transferDetailData = transferOrderData.getExternalTransferDetail();
                if (transferDetailData != null) {
                    transferDetailData.setApprovalStatus(SCMOrderStatus.ACKNOWLEDGED.name());
                    transferDetailData.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
                    transferDetailData.setLastUpdatedBy(userId);
                    transferDetailData = transferOrderManagementDao.update(transferDetailData, true);
                    if (transferDetailData != null) {
                        flag = true;
                    } else {
                        throw new DataUpdationException("Error while updating external details for transfer");
                    }
                } else {
                    throw new DataNotFoundException("Could not find external details to approve");
                }
            } else {
                throw new DataNotFoundException("This is not a valid external transfer");
            }
        } else {
            throw new DataNotFoundException("Could not find any transfer of this id");
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Integer checkIfAlreadyCreated(Integer requestOrderId) {
        TransferOrderData transfer = transferOrderManagementDao.findByRequestOrderId(requestOrderId);
        return transfer != null ? transfer.getId() : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public void updateRequestOrder(Integer requestOrderId) {
        RequestOrderData requestOrderData = transferOrderManagementDao.find(RequestOrderData.class, requestOrderId);
        requestOrderData.setStatus(SCMOrderStatus.TRANSFERRED.name());
        transferOrderManagementDao.update(requestOrderData, true);
    }

    private Float getTotalTransferOrderAmount(TransferOrder transferOrder) {
        BigDecimal total = BigDecimal.ZERO;
        for (TransferOrderItem transferOrderItem : transferOrder.getTransferOrderItems()) {
            total = AppUtils.add(total,
                    AppUtils.multiplyWithScale10(new BigDecimal(transferOrderItem.getTransferredQuantity()),
                            (transferOrderItem.getPrice() == null
                                    ? SCMUtil.convertToBigDecimal(transferOrderItem.getNegotiatedUnitPrice())
                                    : transferOrderItem.getPrice())));
        }
        return total.floatValue();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ExpiryDataRequest getTransferOrderItemExpiries(ExpiryDataRequest transferOrder)
            throws InventoryUpdateException, DataNotFoundException {
        ExpiryDataRequest data = priceDao.checkConsumableData(transferOrder);
        /*
         * for(Object d : data.getInventoryItems()) { ItemExpiryData ied =
         * (ItemExpiryData)d;
         * ied.setAvailableDates(AppUtils.createAvailableExpiryDate(ied.getExpiryDate(),
         * 0)); }
         */
        return data;
    }

    @Override
    public String getEwayBillNumber(int transferOrderId) {
        return transferOrderManagementDao.getEwayBill(transferOrderId);
    }


    public void sendZeroTransferNotification(TransferOrderData transferOrderData) {
        UnitBasicDetail receivingUnit = masterDataCache.getUnitBasicDetail(transferOrderData.getGeneratedForUnitId());
        UnitBasicDetail transferredUnit = masterDataCache.getUnitBasicDetail(transferOrderData.getGenerationUnitId());

        Boolean flag = false;
        StringBuilder message = new StringBuilder("ZERO TRANSFER ORDER ITEM NOTIFICATION\n");
        message.append(
                "Transfer Order Id : " + transferOrderData.getId() +
                        " Transfer From : " + transferredUnit.getName() +
                        " To : " + receivingUnit.getName() + "\n");
        message.append(
                "At : " + SCMUtil.getCurrentTimestamp() +
                        " Generated By : " + masterDataCache.getEmployee(transferOrderData.getGeneratedBy()) +
                        " Fulfillment Date : " + AppUtils.getFormattedDate(transferOrderData.getRequestOrderData().getFulfillmentDate()) + "\n");
        for (TransferOrderItemData itemData : transferOrderData.getTransferOrderItemDatas()) {
            if (itemData.getTransferredQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                flag = true;
                message.append(
                        "Sku Id : " + itemData.getSkuId() +
                                " Sku Name : " + itemData.getSkuName() +
                                " Requested Quantity : " + itemData.getRequestedQuantity() +
                                " Transferred Quantity : " + itemData.getTransferredQuantity() + "\n");
            }
        }
        message.append("\n");
        if (flag == true) {
            try {
                //Send Notification To Area Manager
                EmployeeBasicDetail e1 = masterDataCache.getEmployeeBasicDetail(receivingUnit.getUnitManagerId());
                if (e1 != null && e1.getSlackChannel() != null && e1.getSlackChannel().trim().length() > 0) {
                    LOG.info("Slack to AM {} ", e1.getSlackChannel());
                    SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "TRANSFER ORDER NOTIFICATION", e1.getSlackChannel(), null, message.toString());
                }

                //Send Notification TO Cafe Manager
                EmployeeBasicDetail e2 = masterDataCache.getEmployeeBasicDetail(receivingUnit.getCafeManagerId());
                if (e2 != null && e1.getSlackChannel() != null && e2.getSlackChannel().trim().length() > 0) {
                    LOG.info("Slack to DAM {} ", e2.getSlackChannel());
                    SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "TRANSFER ORDER NOTIFICATION", e2.getSlackChannel(), null, message.toString());
                }
            } catch (Exception e) {
                LOG.info(":::::: Could Not Send Slack Notification To Manager And Area Manager :::::::", e);
            }

            System.out.println(message);
            SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "TRANSFER ORDER NOTIFICATION", null,
                    SlackNotification.ZERO_QUANTITY_TRANSFER.getChannel(props.getEnvType()), message.toString());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public synchronized String getInvoiceId(int generationUnitId, int generatedForUnitId, boolean isDifferentCompanyId) {
        State fromState = masterDataCache.getUnit(generationUnitId).getLocation().getState();
        State toState = masterDataCache.getUnit(generatedForUnitId).getLocation().getState();
        boolean isInvoice = isDifferentCompanyId
                || fromState.getId() != toState.getId();
        Integer financialYear = AppUtils.getFinancialYear();
        if ( financialYear <= 2024) {
            return isInvoice
                    ? fromState.getCode() + "/OST/"
                    + transferOrderManagementDao.getNextStateInvoiceId(fromState.getId(), "TRANSFER_ORDER_ID")
                    : fromState.getCode() + "/DC/"
                    + transferOrderManagementDao.getNextStateInvoiceId(fromState.getId(), "DELIVERY_CHALLAN_ID");
        } else {
            return isInvoice
                    ? fromState.getCode() + "/OST/" + (financialYear%100)+ "/"
                    + transferOrderManagementDao.getNextStateInvoiceId(fromState.getId(), "TRANSFER_ORDER_ID",financialYear)
                    : fromState.getCode() + "/DC/" + (financialYear%100)+ "/"
                    + transferOrderManagementDao.getNextStateInvoiceId(fromState.getId(), "DELIVERY_CHALLAN_ID", financialYear);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean getAlreadyTransferredRO(List<Integer> roIds) {
        return transferOrderManagementDao.getAlreadyTransferredRO(roIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean getAlreadyTransferredBulkRO(List<Integer> roIds) {
        if (!transferOrderManagementDao.getAlreadyTransferredRO(roIds)) {
            return Objects.nonNull(transferOrderManagementDao.findByRequestOrderIds(roIds));
        } else {
            return true;
        }
    }

    private ClubbedTORequest getTransferRequest(Integer unitId, Integer userId) {
        ClubbedTORequest clubbedTORequest = new ClubbedTORequest();
        clubbedTORequest.setGeneratedBy(new IdCodeName(userId, "", ""));
        clubbedTORequest.setGenerationUnit(new IdCodeName(unitId, "", ""));
        return clubbedTORequest;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public BulkTransferEvent initiateBulkTransferEvent(ClubbedTORequest clubbedTORequest, Integer userId,
                                                       Integer unitId, String type) throws SumoException {
        if (Objects.isNull(clubbedTORequest)) {
            clubbedTORequest = getTransferRequest(unitId, userId);
        }
        BulkTransferEventData bulkTransferEventData = new BulkTransferEventData();
        if (Objects.nonNull(clubbedTORequest.getClubRoIds())) {
            bulkTransferEventData.setRoCount(clubbedTORequest.getClubRoIds().size());
        }
        bulkTransferEventData.setGeneratedBy(clubbedTORequest.getGeneratedBy().getId());
        bulkTransferEventData.setSourceCompanyId(masterDataCache.getUnit(clubbedTORequest.getGenerationUnit().
                getId()).getCompany().getId());
        bulkTransferEventData.setGenerationUnitId(clubbedTORequest.getGenerationUnit().getId());
        bulkTransferEventData.setInitiationTime(SCMUtil.getCurrentTimestamp());
        bulkTransferEventData.setStatus(SCMOrderStatus.INITIATED.value());
        bulkTransferEventData.setType(type);
        BulkTransferEvent bulkTransferEvent = SCMDataConverter.convert(transferOrderManagementDao.add(bulkTransferEventData, true), masterDataCache, scmCache, false);
        return bulkTransferEvent;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void closeBulkTransferEvent(Integer TOCount, Integer bulkTransferEventId, Boolean isInvoiceSet) {
        BulkTransferEventData bulkTransferEventData = transferOrderManagementDao.find(BulkTransferEventData.class, bulkTransferEventId);
        bulkTransferEventData.setToCount(TOCount);
        bulkTransferEventData.setCompletionTime(SCMUtil.getCurrentTimestamp());
        bulkTransferEventData.setStatus(SCMOrderStatus.TRANSFERRED.value());
        bulkTransferEventData.setIsInvoiceSet(SCMUtil.setStatus(isInvoiceSet));
        transferOrderManagementDao.update(bulkTransferEventData, true);
    }


    private List<SCMOrderPackaging> addPackagings(List<SCMOrderPackaging> current, List<MultiPackagingAdjustmentsData> toAdd) {
        Map<Integer, SCMOrderPackaging> pkgMap = new HashMap<>();
        pkgMap = current.stream().collect(Collectors.toMap(pkg -> pkg.getPackagingDefinitionData().getPackagingId(), Function.identity()));
        Map<Integer, SCMOrderPackaging> finalPkgMap = pkgMap;
        toAdd.forEach(newPkg -> {
            if (finalPkgMap.containsKey(newPkg.getPackagingId())) {
                SCMOrderPackaging oldPkg = finalPkgMap.get(newPkg.getPackagingId());
                oldPkg.setNumberOfUnitsPacked(BigDecimal.valueOf(oldPkg.getNumberOfUnitsPacked()).add(newPkg.getQuantity()).floatValue());
                oldPkg.setTransferredQuantity(SCMUtil.multiply(oldPkg.getNumberOfUnitsPacked(), oldPkg.getPackagingDefinitionData().getConversionRatio()));
                finalPkgMap.put(newPkg.getPackagingId(), oldPkg);
            } else {
                finalPkgMap.put(newPkg.getPackagingId(), getPackagingObject(newPkg.getQuantity().floatValue(), newPkg.getPackagingId(), true).get(0));
            }
        });

        return new ArrayList<>(finalPkgMap.values());

    }

    private List<SCMOrderPackaging> addDefaultPackagings(List<SCMOrderPackaging> current, List<SCMOrderPackaging> toAdd) {
        Map<Integer, SCMOrderPackaging> pkgMap = new HashMap<>();
        pkgMap = current.stream().collect(Collectors.toMap(pkg -> pkg.getPackagingDefinitionData().getPackagingId(), Function.identity()));
        Map<Integer, SCMOrderPackaging> finalPkgMap = pkgMap;
        toAdd.forEach(newPkg -> {
            if (finalPkgMap.containsKey(newPkg.getPackagingDefinitionData().getPackagingId())) {
                SCMOrderPackaging oldPkg = finalPkgMap.get(newPkg.getPackagingDefinitionData().getPackagingId());
                oldPkg.setNumberOfUnitsPacked(oldPkg.getNumberOfUnitsPacked() + newPkg.getNumberOfUnitsPacked());
                oldPkg.setTransferredQuantity(SCMUtil.multiply(oldPkg.getNumberOfUnitsPacked(), oldPkg.getPackagingDefinitionData().getConversionRatio()));
                finalPkgMap.put(newPkg.getPackagingDefinitionData().getPackagingId(), oldPkg);
            } else {
                finalPkgMap.put(newPkg.getPackagingDefinitionData().getPackagingId(), newPkg);
            }
        });

        return new ArrayList<>(finalPkgMap.values());

    }

    private List<SCMOrderPackaging> getDefaultPackagingDistribution(Float quantity, Integer skuId) {
        LOG.info("Trying To Auto Distribute Packaging For Sku : {}", skuId);
        List<SCMOrderPackaging> packagingList = new ArrayList<>();
        List<PackagingDefinition> skuPackagingList = scmCache.getSkuPackagingMappings(skuId).stream().map(skuPackagingMapping -> scmCache.getPackagingDefinition(skuPackagingMapping.getPackagingId())
        ).collect(Collectors.toList());
        quantity = distributePackaging(skuId, PackagingType.CASE, quantity, skuPackagingList, packagingList);
        if (quantity.compareTo(Float.valueOf(0)) > 0) {
            quantity = distributePackaging(skuId, PackagingType.INNER, quantity, skuPackagingList, packagingList);
        }
        if (quantity.compareTo(Float.valueOf(0)) > 0) {
            quantity = distributePackaging(skuId, PackagingType.LOOSE, quantity, skuPackagingList, packagingList);
        }

        return packagingList;
    }

    private List<PackagingDefinition> sortPackaging(List<PackagingDefinition> packagingList, PackagingType type) {
        List<PackagingDefinition> tempPackagingList = packagingList.stream().filter(scmOrderPackaging -> scmOrderPackaging.getPackagingType().equals(type)).sorted((p1, p2) -> {
            return (int) (p2.getConversionRatio() - p1.getConversionRatio());
        }).collect(Collectors.toList());
        return tempPackagingList;
    }

    private Float distributePackaging(Integer skuId, PackagingType type, Float quantity, List<PackagingDefinition> skuPackagingList,
                                      List<SCMOrderPackaging> distribution) {
        List<PackagingDefinition> sortedPackigingList = sortPackaging(skuPackagingList, type);
        final Float[] remaining = {quantity};
        sortedPackigingList.forEach(scmOrderPackaging -> {
            Float unitsPacked = Float.valueOf(0);
            if (scmOrderPackaging.getPackagingType().equals(type)) {
                if (type.equals(PackagingType.LOOSE)) {
                    if ((remaining[0] / scmOrderPackaging.getConversionRatio()) > 0) {
                        unitsPacked = SCMUtil.divide(BigDecimal.valueOf(remaining[0]), BigDecimal.valueOf(scmOrderPackaging.getConversionRatio())).floatValue();
                        remaining[0] = remaining[0] - SCMUtil.multiply(unitsPacked, scmOrderPackaging.getConversionRatio());
                    }
                } else {
                    if ((int) (remaining[0] / scmOrderPackaging.getConversionRatio()) > 0) {
                        unitsPacked = Float.valueOf((float) Math.floor(remaining[0] / scmOrderPackaging.getConversionRatio()));
                        remaining[0] = remaining[0] - SCMUtil.multiply(unitsPacked, scmOrderPackaging.getConversionRatio());
                    }
                }
                if (unitsPacked.compareTo(Float.valueOf(0)) > 0) {
                    SCMOrderPackaging scmPackaging = new SCMOrderPackaging();
                    scmPackaging.setNumberOfUnitsPacked(unitsPacked);
                    scmPackaging.setTransferredQuantity(SCMUtil.multiply(unitsPacked, scmOrderPackaging.getConversionRatio()));
                    scmPackaging.setPackagingDefinitionData(scmOrderPackaging);
                    distribution.add(scmPackaging);
                }
            }
        });
        return remaining[0];
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public SkuToProductResponse getSkuToProducts(Integer unitId, List<Integer> roIds, Boolean isPackagingRequired, Map<Integer, Integer> productSkuMap) throws SumoException {
        Boolean isSkuMappingMissing = false;
        try {
            LOG.info("Fetching Active Product To Skus Mappings For Unit Id : {}", unitId);

            Map<Integer, List<MultiPackagingAdjustmentsData>> roPkgMap = new HashMap<>();
            Map<Integer, Map<Integer, List<SCMOrderPackaging>>> productUnitPkg = new HashMap<>();
            List<RequestOrderData> requestOrderDataList = requestOrderManagementDao.getRequestOrdersByIds(null, null, null, SCMOrderStatus.ACKNOWLEDGED, roIds);
            List<Integer> roItemIds = new ArrayList<>();
            requestOrderDataList.forEach(ro -> {
                roItemIds.addAll(ro.getRequestOrderItemDatas().stream().map(roItem -> roItem.getId()).collect(Collectors.toList()));
            });
            transferOrderManagementDao.getAdjustedPackaging(roItemIds).ifPresent(pkgs -> {
                pkgs.forEach(pkg -> {
                    roPkgMap.computeIfAbsent(pkg.getRequestOrderItemData().getId(), key -> new ArrayList<>())
                            .add(pkg);
                });
            });
            SkuToProductResponse skuToProductResponse = new SkuToProductResponse();
            Set<Integer> productIds = new HashSet<>();
            for (RequestOrderData requestOrderData : requestOrderDataList) {
                for (RequestOrderItemData requestOrderItemData : requestOrderData.getRequestOrderItemDatas()) {
                    Map<Integer, Map<Integer, RequestOrderItem>> orderItems = skuToProductResponse.getOrderItems();
                    if (!orderItems.containsKey(requestOrderItemData.getProductId())) {
                        orderItems.put(requestOrderItemData.getProductId(), new HashMap<>());
                        productUnitPkg.put(requestOrderItemData.getProductId(), new HashMap<>());
                    }
                    if (!orderItems.get(requestOrderItemData.getProductId()).containsKey(requestOrderData.getRequestUnitId())) {
                        RequestOrderItem requestOrderItem = new RequestOrderItem();
                        requestOrderItem.setProductId(requestOrderItemData.getProductId());
                        requestOrderItem.setOriginalQuantity(requestOrderItemData.getOriginalQuantity());
                        requestOrderItem.setRequestedQuantity(requestOrderItemData.getRequestedQuantity().floatValue());
                        requestOrderItem.setRequestedAbsoluteQuantity(requestOrderItemData.getRequestedAbsoluteQuantity().floatValue());
                        requestOrderItem.setProductName(requestOrderItemData.getProductName());
                        requestOrderItem.setUnitOfMeasure(requestOrderItemData.getUnitOfMeasure());
                        orderItems.get(requestOrderItemData.getProductId()).put(requestOrderData.getRequestUnitId(), requestOrderItem);

                    } else {
                        RequestOrderItem requestOrderItem = orderItems.get(requestOrderItemData.getProductId()).get(requestOrderData.getRequestUnitId());
                        requestOrderItem.setOriginalQuantity(requestOrderItem.getOriginalQuantity().add(requestOrderItemData.getOriginalQuantity()));
                        requestOrderItem.setRequestedQuantity(BigDecimal.valueOf(requestOrderItem.getRequestedQuantity()).add(requestOrderItemData.getRequestedQuantity()).floatValue());
                        requestOrderItem.setRequestedAbsoluteQuantity(BigDecimal.valueOf(requestOrderItem.getRequestedAbsoluteQuantity()).add(requestOrderItemData.getRequestedAbsoluteQuantity()).floatValue());
                        if (Objects.nonNull(requestOrderItemData.getExcessQuantity())) {
                            if (Objects.isNull(requestOrderItem.getExcessQuantity())) {
                                requestOrderItem.setExcessQuantity(requestOrderItemData.getExcessQuantity());
                            } else {
                                requestOrderItem.setExcessQuantity(requestOrderItem.getExcessQuantity().add(requestOrderItemData.getExcessQuantity()));
                            }
                        }
                    }
                    if (isPackagingRequired.equals(Boolean.TRUE)) {
                        if (!productSkuMap.containsKey(requestOrderItemData.getProductId()) &&
                                BigDecimal.ZERO.compareTo(requestOrderItemData.getRequestedQuantity()) != 0) {
                            isSkuMappingMissing = true;
                            throw new SumoException("There is No Sku Mapped With Product : " +
                                    scmCache.getProductDefinition(requestOrderItemData.getProductId()).getProductName());
                        }
                        if (!productUnitPkg.get(requestOrderItemData.getProductId()).containsKey(requestOrderData.getRequestUnitId())) {
                            productUnitPkg.get(requestOrderItemData.getProductId()).put(requestOrderData.getRequestUnitId(), new ArrayList<>());
                        }
                        if (roPkgMap.containsKey(requestOrderItemData.getId())) {
                            productUnitPkg.get(requestOrderItemData.getProductId()).put(requestOrderData.getRequestUnitId(), addPackagings(productUnitPkg.get(requestOrderItemData.getProductId()).get(requestOrderData.getRequestUnitId())
                                    , roPkgMap.get(requestOrderItemData.getId())));
                        } else {
                            productUnitPkg.get(requestOrderItemData.getProductId()).put(requestOrderData.getRequestUnitId(), addDefaultPackagings(productUnitPkg.get(requestOrderItemData.getProductId()).get(requestOrderData.getRequestUnitId())
                                    , getDefaultPackagingDistribution(requestOrderItemData.getRequestedQuantity().floatValue(), productSkuMap.get(requestOrderItemData.getProductId()))));
                        }
                    }

                    skuToProductResponse.setOrderItems(orderItems);
                    productIds.add(requestOrderItemData.getProductId());
                }
            }
            List<Integer> productIdList = new ArrayList<>(productIds);
            if (isPackagingRequired.equals(Boolean.FALSE)) {
                Map<Integer, List<SkuDefinition>> productToSkus = scmProductManagementService.viewAllActiveSkuByUnitId(unitId, productIdList);
                skuToProductResponse.setProductToSkus(productToSkus);
            }
            skuToProductResponse.setProductUnitPackaging(productUnitPkg);
            return skuToProductResponse;
        } catch (Exception e) {
            if (isSkuMappingMissing.equals(Boolean.TRUE)) {
                throw new SumoException(e.getMessage());
            } else {
                throw new SumoException("Error while fetching Product To Sku Mapping ::::::: ", e);
            }

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<BulkTransferEvent> findBulkEventsShort(Integer unitId, Date startDate, Date endDate) {
        List<BulkTransferEvent> bulkTransferEventList = transferOrderManagementDao.findBulkEventByDates(unitId, startDate, endDate, null, true).stream()
                .map(event -> SCMDataConverter.convert(event, masterDataCache, scmCache, false))
                .collect(Collectors.toList());
        if (bulkTransferEventList.isEmpty()) {
            LOG.info("No Bulk Event Found For This Dates");
        }
        return bulkTransferEventList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public BulkTransferEvent findBulkEvent(Integer unitId, Integer bulkEventId) {
        List<BulkTransferEvent> bulkTransferEventList = transferOrderManagementDao.findBulkEventByDates(unitId, null, null, bulkEventId, false).stream()
                .map(event -> SCMDataConverter.convert(event, masterDataCache, scmCache, true))
                .collect(Collectors.toList());
        if (bulkTransferEventList.isEmpty()) {
            LOG.info("No Bulk Event Found For This Dates");
            return null;
        }
        return bulkTransferEventList.get(0);

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<TransferOrder> getTOsByIds(List<Integer> toIds) {
        List<TransferOrder> transferOrders = new ArrayList<>();
        transferOrders = transferOrderManagementDao.findByTransferIds(toIds).stream().
                /*  filter(transferOrder -> !transferOrder.getStatus().equals(SCMOrderStatus.CANCELLED)).*/
                        map(transferOrder -> SCMDataConverter.convert(transferOrder, true, scmCache, masterDataCache))
                .collect(Collectors.toList());
        return transferOrders;
    }


    @Override
    public List<TransferOrder> getTransferObjects(Map<Integer, Map<Integer, GoodsReceivedItem>> unitWiseDistribution,
                                                  Integer unitId, Integer userId, Boolean isStandAlone) {
        List<TransferOrder> transferOrderList = new ArrayList<>();
        for (Integer generationForUnitId : unitWiseDistribution.keySet()) {
            if (unitWiseDistribution.get(generationForUnitId).isEmpty()) {
                //unitWiseDistribution.remove(generationForUnitId);
                continue;
            }
            TransferOrder transferOrder = new TransferOrder();
            transferOrder.setGeneratedBy(new IdCodeName(userId, "", masterDataCache.getEmployee(userId)));
            transferOrder.setGenerationUnitId(new IdCodeName(unitId, "", scmCache.getUnitDetail(unitId).getUnitName()));
            transferOrder.setGeneratedForUnitId(new IdCodeName(generationForUnitId, "", scmCache.getUnitDetail(generationForUnitId).getUnitName()));
            transferOrder.setStatus(SCMOrderStatus.CREATED);
            transferOrder.setToType(TransferOrderType.REGULAR_TRANSFER);
            transferOrder.setLastUpdatedBy(new IdCodeName(userId, "", masterDataCache.getEmployee(userId)));
            transferOrder.setTransferOrderItems(getTransferOrderItemObjects(unitWiseDistribution.get(generationForUnitId), isStandAlone));
            transferOrderList.add(transferOrder);

        }
        return transferOrderList;
    }

    private List<TransferOrderItem> getTransferOrderItemObjects(Map<Integer, GoodsReceivedItem> skuItems, Boolean isStandAlone) {
        List<TransferOrderItem> transferOrderItemList = new ArrayList<>();
        for (Integer skuId : skuItems.keySet()) {
            GoodsReceivedItem item = skuItems.get(skuId);
            TransferOrderItem transferOrderItem = new TransferOrderItem();
            transferOrderItem.setTransferredQuantity(item.getTransferredQuantity());
            transferOrderItem.setSkuId(item.getSkuId());
            transferOrderItem.setSkuName(item.getSkuName());
            transferOrderItem.setProductId(item.getProductId());
            transferOrderItem.setUnitOfMeasure(item.getUnitOfMeasure());
            transferOrderItem.setUnitPrice(item.getUnitPrice());
            transferOrderItem.setNegotiatedUnitPrice(item.getNegotiatedUnitPrice());
            transferOrderItem.setPackagingDetails(item.getPackagingDetails());
            transferOrderItem.setDrillDowns(item.getDrillDowns());
            transferOrderItem.setRequestOrderItemId(item.getRequestOrderItemId());
            if (Objects.nonNull(isStandAlone) && Boolean.FALSE.equals(isStandAlone)) {
                transferOrderItem.setRequestedQuantity(item.getTransferredQuantity());
            }
            transferOrderItemList.add(transferOrderItem);
        }
        return transferOrderItemList;
    }

    @Override
    public GoodsReceivedItem setSkuDistribution(Integer skuId, Float quantity, Integer unitId, Integer packagingId, Boolean isPkgQty) {
        GoodsReceivedItem goodsReceivedItem = new GoodsReceivedItem();
        goodsReceivedItem.setId(1);
        SkuDefinition sku = scmCache.getSkuDefinition(skuId);
        goodsReceivedItem.setSkuId(skuId);
        goodsReceivedItem.setSkuName(sku.getSkuName());
        goodsReceivedItem.setUnitOfMeasure(sku.getUnitOfMeasure());
        goodsReceivedItem.setPackagingDetails(getPackagingObject(quantity, packagingId, isPkgQty));
        goodsReceivedItem.setTransferredQuantity(goodsReceivedItem.getPackagingDetails().get(0).getTransferredQuantity());
        goodsReceivedItem.setProductId(sku.getLinkedProduct().getId());
        goodsReceivedItem.setNegotiatedUnitPrice(Double.valueOf(sku.getNegotiatedUnitPrice()));
        goodsReceivedItem.setUnitPrice(Double.valueOf(sku.getUnitPrice()));
        return goodsReceivedItem;
    }


    private List<SCMOrderPackaging> getPackagingObject(Float quantity, Integer packagingId, Boolean isPkgQty) {
        List<SCMOrderPackaging> packagings = new ArrayList<>();
         /*Optional<SkuPackagingMapping> mapping =  scmCache.getSkuPackagingMappings(skuId).stream().filter(packaging -> Boolean.valueOf(packaging.isIsDefault()).
                 equals(Boolean.TRUE)).findFirst();*/
        if (Objects.nonNull(packagingId)) {
            PackagingDefinition packagingDefinition = scmCache.getPackagingDefinition(packagingId);
            SCMOrderPackaging scmOrderPackaging = new SCMOrderPackaging();

            if (Boolean.TRUE.equals(isPkgQty)) {
                scmOrderPackaging.setNumberOfUnitsPacked(quantity);
                scmOrderPackaging.setTransferredQuantity(SCMUtil.multiply(scmOrderPackaging.getNumberOfUnitsPacked(), packagingDefinition.getConversionRatio()));
            } else {
                scmOrderPackaging.setNumberOfUnitsPacked(SCMUtil.divide(BigDecimal.valueOf(quantity),
                        BigDecimal.valueOf(packagingDefinition.getConversionRatio())).floatValue());
                scmOrderPackaging.setTransferredQuantity(quantity);
            }
            scmOrderPackaging.setPackagingDefinitionData(packagingDefinition);
            packagings.add(scmOrderPackaging);
        }
        return packagings;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<TransferOrderData> bulkStandAloneTransfer(Map<Integer, Map<Integer, GoodsReceivedItem>> unitWiseDistribution, Integer unitId,
                                                          Integer userId, Integer bulkEventId) throws DataNotFoundException, SumoException, TransferOrderCreationException, InventoryUpdateException {
        setExpiryForStandAlone(unitWiseDistribution, unitId);
        List<TransferOrder> transferOrderList = getTransferObjects(unitWiseDistribution, unitId, userId, true);
        Map<Integer, GoodsReceivedItem> productItemMap = createproductItemMap(unitWiseDistribution);
        Map<Integer, ProductionBooking> skuToProductionBooking = new HashMap<>();
        List<ProductionBooking> productionBookingList = bulkProductionBooking(unitId, productItemMap, new IdCodeName(userId, "", ""), skuToProductionBooking);
        List<TransferOrderData> transfers = new ArrayList<>();
        for (TransferOrder transferOrder : transferOrderList) {
            transferOrder.setBulkTransferEventId(bulkEventId);
            updateProductionBookingFlag(transferOrder.getTransferOrderItems(), skuToProductionBooking);
            transfers.add(createBulkTransfer(transferOrder, false, "0", productionBookingList));
        }
        return transfers;

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Integer> getAllActiveReceivingUnits(FulfillmentType fulfillmentType, Integer unitId) {
        List<Integer> unitIds = requestOrderManagementDao.getALlReceivingUnits(fulfillmentType, unitId).stream().filter(id -> {
            Unit unit = masterDataCache.getUnit(id);
            if (unit.isLive() && unit.getStatus().equals(UnitStatus.ACTIVE)) {
                return true;
            } else {
                return false;
            }
        }).collect(Collectors.toList());
        Collections.sort(unitIds);
        LOG.info("Number Of Active Recieving Units For  {} is {} ", unitId, unitIds.size());
        return unitIds;

    }

    @Override
    public Boolean validateStandAloneSheet(Map<Integer, Map<Integer, GoodsReceivedItem>> unitDistribution, Integer unitId) throws SumoException {
        FulfillmentType fulfillmentType = SCMUtil.isKitchen(scmCache.getUnitDetail(unitId)) ? FulfillmentType.KITCHEN : FulfillmentType.WAREHOUSE;
        List<Integer> unitIds = getAllActiveReceivingUnits(fulfillmentType, unitId);
        for (Integer id : unitDistribution.keySet()) {
            if (!unitIds.contains(id)) {
                throw new SumoException("Invalid Excell Sheet !", "Addition of Units is Not allowed In Sheet");
            }
            for (Integer skuId : unitDistribution.get(id).keySet()) {
                GoodsReceivedItem item = unitDistribution.get(id).get(skuId);
                if (item.getTransferredQuantity() < 0.0) {
                    throw new SumoException("Invalid Excell Sheet !", "Some Sku Quantity is Negative .");
                }
            }
        }
        return true;
    }

    private Boolean setExpiryForStandAlone(Map<Integer, Map<Integer, GoodsReceivedItem>> unitDistribution, Integer unitId) throws DataNotFoundException, InventoryUpdateException {
        for (Integer id : unitDistribution.keySet()) {
            for (Integer skuId : unitDistribution.get(id).keySet()) {
                GoodsReceivedItem item = unitDistribution.get(id).get(skuId);
                ProductDefinition product = scmCache.getProductDefinition(item.getProductId());
                if (product.getCategoryDefinition().getId().equals(SCMServiceConstants.CATEGORY_SEMI_FINISHED)) {
                    SkuDefinition sku = scmCache.getSkuDefinition(skuId);
                    ExpiryDataRequest expiryDataRequest = new ExpiryDataRequest();
                    expiryDataRequest.setInventoryType(PriceUpdateEntryType.SKU);
                    expiryDataRequest.setUnitId(unitId);
                    List<ItemExpiryData> inventoryItems = new ArrayList<>();
                    ItemExpiryData itemExpiryData = new ItemExpiryData();
                    itemExpiryData.setConsumptionOrder(ConsumptionOrder.LIFO);
                    itemExpiryData.setExpiryType(ItemExpiryType.FRESH);
                    itemExpiryData.setItemKeyId(skuId);
                    itemExpiryData.setKeyId(skuId);
                    itemExpiryData.setKeyName(item.getSkuName());
                    itemExpiryData.setKeyType(PriceUpdateEntryType.SKU);
                    itemExpiryData.setQuantity(BigDecimal.valueOf(item.getTransferredQuantity()));
                    itemExpiryData.setUom(sku.getUnitOfMeasure());
                    inventoryItems.add(itemExpiryData);
                    expiryDataRequest.setInventoryItems(inventoryItems);
                    ExpiryDataRequest expiryResponse = getTransferOrderItemExpiries(expiryDataRequest);
                    item.getPackagingDetails().get(0).setExpiryDrillDown(expiryResponse.getInventoryItems().get(0).getDrillDowns());

                }
            }
        }


        return true;
    }

    @Override
    public GoodsReceivedData getGrByTO(Integer toId) {
        return scmAssetManagementDao.getGrForTO(toId);
    }

    private Integer getSkuForVendor(Integer unitId, Integer vendorId, Integer productId, Integer packagingId) throws SumoException {
        Set<Integer> vendors = new HashSet<>();
        vendors.add(vendorId);
        UnitBasicDetail unitBasicDetail = masterDataCache.getUnitBasicDetail(unitId);
        Integer deliveryLocationId = masterDataCache.getAllLocations().get(unitBasicDetail.getLocationCode()).getId();
        List<SkuPriceDetail> skuPriceDetails = skuMappingService.getSkuPackagingPriceForVendorsForUnit(
                unitId, vendors, deliveryLocationId);
        if (Objects.isNull(skuPriceDetails)) {
            LOG.info("Sku Price Not Found For Packaging : {} , product  : {} , Vendor : {}", packagingId, productId, vendorId);
            String msg = "Sku Price Not Found For Packaging :" + packagingId + " Product  : " + scmCache.getProductDefinition(productId).getProductName() +
                    " Vendor : " + vendorId;
            throw new SumoException("Sku Price Not Found !! ", msg);
        }
        List<Integer> productIds = new ArrayList<>(Arrays.asList(productId));
        List<Integer> skuIds = scmProductManagementService.viewAllActiveSkuByUnitId(unitId, productIds).get(productId).stream().
                map(sku -> sku.getSkuId()).collect(Collectors.toList());

        for (SkuPriceDetail skuPriceDetail : skuPriceDetails) {
            if (skuIds.contains(skuPriceDetail.getSku().getId()) && skuPriceDetail.getPkg().getId().equals(packagingId)) {
                return skuPriceDetail.getSku().getId();
            }
        }
        LOG.info("Sku Price Not Found For Packaging : {} , product  : {} , Vendor : {}", packagingId, productId, vendorId);
        String msg = "Sku Price Not Found For Packaging :" + packagingId + " Product  : " + scmCache.getProductDefinition(productId).getProductName() +
                " Vendor : " + vendorId;
        throw new SumoException("Sku Price Not Found !! ", msg);


    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean createAutoTransferForSpecializedRO(List<RequestOrderData> requestOrderDataList, Integer userId) throws SumoException {
        try {
            LOG.info("Trying To Auto Transfer Special RO Orders ::::");
            for (RequestOrderData ro : requestOrderDataList) {
                Integer unitId = ro.getRequestUnitId();
                Map<Integer, Map<Integer, GoodsReceivedItem>> unitWiseDistribution = new HashMap<>();
                unitWiseDistribution.put(unitId, new HashMap<>());
                for (RequestOrderItemData roItem : ro.getRequestOrderItemDatas()) {
                    Integer skuId = null;
                    skuId = getSkuForVendor(unitId, roItem.getVendorId(), roItem.getProductId(), roItem.getPackagingId());
                    GoodsReceivedItem grItem = setSkuDistribution(skuId, roItem.getRequestedQuantity().floatValue(),
                            unitId, roItem.getPackagingId(), false);
                    Float conversionRatio = grItem.getPackagingDetails().get(0).getPackagingDefinitionData().getConversionRatio();
                    grItem.getPackagingDetails().get(0).setTransferredQuantity(roItem.getRequestedQuantity().floatValue());
                    grItem.getPackagingDetails().get(0).setNumberOfUnitsPacked(SCMUtil.divide(roItem.getRequestedQuantity(),
                            BigDecimal.valueOf(conversionRatio)).floatValue());
                    grItem.setRequestOrderItemId(roItem.getId());
                    unitWiseDistribution.get(unitId).put(skuId, grItem);
                }
                List<TransferOrder> transferOrders = getTransferObjects(unitWiseDistribution, unitId, userId, false);
                transferOrders.get(0).setRequestOrderId(ro.getId());
                int sourceCompanyId = masterDataCache.getUnit(unitId).getCompany()
                        .getId();
                int receivingCompanyId = masterDataCache.getUnit(unitId).getCompany()
                        .getId();
                boolean isDifferentCompany = sourceCompanyId != receivingCompanyId;

                String invoiceId = getInvoiceId(unitId,
                        unitId, isDifferentCompany);
                Integer toId = createTransferOrder(transferOrders.get(0), false, invoiceId);
                if (Objects.nonNull(ro.getSpecializedUrgentOrder()) && SCMUtil.getStatus(ro.getSpecializedUrgentOrder())) {
                    if (goodsReceiveManagementService.autoGRUrgentSpecializedOrder(toId)) {
                        LOG.info("Auto GR Created For Urgent Specialized RO : {} and TO : {}", ro.getId(), toId);
                    }
                }
            }
            return true;
        }catch (Exception e) {
                LOG.error("Error While Creating Auto Transfer For Specialized RO ::: ", e);
                throw new SumoException("Error While Creating Auto Transfer For Specialized RO");
            }
        }


    public static  List<Pair<String, String>> removeDuplicates(List<Pair<String, String>> list)
    {
        Set<Pair<String, String>> set = new LinkedHashSet<>();
        set.addAll(list);
        list.clear();
        list.addAll(set);
        return list;
    }

    @Override
    public Map<String, String> findMissingDistanceMapping(List<TransferOrder> transferOrderList, Boolean throwError) throws SumoException {
        List<Pair<String, String>> missingMapping = new ArrayList<>();
        Map<String, String> missingMappingMap = new HashMap<>();
        for (TransferOrder transferOrder : transferOrderList) {
            try{
                Unit transferUnit = masterDataCache.getUnit(transferOrder.getGenerationUnitId().getId());
                Unit receivingUnit = masterDataCache.getUnit(transferOrder.getGeneratedForUnitId().getId());

                String srcZipCode = transferUnit.getAddress().getZipCode();
                String desZipCode = receivingUnit.getAddress().getZipCode();
                BigDecimal distance = scmCache.getZipCodeDistanceMapping(srcZipCode, desZipCode);
                if (Objects.isNull(distance)) {
                    missingMapping.add(new Pair(transferUnit.getName()+" PinCode : "+srcZipCode, receivingUnit.getName()+" PinCode : "+desZipCode));
                    missingMappingMap.put(srcZipCode, desZipCode);
                }else{
                    BigDecimal dis = scmCache.getUnitDistanceMapping(transferUnit.getId(),receivingUnit.getId());
                    if(dis==null || dis.equals(BigDecimal.ZERO)){
                        skuMappingDao.updateUnitDistanceMappingData(transferUnit.getId(),null,distance,receivingUnit.getId(),null,distance,false);
                    }
                }
            } catch (Exception e){
                LOG.error("Error fetching generation or generated for unit Id for TO :::::{}", transferOrder.getId());
            }

        }
        if (!missingMapping.isEmpty() && Boolean.TRUE.equals(throwError)) {
            missingMapping = removeDuplicates(missingMapping);
            StringBuilder msg = new StringBuilder(" <B> Distance Mapping Not Found Between </B>");
            for (Pair<String, String> mapping : missingMapping) {
                msg.append("<Br>").append("[").append(mapping.getKey()).append(" and  ").append(mapping.getValue()).append("]").append("</Br>");
            }
            throw new SumoException("Distance Mapping Not Found", msg.toString());

        }
        return missingMappingMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void downloadEInvoiceJson(List<Integer> toIds, HttpServletResponse response) throws IOException, SumoException {

        List<TransferOrder> transferOrderList = getTOsByIds(toIds);
        findMissingDistanceMapping(transferOrderList, true);
        List<EPortalWrapper> data = EWayHelper.covertToEPortalData(transferOrderList, masterDataCache, scmCache);
        String filePath = props.getBasePath() + File.separator + "transfer_e_invoice";
        String fileName = "INVOICE_EPORTAL_TRANSFER_JSON_" + toIds.get(0) + "." + MimeType.JSON.extension();

        File file = new File(filePath);
        if (!file.exists()) {
            file.mkdirs();
        }

        String json = JSONSerializer.toJSON(data);
        String path = SCMUtil.write(json.getBytes(StandardCharsets.UTF_8), filePath, "json", fileName, LOG);

        File downloadFile = new File(path);
        long fileSizeInBytes = downloadFile.length();
        double fileSizeInkB = (double) fileSizeInBytes / (1024);
        if(fileSizeInkB > 1900){
            LOG.info("size:::::::::{}",fileSizeInkB);
            throw new SumoException("File size ("+ (int) fileSizeInkB +" kB) exceeding limit (1900 kB)","Please de-select some transfer orders or reduce the date range to maintain file size.");
        }
        FileInputStream inputStream = new FileInputStream(downloadFile);

        response.setContentType(MimeType.JSON.value());
        response.setContentLength((int) downloadFile.length());

        // set headers for the response
        String headerKey = "Content-Disposition";
        String headerValue = String.format("attachment; filename=\"%s\"", downloadFile.getName());
        response.setHeader(headerKey, headerValue);

        // get output stream of the response
        OutputStream outStream = response.getOutputStream();

        byte[] buffer = new byte[4096];
        int bytesRead = -1;

        // write bytes read from the input stream into the output stream
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            outStream.write(buffer, 0, bytesRead);
        }

        inputStream.close();
        outStream.close();
        FileUtils.deleteQuietly(downloadFile);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean uploadInvoiceSheet(MultipartFile file) throws IOException, SumoException {
        InputStream excelFile = file.getInputStream();
        Workbook workbook = new XSSFWorkbook(excelFile);
        Sheet dataTypeSheet = workbook.getSheetAt(0);
        Row headerRow = dataTypeSheet.getRow(0);
        List<TransferOrderEInvoice> transferOrderEInvoiceList = new ArrayList<>();

        if(Objects.nonNull(headerRow) && Objects.equals(headerRow.getCell(5).getStringCellValue(),"Error Code")){
            for (int i = 1; i <= dataTypeSheet.getLastRowNum(); i++) {
                Row row = dataTypeSheet.getRow(i);
                if(Objects.nonNull(row) && Objects.equals(row.getCell(5).getStringCellValue(),String.valueOf(2150))){
                    String docId = row.getCell(1).getStringCellValue();
                    docId = docId.substring(1, 2).equalsIgnoreCase("/") ? "0".concat(docId) : docId;
                    TransferOrderData transferOrderData = transferOrderManagementDao.findTransfersByGeneratedId(docId);
                    if(Objects.isNull(transferOrderData)) {
                        throw new SumoException("Invalid Doc Id Found!!", "No Transfer Order Found For Doc Id " + docId);
                    }
                    if(Objects.nonNull(transferOrderData.geteInvoiceGenerated()) &&
                            transferOrderData.geteInvoiceGenerated().equalsIgnoreCase(AppConstants.YES)){
                        continue;
                    }
                    TransferOrderEInvoice transferOrderEInvoice = new TransferOrderEInvoice();
                    String errorStringIRN = row.getCell(6).getStringCellValue();
                    String IRN = errorStringIRN.substring(21, errorStringIRN.length()-2);
                    transferOrderEInvoice.setIrnNo(IRN);
                    transferOrderEInvoice.setTransferOrderId(transferOrderData.getId());
                    transferOrderEInvoice.setStatus(TransferOrderEInvoiceStatus.PARTIALLY_COMPLETED.value());
                    transferOrderEInvoiceList.add(transferOrderEInvoice);
                    transferOrderData.seteInvoiceGenerated(AppConstants.YES);

                }
            }
        }
        else{
            for (int i = 1; i <= dataTypeSheet.getLastRowNum(); i++) {
                Row row = dataTypeSheet.getRow(i);
                String docId = row.getCell(4).getStringCellValue();
                docId = docId.substring(1, 2).equalsIgnoreCase("/") ? "0".concat(docId) : docId;
                TransferOrderData transferOrderData = transferOrderManagementDao.findTransfersByGeneratedId(docId);
                if(Objects.nonNull(transferOrderData.geteInvoiceGenerated()) &&
                        transferOrderData.geteInvoiceGenerated().equalsIgnoreCase(AppConstants.YES)){
                    continue;
                }if (Objects.isNull(transferOrderData)) {
                    throw new SumoException("Invalid Doc Id Found!!", "No Transfer Order Found For Doc Id " + docId);
                }
                TransferOrderEInvoice transferOrderEInvoice = new TransferOrderEInvoice();
                transferOrderEInvoice.setIrnNo(row.getCell(1).getStringCellValue());
                transferOrderEInvoice.setAckNo(row.getCell(2).getStringCellValue());
                transferOrderEInvoice.setSignedQrCode(row.getCell(10).toString());
                transferOrderEInvoice.setTransferOrderId(transferOrderData.getId());
                transferOrderData.seteInvoiceGenerated(AppConstants.YES);
                try {
                    QRGenerationServiceImpl impl = new QRGenerationServiceImpl();
                    byte[] generatedBarCode = impl.getQRCodeImage(transferOrderEInvoice.getSignedQrCode(), 100, 100);
                    InputStream inputStream = new ByteArrayInputStream(generatedBarCode);
                    MultipartFile barcodeFile = new MockMultipartFile("new file name", "Original file name", ContentType.IMAGE_PNG.toString(), inputStream);
                    String fileName = "BARCODE_" + transferOrderData.getId() + "." + MimeType.PNG.name().toLowerCase();
                    FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), "INVOICE_BARCODE/TRANSFER_INVOICE", fileName, barcodeFile);
                    DocumentDetail documentDetail = new DocumentDetail();
                    documentDetail.setMimeType(MimeType.PNG);
                    documentDetail.setFileType(FileType.SALES_INVOICE);
                    documentDetail.setDocumentLink(fileName);
                    documentDetail.setS3Key(s3File.getKey());
                    documentDetail.setFileUrl(s3File.getUrl());
                    documentDetail.setS3Bucket(s3File.getBucket());
                    documentDetail.setUploadType(DocUploadType.GSTIN);
                    documentDetail.setUpdatedBy(SCMUtil.generateIdCodeName(transferOrderData.getGeneratedBy(), "", masterDataCache.getEmployee(transferOrderData.getGeneratedBy())));
                    documentDetail.setUpdateTime(SCMUtil.getCurrentTimestamp());
                    DocumentDetailData data = transferOrderManagementDao.add(SCMDataConverter.convert(documentDetail), true);
                    transferOrderEInvoice.setBarCodeId(data.getDocumentId());
                } catch (Exception e) {
                    LOG.error("Encountered error while uploading document", e);
                }
                transferOrderEInvoiceList.add(transferOrderEInvoice);
            }
        }


        transferOrderManagementDao.addAll(transferOrderEInvoiceList);

        return true;

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public TransferOrderEInvoiceDTO findEInvoiceByTOId(Integer toId) throws MalformedURLException {
        TransferOrderEInvoice transferOrderEInvoice = transferOrderManagementDao.findEInvoiceByTOID(toId);
        if (Objects.nonNull(transferOrderEInvoice)) {
            DocumentDetailData documentDetailData = transferOrderManagementDao.find(DocumentDetailData.class, transferOrderEInvoice.getBarCodeId());
            return convertToInvoiceDomain(transferOrderEInvoice, documentDetailData);
        } else {
            return null;
        }
    }

    private TransferOrderEInvoiceDTO convertToInvoiceDomain(TransferOrderEInvoice transferOrderEInvoice, DocumentDetailData documentDetail) throws MalformedURLException {
        TransferOrderEInvoiceDTO result = new TransferOrderEInvoiceDTO();
        result.setAckNo(transferOrderEInvoice.getAckNo());
        result.setTransferOrderId(transferOrderEInvoice.getTransferOrderId());
        result.setTransferOrderEInvoiceId(transferOrderEInvoice.getTransferOrderEInvoiceId());
        result.setIrnNo(transferOrderEInvoice.getIrnNo());
        result.setSignedQrCode(transferOrderEInvoice.getSignedQrCode());
        result.setBarCodeUrl(new URL(documentDetail.getFileUrl()));
        return result;

    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public  Boolean initiateFaTransfer(Map<Integer,BigDecimal> productAndQtyMap , Integer receivingUnitId , Integer fullfillingUnitId ,
                                        String budgetType ,Integer userId , String toType ,Integer roId) throws SumoException {

        List<StockEventDefinitionData> res = scmAssetManagementDao.getInitiatedToEvent(fullfillingUnitId);
      if(!res.isEmpty()){
          String msg;
          if(Objects.equals(res.get(0).getEventType(), StockEventType.TRANSFER_OUT.value()) && res.get(0).getRoId()==null ){
              msg = "Please first finish previous standalone asset transfer in stock take app. (for receiving_unit_id = "+res.get(0).getReceivingUnitId()+")";
          }else if(Objects.equals(res.get(0).getEventType(), StockEventType.ASSET_RECEIVING.value())){
              msg = "Please first finish asset receiving, for gr_id : "+res.get(0).getGrId();
          }else{
           msg = "Please first finish previous  asset transfer in stock take app. (for receiving_unit_id = "+res.get(0).getReceivingUnitId()+", regular_order_id : "+res.get(0).getRoId()+")";
          }

          throw new SumoException(msg);
      }
        try{
            Unit unit = masterDataCache.getUnit(fullfillingUnitId);
            StockEventDefinitionData stockEventDefinition = new StockEventDefinitionData() ;
            stockEventDefinition.setBudgetType(budgetType);
            stockEventDefinition.setReceivingUnitId(receivingUnitId);
            stockEventDefinition.setUnitId(fullfillingUnitId);
            stockEventDefinition.setEventType(StockEventType.TRANSFER_OUT.value());
            stockEventDefinition.setUnitType(unit.getFamily().value());
            stockEventDefinition.setInitiatedBy(userId);
            stockEventDefinition.setSubType(toType);
          stockEventDefinition.setRoId(roId);
          stockEventDefinition.setEventStatus(SCMOrderStatus.INITIATED.value());
          stockEventDefinition.setEventCreationDate(SCMUtil.getCurrentTimestamp());
          stockEventDefinition = transferOrderManagementDao.add(stockEventDefinition,true);
          List<FaTransferData> faTransferDataList = new ArrayList<>();
          for(Integer productId : productAndQtyMap.keySet())
          {
              FaTransferData initiateFaTransfer = new FaTransferData();
              initiateFaTransfer.setEventId(stockEventDefinition.getEventId()) ;
              initiateFaTransfer.setProductId(productId) ;
              initiateFaTransfer.setTransferredQty(productAndQtyMap.get(productId)) ;
              faTransferDataList.add(initiateFaTransfer);
          }

            transferOrderManagementDao.addAll(faTransferDataList) ;
            return true ;
        }catch (Exception e)
        {
            LOG.info("Error while initiating fa transfer data:: ",e);
        }
        return false ;
    }



    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public eInvoiceResponseObject findPendingTransferOrders(Integer selectedStateCode, Integer generationUnitId, Integer generatedForUnitId, Date startDate, Date endDate){
        BigInteger totalTransferOrders = transferOrderManagementDao.getTransferOrdersCount();
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        List<PendingTransferOrder> pendingTransferOrders = transferOrderManagementDao.findPendingTransferOrders(selectedStateCode, generationUnitId,
                generatedForUnitId, startDate, endDate, props.getEnvType());
        List<PendingTransferOrder> partiallyCompleteTransferOrders = transferOrderManagementDao.findPartiallyCompleteTransferOrders(selectedStateCode, generationUnitId,
                generatedForUnitId, startDate, endDate, props.getEnvType());
        List<PendingTransferOrder> lostExcelCompleteTOs = transferOrderManagementDao.findLostExcelCompletedTO(selectedStateCode, generationUnitId,
                generatedForUnitId, startDate, endDate, props.getEnvType());

        LOG.info("Step 1 : Find : {}s",  watch.stop().elapsed(TimeUnit.SECONDS));

        for(PendingTransferOrder pendingTransferOrder : pendingTransferOrders){
            try{
                String employeeName = masterDataCache.getEmployeeBasicDetail(pendingTransferOrder.getGeneratedBy().getId()).getName();
                String  generatorUnitName = masterDataCache.getUnitBasicDetail(pendingTransferOrder.getGenerationUnitId().getId()).getName();
                String generatedForUnitName = masterDataCache.getUnitBasicDetail(pendingTransferOrder.getGeneratedForUnitId().getId()).getName();
                pendingTransferOrder.setGeneratedBy(new IdCodeName(pendingTransferOrder.getGeneratedBy().getId(),null,employeeName));
                pendingTransferOrder.setGenerationUnitId(new IdCodeName(pendingTransferOrder.getGenerationUnitId().getId(),null,generatorUnitName));
                pendingTransferOrder.setGeneratedForUnitId(new IdCodeName(pendingTransferOrder.getGeneratedForUnitId().getId(),null,generatedForUnitName));
                pendingTransferOrder.setPartialInvoiceIrn(null);

            }catch(Exception e){
                LOG.error("Names null for Pending TO :::::: {}",pendingTransferOrder.getId());
            }
        }
        for(PendingTransferOrder partiallyCompleteTransferOrder : partiallyCompleteTransferOrders){
            try{
                String employeeName = masterDataCache.getEmployeeBasicDetail(partiallyCompleteTransferOrder.getGeneratedBy().getId()).getName();
                String  generatorUnitName = masterDataCache.getUnitBasicDetail(partiallyCompleteTransferOrder.getGenerationUnitId().getId()).getName();
                String generatedForUnitName = masterDataCache.getUnitBasicDetail(partiallyCompleteTransferOrder.getGeneratedForUnitId().getId()).getName();
                partiallyCompleteTransferOrder.setGeneratedBy(new IdCodeName(partiallyCompleteTransferOrder.getGeneratedBy().getId(),null,employeeName));
                partiallyCompleteTransferOrder.setGenerationUnitId(new IdCodeName(partiallyCompleteTransferOrder.getGenerationUnitId().getId(),null,generatorUnitName));
                partiallyCompleteTransferOrder.setGeneratedForUnitId(new IdCodeName(partiallyCompleteTransferOrder.getGeneratedForUnitId().getId(),null,generatedForUnitName));
                partiallyCompleteTransferOrder.setPartialInvoiceIrn(null);

            }catch(Exception e){
                LOG.error("Names null for Partially Complete TO :::::: {}",partiallyCompleteTransferOrder.getId());
            }
        }
        for(PendingTransferOrder lostExcelCompleteTransferOrder : lostExcelCompleteTOs){
            try{
                String employeeName = masterDataCache.getEmployeeBasicDetail(lostExcelCompleteTransferOrder.getGeneratedBy().getId()).getName();
                String  generatorUnitName = masterDataCache.getUnitBasicDetail(lostExcelCompleteTransferOrder.getGenerationUnitId().getId()).getName();
                String generatedForUnitName = masterDataCache.getUnitBasicDetail(lostExcelCompleteTransferOrder.getGeneratedForUnitId().getId()).getName();
                lostExcelCompleteTransferOrder.setGeneratedBy(new IdCodeName(lostExcelCompleteTransferOrder.getGeneratedBy().getId(),null,employeeName));
                lostExcelCompleteTransferOrder.setGenerationUnitId(new IdCodeName(lostExcelCompleteTransferOrder.getGenerationUnitId().getId(),null,generatorUnitName));
                lostExcelCompleteTransferOrder.setGeneratedForUnitId(new IdCodeName(lostExcelCompleteTransferOrder.getGeneratedForUnitId().getId(),null,generatedForUnitName));
                lostExcelCompleteTransferOrder.setPartialInvoiceIrn(lostExcelCompleteTransferOrder.getPartialInvoiceIrn());
            }catch(Exception e){
                LOG.error("Names null for lost Excel Complete TO :::::: {}",lostExcelCompleteTransferOrder.getId());
            }
        }
        eInvoiceResponseObject result = new eInvoiceResponseObject(totalTransferOrders, pendingTransferOrders, partiallyCompleteTransferOrders,lostExcelCompleteTOs);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
     public Boolean intiateChangeOnFa(Integer productId, Integer eventId, Boolean toAdd , BigDecimal transferQty) throws SumoException {
        if (Boolean.TRUE.equals(toAdd)) {
            FaTransferData initiateFaTransfer = new FaTransferData();
            initiateFaTransfer.setEventId(eventId) ;
            initiateFaTransfer.setProductId(productId);
            initiateFaTransfer.setTransferredQty(transferQty);
            try {
                transferOrderManagementDao.add(initiateFaTransfer,true) ;
            } catch (Exception e) {
                LOG.error("ERROR while saving  product : {}",e);
                throw new SumoException("error While Saving New Product");
            }
        } else {
           try{
            List<FaTransferData> initiateFaTransfer =  scmAssetManagementDao.fetchFaTransferData(eventId) ;
            List<StockEventAssetMappingDefinitionData> stockEventAssetMappingDefinitionData= scmAssetManagementDao.findAssetsByEventIdAndProductId(eventId , productId) ;
            initiateFaTransfer = initiateFaTransfer.stream().filter(Item ->
                    (Item.getProductId().equals(productId))).collect(Collectors.toList());
            if(!stockEventAssetMappingDefinitionData.isEmpty()){
                scmAssetManagementDao.delete(stockEventAssetMappingDefinitionData.get(0));
            }
            if(!initiateFaTransfer.isEmpty()){
                transferOrderManagementDao.delete(initiateFaTransfer.get(0));
           }
           }
           catch (Exception e){
               LOG.error("ERROR while removing product : {}",e);
               throw  new SumoException("Error while removing product");
           }
        }
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void autoGrSpecializedOrder(Integer toId,TransferOrder transferOrder) throws InventoryUpdateException, ParseException, SumoException {
      goodsReceiveManagementService.autoGRSpecializedOrder(toId,transferOrder);
    }

}

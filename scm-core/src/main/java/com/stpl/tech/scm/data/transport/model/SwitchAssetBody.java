package com.stpl.tech.scm.data.transport.model;

import com.stpl.tech.scm.data.model.AssetDefinitionData;
import com.stpl.tech.scm.domain.model.AssetDefinition;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SwitchAssetBody {

    Integer newAssetId;
    List<Integer> newAssetDocIds;
    String newAssetComments;
    Integer oldAssetId;
    List<Integer> oldAssetDocIds;
    String oldAssetComments;
    String switchReason;
    Integer createdBy;
    Integer requestedBy;
    Integer createdByUnit;
    Integer requestingUnit;
    String ticketId;
    String otpValue;

}

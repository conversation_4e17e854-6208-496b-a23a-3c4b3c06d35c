package com.stpl.tech.scm.core.service;

import java.util.List;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.domain.model.Vehicle;

public interface VehicleMasterService {

	List<Vehicle> getVehicleList();

	boolean saveVehicleDetails(Vehicle vehicle) throws SumoException;

	Vehicle getSingleVehicleData(Integer vehicleId);

	boolean saveUpdatedVehicleDetails(Vehicle vehicle) throws SumoException;

}

/**
 * 
 */
package com.stpl.tech.scm.reports;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.subtlelib.poi.api.sheet.SheetContext;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.reports.modal.VarianceSummaryModal;

/**
 * <AUTHOR>
 *
 */
public class VarianceSummaryUtil {

	public void writeSummaryView(String unitName, SheetContext sheetCtx, List<VarianceSummaryModal> list) {
		sheetCtx.nextRow().mergeCells(10).text(unitName); // heading
		createSummaryEntries(sheetCtx, list, "VARIANCE", "VARIANCE_TYPE");
		createSummaryEntries(sheetCtx, list, "WASTAGE", "REASON");
		createCategorySummaryData(sheetCtx, list, "VARIANCE", true);
		createCategorySummaryData(sheetCtx, list, "WASTAGE", true);
		createCategorySummaryData(sheetCtx, list, "VARIANCE", false);
		createCategorySummaryData(sheetCtx, list, "WASTAGE", false);
	}

	private void createCategorySummaryData(SheetContext sheetCtx, List<VarianceSummaryModal> list, String aggType,
			boolean isCogs) {
		Set<String> desc = new HashSet<>(Arrays.asList("COGS", "Semi Finished"));
		List<VarianceSummaryModal> filtered = list.stream()
				.filter((
						data) -> data.getAggType().equals(aggType) && data.getAggCode().equals("PRODUCT_CATEGORY")
								&& (isCogs && desc.contains(data.getAggDesc())
										|| (!isCogs && !desc.contains(data.getAggDesc()))))
				.collect(Collectors.toList());
		Map<String, Map<String, BigDecimal>> data = getMapData(filtered);
		createEntries(sheetCtx, StringUtils.capitalize(aggType.toLowerCase()), isCogs ? "COGS" : "Consumables", data);
	}

	private void createSummaryEntries(SheetContext sheetCtx, List<VarianceSummaryModal> list, String aggType,
			String aggCode) {
		List<VarianceSummaryModal> filtered = list.stream()
				.filter((data) -> data.getAggType().equals(aggType) && data.getAggCode().equals(aggCode))
				.collect(Collectors.toList());
		Map<String, Map<String, BigDecimal>> data = getMapData(filtered);
		createEntries(sheetCtx, StringUtils.capitalize(aggType.toLowerCase()),
				StringUtils.capitaliseAllWords(aggCode.toLowerCase().replace("_", " ")), data);
	}

	/**
	 * @param filtered
	 * @return
	 */
	private Map<String, Map<String, BigDecimal>> getMapData(List<VarianceSummaryModal> filtered) {
		Map<String, Map<String, BigDecimal>> map = new TreeMap<>();
		filtered.stream().forEach((data) -> {
			if (!map.containsKey(data.getAggDesc())) {
				map.put(data.getAggDesc(), new HashMap<>());
			}
			map.get(data.getAggDesc()).put(data.getType(), data.getValue());
		});
		return map;
	}

	private void createEntries(SheetContext sheetCtx, String type, String code,
			Map<String, Map<String, BigDecimal>> data) {
		sheetCtx.nextRow();
		sheetCtx.nextRow().mergeCells(10).header(type + " by " + code);
		sheetCtx.nextRow().header(code).setColumnWidth(SCMUtil.NAME_COLUMN_WIDTH).header("Today")
				.setColumnWidth(SCMUtil.COLUMN_WIDTH).header("MTD").setColumnWidth(SCMUtil.COLUMN_WIDTH).header("LMTD")
				.setColumnWidth(SCMUtil.COLUMN_WIDTH).header("YTD").setColumnWidth(SCMUtil.COLUMN_WIDTH);

		for (String desc : data.keySet()) {
			Map<String, BigDecimal> value = data.get(desc);
			sheetCtx.nextRow().text(desc)
					.number(value.get("VALUE_TODAY") != null ? value.get("VALUE_TODAY") : BigDecimal.ZERO)
					.number(value.get("VALUE_MTD") != null ? value.get("VALUE_MTD") : BigDecimal.ZERO)
					.number(value.get("VALUE_LMTD") != null ? value.get("VALUE_LMTD") : BigDecimal.ZERO)
					.number(value.get("VALUE_YTD") != null ? value.get("VALUE_YTD") : BigDecimal.ZERO);
		}
	}
}

package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "USER_PRODUCT_CREATION_REQUEST_LOGS")
public class UserProductCreationLogData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PRODUCT_CREATION_LOG_ID")
    private Integer productCreationLogId;

    @Column(name = "FROM_STATUS")
    private String fromStatus;

    @Column(name = "TO_STATUS", nullable = false)
    private String toStatus;

    @Column(name = "UPDATED_BY", nullable = false)
    private Integer updateBy;

    @Column(name = "UPDATED_AT", nullable = false)
    private Date updateAt;

    @Column(name = "PRODUCT_CREATION_ID", nullable = false)
    private Integer productId;

}

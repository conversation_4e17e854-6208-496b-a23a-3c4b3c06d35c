package com.stpl.tech.scm.core.service;

import com.stpl.tech.scm.domain.model.MonkDayCloseEvent;
import com.stpl.tech.scm.domain.model.MonkStatusDayClose;
import com.stpl.tech.scm.domain.model.MonkStatusDayCloseHistory;
import com.stpl.tech.scm.domain.model.MonkStatusDTO;

import java.util.Date;
import java.util.List;

public interface MonkDayCloseService {
    
    MonkDayCloseEvent initializeMonkDayCloseEvent(Integer unitId, Date businessDate, Integer updatedBy);
    
    MonkDayCloseEvent getMonkDayCloseEvent(Integer unitId, Date businessDate);
    
    MonkStatusDayClose updateMonkStatus(Long monkStatusDayCloseId, String monkStatus, Integer updatedBy);
    
    MonkStatusDayClose createMonkStatus(Long eventStatusId, String monkName, String monkStatus, Integer updatedBy);
    
    List<MonkStatusDTO> getAvailableMonkStatuses();
    
    boolean checkAndUpdateEventStatus(Long eventId);
    
    List<MonkDayCloseEvent> getPendingMonkDayCloseEvents();
    
    MonkDayCloseEvent getLatestMonkDayCloseEventByUnit(Integer unitId);
    
    List<MonkStatusDayClose> getLatestMonkStatusesByUnit(Integer unitId);
    
    void linkKettleDayClose(Long eventId, Integer kettleDayCloseId);
    
    void linkSumoDayClose(Long eventId, Integer sumoDayCloseId);
    
    // History operations
    MonkStatusDayClose updateMonkStatusWithHistory(Long monkStatusDayCloseId, String monkStatus, String comment, Integer updatedBy);
    
    List<MonkStatusDayCloseHistory> getMonkStatusHistory(Long monkStatusDayCloseId);
    
    // Auto-completion
    void autoCompleteMonkStatuses();
}

/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "EMPLOYEE_PAYMENT_CARD_MAPPING")
public class EmployeePaymentCardMapping {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "EMPLOYEE_PAYMENT_CARD_MAPPING_ID", nullable = false, unique = true)
    private Integer employeePaymentCardMappingId;

    @Column(name = "EMPLOYEE_ID")
    private Integer employeeId;

    @Column(name = "PAYMENT_CARD")
    private String paymentCard;

    @Column(name = "MAPPING_STATUS")
    private String mappingStatus;

    public Integer getEmployeePaymentCardMappingId() {
        return this.employeePaymentCardMappingId;
    }

    public void setEmployeePaymentCardMappingId(Integer employeePaymentCardMappingId) {
        this.employeePaymentCardMappingId = employeePaymentCardMappingId;
    }

    public Integer getEmployeeId() {
        return this.employeeId;
    }

    public void setEmployeeId(Integer employeeId) {
        this.employeeId = employeeId;
    }

    public String getPaymentCard() {
        return this.paymentCard;
    }

    public void setPaymentCard(String paymentCard) {
        this.paymentCard = paymentCard;
    }

    public String getMappingStatus() {
        return this.mappingStatus;
    }

    public void setMappingStatus(String mappingStatus) {
        this.mappingStatus = mappingStatus;
    }
}

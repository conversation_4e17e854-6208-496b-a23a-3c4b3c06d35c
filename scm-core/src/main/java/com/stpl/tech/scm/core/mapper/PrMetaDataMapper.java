package com.stpl.tech.scm.core.mapper;

import com.stpl.tech.scm.data.model.GstOfStpl;
import com.stpl.tech.scm.data.model.GstStateMetaData;
import com.stpl.tech.scm.data.model.PaymentRequestMetaData;
import com.stpl.tech.scm.data.model.TdsLedgerRate;
import com.stpl.tech.scm.domain.model.GstOfStplDomain;
import com.stpl.tech.scm.domain.model.GstStateMetaDataDomain;
import com.stpl.tech.scm.domain.model.PaymentRequestMetaDataDomain;
import com.stpl.tech.scm.domain.model.TdsLegerRateDomain;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PrMetaDataMapper {

    PrMetaDataMapper INSTANCE = Mappers.getMapper(PrMetaDataMapper.class);
    List<TdsLegerRateDomain> toTdsLegerRateDomain(List<TdsLedgerRate> tdsLedgerRate);

    TdsLedgerRate toTdsLegerRateData(TdsLegerRateDomain tdsLedgerRate);
    TdsLegerRateDomain toTdsLegerRateDomain(TdsLedgerRate tdsLedgerRate);

    List<GstStateMetaDataDomain> toGstStateMetaDataDomain(List<GstStateMetaData> gstStateMetaData);

     GstStateMetaData toGstStateMetaDataData(GstStateMetaDataDomain gstStateMetaData);

    GstStateMetaDataDomain toGstStateMetaDataDomain(GstStateMetaData gstStateMetaData);


    List<GstOfStplDomain> toGstOfStplDomain(List<GstOfStpl> gstOfStpl);

     GstOfStpl toGstOfStplData(GstOfStplDomain gstOfStpl);

    GstOfStplDomain toGstOfStplDomain(GstOfStpl gstOfStpl);



     PaymentRequestMetaDataDomain  toPaymentRequestMetaDataDomain(PaymentRequestMetaData paymentRequestMetaData);

}

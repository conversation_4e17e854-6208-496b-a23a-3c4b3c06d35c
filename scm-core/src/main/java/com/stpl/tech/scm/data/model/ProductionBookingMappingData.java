package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;


@Entity
@Table(name = "PRODUCTION_BOOKING_MAPPING")
public class ProductionBookingMappingData {

    private Integer productionBookingId;
    private Integer productId;
    private String productName;
    private Integer skuId;
    private String unitOfMeasure;
    private BigDecimal quantity;
    private Integer unitId;
    private Date generationTime;
    private Date cancellationTime;
    private Integer generatedBy;
    private Integer cancelledBy;
    private String mappingStatus;
    private String profile;

    private Integer linkedSkuId;
    private String linkedSkuName;
    private String linkedUnitOfMeasure;
    private BigDecimal calculatedQuantity;
    private String autoProduction;

    public ProductionBookingMappingData() {
    }

    public ProductionBookingMappingData(Integer productId, String productName, Integer skuId, String unitOfMeasure, BigDecimal quantity, Integer unitId, Integer generatedBy,  String profile) {
        this.productId = productId;
        this.productName = productName;
        this.skuId = skuId;
        this.unitOfMeasure = unitOfMeasure;
        this.quantity = quantity;
        this.unitId = unitId;
        this.generatedBy = generatedBy;
        this.profile = profile;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PRODUCTION_BOOKING_ID", unique = true,nullable = false)
    public Integer getProductionBookingId() {
        return productionBookingId;
    }

    public void setProductionBookingId(Integer productionBookingId) {
        this.productionBookingId = productionBookingId;
    }

    @Column(name = "PRODUCT_ID", nullable = false)
    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    @Column(name = "PRODUCT_NAME")
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @Column(name = "SKU_ID")
    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    @Column(name = "UNIT_OF_MEASURE")
    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }

    @Column(name = "QUANTITY")
    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    @Column(name = "UNIT_ID",nullable = false)
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }


    @Column(name = "GENERATION_TIME")
    public Date getGenerationTime() {
        return generationTime;
    }

    public void setGenerationTime(Date generationTime) {
        this.generationTime = generationTime;
    }

    @Column(name = "CANCELLATION_TIME")
    public Date getCancellationTime() {
        return cancellationTime;
    }

    public void setCancellationTime(Date cancellationTime) {
        this.cancellationTime = cancellationTime;
    }

    @Column(name = "GENERATED_BY")
    public Integer getGeneratedBy() {
        return generatedBy;
    }

    public void setGeneratedBy(Integer generatedBy) {
        this.generatedBy = generatedBy;
    }

    @Column(name = "CANCELLED_BY")
    public Integer getCancelledBy() {
        return cancelledBy;
    }

    public void setCancelledBy(Integer cancelledBy) {
        this.cancelledBy = cancelledBy;
    }

    @Column(name = "MAPPING_STATUS")
    public String getMappingStatus() {
        return mappingStatus;
    }

    public void setMappingStatus(String mappingStatus) {
        this.mappingStatus = mappingStatus;
    }

    @Column(name = "PROFILE")
    public String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        this.profile = profile;
    }

    @Column(name = "LINKED_SKU_ID", nullable = false)
    public Integer getLinkedSkuId() {
        return linkedSkuId;
    }

    public void setLinkedSkuId(Integer linkedSkuId) {
        this.linkedSkuId = linkedSkuId;
    }

    @Column(name = "LINKED_SKU_NAME")
    public String getLinkedSkuName() {
        return linkedSkuName;
    }

    public void setLinkedSkuName(String linkedSkuName) {
        this.linkedSkuName = linkedSkuName;
    }

    @Column(name = "LINKED_UNIT_OF_MEASURE")
    public String getLinkedUnitOfMeasure() {
        return linkedUnitOfMeasure;
    }

    public void setLinkedUnitOfMeasure(String linkedUnitOfMeasure) {
        this.linkedUnitOfMeasure = linkedUnitOfMeasure;
    }

    @Column(name = "LINKED_QUANTITY")
    public BigDecimal getCalculatedQuantity() {
        return calculatedQuantity;
    }

    public void setCalculatedQuantity(BigDecimal calculatedQuantity) {
        this.calculatedQuantity = calculatedQuantity;
    }

    @Column(name="AUTO_PRODUCTION")
    public String getAutoProduction() {
        return autoProduction;
    }

    public void setAutoProduction(String autoProduction) {
        this.autoProduction = autoProduction;
    }
}

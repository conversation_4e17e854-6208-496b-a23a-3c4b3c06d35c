package com.stpl.tech.scm.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "PRODUCT_STOCK_DRILL_DOWN_UPDATE_EVENT")
public class ProductStockDrillDownUpdateEvent {

	private Integer drillDownUpdateEventId;
	private Integer eventId;
	private Integer inventoryId;
	private BigDecimal price;
	private BigDecimal cost;

	/**
	 * @return the productRecipeCostId
	 */
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "DRILL_DOWN_UPDATE_EVENT_ID", unique = true, nullable = false)
	public Integer getDrillDownUpdateEventId() {
		return drillDownUpdateEventId;
	}

	/**
	 * @param drillDownUpdateEventId the drillDownUpdateEventId to set
	 */
	public void setDrillDownUpdateEventId(Integer drillDownUpdateEventId) {
		this.drillDownUpdateEventId = drillDownUpdateEventId;
	}

	/**
	 * @return the eventId
	 */
	@Column(name = "EVENT_ID", nullable = true)
	public Integer getEventId() {
		return eventId;
	}

	/**
	 * @param eventId the eventId to set
	 */
	public void setEventId(Integer eventId) {
		this.eventId = eventId;
	}

	/**
	 * @return the inventoryId
	 */
	@Column(name = "INVENTORY_ID", nullable = true)
	public Integer getInventoryId() {
		return inventoryId;
	}

	/**
	 * @param inventoryId the inventoryId to set
	 */
	public void setInventoryId(Integer inventoryId) {
		this.inventoryId = inventoryId;
	}

	/**
	 * @return the price
	 */
	@Column(name = "PRICE", nullable = true)
	public BigDecimal getPrice() {
		return price;
	}

	/**
	 * @param price the price to set
	 */
	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	/**
	 * @return the cost
	 */
	@Column(name = "COST", nullable = true)
	public BigDecimal getCost() {
		return cost;
	}

	/**
	 * @param cost the cost to set
	 */
	public void setCost(BigDecimal cost) {
		this.cost = cost;
	}

}

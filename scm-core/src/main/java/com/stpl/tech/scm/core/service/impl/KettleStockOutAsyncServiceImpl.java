/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitHours;
import com.stpl.tech.master.readonly.domain.model.ProductPriceVO;
import com.stpl.tech.master.readonly.domain.model.ProductVO;
import com.stpl.tech.scm.core.service.KettleStockOutAsyncService;
import com.stpl.tech.scm.core.util.SCMConstant;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.ValidationUtil;
import com.stpl.tech.scm.data.dao.KettleStockOutDao;
import com.stpl.tech.scm.data.model.KettleProductDataClone;
import com.stpl.tech.scm.data.model.KettleProductDimensionCloneData;
import com.stpl.tech.scm.data.model.KettleStockOutDateWiseData;
import com.stpl.tech.scm.data.model.KettleStockOutPercentageData;
import com.stpl.tech.scm.data.model.KettleStockOutProductWiseData;
import com.stpl.tech.scm.data.model.KettleStockOutProductWiseTimingsData;
import com.stpl.tech.scm.data.model.KettleStockOutTimingsData;
import com.stpl.tech.scm.data.model.KettleUnitDetailDataClone;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Service
public class KettleStockOutAsyncServiceImpl implements KettleStockOutAsyncService {

    @Autowired
    private KettleStockOutDao kettleStockOutDao;

    @Autowired
    @Qualifier("taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRES_NEW)
    public CompletableFuture<Void> createKettleUnitDetailDataCloneAsync(Unit unit, Brand brand,
                                                                        Map<Integer, Product> productDetails,
                                                                        Collection<ProductVO> productVOS) {
//        try {
//            log.info("Starting async processing for unit {} and brand {}", unit.getId(), brand.getBrandId());
//
//            UnitHours unitHours = getCurrentDayBusinessHours(unit, brand.getBrandId(), SCMUtil.getCurrentBusinessDate());
//            if (Objects.isNull(unitHours) || (brand.getBrandId() == AppConstants.GNT_BRAND_ID ?
//                    (Objects.isNull(unitHours.getDeliveryOpeningTime()) || Objects.isNull(unitHours.getDeliveryClosingTime())) :
//                    (Objects.isNull(unitHours.getDineInOpeningTime()) || Objects.isNull(unitHours.getDineInClosingTime())))) {
//                log.warn("No business hours found for unit {} and brand {}", unit.getId(), brand.getBrandId());
//                return CompletableFuture.completedFuture(null);
//            }
//
//            Map<String, Integer> productCount = new HashMap<>();
//            Map<String, Set<KettleProductDimensionCloneData>> productDimensionMap = new HashMap<>();
//
//            // Process products and build dimension map
//            productVOS.stream()
//                    .filter(e -> Objects.equals(e.getBrandId(), brand.getBrandId()) &&
//                               e.isInventoryTracked() &&
//                               !CollectionUtils.isEmpty(e.getPrices()))
//                    .forEach(productDetail -> processProductDetail(productDetail, productDetails, productCount, productDimensionMap));
//
//            if (ValidationUtil.checkIsEmptyCollection(productCount)) {
//                log.info("No products found for unit {} and brand {}", unit.getId(), brand.getBrandId());
//                return CompletableFuture.completedFuture(null);
//            }
//
//            // Create and persist data in batches for better performance
//            List<KettleUnitDetailDataClone> batchList = new ArrayList<>();
//            productCount.forEach((key, value) -> {
//                KettleUnitDetailDataClone clone = createKettleUnitDetailDataClone(unit, brand, unitHours, key, value, productDimensionMap);
//                batchList.add(clone);
//            });
//
//            // Insert remaining items
//            if (!batchList.isEmpty()) {
//                kettleStockOutDao.insertList(batchList);
//            }
//
//            log.info("Completed async processing for unit {} and brand {} with {} records",
//                     unit.getId(), brand.getBrandId(), productCount.size());
//
//        } catch (Exception e) {
//            log.error("createKettleUnitDetailDataCloneAsync : Error in async processing for unit {} and brand {}", unit.getId(), brand.getBrandId(), e);
//            throw e;
//        }
        
        return CompletableFuture.completedFuture(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRES_NEW)
    public CompletableFuture<Void> processUnitStockOutPercentageAsync(Integer unitId,
                                                                      List<KettleUnitDetailDataClone> kettleUnitDetailDataClones,
                                                                      Map<Integer, KettleProductDataClone> productDataCloneMap) {
//        try {
//            log.info("Starting async processing of stock out percentage for unitId: {}", unitId);
//
//            Date openingMinDate = kettleUnitDetailDataClones.stream()
//                    .map(KettleUnitDetailDataClone::getCafeOpening)
//                    .min(Date::compareTo)
//                    .orElseThrow(() -> new IllegalStateException("No Opening date found for unitId: " + unitId));
//
//            Date closingMaxDate = kettleUnitDetailDataClones.stream()
//                    .map(KettleUnitDetailDataClone::getCafeClosing)
//                    .max(Date::compareTo)
//                    .orElseThrow(() -> new IllegalStateException("No Closing date found for unitId: " + unitId));
//
//            // get the business date of these two
//            Date businessOpeningMinDate = SCMUtil.getBusinessDateScm(openingMinDate);
//            Date businessClosingMaxDate = SCMUtil.getBusinessDateScm(closingMaxDate);
//
//            // get all business dates between opening and closing dates (inclusive)
//            List<Date> businessDatesList = SCMUtil.getBusinessDatesBetween(businessOpeningMinDate, businessClosingMaxDate);
//
//            // get the Kettle Stock Out Date wise data for all these dates and left join fetch the timings also
//            List<KettleStockOutDateWiseData> byUnitIdAndBusinessDates = kettleStockOutDao.findByUnitIdAndBusinessDates(unitId, businessDatesList);
//            for (KettleStockOutDateWiseData data : byUnitIdAndBusinessDates) {
//                KettleProductDataClone productDataClone = productDataCloneMap.get(data.getKettleProductId());
//                data.setInventoryTrackLevel(productDataClone.getInventoryTrackLevel());
//                data.setBrandId(productDataClone.getBrandId());
//            }
//
//            // make map like TreeMap<Date, Map<String , List<KettleStockOutDateWiseData>> group by date , and map it by generate key on the KettleStockOutDateWiseData
//            TreeMap<Date, Map<String, List<KettleStockOutDateWiseData>>> stockOutDateWiseDataMap = byUnitIdAndBusinessDates.stream()
//                    .collect(Collectors.groupingBy(KettleStockOutDateWiseData::getBusinessDate,
//                            TreeMap::new,
//                            Collectors.groupingBy(this::generateKey)));
//
//            List<KettleStockOutPercentageData> kettleStockOutPercentageDataList = new ArrayList<>();
//            kettleUnitDetailDataClones.forEach(kettleUnitDetailDataClone ->
//                calculateAndStoreStockOut(kettleUnitDetailDataClone, stockOutDateWiseDataMap, kettleStockOutPercentageDataList));
//
//            if (!CollectionUtils.isEmpty(kettleStockOutPercentageDataList)) {
//                kettleStockOutDao.addAll(kettleStockOutPercentageDataList);
//            }
//
//            log.info("Completed async processing of stock out percentage for unitId: {} with {} records",
//                     unitId, kettleStockOutPercentageDataList.size());
//
//        } catch (Exception e) {
//            log.error("processUnitStockOutPercentageAsync : Error processing stock out percentage for unitId: {}", unitId, e);
//            throw e;
//        }
        
        return CompletableFuture.completedFuture(null);
    }

//    private void calculateAndStoreStockOut(KettleUnitDetailDataClone kettleUnitDetailDataClone,
//                                           TreeMap<Date, Map<String, List<KettleStockOutDateWiseData>>> stockOutDateWiseDataMap,
//                                           List<KettleStockOutPercentageData> kettleStockOutPercentageDataList) {
//        // now what we want is checking the opening time is in same business date .?
//
//        long stockOutInMinutes = 0;
//
//        Date yesterday = AppUtils.getDayBeforeOrAfterDay(AppUtils.getBusinessDate(), -1);
//
//        Set<String> uniqueCombinations = new HashSet<>();
//        Map<String, Long> productWiseDownTime = new HashMap<>();
//
//        if (!CollectionUtils.isEmpty(kettleUnitDetailDataClone.getKettleProductDimensionCloneDataSet())) {
//            uniqueCombinations = kettleUnitDetailDataClone.getKettleProductDimensionCloneDataSet().stream().map(e -> SCMUtil.generateUniqueKey(e.getProductId().toString(), e.getDimension())).collect(Collectors.toSet());
//        }
//
//        Date openingDate = kettleUnitDetailDataClone.getCafeOpening();
//        Date closingDate = kettleUnitDetailDataClone.getCafeClosing();
//        Date businessOpeningDate = SCMUtil.getBusinessDateScm(openingDate);
//        Date businessClosingDate = SCMUtil.getBusinessDateScm(closingDate);
//
//        List<Date> businessDatesBetween = SCMUtil.getBusinessDatesBetween(businessOpeningDate, businessClosingDate);
//
//        for (Date businessDate : businessDatesBetween) {
//            // for the business date create start of business date and set the time to 5:00 AM and endOfBusinessDate and set the time to 05:00 AM of next day
//            Date startOfBusinessDate = SCMUtil.getStartOfBusinessDate(businessDate);
//            Date endOfBusinessDate = SCMUtil.getEndOfBusinessDate(businessDate);
//
//            Map<String, List<KettleStockOutDateWiseData>> stockOutDataMap = stockOutDateWiseDataMap.getOrDefault(businessDate, new HashMap<>());
//            if (stockOutDataMap.containsKey(generateKey(kettleUnitDetailDataClone))) {
//                List<KettleStockOutDateWiseData> stockOutDataList = stockOutDataMap.get(generateKey(kettleUnitDetailDataClone));
//                if (!CollectionUtils.isEmpty(stockOutDataList)) {
//                    for (KettleStockOutDateWiseData stockOutData : stockOutDataList) {
//                        long stockOutForThatBusinessDay = 0;
//                        for (KettleStockOutTimingsData kettleStockOutTimingsData : stockOutData.getKettleStockOutTimingsDataSet()) {
//                            Date stockOutTime = kettleStockOutTimingsData.getStockOutTime();
//                            Date stockInTime = kettleStockOutTimingsData.getStockInTime();
//
//                            if (Objects.isNull(stockInTime)) {
//                                stockOutForThatBusinessDay += SCMUtil.getMinutesDifference(stockOutTime, endOfBusinessDate);
//                            }
//
//                            if (Objects.nonNull(stockOutTime) && Objects.nonNull(stockInTime)) {
//                                if (stockOutTime.compareTo(openingDate) < 0 && stockInTime.compareTo(openingDate) < 0) {
//                                    log.info("Not Considering the entry as both stock out and stock in time are before opening date for unitId: {}, stockOutTime: {}, stockInTime: {}",
//                                            kettleUnitDetailDataClone.getUnitId(), stockOutTime, stockInTime);
//                                    continue;
//                                }
//
//                                if (stockOutTime.compareTo(closingDate) > 0) {
//                                    log.info("Not Considering the entry as Stock Out time is after closing date for unitId: {}, stockOutTime: {}, stockInTime: {}",
//                                            kettleUnitDetailDataClone.getUnitId(), stockOutTime, stockInTime);
//                                    continue;
//                                }
//
//
//                                if (stockOutTime.compareTo(openingDate) < 0) {
//                                    stockOutTime = openingDate;
//                                }
//
//                                if (stockInTime.after(closingDate)) {
//                                    stockInTime = closingDate;
//                                }
//                            }
//
//
//                            if (Objects.isNull(kettleStockOutTimingsData.getStockInTime())) {
//                                if (stockOutTime.compareTo(closingDate) > 0) {
//                                    log.info("Not Considering the entry as Stock Out time is after closing date for unitId: {}, stockOutTime: {}, stockInTime: {}",
//                                            kettleUnitDetailDataClone.getUnitId(), stockOutTime, stockInTime);
//                                    continue;
//                                }
//
//                                if (closingDate.after(endOfBusinessDate)) {
//                                    stockInTime = endOfBusinessDate;
//                                } else {
//                                    stockInTime = closingDate;
//                                }
//
//                                if (stockOutTime.compareTo(openingDate) < 0) {
//                                    stockOutTime = openingDate;
//                                }
//
//                            }
//
//                            // now we have in time and out time
//                            String key = SCMUtil.generateUniqueKey(stockOutData.getKettleProductId().toString(), stockOutData.getDimension());
//                            if (uniqueCombinations.contains(key)) {
//                                long productDownTime = SCMUtil.getMinutesDifference(stockOutTime, stockInTime);
//                                stockOutInMinutes += productDownTime;
//                                if (!productWiseDownTime.containsKey(key)) {
//                                    productWiseDownTime.put(key, productDownTime);
//                                } else {
//                                    productWiseDownTime.put(key, productWiseDownTime.get(key) + productDownTime);
//                                }
//                            }
//                        }
//                        stockOutData.setBusinessDateWiseStockOutTimeInMin(stockOutForThatBusinessDay);
//                    }
//                }
//            }
//        }
//
//        KettleStockOutPercentageData kettleStockOutPercentageData = new KettleStockOutPercentageData();
//        kettleStockOutPercentageData.setUnitId(kettleUnitDetailDataClone.getUnitId());
//        kettleStockOutPercentageData.setBusinessDate(yesterday);
//        kettleStockOutPercentageData.setBrandId(kettleUnitDetailDataClone.getBrandId());
//        kettleStockOutPercentageData.setInventoryTrackLevel(kettleUnitDetailDataClone.getInventoryLevel());
//        kettleStockOutPercentageData.setProductCount(kettleUnitDetailDataClone.getTotalProducts());
//        kettleStockOutPercentageData.setCafeOpening(kettleUnitDetailDataClone.getCafeOpening());
//        kettleStockOutPercentageData.setCafeClosing(kettleUnitDetailDataClone.getCafeClosing());
//        kettleStockOutPercentageData.setCafeOperational(kettleUnitDetailDataClone.getCafeOperational());
//        kettleStockOutPercentageData.setUnitStatus(kettleUnitDetailDataClone.getUnitStatus());
//        kettleStockOutPercentageData.setIsLive(kettleUnitDetailDataClone.getIsLive());
//        kettleStockOutPercentageData.setIsLiveInventoryEnabled(kettleUnitDetailDataClone.getIsLiveInventoryEnabled());
//        kettleStockOutPercentageData.setProductOperationalTimeInMin((int) SCMUtil.getMinutesDifference(
//                kettleUnitDetailDataClone.getCafeOpening(), kettleUnitDetailDataClone.getCafeClosing()) * kettleUnitDetailDataClone.getTotalProducts());
//        kettleStockOutPercentageData.setTotalOperationTimeInMin(kettleStockOutPercentageData.getProductOperationalTimeInMin() - (int) stockOutInMinutes);
//        kettleStockOutPercentageData.setTotalDownTimeInMin((int) stockOutInMinutes);
//        kettleStockOutPercentageData.setStockOutPercentage(getStockOutPercentage(kettleStockOutPercentageData.getTotalDownTimeInMin(),
//                kettleStockOutPercentageData.getProductOperationalTimeInMin()));
//
//        kettleUnitDetailDataClone.getKettleProductDimensionCloneDataSet().forEach(kettleProductDimensionCloneData -> {
//            String key = SCMUtil.generateUniqueKey(kettleProductDimensionCloneData.getProductId().toString(), kettleProductDimensionCloneData.getDimension());
//            KettleStockOutProductWiseData productWiseData = new KettleStockOutProductWiseData(kettleProductDimensionCloneData.getProductId(), kettleProductDimensionCloneData.getDimension());
//            productWiseData.setTotalDownTime(productWiseDownTime.getOrDefault(key, 0L).intValue());
//            kettleStockOutPercentageData.addProductDimension(productWiseData);
//        });
//
//        kettleStockOutPercentageDataList.add(kettleStockOutPercentageData);
//    }

    private java.math.BigDecimal getStockOutPercentage(Integer totalDownTimeInMin, Integer totalOperationTimeInMin) {
        return SCMUtil.multiplyWithScale(
                SCMUtil.divideWithScale(
                        java.math.BigDecimal.valueOf(totalDownTimeInMin),
                        java.math.BigDecimal.valueOf(totalOperationTimeInMin),
                        6
                ),
                java.math.BigDecimal.valueOf(100),
                6
        );
    }

    // for product count and down time count map key
    private String generateKey(KettleStockOutDateWiseData data) {
        return SCMUtil.generateUniqueKey(
                data.getUnitId().toString(),
                data.getBrandId().toString(),
                data.getInventoryTrackLevel()
        );
    }

    private String generateKey(KettleUnitDetailDataClone data) {
        return SCMUtil.generateUniqueKey(
                data.getUnitId().toString(),
                data.getBrandId().toString(),
                data.getInventoryLevel()
        );
    }

    private void processProductDetail(ProductVO productDetail, Map<Integer, Product> productDetails,
                                    Map<String, Integer> productCount,
                                    Map<String, Set<KettleProductDimensionCloneData>> productDimensionMap) {
        Product product = productDetails.get(productDetail.getId());
        String key = SCMUtil.generateUniqueKey(
                productDetail.getBrandId().toString(),
                product.getInventoryTrackedLevel()
        );
        
        productDimensionMap.computeIfAbsent(key, k -> new HashSet<>());
        int count = 0;
        
        if (ValidationUtil.checkNonEmptyCollection(productDetail.getPrices(), "Prices are empty for product " + productDetail.getId())) {
            for (ProductPriceVO price : productDetail.getPrices()) {
                productDimensionMap.get(key).add(new KettleProductDimensionCloneData(productDetail.getId(), price.getDimension()));
                count++;
            }
        }
        
        productCount.merge(key, count, Integer::sum);
    }

//    private KettleUnitDetailDataClone createKettleUnitDetailDataClone(Unit unit, Brand brand, UnitHours unitHours,
//                                                                     String key, Integer totalProducts,
//                                                                     Map<String, Set<KettleProductDimensionCloneData>> productDimensionMap) {
//        KettleUnitDetailDataClone clone = new KettleUnitDetailDataClone();
//        clone.setUnitId(unit.getId());
//        clone.setBrandId(brand.getBrandId());
//        clone.setCafeOpening(brand.getBrandId() == AppConstants.GNT_BRAND_ID ?
//                SCMUtil.getDate(unitHours.getDeliveryOpeningTime(), unitHours.getDeliveryClosingTime(), false) :
//                SCMUtil.getDate(unitHours.getDineInOpeningTime(), unitHours.getDineInClosingTime(), false));
//        clone.setCafeClosing(brand.getBrandId() == AppConstants.GNT_BRAND_ID ?
//                SCMUtil.getDate(unitHours.getDeliveryOpeningTime(), unitHours.getDeliveryClosingTime(), true) :
//                SCMUtil.getDate(unitHours.getDineInOpeningTime(), unitHours.getDineInClosingTime(), true));
//        clone.setCafeOperational(SCMUtil.getStringFromBoolean(unitHours.isIsOperational()));
//        clone.setUnitStatus(unit.getStatus().toString());
//        clone.setIsLive(AppUtils.setStatus(unit.isLive()));
//        clone.setIsLiveInventoryEnabled(AppUtils.setStatus(unit.isLiveInventoryEnabled()));
//        clone.setInventoryLevel(key.split(SCMConstant.HYPHEN_SEPARATOR)[1]);
//        clone.setTotalProducts(totalProducts);
//
//        Set<KettleProductDimensionCloneData> productDimensions = productDimensionMap.get(key);
//        if (productDimensions != null) {
//            productDimensions.forEach(clone::addProductDimension);
//        }
//
//        return clone;
//    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRES_NEW)
    public CompletableFuture<Void> createKettleUnitDetailDataCloneAsyncByDate(Unit unit, Brand brand,
                                                                              Map<Integer, Product> productDetails,
                                                                              Collection<ProductVO> productVOS,
                                                                              Date businessDate) {
        try {
            log.info("Starting async processing for unit {} and brand {} on date {}", unit.getId(), brand.getBrandId(), businessDate);
            
            UnitHours unitHours = getCurrentDayBusinessHours(unit, brand.getBrandId(), businessDate);
            if (Objects.isNull(unitHours) || (brand.getBrandId() == AppConstants.GNT_BRAND_ID ?
                    (Objects.isNull(unitHours.getDeliveryOpeningTime()) || Objects.isNull(unitHours.getDeliveryClosingTime())) :
                    (Objects.isNull(unitHours.getDineInOpeningTime()) || Objects.isNull(unitHours.getDineInClosingTime())))) {
                log.warn("No business hours found for unit {} and brand {}", unit.getId(), brand.getBrandId());
                return CompletableFuture.completedFuture(null);
            }

            Map<String, Integer> productCount = new HashMap<>();
            Map<String, Set<KettleProductDimensionCloneData>> productDimensionMap = new HashMap<>();
            
            // Process products and build dimension map
            productVOS.stream()
                    .filter(e -> Objects.equals(e.getBrandId(), brand.getBrandId()) && 
                               e.isInventoryTracked() && 
                               !CollectionUtils.isEmpty(e.getPrices()))
                    .forEach(productDetail -> processProductDetail(productDetail, productDetails, productCount, productDimensionMap));

            if (ValidationUtil.checkIsEmptyCollection(productCount)) {
                log.info("No products found for unit {} and brand {}", unit.getId(), brand.getBrandId());
                return CompletableFuture.completedFuture(null);
            }

            // Create and persist data in batches for better performance
            List<KettleUnitDetailDataClone> batchList = new ArrayList<>();
            productCount.forEach((key, value) -> {
                KettleUnitDetailDataClone clone = createKettleUnitDetailDataCloneByDate(unit, brand, unitHours, key, value, productDimensionMap, businessDate);
                batchList.add(clone);
            });

            // Insert remaining items
            if (!batchList.isEmpty()) {
                kettleStockOutDao.insertList(batchList);
            }

            log.info("Completed async processing for unit {} and brand {} on date {} with {} records",
                     unit.getId(), brand.getBrandId(), businessDate, productCount.size());
            
        } catch (Exception e) {
            log.error("createKettleUnitDetailDataCloneAsyncByDate : Error in async processing for unit {} and brand {} on date {}", unit.getId(), brand.getBrandId(), businessDate, e);
            throw e;
        }
        
        return CompletableFuture.completedFuture(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRES_NEW)
    public CompletableFuture<Void> processUnitStockOutPercentageAsyncByDate(Integer unitId,
                                                                            List<KettleUnitDetailDataClone> kettleUnitDetailDataClones,
                                                                            Map<Integer, KettleProductDataClone> productDataCloneMap,
                                                                            Date businessDate) {
        try {
            log.info("Starting async processing of stock out percentage for unitId: {} on date: {}", unitId, businessDate);
            
            Date openingMinDate = kettleUnitDetailDataClones.stream()
                    .map(KettleUnitDetailDataClone::getCafeOpening)
                    .min(Date::compareTo)
                    .orElseThrow(() -> new IllegalStateException("No Opening date found for unitId: " + unitId));

            Date closingMaxDate = kettleUnitDetailDataClones.stream()
                    .map(KettleUnitDetailDataClone::getCafeClosing)
                    .max(Date::compareTo)
                    .orElseThrow(() -> new IllegalStateException("No Closing date found for unitId: " + unitId));

            // get the business date of these two
            Date businessOpeningMinDate = SCMUtil.getBusinessDateScm(openingMinDate);
            Date businessClosingMaxDate = SCMUtil.getBusinessDateScm(closingMaxDate);

            // get all business dates between opening and closing dates (inclusive)
            List<Date> businessDatesList = SCMUtil.getBusinessDatesBetween(businessOpeningMinDate, businessClosingMaxDate);

            log.info("Found {} business dates between {} and {} for unitId: {} for businessDate : {}",
                    Arrays.toString(businessDatesList.toArray()) , businessOpeningMinDate, businessClosingMaxDate, unitId, businessDate);

            // get the Kettle Stock Out Date wise data for all these dates and left join fetch the timings also
            List<KettleStockOutDateWiseData> byUnitIdAndBusinessDates = kettleStockOutDao.findByUnitIdAndBusinessDates(unitId, businessDatesList);
            for (KettleStockOutDateWiseData data : byUnitIdAndBusinessDates) {
                KettleProductDataClone productDataClone = productDataCloneMap.get(data.getKettleProductId());
                data.setInventoryTrackLevel(productDataClone.getInventoryTrackLevel());
                data.setBrandId(productDataClone.getBrandId());
            }

            // make map like TreeMap<Date, Map<String , List<KettleStockOutDateWiseData>> group by date , and map it by generate key on the KettleStockOutDateWiseData
            TreeMap<Date, Map<String, List<KettleStockOutDateWiseData>>> stockOutDateWiseDataMap = byUnitIdAndBusinessDates.stream()
                    .collect(Collectors.groupingBy(KettleStockOutDateWiseData::getBusinessDate,
                            TreeMap::new,
                            Collectors.groupingBy(this::generateKey)));

            List<KettleStockOutPercentageData> kettleStockOutPercentageDataList = new ArrayList<>();
            kettleUnitDetailDataClones.forEach(kettleUnitDetailDataClone -> 
                calculateAndStoreStockOutByDate(kettleUnitDetailDataClone, stockOutDateWiseDataMap, kettleStockOutPercentageDataList, businessDate));

            if (!CollectionUtils.isEmpty(kettleStockOutPercentageDataList)) {
                kettleStockOutDao.addAll(kettleStockOutPercentageDataList);
            }

            log.info("Completed async processing of stock out percentage for unitId: {} on date: {} with {} records", 
                     unitId, businessDate, kettleStockOutPercentageDataList.size());
            
        } catch (Exception e) {
            log.error("processUnitStockOutPercentageAsyncByDate : Error processing stock out percentage for unitId: {} on date: {}", unitId, businessDate, e);
            throw e;
        }
        
        return CompletableFuture.completedFuture(null);
    }

    private KettleUnitDetailDataClone createKettleUnitDetailDataCloneByDate(Unit unit, Brand brand, UnitHours unitHours,
                                                                           String key, Integer totalProducts,
                                                                           Map<String, Set<KettleProductDimensionCloneData>> productDimensionMap,
                                                                           Date businessDate) {
        KettleUnitDetailDataClone clone = new KettleUnitDetailDataClone();
        clone.setUnitId(unit.getId());
        clone.setBrandId(brand.getBrandId());
        clone.setCafeOpening(brand.getBrandId() == AppConstants.GNT_BRAND_ID ?
                SCMUtil.getDate(unitHours.getDeliveryOpeningTime(), unitHours.getDeliveryClosingTime(), false, businessDate) :
                SCMUtil.getDate(unitHours.getDineInOpeningTime(), unitHours.getDineInClosingTime(), false, businessDate));
        clone.setCafeClosing(brand.getBrandId() == AppConstants.GNT_BRAND_ID ?
                SCMUtil.getDate(unitHours.getDeliveryOpeningTime(), unitHours.getDeliveryClosingTime(), true, businessDate) :
                SCMUtil.getDate(unitHours.getDineInOpeningTime(), unitHours.getDineInClosingTime(), true, businessDate));
        clone.setCafeOperational(SCMUtil.getStringFromBoolean(unitHours.isIsOperational()));
        clone.setUnitStatus(unit.getStatus().toString());
        clone.setIsLive(AppUtils.setStatus(unit.isLive()));
        clone.setIsLiveInventoryEnabled(AppUtils.setStatus(unit.isLiveInventoryEnabled()));
        clone.setInventoryLevel(key.split(SCMConstant.HYPHEN_SEPARATOR)[1]);
        clone.setTotalProducts(totalProducts);

        Set<KettleProductDimensionCloneData> productDimensions = productDimensionMap.get(key);
        if (productDimensions != null) {
            productDimensions.forEach(clone::addProductDimension);
        }

        return clone;
    }

    private void calculateAndStoreStockOutByDate(KettleUnitDetailDataClone kettleUnitDetailDataClone,
                                                TreeMap<Date, Map<String, List<KettleStockOutDateWiseData>>> stockOutDateWiseDataMap,
                                                List<KettleStockOutPercentageData> kettleStockOutPercentageDataList,
                                                Date businessDate) {
        // Similar to calculateAndStoreStockOut but uses the passed businessDate for yesterday calculation
        long stockOutInMinutes = 0;

        if (AppUtils.getBusinessDate().compareTo(businessDate) != 0) {
            // If today, we consider yesterday's data
            businessDate = AppUtils.getDayBeforeOrAfterDay(businessDate, 1);
        }
        Date yesterday = AppUtils.getDayBeforeOrAfterDay(businessDate, -1);

        Set<String> uniqueCombinations = new HashSet<>();
        Map<String, Pair<Long, Set<KettleStockOutProductWiseTimingsData>>> productWiseDownTime = new HashMap<>();

        if (!CollectionUtils.isEmpty(kettleUnitDetailDataClone.getKettleProductDimensionCloneDataSet())) {
            uniqueCombinations = kettleUnitDetailDataClone.getKettleProductDimensionCloneDataSet().stream().map(e -> SCMUtil.generateUniqueKey(e.getProductId().toString(), e.getDimension())).collect(Collectors.toSet());
        }

        Date openingDate = kettleUnitDetailDataClone.getCafeOpening();
        Date closingDate = kettleUnitDetailDataClone.getCafeClosing();
        Date businessOpeningDate = SCMUtil.getBusinessDateScm(openingDate);
        Date businessClosingDate = SCMUtil.getBusinessDateScm(closingDate);

        List<Date> businessDatesBetween = SCMUtil.getBusinessDatesBetween(businessOpeningDate, businessClosingDate);

        for (Date currentBusinessDate : businessDatesBetween) {
            // for the business date create start of business date and set the time to 5:00 AM and endOfBusinessDate and set the time to 05:00 AM of next day
            Date startOfBusinessDate = SCMUtil.getStartOfBusinessDate(currentBusinessDate);
            Date endOfBusinessDate = SCMUtil.getEndOfBusinessDate(currentBusinessDate);

            Map<String, List<KettleStockOutDateWiseData>> stockOutDataMap = stockOutDateWiseDataMap.getOrDefault(currentBusinessDate, new HashMap<>());
            if (stockOutDataMap.containsKey(generateKey(kettleUnitDetailDataClone))) {
                List<KettleStockOutDateWiseData> stockOutDataList = stockOutDataMap.get(generateKey(kettleUnitDetailDataClone));
                if (!CollectionUtils.isEmpty(stockOutDataList)) {
                    for (KettleStockOutDateWiseData stockOutData : stockOutDataList) {
                        long stockOutForThatBusinessDay = 0;
                        for (KettleStockOutTimingsData kettleStockOutTimingsData : stockOutData.getKettleStockOutTimingsDataSet()) {
                            Date stockOutTime = kettleStockOutTimingsData.getStockOutTime();
                            Date stockInTime = kettleStockOutTimingsData.getStockInTime();

                            if (Objects.isNull(stockInTime)) {
                                stockOutForThatBusinessDay += SCMUtil.getMinutesDifference(stockOutTime, endOfBusinessDate);
                            }

                            if (Objects.nonNull(stockOutTime) && Objects.nonNull(stockInTime)) {
                                if (stockOutTime.compareTo(openingDate) < 0 && stockInTime.compareTo(openingDate) < 0) {
                                    log.info("Not Considering the entry as both stock out and stock in time are before opening date for unitId: {}, stockOutTime: {}, stockInTime: {}",
                                            kettleUnitDetailDataClone.getUnitId(), stockOutTime, stockInTime);
                                    continue;
                                }

                                if (stockOutTime.compareTo(closingDate) > 0) {
                                    log.info("Not Considering the entry as Stock Out time is after closing date for unitId: {}, stockOutTime: {}, stockInTime: {}",
                                            kettleUnitDetailDataClone.getUnitId(), stockOutTime, stockInTime);
                                    continue;
                                }


                                if (stockOutTime.compareTo(openingDate) < 0) {
                                    stockOutTime = openingDate;
                                }

                                if (stockInTime.after(closingDate)) {
                                    stockInTime = closingDate;
                                }
                            }


                            if (Objects.isNull(kettleStockOutTimingsData.getStockInTime())) {
                                if (stockOutTime.compareTo(closingDate) > 0) {
                                    log.info("Not Considering the entry as Stock Out time is after closing date for unitId: {}, stockOutTime: {}, stockInTime: {}",
                                            kettleUnitDetailDataClone.getUnitId(), stockOutTime, stockInTime);
                                    continue;
                                }

                                if (closingDate.after(endOfBusinessDate)) {
                                    stockInTime = endOfBusinessDate;
                                } else {
                                    stockInTime = closingDate;
                                }

                                if (stockOutTime.compareTo(openingDate) < 0) {
                                    stockOutTime = openingDate;
                                }

                            }

                            // now we have in time and out time
                            String key = SCMUtil.generateUniqueKey(stockOutData.getKettleProductId().toString(), stockOutData.getDimension());
                            if (uniqueCombinations.contains(key)) {
                                long productDownTime = SCMUtil.getMinutesDifference(stockOutTime, stockInTime);
                                // OVER HERE NEED TO ADD THE STOCK OUT TIMINGS BY CAFE TIMINGS
                                stockOutInMinutes += productDownTime;
                                
                                // Create timings data entry
                                KettleStockOutProductWiseTimingsData timingsData = new KettleStockOutProductWiseTimingsData(stockOutTime, stockInTime);
                                
                                if (!productWiseDownTime.containsKey(key)) {
                                    Set<KettleStockOutProductWiseTimingsData> timingsSet = new HashSet<>();
                                    timingsSet.add(timingsData);
                                    productWiseDownTime.put(key, new Pair<>(productDownTime, timingsSet));
                                } else {
                                    Pair<Long, Set<KettleStockOutProductWiseTimingsData>> existingPair = productWiseDownTime.get(key);
                                    Long totalDownTime = existingPair.getKey() + productDownTime;
                                    Set<KettleStockOutProductWiseTimingsData> timingsSet = existingPair.getValue();
                                    timingsSet.add(timingsData);
                                    productWiseDownTime.put(key, new Pair<>(totalDownTime, timingsSet));
                                }
                            }
                        }
                        stockOutData.setBusinessDateWiseStockOutTimeInMin(stockOutForThatBusinessDay);
                    }
                }
            }
        }

        KettleStockOutPercentageData kettleStockOutPercentageData = new KettleStockOutPercentageData();
        kettleStockOutPercentageData.setUnitId(kettleUnitDetailDataClone.getUnitId());
        kettleStockOutPercentageData.setBusinessDate(yesterday);
        kettleStockOutPercentageData.setBrandId(kettleUnitDetailDataClone.getBrandId());
        kettleStockOutPercentageData.setInventoryTrackLevel(kettleUnitDetailDataClone.getInventoryLevel());
        kettleStockOutPercentageData.setProductCount(kettleUnitDetailDataClone.getTotalProducts());
        kettleStockOutPercentageData.setCafeOpening(kettleUnitDetailDataClone.getCafeOpening());
        kettleStockOutPercentageData.setCafeClosing(kettleUnitDetailDataClone.getCafeClosing());
        kettleStockOutPercentageData.setCafeOperational(kettleUnitDetailDataClone.getCafeOperational());
        kettleStockOutPercentageData.setUnitStatus(kettleUnitDetailDataClone.getUnitStatus());
        kettleStockOutPercentageData.setIsLive(kettleUnitDetailDataClone.getIsLive());
        kettleStockOutPercentageData.setIsLiveInventoryEnabled(kettleUnitDetailDataClone.getIsLiveInventoryEnabled());
        kettleStockOutPercentageData.setProductOperationalTimeInMin((int) SCMUtil.getMinutesDifference(
                kettleUnitDetailDataClone.getCafeOpening(), kettleUnitDetailDataClone.getCafeClosing()) * kettleUnitDetailDataClone.getTotalProducts());
        kettleStockOutPercentageData.setTotalOperationTimeInMin(kettleStockOutPercentageData.getProductOperationalTimeInMin() - (int) stockOutInMinutes);
        kettleStockOutPercentageData.setTotalDownTimeInMin((int) stockOutInMinutes);
        kettleStockOutPercentageData.setStockOutPercentage(getStockOutPercentage(kettleStockOutPercentageData.getTotalDownTimeInMin(),
                kettleStockOutPercentageData.getProductOperationalTimeInMin()));

        kettleUnitDetailDataClone.getKettleProductDimensionCloneDataSet().forEach(kettleProductDimensionCloneData -> {
            String key = SCMUtil.generateUniqueKey(kettleProductDimensionCloneData.getProductId().toString(), kettleProductDimensionCloneData.getDimension());
            KettleStockOutProductWiseData productWiseData = new KettleStockOutProductWiseData(kettleProductDimensionCloneData.getProductId(), kettleProductDimensionCloneData.getDimension());
            
            Pair<Long, Set<KettleStockOutProductWiseTimingsData>> downTimePair = productWiseDownTime.get(key);
            if (downTimePair != null) {
                productWiseData.setTotalDownTime(downTimePair.getKey().intValue());
                // Add all timings data to the product wise data
                Set<KettleStockOutProductWiseTimingsData> timingsDataSet = downTimePair.getValue();
                if (timingsDataSet != null) {
                    timingsDataSet.forEach(productWiseData::addTimingsData);
                }
            } else {
                productWiseData.setTotalDownTime(0);
            }
            
            kettleStockOutPercentageData.addProductDimension(productWiseData);
        });

        kettleStockOutPercentageDataList.add(kettleStockOutPercentageData);
    }

    private UnitHours getCurrentDayBusinessHours(Unit unit, Integer brandId, Date passedBusinessDate) {
        try {
            List<UnitHours> unitHours;
            if (Objects.equals(brandId, AppConstants.GNT_BRAND_ID)) {
                unitHours = unit.getGntOperationalHours();
            } else {
                unitHours = unit.getOperationalHours();
            }
            if (ValidationUtil.checkIsEmptyCollection(unitHours)) {
                return null;
            }

            int dayOfWeek = SCMUtil.getDayOfWeek(passedBusinessDate);
            for (UnitHours hours : unitHours) {
                if (Objects.equals(dayOfWeek, hours.getDayOfTheWeekNumber())) {
                    return hours;
                }
            }
            return null;

        } catch (Exception e) {
            log.error("Error while fetching business hours for unit {}", unit.getId(), e);
            return null;
        }
    }
}

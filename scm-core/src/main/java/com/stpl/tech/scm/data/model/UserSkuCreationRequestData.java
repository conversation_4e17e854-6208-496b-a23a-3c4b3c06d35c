package com.stpl.tech.scm.data.model;

import com.stpl.tech.scm.domain.model.SwitchStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static javax.persistence.GenerationType.IDENTITY;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "USER_SKU_CREATION_REQUEST")
public class UserSkuCreationRequestData {

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "USER_SKU_CREATION_ID", unique = true, nullable = false)
    private Integer userSkuCreationId;

    @Column(name = "SKU_NAME", nullable = false)
    private String skuName;

    @Column(name = "SKU_DESCRIPTION", length = 1000)
    private String skuDescription;

    @Column(name = "SUPPORTS_LOOSE_ORDERING", nullable = false, length = 1)
    private String supportsLooseOrdering;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_DATE", length = 19)
    private Date creationDate;

    @Column(name = "CREATED_BY", nullable = false)
    private int createdBy;

    @Column(name = "HAS_INNER", nullable = false, length = 1)
    private String hasInner;

    @Column(name = "HAS_CASE", nullable = false, length = 1)
    private String hasCase;

    @ManyToOne
    @JoinColumn(name = "LINKED_PRODUCT_ID", nullable = false)
    private ProductDefinitionData linkedProduct;

    @Column(name = "SHELF_LIFE_IN_DAYS", nullable = false)
    private int shelfLifeInDays;

    @Column(name = "SKU_STATUS", nullable = false, length = 15)
    @Enumerated(EnumType.STRING)
    private SwitchStatus skuStatus;

    @Column(name = "UNIT_OF_MEASURE", nullable = false, length = 15)
    private String unitOfMeasure;

    @Column(name = "SKU_IMAGE", nullable = true, length = 255)
    private Integer skuImageId;

    @Column(name = "UNIT_PRICE", nullable = true)
    private BigDecimal unitPrice;

    @Column(name = "NEGOTIATED_UNIT_PRICE", nullable = true)
    private BigDecimal negotiatedUnitPrice;

    @Column(name = "PRICE_LAST_UPDATED", nullable = true)
    private Date priceLastUpdated;

    @Column(name = "TORQUS_ID", nullable = true)
    private String torqusSKU;

    @Column(name = "IS_DEFAULT", nullable = false, length = 1)
    private String isDefault = "N";

    @Column(name = "INVENTORY_LIST_ID", nullable = true)
    private Integer inventoryList;

    @Column(name = "SKU_CODE")
    private String skuCode;

    @Column(name = "TAX_CATEGORY_CODE", nullable = false, length = 30)
    private String taxCategoryCode;

    @Column(name = "VO_DISCONTINUED_FROM", nullable = true)
    private Date voDisContinuedFrom;

    @Column(name = "RO_DISCONTINUED_FROM", nullable = true)
    private Date roDisContinuedFrom;

    @Column(name = "IS_BRANDED", nullable = true)
    private String isBranded;

    @Column(name = "BRAND" , nullable = true)
    private String brand;

    @Column(name = "APPROVAL_DOC_ID" , nullable = true)
    private Integer approvalDocId;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "userSkuCreationRequestData")
    private List<SkuCreationRequestLogs> skuCreationRequestLogsList;
}

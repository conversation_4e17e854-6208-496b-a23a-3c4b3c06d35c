package com.stpl.tech.scm.data.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name="GST_STATE_METADATA")
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GstStateMetaData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name="ID")
    Long id;
    @Column(name="GST_STATE_CODE")
    Integer gstStateCode;
    @Column(name="STATE_NAME")
    String stateName;
    @Column(name = "ALPHA_CODE")
    String alphaCode;
}


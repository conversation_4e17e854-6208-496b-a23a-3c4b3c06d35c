/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;

import static javax.persistence.GenerationType.IDENTITY;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;


@SuppressWarnings("serial")
@Entity
@Table(name = "GOODS_RECEIVED_ITEM_DRILLDOWN")
public class GoodsReceivedItemDrilldown implements java.io.Serializable{

	private Integer goodsReceivedItemDrilldownId;
	private GoodsReceivedItemData receivedItemData;
	private BigDecimal quantity;
	private BigDecimal price;
	private Date addTime;
	private Date expiryDate;
	private BigDecimal rejection;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "GOODS_RECEIVED_ITEM_DRILLDOWN_ID", unique = true, nullable = false)
	public Integer getGoodsReceivedItemDrilldownId() {
		return goodsReceivedItemDrilldownId;
	}

	public void setGoodsReceivedItemDrilldownId(Integer goodsReceivedItemDrilldownId) {
		this.goodsReceivedItemDrilldownId = goodsReceivedItemDrilldownId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "GOODS_RECEIVED_ITEM_ID", nullable = true)
	public GoodsReceivedItemData getReceivedItemData() {
		return receivedItemData;
	}

	public void setReceivedItemData(GoodsReceivedItemData receivedItemData) {
		this.receivedItemData = receivedItemData;
	}

	@Column(name = "QUANTITY", precision = 16)
	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	@Column(name = "PRICE", precision = 16)
	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal cost) {
		this.price = cost;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ADD_TIME", nullable = false, length = 19)
	public Date getAddTime() {
		return addTime;
	}

	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EXPIRY_DATE", nullable = false, length = 10)
	public Date getExpiryDate() {
		return expiryDate;
	}

	public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}

	@Column(name = "REJECTION", precision = 16)
	public BigDecimal getRejection() {
		return rejection;
	}

	public void setRejection(BigDecimal rejection) {
		this.rejection = rejection;
	}

}
package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 11-07-2017.
 */
@Entity
@Table(name = "DERIVED_FULFILMENT_TYPE_MAPPING")
public class DerivedMappingData {

    private Integer mappingId;
    private ProductDefinitionData product;
    private String fulfillmentType;
    private Integer unitId;


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "DERIVED_MAPPING_ID", unique = true, nullable = false)
    public Integer getMappingId() {
        return mappingId;
    }

    public void setMappingId(Integer mappingId) {
        this.mappingId = mappingId;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PRODUCT_ID", nullable = false)
    public ProductDefinitionData getProduct() {
        return product;
    }

    public void setProduct(ProductDefinitionData product) {
        this.product = product;
    }

    @Column(name = "FULFILMENT_TYPE", nullable = true)
    public String getFulfillmentType() {
        return fulfillmentType;
    }

    public void setFulfillmentType(String fulfillmentType) {
        this.fulfillmentType = fulfillmentType;
    }

    @Column(name = "DELIVERY_UNIT_ID", nullable = false)
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

}

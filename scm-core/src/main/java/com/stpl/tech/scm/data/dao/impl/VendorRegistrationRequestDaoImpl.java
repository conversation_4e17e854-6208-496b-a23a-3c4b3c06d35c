package com.stpl.tech.scm.data.dao.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.persistence.Query;

import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.dao.SCMVendorManagementDao;
import com.stpl.tech.scm.data.model.VendorDetailData;
import com.stpl.tech.scm.data.model.VendorEditedDetail;
import com.stpl.tech.scm.data.model.VendorLogData;
import com.stpl.tech.scm.notification.email.GenericEmailTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.stpl.tech.master.core.PasswordImpl;
import com.stpl.tech.scm.core.exception.VendorRegistrationException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.VendorRegistrationRequestDao;
import com.stpl.tech.scm.data.model.PaymentRequestData;
import com.stpl.tech.scm.data.model.PurchaseOrderData;
import com.stpl.tech.scm.data.model.VendorCompanyDebitMapping;
import com.stpl.tech.scm.data.model.VendorCompanyDetailData;
import com.stpl.tech.scm.data.model.VendorGoodsReceivedData;
import com.stpl.tech.scm.data.model.VendorRegistrationRequestDetail;
import com.stpl.tech.scm.domain.model.PaymentRequestStatus;
import com.stpl.tech.scm.domain.model.PurchaseOrderStatus;
import com.stpl.tech.scm.domain.model.VendorRegistrationRequest;
import com.stpl.tech.scm.domain.model.VendorStatus;
import com.stpl.tech.util.AppUtils;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Repository
public class VendorRegistrationRequestDaoImpl extends SCMAbstractDaoImpl implements VendorRegistrationRequestDao {

	private static final Logger LOG = LoggerFactory.getLogger(VendorRegistrationRequestDaoImpl.class);

	@Autowired
	private EnvProperties env;

	@Autowired
	private MasterDataCache masterDataCache;

	@Autowired
	private SCMVendorManagementDao vendorManagementDao;

	@Override
	public List<VendorRegistrationRequest> getAllVendorRegistrationRequest(Date startDate, Date endDate,
			boolean getPendingOnly) {
		List<VendorRegistrationRequest> list = new ArrayList<>();
		Query query = null;
		if (getPendingOnly) {
			query = manager.createQuery(
					"FROM VendorRegistrationRequestDetail E WHERE E.requestStatus IN (:requestStatus) AND  E.requestDate >= :startDate AND E.requestDate <= :endDate order by E.requestDate desc");
			List<String> statusList = new ArrayList<String>();
			statusList.add(VendorStatus.IN_PROCESS.name());
			statusList.add(VendorStatus.COMPLETED.name());
			statusList.add(VendorStatus.INITIATED.name());
			query.setParameter("requestStatus", statusList);
		} else {
			query = manager.createQuery(
					"FROM VendorRegistrationRequestDetail E WHERE E.requestDate >= :startDate AND E.requestDate <= :endDate order by E.requestDate desc");
		}
		query.setParameter("startDate", AppUtils.getDate(startDate));
		query.setParameter("endDate", AppUtils.getNextDate(endDate));
		List<VendorRegistrationRequestDetail> result = query.getResultList();
		if (result != null) {
			for (VendorRegistrationRequestDetail r : result) {
				list.add(SCMDataConverter.convert(r));
			}
		}
		return list;
	}

	@Override
	public List<VendorRegistrationRequest> getAllVendorRegistrationRequest(List<String> requestStatus, Integer requestedBy) {
		List<VendorRegistrationRequest> list = new ArrayList<>();
		StringBuilder queryString = new StringBuilder("FROM VendorRegistrationRequestDetail E WHERE E.requestStatus IN (:requestStatus) ");
		boolean check = Objects.equals(SCMServiceConstants.KAPIL_GOLA_USER_ID, requestedBy) || Objects.equals(SCMServiceConstants.SYSTEM_USER_2, requestedBy);
		if(Objects.nonNull(requestedBy) && !check) {
			queryString.append("AND requestById =:requestedBy ");
		}
		queryString.append("order by E.requestDate desc");
		Query query = manager.createQuery(queryString.toString());
		query.setParameter("requestStatus", requestStatus);
		if(Objects.nonNull(requestedBy) && !check) {
			query.setParameter("requestedBy", requestedBy);
		}
		List<VendorRegistrationRequestDetail> result = query.getResultList();
		if (result != null) {
			for (VendorRegistrationRequestDetail r : result) {
				list.add(SCMDataConverter.convert(r));
			}
		}
		return list;
	}

	@Override
	public VendorRegistrationRequestDetail addVendorRegistrationRequest(VendorRegistrationRequest request, boolean edit) throws SumoException {
		VendorRegistrationRequestDetail rd = null;
		if(edit) {
			rd = findByVendorId(request.getVendorId());
		} else {
			rd = new VendorRegistrationRequestDetail();
		}
		setValues(request, rd);
		rd.setRequestStatus(VendorStatus.INITIATED.name());
		rd.setAuthKey(generateAuthKey(request));
		rd.setRegistrationUrl(generateRegistrationUrl());
		if(request.getVendorId() != null && request.getVendorId() > 0){
			rd.setVendorId(request.getVendorId());
		}
		if(Objects.nonNull(request.getPanCardNumber())) {
			rd.setPanCardNumber(request.getPanCardNumber());
		}
		rd = add(rd,true);
		return rd;
	}

	@Override
	public VendorRegistrationRequestDetail findByVendorId(Integer vendorId) {
		try {
			Query query = manager.createQuery("FROM VendorRegistrationRequestDetail E"
					+ " WHERE E.vendorId = :vendorId order by E.requestDate desc");
			query.setParameter("vendorId", vendorId);
			List<VendorRegistrationRequestDetail> result = query.getResultList();
			if (!CollectionUtils.isEmpty(result)) {
				return result.get(0);
			}
		} catch (Exception e) {
			LOG.error("Exception occurred while fetching registration request", e);
			throw e;
		}
		return null;
	}

	@Override
	public String isPanCardAndVendorTypeAvailable(String panCard, String vendorType) {
		Query query = manager.createQuery(
				"FROM VendorRegistrationRequestDetail E WHERE E.panCardNumber= :panCard and E.vendorType = :type and E.requestStatus <>:cancelledStatus");
		query.setParameter("panCard" , panCard);
		query.setParameter("type",vendorType);
		query.setParameter("cancelledStatus", VendorStatus.CANCELLED.value());
		List<VendorRegistrationRequestDetail> vendorRegistrationRequestDetailList = query.getResultList();
		if(!CollectionUtils.isEmpty(vendorRegistrationRequestDetailList)) {
			return vendorRegistrationRequestDetailList.get(0).getVendorName();
		}
		return null;
	}

	@Override
	public List<VendorRegistrationRequestDetail> findInitiatedRequestsByName(String name , String type){
		List<String> requestStatus = Arrays.asList(VendorStatus.INITIATED.value() , VendorStatus.IN_PROCESS.value());
		Query query = null;
		query = manager.createQuery(
				"FROM VendorRegistrationRequestDetail E WHERE E.vendorName = :name and E.vendorType = :type  and  E.requestStatus IN (:requestStatus) ");
		query.setParameter("requestStatus", requestStatus);
		query.setParameter("name" , name);
		query.setParameter("type",type);
		return query.getResultList();

	}

	private String generateRegistrationUrl() {
		return env.getVendorRequestUrl();
	}

	private String generateAuthKey(VendorRegistrationRequest request) {
		try {
			return PasswordImpl.encryptUrlCodec(AppUtils.getCurrentTimeISTString());
		} catch (Exception e) {
			return null;
		}
	}

	@Override
	public boolean cancelVendorRegistrationRequest(Integer id) {
		if (id != null) {
			VendorRegistrationRequestDetail vd = find(VendorRegistrationRequestDetail.class, id);
			vd.setRequestStatus(VendorStatus.CANCELLED.name());
			update(vd,true);
			VendorDetailData vdd = find(VendorDetailData.class, vd.getVendorId());
			List<VendorLogData> logDataList = vendorManagementDao.getAllLogsById(vd.getVendorId());
			if(logDataList.size() >= 2) {
				vdd.setStatus(logDataList.get(1).getStatus());
			} else if(logDataList.size() == 1) {
				vdd.setStatus(logDataList.get(0).getStatus());
			}
			vdd = update(vdd, true);
			if(Objects.nonNull(vdd)) {
				addVendorLog(vdd);
			}
			return true;
		}
		return false;
	}

	public void addVendorLog(VendorDetailData vdd) {
		VendorLogData vld = new VendorLogData();
		try {
			vld.setVendorId(vdd.getVendorId());
			vld.setStatus((vdd.getStatus()));
			vld.setEntityName(vdd.getEntityName());
			vld.setVendorName(vdd.getFirstName() + " " + vdd.getLastName());
			vld.setUpdateTime(SCMUtil.getCurrentDateHourIST());
			vld.setLogData(vdd.getStatus() + "ED by " + masterDataCache.getEmployee(vdd.getRequestedBy()));
			vld = add(vld, true);
			if (Objects.isNull(vld)) {
				throw new SumoException("error in adding log data for vendor...");
			}
		} catch (SumoException e) {
			e.printStackTrace();
		}
	}

	@Override
	public boolean updateVendorStatus(Integer id, VendorStatus status) {
		if (id != null) {
			VendorRegistrationRequestDetail vd = find(VendorRegistrationRequestDetail.class, id);
			vd.setRequestStatus(status.name());
			update(vd,true);
			return true;
		}
		return false;
	}

	@Override
	public VendorRegistrationRequestDetail findByToken(String id) throws VendorRegistrationException {
		try {
			Query query = manager.createQuery("FROM VendorRegistrationRequestDetail E"
					+ " WHERE E.authKey = :authKey order by E.requestDate desc");
			query.setParameter("authKey", id);
			VendorRegistrationRequestDetail result = (VendorRegistrationRequestDetail) query.getSingleResult();
			if (result != null) {
				if (VendorStatus.INITIATED.equals(VendorStatus.valueOf(result.getRequestStatus()))
						|| VendorStatus.IN_PROCESS.equals(VendorStatus.valueOf(result.getRequestStatus()))) {
					return result;
				} else {
					throw new VendorRegistrationException(
							"Not a valid request. Please contact Chaayos to resolve conflicts.");
				}
			}
		} catch (Exception e) {
			LOG.error("Exception occurred while fetching registration request", e);
			throw new VendorRegistrationException("Could not find registration request", e);
		}
		return null;
	}

	@Override
	public VendorRegistrationRequestDetail getActiveVendorRequest(Integer vendorId) throws VendorRegistrationException {
		try {
			Query query = manager.createQuery("FROM VendorRegistrationRequestDetail E"
					+ " WHERE E.vendorId = :vendorId and E.requestStatus = :status order by E.requestDate desc");
			query.setParameter("vendorId", vendorId);
			query.setParameter("status", VendorStatus.IN_PROCESS.name());
			query.setMaxResults(1);
			VendorRegistrationRequestDetail result = (VendorRegistrationRequestDetail) query.getSingleResult();
			if (result != null) {
				if (VendorStatus.INITIATED.equals(VendorStatus.valueOf(result.getRequestStatus()))
						|| VendorStatus.IN_PROCESS.equals(VendorStatus.valueOf(result.getRequestStatus()))) {
					return result;
				} else {
					throw new VendorRegistrationException(
							"Not a valid request. Please contact Chaayos to resolve conflicts.");
				}
			}
		} catch (Exception e) {
			LOG.error("Exception occurred while fetching registration request", e);
			throw new VendorRegistrationException("Could not find registration request", e);
		}
		return null;
	}

	private void setValues(VendorRegistrationRequest r, VendorRegistrationRequestDetail v) {
		v.setCopyEmails(r.getCopyEmails());
		v.setEmail(r.getEmail());
		v.setRequestById(r.getRequestById());
		v.setRequestByName(r.getRequestByName());
		v.setRequestDate(AppUtils.getCurrentTimestamp());
		v.setRequestForId(r.getRequestForId());
		v.setRequestForName(r.getRequestForName());
		v.setVendorName(r.getVendorName());
		v.setVendorType(r.getVendorType().name());
		// skipped as it should be null when requesting a new vendor
		// v.setVendorLink(r.getVendorLink());
	}

	@Override
	public boolean findDuplicatePanVendor(String vendorType, String vendorPan) {
		Query query = manager.createQuery(
				"FROM VendorCompanyDetailData E WHERE E.PAN =:vendorPan");
		query.setParameter("vendorPan", vendorPan);
		List<VendorCompanyDetailData> result = query.getResultList();
		if (result != null) {
			for(VendorCompanyDetailData companyDetail : result) {
				if(companyDetail.getVendorDetail().getType().equalsIgnoreCase(vendorType)) {
					return true;
				}
			}
		}
		return false;
	}

	@Override
	public boolean findDuplicateVendorName(String vendorName,String vendorType, String city , String state) {
		List<String> ingoredStatuses = Arrays.asList(VendorStatus.IN_PROCESS.value() , VendorStatus.IN_ACTIVE.value() , VendorStatus.CANCELLED.value());
		Query query = manager.createQuery(
				"FROM VendorDetailData V WHERE V.entityName  = :name and V.type = :vendorType and V.status not in :ignoredStatus ");
		query.setParameter("vendorType", vendorType);
		query.setParameter("name", vendorName);
		query.setParameter("ignoredStatus",ingoredStatuses);
		List<VendorDetailData> result = query.getResultList();
		if (Objects.nonNull(result) && !result.isEmpty()) {
			result = result.stream().filter(vendor -> vendor.getVendorAddress().getCity().equals(city) && vendor.getVendorAddress().getState().equals(state)).
					collect(Collectors.toList());
			if(result.size() > 0){
				return true;
			}else{
				return false;
			}
		}
		return false;
	}

	@Override
	public String findVendorStatus(int vendorId) {
		String validationData = "";
		List<String> purchaseOrderStatus = new ArrayList<String>();
		purchaseOrderStatus.add(PurchaseOrderStatus.APPROVED.name());
		purchaseOrderStatus.add(PurchaseOrderStatus.IN_PROGRESS.name());
		Query query = manager.createQuery(
				"FROM PurchaseOrderData E WHERE E.generatedForVendor =:vendorId and E.status IN :postatusList");
		query.setParameter("vendorId", vendorId);
		query.setParameter("postatusList", purchaseOrderStatus);
		List<PurchaseOrderData> purchaseOrders = query.getResultList();
		if(!purchaseOrders.isEmpty()) {
			validationData = "There are "+purchaseOrders.size()+" Purchase Order's in InProgress State, ";
		}

		List<String> paymentRequestStatus = new ArrayList<String>();
		paymentRequestStatus.add(PaymentRequestStatus.CANCELLED.name());
		paymentRequestStatus.add(PaymentRequestStatus.REJECTED.name());
		Query querySec = manager.createQuery(
				"FROM PaymentRequestData P WHERE P.vendorId =:vendorId and P.currentStatus NOT IN :prstatusList");
		querySec.setParameter("vendorId", vendorId);
		querySec.setParameter("prstatusList", paymentRequestStatus);
		List<PaymentRequestData> paymentRequestData = querySec.getResultList();
		if(!paymentRequestData.isEmpty()) {
			validationData =validationData + "There are "+paymentRequestData.size()+" Payment Request's in Open State, ";
		}

		Query queryThird = manager.createQuery(
				"FROM VendorGoodsReceivedData G WHERE G.generatedForVendor =:vendorId");
		queryThird.setParameter("vendorId", vendorId);
		List<VendorGoodsReceivedData> vendorGoodRecieveData = queryThird.getResultList();
		if(!vendorGoodRecieveData.isEmpty()) {
			for(VendorGoodsReceivedData vendorGoods : vendorGoodRecieveData) {
				if(vendorGoods.getToBePaid().equalsIgnoreCase("Y") && vendorGoods.getPaymentRequestData() == null) {
					validationData =validationData + " Vendor Good Recieves are Pending, ";
					break;
				}
			}
		}

		Query queryFourth = manager.createQuery(
				"FROM VendorCompanyDebitMapping D WHERE D.vendorMappingId =:vendorId");
		queryFourth.setParameter("vendorId", vendorId);
		List<VendorCompanyDebitMapping> vendorCompanyDebitMapping = queryFourth.getResultList();
		if(!vendorCompanyDebitMapping.isEmpty()) {
			BigDecimal sum = BigDecimal.ZERO;
			for(VendorCompanyDebitMapping vendorCompanyDebit : vendorCompanyDebitMapping) {
				sum = sum.add(vendorCompanyDebit.getDebitBalance());
				}
			if(sum.compareTo(BigDecimal.ZERO) != 0) {
				validationData =validationData + " Vendor have remaining Debit balance.";
			}
		}

		return validationData;
	}


	@Override
	public  List<VendorEditedDetail> findVendorEditedDetailByVendorId(Integer vendorId)
	{
		try {
			Query query = manager.createQuery("From VendorEditedDetail "
				+ " where vendorId = :vendorId");
			query.setParameter("vendorId", vendorId);
			return query.getResultList();
		}
		catch (Exception e)
		{
			LOG.error("VendorEditedDetail not found for id {}",vendorId);
			return  null;
		}
	}

	@Override
	public  List<VendorCompanyDetailData> getInoperativePanData()
	{
		try {
			Query query = manager.createQuery("From VendorCompanyDetailData where panStatus is null");
			return query.getResultList();
		}catch(Exception e){
			LOG.error("Error while getting in-operative pan data : {}",e.getMessage());
			return  null;
		}
	}

	@Override
	public  List<VendorCompanyDetailData> getVendorCompanyDataFromPan(String pan)
	{
		try {
			Query query = manager.createQuery("From VendorCompanyDetailData where pan =:pan");
			query.setParameter("pan",pan);
			return query.getResultList();
		}catch(Exception e){
			LOG.error("Error while getting VendorCompanyDataFromPan : {}",e.getMessage());
			return  null;
		}
	}

    @Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean submitVendorDocument(VendorRegistrationRequest request) {
		try {
			VendorRegistrationRequestDetail vd = find(VendorRegistrationRequestDetail.class, request.getId());
			vd.setRequestStatus(VendorStatus.COMPLETED.name());
			VendorDetailData vdd = find(VendorDetailData.class, request.getVendorId());
			vdd.setStatus(VendorStatus.COMPLETED.name());
			vdd.setDocumentId(request.getDocumentId());
			sendMailForVendorRegistration(vd);
			update(vd,true);
			vdd = update(vdd, true);
			if(Objects.nonNull(vdd)) {
				addVendorLog(vdd);
			}
			return true;
		} catch (Exception e) {
			LOG.error("Error while adding document id to VendorRegistrationRequestData for requestId ", e);
			throw e;
		}
    }

	@Override
	public void sendMailForVendorRegistration(VendorRegistrationRequestDetail vd) {
		try {
			StringBuilder subject = new StringBuilder();
			StringBuilder body = new StringBuilder();
			List<String> emailsList = new ArrayList<>();
			emailsList.add(SCMServiceConstants.TECHNOLOGY_EMAIL);
			EmployeeBasicDetail emp = masterDataCache.getEmployeeBasicDetail(vd.getRequestById());
			if(vd.getRequestStatus().equals(VendorStatus.COMPLETED.name())) {
				emailsList.add(SCMServiceConstants.FINANCE_EMAIL);
				subject.append("VENDOR APPROVAL REQUEST");
				body.append("<b>" + emp.getName() + "</b> has uploaded the document for vendor : <b>" + vd.getVendorName() + "</b>.").append("Please proceed further.");
			} else {
				subject.append("UPLOAD VENDOR DOCUMENT");
				body.append("Vendor : <b>" + vd.getVendorName() + "</b> has filled the details. Please Upload the document and submit it for finance approval.");
				if (!StringUtils.isEmpty(emp.getEmailId())) {
					emailsList.add(emp.getEmailId());
				}
			}
			LOG.info("Logging mail with subject : {}", subject);
			new GenericEmailTemplate(subject.toString(), body.toString(), emailsList.toArray(new String[emailsList.size()]), env.getEnvType(), SCMServiceConstants.REPORTING_EMAIL).sendEmail();
		} catch (Exception e) {
			LOG.error("Error while logging Email : ", e);
		}
	}


}

package com.stpl.tech.scm.notification.SMS;

import com.stpl.tech.master.core.notification.sms.Messenger;
import com.stpl.tech.master.core.notification.sms.SMSType;
import com.stpl.tech.master.core.notification.sms.SMSWebServiceClient;
import com.stpl.tech.master.core.notification.sms.SolsInfiniWebServiceClient;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.RequestOrder;
import com.stpl.tech.scm.domain.model.RequestOrderItem;

/**
 * Created by Chaayos on 05-09-2016.
 */
public enum VendorROSMSNotification {

    VENDOR_RO_CREATE_NOTIFICATION(new Messenger<Object, String>() {
        public String getMessage(Object notification) {
            if(notification instanceof RequestOrder){
                RequestOrder requestOrder = (RequestOrder) notification;
                String products = "";
                for(RequestOrderItem requestOrderItem : requestOrder.getRequestOrderItems()){
                    products += requestOrderItem.getProductName()+" "+requestOrderItem.getRequestedAbsoluteQuantity()+" "+requestOrderItem.getUnitOfMeasure()+",";
                }
                products = products.substring(0, products.length()-1);
                String message = String.format(
                    "Request Order Chaayos %s, RO Id: %s requested: %s at %s",
                    requestOrder.getRequestUnit().getName(), requestOrder.getId().toString(), products, SCMUtil.getFormattedTime(requestOrder.getGenerationTime(), "dd MMM yyyy hh:mm aaa"));
                return message;
            }
            return null;
        }

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
    }),

    VENDOR_RO_RECEIVING_NOTIFICATION(new Messenger<Object, String>() {
        public String getMessage(Object notification) {
            if(notification instanceof RequestOrder){
                RequestOrder requestOrder = (RequestOrder) notification;
                String products = "";
                for(RequestOrderItem requestOrderItem : requestOrder.getRequestOrderItems()){
                    products += requestOrderItem.getProductName()+" "+requestOrderItem.getRequestedAbsoluteQuantity()+
                        "/"+requestOrderItem.getReceivedQuantity()+" "+requestOrderItem.getUnitOfMeasure()+",";
                }
                products = products.substring(0, products.length()-1);
                String message = String.format(
                    "Receiving Chaayos %s, RO Id: %s requested/received: %s",
                    requestOrder.getRequestUnit().getName(), requestOrder.getId().toString(), products);
                return message;
            }
            return null;
        }

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
    }),

    VENDOR_RO_RECEIVING_SLACK_NOTIFICATION(new Messenger<Object, String>() {
        public String getMessage(Object notification) {
            if(notification instanceof RequestOrder){
                RequestOrder requestOrder = (RequestOrder) notification;
                String products = "";
                for(RequestOrderItem requestOrderItem : requestOrder.getRequestOrderItems()){
                    if(requestOrderItem.getRequestedAbsoluteQuantity()!=requestOrderItem.getReceivedQuantity()){
                        products += "*_"+requestOrderItem.getProductName()+" "+requestOrderItem.getRequestedAbsoluteQuantity()+
                            "/"+requestOrderItem.getReceivedQuantity()+" "+requestOrderItem.getUnitOfMeasure()+"_*,";
                    }else{
                        products += requestOrderItem.getProductName()+" "+requestOrderItem.getRequestedAbsoluteQuantity()+
                            "/"+requestOrderItem.getReceivedQuantity()+" "+requestOrderItem.getUnitOfMeasure()+",";
                    }
                }
                products = products.substring(0, products.length()-1);
                String message = String.format(
                    "Receiving Chaayos %s, RO Id: %s requested/received: %s",
                    requestOrder.getRequestUnit().getName(), requestOrder.getId().toString(), products);
                return message;
            }
            return null;
        }

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
    });


    private final Messenger<Object, String> template;

    private VendorROSMSNotification(Messenger<Object, String> template) {
        this.template = template;
    }

    public Messenger<Object, String> getTemplate() {
        return template;
    }

    public String getMessage(Object object) {
        return template.getMessage(object);
    }

    public String getMessage(Object object, UnitBasicDetail detail) {
        return template.getMessage(object);
    }
}

package com.stpl.tech.scm.data.redis.service;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

@RequiredArgsConstructor
public class RedisHashMap<K, V> implements ConcurrentMap<K, V> {
    private final RedisTemplate<String, Object> redisTemplate;
    private final String redisKey;
    private final ObjectMapper objectMapper;
    private final Class<V> valueType;
    private final TypeReference<Map<K, V>> typeRef;

    @Override
    public V put(K key, V value) {
        try {
            redisTemplate.opsForHash().put(redisKey, key.toString(), objectMapper.writeValueAsString(value));
        } catch (Exception e) {
            throw new RuntimeException("Failed to serialize value for Redis", e);
        }
        return value;
    }


    @Override
    public V get(Object key) {
        Object raw = redisTemplate.opsForHash().get(redisKey, key.toString());
        if (raw instanceof String str) {
            try {
                return objectMapper.readValue(str, valueType);
            } catch (Exception e) {
                throw new RuntimeException("Failed to deserialize value from Redis", e);
            }
        }
        return null;
    }

//    @Override
    public Map<K, V> getAll() {
        Map<Object, Object> entries = redisTemplate.opsForHash().entries(redisKey);
        return entries.entrySet().stream().collect(Collectors.toMap(
                e -> (K) e.getKey(),
                e -> {
                    try {
                        return objectMapper.readValue((String) e.getValue(), valueType);
                    } catch (Exception ex) {
                        throw new RuntimeException("Failed to deserialize Redis value", ex);
                    }
                }
        ));
    }

    @Override
    public V remove(Object key) {
        redisTemplate.opsForHash().delete(redisKey, key.toString());
        return null;
    }

    @Override
    public void clear() {
        redisTemplate.delete(redisKey);
    }

    @Override
    public boolean containsKey(Object key) {
        return redisTemplate.opsForHash().hasKey(redisKey, key.toString());
    }

    @Override
    public Set<K> keySet() {
        return redisTemplate.opsForHash().keys(redisKey).stream()
                .map(k -> (K) k)
                .collect(Collectors.toSet());
    }

    @Override
    public Collection<V> values() {
        return redisTemplate.opsForHash().values(redisKey).stream()
                .map(o -> {
                    try {
                        return objectMapper.readValue((String) o, valueType);
                    } catch (Exception e) {
                        throw new RuntimeException("Failed to deserialize Redis value", e);
                    }
                })
                .collect(Collectors.toList());
    }

    // Optional overrides for map-like behavior
    @Override
    public int size() {
        return redisTemplate.opsForHash().size(redisKey).intValue();
    }

    @Override
    public boolean isEmpty() {
        return size() == 0;
    }

    @Override
    public boolean containsValue(Object value) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void putAll(Map<? extends K, ? extends V> m) {
        m.forEach(this::put);
    }

    @Override
    public Set<Entry<K, V>> entrySet() {
        return getAll().entrySet();
    }

    @Override
    public V putIfAbsent(K key, V value) {
        if (!containsKey(key)) {
            return put(key, value);
        }
        return get(key);
    }

    @Override
    public boolean remove(Object key, Object value) {
        throw new UnsupportedOperationException();
    }

    @Override
    public boolean replace(K key, V oldValue, V newValue) {
        throw new UnsupportedOperationException();
    }

    @Override
    public V replace(K key, V value) {
        throw new UnsupportedOperationException();
    }
}
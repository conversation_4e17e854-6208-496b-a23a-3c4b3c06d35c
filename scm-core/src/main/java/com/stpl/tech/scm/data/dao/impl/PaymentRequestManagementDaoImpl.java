package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.dao.PaymentRequestManagementDao;
import com.stpl.tech.scm.data.model.AdvancePaymentAuditLogData;
import com.stpl.tech.scm.data.model.AdvancePaymentData;
import com.stpl.tech.scm.data.model.CompanyBankMapping;
import com.stpl.tech.scm.data.model.DebitNoteDetailData;
import com.stpl.tech.scm.data.model.DocumentDetailData;
import com.stpl.tech.scm.data.model.GoodsRecievedToPaymentRequestMapping;
import com.stpl.tech.scm.data.model.HolidaysListData;
import com.stpl.tech.scm.data.model.InvoiceDeviationMappingData;
import com.stpl.tech.scm.data.model.PaymentCalendarData;
import com.stpl.tech.scm.data.model.PaymentInvoiceData;
import com.stpl.tech.scm.data.model.PaymentInvoiceItemData;
import com.stpl.tech.scm.data.model.PaymentInvoiceItemTaxData;
import com.stpl.tech.scm.data.model.PaymentRequestData;
import com.stpl.tech.scm.data.model.PaymentRequestItemMappingData;
import com.stpl.tech.scm.data.model.PaymentRequestLogData;
import com.stpl.tech.scm.data.model.PaymentRequestMetaData;
import com.stpl.tech.scm.data.model.PaymentRequestStatusLogData;
import com.stpl.tech.scm.data.model.ServiceReceivedItemData;
import com.stpl.tech.scm.data.model.SpecializedOrderInvoiceData;
import com.stpl.tech.scm.data.model.VendorDetailData;
import com.stpl.tech.scm.data.model.VendorGoodsReceivedData;
import com.stpl.tech.scm.domain.model.AdvancePaymentStatus;
import com.stpl.tech.scm.domain.model.HolidayDetails;
import com.stpl.tech.scm.domain.model.PaymentRequestStatus;
import com.stpl.tech.scm.domain.model.PaymentRequestType;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository
public class PaymentRequestManagementDaoImpl extends SCMAbstractDaoImpl implements PaymentRequestManagementDao {

    private static final Logger LOG = LoggerFactory.getLogger(PaymentRequestManagementDaoImpl.class);
    @Autowired
    private MasterDataCache masterDataCache;

    @Override
    public List<PaymentRequestData> getPaymentRequests(Integer vendorId, Integer unitId, Integer prId, Integer grId , String invoiceNumber,
                                                       Date startDate, Date endDate, String status, Integer companyId, Date paymentDate, String requestType) {
            StringBuilder queryString = new StringBuilder("");
            if("GOODS_RECEIVED".equals(requestType) || AppConstants.ADVANCE_PAYMENT.equalsIgnoreCase(requestType)){
                queryString.append("FROM PaymentRequestData p WHERE ");
            }else if("SERVICE_RECEIVED".equals(requestType)){
                queryString.append(" SELECT p,p.id, CONCAT(r.businessCostCenterName, ' (',b.code,')' ) FROM PaymentRequestData p LEFT JOIN ServiceReceivedData s ON p.id= s.paymentRequestData.id LEFT JOIN ServiceReceivedItemData r ON s.serviceReceivedId = r.serviceReceivedDataId " +
                        " LEFT JOIN BusinessCostCenterData b ON b.id = r.businessCostCenterId WHERE ");
            }else{
                LOG.info("Unexpected request type");
                return null;
            }

            if (prId != null) {
                queryString.append("p.id = :prId ");
                queryString.append("order by p.creationTime desc");
                Query query = manager.createQuery(queryString.toString());
                query.setParameter("prId", prId);
                List<PaymentRequestData> paymentRequestDataList = new ArrayList<>();
                List<Object[]> list = new ArrayList<>();
                Map<Integer,HashSet<String>> bccMap = new HashMap<>();
                HashSet<PaymentRequestData> dataHashSet = new HashSet<>();
                if("SERVICE_RECEIVED".equals(requestType)) {
                    list = query.getResultList();
                    for (Object[] obj : list) {
                        dataHashSet.add((PaymentRequestData) obj[0]);
                        if (Objects.isNull(obj[2])) {
                            continue;
                        }
                        if (!bccMap.containsKey(Integer.parseInt(String.valueOf(obj[1])))) {
                            bccMap.put(Integer.parseInt(String.valueOf(obj[1])), new HashSet<>());
                        }
                        bccMap.get(Integer.parseInt(String.valueOf(obj[1]))).add(String.valueOf(obj[2]));
                    }

                    for (PaymentRequestData data : dataHashSet) {
                        if (bccMap.containsKey(data.getId())) {
                            data.setBusinessCostCentreName(String.join(",", bccMap.get(data.getId())));
                        }
                        paymentRequestDataList.add(data);
                    }
                } else {
                    paymentRequestDataList = query.getResultList();
                }
                return paymentRequestDataList;
            } else {
                if (companyId != null) {
                    queryString.append("p.companyId = :companyId and ");
                }

                if (grId != null) {
                    //TODO find from PaymentRequestItemMappingData
                } else if (invoiceNumber != null) {
                    queryString.append("p.invoiceNumber = :invoiceNumber and ");
                } else {
                    if (vendorId != null) {
                        queryString.append("p.vendorId = :vendorId and ");
                    }
                    if (unitId != null) {
                        if ("GOODS_RECEIVED".equals(requestType)) {
                            queryString.append("p.requestingUnit = :unitId and ");
                        }
                        if ("SERVICE_RECEIVED".equals(requestType)) {
                            queryString.append("r.businessCostCenterName = :businessCostCenterName and ");
                        }
                    }
                    if (status != null) {
                        queryString.append("p.currentStatus = :status and ");
                    }
                    if (paymentDate != null) {
                        queryString.append("p.paymentDate = :paymentDate and ");
                    }

                }
                queryString.append("p.type = :requestType and ");
                queryString.append("p.creationTime >= :startDate and p.creationTime <= :endDate ");
                queryString.append("order by p.creationTime desc");
                Query query = manager.createQuery(queryString.toString());


                if (grId != null) {
                    query.setParameter("grId", grId);
                } else if (invoiceNumber != null) {
                    query.setParameter("invoiceNumber", invoiceNumber);
                } else {
                    if (vendorId != null) {
                        query.setParameter("vendorId", vendorId);
                    }
                    if (unitId != null) {
                        if("GOODS_RECEIVED".equals(requestType)) {
                            query.setParameter("unitId", unitId);
                        }
                        if("SERVICE_RECEIVED".equals(requestType)) {
                            query.setParameter("businessCostCenterName",masterDataCache.getUnit(unitId).getName() );
                        }
                        }
                    if (companyId != null) {
                        query.setParameter("companyId", companyId);
                    }
                    if (status != null) {
                        query.setParameter("status", status);
                    }
                    if (paymentDate != null) {
                        query.setParameter("paymentDate", paymentDate);
                    }
                }
                query.setParameter("startDate", startDate);
                query.setParameter("endDate", endDate);
                query.setParameter("requestType", requestType);
                if("GOODS_RECEIVED".equals(requestType) || AppConstants.ADVANCE_PAYMENT.equalsIgnoreCase(requestType)){
                    return query.getResultList();
                }
                else if("SERVICE_RECEIVED".equals(requestType)){
                    List<PaymentRequestData> paymentRequestDataList = new ArrayList<>();
                    List<String> bccList = new ArrayList<>();
                    List<Object[]> list = query.getResultList();
                 PaymentRequestData paymentRequestData = new PaymentRequestData();
                 Map<Integer,HashSet<String>> bccMap = new HashMap<>();
                 HashSet<PaymentRequestData> dataHashSet = new HashSet<>();
                 for(Object[] obj : list){
                     dataHashSet.add((PaymentRequestData) obj[0]);
                     if(!bccMap.containsKey(Integer.parseInt(String.valueOf(obj[1])))){
                         bccMap.put(Integer.parseInt(String.valueOf(obj[1])), new HashSet<>());
                     }
                     bccMap.get(Integer.parseInt(String.valueOf(obj[1]))).add(String.valueOf(obj[2]));
                 }

                 for(PaymentRequestData data : dataHashSet){
                     data.setBusinessCostCentreName(String.join(",", bccMap.get(data.getId())));
                     paymentRequestDataList.add(data);
                 }
                 return paymentRequestDataList;
                }
        }
        return new ArrayList<>();
    }

    @Override
    public List<PaymentRequestData> getGRPaymentRequestsForProcess(PaymentRequestType type, Integer vendorId, Integer unitId, Integer prId, String invoiceNumber,
                                                                   Date startDate, Date endDate, Integer companyId, String dateType, List<String> statusList) {

        StringBuilder queryString = new StringBuilder("SELECT DISTINCT p FROM PaymentRequestData p,VendorGoodsReceivedData vgr ");
        queryString.append("WHERE p.id = vgr.paymentRequestData and p.type=:type and ");
        if (prId != null) {
            queryString.append("p.id = :prId ");
            queryString.append("order by vgr.createdAt desc");
            Query query = manager.createQuery(queryString.toString());
            query.setParameter("type", type.name());
            query.setParameter("prId", prId);
            return query.getResultList();
        } else {
            if (companyId != null) {
                queryString.append("p.companyId = :companyId and ");
            }
            if (invoiceNumber != null) {
                queryString.append("p.invoiceNumber = :invoiceNumber and");
            }
            if (vendorId != null) {
                queryString.append("p.vendorId = :vendorId and ");
            }
            if (unitId != null) {
                queryString.append("p.requestingUnit = :unitId and ");
            }
            if (dateType.equals("GR Creation Date")) {
                queryString.append(" vgr.createdAt >= :startDate and vgr.createdAt <=:endDate and");
            }
            queryString.append(" p.currentStatus IN :statusList");
            Query query = manager.createQuery(queryString.toString());
            query.setParameter("type", type.name());
            if (dateType != null) {
                query.setParameter("startDate", startDate);
                query.setParameter("endDate", endDate);
            }
            if (companyId != null) {
                query.setParameter("companyId", companyId);
            }
            if (invoiceNumber != null) {
                query.setParameter("invoiceNumber", invoiceNumber);
            }
            if (vendorId != null) {
                query.setParameter("vendorId", vendorId);
            }
            if (unitId != null) {
                query.setParameter("unitId", unitId);
            }
            query.setParameter("statusList", statusList);
            return query.getResultList();
        }
    }

    @Override
    public List<PaymentRequestData> getPaymentRequestsForProcess(PaymentRequestType type, Integer vendorId, Integer unitId, Integer prId, String invoiceNumber,
                                                                 Date startDate, Date endDate, Integer companyId, String dateType, List<String> statusList) {
        StringBuilder queryString = new StringBuilder("FROM PaymentRequestData p WHERE p.type=:type and ");
        if (prId != null) {
            queryString.append("p.id = :prId ");
            queryString.append("order by p.creationTime desc");
            Query query = manager.createQuery(queryString.toString());
            query.setParameter("type", type.name());
            query.setParameter("prId", prId);
            return query.getResultList();
        } else {
            if (companyId != null) {
                queryString.append("p.companyId = :companyId and ");
            }
            if (invoiceNumber != null) {
                queryString.append("p.invoiceNumber = :invoiceNumber and");
            }
            if (vendorId != null) {
                queryString.append("p.vendorId = :vendorId and ");
            }
            if (unitId != null) {
                queryString.append("p.requestingUnit = :unitId and ");
            }
            if (dateType.equals("Payment Date")) {
                queryString.append(" p.paymentDate >= :startDate and p.paymentDate <=:endDate and");
            }
            if (dateType.equals("PR Creation Date")) {
                queryString.append(" p.creationTime >= :startDate and p.creationTime <= :endDate and");
            }
            queryString.append(" p.currentStatus IN :statusList order by p.creationTime desc");
            Query query = manager.createQuery(queryString.toString());
            query.setParameter("type", type.name());
            if (dateType != null) {
                query.setParameter("startDate", startDate);
                query.setParameter("endDate", endDate);
            }
            if (companyId != null) {
                query.setParameter("companyId", companyId);
            }
            if (invoiceNumber != null) {
                query.setParameter("invoiceNumber", invoiceNumber);
            }
            if (vendorId != null) {
                query.setParameter("vendorId", vendorId);
            }
            if (unitId != null) {
                query.setParameter("unitId", unitId);
            }
            query.setParameter("statusList", statusList);
            return query.getResultList();
        }
    }


    @Override
    public List<VendorGoodsReceivedData> findVendorGRFromPaymentRequest(int paymentRequestId) {
        Query query = manager.createQuery("FROM VendorGoodsReceivedData v WHERE v.paymentRequestData.id = :paymentRequestId");
        query.setParameter("paymentRequestId", paymentRequestId);
        return query.getResultList();
    }

    @Override
    public List<VendorGoodsReceivedData> findVendorGRFromPRByType(int paymentRequestId, String type) {
        Query query = manager.createQuery("FROM VendorGoodsReceivedData v WHERE v.paymentRequestData.id = :paymentRequestId and v.type = :type");
        query.setParameter("paymentRequestId", paymentRequestId);
        query.setParameter("type", type);
        return query.getResultList();
    }

    @Override
    public List<VendorGoodsReceivedData> findPaymentRequestFromVendorGR(int grId) {
        Query query = manager.createQuery("FROM VendorGoodsReceivedData v WHERE v.goodsReceivedId = :grId");
        query.setParameter("grId", grId);
        return query.getResultList();
    }

    @Override
    public List<PaymentRequestStatusLogData> findStatusChangeLogsByPaymentRequestId(int paymentRequestId) {
        Query query = manager.createQuery("FROM PaymentRequestStatusLogData p WHERE p.paymentRequestId = :paymentRequestId");
        query.setParameter("paymentRequestId", paymentRequestId);
        return query.getResultList();
    }

    @Override
    public List<PaymentRequestLogData> findPRLogsByPaymentRequest(int paymentRequestId) {
        Query query = manager.createQuery("FROM PaymentRequestLogData p WHERE p.paymentRequestId = :paymentRequestId");
        query.setParameter("paymentRequestId", paymentRequestId);
        return query.getResultList();
    }

    @Override
    public List<PaymentRequestItemMappingData> findMappedItemsByPaymenrRequestId(int paymentRequestId) {
        Query query = manager.createQuery("FROM PaymentRequestItemMappingData p WHERE p.paymentRequestId = :paymentRequestId");
        query.setParameter("paymentRequestId", paymentRequestId);
        return query.getResultList();
    }

    @Override
    public List<PaymentRequestItemMappingData> findMappedItemsByGRId(int vendorGRId, PaymentRequestType paymentRequestType) {
        Query query = manager.createQuery("FROM PaymentRequestItemMappingData p WHERE p.paymentRequestItemId = :vendorGRId AND " +
                "p.paymentRequestType = :paymentRequestType AND p.linkedPaymentRequestId IS NULL");
        query.setParameter("vendorGRId", vendorGRId)
                .setParameter("paymentRequestType", paymentRequestType.value());
        return query.getResultList();
    }

    @Override
    public PaymentInvoiceData findInvoiceByPaymentRequestId(int paymentRequestId) {
        Query query = manager.createQuery("FROM PaymentInvoiceData p WHERE p.paymentRequestData.id = :paymentRequestId");
        query.setParameter("paymentRequestId", paymentRequestId);
        return (PaymentInvoiceData) query.getSingleResult();
    }

    @Override
    public DebitNoteDetailData findDebitNoteByPaymentRequestId(int paymentRequestId) {
        Query query = manager.createQuery("FROM DebitNoteDetailData d WHERE d.paymentRequest.id = :paymentRequestId");
        query.setParameter("paymentRequestId", paymentRequestId);
        try {
            return (DebitNoteDetailData) query.getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    @Override
    public List<DebitNoteDetailData> findDebitNoteByPaymentRequest(int paymentRequestId) {
        Query query = manager.createQuery("FROM DebitNoteDetailData d WHERE d.paymentRequest.id = :paymentRequestId");
        query.setParameter("paymentRequestId", paymentRequestId);
        return query.getResultList();
    }

    @Override
    public List<InvoiceDeviationMappingData> findDeviations(int itemId, String deviationLevel) {
        Query query = manager.createQuery("FROM InvoiceDeviationMappingData p WHERE p.deviationItemId = :itemId AND p.deviationItemType = :deviationLevel");
        query.setParameter("itemId", itemId);
        query.setParameter("deviationLevel", deviationLevel);
        return query.getResultList();
    }

    @Override
    public List<DebitNoteDetailData> getDebitNotes(Integer vendorId, Integer unitId, Integer prId, Integer dnId, String invoiceNumber,
                                                   Date startDate, Date endDate, Integer companyId, String status) {
        StringBuilder queryString = new StringBuilder("FROM DebitNoteDetailData d WHERE ");
        if (prId != null) {
            queryString.append("d.paymentRequest.id = :prId ");
        } else if (dnId != null) {
            queryString.append("d.debitNoteDetailId = :dnId ");
        } else if (invoiceNumber != null) {
            queryString.append("d.invoiceNumber = :invoiceNumber ");
        } else {
            if (vendorId != null) {
                queryString.append("d.paymentRequest.vendorId = :vendorId and ");
            }
            if (unitId != null) {
                queryString.append("d.paymentRequest.requestingUnit = :unitId and ");
            }
            if (companyId != null) {
                queryString.append("d.paymentRequest.companyId = :companyId and ");
            }
            if (status != null) {
                queryString.append("d.debitNoteStatus = :debitNoteStatus and ");
            }
            queryString.append("d.generationTime >= :startDate and d.generationTime <= :endDate ");
        }
        queryString.append("order by d.generationTime desc");
        Query query = manager.createQuery(queryString.toString());
        if (prId != null) {
            query.setParameter("prId", prId);
        } else if (dnId != null) {
            query.setParameter("dnId", dnId);
        } else if (invoiceNumber != null) {
            query.setParameter("invoiceNumber", invoiceNumber);
        } else {
            if (vendorId != null) {
                query.setParameter("vendorId", vendorId);
            }
            if (unitId != null) {
                query.setParameter("unitId", unitId);
            }
            if (companyId != null) {
                query.setParameter("companyId", companyId);
            }
            if (status != null) {
                query.setParameter("debitNoteStatus", status);
            }
            query.setParameter("startDate", startDate)
                    .setParameter("endDate", endDate);
        }
        return query.getResultList();
    }

    @Override
    public List<PaymentCalendarData> getPaymentDatesFromCalendar(Integer cycleTag, Date prDate, Date invoiceDate, int companyId) {
        StringBuilder queryString = new StringBuilder("FROM PaymentCalendarData p WHERE p.paymentDate > :currentDate AND p.prCreationDate > :prDate " +
                "AND p.invoiceDate > :invoiceDate AND p.companyId = :companyId");
        if (cycleTag == 1) {
            queryString.append(" AND p.cycleTag = :cycleTag ");
        }
        queryString.append(" ORDER BY p.paymentDate ASC");
        Query query = manager.createQuery(queryString.toString());
        query.setParameter("currentDate", SCMUtil.getCurrentTimestamp())
                .setParameter("prDate", prDate)
                .setParameter("invoiceDate", invoiceDate)
                .setParameter("companyId", companyId);
        if (cycleTag == 1) {
            query.setParameter("cycleTag", cycleTag);
        }
        return query.getResultList();
    }

    @Override
    public List<PaymentRequestData> findUnfinishedPaymentsForVendor(Integer vendorId) {
        Query query = manager.createQuery("FROM PaymentRequestData p WHERE p.vendorId = :vendorId AND p.currentStatus != :cancelled" +
                " AND p.currentStatus != :rejected AND p.currentStatus != :paid AND p.currentStatus != :sentForPayment");
        query.setParameter("vendorId", vendorId)
                .setParameter("cancelled", PaymentRequestStatus.CANCELLED.value())
                .setParameter("rejected", PaymentRequestStatus.REJECTED.value())
                .setParameter("paid", PaymentRequestStatus.PAID.value())
                .setParameter("sentForPayment", PaymentRequestStatus.SENT_FOR_PAYMENT.value());
        return query.getResultList();
    }

    @Override
    public List<CompanyBankMapping> getBanksOfComapny(int companyId, String bankCode) {
        StringBuilder queryString = new StringBuilder("FROM CompanyBankMapping p WHERE p.companyId = :companyId AND accountStatus = :accountStatus");
        if (bankCode != null) {
            queryString.append(" and p.bankCode = :bankCode ");
        }
        Query query = manager.createQuery(queryString.toString());
        query.setParameter("companyId", companyId);
        query.setParameter("accountStatus", AppConstants.ACTIVE);
        if (bankCode != null) {
            query.setParameter("bankCode", bankCode);
        }
        return query.getResultList();
    }

    @Override
    public List<PaymentRequestData> findAllSentPaymentsForVendor(int vendorId) {
        Query query = manager.createQuery("FROM PaymentRequestData p WHERE p.vendorId = :vendorId AND p.currentStatus = :sentForPayment");
        query.setParameter("vendorId", vendorId)
                .setParameter("sentForPayment", PaymentRequestStatus.SENT_FOR_PAYMENT.value());
        return query.getResultList();
    }

    @Override
    public GoodsRecievedToPaymentRequestMapping getRejectedPaymentMapping(Integer grId) {
        Query query = manager.createQuery("FROM GoodsRecievedToPaymentRequestMapping p WHERE p.goodsRecievedId = :goodsRecievedId AND p.currentStatus = :currentStatus ORDER BY p.lastUpdated DESC");
        query.setParameter("goodsRecievedId", grId);
        query.setParameter("currentStatus", PaymentRequestStatus.REJECTED.value());
        try {
            return (GoodsRecievedToPaymentRequestMapping) query.setMaxResults(1).getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    @Override
    public List<VendorGoodsReceivedData> getRejectedPrForGr(Integer unitId, Integer vendorId) {
        StringBuilder queryString = new StringBuilder("FROM VendorGoodsReceivedData p WHERE ");;
        if (unitId != null) {
            queryString.append("p.deliveryUnitId = :unitId and ");
        }
        if (vendorId != null) {
            queryString.append("p.generatedForVendor = :vendorId and ");
        }
        queryString.append("p.paymentRequestData.id IS NULL ");
        Query query = manager.createQuery(queryString.toString());
        if (unitId != null) {
            query.setParameter("unitId", unitId);
        }
        if (vendorId != null) {
            query.setParameter("vendorId", vendorId);
        }
        return query.getResultList();
    }

    @Override
    public PaymentInvoiceData findPaymentInvoice(Integer paymentId) {
        Query query = manager.createQuery("FROM PaymentInvoiceData p WHERE p.paymentRequestData.id = :paymentId");
        query.setParameter("paymentId", paymentId);
        PaymentInvoiceData data = (PaymentInvoiceData) query.getSingleResult();
        return data;
    }

    @Override
    public boolean updatePrToGRMapping(Integer id,VendorGoodsReceivedData vendorGoodsReceivedData, PaymentRequestStatus paymentRequestStatus) {
        Query query = manager.createQuery("update GoodsRecievedToPaymentRequestMapping p set p.currentStatus= :status, p.lastUpdated = :updatedTime where"
                + " p.paymentRequestId = :paymentId and p.currentStatus = :currentStatus and p.goodsRecievedId =:grIds");
        query.setParameter("paymentId", id);
        query.setParameter("grIds", vendorGoodsReceivedData.getGoodsReceivedId());
        query.setParameter("status", paymentRequestStatus.value());
        query.setParameter("updatedTime", SCMUtil.getCurrentTimestamp());
        query.setParameter("currentStatus", PaymentRequestStatus.CREATED.value());
        return query.executeUpdate()>0?true:false;
    }

    @Override
    public Boolean isHoliday(Date date) {
        LOG.info("Checking from database holiday or not");
        try {
            Calendar cal = SCMUtil.getCalender();
            cal.setTime(date);
            LOG.info("Date to check is  : {} ",date);
            cal.set(Calendar.HOUR_OF_DAY, 0); // gets hour in 24h format
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);;
            return checkForDateInDatabase(cal.getTime());
        }
        catch (Exception e){
            LOG.error("Exception Occurred while checking for holiday or not :",e);
            return false;
        }
    }

    private Boolean checkForDateInDatabase(Date date){
        try{
            LOG.info("Checking for date in database : {}",date);
            Query query = manager.createQuery("FROM HolidaysListData d WHERE d.holidayDate=:holidayDate and d.status=:status");
            query.setParameter("holidayDate", date).setParameter("status",AppConstants.ACTIVE);
            List<HolidaysListData> data = query.getResultList();
            LOG.info("data returned is : {}", data.size());
            if(!data.isEmpty()) {
                return true;
            }
            else{
                return false;
            }
        }
        catch (Exception e){
            LOG.error("Exception caught while checking for date in database  :: ",e);
            return false;
        }
    }

    @Override
    public boolean addHoliday(HolidayDetails holidayDetails){
        Boolean check = checkForDate(holidayDetails.getHolidayDate(), holidayDetails.getHolidayType());
        if (check){
            HolidaysListData data = new HolidaysListData();
            data.setHolidayType(holidayDetails.getHolidayType());
            data.setHolidayDate(holidayDetails.getHolidayDate());
            data.setHolidayMonth(holidayDetails.getHolidayMonth());
            data.setHolidayYear(holidayDetails.getHolidayYear());
            data.setStatus(holidayDetails.getStatus());
            data.setCreatedBy(holidayDetails.getCreatedBy());
            data.setCreatedTime(AppUtils.getCurrentTimestamp());
            manager.persist(data);
            manager.flush();
            return true;
        }
        else {
            return false;
        }
    }

    private Boolean checkForDate(Date date, String holidayType){
        try {
            if (date != null) {
                LOG.info("Checking for date in database : {}",date);
                Query query = manager.createQuery("FROM HolidaysListData d WHERE d.holidayDate=:holidayDate and d.holidayType=:holidayType");
                query.setParameter("holidayDate", date).setParameter("holidayType", holidayType);
                List<HolidaysListData> data = query.getResultList();
                LOG.info("data returned is : {}", data.size());
                if (data != null && !data.isEmpty()) {
                    return false;
                }
                return true;
            }
            else return false;
        }
        catch (Exception e){
            LOG.error("Exception occured while while checking date in holidays : ",e);
            return false;
        }
    }

    @Override
    public List<HolidayDetails> listOfHolidays(String holidayType, Integer year){
        List<HolidayDetails> result = new ArrayList<>();
        try {
            StringBuilder queryToUse = new StringBuilder("FROM HolidaysListData d");
            if (holidayType != null && year != null) {
                queryToUse.append(" WHERE d.holidayType=:holidayType and d.holidayYear=:holidayYear");
            } else if (holidayType != null) {
                queryToUse.append(" WHERE d.holidayType=:holidayType");
            } else if (year != null) {
                queryToUse.append(" WHERE d.holidayYear=:holidayYear");
            }
            Query query = manager.createQuery(queryToUse.toString());
            if(holidayType != null && year != null){
                query.setParameter("holidayType", holidayType).setParameter("holidayYear", year);
            } else if (holidayType != null) {
                query.setParameter("holidayType", holidayType);
            } else if (year != null) {
                query.setParameter("holidayYear", year);
            }
            List<HolidaysListData> listOfHolidays = query.getResultList();
            if (listOfHolidays != null && !listOfHolidays.isEmpty()) {
                for (HolidaysListData data : listOfHolidays) {
                    HolidayDetails entry = new HolidayDetails();
                    entry.setHolidayListId(data.getHolidayListId());
                    entry.setHolidayType(data.getHolidayType());
                    entry.setHolidayDate(data.getHolidayDate());
                    entry.setStatus(data.getStatus());
                    entry.setHolidayYear(data.getHolidayYear());
                    result.add(entry);
                }
            }
            return result;
        }
        catch (Exception e){
            LOG.error("Exception occurred during fetching values ::: ",e);
            return result;
        }
    }

    @Override
    public SpecializedOrderInvoiceData findSpecializedOrderInvoiceByPrId(Integer prId){
        try{
            Query query = manager.createQuery("FROM SpecializedOrderInvoiceData inv where inv.prId = :prId");
            query.setParameter("prId", prId);
            return (SpecializedOrderInvoiceData) query.getSingleResult();
        }catch ( Exception e){
            LOG.info("Error While Getting Specialized Order Invoice For Pr ID : {} ::::::",prId,e);
            return null;
        }
    }

    @Override
    public List<PaymentRequestData> getSRPaymentRequests(Integer vendorId, Integer unitId, Integer prId, Integer grId,Integer srId,
                                                         String invoiceNumber, Date startDate, Date endDate, String status,
                                                         Integer companyId, Date paymentDate, String requestType) {
        try {
            StringBuilder queryString = new StringBuilder("");
                queryString.append("SELECT p,r  FROM ServiceReceivedData s INNER JOIN FETCH PaymentRequestData p  ON p.id= s.paymentRequestData.id INNER JOIN FETCH ServiceReceivedItemData r ON s.serviceReceivedId = r.serviceReceivedDataId WHERE s.serviceReceivedId= :srId and ");
            if (invoiceNumber != null) {
                queryString.append("p.invoiceNumber = :invoiceNumber ");
            }
            if (vendorId != null) {
                queryString.append("p.vendorId = :vendorId and ");
            }
            if (unitId != null) {
                queryString.append("r.businessCostCenterName = :businessCostCenterName and ");
            }
            if (status != null) {
                queryString.append("p.currentStatus = :status and ");
            }
            if (paymentDate != null) {
                queryString.append("p.paymentCalendarData.paymentDate = :paymentDate and ");
            }
            if(companyId !=null) {
                queryString.append("p.companyId = :companyId and ");
            }
            queryString.append("p.creationTime >= :startDate and p.creationTime <= :endDate ");

            Query query = manager.createQuery(queryString.toString());
            query.setParameter("srId",srId);
            if(invoiceNumber!=null){
               query.setParameter("invoiceNumber",invoiceNumber);
            }
            if (vendorId != null) {
                query.setParameter("vendorId", vendorId);
            }
            if(unitId != null) {
                query.setParameter("businessCostCenterName", masterDataCache.getUnit(unitId).getName());
            }
            if(companyId != null){
                query.setParameter("companyId",companyId);
            }
            if(status != null){
                query.setParameter("status",status);
            }
            if(paymentDate!=null){
                query.setParameter("paymentDate",paymentDate);
            }
            query.setParameter("startDate", startDate);
            query.setParameter("endDate", endDate);

            List<Object[]> list = query.getResultList();
            List<PaymentRequestData> resultant = new ArrayList<>();
            Map<Integer,PaymentRequestData> paymentRequestDataMap = new HashMap<>();
            for(Object[] obj : list){
                PaymentRequestData prd = (PaymentRequestData) obj[0];
                PaymentRequestData data = new PaymentRequestData();
                data.setAmountsMatch(prd.getAmountsMatch());
                data.setIsBlocked(prd.getIsBlocked());
                data.setBusinessCostCentreName(prd.getBusinessCostCentreName());
                data.setCompanyId(prd.getCompanyId());
                data.setCreatedBy(prd.getCreatedBy());
                data.setCreationTime(prd.getCreationTime());
                data.setCurrentStatus(prd.getCurrentStatus());
                data.setDeviationCount(prd.getDeviationCount());
                data.setGrDocType(prd.getGrDocType());
                data.setInvoiceNumber(prd.getInvoiceNumber());
                data.setLastUpdated(prd.getLastUpdated());
                data.setPaidAdhoc(prd.getPaidAdhoc());
                data.setPaidAmount(prd.getPaidAmount());
                data.setPaymentDate(prd.getPaymentDate());
                data.setId(prd.getId());
                data.setProposedAmount(prd.getProposedAmount());
                data.setVendorPaymentDate(prd.getVendorPaymentDate());
                data.setVendorId(prd.getVendorId());
                data.setType(prd.getType());
                ServiceReceivedItemData srData = (ServiceReceivedItemData) obj[1];
                data.setBusinessCostCentreName(srData.getBusinessCostCenterName());
                resultant.add(data);
            }
            for (PaymentRequestData data :resultant){
                if (paymentRequestDataMap.containsKey(data.getId())){
                    PaymentRequestData paymentRequestData = paymentRequestDataMap.get(data.getId());
                    LOG.info("{} {}",paymentRequestDataMap.get(data.getId()).getBusinessCostCentreName(),data.getBusinessCostCentreName());
                    String bcc = paymentRequestDataMap.get(data.getId()).getBusinessCostCentreName() + "," + data.getBusinessCostCentreName();
                    paymentRequestData.setBusinessCostCentreName(bcc);
                    paymentRequestDataMap.put(data.getId(),paymentRequestData);
                } else {
                    LOG.info("{}",data.getBusinessCostCentreName());
                    paymentRequestDataMap.put(data.getId(),data);
                }
            }
            return new ArrayList<>(paymentRequestDataMap.values());
        }catch (Exception e){
            LOG.info("Error While Getting Payment Request Data",e);
            return new ArrayList<>();
        }

    }



    @Override
    public List<Pair<String,String>> getMandatoryReqDoc(Integer prId) {
        PaymentRequestData paymentRequestData= manager.find(PaymentRequestData.class,prId);
        if (Objects.isNull(paymentRequestData.getMandatoryRequiredDocument())){
            return new ArrayList<>();
        }
        List<String> docIds = Arrays.asList(paymentRequestData.getMandatoryRequiredDocument().split(","));
        List<Integer> docId = docIds.stream().filter(e-> !e.isEmpty()).map(Integer::parseInt).collect(Collectors.toList());
        Query query = manager.createQuery("SELECT d FROM DocumentDetailData d where d.id IN :ids");
        query.setParameter("ids",docId);
        List<DocumentDetailData> list = query.getResultList();
        List<Pair<String,String>> result = new ArrayList<>();
        for (DocumentDetailData data : list) {
            String[] keyList = data.getDocumentLink().split("-");
            result.add(new Pair<>(keyList[0],data.getFileUrl()));
        }
        return result;
    }

    @Override
    public AdvancePaymentData getVendorAdvancePayment(Integer vendorId, List<String> statusList, String advanceType, Integer poId, Integer soId) {
        AdvancePaymentData advancePaymentData = null;
        try {
            StringBuilder stringBuilder = new StringBuilder("FROM AdvancePaymentData a WHERE a.vendorId =: vendorId AND a.advanceStatus IN(:statusList) and a.advanceType =:advanceType");
            if (Objects.nonNull(poId)) {
                stringBuilder.append(" and a.purchaseOrderData.id = :poId");
            }
            if (Objects.nonNull(soId)) {
                stringBuilder.append(" and a.serviceOrderData.id = :soId");
            }
            Query query = manager.createQuery(stringBuilder.toString());
            query.setParameter("vendorId", vendorId);
            query.setParameter("statusList", statusList);
            query.setParameter("advanceType", advanceType);
            if (Objects.nonNull(poId)) {
                query.setParameter("poId", poId);
            }
            if (Objects.nonNull(soId)) {
                query.setParameter("soId", soId);
            }
            advancePaymentData = (AdvancePaymentData) query.getSingleResult();
        } catch (NoResultException e) {
            LOG.info("No entries found for Advance payment of Vendor Id : {}",vendorId);
        }
        catch (Exception e) {
            LOG.error("Exception Occurred while getting the advance payment of Vendor Id :: {} :: ",vendorId,e);
        }
        return advancePaymentData;
    }

    //Revert when we start the Use of STAND_ALONE_ADVANCE
    @Override
    public AdvancePaymentData getAdvancePaymentByPr(Integer prId) {
        AdvancePaymentData advancePaymentData = null;
        try {
            Query query = manager.createQuery("FROM AdvancePaymentData a WHERE a.paymentRequestId.id =: prId AND a.advanceStatus NOT IN(:statusList)");
            query.setParameter("prId", prId);
            query.setParameter("statusList", Arrays.asList(AdvancePaymentStatus.REJECTED.value(), AdvancePaymentStatus.CANCELLED.value()));
            advancePaymentData = (AdvancePaymentData) query.getSingleResult();
        } catch (NoResultException e) {
            LOG.info("No entries found for Advance payment of PR Id : {}",prId);
        }
        catch (Exception e) {
            LOG.error("Exception Occurred while getting the advance payment of PR Id :: {} :: ",prId,e);
        }
        return advancePaymentData;
    }

    @Override
    public List<AdvancePaymentData> getAdvancePaymentByAdvanceIds(List<Integer> advancePaymentIds) {
        Query query = manager.createQuery("FROM AdvancePaymentData a WHERE a.advancePaymentId IN(:advancePaymentIds) and a.availableAmount > 0 and a.advanceStatus =:advanceStatus ORDER BY a.advancePaymentId");
        query.setParameter("advancePaymentIds", advancePaymentIds);
        query.setParameter("advanceStatus", AdvancePaymentStatus.CREATED.value());
        return query.getResultList();
    }

    @Override
    public List<AdvancePaymentData> getAllVendorAdvances(Date startDate, Date endDate, Integer vendorId, List<String> statusList, List<Integer> advancePaymentIds) {
        if (Objects.isNull(advancePaymentIds)) {
            Query query = manager.createQuery("FROM AdvancePaymentData a WHERE a.vendorId =: vendorId and a.advanceStatus IN(:statusList) and a.createdAt >=: startDate and a.createdAt <=: endDate");
            query.setParameter("vendorId", vendorId);
            query.setParameter("statusList", statusList);
            query.setParameter("startDate", startDate);
            query.setParameter("endDate", endDate);
            return (List<AdvancePaymentData>) query.getResultList();
        } else {
            Query query = manager.createQuery("FROM AdvancePaymentData a WHERE a.advancePaymentId IN(:advancePaymentIds)");
            query.setParameter("advancePaymentIds", advancePaymentIds);
            return (List<AdvancePaymentData>) query.getResultList();
        }
    }

    @Override
    public AdvancePaymentData getVendorAdjustedAdvance(AdvancePaymentData vendorAdvancePayment) {
        StringBuilder stringBuilder = new StringBuilder("FROM AdvancePaymentData a WHERE a.advanceType =:advanceType");
        if (vendorAdvancePayment.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
            stringBuilder.append(" and a.purchaseOrderData.id =:poId");
        }
        if (vendorAdvancePayment.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.SO_ADVANCE)) {
            stringBuilder.append(" and a.serviceOrderData.id =:soId");
        }
        Query query = manager.createQuery(stringBuilder.toString());
        query.setParameter("advanceType", vendorAdvancePayment.getAdvanceType());
        if (vendorAdvancePayment.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
            query.setParameter("poId", vendorAdvancePayment.getAdjustedPoSo());
        }
        if (vendorAdvancePayment.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.SO_ADVANCE)) {
            query.setParameter("soId", vendorAdvancePayment.getAdjustedPoSo());
        }
        return (AdvancePaymentData) query.getResultList().get(0);
    }

    @Override
    public List<AdvancePaymentData> getAllParentAdvances(AdvancePaymentData advancePaymentData) {
        Query query = manager.createQuery("FROM AdvancePaymentData a WHERE a.childAdvance.advancePaymentId =:advancePaymentId");
        query.setParameter("advancePaymentId", advancePaymentData.getChildAdvance().getAdvancePaymentId());
        return (List<AdvancePaymentData>) query.getResultList();
    }

    @Override
    public List<AdvancePaymentAuditLogData> getAdvancePaymentAuditLog(PaymentRequestData paymentRequestData) {
        Query query = manager.createQuery("FROM AdvancePaymentAuditLogData a WHERE a.prId =:prId");
        query.setParameter("prId", paymentRequestData.getId());
        return (List<AdvancePaymentAuditLogData>) query.getResultList();
    }

    @Override
    public List<AdvancePaymentData> getAllAdvancesByAdvanceStatus(AdvancePaymentData advancePaymentData, String advanceStatus) {
        StringBuilder stringBuilder = new StringBuilder("FROM AdvancePaymentData a WHERE a.advanceType =:advanceType");
        if (advancePaymentData.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
            stringBuilder.append(" and a.purchaseOrderData.id =:poId");
        }
        if (advancePaymentData.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.SO_ADVANCE)) {
            stringBuilder.append(" and a.serviceOrderData.id =:soId");
        }
        stringBuilder.append(" and a.advanceStatus =:advanceStatus");
        Query query = manager.createQuery(stringBuilder.toString());
        query.setParameter("advanceType", advancePaymentData.getAdvanceType());
        if (advancePaymentData.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
            query.setParameter("poId", advancePaymentData.getPurchaseOrderData().getId());
        }
        if (advancePaymentData.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.SO_ADVANCE)) {
            query.setParameter("soId", advancePaymentData.getServiceOrderData().getId());
        }
        query.setParameter("advanceStatus",advanceStatus);
        return (List<AdvancePaymentData>) query.getResultList();
    }

    @Override
    public List<AdvancePaymentData> getNonRefundAdvancesForBlocking(Date currentDate, List<String> nonRefundStatusList, Integer vendorId) {
        StringBuilder stringBuilder = new StringBuilder("FROM AdvancePaymentData a WHERE a.advanceStatus IN(:statusList) and a.maxSettlementTime <:currentDate");
        if (Objects.nonNull(vendorId)) {
            stringBuilder.append(" and a.vendorId =:vendorId");
        }
        Query query = manager.createQuery(stringBuilder.toString());
        query.setParameter("statusList", nonRefundStatusList);
        query.setParameter("currentDate", currentDate);
        if (Objects.nonNull(vendorId)) {
            query.setParameter("vendorId", vendorId);
        }
        return (List<AdvancePaymentData>) query.getResultList();
    }

    @Override
    public List<AdvancePaymentData> getRefundProcessedVendorAdvance() {
        Query query = manager.createQuery("FROM AdvancePaymentData a WHERE a.advanceStatus IN(:statusList) and a.refundDate <:currentDate");
        query.setParameter("statusList", Arrays.asList(AdvancePaymentStatus.REFUND_INITIATED.value(),AdvancePaymentStatus.REFUND_APPROVED.value()));
        query.setParameter("currentDate", AppUtils.getCurrentTimestamp());
        return (List<AdvancePaymentData>) query.getResultList();
    }

    @Override
    public List<VendorDetailData> getBlockedVendorsDueToAdvance() {
        Query query = manager.createQuery("FROM VendorDetailData a WHERE a.vendorBlocked =:vendorBlocked");
        query.setParameter("vendorBlocked", AppConstants.YES);
        return (List<VendorDetailData>) query.getResultList();
    }

    @Override
    public List<AdvancePaymentData> getAllRunningAdvances() {
        Query query = manager.createQuery("FROM AdvancePaymentData a WHERE a.advanceStatus IN(:statusList)");
        query.setParameter("statusList", Arrays.asList(AdvancePaymentStatus.INITIATED.value(),AdvancePaymentStatus.CREATED.value()));
        return (List<AdvancePaymentData>) query.getResultList();
    }

    @Override
    public List<PaymentRequestData> getQueriedPrsToReject() {
        Query query = manager.createQuery("FROM PaymentRequestData a WHERE a.currentStatus =:status and a.vendorPaymentDate <:currentDate");
        query.setParameter("status", PaymentRequestStatus.QUERIED.value());
        query.setParameter("currentDate", AppUtils.getCurrentDate());
        return (List<PaymentRequestData>) query.getResultList();
    }

    @Override
    public List<String> getEmployeePaymentCards(Integer userId) {
        Query query = manager.createQuery("SELECT a.paymentCard FROM EmployeePaymentCardMapping a WHERE a.employeeId =:userId and a.mappingStatus =:mappingStatus");
        query.setParameter("userId", userId);
        query.setParameter("mappingStatus", AppConstants.ACTIVE);
        return (List<String>) query.getResultList();
    }

    @Override
    public List<PaymentRequestData> getPaymentRequestsWithPrIds(List<Integer> paymentRequestIds) {
        Query query = manager.createQuery("FROM PaymentRequestData a WHERE a.id IN(:paymentRequestIds)");
        query.setParameter("paymentRequestIds", paymentRequestIds);
        return (List<PaymentRequestData>) query.getResultList();
    }

    @Override
    public List<String> getAllBudgetCategory(){
        Query query = manager.createQuery("SELECT DISTINCT(budgetCategory) FROM ListTypeData group by  budgetCategory ");
        return (List<String>) query.getResultList();
    }

    @Override
    public PaymentRequestMetaData getPaymentRequestMetaDataForPr(PaymentRequestData paymentRequestData) {
        try {
            Query query = manager.createQuery("FROM PaymentRequestMetaData where paymentRequest =:pr ");
            query.setParameter("pr", paymentRequestData);
            return (PaymentRequestMetaData) query.getSingleResult();
        } catch (NoResultException ex) {
            LOG.error("######## No pr metadata found for pr id : {}", paymentRequestData.getId());
            return null;
        } catch (Exception e) {
            LOG.error("######## Error while getting payment request metadata for pr id : {}", paymentRequestData.getId());
            throw e;
        }
    }
    @Override
    public void removePaymentInvoiceTaxItem(Integer prInvoiceItemId){
         try{
             Query query = manager.createQuery("FROM PaymentInvoiceItemData where id =:id ");
             query.setParameter("id",prInvoiceItemId);
             List<PaymentInvoiceItemData> items =  query.getResultList();
             if(items.size() >0){
                 query = manager.createQuery("FROM PaymentInvoiceItemTaxData where paymentInvoiceItemData in (:items)");
                 query.setParameter("items",items);
                 List<PaymentInvoiceItemTaxData> taxItems = query.getResultList();
                 for(PaymentInvoiceItemTaxData taxData : taxItems){
                     delete(taxData);
                 }
             }
         }catch (Exception e){
             LOG.error("######## Error while remove payment invoice tax item for prInvoiceItemId : {}",prInvoiceItemId);
             throw e;
         }
    }

    @Override
    public List<AdvancePaymentData> getAllAdvancePaymentByAdvanceIds(List<Integer> advancePaymentIds) {
        Query query = manager.createQuery("FROM AdvancePaymentData a WHERE a.advancePaymentId IN(:advancePaymentIds) and a.advanceStatus =:advanceStatus ORDER BY a.advancePaymentId");
        query.setParameter("advancePaymentIds", advancePaymentIds);
        query.setParameter("advanceStatus", AdvancePaymentStatus.CREATED.value());
        return query.getResultList();
    }

}

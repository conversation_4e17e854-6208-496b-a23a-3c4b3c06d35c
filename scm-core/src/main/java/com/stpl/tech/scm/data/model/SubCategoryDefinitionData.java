package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * Created by <PERSON><PERSON> on 11-06-2016.
 */
@Entity
@Table(name = "SUB_CATEGORY_DEFINITION")
public class SubCategoryDefinitionData {

    private int id;
    private String name;
    private String code;
    private String description;
    private String status;
    private Integer lifeTimeCategoryMonths;
    private Integer shelfLifeInDays;
    private String shelfLifeRange;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "SUB_CATEGORY_ID", nullable = false, unique = true)
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Column(name = "SUB_CATEGORY_NAME", nullable = false, unique = true)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Column(name = "SUB_CATEGORY_CODE", nullable = false, unique = true)
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "SUB_CATEGORY_DESCRIPTION", nullable = true)
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Column(name = "SUB_CATEGORY_STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "LIFE_TIME_CATEGORY_IN_MONTHS")
    public Integer getLifeTimeCategoryMonths() {
        return lifeTimeCategoryMonths;
    }

    public void setLifeTimeCategoryMonths(Integer lifeTimeCategoryMonths) {
        this.lifeTimeCategoryMonths = lifeTimeCategoryMonths;
    }

    @Column(name = "SHELF_LIFE_IN_DAYS")
    public Integer getShelfLifeInDays() {
        return shelfLifeInDays;
    }

    public void setShelfLifeInDays(Integer shelfLifeInDays) {
        this.shelfLifeInDays = shelfLifeInDays;
    }

    @Column(name = "SHELF_LIFE_RANGE")
    public String getShelfLifeRange() {
        return shelfLifeRange;
    }

    public void setShelfLifeRange(String shelfLifeRange) {
        this.shelfLifeRange = shelfLifeRange;
    }
}

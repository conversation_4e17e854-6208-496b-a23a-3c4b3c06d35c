package com.stpl.tech.scm.data.transport.model;

public class WarehouseStats {
    public int count = 0, totaDelayRoRaised = 0;
    public double sumF = 0.0, onTimeFTotal = 0.0, onDateFTotal = 0.0;
    public double totalOnTimeRoRaisedPer = 0.0, totalOnDateRoRaisedPer = 0.0, totalDelayRoRaisedPer = 0.0;
    public double sumImF = 0.0;

    // critical stats
    public int criticalCount = 0;
    public double criticalProductQty = 0.0, criticalSum = 0.0;
    public double criticalProductFF = 0.0, criticalProductOnTimeFF = 0.0, criticalProductOnDateFF = 0.0;
    public double criticalProdRoRaisedOnTimeFF = 0.0, criticalProdRoRaisedOnDateFF = 0.0, criticalProdRoRaisedDelayFF = 0.0;

    //Bakery stats
    public int bakeryCount = 0;
    public double bakerySum = 0.0, bakeryOnTimeFFSum = 0.0, bakeryOnDateFFSum = 0.0;
    public double bakeryRoRaisedOnTime = 0.0, bakeryRoRaisedOnDate = 0.0, bakeryRoDelayRaised = 0.0;

    // product LEVEL_1
    public double productLevel1FF = 0.0, productLevel1OnTimeFF = 0.0, productLevel1OnDateFF = 0.0, productLevel1Qty = 0.0,
            productLevel1RaisedOnTime = 0.0, productLevel1RaisedOnDate = 0.0, productLevel1DelayRaised = 0.0;

    // product LEVEL_2
    public double productLevel2FF = 0.0, productLevel2OnTimeFF = 0.0, productLevel2OnDateFF = 0.0, productLevel2Qty = 0.0,
            productLevel2RaisedOnTime = 0.0, productLevel2RaisedOnDate = 0.0, productLevel2DelayRaised = 0.0;

    // product LEVEL_3
    public double productLevel3FF = 0.0, productLevel3OnTimeFF = 0.0, productLevel3OnDateFF = 0.0, productLevel3Qty = 0.0,
            productLevel3RaisedOnTime = 0.0, productLevel3RaisedOnDate = 0.0, productLevel3DelayRaised = 0.0;

    // product LEVEL_4
    public double productLevel4FF = 0.0, productLevel4OnTimeFF = 0.0, productLevel4OnDateFF = 0.0, productLevel4Qty = 0.0,
            productLevel4RaisedOnTime = 0.0, productLevel4RaisedOnDate = 0.0, productLevel4DelayRaised = 0.0;

    // product LEVEL_5
    public double productLevel5FF = 0.0, productLevel5OnTimeFF = 0.0, productLevel5OnDateFF = 0.0, productLevel5Qty = 0.0,
            productLevel5RaisedOnTime = 0.0, productLevel5RaisedOnDate = 0.0, productLevel5DelayRaised = 0.0;

    // LEVEL isn't defined on products
    public double productLevelNAFF = 0.0, productLevelNAOnTimeFF = 0.0, productLevelNAOnDateFF = 0.0,
            productLevelNAQty = 0.0, productLevelNARORaisedOnTime = 0.0, productLevelNARORaisedOnDate = 0.0, productLevelNADelayedRo = 0.0;

    public int productLevel1Count = 0, productLevel2Count = 0, productLevel3Count = 0,
            productLevel4Count = 0, productLevel5Count = 0, productLevelNACount = 0;


}
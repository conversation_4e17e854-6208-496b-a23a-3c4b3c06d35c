package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.data.model.VendorContractItemData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface VendorContractItemDataDao extends JpaRepository<VendorContractItemData,Integer> {
    List<VendorContractItemData> findByVendorIdAndStatus(Integer vendorId, String status);

    VendorContractItemData findByVendorIdAndSkuIdAndSkuPackagingIdAndDispatchLocationAndDeliveryLocation(Integer vendorId, Integer skuId, Integer skuPackagingId, String dispatch, String delivery);

    @Query("SELECT COUNT(*), SUM(CASE WHEN STATUS=?2 THEN 1 ELSE 0 END)" +
            " FROM VendorContractItemData WHERE workOrderData.workOrderId=?1")
    List<Object[]> getTotalRemovedOfContractId(Integer workOrderId, String removed);

    @Query(value = """
        SELECT DISTINCT vci.* FROM VENDOR_CONTRACT_ITEM_DATA vci
        WHERE CONCAT(vci.SKU_ID, '-', vci.SKU_PACKAGING_ID, '-', vci.DISPATCH_LOCATION_ID, '-', vci.DELIVERY_LOCATION_ID)
        IN (:compositeKeys) AND vci.VENDOR_ID = :vendorId
        ORDER BY vci.CONTRACT_ITEM_ID DESC
        """, nativeQuery = true)
    List<VendorContractItemData> findItemsByCompositeKeys(@Param("compositeKeys") List<String> compositeKeys, @Param("vendorId")Integer vendorId);
}

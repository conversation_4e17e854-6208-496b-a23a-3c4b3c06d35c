package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 10-06-2016.
 */
@Entity
@Table(name = "REQUEST_ORDER_ITEM")
public class RequestOrderItemData {

    private Integer id;
    private int productId;
    private String productName;
    private BigDecimal requestedQuantity;
    private BigDecimal requestedAbsoluteQuantity;
    private BigDecimal transferredQuantity;
    private BigDecimal receivedQuantity;
    private String unitOfMeasure;
    private BigDecimal unitPrice;
    private BigDecimal negotiatedUnitPrice;
    private BigDecimal calculatedAmount;
    private RequestOrderData requestOrderData;
    private Integer vendorId;
    private String taxCode;
    private BigDecimal taxAmount;
    private List<RequestOrderItemTaxDetail> orderItemTaxes = new ArrayList<>();
    private BigDecimal excessQuantity;
    private BigDecimal originalQuantity;
    private Date expiryDate;
    private String productionBookingCompleted;
    private BigDecimal productionBookingQuantity;
    private BigDecimal adjustedQuantity;
    private String adjustedReason;
    private String reason;
    private String comment;
    private Integer packagingId;
    private List<MultiPackagingAdjustmentsData> packagingAdjustmentsData = new ArrayList<>(0);
    private BigDecimal predictedQuantity;
    private BigDecimal suggestedQuantity;
    private BigDecimal diffQuantity;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "REQUEST_ORDER_ITEM_ID", nullable = false, unique = true)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "PRODUCT_ID", nullable = false)
    public int getProductId() {
        return productId;
    }

    public void setProductId(int productId) {
        this.productId = productId;
    }

    @Column(name = "PRODUCT_NAME", nullable = false)
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @Column(name = "REQUESTED_QUANTITY", nullable = false)
    public BigDecimal getRequestedQuantity() {
        return requestedQuantity;
    }

    public void setRequestedQuantity(BigDecimal requestedQuantity) {
        this.requestedQuantity = requestedQuantity;
    }

    @Column(name = "REQUESTED_ABSOLUTE_QUANTITY", nullable = false)
    public BigDecimal getRequestedAbsoluteQuantity() {
        return requestedAbsoluteQuantity;
    }

    public void setRequestedAbsoluteQuantity(BigDecimal requestedAbsoluteQuantity) {
        this.requestedAbsoluteQuantity = requestedAbsoluteQuantity;
    }

    @Column(name = "TRANSFERRED_QUANTITY", nullable = true)
    public BigDecimal getTransferredQuantity() {
        return transferredQuantity;
    }

    public void setTransferredQuantity(BigDecimal transferredQuantity) {
        this.transferredQuantity = transferredQuantity;
    }

    @Column(name = "RECEIVED_QUANTITY", nullable = true)
    public BigDecimal getReceivedQuantity() {
        return receivedQuantity;
    }

    public void setReceivedQuantity(BigDecimal receivedQuantity) {
        this.receivedQuantity = receivedQuantity;
    }

    @Column(name = "UNIT_OF_MEASURE", nullable = true, length = 1000)
    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "REQUEST_ORDER_ID", nullable = false)
    public RequestOrderData getRequestOrderData() {
        return requestOrderData;
    }

    public void setRequestOrderData(RequestOrderData requestOrderData) {
        this.requestOrderData = requestOrderData;
    }

    @Column(name = "VENDOR_ID", nullable = true)
    public Integer getVendorId() {
        return vendorId;
    }

    public void setVendorId(Integer vendorId) {
        this.vendorId = vendorId;
    }

    @Column(name = "UNIT_PRICE", nullable = true)
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    @Column(name = "NEGOTIATED_UNIT_PRICE", nullable = true)
    public BigDecimal getNegotiatedUnitPrice() {
        return negotiatedUnitPrice;
    }

    public void setNegotiatedUnitPrice(BigDecimal negotiatedUnitPrice) {
        this.negotiatedUnitPrice = negotiatedUnitPrice;
    }

    @Column(name = "CALCULATED_AMOUNT", nullable = true)
    public BigDecimal getCalculatedAmount() {
        return calculatedAmount;
    }

    public void setCalculatedAmount(BigDecimal calculatedAmount) {
        this.calculatedAmount = calculatedAmount;
    }

    @Column(name = "TAX_CODE", nullable = true, length = 40)
    public String getTaxCode() {
        return taxCode;
    }

    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode;
    }

    @Column(name = "TOTAL_TAX", precision = 10)
    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }


    @OneToMany(fetch = FetchType.LAZY, mappedBy = "orderItem")
    public List<RequestOrderItemTaxDetail> getOrderItemTaxes() {
        return orderItemTaxes;
    }

    @Column(name = "EXCESS_QUANTITY", nullable = false)
    public BigDecimal getExcessQuantity() {
        return excessQuantity;
    }

    public void setExcessQuantity(BigDecimal excessQuantity) {
        this.excessQuantity = excessQuantity;
    }

    public void setOrderItemTaxes(List<RequestOrderItemTaxDetail> orderItemTaxes) {
        this.orderItemTaxes = orderItemTaxes;

    }

    @Column(name = "ORIGINAL_QUANTITY", nullable = false)
    public BigDecimal getOriginalQuantity() {
        return originalQuantity;
    }

    public void setOriginalQuantity(BigDecimal originalQuantity) {
        this.originalQuantity = originalQuantity;
    }

    @Column(name = "EXPIRY_DATE", nullable = true)
    public Date getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    @Column(name="PRODUCTION_BOOKING_COMPETED",nullable = true)
    public String getProductionBookingCompleted() {
        return productionBookingCompleted;
    }

    public void setProductionBookingCompleted(String productionBookingCompleted) {
        this.productionBookingCompleted = productionBookingCompleted;
    }

    @Column(name = "PRODUCTION_BOOKING_QUANTITY",nullable = true)
    public BigDecimal getProductionBookingQuantity() {
        return productionBookingQuantity;
    }

    public void setProductionBookingQuantity(BigDecimal productionBookingQuantity) {
        this.productionBookingQuantity = productionBookingQuantity;
    }

    @Column(name = "ADJUSTED_QUANTITY", nullable = true)
    public BigDecimal getAdjustedQuantity() {
        return adjustedQuantity;
    }

    public void setAdjustedQuantity(BigDecimal adjustedQuantity) {
        this.adjustedQuantity = adjustedQuantity;
    }

    @Column(name = "ADJUSTED_REASON", nullable = true)
    public String getAdjustedReason() {
        return adjustedReason;
    }

    public void setAdjustedReason(String adjustedReason) {
        this.adjustedReason = adjustedReason;
    }

    @Column(name = "REASON", nullable = true)
    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    @Column(name = "COMMENT", nullable = true)
    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    @Column(name = "PACKAGING_ID", nullable = true)
    public Integer getPackagingId() {
        return packagingId;
    }

    public void setPackagingId(Integer packagingId) {
        this.packagingId = packagingId;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "requestOrderItemData")
    public List<MultiPackagingAdjustmentsData> getPackagingAdjustmentsData() {
        return packagingAdjustmentsData;
    }

    public void setPackagingAdjustmentsData(List<MultiPackagingAdjustmentsData> packagingAdjustmentsData) {
        this.packagingAdjustmentsData = packagingAdjustmentsData;
    }

    @Transient
    public BigDecimal getPredictedQuantity() {
        return predictedQuantity;
    }

    public void setPredictedQuantity(BigDecimal predictedQuantity) {
        this.predictedQuantity = predictedQuantity;
    }

    @Transient
    public BigDecimal getSuggestedQuantity() {
        return suggestedQuantity;
    }

    public void setSuggestedQuantity(BigDecimal suggestedQuantity) {
        this.suggestedQuantity = suggestedQuantity;
    }

    @Transient
    public BigDecimal getDiffQuantity() {
        return diffQuantity;
    }

    public void setDiffQuantity(BigDecimal diffQuantity) {
        this.diffQuantity = diffQuantity;
    }
}

/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.reports;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import com.stpl.tech.kettle.report.metadata.model.ReportData;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.StockTakeException;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.dao.StockManagementDao;
import com.stpl.tech.scm.data.model.DayCloseEventLogData;
import com.stpl.tech.scm.data.model.SCMDayCloseEventData;
import com.stpl.tech.scm.data.model.WriteOffItemData;
import com.stpl.tech.scm.domain.model.DayCloseEventLogType;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.UnitDetail;
import com.stpl.tech.scm.reports.modal.VarianceModal;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.EnvType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.subtlelib.poi.api.sheet.SheetContext;
import org.subtlelib.poi.api.style.Style;
import org.subtlelib.poi.api.workbook.WorkbookContext;
import org.subtlelib.poi.impl.workbook.WorkbookContextFactory;

public class WareHouseVarianceReport extends ReportData implements VarianceReport {
	private final static Logger LOG = LoggerFactory.getLogger(CafeVarianceReport.class);
    private final SCMCache scmCache;
    private WorkbookContext workbookCtx;
	private String unitName;
	private String filePath;
	private Date businessDate;
	private EnvType envType;
	private String toEmail;
	private boolean generated = false;
	private String fileName;
	private String mimeType = AppConstants.EXCEL_MIME_TYPE;
	private SCMDayCloseEventData eventData;
	private StockManagementDao stockManagementDao;

	public WareHouseVarianceReport(WorkbookContextFactory ctxFactory, StockManagementDao stockManagementDao,
                                   String basePath, EnvType envType, SCMDayCloseEventData eventData, SCMCache scmCache) {
        UnitDetail unit = scmCache.getUnitDetail(eventData.getUnitId());
        this.eventData = eventData;
		this.unitName = unit.getUnitName();
		this.filePath = basePath + "variance_reports" + File.separator;
		this.fileName = "Variance_Report_ " + this.unitName + SCMUtil.getCurrentTimeISTStringWithNoColons() + ".xls";
		this.workbookCtx = ctxFactory.createWorkbook();
		this.stockManagementDao = stockManagementDao;
		this.toEmail = unit.getUnitEmail();
		this.envType = envType;
		this.businessDate = eventData.getBusinessDate();
		this.eventData = eventData;
		this.scmCache = scmCache;
	}


	@Override
	public void renderVariance() throws StockTakeException {

		List<VarianceModal> data = stockManagementDao.getWareHouseVarianceStock(eventData);
		List<Integer> skuList = data.stream().mapToInt(value -> value.getId()).boxed().collect(Collectors.toList());
		Map<Integer,String> closingDates = stockManagementDao.getSkuClosingDates(eventData.getUnitId(),skuList);

		// sort in descending order based on variance cost
		data.sort((a, b) -> SCMUtil.convertToBigDecimal(b.getVarianceCost())
				.compareTo(SCMUtil.convertToBigDecimal(a.getVarianceCost())));

		SheetContext sheetCtx = workbookCtx.createSheet("Variance Report");

		Style headerStyle = SCMUtil.getHeaderStyle(workbookCtx);



		sheetCtx.nextRow().mergeCells(10).text(this.unitName); // heading
		sheetCtx.nextRow().setTextStyle(headerStyle).text("SKU ID").text("SKU Name").setColumnWidth(SCMUtil.NAME_COLUMN_WIDTH)
				.text("Unit Of Measure").text("Last Closed").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Category").setColumnWidth(SCMUtil.COLUMN_WIDTH)
				.text("Sub Category").setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Opening Stock")
				.setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Bookings")
				.setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Reverse Bookings").
				setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Transferred").setColumnWidth(SCMUtil.COLUMN_WIDTH)
				.text("Received").setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Wastage")
				.setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Consumption")
				.setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Reverse Consumption")
				.setColumnWidth(SCMUtil.COLUMN_WIDTH)
				.text("Closing Stock").setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Variance")
				.setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Unit Cost").setColumnWidth(SCMUtil.COLUMN_WIDTH)
				.text("Value").setColumnWidth(SCMUtil.COLUMN_WIDTH);

		for (VarianceModal stockForUnit : data) {
		    String closingDate = closingDates!=null && closingDates.get(stockForUnit.getId()) != null
                                    ? closingDates.get(stockForUnit.getId()) : "N/A";
            sheetCtx.nextRow().number(stockForUnit.getId()).text(stockForUnit.getName()).text(stockForUnit.getUom())
                    .text(closingDate).text(stockForUnit.getCategory()).text(stockForUnit.getSubCategory())
					.number(stockForUnit.getOpeningStock() != null ? stockForUnit.getOpeningStock() : BigDecimal.ZERO)
                    .number(stockForUnit.getBooking() != null ? stockForUnit.getBooking() : BigDecimal.ZERO)
                    .number(stockForUnit.getReverseBooking() != null ? stockForUnit.getReverseBooking() : BigDecimal.ZERO)
					.number(stockForUnit.getTransferred() != null ? stockForUnit.getTransferred() : BigDecimal.ZERO)
					.number(stockForUnit.getReceived() != null ? stockForUnit.getReceived() : BigDecimal.ZERO)
					.number(stockForUnit.getWasted() != null ? stockForUnit.getWasted() : BigDecimal.ZERO)
					.number(stockForUnit.getConsumption() != null ? stockForUnit.getConsumption() : BigDecimal.ZERO)
					.number(stockForUnit.getReverseConsumption() != null ? stockForUnit.getReverseConsumption() : BigDecimal.ZERO)
					.number(stockForUnit.getClosingStock() != null ? stockForUnit.getClosingStock() : BigDecimal.ZERO)
					.number(stockForUnit.getVariance() != null ? stockForUnit.getVariance() : BigDecimal.ZERO)
					.number(stockForUnit.getUnitCost() != null ? stockForUnit.getUnitCost() : BigDecimal.ZERO)
					.number(stockForUnit.getVarianceCost() != null ? stockForUnit.getVarianceCost() : BigDecimal.ZERO);
		}

	}

    @Override
    public void renderNegativeWriteOff() {
        List<WriteOffItemData> data = getNegativeWriteOffList(eventData);
		Style  headerStyle = SCMUtil.getHeaderStyle(workbookCtx);

        if(data!=null && !data.isEmpty()){
            data.sort(Comparator.comparing(WriteOffItemData::getSkuId));
            SheetContext sheetCtx = workbookCtx.createSheet("Negative Write Off Report");

            sheetCtx.nextRow().mergeCells(10).text(this.unitName); // heading
            sheetCtx.nextRow().setTextStyle(headerStyle).text("SKU ID").text("SKU Name").setColumnWidth(SCMUtil.NAME_COLUMN_WIDTH)
                    .text("Unit Of Measure").text("Category").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                    .text("Sub Category").setColumnWidth(SCMUtil.COLUMN_WIDTH).text("System Stock")
                    .setColumnWidth(SCMUtil.COLUMN_WIDTH).text("Corrected Stock").setColumnWidth(SCMUtil.COLUMN_WIDTH);

            for (WriteOffItemData stockForUnit : data) {
                SkuDefinition sku = scmCache.getSkuDefinition(stockForUnit.getSkuId());
                ProductDefinition product = scmCache.getProductDefinition(sku.getLinkedProduct().getId());
                sheetCtx.nextRow()
                        .number(stockForUnit.getSkuId())
                        .text(sku.getSkuName())
                        .text(sku.getUnitOfMeasure())
                        .text(product.getCategoryDefinition().getName())
                        .text(product.getSubCategoryDefinition().getName())
                        .number(SCMUtil.convertToBigDecimal(stockForUnit.getExpectedValue()))
                        .number(SCMUtil.convertToBigDecimal(stockForUnit.getCorrectedValue()));
            }
        }

    }

    private List<WriteOffItemData> getNegativeWriteOffList(SCMDayCloseEventData eventData) {
        Optional<DayCloseEventLogData> correctionLog = eventData.getEventLogDataList().stream()
                .filter(data -> data.getEventCompleted().equals(DayCloseEventLogType.CORRECTION.name()))
                .findAny();

        if(correctionLog.isPresent()){
            return correctionLog.get().getWriteOffItemDatas();
        }
	    return null;
    }

    @Override
	public void renderWastage() {
	}

	@Override
	public void renderUnsettledTO() {
	}

	@Override
	public boolean renderSummaryView() {
		return true;
	}

	@Override
	public void generateReport(String filePath, byte[] content) throws IOException {
		String writtenPath = SCMUtil.write(content, filePath, this.unitName, "varianceReport", this.fileName, LOG);
		if (writtenPath != null) {
			this.filePath = writtenPath;
			this.generated = true;
		}
	}

	@Override
	public String getFilePath() {
		return this.filePath;
	}

	@Override
	public Date getBusinessDate() {
		return this.businessDate;
	}

	@Override
	public EnvType getEnv() {
		return this.envType;
	}

	@Override
	public String getEmailId() {
		return this.toEmail;
	}

	@Override
	public String getUnitName() {
		return this.unitName;
	}

	@Override
	public boolean isGenerated() {
		return this.generated;
	}

	@Override
	public String getFileName() {
		return this.fileName;
	}

	@Override
	public String getMimeType() {
		return this.mimeType;
	}

	@Override
	public WorkbookContext getWorkbook() {
		return workbookCtx;
	}

}

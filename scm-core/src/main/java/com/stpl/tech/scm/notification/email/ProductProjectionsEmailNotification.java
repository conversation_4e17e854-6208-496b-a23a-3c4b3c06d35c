package com.stpl.tech.scm.notification.email;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.notification.email.template.ProductProjectionsEmailNotificationTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

public class ProductProjectionsEmailNotification extends EmailNotification {
    private ProductProjectionsEmailNotificationTemplate productProjectionsEmailNotificationTemplate;
    private EnvType envType;
    private String[] emails;
    private String subjectOfEmail;

    public ProductProjectionsEmailNotification(){

    }

    public ProductProjectionsEmailNotification(ProductProjectionsEmailNotificationTemplate productProjectionsEmailNotificationTemplate, EnvType envType, String[] emails) {
        this.productProjectionsEmailNotificationTemplate = productProjectionsEmailNotificationTemplate;
        this.envType = envType;
        this.emails = emails;
    }

    @Override
    public String[] getToEmails() {
            return new String[] { "<EMAIL>" };
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        subjectOfEmail = "SCM Product Projections";
        if (SCMUtil.isDev(envType)) {
            subjectOfEmail = " [DEV] : " + subjectOfEmail;
        }
        return subjectOfEmail;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return productProjectionsEmailNotificationTemplate.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}

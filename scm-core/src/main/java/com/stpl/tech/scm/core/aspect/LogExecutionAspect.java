package com.stpl.tech.scm.core.aspect;

import com.stpl.tech.scm.core.util.SCMUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.sun.management.OperatingSystemMXBean;

import java.lang.management.ManagementFactory;
import java.text.DecimalFormat;

/**
 * Created by shikhar on 7/6/19.
 */


@Aspect
@Component
public class LogExecutionAspect {
    private static final Logger LOG = LoggerFactory.getLogger(LogExecutionAspect.class);

    @Around("@annotation(com.stpl.tech.scm.core.annotation.LogExecutionTime)")
    public Object logExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
        long start = System.currentTimeMillis();
        Object proceed = joinPoint.proceed();
        logSystemHealth();
        long executionTime = (System.currentTimeMillis() - start)/1000;
        System.out.println(joinPoint.getSignature() + " executed in " + executionTime + "s");
        return proceed;
    }

    private void logSystemHealth() {
        OperatingSystemMXBean osBean = ManagementFactory.getPlatformMXBean(OperatingSystemMXBean.class);
        // % CPU load this current JVM is taking, from 0.0-1.0
        LOG.info("CURRENT JVM LOAD ON CPU :::: {}", osBean.getProcessCpuLoad());
        // % load the overall system is at, from 0.0-1.0
        LOG.info("CURRENT OVERALL LOAD ON CPU :::: {}", osBean.getSystemCpuLoad());
        // current memory footprint is at, from 0.00 to 1.00
        LOG.info("CURRENT MEMORY USAGE FOOTPRINT :::: {}", getMemoryFootprint(osBean));

    }

    private String getMemoryFootprint(OperatingSystemMXBean osBean){
        long total = osBean.getTotalPhysicalMemorySize();
        long free = osBean.getFreePhysicalMemorySize();
        long usage = (total-free)/total;
        DecimalFormat f = new DecimalFormat("##.00");
        return f.format(usage);
    }
}

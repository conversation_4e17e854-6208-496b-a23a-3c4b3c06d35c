/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;


@SuppressWarnings("serial")
@Entity
@Table(name = "ASSET_RECOVERY")
public class AssetRecoveryDefinitionData implements java.io.Serializable {

    private Integer assetRecoveryId;

    private Integer assetId;

    private Integer eventId;

    private Integer unitId;

    private String assetStatus;

    private String recoveryType;

    private Integer recoveryEmpId;

    private Integer recoveryUnit;

    private String recoveryUnitType;

    private BigDecimal recoveryAmount;

    private String recoveryStatus;

    private Date salaryDeductionDate;

    private Date recoveryDate;

    private Integer createdBy;

    private Date creationDate;

    private BigDecimal recoveredAmount;

    private Integer recoveredBy;

    private Integer approvedBy;

    private Date approvalDate;


    public AssetRecoveryDefinitionData() {
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ASSET_RECOVERY_ID", unique = true, nullable = false)
    public Integer getAssetRecoveryId() {
        return assetRecoveryId;
    }

    public void setAssetRecoveryId(Integer assetRecoveryId) {
        this.assetRecoveryId = assetRecoveryId;
    }

    @Column(name = "ASSET_ID", unique = true, nullable = false)
    public Integer getAssetId() {
        return assetId;
    }

    public void setAssetId(Integer assetId) {
        this.assetId = assetId;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public Integer getUnitId() {
        return unitId;
    }
    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "ASSET_STATUS", nullable = false, length = 50)
    public String getAssetStatus() {
        return assetStatus;
    }

    public void setAssetStatus(String assetStatus) {
        this.assetStatus = assetStatus;
    }

    @Column(name = "RECOVERY_TYPE")
    public String getRecoveryType() {
        return recoveryType;
    }

    public void setRecoveryType(String recoveryType) {
        this.recoveryType = recoveryType;
    }

    @Column(name = "RECOVERY_AMOUNT")
    public BigDecimal getRecoveryAmount() {
        return recoveryAmount;
    }

    public void setRecoveryAmount(BigDecimal recoveryAmount) {
        this.recoveryAmount = recoveryAmount;
    }

    @Column(name = "RECOVERY_STATUS")
    public String getRecoveryStatus() {
        return recoveryStatus;
    }

    public void setRecoveryStatus(String recoveryStatus) {
        this.recoveryStatus = recoveryStatus;
    }


    @Column(name = "RECOVERY_EMP_ID")
    public Integer getRecoveryEmpId() {
        return recoveryEmpId;
    }

    public void setRecoveryEmpId(Integer recoveryEmpId) {
        this.recoveryEmpId = recoveryEmpId;
    }

    @Column(name = "RECOVERY_UNIT")
    public Integer getRecoveryUnit() {
        return recoveryUnit;
    }

    public void setRecoveryUnit(Integer recoveryUnit) {
        this.recoveryUnit = recoveryUnit;
    }

    @Column(name = "RECOVERY_UNIT_TYPE")
    public String getRecoveryUnitType() {
        return recoveryUnitType;
    }

    public void setRecoveryUnitType(String recoveryUnitType) {
        this.recoveryUnitType = recoveryUnitType;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "SALLARY_DEDUCTION_DATE", length = 19)
    public Date getSalaryDeductionDate() {
        return salaryDeductionDate;
    }

    public void setSalaryDeductionDate(Date salaryDeductionDate) {
        this.salaryDeductionDate = salaryDeductionDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "RECOVERY_DATE", length = 19)
    public Date getRecoveryDate() {
        return recoveryDate;
    }


    public void setRecoveryDate(Date recoveryDate) {
        this.recoveryDate = recoveryDate;
    }

    @Column(name = "CREATED_BY")
    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_DATE", length = 19)
    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    @Column(name = "RECOVERED_AMOUNT")
    public BigDecimal getRecoveredAmount() {
        return recoveredAmount;
    }

    public void setRecoveredAmount(BigDecimal recoveredAmount) {
        this.recoveredAmount = recoveredAmount;
    }

    @Column(name = "RECOVERED_BY")
    public Integer getRecoveredBy() {
        return recoveredBy;
    }

    public void setRecoveredBy(Integer recoveredBy) {
        this.recoveredBy = recoveredBy;
    }

    @Column(name = "EVENT_ID")
    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    @Column(name = "APPROVED_BY")
    public Integer getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(Integer approvedBy) {
        this.approvedBy = approvedBy;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "APPROVAL_DATE", length = 19)
    public Date getApprovalDate() {
        return approvalDate;
    }

    public void setApprovalDate(Date approvalDate) {
        this.approvalDate = approvalDate;
    }
}
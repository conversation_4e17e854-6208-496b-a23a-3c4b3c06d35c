package com.stpl.tech.scm.data.dao;

import java.util.Date;
import java.util.List;

import com.stpl.tech.scm.data.model.AssetDefinitionData;
import com.stpl.tech.scm.data.model.GoodsReceivedData;
import com.stpl.tech.scm.data.model.InvoiceExcessQuantity;
import com.stpl.tech.scm.data.model.PurchaseOrderVendorGRMappingData;
import com.stpl.tech.scm.data.model.SpecializedOrderInvoiceData;
import com.stpl.tech.scm.data.model.VendorGoodsReceivedData;
import com.stpl.tech.scm.domain.model.GrItemQuantityUpdation;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.VendorPoGRItems;

/**
 * Created by <PERSON><PERSON> on 22-06-2016.
 */
public interface GoodsReceiveManagementDao extends SCMAbstractDao {

    public List<GoodsReceivedData> getPendingGrs(Integer unitId, Date updatedAt , Boolean fetchRejected);

    public List<GoodsReceivedData> getRejectedPendingGrs(Integer unitId, Date updatedAt);

    public List<GoodsReceivedData> findGoodsReceived(Integer generationUnitId, Integer generatedForUnitId, Date startDate, Date endDate, SCMOrderStatus status, Integer goodsReceiveOrderId);

    public List<VendorGoodsReceivedData> findVendorGRs(Integer vendorId, Integer dispatchId, int deliveryUnitId, List<Integer> skus, Date startDate, Date endDate, Integer goodReceivedId);

	public List<VendorGoodsReceivedData> findVendorGRsForPayment(Integer vendorId, Integer dispatchId, int deliveryUnitId, Date startDate, Date endDate);

	public List<VendorGoodsReceivedData> findVendorGRsForPo(Integer vendorId, Integer dispatchId, Integer deliveryUnitId, List<Integer> skus, Date startDate, Date endDate, Integer paymentRequestId, Integer goodsReceivedId);

	public List<VendorPoGRItems> rejectedGr(Integer grId);
	/**
	 * @param unitId
	 * @return
	 */
	public List<GoodsReceivedData> getRaisedDisputedGrs(Integer unitId);

	public List<Integer> getExistingGrInFinancialYear(int unitId, Integer vendorId, String docNumber);

	public String findStatusForPoSoRo(String handOverData, Integer unitId, String unitName);

	Boolean checkIfSpecialOrder(Integer grId, List<Integer> vendorId);

	public List<VendorGoodsReceivedData> findRegularVendorsGrs(Integer unitId, Integer vendorId);

	public List<VendorPoGRItems> mapping(Integer grId);

	public List<GrItemQuantityUpdation> grItemQuantityDeviations();

	public List<AssetDefinitionData> getInitiatedAssetForGr(List <Integer> grIds);

	public List<AssetDefinitionData> getAssetForGr(List<Integer> grIds);

	public List<SpecializedOrderInvoiceData> findMilkInvoicesForPayment(Integer unitId , Integer vendorId , Date startDate , Date endDate);

	public List<GoodsReceivedData> findGrsByInvoiceId(Integer unitId ,Integer invoiceId);

	public List<InvoiceExcessQuantity> findPrExcessQuantity(Integer unitId , Integer invoiceId);

    public List<PurchaseOrderVendorGRMappingData> findGrsWithPoId(Integer tempPoId);

    public List<PurchaseOrderVendorGRMappingData> findPoWithGrId(Integer tempGrId);
}

package com.stpl.tech.scm.core.templates;

import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.model.VendorContractData;
import com.stpl.tech.scm.data.model.WorkOrderData;
import com.stpl.tech.scm.domain.model.VendorContractItemDataVO;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.WorkOrderApprovalMetaDataDto;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.velocity.tools.generic.DateTool;
import org.apache.velocity.tools.generic.MathTool;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class VendorContractTemplate extends AbstractVelocityTemplate {

    private WorkOrderData workOrderData;
    private List<VendorContractItemDataVO> vendorContractItemDataVOS;
    private VendorDetail vendorDetail;
    private EmployeeBasicDetail employeeBasicDetail;
    private String basePath;
    private String templateName;
    private WorkOrderApprovalMetaDataDto woMetaData;
    private String vendorContractString;
    private String companyName;

    @Override
    public String getTemplatePath() {
        return "templates/"+ templateName +".html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/" + workOrderData.getWorkOrderId() + ".html";
    }

    @Override
    public Map<String, Object> getData() {

        String addressOfSite = vendorContractItemDataVOS.stream()
                .map(VendorContractItemDataVO::getDeliveryLocation)
                .filter(Objects::nonNull)
                .map(SCMUtil::toTitleCase)
                .distinct()
                .collect(Collectors.joining(", "));

        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("workOrderData", workOrderData);
        stringObjectMap.put("vendorContractItemDataVOS", vendorContractItemDataVOS);
        stringObjectMap.put("vendorDetail", vendorDetail);
        stringObjectMap.put("startDate", AppUtils.getSQLFormattedDate(workOrderData.getStartDate()));
        stringObjectMap.put("endDate", AppUtils.getSQLFormattedDate(workOrderData.getEndDate()));
        stringObjectMap.put("currentBizDate", AppUtils.getSQLFormattedDate(AppUtils.getBusinessDate()));
        stringObjectMap.put("employeeBasicDetail", employeeBasicDetail);
        stringObjectMap.put("basePath", basePath);
        stringObjectMap.put("woMetaData", woMetaData);
        stringObjectMap.put("vendorContractString", vendorContractString.isEmpty() ? null : vendorContractString);
        stringObjectMap.put("companyName", companyName);
        stringObjectMap.put("dateTool", new DateTool());
        stringObjectMap.put("mathTool", new MathTool());
        stringObjectMap.put("addressOfSite", addressOfSite);
        stringObjectMap.put("vendorCompanyDispatchLocation", SCMUtil.getVendorCompanyDispatchLocation(vendorDetail));
        return stringObjectMap;
    }
}

package com.stpl.tech.scm.data.model;

import javax.persistence.*;

@Entity
@Table(name = "AGREEMENT_DETAIL")
public class AgreementDetail {

    private Integer agreementId;
    private String type;
    private String subtype;
    private String version;
    private String value;
    private String status;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name="AGREEMENT_ID", nullable = false)
    public Integer getAgreementId() {
        return agreementId;
    }

    public void setAgreementId(Integer agreementId) {
        this.agreementId = agreementId;
    }

    @Column(name="AGREEMENT_TYPE", nullable = false)
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Column(name = "AGREEMENT_SUBTYPE", nullable = false)
    public String getSubtype() {
        return subtype;
    }

    public void setSubtype(String subtype) {
        this.subtype = subtype;
    }

    @Column(name="AGREEMENT_VERSION", nullable = false)
    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    @Column(name="AGREEMENT_VALUE", nullable = false)
    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Column(name="AGREEMENT_STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}

package com.stpl.tech.scm.core.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 11-06-2017.
 */

@ResponseStatus(value = HttpStatus.CONFLICT,
        reason = "Day Closure in progress! \n You cannot create any transaction in the system until Day close is complete")
public class DayCloseInitiatedException extends Exception {
    private static final long serialVersionUID = 616315625637405710L;

    public DayCloseInitiatedException() {
    }

    public DayCloseInitiatedException(String message) {
        super(message);
    }

    public DayCloseInitiatedException(Throwable cause) {
        super(cause);
    }

    public DayCloseInitiatedException(String message, Throwable cause) {
        super(message, cause);
    }

    public DayCloseInitiatedException(String message, Throwable cause, boolean enableSuppression,
                                  boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}

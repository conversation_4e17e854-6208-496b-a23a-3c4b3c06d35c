package com.stpl.tech.scm.core.cache.dao.impl;

import com.stpl.tech.master.data.dao.AbstractDao;
import com.stpl.tech.scm.core.cache.dao.CacheManagementDao;
import com.stpl.tech.scm.core.cache.service.SkuCacheManager;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.dao.SCMAbstractDao;
import com.stpl.tech.scm.data.dao.impl.SCMAbstractDaoImpl;
import com.stpl.tech.scm.data.model.DerivedMappingData;
import com.stpl.tech.scm.data.model.ProductDefinitionData;
import com.stpl.tech.scm.data.model.SkuDefinitionData;
import com.stpl.tech.scm.data.redis.CacheUtil;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.SwitchStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Repository
public class CacheManagementDaoImpl extends SCMAbstractDaoImpl implements CacheManagementDao {

    @Override
    public List<ProductDefinitionData> getAllProducts() {
        String jpql = "SELECT DISTINCT p FROM ProductDefinitionData p " +
                "LEFT JOIN FETCH p.categoryDefinition " +
                "LEFT JOIN FETCH p.profileDefinitionData " +
                "LEFT JOIN FETCH p.subCategoryDefinition " +
                "LEFT JOIN FETCH p.uomConversionMappingData ";

        return manager.createQuery(jpql, ProductDefinitionData.class)
                .getResultList();
    }

    @Override
    public Map<Integer, Set<SkuDefinitionData>> getSkusForProducts(List<Integer> productIds) {
        if (productIds == null || productIds.isEmpty()) {
            return Collections.emptyMap();
        }
        TypedQuery<SkuDefinitionData> query = manager.createQuery(
                "SELECT s FROM SkuDefinitionData s WHERE s.linkedProduct.productId IN :ids", SkuDefinitionData.class);
        query.setParameter("ids", productIds);

        List<SkuDefinitionData> skus = query.getResultList();
        // Group by productId
        return skus.stream()
                .collect(Collectors.groupingBy(
                        s -> s.getLinkedProduct().getProductId(),
                        Collectors.toSet()
                ));
    }

    @Override
    public Map<Integer, List<DerivedMappingData>> getDerivedMappingsForProducts(List<Integer> productIds) {
        if (productIds == null || productIds.isEmpty()) {
            return Collections.emptyMap();
        }

        TypedQuery<DerivedMappingData> query = manager.createQuery(
                "SELECT d FROM DerivedMappingData d WHERE d.product.productId IN :ids", DerivedMappingData.class);
        query.setParameter("ids", productIds);

        List<DerivedMappingData> mappings = query.getResultList();
        // Group by productId
        return mappings.stream().collect(Collectors.groupingBy(d -> d.getProduct().getProductId()));
    }

    @Override
    public List<SkuCacheManager.SkuWithProduct> getAllSkus() {
        Query query = manager.createQuery(
                "select s, p.productId, p.productName, p.taxCategoryCode " +
                        "from SkuDefinitionData s " +
                        "join s.linkedProduct p " +
                        "where s.skuStatus not in (:statusList)"
                );
        query.setParameter("statusList", List.of(SwitchStatus.INITIATED.name(), SwitchStatus.CANCELLED.name(), SwitchStatus.REJECTED.name()));
        List<Object[]> results = query.getResultList();

        List<SkuCacheManager.SkuWithProduct> skuList = new ArrayList<>();
        results.forEach(row -> {
            SkuDefinitionData skuData = (SkuDefinitionData) row[0];
            IdCodeName product = SCMUtil.generateIdCodeName(
                    (Integer) row[1],
                    (String) row[3],
                    (String) row[2]
            );
            skuList.add(new SkuCacheManager.SkuWithProduct(skuData, product));
        });
        return skuList;
    }


}

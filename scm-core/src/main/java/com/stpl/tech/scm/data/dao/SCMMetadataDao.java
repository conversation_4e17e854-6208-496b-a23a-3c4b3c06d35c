/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.dao;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import com.stpl.tech.master.core.external.cache.ProductRecipeKey;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.data.model.AssetDefinitionData;
import com.stpl.tech.scm.data.model.AuditLogData;
import com.stpl.tech.scm.data.model.MaintenanceWhZoneMapping;
import com.stpl.tech.scm.data.model.ProductDefinitionData;
import com.stpl.tech.scm.data.model.RegionFulfillmentMapping;
import com.stpl.tech.scm.data.model.SkuDefinitionData;
import com.stpl.tech.scm.data.model.UnitDistanceMappingData;
import com.stpl.tech.scm.data.model.PlanOrderItemData;
import com.stpl.tech.scm.data.model.UnitVendorMappingData;
import com.stpl.tech.scm.data.model.VendorDetailData;
import com.stpl.tech.scm.data.model.ZipCodeDistanceMapping;
import com.stpl.tech.scm.domain.model.UnitDetail;

/**
 * Created by Rahul Singh on 04-05-2016.
 */
public interface SCMMetadataDao extends SCMAbstractDao {

	public  List<VendorDetailData> findAllVendorDetailData();

    List<ProductDefinitionData> findAllProductDefinitions();

    public List<UnitVendorMappingData> getVendorMappings(int unitId);

	public List<Pair<Integer, Integer>> getUnitToProductList(List<Integer> units);

	public List<Integer> getUnits();

	public List<Pair<Integer, Integer>> getUnitToSKUList(List<Integer> units);

	public List<UnitDistanceMappingData> getUnitDistanceMapping();

    List<ZipCodeDistanceMapping> getZipCodeDistanceMapping();
	Map<Integer,Map<Integer,ProductRecipeKey>> getProductProfileMapping();

	public PlanOrderItemData getPlanOrderData(Integer  productId, Integer unit);

	public List<RegionFulfillmentMapping> getAllActiveRegionMappings();

	public List<Integer> getUnitsForNonZeroSkuInventory(Integer skuId);

	public List<Integer>  checkForInCompletePurchaseOrder(Integer skuId , Integer unitId , Boolean isAsset);

	public List<Integer>  checkForInCompleteTO(Integer skuId , Integer unitId);

	public List<Integer> checkForInCompleteInternalGr(Integer skuId , Integer unitId , Boolean isAsset);

	public List<Integer> checkForProductionBookingMapping(Integer skuId);

	public List<Integer> checkInCompleteGrForPayment(Integer skuId, Integer unitId);

	public List<Integer> checkInCompletePayment(Integer skuId , Integer unitId);

	public List<Integer> getAllActiveLinkedSKUs(Integer productId);

	public List<Integer> getAllActiveAssetsForUnit(Integer unitId);

	public Optional<List<MaintenanceWhZoneMapping>> getAllMaintenanceWHMappings();

	public AuditLogData getLastAuditEntry();

	public List<Integer> checkForFaReceiveing(List<Integer> unitId) ;

    public List<Object[]> getPendingMilkBreadRos(Date businessDate, List<Integer> milkBreadProductIds, Integer requestUnitId, Set<Integer> roIds);

	public List<UnitVendorMappingData> getAllUnitVendorsWithVendorIdsAndUnitIds(Set<Integer> specializedVendorIds, Set<Integer> unitIds);
	Set<Integer> checkActiveStocks(UnitDetail unitDetail);

	public List<SkuDefinitionData> findAllSkus();

	Set<Integer> checkActiveGatePass(Integer unitId);

    List<AssetDefinitionData> findAssetDefinitionsByUnitIds(List<Integer> unitIds);

}

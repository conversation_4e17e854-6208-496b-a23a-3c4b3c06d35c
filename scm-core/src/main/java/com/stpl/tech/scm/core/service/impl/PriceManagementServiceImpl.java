/*
 * Created By Shanmukh
 */

/**
 * 
 */
package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.MappingCache;
import com.stpl.tech.scm.core.service.PriceManagementService;
import com.stpl.tech.scm.core.service.SCMProductManagementService;
import com.stpl.tech.scm.core.service.SCMProductPriceManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.PriceManagementDao;
import com.stpl.tech.scm.data.model.CostDetailData;
import com.stpl.tech.scm.data.model.CostDetailDataCafe;
import com.stpl.tech.scm.data.model.ExpiryDateCorrectionData;
import com.stpl.tech.scm.domain.model.CostDetail;
import com.stpl.tech.scm.domain.model.ExpiryDataCorrectionTypeEnum;
import com.stpl.tech.scm.domain.model.FulfillmentType;
import com.stpl.tech.scm.domain.model.PriceUpdateEntryType;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ScmRecipeProductCost;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.vo.ConsumptionVO;
import com.stpl.tech.scm.domain.vo.ReceivingVO;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 *
 */
@Service
public class PriceManagementServiceImpl implements PriceManagementService{

	private static final Logger LOG = LoggerFactory.getLogger(PriceManagementServiceImpl.class);
	
	@Autowired
	private PriceManagementDao priceManagementDao;
	
	@Autowired
	private SCMProductManagementService sCMProductManagementService;

	@Autowired
    private SCMCache scmCache;

	@Autowired
	private MappingCache mappingCacheService;

	@Autowired
	private RecipeCache recipeCache;

	@Autowired
	private SCMProductPriceManagementService scmProductPriceManagementService;

	// While adding the receivings, we need to do the following
	// 1. create the entry in audit table as well as the drill down table.
	// 2. Incase prices differ from the latest then we need to add a new row and
	// mark it as latest.
	// 3. Incase the prices match then we need to topup the inventory.
	// 4. Incase the prices are not found then a new entry is made.

	public ReceivingVO addReceiving(ReceivingVO rec){
		return rec;
		
	}

	// While reducing the consumption we need to do the following
	// 1. get all the inventory data for all the listed products in the
	// consumption VO.
	// 2. Try to fulfill all the required inventory for consumable using the
	// fifo for quantity of a product or a cafe.
	// 3. Get the weighted mean of the product/sku and set against each item.
	// 4. Set the drilldowns for each entry from the inventory entries being
	// consumed at that point of time.
	public ConsumptionVO reduceConsumbale(ConsumptionVO rec){
		return rec;
		
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<CostDetail> getPriceDetails(int unitId,String keyType,int keyId) {
		List<Integer> keyIdList= Arrays.asList(keyId);
		List<CostDetailData> costDetailDataList=priceManagementDao.getCurrentPrices(PriceUpdateEntryType.fromValue(keyType), unitId,keyIdList, false);
		return setPrices(costDetailDataList);
	}

	@Override
	public Map<Integer, ScmRecipeProductCost> getScmRecipeCost(String region, List<Integer> productIdList) {
		Map<Integer, ScmRecipeProductCost> result = new HashMap<>();
		Pair<Integer, Integer> fulfilmentKitchenWH = scmProductPriceManagementService.getFulfilmentKitchenWHOfRegion(region);
		List<String> regions = scmProductPriceManagementService.getUniqueRegions(region, fulfilmentKitchenWH);
		Map<String, List<ProductDefinition>> productsFulfilmentByType = scmProductPriceManagementService.getProductsFulfilmentByType(productIdList);
		Map<Integer, Pair<List<SkuDefinition> , BigDecimal>> productSkuMapWithPrice = scmProductPriceManagementService.getProductSkuMapWithPrice(productIdList);
		Map<String, List<Integer>> fulfilmentTypeWithSkus = scmProductPriceManagementService.getFulfilmentTypeWithSkus(productsFulfilmentByType, productSkuMapWithPrice);
		List<CostDetailData> costDetailDataKitchens = new ArrayList<>();
		List<CostDetailData> costDetailDataWhs = new ArrayList<>();
		if (Objects.nonNull(fulfilmentKitchenWH.getKey()) && fulfilmentTypeWithSkus.containsKey(FulfillmentType.KITCHEN.value()) && Objects.nonNull(fulfilmentTypeWithSkus.get(FulfillmentType.KITCHEN.value()))
				&& !fulfilmentTypeWithSkus.get(FulfillmentType.KITCHEN.value()).isEmpty()) {
			costDetailDataKitchens = priceManagementDao.getCurrentPrices(PriceUpdateEntryType.SKU, fulfilmentKitchenWH.getKey(),fulfilmentTypeWithSkus.get(FulfillmentType.KITCHEN.value()), false);
			scmProductPriceManagementService.setPricesInProductsSkuMap(productSkuMapWithPrice, costDetailDataKitchens);
		}
		if (Objects.nonNull(fulfilmentKitchenWH.getValue()) && fulfilmentTypeWithSkus.containsKey(FulfillmentType.WAREHOUSE.value()) && Objects.nonNull(fulfilmentTypeWithSkus.get(FulfillmentType.WAREHOUSE.value()))
				&& !fulfilmentTypeWithSkus.get(FulfillmentType.WAREHOUSE.value()).isEmpty()) {
			costDetailDataWhs = priceManagementDao.getCurrentPrices(PriceUpdateEntryType.SKU, fulfilmentKitchenWH.getValue(),fulfilmentTypeWithSkus.get(FulfillmentType.WAREHOUSE.value()), false);
			scmProductPriceManagementService.setPricesInProductsSkuMap(productSkuMapWithPrice, costDetailDataWhs);
		}

		List<Integer> missingProductPriceIds = scmProductPriceManagementService.getMissingPricesProductKeys(productSkuMapWithPrice);
		if (!missingProductPriceIds.isEmpty()) {
			LOG.info("Missing Prices From Fulfilment Unit Products are : {} ", missingProductPriceIds.size());
			try {
				List<CostDetailData> costDetailDataList = priceManagementDao.getAllCurrentPriceForAnyUnitInRegionByKeyId(PriceUpdateEntryType.PRODUCT, missingProductPriceIds, regions, false);
				scmProductPriceManagementService.setPricesInProductsSkuMap(productSkuMapWithPrice, costDetailDataList);
			} catch (Exception e) {
				LOG.error("Exception Occurred While getting Price From Region : {} ::: ",region,e);
			}
		}

		missingProductPriceIds.clear();
		missingProductPriceIds = scmProductPriceManagementService.getMissingPricesProductKeys(productSkuMapWithPrice);
		if (!missingProductPriceIds.isEmpty()) {
			LOG.info("Now searching for SKUs in complete region");
			if (Objects.nonNull(fulfilmentKitchenWH.getKey()) && fulfilmentTypeWithSkus.containsKey(FulfillmentType.KITCHEN.value()) && Objects.nonNull(fulfilmentTypeWithSkus.get(FulfillmentType.KITCHEN.value()))
					&& !fulfilmentTypeWithSkus.get(FulfillmentType.KITCHEN.value()).isEmpty()) {
				costDetailDataKitchens.clear();
				try {
					costDetailDataKitchens = priceManagementDao.getAllCurrentPriceForAnyUnitInRegionByKeyId(PriceUpdateEntryType.SKU, fulfilmentTypeWithSkus.get(FulfillmentType.KITCHEN.value()), regions, false);
					scmProductPriceManagementService.setPricesInProductsSkuMap(productSkuMapWithPrice, costDetailDataKitchens);
				} catch (Exception e) {
					LOG.error("Exception Occurred While getting Price From Region : {} ::: ",region,e);
				}
			}
			if (Objects.nonNull(fulfilmentKitchenWH.getValue()) && fulfilmentTypeWithSkus.containsKey(FulfillmentType.WAREHOUSE.value()) && Objects.nonNull(fulfilmentTypeWithSkus.get(FulfillmentType.WAREHOUSE.value()))
					&& !fulfilmentTypeWithSkus.get(FulfillmentType.WAREHOUSE.value()).isEmpty()) {
				costDetailDataWhs.clear();
				try {
					costDetailDataWhs = priceManagementDao.getAllCurrentPriceForAnyUnitInRegionByKeyId(PriceUpdateEntryType.SKU, fulfilmentTypeWithSkus.get(FulfillmentType.WAREHOUSE.value()), regions, false);
					scmProductPriceManagementService.setPricesInProductsSkuMap(productSkuMapWithPrice, costDetailDataWhs);
				} catch (Exception e) {
					LOG.error("Exception Occurred While getting Price From Region : {} ::: ",region,e);
				}
			}
		}
		for (int productId : productIdList) {
			ProductDefinition productDefinition = scmCache.getProductDefinition(productId);
			ScmRecipeProductCost scmRecipeProductCost = new ScmRecipeProductCost();
			if (productSkuMapWithPrice.containsKey(productId)) {
				Pair<List<SkuDefinition> , BigDecimal> pair = productSkuMapWithPrice.get(productId);
				if (Objects.nonNull(pair)) {
					if(Objects.nonNull(pair.getValue())) {
						scmRecipeProductCost.setCost(pair.getValue());
						scmRecipeProductCost.setCalculatedFromNegotiatedPrice(AppConstants.NO);
					} else {
						scmRecipeProductCost.setCalculatedFromNegotiatedPrice(AppConstants.YES);
						scmRecipeProductCost.setCost(SCMUtil.convertToBigDecimal(productDefinition.getNegotiatedUnitPrice()));
					}
				}
			} else {
				scmRecipeProductCost.setCalculatedFromNegotiatedPrice(AppConstants.YES);
				scmRecipeProductCost.setCost(SCMUtil.convertToBigDecimal(productDefinition.getNegotiatedUnitPrice()));
			}
			if (productDefinition.isRecipeRequired()) {
				scmRecipeProductCost.setIsRecipeProduct(AppConstants.YES);
			}
			result.put(productId, scmRecipeProductCost);
		}
		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<Integer, BigDecimal> getWeightedPriceDetailsForProducts(int unitId, String keyType,
			List<Integer> productIds) {
		Map<Integer, BigDecimal> productPriceMap = new HashMap<>();
		for (int productId : productIds) {
			BigDecimal priceInverntorySum = new BigDecimal(0);
			BigDecimal totalInverntory = new BigDecimal(0);
			ProductDefinition productDefinition = scmCache.getProductDefinition(productId);
			if (productDefinition.isRecipeRequired()) {
				LOG.info("recipe is required for product id : {} and name : {}",productDefinition.getProductId(),productDefinition.getProductName());
				List<Integer> productsIds = getProductIdOfRecipeProduct(unitId, productDefinition);
				if (productsIds != null) {
					LOG.info("for product id : {} got subproducts {} list",productId,Arrays.toString(productsIds.toArray()));
					for (Integer id : productsIds) {
						List<Integer> skuIds = getSkuIdList(id);
						LOG.info("got sku's list  {} for product {}",Arrays.toString(skuIds.toArray()),productId);
						List<CostDetailData> costDetailSkuList = priceManagementDao.getCurrentPrices(PriceUpdateEntryType.SKU,
								unitId, skuIds, true);
						for (CostDetailData detailData : costDetailSkuList) {
							BigDecimal quantity = detailData.getQuantity().setScale(6, BigDecimal.ROUND_HALF_UP);
							if (quantity.compareTo(BigDecimal.ZERO) == 0) {
								quantity = BigDecimal.ONE;
							}
							priceInverntorySum = AppUtils.add(priceInverntorySum,
									AppUtils.multiplyWithScale(detailData.getPrice(), quantity, 6));
							totalInverntory = AppUtils.add(totalInverntory, quantity);
						}
					}
					productPriceMap.put(productId, AppUtils.divide(priceInverntorySum, totalInverntory));
				}
				else {
					LOG.info("for product id : {} no recipe products found",productId);
					productPriceMap.put(productId, null);
				}
			}
			else {
				LOG.info("recipe is not required for product id : {} and name : {}",productDefinition.getProductId(),productDefinition.getProductName());
				List<Integer> skuIds = getSkuIdList(productId);
				LOG.info("got sku's list of size {} for product {}",skuIds.size(),productId);
				List<CostDetailData> costDetailSkuList = priceManagementDao.getCurrentPrices(PriceUpdateEntryType.SKU,
						unitId, skuIds, true);
				for (CostDetailData detailData : costDetailSkuList) {
					BigDecimal quantity = detailData.getQuantity().setScale(6, BigDecimal.ROUND_HALF_UP);
					if (quantity.compareTo(BigDecimal.ZERO) == 0) {
						quantity = BigDecimal.ONE;
					}
					priceInverntorySum = AppUtils.add(priceInverntorySum,
							AppUtils.multiplyWithScale(detailData.getPrice(), quantity, 6));
					totalInverntory = AppUtils.add(totalInverntory, quantity);
				}
				productPriceMap.put(productId, AppUtils.divide(priceInverntorySum, totalInverntory));
			}
			LOG.info("product id {} and name {} price is {} and total is {}",productDefinition.getProductId(),productDefinition.getProductName(), priceInverntorySum, totalInverntory);
		}
		return productPriceMap;
	}

	private List<Integer> getProductIdOfRecipeProduct(int unitId, ProductDefinition productDefinition) {
		List<Integer> result = new ArrayList<>();
		try {
			LOG.info("checking recipe for id : {}",productDefinition.getProductId());
			RecipeDetail recipe = recipeCache.getScmRecipe(scmCache.getRecipeProfile(unitId, productDefinition.getProductId()));
			if (recipe == null) {
				return null;
			}
			for (IngredientProductDetail pd : recipe.getIngredient().getComponents()) {
				ProductDefinition def = scmCache.getProductDefinition(pd.getProduct().getProductId());
				if (def != null && def.isRecipeRequired()) {
					List<Integer> recursiveList = getProductIdOfRecipeProduct(unitId,def);
					if (recursiveList == null) {
						return null;
					}
					else {
						result.addAll(recursiveList);
					}
				}
				else {
					result.add(pd.getProduct().getProductId());
				}
			}
			return result;
		}
		catch (Exception e) {
			LOG.error("Exception Occurred while getting price of recipe product :: ",e);
			return null;
		}
	}

	//TODO SUMEET Move this to dao and then to cache
	private List<Integer>  getSkuIdList(int productId){
		Map<Integer, List<SkuDefinition>> productSkuMap=new HashMap<>();//=sCMProductManagementService.viewAllSkuByProduct(productId);
		List<SkuDefinition> def=mappingCacheService.findAllSkuForProductId(productId);
		productSkuMap.put(productId,def);
		List<SkuDefinition> skuList=productSkuMap.get(productId);
		List<Integer> skuIds = new ArrayList<>(); 
		for(SkuDefinition sku:skuList){
			skuIds.add(sku.getSkuId());
		}
		return skuIds;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean addPriceDetails(CostDetail costDetail) throws InventoryUpdateException {
		boolean result =true;
		CostDetailData costDetailData=priceManagementDao.addNewCostDetailEntry(costDetail.getUnitId(),
				costDetail, costDetail.getOldPrice(), false, true, false);
		if(costDetailData==null){
			result=false;
		}
		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void addPriceDetailsInBulk(Integer unitId, List<CostDetail> costDetails, List<String> errorMessages) {
		try {
			priceManagementDao.addNewCostDetailEntryInBulk(unitId, costDetails);
		} catch (Exception e) {
			LOG.error("Exception Occurred While Adding prices in Bulk ", e);
			errorMessages.add("Exception Occurred While Adding prices in Bulk for Unit Id : " + unitId);
		}
	}

	@Override
	public Map<Integer, List<CostDetail>> readUploadedCostDetails(MultipartFile file) throws SumoException {
		List<CostDetail> result = new ArrayList<>();
		try {
			List<String> errorMessage = new ArrayList<>();
			InputStream excelFile = file.getInputStream();
			XSSFWorkbook workbook = new XSSFWorkbook(excelFile);
			XSSFSheet sheet1 = workbook.getSheetAt(0);
			int lastRowNumber = sheet1.getLastRowNum();
			XSSFRow headerRow = sheet1.getRow(0);
			if (headerRow.getCell(0).getStringCellValue().equalsIgnoreCase("SKU ID")) {
				for (int i = 1; i <= lastRowNumber; i++) {
					XSSFRow currentRow = sheet1.getRow(i);
					CostDetail costDetail = new CostDetail();
					if (Objects.nonNull(currentRow.getCell(0))) {
						costDetail.setKeyId((int) currentRow.getCell(0).getNumericCellValue());
						SkuDefinition skuDefinition = scmCache.getSkuDefinition(costDetail.getKeyId());
						if (Objects.nonNull(skuDefinition)) {
							costDetail.setKeyType(PriceUpdateEntryType.SKU);
							costDetail.setQuantity(BigDecimal.ZERO);
							if (Objects.nonNull(currentRow.getCell(2))) {
								costDetail.setUnitId((int) currentRow.getCell(2).getNumericCellValue());
							} else {
								errorMessage.add("Missing Unit Id of SKU Id : " + costDetail.getKeyId() + " at Row : " + (i+1));
								continue;
							}
							costDetail.setUom(skuDefinition.getUnitOfMeasure());
							if (Objects.isNull(currentRow.getCell(5))) {
								errorMessage.add("Missing Price of SKU Id : " + costDetail.getKeyId() + " at Row : " + (i+1));
								continue;
							}
							costDetail.setPrice(BigDecimal.valueOf(currentRow.getCell(5).getNumericCellValue()));
							costDetail.setOldPrice(BigDecimal.valueOf(currentRow.getCell(5).getNumericCellValue()));
							costDetail.setExpiryDate(SCMUtil.format1AmExpiry(AppUtils.getDayBeforeOrAfterCurrentDay(
									skuDefinition.getShelfLifeInDays())));
							result.add(costDetail);
						} else {
							errorMessage.add("Can not Find Sku Definition of SKU Id : " + costDetail.getKeyId() + " at Row : " + (i+1));
						}
					} else {
						errorMessage.add("Missing Sku Id At ROW : " + (i+1));
					}
				}
				if (errorMessage.isEmpty()) {
					return result.stream().collect(Collectors.groupingBy(CostDetail::getUnitId));
				} else {
					throw new SumoException("Error While Parsing Sheet ..! ", "Please Fix these errors <br> " + Arrays.asList(errorMessage.toArray()));
				}
			} else {
				throw new SumoException("Invalid Sheet", "Please upload Correct Sheet..!");
			}
		} catch (SumoException e) {
			throw e;
		} catch (Exception e) {
			throw new SumoException("Error reading excel file :: {}",e.getMessage());
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updatePriceDetails(CostDetail costDetail){
		boolean result = true;
		CostDetailData costDetailData=priceManagementDao.updateCostDetailEntry(costDetail.getCostDetailId(),
				costDetail.getOldPrice(), costDetail, false);
		if(costDetailData==null){
			result=false;
		}
		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<CostDetail> getPriceDetailsForUnit(int unitId) {
		return setPrices(priceManagementDao.getPriceDetailsForUnit(unitId));
	}

	private List<CostDetail> setPrices(List<CostDetailData> costDetailDataList){
        List<CostDetail> costs = new ArrayList<>();
	    for(CostDetailData csd :costDetailDataList){
            CostDetail cost = SCMDataConverter.convert(csd);
            String name = cost.getKeyType().equals(PriceUpdateEntryType.PRODUCT)
                    ? scmCache.getProductDefinition(cost.getKeyId()).getProductName()
                    : scmCache.getSkuDefinition(cost.getKeyId()).getSkuName();
            cost.setName(name);
            costs.add(cost);
        }
        return costs;
    }

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void resetExpiryDateOfExpiredStockForCafes() {
		try {
			List<CostDetailData> costDetailDataList = priceManagementDao.resetExpiryDateOfExpiringStock(true);
			resetCostDetailData(costDetailDataList, ExpiryDataCorrectionTypeEnum.COST_DETAIL_DATA_CAFE_RESET.name());
		} catch (Exception e) {
			LOG.error("Exception Occurred While Resetting Expiry Date for CAFES :: ", e);
		}
	}

	private void resetCostDetailData(List<CostDetailData> costDetailDataList, String expiryDataCorrectionType) throws SumoException {
		if (Objects.nonNull(costDetailDataList) && !costDetailDataList.isEmpty()) {
			LOG.info("Found list Of Cost Detail while resetting {} : {}",expiryDataCorrectionType , costDetailDataList.size());
			for (CostDetailData costDetailData : costDetailDataList) {
				costDetailData.setLastUpdatedTime(AppUtils.getCurrentTimestamp());
				Date originalExpiryDate = costDetailData.getExpiryDate();
				costDetailData.setExpiryDate(SCMUtil.getNextExpiryDate(costDetailData.getExpiryDate()));
				addExpiryDateCorrection(expiryDataCorrectionType, costDetailData.getKeyId(),
						costDetailData.getExpiryDate(), originalExpiryDate , AppConstants.SYSTEM_EMPLOYEE_ID, costDetailData.getUnitId(), costDetailData.getKeyType(), costDetailData.getCostDetailDataId());
			}
			priceManagementDao.update(costDetailDataList, false);
		}
		priceManagementDao.flush();
	}

	@Override
	public void addExpiryDateCorrection(String expiryDataCorrectionType, Integer keyId, Date updatedExpiryDate, Date originalExpiryDate, Integer createdBy,
										int unitId, String keyType, Integer transactionId) throws SumoException {
		ExpiryDateCorrectionData expiryDateCorrectionData = new ExpiryDateCorrectionData();
		expiryDateCorrectionData.setExpiryDateCorrectionType(expiryDataCorrectionType);
		expiryDateCorrectionData.setTransactionId(transactionId);
		expiryDateCorrectionData.setUnitId(unitId);
		expiryDateCorrectionData.setKeyId(keyId);
		expiryDateCorrectionData.setKeyType(keyType);
		expiryDateCorrectionData.setUpdatedExpiryDate(updatedExpiryDate);
		expiryDateCorrectionData.setOriginalExpiryDate(originalExpiryDate);
		expiryDateCorrectionData.setCreatedBy(createdBy);
		expiryDateCorrectionData.setCreatedAt(AppUtils.getCurrentTimestamp());
		priceManagementDao.add(expiryDateCorrectionData, false);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void resetExpiryDateOfExpiredStockForKWh() {
		try {
			List<CostDetailData> costDetailDataList = priceManagementDao.resetExpiryDateOfExpiringStock(false);
			resetCostDetailData(costDetailDataList, ExpiryDataCorrectionTypeEnum.COST_DETAIL_DATA_WH_RESET.name());
		} catch (Exception e) {
			LOG.error("Exception Occurred While Resetting Expiry Date for KWH :: ", e);
		}
	}
}

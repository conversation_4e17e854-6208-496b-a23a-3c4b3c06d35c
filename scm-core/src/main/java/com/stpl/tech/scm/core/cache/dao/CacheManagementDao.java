package com.stpl.tech.scm.core.cache.dao;

import com.stpl.tech.scm.core.cache.service.SkuCacheManager;
import com.stpl.tech.scm.data.dao.SCMAbstractDao;
import com.stpl.tech.scm.data.model.DerivedMappingData;
import com.stpl.tech.scm.data.model.ProductDefinitionData;
import com.stpl.tech.scm.data.model.SkuDefinitionData;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface CacheManagementDao extends SCMAbstractDao {

    List<ProductDefinitionData> getAllProducts();
    Map<Integer, Set<SkuDefinitionData>> getSkusForProducts(List<Integer> products);
    Map<Integer, List<DerivedMappingData>> getDerivedMappingsForProducts(List<Integer> productIds);
    List<SkuCacheManager.SkuWithProduct> getAllSkus();

}

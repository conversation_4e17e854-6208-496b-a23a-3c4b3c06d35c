package com.stpl.tech.scm.notification.email;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.SalesPerformaInvoice;
import com.stpl.tech.scm.notification.email.template.B2BReturnMailTemplate;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

public class B2BReturnGenerationNotification extends EmailNotification {

    private EnvType envType;
    private SalesPerformaInvoice invoice;
    private B2BReturnMailTemplate template;

    public B2BReturnGenerationNotification(EnvType envType, B2BReturnMailTemplate template, SalesPerformaInvoice invoice) {
        this.envType = envType;
        this.template = template;
        this.invoice = invoice;
    }

    @Override
    public String[] getToEmails() {
        if (SCMUtil.isDev(envType)) {
            return new String[]{SCMServiceConstants.TECHNOLOGY_EMAIL};
        } else {
            return new String[]{"<EMAIL>"};
        }
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        String msg = "B2B Return Invoice Created For : " + invoice.getVendor().getName();
        String subject = msg + " On " +
                AppUtils.getTimeISTString(AppUtils.getCurrentTimestamp());
        if (AppUtils.isDev(envType)) {
            subject = "[DEV] : " + subject;
        }
        return subject;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return template.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
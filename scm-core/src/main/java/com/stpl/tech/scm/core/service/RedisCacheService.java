package com.stpl.tech.scm.core.service;

import com.stpl.tech.scm.domain.model.AssetDefinition;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import java.util.List;
import java.util.Map;

public interface RedisCacheService {

    // Asset Definition Cache Methods
    public List<AssetDefinition> getAssetsByUnitId(Integer unitId);

    public List<AssetDefinition> getAllAssets();

    public AssetDefinition getAssetByAssetId(Integer assetId);

    public void updateAssetToCache(AssetDefinition assetDefinition);

    public void reloadAssetCache();

    public void deleteAssetCache();

    public void initialAssetCacheLoad();


}

package com.stpl.tech.scm.core.util;

import com.stpl.tech.scm.domain.model.IdCodeName;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class TemplateConstants {

    public enum TemplateCode {
        VENDOR_CONTRACT_1(1, "VendorContract_1", "365"),
        MASTER_VENDOR_CONTRACT(2, "MasterVendorContractTemplate", "MASTER_VENDOR_CONTRACT"),
        WORK_ORDER(3, "WorkOrderTemplate", "WORK_ORDER"),
        CUSTOMER_CONTRACT(4, "CustomerContractTemplate", "CUSTOMER_CONTRACT"),
        MASTER_VENDOR_CONTRACT_V2(5, "master-vendor-contract-template-v2", "MASTER_VENDOR_CONTRACT_V2");

        public final int id;
        public final String code; // html file name
        public final String name;

        TemplateCode(int id, String code, String name) {
            this.id = id;
            this.code = code;
            this.name = name;
        }
    }

    private Map<Integer, IdCodeName> template;

    public Map<Integer, IdCodeName> getVendorContractTemplate() {
        if (template == null || template.isEmpty()) {
            template = new HashMap<>();
            for (TemplateCode t : TemplateCode.values()) {
                template.put(t.id, new IdCodeName(t.id, t.code, t.name));
            }
        }
        return template;
    }


}

/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;
// Generated 4 May, 2016 12:06:56 AM by Hibernate Tools 3.2.2.GA

import com.stpl.tech.scm.core.service.eventListeners.SkuEventListener;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Fetch;

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * SkuDefinitionData generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "SKU_DEFINITION")
@Getter
@Setter
@EntityListeners(SkuEventListener.class)
public class SkuDefinitionData implements java.io.Serializable {

    private Integer skuId;
    private String skuName;
    private String skuDescription;
    private String supportsLooseOrdering;
    private Date creationDate;
    private int createdBy;
    private String hasInner;
    private String hasCase;
    private ProductDefinitionData linkedProduct;
    private int shelfLifeInDays;
    private String skuStatus;
    private String unitOfMeasure;
    private String skuImage;
    private BigDecimal unitPrice;
    private BigDecimal negotiatedUnitPrice;
    private Date priceLastUpdated;
    private String torqusSKU;
    private String isDefault = "N";
    private Integer inventoryList;
    private String skuCode;
    private String taxCategoryCode;
    private Date voDisContinuedFrom;
    private Date roDisContinuedFrom;
    private String isBranded;
    private String brand;
    private Integer approvalDocId;
    private Integer userSkuCreationId;

    public SkuDefinitionData() {
    }

    public SkuDefinitionData(String skuName, String supportsLooseOrdering, int createdBy, String hasInner,
                             String hasCase, ProductDefinitionData linkedProduct, int shelfLifeInDays, String skuStatus,
                             String unitOfMeasure,String isBranded) {
        this.skuName = skuName;
        this.supportsLooseOrdering = supportsLooseOrdering;
        this.createdBy = createdBy;
        this.hasInner = hasInner;
        this.hasCase = hasCase;
        this.linkedProduct = linkedProduct;
        this.shelfLifeInDays = shelfLifeInDays;
        this.skuStatus = skuStatus;
        this.unitOfMeasure = unitOfMeasure;
        this.isBranded = isBranded;
    }

    public SkuDefinitionData(String skuName, String skuDescription, String supportsLooseOrdering, Date creationDate,
                             int createdBy, String hasInner, String hasCase, ProductDefinitionData linkedProduct, int shelfLifeInDays,
                             String skuStatus, String unitOfMeasure, String isBranded) {
        this.skuName = skuName;
        this.skuDescription = skuDescription;
        this.supportsLooseOrdering = supportsLooseOrdering;
        this.creationDate = creationDate;
        this.createdBy = createdBy;
        this.hasInner = hasInner;
        this.hasCase = hasCase;
        this.linkedProduct = linkedProduct;
        this.shelfLifeInDays = shelfLifeInDays;
        this.skuStatus = skuStatus;
        this.unitOfMeasure = unitOfMeasure;
        this.isBranded = isBranded;
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)

    @Column(name = "SKU_ID", unique = true, nullable = false)
    public Integer getSkuId() {
        return this.skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    @Column(name = "SKU_NAME", nullable = false)
    public String getSkuName() {
        return this.skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    @Column(name = "SKU_DESCRIPTION", length = 1000)
    public String getSkuDescription() {
        return this.skuDescription;
    }

    public void setSkuDescription(String skuDescription) {
        this.skuDescription = skuDescription;
    }

    @Column(name = "SUPPORTS_LOOSE_ORDERING", nullable = false, length = 1)
    public String getSupportsLooseOrdering() {
        return this.supportsLooseOrdering;
    }

    public void setSupportsLooseOrdering(String supportsLooseOrdering) {
        this.supportsLooseOrdering = supportsLooseOrdering;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_DATE", length = 19)
    public Date getCreationDate() {
        return this.creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    @Column(name = "CREATED_BY", nullable = false)
    public int getCreatedBy() {
        return this.createdBy;
    }

    public void setCreatedBy(int createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "HAS_INNER", nullable = false, length = 1)
    public String getHasInner() {
        return this.hasInner;
    }

    public void setHasInner(String hasInner) {
        this.hasInner = hasInner;
    }

    @Column(name = "HAS_CASE", nullable = false, length = 1)
    public String getHasCase() {
        return this.hasCase;
    }

    public void setHasCase(String hasCase) {
        this.hasCase = hasCase;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "LINKED_PRODUCT_ID", nullable = false)
    public ProductDefinitionData getLinkedProduct() {
        return this.linkedProduct;
    }

    public void setLinkedProduct(ProductDefinitionData linkedProduct) {
        this.linkedProduct = linkedProduct;
    }

    @Column(name = "SHELF_LIFE_IN_DAYS", nullable = false)
    public int getShelfLifeInDays() {
        return this.shelfLifeInDays;
    }

    public void setShelfLifeInDays(int shelfLifeInDays) {
        this.shelfLifeInDays = shelfLifeInDays;
    }

    @Column(name = "SKU_STATUS", nullable = false, length = 15)
    public String getSkuStatus() {
        return this.skuStatus;
    }

    public void setSkuStatus(String skuStatus) {
        this.skuStatus = skuStatus;
    }

    @Column(name = "UNIT_OF_MEASURE", nullable = false, length = 15)
    public String getUnitOfMeasure() {
        return this.unitOfMeasure;
    }

    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }

    @Column(name = "SKU_IMAGE", nullable = true, length = 255)
    public String getSkuImage() {
        return skuImage;
    }

    public void setSkuImage(String skuImage) {
        this.skuImage = skuImage;
    }

    @Column(name = "UNIT_PRICE", nullable = true)
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    @Column(name = "NEGOTIATED_UNIT_PRICE", nullable = true)
    public BigDecimal getNegotiatedUnitPrice() {
        return negotiatedUnitPrice;
    }

    public void setNegotiatedUnitPrice(BigDecimal negotiatedUnitPrice) {
        this.negotiatedUnitPrice = negotiatedUnitPrice;
    }

    @Column(name = "PRICE_LAST_UPDATED", nullable = true)
    public Date getPriceLastUpdated() {
        return priceLastUpdated;
    }

    public void setPriceLastUpdated(Date priceLastUpdated) {
        this.priceLastUpdated = priceLastUpdated;
    }

    @Column(name = "TORQUS_ID", nullable = true)
    public String getTorqusSKU() {
        return torqusSKU;
    }

    public void setTorqusSKU(String torqusSKU) {
        this.torqusSKU = torqusSKU;
    }

    @Column(name = "IS_DEFAULT", nullable = false, length = 1)
    public String getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(String isDefault) {
        this.isDefault = isDefault;
    }

    @Column(name = "INVENTORY_LIST_ID", nullable = true)
    public Integer getInventoryList() {
        return inventoryList;
    }

    public void setInventoryList(Integer inventoryList) {
        this.inventoryList = inventoryList;
    }

    @Column(name = "SKU_CODE")
    public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    @Column(name = "TAX_CATEGORY_CODE", nullable = false, length = 30)
    public String getTaxCategoryCode() {
        return this.taxCategoryCode;
    }

    public void setTaxCategoryCode(String hsnCode) {
        this.taxCategoryCode = hsnCode;
    }

    @Column(name = "VO_DISCONTINUED_FROM", nullable = true)
    public Date getVoDisContinuedFrom() {
        return voDisContinuedFrom;
    }

    public void setVoDisContinuedFrom(Date voDisContinuedFrom) {
        this.voDisContinuedFrom = voDisContinuedFrom;
    }

    @Column(name = "RO_DISCONTINUED_FROM", nullable = true)
    public Date getRoDisContinuedFrom() {
        return roDisContinuedFrom;
    }

    public void setRoDisContinuedFrom(Date roDisContinuedFrom) {
        this.roDisContinuedFrom = roDisContinuedFrom;
    }

    @Column(name = "IS_BRANDED", nullable = true)
    public String getIsBranded() { return isBranded; }

    public void setIsBranded(String isBranded) {this.isBranded = isBranded;}

    @Column(name = "BRAND" , nullable = true)
    public String getBrand() {
        return this.brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    @Column(name = "APPROVAL_DOC_ID")
    public Integer getApprovalDocId() {
        return approvalDocId;
    }
    public void setApprovalDocId(Integer approvalDocId) {
        this.approvalDocId = approvalDocId;
    }

    @Column(name = "USER_SKU_CREATION_ID")
    public Integer getUserSkuCreationId() {
        return userSkuCreationId;
    }
    public void setUserSkuCreationId(Integer userSkuCreationId) {
        this.userSkuCreationId = userSkuCreationId;
    }

}

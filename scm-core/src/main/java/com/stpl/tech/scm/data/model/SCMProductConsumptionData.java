/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.stpl.tech.scm.core.util.SCMUtil;

@Entity
@Table(name = "DAY_CLOSE_PRODUCT_VALUES")
public class SCMProductConsumptionData {
	private Integer productConsumptionId;
	private int productId;
	private int unitId;
	private Date businessDate;
	private BigDecimal taxableConsumption;
	private BigDecimal taxableAmount;
	private BigDecimal taxPercentage;
	private BigDecimal taxAmount;
	private BigDecimal consumption;
	private BigDecimal consumptionPrice;
	private BigDecimal consumptionCost;
	private BigDecimal transferOut;
	private BigDecimal transferOutPrice;
	private BigDecimal transferOutCost;
	private BigDecimal wastage;
	private BigDecimal wastagePrice;
	private BigDecimal wastageCost;
	private BigDecimal received;
	private BigDecimal receivedPrice;
	private BigDecimal receivedCost;
	private String unitOfMeasure;
	private SCMDayCloseEventData eventId;
	private String stockType;
	private BigDecimal varianceTillNow;
	private BigDecimal expiredWastage;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "CALCULATED_PRODUCT_ID", unique = true, nullable = false)
	public Integer getProductConsumptionId() {
		return productConsumptionId;
	}

	public void setProductConsumptionId(Integer productConsumptionId) {
		this.productConsumptionId = productConsumptionId;
	}

	@Column(name = "PRODUCT_ID", nullable = false)
	public int getProductId() {
		return productId;
	}

	public void setProductId(int productId) {
		this.productId = productId;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "BUSINESS_DATE", nullable = false)
	public Date getBusinessDate() {
		return businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	@Column(name = "CONSUMPTION")
	public BigDecimal getConsumption() {
		return consumption;
	}

	public void setConsumption(BigDecimal consumption) {
		this.consumption = SCMUtil.convertToBigDecimal(consumption).setScale(6,RoundingMode.HALF_UP);
	}

	@Column(name = "TRANSFER_OUT")
	public BigDecimal getTransferOut() {
		return transferOut;
	}

	public void setTransferOut(BigDecimal transferOut) {
		this.transferOut = SCMUtil.convertToBigDecimal(transferOut).setScale(6,RoundingMode.HALF_UP);
	}

	@Column(name = "WASTAGE")
	public BigDecimal getWastage() {
		return wastage;
	}

	public void setWastage(BigDecimal wastage) {
		this.wastage = SCMUtil.convertToBigDecimal(wastage).setScale(6,RoundingMode.HALF_UP);
	}

	@Column(name = "RECEIVED")
	public BigDecimal getReceived() {
		return received;
	}

	public void setReceived(BigDecimal received) {
		this.received = SCMUtil.convertToBigDecimal(received).setScale(6,RoundingMode.HALF_UP);
	}

	@Column(name = "UOM", nullable = false)
	public String getUnitOfMeasure() {
		return unitOfMeasure;
	}

	public void setUnitOfMeasure(String unitOfMeasure) {
		this.unitOfMeasure = unitOfMeasure;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "EVENT_ID", nullable = false)
	public SCMDayCloseEventData getEventId() {
		return eventId;
	}

	public void setEventId(SCMDayCloseEventData eventId) {
		this.eventId = eventId;
	}

	@Column(name = "CONSUMPTION_PRICE")
	public BigDecimal getConsumptionPrice() {
		return consumptionPrice;
	}

	public void setConsumptionPrice(BigDecimal consumptionPrice) {
		this.consumptionPrice = SCMUtil.convertToBigDecimal(consumptionPrice).setScale(6, RoundingMode.HALF_UP);;
	}

	@Column(name = "CONSUMPTION_COST")
	public BigDecimal getConsumptionCost() {
		return consumptionCost;
	}

	public void setConsumptionCost(BigDecimal consumptionCost) {
		this.consumptionCost = SCMUtil.convertToBigDecimal(consumptionCost).setScale(6, RoundingMode.HALF_UP);
	}

	@Column(name = "TRANSFER_OUT_PRICE")
	public BigDecimal getTransferOutPrice() {
		return transferOutPrice;
	}

	public void setTransferOutPrice(BigDecimal transferOutPrice) {
		this.transferOutPrice = SCMUtil.convertToBigDecimal(transferOutPrice).setScale(6, RoundingMode.HALF_UP);
	}

	@Column(name = "TRANSFER_OUT_COST")
	public BigDecimal getTransferOutCost() {
		return transferOutCost;
	}

	public void setTransferOutCost(BigDecimal transferOutCost) {
		this.transferOutCost = SCMUtil.convertToBigDecimal(transferOutCost).setScale(6, RoundingMode.HALF_UP);
	}

	@Column(name = "WASTAGE_PRICE")
	public BigDecimal getWastagePrice() {
		return wastagePrice;
	}

	public void setWastagePrice(BigDecimal wastagePrice) {
		this.wastagePrice = SCMUtil.convertToBigDecimal(wastagePrice).setScale(6, RoundingMode.HALF_UP);
	}

	@Column(name = "WASTAGE_COST")
	public BigDecimal getWastageCost() {
		return wastageCost;
	}

	public void setWastageCost(BigDecimal wastageCost) {
		this.wastageCost = SCMUtil.convertToBigDecimal(wastageCost).setScale(6, RoundingMode.HALF_UP);
	}

	@Column(name = "RECEIVED_PRICE")
	public BigDecimal getReceivedPrice() {
		return receivedPrice;
	}

	public void setReceivedPrice(BigDecimal receivedPrice) {
		this.receivedPrice = SCMUtil.convertToBigDecimal(receivedPrice).setScale(6, RoundingMode.HALF_UP);
	}

	@Column(name = "RECEIVED_COST")
	public BigDecimal getReceivedCost() {
		return receivedCost;
	}

	public void setReceivedCost(BigDecimal receivedCost) {
		this.receivedCost = SCMUtil.convertToBigDecimal(receivedCost).setScale(6, RoundingMode.HALF_UP);
	}

    @Column(name = "STOCK_TYPE")
    public String getStockType() {
        return stockType;
    }

    public void setStockType(String stockType) {
        this.stockType = stockType;
    }

	@Column(name = "TAXABLE_CONSUMPTION")
	public BigDecimal getTaxableConsumption() {
		return taxableConsumption;
	}

	public void setTaxableConsumption(BigDecimal taxableConsumption) {
		this.taxableConsumption = taxableConsumption;
	}

	@Column(name = "TAXABLE_AMOUNT")
	public BigDecimal getTaxableAmount() {
		return taxableAmount;
	}

	public void setTaxableAmount(BigDecimal taxableAmount) {
		this.taxableAmount = SCMUtil.convertToBigDecimal(taxableAmount).setScale(6, RoundingMode.HALF_UP);
	}

	@Column(name = "TAX_PERCENTAGE")
	public BigDecimal getTaxPercentage() {
		return taxPercentage;
	}

	public void setTaxPercentage(BigDecimal taxPercentage) {
		this.taxPercentage = taxPercentage;
	}

	@Column(name = "TAX_AMOUNT")
	public BigDecimal getTaxAmount() {
		return taxAmount;
	}

	public void setTaxAmount(BigDecimal taxAmount) {
		this.taxAmount = SCMUtil.convertToBigDecimal(taxAmount).setScale(6, RoundingMode.HALF_UP);
	}

	@Transient
	public BigDecimal getVarianceTillNow() {
		return varianceTillNow;
	}

	public void setVarianceTillNow(BigDecimal varianceTillNow) {
		this.varianceTillNow = varianceTillNow;
	}

	@Transient
	public BigDecimal getExpiredWastage() {
		return expiredWastage;
	}

	public void setExpiredWastage(BigDecimal expiredWastage) {
		this.expiredWastage = expiredWastage;
	}
}

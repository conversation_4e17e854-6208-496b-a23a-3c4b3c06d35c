package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.data.model.KettleStockOutDateWiseData;
import com.stpl.tech.scm.data.model.KettleStockOutTimingsData;
import com.stpl.tech.scm.data.model.KettleProductDataClone;
import com.stpl.tech.scm.data.model.KettleUnitDetailDataClone;

import java.util.Date;
import java.util.List;
import java.util.Set;


public interface KettleStockOutDao extends SCMAbstractDao {

    <T> void insertList(List<T> entries);

    <T> void updateList(List<T> entries);

    void clearAndCreateProductDataCloneTable();

    void clearAndCreateUnitDetailDataCloneTable();

    List<KettleStockOutDateWiseData> getKettleStockOutDayWiseData();

    List<KettleStockOutTimingsData> getKettleStockOutTimingsData(Set<Integer> integers);

    List<KettleUnitDetailDataClone> getKettleUnitDetailDataClone();

    List<KettleProductDataClone> getKettleProductDataClone();

    List<KettleStockOutDateWiseData> findByUnitIdAndBusinessDates(Integer unitId, List<Date> businessDates);
}

package com.stpl.tech.scm.core.config;

import org.springframework.core.convert.converter.Converter;
import org.springframework.data.convert.ReadingConverter;
import org.springframework.data.convert.WritingConverter;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;


@Component
@WritingConverter
public class DateToBytesConverter implements Converter<Timestamp, byte[]> {

    @Override
    public byte[] convert(Timestamp source) {
        long timestampValue = source.getTime();
        String stringValue = String.valueOf(timestampValue);
        return stringValue.getBytes();
    }
}

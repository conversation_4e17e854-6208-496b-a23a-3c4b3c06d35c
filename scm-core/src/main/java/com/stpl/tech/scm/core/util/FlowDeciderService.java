package com.stpl.tech.scm.core.util;

import com.amazonaws.util.CollectionUtils;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.data.model.PurchaseOrderData;
import com.stpl.tech.scm.data.model.PurchaseOrderVendorGRMappingData;
import com.stpl.tech.scm.data.model.VendorGoodsReceivedData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class FlowDeciderService {

    private final EnvProperties envProperties;

    private LocalDate getNewBudgetFlowDeployedDate() {
        String deployedDate = envProperties.getProductWiseBudgetDeductionDeploymentDate();
        if (deployedDate == null || deployedDate.isEmpty()) {
            return null;
        }
        try {
            return LocalDate.parse(deployedDate, DateTimeFormatter.ofPattern("dd-MM-yyyy"));
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid deployment date format: " + deployedDate, e);
        }
    }

    @Deprecated
    public boolean shouldUseNewBudgetFlow(VendorGoodsReceivedData vendorGR) {
        if(vendorGR == null) {
            log.info("Vendor GR is null");
            return true;
        }
        List<PurchaseOrderVendorGRMappingData> poMappingList = vendorGR.getPoMappingList();

        if(CollectionUtils.isNullOrEmpty(poMappingList)) {
            log.info("PO Mapping list is null or empty");
            return true;
        }

        PurchaseOrderData po = poMappingList.get(0).getPurchaseOrderData();
        return shouldUseNewBudgetFlow(po);
    }

    @Deprecated
    public boolean shouldUseNewBudgetFlow(PurchaseOrderData po) {
        if (po == null || po.getGenerationTime() == null) {
            return true;
        }

        LocalDate poDate = po.getGenerationTime()
                .toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        return shouldUseNewBudgetFlow(poDate);
    }

    @Deprecated
    public boolean shouldUseNewBudgetFlow(LocalDate poDate) {
        LocalDate deployedDate = getNewBudgetFlowDeployedDate();
        if (deployedDate == null) {
            return true;
        }
        // Compare: PO date >= deployed date → new flow
        return !poDate.isBefore(deployedDate);
    }

}

package com.stpl.tech.scm.data.model;

import javax.persistence.*;

@Entity
@Table(name = "NON_SCANNABLE_ASSET_PRODUCTS")
public class NonScannableAssetProducts {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "NON_SCANNABLE_ASSET_PRODUCTS_ID")
    private Integer nonScannableAssetProductsId;

    @Column(name = "PRODUCT_ID")
    private Integer productId;

    @Column(name = "MAPPING_STATUS")
    private String mappingStatus;

    public Integer getNonScannableAssetProductsId() {
        return this.nonScannableAssetProductsId;
    }

    public void setNonScannableAssetProductsId(Integer nonScannableAssetProductsId) {
        this.nonScannableAssetProductsId = nonScannableAssetProductsId;
    }

    public Integer getProductId() {
        return this.productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getMappingStatus() {
        return this.mappingStatus;
    }

    public void setMappingStatus(String mappingStatus) {
        this.mappingStatus = mappingStatus;
    }
}

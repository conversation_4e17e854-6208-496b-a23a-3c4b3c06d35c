package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "GATEPASS_DATA")
public class GatepassData {

	private Integer id;
	private Integer vendorId;
	private Integer dispatchLocationId;
	private String operationType;
	private String returnable;
	private Integer expectedReturn;
	private String status;
	private String returnStatus;
	private BigDecimal totalCost;
	private BigDecimal totalTax;
	private BigDecimal additionalCharges;
	private String comment;
	private String reason;
	private Integer createdBy;
	private Date createdAt;
	private int sendingUnit;
	private int sendingCompany;
	private String needsApproval;
	private Date issueDate;
	private String hasLoss;
	private Integer cancelledBy;
	private Date cancelledAt;
	private List<GatepassItemData> itemDatas = new ArrayList<GatepassItemData>();
	private String assetGatePass;
    private Integer approvalRequestedTo;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "GATEPASS_ID", nullable = false, unique = true)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "VENDOR_ID", nullable = false)
	public Integer getVendorId() {
		return vendorId;
	}

	public void setVendorId(Integer vendorId) {
		this.vendorId = vendorId;
	}

	@Column(name = "DISPATCH_LOCATION_ID", nullable = false)
	public Integer getDispatchLocationId() {
		return dispatchLocationId;
	}

	public void setDispatchLocationId(Integer dispatchLocationId) {
		this.dispatchLocationId = dispatchLocationId;
	}

	@Column(name = "OPERATION_TYPE", nullable = false)
	public String getOperationType() {
		return operationType;
	}

	public void setOperationType(String operationType) {
		this.operationType = operationType;
	}

	@Column(name = "RETURNABLE", nullable = false)
	public String getReturnable() {
		return returnable;
	}

	public void setReturnable(String returnable) {
		this.returnable = returnable;
	}

	@Column(name = "EXPECTED_RETURN", nullable = true)
	public Integer getExpectedReturn() {
		return expectedReturn;
	}

	public void setExpectedReturn(Integer expectedReturn) {
		this.expectedReturn = expectedReturn;
	}

	@Column(name = "STATUS", nullable = false)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "RETURN_STATUS", nullable = true)
	public String getReturnStatus() {
		return returnStatus;
	}

	public void setReturnStatus(String returnStatus) {
		this.returnStatus = returnStatus;
	}

	@Column(name = "TOTAL_COST", nullable = false)
	public BigDecimal getTotalCost() {
		return totalCost;
	}

	public void setTotalCost(BigDecimal totalCost) {
		this.totalCost = totalCost;
	}

	@Column(name = "TOTAL_TAX", nullable = false)
	public BigDecimal getTotalTax() {
		return totalTax;
	}

	public void setTotalTax(BigDecimal totalTax) {
		this.totalTax = totalTax;
	}

	@Column(name = "ADDITIONAL_CHARGES", nullable = false)
	public BigDecimal getAdditionalCharges() {
		return additionalCharges;
	}

	public void setAdditionalCharges(BigDecimal additionalCharges) {
		this.additionalCharges = additionalCharges;
	}

	@Column(name = "COMMENT", nullable = true)
	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	@Column(name = "REASON", nullable = true)
	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	@Column(name = "CREATED_BY", nullable = false)
	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	@Column(name = "CREATED_AT", nullable = false)
	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	@Column(name = "SENDING_UNIT", nullable = false)
	public int getSendingUnit() {
		return sendingUnit;
	}

	public void setSendingUnit(int sendingUnit) {
		this.sendingUnit = sendingUnit;
	}

	@Column(name = "SENDING_COMPANY", nullable = false)
	public int getSendingCompany() {
		return sendingCompany;
	}

	public void setSendingCompany(int sendingCompany) {
		this.sendingCompany = sendingCompany;
	}

	@Column(name = "NEEDS_APPROVAL", nullable = false)
	public String getNeedsApproval() {
		return needsApproval;
	}

	public void setNeedsApproval(String needsApproval) {
		this.needsApproval = needsApproval;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "ISSUE_DATE", nullable = false)
	public Date getIssueDate() {
		return issueDate;
	}

	public void setIssueDate(Date issueDate) {
		this.issueDate = issueDate;
	}

	@Column(name = "HAS_LOSS", nullable = false)
	public String getHasLoss() {
		return hasLoss;
	}

	public void setHasLoss(String hasLoss) {
		this.hasLoss = hasLoss;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "gatepassData", cascade = CascadeType.PERSIST)
	public List<GatepassItemData> getItemDatas() {
		return itemDatas;
	}

	public void setItemDatas(List<GatepassItemData> itemDatas) {
		this.itemDatas = itemDatas;
	}

	@Column(name = "CANCELLED_BY", nullable = true)
	public Integer getCancelledBy() {
		return cancelledBy;
	}

	public void setCancelledBy(Integer cancelledBy) {
		this.cancelledBy = cancelledBy;
	}

	@Column(name = "CANCELLED_AT", nullable = true)
	public Date getCancelledAt() {
		return cancelledAt;
	}

	public void setCancelledAt(Date cancelledAt) {
		this.cancelledAt = cancelledAt;
	}

	@Column(name = "IS_ASSET_GATE_PASS")
	public String getAssetGatePass() {
		return assetGatePass;
	}

	public void setAssetGatePass(String assetGatePass) {
		this.assetGatePass = assetGatePass;
	}

    @Column(name = "APPROVAL_REQUESTED_TO")
    public Integer getApprovalRequestedTo() {
        return approvalRequestedTo;
    }
    public void setApprovalRequestedTo(Integer approvalRequestedTo) {
        this.approvalRequestedTo = approvalRequestedTo;
    }

}

package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.scm.data.model.ServiceReceivedData;
import com.stpl.tech.scm.domain.model.SREmailShort;
import com.stpl.tech.scm.domain.model.ServiceOrderEmailShort;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import org.apache.velocity.tools.generic.DateTool;
import org.apache.velocity.tools.generic.MathTool;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SrEmailNotificationTemplate extends AbstractVelocityTemplate {

    private ServiceReceivedData serviceReceivedData;
    private String createdBy;
    private String basePath;
    private String company;
    private EnvType envType;
    private Map<Integer,List<SREmailShort>> soSrMap;
    private Map<Integer, ServiceOrderEmailShort> soDataMap;
    private String subjectOfEmail;

    public SrEmailNotificationTemplate() {
    }

    public SrEmailNotificationTemplate(ServiceReceivedData serviceReceivedData, String createdBy, String basePath, String company, EnvType envType,
                                       Map<Integer, List<SREmailShort>> soSrMap, Map<Integer, ServiceOrderEmailShort> soDataMap, String subjectOfEmail) {
        this.serviceReceivedData = serviceReceivedData;
        this.createdBy = createdBy;
        this.basePath = basePath;
        this.company = company;
        this.envType = envType;
        this.soSrMap = soSrMap;
        this.soDataMap = soDataMap;
        this.subjectOfEmail = subjectOfEmail;
    }

    @Override
    public String getTemplatePath() {
        return "templates/SRNotificationTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/serviceRecievings"
                + serviceReceivedData.getServiceReceivedId() + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("serviceReceivedData", serviceReceivedData);
        stringObjectMap.put("createdBy", createdBy);
        stringObjectMap.put("basePath", basePath);
        stringObjectMap.put("company", company);
        stringObjectMap.put("envType", envType);
        stringObjectMap.put("mathTool", new MathTool());
        stringObjectMap.put("dateTool", new DateTool());
        stringObjectMap.put("soSrMap", soSrMap);
        stringObjectMap.put("soDataMap", soDataMap);
        return stringObjectMap;
    }

    public String getSubjectOfEmail() {
        return subjectOfEmail;
    }
}

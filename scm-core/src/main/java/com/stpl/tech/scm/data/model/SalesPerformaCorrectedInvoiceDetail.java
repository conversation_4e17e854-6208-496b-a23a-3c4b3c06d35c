package com.stpl.tech.scm.data.model;

import javax.persistence.Entity;
import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;

import java.math.BigDecimal;
import java.util.List;

@Entity
@Table(name = "SALES_PERFORMA_CORRECTED_INVOICE")
public class SalesPerformaCorrectedInvoiceDetail {

    private Integer invoiceId;
    private BigDecimal additionalCharges;
    private BigDecimal totalAmount;
    private BigDecimal totalCorrectedAmount;
    private BigDecimal totalSellingCost;
    private BigDecimal totalCorrectedSellingCost;
    private BigDecimal totalTax;
    private BigDecimal totalCorrectedTax;
    private List<SalesPerformaCorrectedInvoiceItemDetail> correctedInvoiceItems;


    @Id
    @Column(name = "INVOICE_ID", nullable = false, unique = true)
    public Integer getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Integer invoiceId) {
        this.invoiceId = invoiceId;
    }

    @Column(name = "ADDITIONAL_CHARGES", nullable = false)
    public BigDecimal getAdditionalCharges() {
        return additionalCharges;
    }

    public void setAdditionalCharges(BigDecimal additionalCharges) {
        this.additionalCharges = additionalCharges;
    }

    @Column(name = "TOTAL_AMOUNT", nullable = false)
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    @Column(name = "TOTAL_CORRECTED_AMOUNT", nullable = false)
    public BigDecimal getTotalCorrectedAmount() {
        return totalCorrectedAmount;
    }

    public void setTotalCorrectedAmount(BigDecimal totalCorrectedAmount) {
        this.totalCorrectedAmount = totalCorrectedAmount;
    }

    @Column(name = "TOTAL_SELLING_COST", nullable = false)
    public BigDecimal getTotalSellingCost() {
        return totalSellingCost;
    }

    public void setTotalSellingCost(BigDecimal totalSellingCost) {
        this.totalSellingCost = totalSellingCost;
    }

    @Column(name = "TOTAL_CORRECTED_SELLING_COST", nullable = false)
    public BigDecimal getTotalCorrectedSellingCost() {
        return totalCorrectedSellingCost;
    }

    public void setTotalCorrectedSellingCost(BigDecimal totalCorrectedSellingCost) {
        this.totalCorrectedSellingCost = totalCorrectedSellingCost;
    }

    @Column(name = "TOTAL_TAX", nullable = false)
    public BigDecimal getTotalTax() {
        return totalTax;
    }

    public void setTotalTax(BigDecimal totalTax) {
        this.totalTax = totalTax;
    }

    @Column(name = "TOTAL_CORRECTED_TAX", nullable = false)
    public BigDecimal getTotalCorrectedTax() {
        return totalCorrectedTax;
    }

    public void setTotalCorrectedTax(BigDecimal totalCorrectedTax) {
        this.totalCorrectedTax = totalCorrectedTax;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "correctedInvoice")
    public List<SalesPerformaCorrectedInvoiceItemDetail> getCorrectedInvoiceItems() {
        return correctedInvoiceItems;
    }

    public void setCorrectedInvoiceItems(List<SalesPerformaCorrectedInvoiceItemDetail> correctedInvoiceItems) {
        this.correctedInvoiceItems = correctedInvoiceItems;
    }
}

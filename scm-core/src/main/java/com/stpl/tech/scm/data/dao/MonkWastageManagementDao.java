package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.data.model.MonkWastageDetailData;
import com.stpl.tech.scm.data.model.DuplicateMonkWastageDetailData;
import com.stpl.tech.scm.domain.model.MonkWastageProcessingEnum;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface MonkWastageManagementDao extends SCMAbstractDao {
    
    /**
     * Save monk wastage detail data
     * @param wastageDetailData the wastage detail to save
     * @return saved wastage detail data
     */
    MonkWastageDetailData saveMonkWastageDetail(MonkWastageDetailData wastageDetailData);
    
    /**
     * Get all unprocessed monk wastage details for a specific unit
     * @param unitId the unit ID
     * @return list of unprocessed wastage details
     */
    List<MonkWastageDetailData> getUnprocessedWastageDetails(Integer unitId);
    
    /**
     * Get all unprocessed monk wastage details grouped by unit
     * @return map of unit ID to list of unprocessed wastage details
     */
    Map<Integer, List<MonkWastageDetailData>> getAllUnprocessedWastageDetailsGroupedByUnit();
    
    /**
     * Get unprocessed monk wastage details grouped by unit with optional unit filter
     * @param unitId the unit ID to filter by, or null to get all units
     * @return map of unit ID to list of unprocessed wastage details
     */
    Map<Integer, List<MonkWastageDetailData>> getUnprocessedWastageDetailsGroupedByUnit(Integer unitId);
    
    /**
     * Update both wastage item ID and processing status for processed entries
     * @param wastageDetailIds list of wastage detail IDs
     * @param wastageItemId the wastage item ID to set
     * @param status the processing status to set
     */
    void updateWastageItemIdAndProcessingStatus(List<Integer> wastageDetailIds, Integer wastageItemId, MonkWastageProcessingEnum status);
    
    /**
     * Find the latest monk wastage detail entry for a given task ID before the current timestamp
     * @param taskId the task ID to search for
     * @param currentLogTime the current log time to exclude
     * @return the latest wastage detail before current time, or null if not found
     */
    MonkWastageDetailData findLatestWastageDetailWithErrorAndRemake(Integer taskId, Date currentLogTime);
    
    /**
     * Save duplicate monk wastage detail data
     * @param duplicateWastageDetailData the duplicate wastage detail to save
     * @return saved duplicate wastage detail data
     */
    DuplicateMonkWastageDetailData saveDuplicateMonkWastageDetail(DuplicateMonkWastageDetailData duplicateWastageDetailData);
} 
package com.stpl.tech.scm.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 16-07-2018.
 */

@Entity
@Table(name = "SALES_PERFORMA_ITEM_DRILLDOWN")
public class SalesPerformaItemDrilldown {

    private Integer drilldownId;
    private SalesPerformaInvoiceItemData invoiceItem;
    private BigDecimal quantity;
    private BigDecimal price;
    private Date addTime;
    private Date expiryDate;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "DRILLDOWN_ID", unique = true, nullable = false)
    public Integer getDrilldownId() {
        return drilldownId;
    }

    public void setDrilldownId(Integer drilldownId) {
        this.drilldownId = drilldownId;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "INVOICE_ITEM_ID", nullable = false)
    public SalesPerformaInvoiceItemData getInvoiceItem() {
        return invoiceItem;
    }

    public void setInvoiceItem(SalesPerformaInvoiceItemData invoiceItem) {
        this.invoiceItem = invoiceItem;
    }


    @Column(name = "QUANTITY", precision = 16)
    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    @Column(name = "PRICE", precision = 16)
    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "ADD_TIME", nullable = false, length = 19)
    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "EXPIRY_DATE", nullable = false, length = 10)
    public Date getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }
}

/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "MILK_BREAD_BYPASS")
public class MilkBreadBypassData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MILK_BREAD_BYPASS_ID", unique = true, nullable = false)
    private Integer milkBreadBypassId;

    @Column(name = "UNIT_ID")
    private Integer unitId;

    @Column(name = "BYPASS_REASON")
    private String bypassReason;

    @Column(name = "COMMENT")
    private String comment;

    @Column(name = "RO_IDs")
    private String roIDs;

    @Column(name = "MAX_ALLOWED_TIME")
    private Date maxAllowedTime;

    @Column(name = "BYPASSED_BY")
    private String bypassedBy;

    @Column(name = "BYPASSED_TIME")
    private Date bypassedTime;

    public Integer getMilkBreadBypassId() {
        return this.milkBreadBypassId;
    }

    public void setMilkBreadBypassId(Integer milkBreadBypassId) {
        this.milkBreadBypassId = milkBreadBypassId;
    }

    public Integer getUnitId() {
        return this.unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getBypassReason() {
        return this.bypassReason;
    }

    public void setBypassReason(String bypassReason) {
        this.bypassReason = bypassReason;
    }

    public String getComment() {
        return this.comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getRoIDs() {
        return this.roIDs;
    }

    public void setRoIDs(String roIDs) {
        this.roIDs = roIDs;
    }

    public Date getMaxAllowedTime() {
        return maxAllowedTime;
    }

    public void setMaxAllowedTime(Date maxAllowedTime) {
        this.maxAllowedTime = maxAllowedTime;
    }

    public String getBypassedBy() {
        return this.bypassedBy;
    }

    public void setBypassedBy(String bypassedBy) {
        this.bypassedBy = bypassedBy;
    }

    public Date getBypassedTime() {
        return bypassedTime;
    }

    public void setBypassedTime(Date bypassedTime) {
        this.bypassedTime = bypassedTime;
    }
}

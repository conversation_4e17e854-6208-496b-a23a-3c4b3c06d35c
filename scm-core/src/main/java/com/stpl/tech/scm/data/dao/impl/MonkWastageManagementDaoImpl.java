package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.scm.data.dao.MonkWastageManagementDao;
import com.stpl.tech.scm.data.model.MonkWastageDetailData;
import com.stpl.tech.scm.data.model.DuplicateMonkWastageDetailData;
import com.stpl.tech.scm.domain.model.MonkWastageProcessingEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Repository
public class MonkWastageManagementDaoImpl extends SCMAbstractDaoImpl implements MonkWastageManagementDao {

    @Override
    public MonkWastageDetailData saveMonkWastageDetail(MonkWastageDetailData wastageDetailData) {
        try {
            // Set default processing status if not set
            if (wastageDetailData.getIsProcessed() == null) {
                wastageDetailData.setIsProcessed(MonkWastageProcessingEnum.NOT_PROCESSED);
            }

            return add(wastageDetailData, true);
        } catch (Exception e) {
            log.error("Error saving monk wastage detail data: ", e);
            throw new RuntimeException("Failed to save monk wastage detail", e);
        }
    }

    @Override
    public List<MonkWastageDetailData> getUnprocessedWastageDetails(Integer unitId) {
        try {
            Query query = manager.createQuery(
                    "SELECT m FROM MonkWastageDetailData m " +
                            "WHERE m.unitId = :unitId " +
                            "AND m.isProcessed = :status " +
                            "ORDER BY m.id ASC"
            );

            query.setParameter("unitId", unitId);
            query.setParameter("status", MonkWastageProcessingEnum.NOT_PROCESSED);

            @SuppressWarnings("unchecked")
            List<MonkWastageDetailData> results = (List<MonkWastageDetailData>) query.getResultList();
            return results;
        } catch (Exception e) {
            log.error("Error getting unprocessed wastage details for unit: {}", unitId, e);
            throw new RuntimeException("Failed to get unprocessed wastage details", e);
        }
    }

    @Override
    public Map<Integer, List<MonkWastageDetailData>> getAllUnprocessedWastageDetailsGroupedByUnit() {
        try {
            Query query = manager.createQuery(
                    "SELECT m FROM MonkWastageDetailData m " +
                            "WHERE m.isProcessed = :status " +
                            "ORDER BY m.unitId ASC, m.id ASC"
            );

            query.setParameter("status", MonkWastageProcessingEnum.NOT_PROCESSED);

            @SuppressWarnings("unchecked")
            List<MonkWastageDetailData> allUnprocessedDetails = (List<MonkWastageDetailData>) query.getResultList();

            // Group by unit ID
            Map<Integer, List<MonkWastageDetailData>> groupedByUnit = allUnprocessedDetails.stream()
                    .collect(Collectors.groupingBy(MonkWastageDetailData::getUnitId));

            log.info("Found unprocessed wastage details for {} units", groupedByUnit.size());
            return groupedByUnit;
        } catch (Exception e) {
            log.error("Error getting all unprocessed wastage details grouped by unit: ", e);
            throw new RuntimeException("Failed to get all unprocessed wastage details", e);
        }
    }

    @Override
    public Map<Integer, List<MonkWastageDetailData>> getUnprocessedWastageDetailsGroupedByUnit(Integer unitId) {
        try {
            StringBuilder queryString = new StringBuilder(
                    "SELECT m FROM MonkWastageDetailData m " +
                            "WHERE m.isProcessed = :status "
            );
            
            if (unitId != null) {
                queryString.append("AND m.unitId = :unitId ");
            }
            
            queryString.append("ORDER BY m.unitId ASC, m.id ASC");

            Query query = manager.createQuery(queryString.toString());
            query.setParameter("status", MonkWastageProcessingEnum.NOT_PROCESSED);
            
            if (unitId != null) {
                query.setParameter("unitId", unitId);
            }

            @SuppressWarnings("unchecked")
            List<MonkWastageDetailData> unprocessedDetails = (List<MonkWastageDetailData>) query.getResultList();

            // Group by unit ID
            Map<Integer, List<MonkWastageDetailData>> groupedByUnit = unprocessedDetails.stream()
                    .collect(Collectors.groupingBy(MonkWastageDetailData::getUnitId));

            log.info("Found unprocessed wastage details for {} units{}", 
                    groupedByUnit.size(), 
                    unitId != null ? " (filtered by unit: " + unitId + ")" : "");
            return groupedByUnit;
        } catch (Exception e) {
            log.error("Error getting unprocessed wastage details grouped by unit{}: ", 
                    unitId != null ? " for unit: " + unitId : "", e);
            throw new RuntimeException("Failed to get unprocessed wastage details", e);
        }
    }

    @Override
    public void updateWastageItemIdAndProcessingStatus(List<Integer> wastageDetailIds, Integer wastageItemId, MonkWastageProcessingEnum status) {
        try {
            Query query = manager.createQuery(
                    "UPDATE MonkWastageDetailData m " +
                            "SET m.wastageData = :wastageItemId, m.isProcessed = :status " +
                            "WHERE m.id IN :ids"
            );

            query.setParameter("wastageItemId", wastageItemId);
            query.setParameter("status", status);
            query.setParameter("ids", wastageDetailIds);

            int updatedCount = query.executeUpdate();
            manager.flush();

            log.info("Updated wastage item ID and processing status for {} monk wastage details", updatedCount);
        } catch (Exception e) {
            log.error("Error updating wastage item ID and processing status for wastage details: {}", wastageDetailIds, e);
            throw new RuntimeException("Failed to update wastage item ID and processing status", e);
        }
    }

    @Override
    public MonkWastageDetailData findLatestWastageDetailWithErrorAndRemake(Integer taskId, Date currentLogTime) {
        try {
            Query query = manager.createQuery(
                    "SELECT m FROM MonkWastageDetailData m " +
                            "WHERE m.taskId = :taskId " +
                            "AND m.logAddTime < :currentLogTime " +
                            "ORDER BY m.logAddTime DESC"
            );

            query.setParameter("taskId", taskId);
            query.setParameter("currentLogTime", currentLogTime);
            query.setMaxResults(1);

            List<MonkWastageDetailData> results = query.getResultList();
            
            if (results.isEmpty()) {
                log.info("No wastage detail found with error code and remake event for task ID: {}", taskId);
                return null;
            }
            
            MonkWastageDetailData latestEntry = results.get(0);
            log.info("Found latest wastage detail with error code and remake event for task ID: {}, chai monk: {}",
                    taskId, latestEntry.getChaiMonk());
            return latestEntry;
        } catch (Exception e) {
            log.error("Error finding latest wastage detail with error and remake for task ID: {}", taskId, e);
            throw new RuntimeException("Failed to find latest wastage detail with error and remake", e);
        }
    }

    @Override
    public DuplicateMonkWastageDetailData saveDuplicateMonkWastageDetail(DuplicateMonkWastageDetailData duplicateWastageDetailData) {
        try {
            // Set default processing status if not set
            if (duplicateWastageDetailData.getIsProcessed() == null) {
                duplicateWastageDetailData.setIsProcessed(MonkWastageProcessingEnum.NOT_PROCESSED);
            }

            return add(duplicateWastageDetailData, true);
        } catch (Exception e) {
            log.error("Error saving duplicate monk wastage detail data: ", e);
            throw new RuntimeException("Failed to save duplicate monk wastage detail", e);
        }
    }
} 
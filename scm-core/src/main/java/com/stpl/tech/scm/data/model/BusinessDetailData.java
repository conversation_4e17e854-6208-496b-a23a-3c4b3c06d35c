package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "BUSINESS_DETAIL_DATA")
public class BusinessDetailData {

    private Integer businessId;
    private String businessName;
    private String businessDescription;


    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "BUSINESS_ID", unique = true, nullable = false)
    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }


    @Column(name = "BUSINESS_NAME", nullable = false)
    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    @Column(name = "BUSINESS_DESCRIPTION", nullable = false, length = 5000)

    public String getBusinessDescription() {
        return businessDescription;
    }

    public void setBusinessDescription(String businessDescription) {
        this.businessDescription = businessDescription;
    }
}

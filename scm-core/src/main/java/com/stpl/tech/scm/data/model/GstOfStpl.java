package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "GST_OF_STPL")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GstOfStpl {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    Long id;
    @Column(name = "ALPHA_CODE")
    String alphaCode;
    @Column(name="NAME")
    String name;
    @Column(name="GSTIN", unique = true)
    String gstin;

}

package com.stpl.tech.scm.data.model;

import javax.persistence.Entity;
import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.FetchType;
import javax.persistence.OneToMany;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "SALES_PERFORMA_INVOICE_CREDIT_DEBIT_NOTE")
public class SalesPerformaInvoiceCreditDebitNoteDetail {

    private Integer id;
    private String invoiceId;
    private Integer vendorId;
    private String status;
    private Integer createdBy;
    private Date generationTime;
    private String approvalRequired;
    private Integer approvedBy;
    private Date updatedAt;
    private String creditNoteId;
    private Integer creditNoteDocId;
    private String creditNoteDocUrl;
    private BigDecimal totalTax;
    private BigDecimal netAmount;
    private BigDecimal totalAmount;
    private String invoiceDocUrl;
    private Date invoiceDate;
    private String vendorInvoiceNumber;
    private List<SalesPerformaInvoiceCreditDebitNoteItemDetail> creditDebitNoteItems;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false, unique = true)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "INVOICE_ID", nullable = false)
    public String getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(String invoiceId) {
        this.invoiceId = invoiceId;
    }

    @Column(name = "VENDOR_ID", nullable = false)
    public Integer getVendorId() {
        return vendorId;
    }

    public void setVendorId(Integer vendorId) {
        this.vendorId = vendorId;
    }

    @Column(name = "STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "CREATED_BY" , nullable = false)
    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "GENERATION_TIME" , nullable = false)
    public Date getGenerationTime() {
        return generationTime;
    }

    public void setGenerationTime(Date generationTime) {
        this.generationTime = generationTime;
    }

    @Column(name = "APPROVAL_REQUIRED")
    public String getApprovalRequired() {
        return approvalRequired;
    }

    public void setApprovalRequired(String approvalRequired) {
        this.approvalRequired = approvalRequired;
    }

    @Column(name = "APPROVED_BY")
    public Integer getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(Integer approvedBy) {
        this.approvedBy = approvedBy;
    }

    @Column(name = "UPDATED_AT")
    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Column(name = "CREDIT_NOTE_ID", nullable = true)
    public String getCreditNoteId() {
        return creditNoteId;
    }

    public void setCreditNoteId(String creditNoteId) {
        this.creditNoteId = creditNoteId;
    }

    @Column(name = "CREDIT_NOTE_DOC_ID", nullable = true)
    public Integer getCreditNoteDocId() {
        return creditNoteDocId;
    }

    public void setCreditNoteDocId(Integer creditNoteDocId) {
        this.creditNoteDocId = creditNoteDocId;
    }

    @Column(name = "CREDIT_NOTE_DOC_URL", nullable = true)
    public String getCreditNoteDocUrl() {
        return creditNoteDocUrl;
    }

    public void setCreditNoteDocUrl(String creditNoteDocUrl) {
        this.creditNoteDocUrl = creditNoteDocUrl;
    }

    @Column(name = "TOTAL_TAX", nullable = true)
    public BigDecimal getTotalTax() {
        return totalTax;
    }

    public void setTotalTax(BigDecimal totalTax) {
        this.totalTax = totalTax;
    }

    @Column(name = "NET_AMOUNT", nullable = true)
    public BigDecimal getNetAmount() {
        return netAmount;
    }

    public void setNetAmount(BigDecimal netAmount) {
        this.netAmount = netAmount;
    }

    @Column(name = "TOTAL_AMOUNT", nullable = true)
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    @Column(name = "INVOICE_DOC_URL", nullable = true)
    public String getInvoiceDocUrl() {
        return invoiceDocUrl;
    }

    public void setInvoiceDocUrl(String invoiceDocUrl) {
        this.invoiceDocUrl = invoiceDocUrl;
    }

    @Column(name = "INVOICE_DATE", nullable = true)
    public Date getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(Date invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    @Column(name = "VENDOR_INVOICE_NUMBER", nullable = true)
    public String getVendorInvoiceNumber() {
        return vendorInvoiceNumber;
    }

    public void setVendorInvoiceNumber(String vendorInvoiceNumber) {
        this.vendorInvoiceNumber = vendorInvoiceNumber;
    }


    @OneToMany(fetch = FetchType.LAZY, mappedBy = "creditDebitNoteDetail")
    public List<SalesPerformaInvoiceCreditDebitNoteItemDetail> getCreditDebitNoteItems() {
        return creditDebitNoteItems;
    }

    public void setCreditDebitNoteItems(List<SalesPerformaInvoiceCreditDebitNoteItemDetail> creditDebitNoteItems) {
        this.creditDebitNoteItems = creditDebitNoteItems;
    }
}

package com.stpl.tech.scm.notification.email;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.notification.email.template.VendorROEmailNotificationTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

/**
 * Created by Chaayos on 05-09-2016.
 */
public class VendorROEmailNotification extends EmailNotification {

	private VendorROEmailNotificationTemplate vendorROEmailNotificationTemplate;
	private EnvType envType;
	private String[] emails;

	public VendorROEmailNotification() {

	}

	public VendorROEmailNotification(VendorROEmailNotificationTemplate vendorROEmailNotificationTemplate,
			EnvType envType, String[] emails) {
		this.vendorROEmailNotificationTemplate = vendorROEmailNotificationTemplate;
		this.envType = envType;
		this.emails = emails;
	}

	@Override
	public String[] getToEmails() {
		List<String> mails = new ArrayList<>();
		Arrays.asList(emails).forEach(email -> {
			mails.add(email);
		});
		VendorDetail vendorDetail = vendorROEmailNotificationTemplate.getVendorDetail();
		if (vendorDetail.getPrimaryEmail() != null) {
			mails.add(vendorDetail.getPrimaryEmail());
		}
		if (vendorDetail.getSecondaryEmail() != null) {
			mails.add(vendorDetail.getSecondaryEmail());
		}
		/*for (Integer unitId : vendorROEmailNotificationTemplate.getRequestOrderMap().keySet()) {
			Unit unit = vendorROEmailNotificationTemplate.getUnitMap().get(unitId);
			if (unit.getUnitEmail() != null) {
				mails.add(unit.getUnitEmail());
			}
		}*/
		mails.add("<EMAIL>");
		String[] simpleArray = new String[mails.size()];
		return SCMUtil.isDev(envType) ? new String[] { "<EMAIL>" } : mails.toArray(simpleArray);
	}

	@Override
	public String getFromEmail() {
		return "<EMAIL>";
	}

	@Override
	public String subject() {
		String subject;
		if (vendorROEmailNotificationTemplate.isReceiving()) {
			subject = "Receiving details From Chaayos for "
					+ SCMUtil.getFormattedTime(SCMUtil.getPreviousDateIST(), "EEE dd MMM yyyy");
		} else {
			subject = "Request Orders From Chaayos for "
					+ SCMUtil.getFormattedTime(SCMUtil.getCurrentDateIST(), "EEE dd MMM yyyy");
		}
		if (SCMUtil.isDev(envType)) {
			subject = "Dev " + subject;
		}
		return subject;
	}

	@Override
	public String body() throws EmailGenerationException {
		try {
			return vendorROEmailNotificationTemplate.getContent();
		} catch (TemplateRenderingException e) {
			throw new EmailGenerationException("Failed to render the template", e);
		}
	}

	@Override
	public EnvType getEnvironmentType() {
		return envType;
	}
}

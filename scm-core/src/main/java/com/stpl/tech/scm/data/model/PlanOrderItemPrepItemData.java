package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.math.BigDecimal;

@Entity
@Table(name = "PLAN_ORDER_ITEM_PREP_ITEM")
public class PlanOrderItemPrepItemData {

	private Integer id;
	private int productId;
	private String productName;
	private BigDecimal quantity;
	private String unitOfMeasure;
	private PlanOrderItemPrepData planOrderItemPrepData;
	private Integer parentOrderItem;
	private String instructions;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ITEM_PREPARATION_ITEM_ID", nullable = false, unique = true)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "PRODUCT_ID", nullable = false)
	public int getProductId() {
		return productId;
	}

	public void setProductId(int productId) {
		this.productId = productId;
	}

	@Column(name = "PRODUCT_NAME", nullable = false)
	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	@Column(name = "QUANTITY", nullable = false)
	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	@Column(name = "UNIT_OF_MEASURE", nullable = false)
	public String getUnitOfMeasure() {
		return unitOfMeasure;
	}

	public void setUnitOfMeasure(String unitOfMeasure) {
		this.unitOfMeasure = unitOfMeasure;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ITEM_PREPARATION_ID", nullable = false)
	public PlanOrderItemPrepData getPlanOrderItemPrepData() {
		return planOrderItemPrepData;
	}

	public void setPlanOrderItemPrepData(PlanOrderItemPrepData planOrderItemPrepData) {
		this.planOrderItemPrepData = planOrderItemPrepData;
	}

	@Column(name = "PARENT_ITEM_ID", nullable = true)
	public Integer getParentOrderItem() {
		return parentOrderItem;
	}

	public void setParentOrderItem(Integer parentOrderItem) {
		this.parentOrderItem = parentOrderItem;
	}

	@Column(name = "INSTRUCTIONS")
	public String getInstructions() {
		return instructions;
	}

	public void setInstructions(String instructions) {
		this.instructions = instructions;
	}
}

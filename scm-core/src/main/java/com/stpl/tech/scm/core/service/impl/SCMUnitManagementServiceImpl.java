package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.NegativeStockException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.*;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.SCMUnitManagementDao;
import com.stpl.tech.scm.data.dao.StockManagementDao;
import com.stpl.tech.scm.data.model.*;
import com.stpl.tech.scm.data.mongo.AuditChangeLogTypes;
import com.stpl.tech.scm.domain.model.*;
import com.stpl.tech.util.AppConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.NoResultException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by Chaayos on 07-05-2016.
 */

@Service
public class SCMUnitManagementServiceImpl implements SCMUnitManagementService {

	private static final Logger LOG = LoggerFactory.getLogger(SCMUnitManagementServiceImpl.class);

	@Autowired
	private SCMUnitManagementDao unitManagementDao;

	@Autowired
	private StockManagementDao stockManagementDao;

	@Autowired
	private SCMCache scmCache;

	@Autowired
	private MasterDataCache masterDataCache;

	@Autowired
	private StockManagementService stockManagementService;

	@Autowired
	private ServiceReceiveManagementService serviceReceiveManagementService;

	@Autowired
	private SCMMetadataService scmMetadataService;

	@Autowired
	private SCMAssetManagementService scmAssetManagementService;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public UnitDetail viewUnit(int unitId) {
		return scmCache.getUnitDetails().get(unitId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<UnitDetail> viewAllUnits() {
		List<UnitDetail> udList = new ArrayList<>(scmCache.getUnitDetails().values());
		return udList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public UnitDetailData addUnitDetail(UnitDetail unitDetail) throws SumoException {
		UnitDetailData unitDetailDataFind = unitManagementDao.find(UnitDetailData.class,unitDetail.getUnitId());
		if(unitDetailDataFind!=null)
		{
			LOG.error("Error encountered while generating opening event for unit ::::", unitDetailDataFind.getUnitId());
			return null;
		}
		UnitDetailData unitDetailData = unitManagementDao.add(
				SCMDataConverter.convert(unitDetail, scmCache.getUnitCategories().get(unitDetail.getUnitCategoryId())),
				true);
		if (unitDetailData != null) {
			scmCache.getUnitDetails().put(unitDetailData.getUnitId(), SCMDataConverter.convert(unitDetailData));
			try {
				stockManagementService.generateOpeningEvent(unitDetailData);
				Date date = SCMUtil.getCurrentBusinessDate();
				Date monthEnd = SCMUtil.getLastDayOfMonth(date);
				Date thursdayAfterGivenDate = SCMUtil.getNextThursday(date);
				//checking if month end and next thursday are on the same date or not
				thursdayAfterGivenDate=thursdayAfterGivenDate.equals(monthEnd)?SCMUtil.getNextThursday(monthEnd):thursdayAfterGivenDate;
				stockManagementService.addStockCalendarEvent(unitDetailData.getUnitId(), StockTakeType.MONTHLY, monthEnd); // monthly event trigger.
				stockManagementService.addStockCalendarEvent(unitDetailData.getUnitId(), StockTakeType.WEEKLY,thursdayAfterGivenDate);// weekly event trigger.
			} catch (NegativeStockException e) {
				LOG.error("Error encountered while generating opening event for unit ::::", unitDetailData.getUnitId());
			}
			return unitDetailData;
		}
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updateUnit(UnitDetail unitDetail) {
		UnitDetailData unitDetailData = unitManagementDao.find(UnitDetailData.class, unitDetail.getUnitId());
		if (unitDetailData != null) {
			unitDetailData.setUnitStatus(unitDetail.getUnitStatus().value());
			unitDetailData.setUnitName(unitDetail.getUnitName());
			unitDetailData.setUnitId(unitDetail.getUnitId());
			unitDetailData.setUnitEmail(unitDetail.getUnitEmail());
			UnitCategoryData unitCategoryData = unitManagementDao.find(UnitCategoryData.class,
					unitDetail.getUnitCategoryId());
			unitDetailData.setUnitCategory(unitCategoryData);
			unitDetailData = unitManagementDao.update(unitDetailData, true);
			if (unitDetailData != null) {
				scmCache.getUnitDetails().put(unitDetailData.getUnitId(), SCMDataConverter.convert(unitDetailData));
				return true;
			}
		}
		LOG.info("Unit productId: {} not found!", unitDetail.getUnitId());
		return false;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public DeactivateValidateResponse deactivateUnit(int unitId) throws SumoException, URISyntaxException {
		DeactivateValidateResponse validateResponse = scmMetadataService.validateForDeactivation(unitId,"UNIT");
		if(validateResponse.getCanBeDeActivated().equals(Boolean.TRUE)){
			UnitDetailData unitDetailData = unitManagementDao.find(UnitDetailData.class, unitId);
			if (unitDetailData != null) {
				unitDetailData.setUnitStatus(SwitchStatus.IN_ACTIVE.value());
				unitDetailData = unitManagementDao.update(unitDetailData, true);
				if (unitDetailData != null) {
					scmCache.getUnitDetails().put(unitDetailData.getUnitId(), SCMDataConverter.convert(unitDetailData));
					//return true;
				}
			}
		}
		return validateResponse;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean activateUnit(int unitId) throws SumoException {
		UnitDetailData unitDetailData = unitManagementDao.find(UnitDetailData.class, unitId);
		if (unitDetailData != null) {
			unitDetailData.setUnitStatus(SwitchStatus.ACTIVE.value());
			unitDetailData = unitManagementDao.update(unitDetailData, true);
			if (unitDetailData != null) {
				scmCache.getUnitDetails().put(unitDetailData.getUnitId(), SCMDataConverter.convert(unitDetailData));
				try{
					scmMetadataService.saveAuditLog(unitId, AuditChangeLogTypes.UNIT.value(), null,unitDetailData,
							AuditChangeLogTypes.NEW_ENTRY.value());
				}catch (Exception e){
					LOG.info("Error While Saving Audit Log Data In Mongo",e);
				}

				return true;
			} else {
				return false;
			}
		} else {
			return addUnit(unitId);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<UnitDetail> viewUnitsForAdhocRequest(int unitId) {
		List<FulfillmentUnitMappingData> fulfillmentUnitMappingDataList = unitManagementDao
				.viewUnitsForAdhocRequest(FulfillmentType.INTERNAL.value(), unitId);
		List<UnitDetail> unitDetailList = new ArrayList<UnitDetail>();
		for (FulfillmentUnitMappingData fulfillmentUnitMappingData : fulfillmentUnitMappingDataList) {
			unitDetailList.add(scmCache.getUnitDetails().get(fulfillmentUnitMappingData.getFulfillingUnitId()));
		}
		return unitDetailList;
	}

	@Override
	public List<FulfillmentUnitMappingData> getUnitToFulfillmentTypeMapping(int unitId) {
		return unitManagementDao.getUnitToFulfillmentTypeMapping(unitId);
	}

	@Override
	public List<UnitDistanceMappingData> getUnitDistanceMapping(int sourceUnitId, int destinationUnitId) {
		return unitManagementDao.getUnitDistanceMapping(sourceUnitId, destinationUnitId);
	}

	@Override
	public List<UnitDistanceMappingData> updateUnitDistanceMapping(List<UnitDistanceMappingData> list)
			throws SumoException {
		return unitManagementDao.updateUnitDistanceMapping(list);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updateSCMUnit(Unit unit) throws SumoException {
		updateBusinessCostCentre(unit);
		if (unitManagementDao.find(UnitDetailData.class, unit.getId()) != null) {
			UnitDetail unitDetail = (convert(unit));
			if (unit.getStatus().value().equals(AppConstants.ACTIVE)) {
				unitDetail.setUnitStatus(SwitchStatus.ACTIVE);
			} else {
				unitDetail.setUnitStatus(SwitchStatus.IN_ACTIVE);
			}

			UnitDetailData unitDetailData = SCMDataConverter.convert(unitDetail,
				scmCache.getUnitCategories().get(unitDetail.getUnitCategoryId()));

			unitDetailData = unitManagementDao.update(unitDetailData, true);
			if (unitDetailData != null) {
				scmCache.getUnitDetails().put(unitDetailData.getUnitId(), SCMDataConverter.convert(unitDetailData));
				try {
					scmMetadataService.saveAuditLog(unit.getId(),AuditChangeLogTypes.UNIT.value(), null, ((Object) unitDetailData),
							AuditChangeLogTypes.UPDATE_ENTRY.value());
				}catch (Exception e){
					LOG.info("Error While Saving Audit Log Data In Mongo",e);
				}
				return true;
			} else {
				return false;
			}
		}else {
			return false;
		}

	}

	private Boolean updateBusinessCostCentre(Unit unitEdit) {

		try{
			LOG.info("Trying to update cost centre data for unit ",unitEdit.getName());
			List<BusinessCostCenterData> businessCostCenters = unitManagementDao.getBusinessCostCentresByUnitId(String.valueOf(unitEdit.getId()));
			BusinessCostCenterData data;
			data = businessCostCenters.get(0);
			if(Objects.nonNull(data)){
				data.setCode(String.valueOf(unitEdit.getId()));
				data.setType(unitEdit.getFamily().name());
				data.setStatus(unitEdit.getStatus().value());
				data.setCompanyId(unitEdit.getCompany().getId());
				data.setName(unitEdit.getName());
				data.setLocationId(unitEdit.getLocation().getId());
				unitManagementDao.update(data, true);
				return true;
			}
			return false;
		}catch(NoResultException e){
			LOG.error("Error while fetching business cost centres for bccCode ::{} ",unitEdit.getId(),e);
			return false;
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean addUnit(int unitId) throws SumoException {
		boolean flag = false;
		Unit unit = masterDataCache.getUnit(unitId);
		if (unit != null) {
			UnitDetailData unitDetailData = addUnitDetail(convert(unit));
			if (unitDetailData != null) {
				addFulFillmentTypeUnitMapping(unitDetailData, unit.getRegion());
				activateUnit(unitId);
				addBusinessCostCenter(unit);
				updateDerivedProductsMapping(unitDetailData);
				flag = true;
			}
		}
		return flag;
	}

	private void updateDerivedProductsMapping(UnitDetailData unitDetailData) {
		for (ProductDefinition product : scmCache.getProductDefinitions().values()) {
			try {
				if (SCMUtil.hasProductFulFillmentTypeMappings(product)) {
					ProductDefinitionData productData = unitManagementDao.find(ProductDefinitionData.class,
							product.getProductId());
					DerivedMappingData mappingData = new DerivedMappingData();
					mappingData.setProduct(productData);
					mappingData.setFulfillmentType(product.getDefaultFulfillmentType().name());
					mappingData.setUnitId(unitDetailData.getUnitId());
					unitManagementDao.add(mappingData, false);
				}
			} catch (Exception e) {
				LOG.info("Error while adding derived mapping for product {} - {} and unit {} - {}",
						product.getProductId(), product.getProductName(), unitDetailData.getUnitId(),
						unitDetailData.getUnitName(), e);
			}
		}
		unitManagementDao.flush();
	}

	private void addBusinessCostCenter(Unit unit) throws SumoException {
		BusinessCostCenter businessCostCenterData = new BusinessCostCenter();
		businessCostCenterData.setCode(String.valueOf(unit.getId()));
		businessCostCenterData.setType(unit.getFamily().name());
		businessCostCenterData.setStatus(AppConstants.ACTIVE);
		businessCostCenterData.setCompany(new IdCodeName(unit.getCompany().getId(), unit.getCompany().getShortCode(),
				unit.getCompany().getName()));
		businessCostCenterData.setLocation(
				new IdCodeName(unit.getLocation().getId(), unit.getLocation().getCode(), unit.getLocation().getName()));
		businessCostCenterData.setName(unit.getName());
		serviceReceiveManagementService.createBusinessCostCentersData(businessCostCenterData);
	}



	private void addFulFillmentTypeUnitMapping(UnitDetailData unitDetailData, String region) throws SumoException {
		Map<FulfillmentType, Integer> fulfillmentTypeMap = new HashMap<>();
		List<FulfillmentType> types = Arrays.asList(FulfillmentType.KITCHEN,FulfillmentType.WAREHOUSE);
		/*if (region.equals(UnitRegion.NCR) || region.equals(UnitRegion.CHANDIGARH)
				|| region.equals(UnitRegion.NCR_EDU)) {
			fulfillmentTypeMap.put(FulfillmentType.WAREHOUSE, 26185);
			fulfillmentTypeMap.put(FulfillmentType.KITCHEN, 26130);
		}
		if (region.equals(UnitRegion.MUMBAI) || region.equals(UnitRegion.PUNE)) {
			fulfillmentTypeMap.put(FulfillmentType.WAREHOUSE, 26327);
			fulfillmentTypeMap.put(FulfillmentType.KITCHEN, 24002);
		}

		if (region.equals(UnitRegion.BANGALORE) || region.equals(UnitRegion.CHENNAI)) {
			fulfillmentTypeMap.put(FulfillmentType.WAREHOUSE, 26282);
			fulfillmentTypeMap.put(FulfillmentType.KITCHEN, 26100);
		}

		if (region.equals(UnitRegion.HYDERABAD)) {
			fulfillmentTypeMap.put(FulfillmentType.WAREHOUSE, 26496);
			fulfillmentTypeMap.put(FulfillmentType.KITCHEN, 26495);
		}

*/
		for(FulfillmentType type : types){
			int companyId = masterDataCache.getUnit(unitDetailData.getUnitId()).getCompany().getId();
			if(SCMUtil.getCompaniesByBrand(AppConstants.CHAAYOS_BRAND_ID,masterDataCache).contains(companyId)) {
				companyId = 1000;
			}
			String key = SCMUtil.generateUniqueKey(region, type.value(), String.valueOf(companyId));
			if(scmCache.getRegionFulfillmentMapping(key) != null) {
				fulfillmentTypeMap.put(type,scmCache.getRegionFulfillmentMapping(key));
			}
		}
		fulfillmentTypeMap.put(FulfillmentType.EXTERNAL, unitDetailData.getUnitId());

		List<FulfillmentUnitMappingData> mappingDataList = new ArrayList<>();
		for (FulfillmentType type : fulfillmentTypeMap.keySet()) {
			FulfillmentUnitMappingData mappingData = new FulfillmentUnitMappingData();
			mappingData.setFulfillmentType(type.name());
			mappingData.setRequestingUnitId(unitDetailData.getUnitId());
			mappingData.setFulfillingUnitId(fulfillmentTypeMap.get(type));
			mappingData.setStatus(SwitchStatus.ACTIVE.name());
			mappingDataList.add(mappingData);
		}
		unitManagementDao.addAll(mappingDataList);
		unitManagementDao.flush();
		scmCache.refreshFulfillmentMapping();
	}

	private UnitDetail convert(Unit unit) {
		UnitDetail unitDetail = new UnitDetail();
		unitDetail.setUnitId(unit.getId());
		unitDetail.setUnitEmail(unit.getUnitEmail());
		unitDetail.setUnitName(unit.getName());
		unitDetail.setTin(unit.getTin());
		unitDetail.setUnitRegion(unit.getRegion());
		unitDetail.setUnitStatus(SwitchStatus.IN_ACTIVE);
		unitDetail.setUnitCategoryId(getUnitCategoryId(unit.getFamily()));
		unitDetail.setCompanyId(unit.getCompany().getId());
		unitDetail.setShortCode(unit.getShortCode());
		return unitDetail;
	}

	private int getUnitCategoryId(UnitCategory family) {
		switch (family) {
		case TAKE_AWAY:
		case CAFE:
			return 1;
		case DELIVERY:
			return 2;
		case WAREHOUSE:
			return 3;
		case KITCHEN:
			return 4;
		case COD:
			return 5;
		case OFFICE:
			return 6;
		case CHAI_MONK:
			return 7;
		default:
			return 1;
		}
	}



	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<BusinessCostCenter> getBusinessCostCentersByCompanyId(int companyId, String accountType, String businessCostCentreCode) throws SumoException {
		List<BusinessCostCenterData> businessCostCenterDataList= null;
		List<BusinessCostCenter> businessCostCenters = new ArrayList<>();
		try{
			businessCostCenterDataList = unitManagementDao.getBusinessCostCentresByAccountTypeAndCompanyId(companyId,accountType,businessCostCentreCode);
		}catch (NoResultException e){
			LOG.error("Error while fetching business cost centres for type ::{} ",accountType,e);
		}

		if(Objects.nonNull(businessCostCenterDataList) && !businessCostCenterDataList.isEmpty()){
			serviceReceiveManagementService.getBusinessCostCentres(businessCostCenterDataList,businessCostCenters);
		}
		return businessCostCenters;
	}

	@Override
	public List<BusinessCostCenter> getBusinessCostCentersByType(String businessCostCentreType) {
		try{
			List<BusinessCostCenterData > businessCostCenters = unitManagementDao.getBusinessCostCentresByType(businessCostCentreType);
			List<BusinessCostCenter> businessCostCenterList = new ArrayList<>();
			if(Objects.nonNull(businessCostCenters) && !businessCostCenters.isEmpty()){
				serviceReceiveManagementService.getBusinessCostCentres(businessCostCenters,businessCostCenterList);
			}
			return businessCostCenterList;
		}catch(NoResultException e){
			LOG.error("Error while fetching business cost centres for bccType ::{} ",businessCostCentreType,e);
		}
		return null;
	}

	@Override
	public List<BusinessCostCenterData> getBusinessCostCentresByBccCode(Integer unitId) {
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public NsoEventAssetsResponse getAssetsToHandover(int unitId ) {
		NsoEventAssetsResponse response = new NsoEventAssetsResponse();
		List<AssetDefinition> assetDefinitionList = new ArrayList<>();
		try {
			List<Pair<Pair<Integer, String>, Date>> activeEvent = unitManagementDao.getActiveStockTakeEvent(unitId);

			if(!activeEvent.isEmpty()) {

				Integer activeEventId = activeEvent.get(0).getKey().getKey();
				String activeEventStatus = activeEvent.get(0).getKey().getValue();
				Date activeEventLastUpdationDate = activeEvent.get(0).getValue();

				if(activeEventStatus.equals(StockEventStatusType.IN_PROCESS.value())){
					response.setEventStatus(StockEventStatusType.IN_PROCESS.value());
					List<Pair<AssetDefinitionData,String>> assetDefinitionDataList = unitManagementDao.getAssetsToHandover(unitId, activeEventId);

					if (!assetDefinitionDataList.isEmpty()) {
						for (Pair<AssetDefinitionData,String> assetDefinitionData : assetDefinitionDataList) {
							AssetDefinition ad = scmAssetManagementService.convertAssetDefinitionDataToAssetDefinition(assetDefinitionData.getKey(), false);
							ad.setScanned(!Objects.equals(assetDefinitionData.getValue(), "PENDING_LOST"));
							assetDefinitionList.add(ad);
						}
					}

				}else{
					response.setEventStatus(StockEventStatusType.COMPLETED.value());
					response.setHourDiff(SCMUtil.getDateDifferenceInHours(activeEventLastUpdationDate,SCMUtil.getCurrentBusinessDate()));
				}
			}
			else{
				response.setEventStatus(null);
				response.setHourDiff(null);
			}
		} catch (Exception e) {
			LOG.info("No Active Stock Take event for unit {}", unitId, e);
		}
		response.setAssetDefinition(assetDefinitionList);
		return response;
	}
}

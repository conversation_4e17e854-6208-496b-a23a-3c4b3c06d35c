package com.stpl.tech.scm.core.util.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class ProductionBookingDataVO {

	private Integer productId;
	private String productName;
	private Integer skuId;
	private String unitOfMeasure;
	private BigDecimal quantity;
	private int unitId;
	private BigDecimal unitPrice;
	private BigDecimal totalCost;
	private String profile;

	private List<BookingConsumptionDataVO> consumption = new ArrayList<BookingConsumptionDataVO>();

	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public Integer getSkuId() {
		return skuId;
	}

	public void setSkuId(Integer skuId) {
		this.skuId = skuId;
	}

	public String getUnitOfMeasure() {
		return unitOfMeasure;
	}

	public void setUnitOfMeasure(String unitOfMeasure) {
		this.unitOfMeasure = unitOfMeasure;
	}

	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public BigDecimal getUnitPrice() {
		return unitPrice;
	}

	public void setUnitPrice(BigDecimal unitPrice) {
		this.unitPrice = unitPrice;
	}

	public BigDecimal getTotalCost() {
		return totalCost;
	}

	public void setTotalCost(BigDecimal totalCost) {
		this.totalCost = totalCost;
	}

	public String getProfile() {
		return profile;
	}

	public void setProfile(String profile) {
		this.profile = profile;
	}

	public List<BookingConsumptionDataVO> getConsumption() {
		if(consumption == null) {
			consumption = new ArrayList<BookingConsumptionDataVO>();
		}
		return consumption;
	}

	public void setConsumption(List<BookingConsumptionDataVO> consumption) {
		this.consumption = consumption;
	}
	

}

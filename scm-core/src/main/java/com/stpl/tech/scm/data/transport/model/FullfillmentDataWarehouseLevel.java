package com.stpl.tech.scm.data.transport.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class FullfillmentDataWarehouseLevel {
    String transferringUnit;

    // Overall
    Double avgFPer;
    Double avgOnTimeFFPer;
    Double avgOnDateFFPer;
    Double avgOnTimeRoRaisedPer;
    Double avgOnDateRoRaisedPer;
    Double avgDelayRoRaisedPer;

    // Impact FF
    Double avgImFPer;
//    Double avgImOnTimeFFPer;
//    Double avgImOnDateFFPer;
    
    // critical 
    Double criticalAvg;
    Double criticalOnTimeFF;
    Double criticalOnDateFF;
    Double criticalProductFF;
    Double criticalOnTimeRaisedFF;
    Double criticalOnDateRaisedFF;
    Double criticalDelayRaisedFF;
    String isCriticalProd;
    
    // baker
    Double bakeryFP;
    Double bakeryOnTimeFP;
    Double bakeryOnDateFP;
    Double bakeryOnTimeRoRaisedPer;
    Double bakeryOnDateRoRaisedPer;
    Double bakeryRoRaisedDelayPer;

    // without bakery
    Double withoutBakeryAvgFPer;
    Double withoutBakeryOnTimeAvgFPer;
    Double withoutBakeryOnDateAvgFPer;
    
    // Level 1
    Double productLevel1FFPer = 0.0;
    Double productLevel1OnTimeFFPer = 0.0;
    Double productLevel1OnDateFFPer = 0.0;
    Double productLevel1RaisedOnTimePer = 0.0;
    Double productLevel1RaisedOnDatePer = 0.0;
    Double productLevel1DelayRaisedPer = 0.0;

    // Level 2
    Double productLevel2FFPer = 0.0;
    Double productLevel2OnTimeFFPer = 0.0;
    Double productLevel2OnDateFFPer = 0.0;
    Double productLevel2RaisedOnTimePer = 0.0;
    Double productLevel2RaisedOnDatePer = 0.0;
    Double productLevel2DelayRaisedPer = 0.0;

    // Level 3
    Double productLevel3FFPer = 0.0;
    Double productLevel3OnTimeFFPer = 0.0;
    Double productLevel3OnDateFFPer = 0.0;
    Double productLevel3RaisedOnTimePer = 0.0;
    Double productLevel3RaisedOnDatePer = 0.0;
    Double productLevel3DelayRaisedPer = 0.0;

    // Level 4
    Double productLevel4FFPer = 0.0;
    Double productLevel4OnTimeFFPer = 0.0;
    Double productLevel4OnDateFFPer = 0.0;
    Double productLevel4RaisedOnTimePer = 0.0;
    Double productLevel4RaisedOnDatePer = 0.0;
    Double productLevel4DelayRaisedPer = 0.0;

    // Level 5
    Double productLevel5FFPer = 0.0;
    Double productLevel5OnTimeFFPer = 0.0;
    Double productLevel5OnDateFFPer = 0.0;
    Double productLevel5RaisedOnTimePer = 0.0;
    Double productLevel5RaisedOnDatePer = 0.0;
    Double productLevel5DelayRaisedPer = 0.0;

    // LEVEL Isn't defined
    Double productLevelNAFFPer = 0.0;
    Double productLevelNAOnTimeFFPer = 0.0;
    Double productLevelNAOnDateFFPer = 0.0;
    Double productLevelNARaisedOnTimePer = 0.0;
    Double productLevelNARaisedOnDatePer = 0.0;
    Double productLevelNADelayRaisedPer = 0.0;
}

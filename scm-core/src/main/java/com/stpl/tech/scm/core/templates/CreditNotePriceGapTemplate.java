package com.stpl.tech.scm.core.templates;

import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.model.SalesPerformaInvoiceCreditDebitNoteDetail;
import com.stpl.tech.scm.domain.model.CreditDebitNoteDetail;
import com.stpl.tech.scm.domain.model.SalesPerformaInvoice;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import org.apache.velocity.tools.generic.DateTool;
import org.apache.velocity.tools.generic.MathTool;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class CreditNotePriceGapTemplate extends AbstractVelocityTemplate {

    private SalesPerformaInvoiceCreditDebitNoteDetail creditDebitNoteDetail;
    private String basePath;
    private String totalAmountInWords;
    private String creditNoteNo;
    private Unit unitData;
    private SalesPerformaInvoice invoice;

    public CreditNotePriceGapTemplate() {
    }

    public CreditNotePriceGapTemplate(SalesPerformaInvoiceCreditDebitNoteDetail creditDebitNoteDetail, String basePath, String totalAmountInWords, String creditNoteNo, Unit unitData , SalesPerformaInvoice invoice) {
        this.basePath = basePath;
        this.totalAmountInWords = totalAmountInWords;
        this.creditDebitNoteDetail = creditDebitNoteDetail;
        this.creditNoteNo = creditNoteNo;
        this.unitData = unitData;
        this.invoice = invoice;
    }

    @Override
    public String getTemplatePath() {
        return "templates/CreditNotePriceGap.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/" + creditDebitNoteDetail.getId() + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();

        stringObjectMap.put("creditNoteDate",SCMUtil.getDateString(creditDebitNoteDetail.getGenerationTime()));
        stringObjectMap.put("invoiceDate",SCMUtil.getDateString(creditDebitNoteDetail.getInvoiceDate()));
        stringObjectMap.put("invoice",invoice);
        stringObjectMap.put("unitData", unitData);
        stringObjectMap.put("creditNote", creditDebitNoteDetail);
        stringObjectMap.put("creditNoteNo", creditNoteNo);
        stringObjectMap.put("totalAmountInWords", totalAmountInWords);
        stringObjectMap.put("dateTool", new DateTool());
        stringObjectMap.put("mathTool", new MathTool());
        stringObjectMap.put("basePath", basePath);
        return stringObjectMap;
    }

}

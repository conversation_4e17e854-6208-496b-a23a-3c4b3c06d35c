package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.AttributeManagementService;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.SCMProfileManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.SCMProfileManagementDao;
import com.stpl.tech.scm.data.model.AttributeDefinitionData;
import com.stpl.tech.scm.data.model.AttributeValueData;
import com.stpl.tech.scm.data.model.EntityAttributeValueMappingData;
import com.stpl.tech.scm.data.model.ProfileAttributeMappingData;
import com.stpl.tech.scm.data.model.ProfileDefinitionData;
import com.stpl.tech.scm.domain.model.AttributeValue;
import com.stpl.tech.scm.domain.model.EntityAttributeValueMapping;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.ProfileAttributeMapping;
import com.stpl.tech.scm.domain.model.ProfileDefinition;
import com.stpl.tech.scm.domain.model.SwitchStatus;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by Abhishek Sirohi on 07-05-2016.
 */
@Service

public class SCMProfileManagementServiceImpl implements SCMProfileManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(SCMProfileManagementServiceImpl.class);

    @Autowired
    private SCMProfileManagementDao scmProfileManagementDao;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private MasterDataCache masterDataCache;


    @Autowired
    private FileArchiveService fileArchiveService;

    @Autowired
    private AttributeManagementService attributeManagementService;

    @Autowired
    private EnvProperties props;


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ProfileDefinition viewProfile(int profileId) {
        return scmCache.getProfileDefinition(profileId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public List<ProfileAttributeMapping> addProfileAttributeMappings(List<ProfileAttributeMapping> profileAttributeMappings) throws SumoException {
        List<ProfileAttributeMapping> list = new ArrayList<>();
        for (ProfileAttributeMapping pam : profileAttributeMappings) {
            if (pam.getProfileAttributeMappingId() == null) {
                pam.setCreationDate(SCMUtil.getCurrentDateIST());
            }
            pam.setUpdationDate(SCMUtil.getCurrentDateIST());
            ProfileAttributeMappingData profileAttributeMappingData = SCMDataConverter.convert(
                    pam, scmProfileManagementDao.find(ProfileDefinitionData.class, pam.getProfileId()),
                    scmProfileManagementDao.find(AttributeDefinitionData.class, pam.getAttributeId()));
            if (profileAttributeMappingData.getProfileAttributeMappingId() == null) {
                profileAttributeMappingData = scmProfileManagementDao.add(profileAttributeMappingData, true);
            } else {
                profileAttributeMappingData = scmProfileManagementDao.update(profileAttributeMappingData, true);
            }
            ProfileAttributeMapping profileAttributeMapping = updateProfileAttributeMappingCache(profileAttributeMappingData);
            list.add(profileAttributeMapping);
        }
        return list;
    }

    private ProfileAttributeMapping updateProfileAttributeMappingCache(ProfileAttributeMappingData profileAttributeMappingData) {
        List<ProfileAttributeMapping> mappings = scmCache.getProfileAttributeMappings().get(profileAttributeMappingData.getProfileDefinitionData().getProfileId());
        if (mappings == null) {
            mappings = new ArrayList<>();
        }
        IdCodeName createdBy = SCMUtil.generateIdCodeName(profileAttributeMappingData.getCreatedBy(), "",
                masterDataCache.getEmployees().get(profileAttributeMappingData.getCreatedBy()));
        IdCodeName updatedBy = SCMUtil.generateIdCodeName(profileAttributeMappingData.getUpdatedBy(), "",
                masterDataCache.getEmployees().get(profileAttributeMappingData.getUpdatedBy()));
        ProfileAttributeMapping profileAttributeMapping = SCMDataConverter.convert(profileAttributeMappingData, createdBy, updatedBy);

        boolean mappingExists = false;
        int counter = 0;
        for (ProfileAttributeMapping mapping : mappings) {
            if (mapping.getProfileAttributeMappingId().equals(profileAttributeMapping.getProfileAttributeMappingId())) {
                mappingExists = true;
                mappings.set(counter, profileAttributeMapping);
            }
            counter++;
        }
        if (!mappingExists) {
            mappings.add(profileAttributeMapping);
        }
        scmCache.getProfileAttributeMappings().put(profileAttributeMappingData.getProfileDefinitionData().getProfileId(), mappings);
        return profileAttributeMapping;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ProfileAttributeMapping> getAllAttributeMappingsForProfile(int profileId) {
        List<ProfileAttributeMapping> list = new ArrayList<>();
        List<ProfileAttributeMapping> mappings = scmCache.getProfileAttributeMappings().get(profileId);
        if (mappings == null) {
            return list;
        }
        list.addAll(mappings);
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ProfileAttributeMapping> getAllAttributeMappingsForProfileAndSKU(int profileId) {
        List<ProfileAttributeMapping> list = new ArrayList<>();
        List<ProfileAttributeMapping> mappings = scmCache.getProfileAttributeMappings().get(profileId);
        if (mappings == null) {
            return list;
        }
        for (ProfileAttributeMapping profileAttributeMapping : mappings) {
            if (profileAttributeMapping.isDefinedAtSKU()) {
                list.add(profileAttributeMapping);
            }
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ProfileAttributeMapping> getAllAttributeMappingsForProfileAndAsset(int profileId) {
        List<ProfileAttributeMapping> list = new ArrayList<>();
        List<ProfileAttributeMapping> mappings = scmCache.getProfileAttributeMappings().get(profileId);
        if (mappings == null) {
            return list;
        }
        for (ProfileAttributeMapping profileAttributeMapping : mappings) {
            if (profileAttributeMapping.isDefinedAtAsset() || profileAttributeMapping.isOverridableAtSKU()) {
                list.add(profileAttributeMapping);
            }
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ProfileAttributeMapping> getAllAttributeMappingsForProfile(int profileId, String status) {
        List<ProfileAttributeMapping> list = new ArrayList<>();
        List<ProfileAttributeMapping> mappings = scmCache.getProfileAttributeMappings().get(profileId);
        if (mappings != null) {
            for (ProfileAttributeMapping profileAttributeMapping : mappings) {
                if (profileAttributeMapping.getStatus().equals(status)) {
                    list.add(profileAttributeMapping);
                }
            }
        }


        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public String uploadProfileMappingImage(MimeType mimeType, Integer profileMappingId, MultipartFile file) throws SumoException {
        if (profileMappingId != null) {
            ProfileAttributeMappingData pdd = scmProfileManagementDao.find(ProfileAttributeMappingData.class, profileMappingId);
            if (pdd != null) {
                String fileName = profileMappingId + "." + mimeType.name().toLowerCase();
                String baseDir = "scm-service/profile_attribute_image";
                try {
                    FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3ProductBucket(), baseDir, fileName, file, true);
                    if (s3File != null) {
                        pdd.setAssociatedImage(fileName);
                        pdd = scmProfileManagementDao.update(pdd, true);
                        if (pdd != null) {
                            updateProfileAttributeMappingCache(pdd);
                            return fileName;
                        } else {
                            throw new SumoException("Error updating product image entry.");
                        }
                    } else {
                        throw new SumoException("Error uploading image.");
                    }
                } catch (Exception e) {
                    LOG.error("Encountered error while uploading product image", e);
                }
            } else {
                throw new SumoException("Profile Attribute mapping is not valid.");
            }
        } else {
            throw new SumoException("Provide Profile Attribute mapping identifier.");
        }
        return null;
    }

    @Override
    public List<EntityAttributeValueMapping> getEntityAttributeMappings(int entityId, String entityType) {
        List<EntityAttributeValueMapping> returnList = new ArrayList<>();
        List<EntityAttributeValueMappingData> list = scmProfileManagementDao.getEntityAttributeValueMappingForEntityAndType(entityId, entityType);
        list.forEach(data -> {
            IdCodeName createdBy = SCMUtil.generateIdCodeName(data.getCreatedBy(), "",
                    masterDataCache.getEmployees().get(data.getCreatedBy()));
            returnList.add(SCMDataConverter.convert(data, createdBy,
                    scmCache.getAttributeDefinitions().get(data.getAttributeId()).getAttributeName()));
        });
        return returnList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public List<EntityAttributeValueMapping> addGenericAttributeValueMappings(List<EntityAttributeValueMapping> entityAttributeValueMappings) throws SumoException {
        List<EntityAttributeValueMapping> list = new ArrayList<>();
        for (EntityAttributeValueMapping gam : entityAttributeValueMappings) {
            gam.setCreationDate(SCMUtil.getCurrentDateIST());
            EntityAttributeValueMappingData entityAttributeValueMappingData = SCMDataConverter.convert(gam);
            entityAttributeValueMappingData = scmProfileManagementDao.add(entityAttributeValueMappingData, true);
            IdCodeName createdBy = SCMUtil.generateIdCodeName(entityAttributeValueMappingData.getCreatedBy(), "",
                    masterDataCache.getEmployees().get(entityAttributeValueMappingData.getCreatedBy()));
            EntityAttributeValueMapping entityMapping = SCMDataConverter.convert(entityAttributeValueMappingData, createdBy,
                    scmCache.getAttributeDefinitions().get(entityAttributeValueMappingData.getAttributeId()).getAttributeName());
            list.add(entityMapping);
        }
        return list;
    }

    @Override
    public List<EntityAttributeValueMapping> addGenericAttributeValueMappingsAndAttribute(List<EntityAttributeValueMapping> entityAttributeValueMappings) throws SumoException {
        List<EntityAttributeValueMapping> list = new ArrayList<>();
        for (EntityAttributeValueMapping gam : entityAttributeValueMappings) {
            int attributeId = gam.getAttributeId();
            List<AttributeValue> attributeValues = new ArrayList<>();
            AttributeValue attributeValue = new AttributeValue();
            attributeValue.setAttributeValue(gam.getAttributeValue());
            // attributeValue.setAttributeValueId(attributeId);
            attributeValue.setAttributeDefinitionId(attributeId);
            attributeValue.setAttributeValueShortCode(gam.getAttributeValue());
            attributeValue.setAttributeValueStatus(SwitchStatus.ACTIVE);
            attributeValues.add(attributeValue);
            AttributeValueData attributeValueData = attributeManagementService.addNewAttributeValue(attributeValues).get(0);
            gam.setAttributeValueId(attributeValueData.getAttributeValueId());
            gam.setCreationDate(SCMUtil.getCurrentDateIST());
            EntityAttributeValueMappingData entityAttributeValueMappingData = SCMDataConverter.convert(gam);
            entityAttributeValueMappingData = scmProfileManagementDao.add(entityAttributeValueMappingData, true);
            IdCodeName createdBy = SCMUtil.generateIdCodeName(entityAttributeValueMappingData.getCreatedBy(), "",
                    masterDataCache.getEmployees().get(entityAttributeValueMappingData.getCreatedBy()));
            EntityAttributeValueMapping entityMapping = SCMDataConverter.convert(entityAttributeValueMappingData, createdBy,
                    scmCache.getAttributeDefinitions().get(entityAttributeValueMappingData.getAttributeId()).getAttributeName());
            list.add(entityMapping);
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public List<EntityAttributeValueMapping> updateGenericAttributeValueMappings(List<EntityAttributeValueMapping> entityAttributeValueMappings) {
        List<EntityAttributeValueMapping> list = new ArrayList<>();
        for (EntityAttributeValueMapping gam : entityAttributeValueMappings) {
            EntityAttributeValueMappingData entityAttributeValueMappingData = scmProfileManagementDao.find(
                    EntityAttributeValueMappingData.class, gam.getEntityAttributeValueMappingId());
            entityAttributeValueMappingData.setAttributeValueId(gam.getAttributeValueId());
            entityAttributeValueMappingData = scmProfileManagementDao.update(entityAttributeValueMappingData, true);
            IdCodeName createdBy = SCMUtil.generateIdCodeName(entityAttributeValueMappingData.getCreatedBy(), "",
                    masterDataCache.getEmployees().get(entityAttributeValueMappingData.getCreatedBy()));
            EntityAttributeValueMapping entityMapping = SCMDataConverter.convert(entityAttributeValueMappingData, createdBy,
                    scmCache.getAttributeDefinitions().get(entityAttributeValueMappingData.getAttributeId()).getAttributeName());
            list.add(entityMapping);
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ProfileDefinition> viewAllProfiles() {
        return scmCache.getProfileDefinitions().values().stream().collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public ProfileDefinition addNewProfile(ProfileDefinition profileDefinition) throws SumoException {
       // try{

            profileDefinition.setCreationDate(SCMUtil.getCurrentDateIST());
            ProfileDefinitionData pdd = SCMDataConverter.convert(profileDefinition);
            if (pdd.getProfileId() != null) {
                pdd = scmProfileManagementDao.update(pdd, true);
            } else {
                ProfileDefinitionData profileDefinitionData = scmProfileManagementDao.getProfileByName(profileDefinition.getProfileName());
                if(profileDefinitionData != null){
                    throw new SumoException("Error creating profile", "Profile Name Already in use");
                }
                pdd = scmProfileManagementDao.add(pdd, true);
            }

            IdCodeName createdBy = SCMUtil.generateIdCodeName(pdd.getCreatedBy(), "",
                    masterDataCache.getEmployees().get(pdd.getCreatedBy()));
            profileDefinition = SCMDataConverter.convert(pdd, createdBy);
            scmCache.getProfileDefinitions().put(pdd.getProfileId(), SCMDataConverter.convert(pdd, createdBy));
            return profileDefinition;
//        } catch (Exception e){
//            e.printStackTrace();
//            Log.info("---------------");
//            throw new SumoException("Error creating profile", e.getMessage());
//        }
    }
}

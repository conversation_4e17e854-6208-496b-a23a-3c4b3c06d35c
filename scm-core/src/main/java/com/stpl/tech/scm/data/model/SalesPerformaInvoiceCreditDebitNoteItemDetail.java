package com.stpl.tech.scm.data.model;

import javax.persistence.Entity;
import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.JoinColumn;
import javax.persistence.GenerationType;
import javax.persistence.GeneratedValue;

import java.math.BigDecimal;

@Entity
@Table(name = "SALES_PERFORMA_INVOICE_CREDIT_DEBIT_NOTE_ITEM")
public class SalesPerformaInvoiceCreditDebitNoteItemDetail {


    protected Integer itemId;
    protected String itemDesc;
    protected Integer referenceId;
    protected BigDecimal qty;
    protected BigDecimal price;
    protected BigDecimal netAmount;
    protected BigDecimal taxPercent;
    protected BigDecimal taxAmount;
    protected BigDecimal totalAmount;
    protected SalesPerformaInvoiceCreditDebitNoteDetail creditDebitNoteDetail;


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ITEM_ID", nullable = false, unique = true)
    public Integer getItemId() {
        return itemId;
    }

    public void setItemId(Integer itemId) {
        this.itemId = itemId;
    }

    @Column(name = "ITEM_DESC", nullable = false)
    public String getItemDesc() {
        return itemDesc;
    }

    public void setItemDesc(String itemDesc) {
        this.itemDesc = itemDesc;
    }

    @Column(name = "QUANTITY", nullable = false)
    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    @Column(name = "PRICE", nullable = false)
    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Column(name = "NET_AMOUNT", nullable = false)
    public BigDecimal getNetAmount() {
        return netAmount;
    }

    public void setNetAmount(BigDecimal netAmount) {
        this.netAmount = netAmount;
    }

    @Column(name = "TAX_PERCENT", nullable = false)
    public BigDecimal getTaxPercent() {
        return taxPercent;
    }

    public void setTaxPercent(BigDecimal taxPercent) {this.taxPercent = taxPercent;}

    @Column(name = "TAX_AMOUNT", nullable = false)
    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    @Column(name = "TOTAL_AMOUNT", nullable = false)
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="REFERENCE_ID", nullable = false)
    public SalesPerformaInvoiceCreditDebitNoteDetail getCreditDebitNoteDetail() {
        return creditDebitNoteDetail;
    }

    public void setCreditDebitNoteDetail(SalesPerformaInvoiceCreditDebitNoteDetail creditDebitNoteDetail) {
        this.creditDebitNoteDetail = creditDebitNoteDetail;
    }
}

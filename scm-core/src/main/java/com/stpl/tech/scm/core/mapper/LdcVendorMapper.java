package com.stpl.tech.scm.core.mapper;

import com.stpl.tech.scm.data.model.LdcVendorData;
import com.stpl.tech.scm.domain.model.LdcVendorDomain;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface LdcVendorMapper {

    LdcVendorMapper INSTANCE = Mappers.getMapper(LdcVendorMapper.class);
    LdcVendorDomain getLdcVendorDomain(LdcVendorData ldcVendorData);

    LdcVendorData getLdcVendorData(LdcVendorDomain ldcVendorDomain);

}

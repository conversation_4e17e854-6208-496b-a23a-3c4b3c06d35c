package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * Created by <PERSON><PERSON> on 13-06-2016.
 */
@Entity
@Table(name = "FULFILLMENT_UNIT_MAPPING")
public class FulfillmentUnitMappingData {

    private int id;
    private int requestingUnitId;
    private String fulfillmentType;
    private int fulfillingUnitId;
    private String status;


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "FULFILLMENT_UNIT_MAPPING_ID", unique = true, nullable = false)
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Column(name = "REQUESTING_UNIT_ID", nullable = false)
    public int getRequestingUnitId() {
        return requestingUnitId;
    }

    public void setRequestingUnitId(int requestingUnitId) {
        this.requestingUnitId = requestingUnitId;
    }

    @Column(name = "FULFILLMENT_TYPE", nullable = false, length = 30)
    public String getFulfillmentType() {
        return fulfillmentType;
    }

    public void setFulfillmentType(String fulfillmentType) {
        this.fulfillmentType = fulfillmentType;
    }

    @Column(name = "FULFILLING_UNIT_ID", nullable = false)
    public int getFulfillingUnitId() {
        return fulfillingUnitId;
    }

    public void setFulfillingUnitId(int fulfillingUnitId) {
        this.fulfillingUnitId = fulfillingUnitId;
    }

    @Column(name = "MAPPING_STATUS", nullable = false, length = 30)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}

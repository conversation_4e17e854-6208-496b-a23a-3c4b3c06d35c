package com.stpl.tech.scm.core.service.impl;

import com.google.common.base.Stopwatch;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.FullfillmentReportGenerateService;
import com.stpl.tech.scm.core.service.FullfillmentReportService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.ValidationUtil;
import com.stpl.tech.scm.data.dao.FullfillmentReportDao;
import com.stpl.tech.scm.data.transport.model.CriticalProductFF;
import com.stpl.tech.scm.data.transport.model.FullfillmentData;
import com.stpl.tech.scm.data.transport.model.FullfillmentDataUnitLevel;
import com.stpl.tech.scm.data.transport.model.FullfillmentDataWarehouseLevel;
import com.stpl.tech.scm.data.transport.model.MenuToScmData;
import com.stpl.tech.scm.data.transport.model.WarehouseStats;
import com.stpl.tech.scm.notification.email.FullfillmentReportEmailNotification;
import com.stpl.tech.scm.notification.email.template.FullfillmentReportEmailNotificationTemplate;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.notification.AttachmentData;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.time.StopWatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class FullfillmentReportServiceImpl implements FullfillmentReportService {

    private static final Logger LOG = LoggerFactory.getLogger(FullfillmentReportServiceImpl.class);
    @Autowired
    FullfillmentReportDao fullfillmentReportDao;

    @Autowired
    private EnvProperties env;

    @Autowired
    private FullfillmentReportGenerateService fullfillmentReportGenerateService;

    @Autowired
    MasterDataCache masterDataCache;

    private static String requestId;

    private void buildMapFromResult(HashMap<Long, Set<Long>> transferUnitToRequestUnit, HashMap<Long, ArrayList<FullfillmentData>> requestUnitToScmProduct, List<FullfillmentData> result, Boolean removeBakeryProducts) {
        List<Integer> bakeryProductIds = new ArrayList<>(Arrays.asList(100153,
                100246,
                100259,
                100776,
                100856,
                101303,
                103083,
                103188,
                103306,
                103307));

        List<Long> LongBakeryProductIds = bakeryProductIds.stream()
                .mapToLong(Integer::longValue)
                .boxed().collect(Collectors.toList());

        Set<Long> bakeryProductsSet = new HashSet<>(LongBakeryProductIds);

        for (FullfillmentData fd : result) {

            Long trasnferUnitToRequestUnitKey = fd.getTransferringUnitId();
            Set<Long> requestingUnitIds = transferUnitToRequestUnit.getOrDefault(trasnferUnitToRequestUnitKey, new HashSet<>());
            requestingUnitIds.add(fd.getRequestingUnitId());
            transferUnitToRequestUnit.put(trasnferUnitToRequestUnitKey, requestingUnitIds);

            Long requestUnitTOScmProductKey = fd.getRequestingUnitId();
            ArrayList<FullfillmentData> scmProdutcsData = requestUnitToScmProduct.getOrDefault(requestUnitTOScmProductKey, new ArrayList<>());

            if (Boolean.TRUE.equals(removeBakeryProducts) && !bakeryProductsSet.contains(fd.getProductId())) {
                scmProdutcsData.add(fd);
            }

            if (bakeryProductsSet.contains(fd.getProductId())) {
                fd.setIsBakeryProduct(true);
            }
            if (Boolean.FALSE.equals(removeBakeryProducts)) {
                scmProdutcsData.add(fd);
            }

            requestUnitToScmProduct.put(requestUnitTOScmProductKey, scmProdutcsData);
        }
    }

    @Override
    public boolean startFullfillmentReportProcess() throws SumoException {
        Stopwatch totalWatch = Stopwatch.createStarted();
        Stopwatch stepWatch = Stopwatch.createStarted();

        requestId = SCMUtil.getCurrentTimeISTString();

        try {
            logStep(stepWatch, "################### Starting-Fullfillment-Report-Process ###################");
            // -------- Last 30 days --------
            List<FullfillmentData> result = fullfillmentReportDao.getFullfillmentData(30);
            logStep(stepWatch, "Loaded 30-day fullfillment data, size=%d", result.size());

            HashMap<Long, Set<Long>> transferUnitToRequestUnit = new HashMap<>();
            HashMap<Long, ArrayList<FullfillmentData>> requestUnitToScmProduct = new HashMap<>();

            buildMapFromResult(transferUnitToRequestUnit, requestUnitToScmProduct, result, false);

            createImpactedFFPer(transferUnitToRequestUnit, requestUnitToScmProduct);
            logStep(stepWatch, "Created impacted FF percentage with bakery products");

            ArrayList<FullfillmentDataWarehouseLevel> fullfillmentDataWarehouseLevelResultLastThirtyDay = fullfillmentProcessDataWarehouseLevel(transferUnitToRequestUnit, requestUnitToScmProduct);
            logStep(stepWatch, "Processed warehouse level data for last 30 days, size=%d", fullfillmentDataWarehouseLevelResultLastThirtyDay.size());

            transferUnitToRequestUnit = new HashMap<>();
            requestUnitToScmProduct = new HashMap<>();
            buildMapFromResult(transferUnitToRequestUnit, requestUnitToScmProduct, result, true);

            createImpactedFFPer(transferUnitToRequestUnit, requestUnitToScmProduct);
            logStep(stepWatch, "Created impacted FF percentage without bakery products");

            ArrayList<FullfillmentDataWarehouseLevel> fullfillmentDataWarehouseLevelResultLastThirtyDayWithoutBakery = fullfillmentProcessDataWarehouseLevel(transferUnitToRequestUnit, requestUnitToScmProduct);
            logStep(stepWatch, "Processed warehouse level data (30 days, without bakery), size=%d", fullfillmentDataWarehouseLevelResultLastThirtyDayWithoutBakery.size());

            for (FullfillmentDataWarehouseLevel fd : fullfillmentDataWarehouseLevelResultLastThirtyDay) {
                for (FullfillmentDataWarehouseLevel fd2 : fullfillmentDataWarehouseLevelResultLastThirtyDayWithoutBakery) {
                    if (Objects.equals(fd.getTransferringUnit(), fd2.getTransferringUnit())) {
                        fd.setWithoutBakeryAvgFPer(fd2.getAvgImFPer());
                    }
                }
            }

            // -------- Last 1 day --------
            List<FullfillmentData> lastDayResult = fullfillmentReportDao.getFullfillmentData(1);
            logStep(stepWatch, "Loaded 1-day fullfillment data, size=%d", lastDayResult.size());

            transferUnitToRequestUnit = new HashMap<>();
            requestUnitToScmProduct = new HashMap<>();
            buildMapFromResult(transferUnitToRequestUnit, requestUnitToScmProduct, lastDayResult, false);

            createImpactedFFPer(transferUnitToRequestUnit, requestUnitToScmProduct);
            logStep(stepWatch, "Created impacted FF percentage for last day");

            ArrayList<FullfillmentDataWarehouseLevel> fullfillmentDataWarehouseLevelResult = fullfillmentProcessDataWarehouseLevel(transferUnitToRequestUnit, requestUnitToScmProduct);
            logStep(stepWatch, "Processed warehouse level data (last day), size=%d", fullfillmentDataWarehouseLevelResult.size());

            ArrayList<FullfillmentDataUnitLevel> fullfillmentDataUnitLevelResult = fullfillmentProcessUnitlevel(transferUnitToRequestUnit, requestUnitToScmProduct);
            logStep(stepWatch, "Processed unit level data (last day), size=%d", fullfillmentDataUnitLevelResult.size());

            transferUnitToRequestUnit = new HashMap<>();
            requestUnitToScmProduct = new HashMap<>();
            buildMapFromResult(transferUnitToRequestUnit, requestUnitToScmProduct, lastDayResult, true);

            createImpactedFFPer(transferUnitToRequestUnit, requestUnitToScmProduct);
            logStep(stepWatch, "Created impacted FF percentage for last day (without bakery)");

            ArrayList<FullfillmentDataWarehouseLevel> fullfillmentDataWarehouseLevelResultWithoutBakery = fullfillmentProcessDataWarehouseLevel(transferUnitToRequestUnit, requestUnitToScmProduct);
            logStep(stepWatch, "Processed warehouse level data (last day, without bakery), size=%d", fullfillmentDataWarehouseLevelResultWithoutBakery.size());

            for (FullfillmentDataWarehouseLevel fd : fullfillmentDataWarehouseLevelResult) {
                for (FullfillmentDataWarehouseLevel fd2 : fullfillmentDataWarehouseLevelResultWithoutBakery) {
                    if (Objects.equals(fd.getTransferringUnit(), fd2.getTransferringUnit())) {
                        fd.setWithoutBakeryAvgFPer(fd2.getAvgImFPer());
                    }
                }
            }

            // -------- Last 7 days --------
            List<FullfillmentData> lastSevenDayResult = fullfillmentReportDao.getFullfillmentData(7);
            logStep(stepWatch, "Loaded 7-day fullfillment data, size=%d", lastSevenDayResult.size());

            transferUnitToRequestUnit = new HashMap<>();
            requestUnitToScmProduct = new HashMap<>();
            buildMapFromResult(transferUnitToRequestUnit, requestUnitToScmProduct, lastSevenDayResult, false);
            createImpactedFFPer(transferUnitToRequestUnit, requestUnitToScmProduct);
            ArrayList<FullfillmentDataWarehouseLevel> fullfillmentDataWarehouseSevenDaysLevelResult = fullfillmentProcessDataWarehouseLevel(transferUnitToRequestUnit, requestUnitToScmProduct);
            logStep(stepWatch, "Processed warehouse level data (7 days), size=%d", fullfillmentDataWarehouseSevenDaysLevelResult.size());

            transferUnitToRequestUnit = new HashMap<>();
            requestUnitToScmProduct = new HashMap<>();
            buildMapFromResult(transferUnitToRequestUnit, requestUnitToScmProduct, lastSevenDayResult, true);
            createImpactedFFPer(transferUnitToRequestUnit, requestUnitToScmProduct);
            ArrayList<FullfillmentDataWarehouseLevel> fullfillmentDataWarehouseSevenDaysLevelResultWithoutBakery = fullfillmentProcessDataWarehouseLevel(transferUnitToRequestUnit, requestUnitToScmProduct);
            logStep(stepWatch, "Processed warehouse level data (7 days, without bakery), size=%d", fullfillmentDataWarehouseSevenDaysLevelResultWithoutBakery.size());

            for (FullfillmentDataWarehouseLevel fd : fullfillmentDataWarehouseSevenDaysLevelResult) {
                for (FullfillmentDataWarehouseLevel fd2 : fullfillmentDataWarehouseSevenDaysLevelResultWithoutBakery) {
                    if (Objects.equals(fd.getTransferringUnit(), fd2.getTransferringUnit())) {
                        fd.setWithoutBakeryAvgFPer(fd2.getAvgImFPer());
                    }
                }
            }

            ArrayList<FullfillmentDataUnitLevel> fullfillmentDataUnitSevenDaysLevelResult = fullfillmentProcessUnitlevel(transferUnitToRequestUnit, requestUnitToScmProduct);
            logStep(stepWatch, "Processed unit level data (7 days), size=%d", fullfillmentDataUnitSevenDaysLevelResult.size());

            String dateTime = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss").format(new Date());
            String fileName = "Fullfillment_Reports(New)-" + dateTime + ".xlsx";
            generateRequiredReports( fullfillmentDataWarehouseLevelResult,
                    fullfillmentDataWarehouseLevelResultLastThirtyDay,
                    fullfillmentDataWarehouseSevenDaysLevelResult,
                    lastDayResult, lastSevenDayResult, fullfillmentDataUnitLevelResult,
                    fullfillmentDataUnitSevenDaysLevelResult, stepWatch, fileName);

            // -------- Critical Products --------
            List<CriticalProductFF> criticalProductsSummary = getFFSummaryForCriticalProducts(lastDayResult, lastSevenDayResult, result);
            logStep(stepWatch, "Generated critical products summary, size=%d", criticalProductsSummary.size());

            sendEmail(fileName, fullfillmentDataWarehouseLevelResult, fullfillmentDataWarehouseLevelResultLastThirtyDay, criticalProductsSummary);
            logStep(stepWatch, "Sent fulfillment report email");

            logStep(totalWatch, "######## Fullfillment Reports Process Completed Successfully, Total");
            return true;
        } catch (Exception e) {
            LOG.error("######## Error in Fullfillment Report Process after {} ms: {} [request_id: {}]", totalWatch.elapsed(TimeUnit.MILLISECONDS), e.getMessage(), requestId, e);
            throw new SumoException("Error in Fullfillment Report Process ", e);
        }
    }

    // -------- Report Generation --------
    private void generateRequiredReports(ArrayList<FullfillmentDataWarehouseLevel> fullfillmentDataWarehouseLevelResult,
                                         ArrayList<FullfillmentDataWarehouseLevel> fullfillmentDataWarehouseLevelResultLastThirtyDay,
                                         ArrayList<FullfillmentDataWarehouseLevel> fullfillmentDataWarehouseSevenDaysLevelResult,
                                         List<FullfillmentData> lastDayResult, List<FullfillmentData> lastSevenDayResult,
                                         ArrayList<FullfillmentDataUnitLevel> fullfillmentDataUnitLevelResult,
                                         ArrayList<FullfillmentDataUnitLevel> fullfillmentDataUnitSevenDaysLevelResult,
                                         Stopwatch stepWatch, String fileName) throws SumoException {

        fullfillmentReportGenerateService.generateWarehouseLevelSheet(fullfillmentDataWarehouseLevelResult, "Fullfillment Percentage - Kitchen and Warehouse", true, fileName);
        logStep(stepWatch, "Generated warehouse-level sheet (1 day)");

        fullfillmentReportGenerateService.generateWarehouseLevelSheet(fullfillmentDataWarehouseLevelResultLastThirtyDay, "Fullfillment Percentage For This Month - Kitchen or Warehouse", false, fileName);
        logStep(stepWatch, "Generated warehouse-level sheet (30 days)");

        fullfillmentReportGenerateService.generateWarehouseLevelSheet(fullfillmentDataWarehouseSevenDaysLevelResult, "Fullfillment Percentage(7 days) - Kitchen and Warehouse", false, fileName);
        logStep(stepWatch, "Generated warehouse-level sheet (7 days)");

        fullfillmentReportGenerateService.generateOnTimeDateSheet(fullfillmentDataWarehouseLevelResult, " (1 day) Percentage of On-Time, On-Date, and Delayed RO's Raised – Kitchen & Warehouse", fileName);
        logStep(stepWatch, "Generated Percentage of On-Time, On-Date, and Delayed RO's Raised – Kitchen & Warehouse (1 day)");

        fullfillmentReportGenerateService.generateOnTimeDateSheet(fullfillmentDataWarehouseSevenDaysLevelResult, "(7 days) Percentage of On-Time, On-Date, and Delayed RO's Raised – Kitchen & Warehouse", fileName);
        logStep(stepWatch, "Generated Percentage of On-Time, On-Date, and Delayed RO's Raised – Kitchen & Warehouse (7 days)");

        fullfillmentReportGenerateService.generateDetailFullFillmentSheet(lastDayResult, fileName, "Fullfilment Details - Kitchen and Warehouse");
        logStep(stepWatch, "Generated detail sheet (last day)");

        fullfillmentReportGenerateService.generateDetailFullFillmentSheet(lastSevenDayResult, fileName, "Fullfilment Details(7 days) - Kitchen and Warehouse");
        logStep(stepWatch, "Generated detail sheet (7 days)");

        fullfillmentReportGenerateService.generateUnitLevelDetailFullFillmentSheet(fullfillmentDataUnitLevelResult, fileName, "Fullfilment Details Unit level - Kitchen and Warehouse");
        logStep(stepWatch, "Generated unit-level detail sheet (last day)");

        fullfillmentReportGenerateService.generateUnitLevelDetailFullFillmentSheet(fullfillmentDataUnitSevenDaysLevelResult, fileName, "Fullfilment Details(7 days) Unit level - Kitchen and Warehouse");
        logStep(stepWatch, "Generated unit-level detail sheet (7 days)");
    }

    private void logStep(Stopwatch watch, String message, Object... args) {
        long elapsed = watch.elapsed(TimeUnit.MILLISECONDS);
        LOG.info("✔ {} (time={} ms --> [request_id: {}])", String.format(message, args), elapsed, requestId);
        watch.reset().start(); // reset for the next step
    }


    private void createImpactedFFPer(HashMap<Long, Set<Long>> transferUnitToRequestUnit, HashMap<Long, ArrayList<FullfillmentData>> requestUnitToScmProduct) throws SumoException {
        for (Map.Entry<Long, Set<Long>> t : transferUnitToRequestUnit.entrySet()) {
            // get transfer unit id;
            Long transferUnit = t.getKey();
            Set<Long> requestingUnits = t.getValue();

            for (Long r : requestingUnits) {
                // All scm products for requesting_unit <- transfer_unit
                ArrayList<FullfillmentData> scm_products = requestUnitToScmProduct.get(r);

                List<FullfillmentData> criticalProducts = scm_products.stream().filter(p -> Objects.equals(p.getIsCritical(), "Y") && Objects.equals(p.getTransferringUnitId(), transferUnit)).collect(Collectors.toList());
                List<FullfillmentData> nonCriticalProducts = scm_products.stream().filter(p -> Objects.equals(p.getIsCritical(), "N") && Objects.equals(p.getTransferringUnitId(), transferUnit)).collect(Collectors.toList());

                // Map for scm_product_id -> Unique List(menu_product_id);
                HashMap<Long, Set<Long>> scmProductToMenuProduct = new HashMap<>();
                // Map for menu_product_id -> List(scm_products)
                HashMap<Long, ArrayList<MenuToScmData>> menuProductToScm = new HashMap<>();
                // get menu product data for requesting unit
                fullfillmentReportDao.getMenuToScmData(r, scmProductToMenuProduct, menuProductToScm);

                // get all impacted products for specific critical product
                for (FullfillmentData fdCritical : criticalProducts) {
                    Long criticalProductId = fdCritical.getProductId();
                    Set<Long> impactedScmProducts = new HashSet<>();
                    // get all menuProducts of the critical scm product
                    Set<Long> menuProducts = scmProductToMenuProduct.get(criticalProductId);

                    // for each menu product get the impacted scm product
                    for (Long mpId : menuProducts) {
                        ArrayList<MenuToScmData> products = menuProductToScm.get(mpId);
                        // get all impacted scm products of specific menu product
                        for (MenuToScmData m : products) {
                            if (!Objects.equals(m.getScmProductId(), criticalProductId)) {
                                impactedScmProducts.add(m.getScmProductId());
                            }
                        }
                    }
                    //set impacted scm products for specific critical product
                    fdCritical.setImpactedScmProducts(impactedScmProducts);
                }

                // make impact of critical product on non-critical product
                for (FullfillmentData fdCritical : criticalProducts) {
                    Double fullfillmentPerOfCriticalProduct = fdCritical.getFullfillmentPercentage() / 100;
                    Set<Long> impactedProducts = fdCritical.getImpactedScmProducts();
                    for (FullfillmentData fdNonCritical : nonCriticalProducts) {
                        if (impactedProducts.contains(fdNonCritical.getProductId())) {
                            if (fdNonCritical.getImpactedFullfillmentPercentage() == -1) {
                                Double fullfillPercentage = fdNonCritical.getFullfillmentPercentage();
                                Double impactedFfPercentage = (fullfillPercentage / 100) * fullfillmentPerOfCriticalProduct;
                                fdNonCritical.setImpactedFullfillmentPercentage(impactedFfPercentage * 100);
                            } else {
                                Double impfullfillPercentage = fdNonCritical.getImpactedFullfillmentPercentage();
                                Double impactedFfPercentage = (impfullfillPercentage / 100) * fullfillmentPerOfCriticalProduct;
                                fdNonCritical.setImpactedFullfillmentPercentage(impactedFfPercentage * 100);
                            }
                            fdNonCritical.setIsImpacted("Y");
                        } else {
                            fdNonCritical.setImpactedFullfillmentPercentage(fdNonCritical.getFullfillmentPercentage());
                            fdNonCritical.setIsImpacted("N");
                        }
                    }
                }

                // make impact of critical product on critical product
                for (FullfillmentData fdCritical : criticalProducts) {
                    Double fullfillmentPerOfCriticalProduct = fdCritical.getFullfillmentPercentage() / 100;
                    Set<Long> impactedProducts = fdCritical.getImpactedScmProducts();
                    for (FullfillmentData fdCriticalOther : criticalProducts) {
                        if (!Objects.equals(fdCritical.getProductId(), fdCriticalOther.getProductId()) && impactedProducts.contains(fdCriticalOther.getProductId())) {
                            if (fdCriticalOther.getImpactedFullfillmentPercentage() == -1) {
                                Double fullfillPercentage = fdCriticalOther.getFullfillmentPercentage();
                                Double impactedFfPercentage = (fullfillPercentage / 100) * fullfillmentPerOfCriticalProduct;
                                fdCriticalOther.setImpactedFullfillmentPercentage(impactedFfPercentage * 100);
                            } else {
                                Double impfullfillPercentage = fdCriticalOther.getImpactedFullfillmentPercentage();
                                Double impactedFfPercentage = (impfullfillPercentage / 100) * fullfillmentPerOfCriticalProduct;
                                fdCriticalOther.setImpactedFullfillmentPercentage(impactedFfPercentage * 100);
                            }
                            fdCriticalOther.setIsImpacted("Y");
                        } else {
                            fdCriticalOther.setImpactedFullfillmentPercentage(fdCriticalOther.getFullfillmentPercentage());
                            fdCriticalOther.setIsImpacted("N");
                        }
                    }
                }
            }
        }
    }

    private ArrayList<FullfillmentDataWarehouseLevel> fullfillmentProcessDataWarehouseLevel(HashMap<Long, Set<Long>> transferUnitToRequestUnit, HashMap<Long, ArrayList<FullfillmentData>> requestUnitToScmProduct) {
        ArrayList<FullfillmentDataUnitLevel> ffUnitLevelData = fullfillmentProcessUnitlevel(transferUnitToRequestUnit, requestUnitToScmProduct);
        // Group unit level data by transfer unit
        HashMap<Long, ArrayList<FullfillmentDataUnitLevel>> transferUnitToRequestUnitLevel = groupByTransferUnit(ffUnitLevelData);
        ArrayList<FullfillmentDataWarehouseLevel> fullfillmentDataWarehouseLevelResult = new ArrayList<>();

        for (Map.Entry<Long, ArrayList<FullfillmentDataUnitLevel>> entry : transferUnitToRequestUnitLevel.entrySet()) {
            Long transferUnit = entry.getKey();
            ArrayList<FullfillmentDataUnitLevel> requestingUnits = entry.getValue();
            FullfillmentDataWarehouseLevel fdWarehouseLevel = buildWarehouseLevel(transferUnit, requestingUnits, requestUnitToScmProduct);
            fullfillmentDataWarehouseLevelResult.add(fdWarehouseLevel);
        }
        return fullfillmentDataWarehouseLevelResult;
    }

    private HashMap<Long, ArrayList<FullfillmentDataUnitLevel>> groupByTransferUnit(
            ArrayList<FullfillmentDataUnitLevel> ffUnitLevelData) {
        HashMap<Long, ArrayList<FullfillmentDataUnitLevel>> transferUnitToRequestUnitLevel = new HashMap<>();

        for (FullfillmentDataUnitLevel fd : ffUnitLevelData) {
            Long transferUnitId = fd.getTransferringUnitId();
            ArrayList<FullfillmentDataUnitLevel> requestingUnitData =
                    transferUnitToRequestUnitLevel.getOrDefault(transferUnitId, new ArrayList<>());
            requestingUnitData.add(fd);
            transferUnitToRequestUnitLevel.put(transferUnitId, requestingUnitData);
        }

        return transferUnitToRequestUnitLevel;
    }

    private FullfillmentDataWarehouseLevel buildWarehouseLevel(Long transferUnit,
                                                               ArrayList<FullfillmentDataUnitLevel> requestingUnits,
                                                               HashMap<Long, ArrayList<FullfillmentData>> requestUnitToScmProduct) {

        FullfillmentDataWarehouseLevel fdWarehouseLevel = new FullfillmentDataWarehouseLevel();
        // Initialize counters and accumulators
        WarehouseStats stats = new WarehouseStats();

        for (FullfillmentDataUnitLevel r : requestingUnits) {
            if (stats.count == 0) {
                fdWarehouseLevel.setTransferringUnit(r.getTransferringUnit());
            }
            ArrayList<FullfillmentData> products = requestUnitToScmProduct.get(r.getRequestingUnitId());

            // Categorize products
            List<FullfillmentData> transferUnitProducts = filterCriticalProducts(products, transferUnit);
            Map<ProductLevel, List<FullfillmentData>> grouped = groupByLevel(products, transferUnit);

            // Aggregate stats
            accumulateLevelStats(grouped.getOrDefault(ProductLevel.LEVEL_1, List.of()), stats, ProductLevel.LEVEL_1);
            accumulateLevelStats(grouped.getOrDefault(ProductLevel.LEVEL_2, List.of()), stats, ProductLevel.LEVEL_2);
            accumulateLevelStats(grouped.getOrDefault(ProductLevel.LEVEL_3, List.of()), stats, ProductLevel.LEVEL_3);
            accumulateLevelStats(grouped.getOrDefault(ProductLevel.LEVEL_4, List.of()), stats, ProductLevel.LEVEL_4);
            accumulateLevelStats(grouped.getOrDefault(ProductLevel.LEVEL_5, List.of()), stats, ProductLevel.LEVEL_5);
            accumulateLevelStats(grouped.getOrDefault(ProductLevel.NA, List.of()), stats, ProductLevel.NA);
            accumulateCriticalStats(transferUnitProducts, stats);

            stats.count++;

            // Total FF
            stats.sumF += r.getFPerWeightedAvg();
            stats.onTimeFTotal += r.getOnTimeFPerWeightedAvg();
            stats.onDateFTotal += r.getOnDateFPerWeightedAvg();
            stats.totalOnTimeRoRaisedPer += r.getTotalRoRaisedOnTimePer();
            stats.totalOnDateRoRaisedPer += r.getTotalRoRaisedOnDatePer();
            stats.totalDelayRoRaisedPer += r.getTotalDelayRoRaisedPer();

            // Total Impacted FF
            stats.sumImF += r.getImFPerWeightedAvg();

            if (r.getIsBakeryProduct()) {
                stats.bakerySum += r.getBakeryProductFPAvg();
                stats.bakeryOnDateFFSum += r.getBakeryProductOnDateFFAvg();
                stats.bakeryOnTimeFFSum += r.getBakeryProductOnTimeFFAvg();

                stats.bakeryRoRaisedOnTime += r.getBakeryRoRaisedOnTimePer();
                stats.bakeryRoRaisedOnDate += r.getBakeryRoRaisedOnDatePer();
                stats.bakeryRoDelayRaised += r.getBakeryDelayRoRaisedPer();
                stats.bakeryCount++;
            }
        }

        // Finalize values into fdWarehouseLevel
        assignWarehouseStats(fdWarehouseLevel, stats);

        return fdWarehouseLevel;
    }

    private List<FullfillmentData> filterCriticalProducts(List<FullfillmentData> products, Long transferUnit) {
        return products.stream()
                .filter(p -> Objects.equals(p.getTransferringUnitId(), transferUnit)
                        && Objects.equals(p.getIsCritical(), "Y"))
                .collect(Collectors.toList());
    }

    private Map<ProductLevel, List<FullfillmentData>> groupByLevel(List<FullfillmentData> products, Long transferUnit) {
        return products.stream()
                .filter(p -> Objects.equals(p.getTransferringUnitId(), transferUnit))
                .collect(Collectors.groupingBy(p -> {
                    String levelStr = p.getProductLevel();
                    if (!ValidationUtil.checkStringIsNotEmpty(levelStr)) {
                        return ProductLevel.NA;
                    }
                    try {
                        return ProductLevel.valueOf(levelStr);
                    } catch (IllegalArgumentException e) {
                        return ProductLevel.NA; // fallback safety
                    }
                }));
    }


    private void accumulateLevelStats(List<FullfillmentData> products, WarehouseStats stats, ProductLevel level) {
        for (FullfillmentData fd : products) {
            switch (level) {
                case LEVEL_1 -> {
                    stats.productLevel1Count++;
                    stats.productLevel1Qty += fd.getRequestedAbsoluteQuantity();
                    stats.productLevel1FF += fd.getRequestedAbsoluteQuantity() * fd.getFullfillmentPercentage();
                    stats.productLevel1OnTimeFF += fd.getRequestedAbsoluteQuantity() * fd.getOnTimeFullFillmentPercentage();
                    stats.productLevel1OnDateFF += fd.getRequestedAbsoluteQuantity() * fd.getOnDateFullFillmentPercentage();
                    stats.productLevel1RaisedOnTime += fd.getTotalRos() * fd.getOnTimeRoRaisedPercentage();
                    stats.productLevel1RaisedOnDate += fd.getTotalRos() * fd.getOnDateRoRaisedPercentage();
                    stats.productLevel1DelayRaised += fd.getTotalRos() * fd.getDelayRoRaisedPercentage();
                }
                case LEVEL_2 -> {
                    stats.productLevel2Count++;
                    stats.productLevel2Qty += fd.getRequestedAbsoluteQuantity();
                    stats.productLevel2FF += fd.getRequestedAbsoluteQuantity() * fd.getFullfillmentPercentage();
                    stats.productLevel2OnTimeFF += fd.getRequestedAbsoluteQuantity() * fd.getOnTimeFullFillmentPercentage();
                    stats.productLevel2OnDateFF += fd.getRequestedAbsoluteQuantity() * fd.getOnDateFullFillmentPercentage();
                    stats.productLevel2RaisedOnTime += fd.getTotalRos() * fd.getOnTimeRoRaisedPercentage();
                    stats.productLevel2RaisedOnDate += fd.getTotalRos() * fd.getOnDateRoRaisedPercentage();
                    stats.productLevel2DelayRaised += fd.getTotalRos() * fd.getDelayRoRaisedPercentage();
                }
                case LEVEL_3 -> {
                    stats.productLevel3Count++;
                    stats.productLevel3Qty += fd.getRequestedAbsoluteQuantity();
                    stats.productLevel3FF += fd.getRequestedAbsoluteQuantity() * fd.getFullfillmentPercentage();
                    stats.productLevel3OnTimeFF += fd.getRequestedAbsoluteQuantity() * fd.getOnTimeFullFillmentPercentage();
                    stats.productLevel3OnDateFF += fd.getRequestedAbsoluteQuantity() * fd.getOnDateFullFillmentPercentage();
                    stats.productLevel3RaisedOnTime += fd.getTotalRos() * fd.getOnTimeRoRaisedPercentage();
                    stats.productLevel3RaisedOnDate += fd.getTotalRos() * fd.getOnDateRoRaisedPercentage();
                    stats.productLevel3DelayRaised += fd.getTotalRos() * fd.getDelayRoRaisedPercentage();
                }
                case LEVEL_4 -> {
                    stats.productLevel4Count++;
                    stats.productLevel4Qty += fd.getRequestedAbsoluteQuantity();
                    stats.productLevel4FF += fd.getRequestedAbsoluteQuantity() * fd.getFullfillmentPercentage();
                    stats.productLevel4OnTimeFF += fd.getRequestedAbsoluteQuantity() * fd.getOnTimeFullFillmentPercentage();
                    stats.productLevel4OnDateFF += fd.getRequestedAbsoluteQuantity() * fd.getOnDateFullFillmentPercentage();
                    stats.productLevel4RaisedOnTime += fd.getTotalRos() * fd.getOnTimeRoRaisedPercentage();
                    stats.productLevel4RaisedOnDate += fd.getTotalRos() * fd.getOnDateRoRaisedPercentage();
                    stats.productLevel4DelayRaised += fd.getTotalRos() * fd.getDelayRoRaisedPercentage();
                }
                case LEVEL_5 -> {
                    stats.productLevel5Count++;
                    stats.productLevel5Qty += fd.getRequestedAbsoluteQuantity();
                    stats.productLevel5FF += fd.getRequestedAbsoluteQuantity() * fd.getFullfillmentPercentage();
                    stats.productLevel5OnTimeFF += fd.getRequestedAbsoluteQuantity() * fd.getOnTimeFullFillmentPercentage();
                    stats.productLevel5OnDateFF += fd.getRequestedAbsoluteQuantity() * fd.getOnDateFullFillmentPercentage();
                    stats.productLevel5RaisedOnTime += fd.getTotalRos() * fd.getOnTimeRoRaisedPercentage();
                    stats.productLevel5RaisedOnDate += fd.getTotalRos() * fd.getOnDateRoRaisedPercentage();
                    stats.productLevel5DelayRaised += fd.getTotalRos() * fd.getDelayRoRaisedPercentage();
                }
                case NA -> {
                    stats.productLevelNACount++;
                    stats.productLevelNAQty += fd.getRequestedAbsoluteQuantity();
                    stats.productLevelNAFF += fd.getRequestedAbsoluteQuantity() * fd.getFullfillmentPercentage();
                    stats.productLevelNAOnTimeFF += fd.getRequestedAbsoluteQuantity() * fd.getOnTimeFullFillmentPercentage();
                    stats.productLevelNAOnDateFF += fd.getRequestedAbsoluteQuantity() * fd.getOnDateFullFillmentPercentage();
                    stats.productLevelNARORaisedOnTime += fd.getTotalRos() * fd.getOnTimeRoRaisedPercentage();
                    stats.productLevelNARORaisedOnDate += fd.getTotalRos() * fd.getOnDateRoRaisedPercentage();
                    stats.productLevelNADelayedRo += fd.getTotalRos() * fd.getDelayRoRaisedPercentage();
                }
            }
        }
    }

    private void accumulateCriticalStats(List<FullfillmentData> criticalProducts, WarehouseStats stats) {
        for (FullfillmentData fd : criticalProducts) {
            stats.criticalCount++;
            stats.criticalSum += fd.getImpactedFullfillmentPercentage();
            stats.criticalProductQty += fd.getRequestedAbsoluteQuantity();
            stats.criticalProductFF += fd.getRequestedAbsoluteQuantity() * fd.getFullfillmentPercentage();
            stats.criticalProductOnTimeFF += fd.getRequestedAbsoluteQuantity() * fd.getOnTimeFullFillmentPercentage();
            stats.criticalProductOnDateFF += fd.getRequestedAbsoluteQuantity() * fd.getOnDateFullFillmentPercentage();

            stats.criticalProdRoRaisedOnTimeFF += fd.getTotalRos() * fd.getOnTimeRoRaisedPercentage();
            stats.criticalProdRoRaisedOnDateFF += fd.getTotalRos() * fd.getOnDateRoRaisedPercentage();
            stats.criticalProdRoRaisedDelayFF += fd.getTotalRos() * fd.getDelayRoRaisedPercentage();
        }
    }


    private void assignWarehouseStats(FullfillmentDataWarehouseLevel fdWarehouseLevel, WarehouseStats stats) {
        // Bakery
        fdWarehouseLevel.setBakeryFP(stats.bakeryCount == 0 ? -1.0 : stats.bakerySum / stats.bakeryCount);
        fdWarehouseLevel.setBakeryOnDateFP(stats.bakeryCount == 0 ? -1.0 : stats.bakeryOnDateFFSum / stats.bakeryCount);
        fdWarehouseLevel.setBakeryOnTimeFP(stats.bakeryCount == 0 ? -1.0 : stats.bakeryOnTimeFFSum / stats.bakeryCount);
        fdWarehouseLevel.setBakeryOnDateRoRaisedPer(stats.bakeryCount == 0 ? -1.0 : stats.bakeryRoRaisedOnDate / stats.bakeryCount);
        fdWarehouseLevel.setBakeryOnTimeRoRaisedPer(stats.bakeryCount == 0 ? -1.0 : stats.bakeryRoRaisedOnTime / stats.bakeryCount);
        fdWarehouseLevel.setBakeryRoRaisedDelayPer(stats.bakeryCount == 0 ? -1.0 : stats.bakeryRoDelayRaised / stats.bakeryCount);

        // Avg Total
        fdWarehouseLevel.setAvgFPer(stats.count == 0 ? 0.0 : stats.sumF / stats.count);
        fdWarehouseLevel.setAvgOnDateFFPer(stats.count == 0 ? 0.0 : stats.onDateFTotal / stats.count);
        fdWarehouseLevel.setAvgOnTimeFFPer(stats.count == 0 ? 0.0 : stats.onTimeFTotal / stats.count);
        fdWarehouseLevel.setAvgOnTimeRoRaisedPer(stats.count == 0 ? 0.0 : stats.totalOnTimeRoRaisedPer / stats.count);
        fdWarehouseLevel.setAvgOnDateRoRaisedPer(stats.count == 0 ? 0.0 : stats.totalOnDateRoRaisedPer / stats.count);
        fdWarehouseLevel.setAvgDelayRoRaisedPer(stats.count == 0 ? 0.0 : stats.totalDelayRoRaisedPer / stats.count);

        // Impacted Total
        fdWarehouseLevel.setAvgImFPer(stats.count == 0 ? 0.0 : stats.sumImF / stats.count);

        // Critical
        fdWarehouseLevel.setIsCriticalProd(stats.criticalCount == 0 ? "N" : "Y");
        fdWarehouseLevel.setCriticalAvg(stats.criticalCount == 0 ? -1.0 : stats.criticalSum / stats.criticalCount);
        fdWarehouseLevel.setCriticalProductFF(stats.criticalCount == 0 ? -1.0 : stats.criticalProductFF / stats.criticalProductQty);
        fdWarehouseLevel.setCriticalOnTimeFF(stats.criticalCount == 0 ? -1.0 : stats.criticalProductOnTimeFF / stats.criticalProductQty);
        fdWarehouseLevel.setCriticalOnDateFF(stats.criticalCount == 0 ? -1.0 : stats.criticalProductOnDateFF / stats.criticalProductQty);

        fdWarehouseLevel.setCriticalOnTimeRaisedFF(stats.criticalCount == 0 ? -1.0 : stats.criticalProdRoRaisedOnTimeFF / stats.criticalCount);
        fdWarehouseLevel.setCriticalOnDateRaisedFF(stats.criticalCount == 0 ? -1.0 : stats.criticalProdRoRaisedOnDateFF / stats.criticalCount);
        fdWarehouseLevel.setCriticalDelayRaisedFF(stats.criticalCount == 0 ? -1.0 : stats.criticalProdRoRaisedDelayFF / stats.criticalCount);

        // FF, ON_TIME_FF, ON_DATE_FF for products Level_1
        fdWarehouseLevel.setProductLevel1FFPer(stats.productLevel1Count == 0 ? -1.0 : stats.productLevel1FF / stats.productLevel1Qty);
        fdWarehouseLevel.setProductLevel1OnDateFFPer(stats.productLevel1Count == 0 ? -1.0 : stats.productLevel1OnDateFF / stats.productLevel1Qty);
        fdWarehouseLevel.setProductLevel1OnTimeFFPer(stats.productLevel1Count == 0 ? -1.0 : stats.productLevel1OnTimeFF / stats.productLevel1Qty);
        fdWarehouseLevel.setProductLevel1RaisedOnTimePer(stats.productLevel1Count == 0 ? -1.0 : stats.productLevel1RaisedOnTime / stats.productLevel1Count);
        fdWarehouseLevel.setProductLevel1RaisedOnDatePer(stats.productLevel1Count == 0 ? -1.0 : stats.productLevel1RaisedOnDate / stats.productLevel1Count);
        fdWarehouseLevel.setProductLevel1DelayRaisedPer(stats.productLevel1Count == 0 ? -1.0 : stats.productLevel1DelayRaised / stats.productLevel1Count);

        // FF, ON_TIME_FF, ON_DATE_FF for products Level_2
        fdWarehouseLevel.setProductLevel2FFPer(stats.productLevel2Count == 0 ? -1.0 : stats.productLevel2FF / stats.productLevel2Qty);
        fdWarehouseLevel.setProductLevel2OnDateFFPer(stats.productLevel2Count == 0 ? -1.0 : stats.productLevel2OnDateFF / stats.productLevel2Qty);
        fdWarehouseLevel.setProductLevel2OnTimeFFPer(stats.productLevel2Count == 0 ? -1.0 : stats.productLevel2OnTimeFF / stats.productLevel2Qty);
        fdWarehouseLevel.setProductLevel2RaisedOnTimePer(stats.productLevel2Count == 0 ? -1.0 : stats.productLevel2RaisedOnTime / stats.productLevel2Count);
        fdWarehouseLevel.setProductLevel2RaisedOnDatePer(stats.productLevel2Count == 0 ? -1.0 : stats.productLevel2RaisedOnDate / stats.productLevel2Count);
        fdWarehouseLevel.setProductLevel2DelayRaisedPer(stats.productLevel2Count == 0 ? -1.0 : stats.productLevel2DelayRaised / stats.productLevel2Count);

        // FF, ON_TIME_FF, ON_DATE_FF for products Level_3
        fdWarehouseLevel.setProductLevel3FFPer(stats.productLevel3Count == 0 ? -1.0 : stats.productLevel3FF / stats.productLevel3Qty);
        fdWarehouseLevel.setProductLevel3OnDateFFPer(stats.productLevel3Count == 0 ? -1.0 : stats.productLevel3OnDateFF / stats.productLevel3Qty);
        fdWarehouseLevel.setProductLevel3OnTimeFFPer(stats.productLevel3Count == 0 ? -1.0 : stats.productLevel3OnTimeFF / stats.productLevel3Qty);
        fdWarehouseLevel.setProductLevel3RaisedOnTimePer(stats.productLevel3Count == 0 ? -1.0 : stats.productLevel3RaisedOnTime / stats.productLevel3Count);
        fdWarehouseLevel.setProductLevel3RaisedOnDatePer(stats.productLevel3Count == 0 ? -1.0 : stats.productLevel3RaisedOnDate / stats.productLevel3Count);
        fdWarehouseLevel.setProductLevel3DelayRaisedPer(stats.productLevel3Count == 0 ? -1.0 : stats.productLevel3DelayRaised / stats.productLevel3Count);

        // FF, ON_TIME_FF, ON_DATE_FF for products Level_4
        fdWarehouseLevel.setProductLevel4FFPer(stats.productLevel4Count == 0 ? -1.0 : stats.productLevel4FF / stats.productLevel4Qty);
        fdWarehouseLevel.setProductLevel4OnDateFFPer(stats.productLevel4Count == 0 ? -1.0 : stats.productLevel4OnDateFF / stats.productLevel4Qty);
        fdWarehouseLevel.setProductLevel4OnTimeFFPer(stats.productLevel4Count == 0 ? -1.0 : stats.productLevel4OnTimeFF / stats.productLevel4Qty);
        fdWarehouseLevel.setProductLevel4RaisedOnTimePer(stats.productLevel4Count == 0 ? -1.0 : stats.productLevel4RaisedOnTime / stats.productLevel4Count);
        fdWarehouseLevel.setProductLevel4RaisedOnDatePer(stats.productLevel4Count == 0 ? -1.0 : stats.productLevel4RaisedOnDate / stats.productLevel4Count);
        fdWarehouseLevel.setProductLevel4DelayRaisedPer(stats.productLevel4Count == 0 ? -1.0 : stats.productLevel4DelayRaised / stats.productLevel4Count);

        // FF, ON_TIME_FF, ON_DATE_FF for products Level_5
        fdWarehouseLevel.setProductLevel5FFPer(stats.productLevel5Count == 0 ? -1.0 : stats.productLevel5FF / stats.productLevel5Qty);
        fdWarehouseLevel.setProductLevel5OnDateFFPer(stats.productLevel5Count == 0 ? -1.0 : stats.productLevel5OnDateFF / stats.productLevel5Qty);
        fdWarehouseLevel.setProductLevel5OnTimeFFPer(stats.productLevel5Count == 0 ? -1.0 : stats.productLevel5OnTimeFF / stats.productLevel5Qty);
        fdWarehouseLevel.setProductLevel5RaisedOnTimePer(stats.productLevel5Count == 0 ? -1.0 : stats.productLevel5RaisedOnTime / stats.productLevel5Count);
        fdWarehouseLevel.setProductLevel5RaisedOnDatePer(stats.productLevel5Count == 0 ? -1.0 : stats.productLevel5RaisedOnDate / stats.productLevel5Count);
        fdWarehouseLevel.setProductLevel5DelayRaisedPer(stats.productLevel5Count == 0 ? -1.0 : stats.productLevel5DelayRaised / stats.productLevel5Count);

        // FF, ON_TIME_FF, ON_DATE_FF for products where LEVEL is not defined
        fdWarehouseLevel.setProductLevelNAFFPer(stats.productLevelNACount == 0 ? -1.0 : stats.productLevelNAFF / stats.productLevelNAQty);
        fdWarehouseLevel.setProductLevelNAOnDateFFPer(stats.productLevelNACount == 0 ? -1.0 : stats.productLevelNAOnDateFF / stats.productLevelNAQty);
        fdWarehouseLevel.setProductLevelNAOnTimeFFPer(stats.productLevelNACount == 0 ? -1.0 : stats.productLevelNAOnTimeFF / stats.productLevelNAQty);
        fdWarehouseLevel.setProductLevelNARaisedOnTimePer(stats.productLevelNACount == 0 ? -1.0 : stats.productLevelNARORaisedOnTime / stats.productLevelNACount);
        fdWarehouseLevel.setProductLevelNARaisedOnDatePer(stats.productLevelNACount == 0 ? -1.0 : stats.productLevelNARORaisedOnDate / stats.productLevelNACount);
        fdWarehouseLevel.setProductLevelNADelayRaisedPer(stats.productLevelNACount == 0 ? -1.0 : stats.productLevelNADelayedRo / stats.productLevelNACount);
    }


    private ArrayList<FullfillmentDataUnitLevel> fullfillmentProcessUnitlevel(HashMap<Long, Set<Long>> transferUnitToRequestUnit,
            HashMap<Long, ArrayList<FullfillmentData>> requestUnitToScmProduct) {

        ArrayList<FullfillmentDataUnitLevel> results = new ArrayList<>();

        for (Map.Entry<Long, Set<Long>> entry : transferUnitToRequestUnit.entrySet()) {
            Long transferUnit = entry.getKey();
            Set<Long> requestingUnits = entry.getValue();

            for (Long requestingUnit : requestingUnits) {
                List<FullfillmentData> fdData = filterByTransferUnit(requestUnitToScmProduct.get(requestingUnit), transferUnit);
                if (!fdData.isEmpty()) {
                    results.add(buildUnitLevel(transferUnit, requestingUnit, fdData));
                }
            }
        }
        return results;
    }

    private List<FullfillmentData> filterByTransferUnit(List<FullfillmentData> fdProductsData, Long transferUnit) {
        if (fdProductsData == null) return Collections.emptyList();
        return fdProductsData.stream()
                .filter(d -> Objects.equals(d.getTransferringUnitId(), transferUnit))
                .collect(Collectors.toList());
    }

    private FullfillmentDataUnitLevel buildUnitLevel(Long transferUnit, Long requestingUnit, List<FullfillmentData> fdData) {
        FullfillmentDataUnitLevel fdUnitLevel = new FullfillmentDataUnitLevel();
        fdUnitLevel.setTransferringUnitId(transferUnit);
        fdUnitLevel.setRequestingUnitId(requestingUnit);

        FullfillmentData fd = fdData.get(0);
        fdUnitLevel.setTransferringUnit(fd.getTransferringUnit());
        fdUnitLevel.setRequestingUnit(fd.getRequestingUnit());

        calculateAndFillAggregates(fdUnitLevel, fdData);
        return fdUnitLevel;
    }

    private void calculateAndFillAggregates(FullfillmentDataUnitLevel fdUnitLevel, List<FullfillmentData> fdData) {
            Double qtySum = 0.0, fpSum = 0.0, onTimeFpSum = 0.0, onDateFpSum = 0.0, imFpSum = 0.0;
        Double totalOnTimeRoPer = 0.0, totalOnDateRoPer = 0.0, totalDelayedRoPer = 0.0;
        Long sumOfAllRos = 0L;
        Double bakerySum = 0.0, bakeryOnTimeSum = 0.0, bakeryOnDateSum = 0.0;
        Double bakeryOnTimeRoRaised = 0.0, bakeryOnDateRoRaised = 0.0, bakeryDelayedRoRaised = 0.0;
        int bakeryCount = 0;

        for (FullfillmentData f : fdData) {
            if (f.getIsBakeryProduct()) {
                bakerySum += f.getFullfillmentPercentage();
                bakeryOnTimeSum += f.getOnTimeFullFillmentPercentage();
                bakeryOnDateSum += f.getOnDateFullFillmentPercentage();

                bakeryOnTimeRoRaised += f.getOnTimeRoRaisedPercentage();
                bakeryOnDateRoRaised += f.getOnDateRoRaisedPercentage();
                bakeryDelayedRoRaised += f.getDelayRoRaisedPercentage();
                bakeryCount++;
            }
            Double qty = f.getRequestedAbsoluteQuantity();
            Long totalRos = f.getTotalRos();
            sumOfAllRos += totalRos;
            qtySum += qty;

            fpSum += qty * f.getFullfillmentPercentage();
            onTimeFpSum += qty * f.getOnTimeFullFillmentPercentage();
            onDateFpSum += qty * f.getOnDateFullFillmentPercentage();
            imFpSum += qty * f.getImpactedFullfillmentPercentage();
            totalOnTimeRoPer += totalRos * f.getOnTimeRoRaisedPercentage();
            totalOnDateRoPer += totalRos * f.getOnDateRoRaisedPercentage();
            totalDelayedRoPer += totalRos * f.getDelayRoRaisedPercentage();
        }

        fdUnitLevel.setFPerWeightedAvg(fpSum / qtySum);
        fdUnitLevel.setOnDateFPerWeightedAvg(onDateFpSum / qtySum);
        fdUnitLevel.setOnTimeFPerWeightedAvg(onTimeFpSum / qtySum);

        fdUnitLevel.setTotalRoRaisedOnTimePer(totalOnTimeRoPer / sumOfAllRos);
        fdUnitLevel.setTotalRoRaisedOnDatePer(totalOnDateRoPer / sumOfAllRos);
        fdUnitLevel.setTotalDelayRoRaisedPer(totalDelayedRoPer / sumOfAllRos);

        double impactedWeightedPercentage = imFpSum / qtySum;
        fdUnitLevel.setImFPerWeightedAvg(
                impactedWeightedPercentage < 0 ? (fpSum / qtySum) : impactedWeightedPercentage
        );

        fdUnitLevel.setIsBakeryProduct(false);
        if (bakeryCount > 0) {
            fdUnitLevel.setBakeryProductFPAvg(bakerySum / bakeryCount);
            fdUnitLevel.setBakeryProductOnTimeFFAvg(bakeryOnTimeSum / bakeryCount);
            fdUnitLevel.setBakeryProductOnDateFFAvg(bakeryOnDateSum / bakeryCount);
            fdUnitLevel.setIsBakeryProduct(true);

            fdUnitLevel.setBakeryRoRaisedOnTimePer(bakeryOnTimeRoRaised / bakeryCount);
            fdUnitLevel.setBakeryRoRaisedOnDatePer(bakeryOnDateRoRaised / bakeryCount);
            fdUnitLevel.setBakeryDelayRoRaisedPer(bakeryDelayedRoRaised / bakeryCount);
        }
    }


    private void sendEmail(String fileName, ArrayList<FullfillmentDataWarehouseLevel> lastDay, ArrayList<FullfillmentDataWarehouseLevel> lastThirtyDay, List<CriticalProductFF> criticalProductSummary) throws EmailGenerationException {

        byte[] barray = null;
        try {
            File file = new File(env.getBasePath() + "/" + fileName);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            bos.write(FileUtils.readFileToByteArray(file));
            barray = bos.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
        }
        List<AttachmentData> attachments = new ArrayList<>();
        AttachmentData fullfillmentReport = null;
        String[] fileNameSplit = fileName.split("\\.");
        fullfillmentReport = new AttachmentData(barray, fileNameSplit[0], AppConstants.EXCEL_MIME_TYPE);
        attachments.add(fullfillmentReport);

        FullfillmentReportEmailNotificationTemplate emailTemplate = new FullfillmentReportEmailNotificationTemplate(lastDay, lastThirtyDay, env.getBasePath(), criticalProductSummary);
        FullfillmentReportEmailNotification emailNotification = new FullfillmentReportEmailNotification(emailTemplate, env.getEnvType());
        emailNotification.sendRawMail(attachments);

        File fileToUpload = new File(env.getBasePath() + "/" + fileName);
        boolean res = fileToUpload.delete();
        LOG.info("File deleted : {}", res);
    }

    private List<CriticalProductFF> getFFSummaryForCriticalProducts(List<FullfillmentData> lastDayData, List<FullfillmentData> lastSevenData, List<FullfillmentData> mtdData) {

        Map<Long, CriticalProductFF> criticalProducts = new HashMap<>();

        List<FullfillmentData> criticalProductData = lastDayData.stream().filter(e -> e.getProductLevel() != null && !e.getProductLevel().isEmpty()).collect(Collectors.toList());
        storeCriticalProductSummary(criticalProductData, 1, criticalProducts);

        criticalProductData = lastSevenData.stream().filter(e -> e.getProductLevel() != null && !e.getProductLevel().isEmpty()).collect(Collectors.toList());
        storeCriticalProductSummary(criticalProductData, 7, criticalProducts);

        criticalProductData = mtdData.stream().filter(e -> e.getProductLevel() != null && !e.getProductLevel().isEmpty()).collect(Collectors.toList());
        storeCriticalProductSummary(criticalProductData, 30, criticalProducts);

        List<CriticalProductFF> result = criticalProducts.values().stream().toList();
        return result;
    }

    private void storeCriticalProductSummary(List<FullfillmentData> data, int days, Map<Long, CriticalProductFF> criticalProducts) {
        Map<Long, Double> productToQty = new HashMap<>();
        data.forEach(e -> {
            CriticalProductFF criticalProductFF = criticalProducts.getOrDefault(e.getProductId(), new CriticalProductFF());
            if (criticalProductFF.getProductId() == null) {
                criticalProductFF.setProductId(e.getProductId());
                criticalProductFF.setProductName(e.getProductName());
                criticalProductFF.setProductLevel(e.getProductLevel());
            }
            Double val = e.getFullfillmentPercentage() * e.getRequestedAbsoluteQuantity();
            Double onTimeVal = e.getOnTimeFullFillmentPercentage() * e.getRequestedAbsoluteQuantity();
            Double onDateVal = e.getOnDateFullFillmentPercentage() * e.getRequestedAbsoluteQuantity();

            Double qty = productToQty.getOrDefault(e.getProductId(), 0.0);
            qty = qty + e.getRequestedAbsoluteQuantity();
            productToQty.put(e.getProductId(), qty);

            if (days == 1) {
                criticalProductFF.setLastDayFF(criticalProductFF.getLastDayFF() + val);
                criticalProductFF.setLastDayOnDateFF(criticalProductFF.getLastDayOnDateFF() + onDateVal);
                criticalProductFF.setLastDayOnTimeFF(criticalProductFF.getLastDayOnTimeFF() + onTimeVal);
            } else if (days == 7) {
                criticalProductFF.setLastSevenDaysFF(criticalProductFF.getLastSevenDaysFF() + val);
                criticalProductFF.setLastSevenDaysOnDateFF(criticalProductFF.getLastSevenDaysOnDateFF() + onDateVal);
                criticalProductFF.setLastSevenDaysOnTimeFF(criticalProductFF.getLastSevenDaysOnTimeFF() + onTimeVal);
            } else if (days == 30) {
                criticalProductFF.setMtdFF(criticalProductFF.getMtdFF() + val);
                criticalProductFF.setMtdOnDateFF(criticalProductFF.getMtdOnDateFF() + onDateVal);
                criticalProductFF.setMtdOnTimeFF(criticalProductFF.getMtdOnTimeFF() + onTimeVal);
            }
            criticalProducts.put(e.getProductId(), criticalProductFF);
        });

        criticalProducts.values().forEach(e -> {
            Double qty = productToQty.get(e.getProductId());
            if (qty != null) {
                if (days == 1) {
                    e.setLastDayFF((double) Math.round(e.getLastDayFF() / qty));
                    e.setLastDayOnDateFF((double) Math.round(e.getLastDayOnDateFF() / qty));
                    e.setLastDayOnTimeFF((double) Math.round(e.getLastDayOnTimeFF() / qty));
                } else if (days == 7) {
                    e.setLastSevenDaysFF((double) Math.round(e.getLastSevenDaysFF() / qty));
                    e.setLastSevenDaysOnDateFF((double) Math.round(e.getLastSevenDaysOnDateFF() / qty));
                    e.setLastSevenDaysOnTimeFF((double) Math.round(e.getLastSevenDaysOnTimeFF() / qty));
                } else if (days == 30) {
                    e.setMtdFF((double) Math.round(e.getMtdFF() / qty));
                    e.setMtdOnDateFF((double) Math.round(e.getMtdOnDateFF() / qty));
                    e.setMtdOnTimeFF((double) Math.round(e.getMtdOnTimeFF() / qty));
                }
            }
        });

    }

    private static enum ProductLevel {
        LEVEL_1,
        LEVEL_2,
        LEVEL_3,
        LEVEL_4,
        LEVEL_5,
        NA
    }


}

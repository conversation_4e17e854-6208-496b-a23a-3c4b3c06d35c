package com.stpl.tech.scm.data.model;

import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

@Data
@Entity
@EntityListeners(AuditingEntityListener.class)
@Table(name = "REGION_PRODUCT_PACKAGING_MAPPING_DATA")
public class RegionProductPackagingMappingData {

    @Id
    @Column(name = "MAPPING_ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer mappingId;

    @Column(name = "REGION", nullable = false)
    private String regionCode;

    @Column(name = "PRODUCT_ID", nullable = false)
    private Integer productId;

    @Column(name = "PACKAGING_ID", nullable = false)
    private Integer packagingId;

    @Column(name = "MAPPING_STATUS")
    private String status;

    @Column(name = "CREATED_BY")
    private Integer createdBy;

    @CreatedDate
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_DATE", updatable = false, nullable = false)
    private Date creationDate;

}

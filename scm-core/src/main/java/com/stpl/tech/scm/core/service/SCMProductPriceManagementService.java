package com.stpl.tech.scm.core.service;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.ProductRecipeKey;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.recipe.model.RecipeCost;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.data.model.CostDetailData;
import com.stpl.tech.scm.domain.model.CostDetail;
import com.stpl.tech.scm.domain.model.PriceUpdateEvent;
import com.stpl.tech.scm.domain.model.PriceUpdateEventStatus;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductPriceData;
import com.stpl.tech.scm.domain.model.ProductionBooking;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.TrimmedCostDetail;
import org.springframework.web.servlet.View;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface SCMProductPriceManagementService {

	public List<PriceUpdateEvent> getSkuPriceUpdateEvents();

	public View downloadSkuPriceUpdateFile();

	public boolean updateSkuPriceEvent(int eventId, int userId, String userName, PriceUpdateEventStatus approved)
            throws IOException, SumoException;

	public Integer addSkuPriceUpdateFile(PriceUpdateEvent event) throws DataUpdationException, SumoException;

	public int updateSkuPriceEvent(PriceUpdateEvent event, Integer userId, String userName) throws SumoException;

	public RecipeCost calculateRecipeCost(RecipeDetail recipe);

    List<String> getUniqueRegions(String region, Pair<Integer, Integer> fulfilmentKitchenWH);

    List<Integer> getMissingPricesProductKeys(Map<Integer, Pair<List<SkuDefinition>, BigDecimal>> productSkuMapWithPrice);

	void setPricesInProductsSkuMap(Map<Integer, Pair<List<SkuDefinition>, BigDecimal>> productSkuMapWithPrice, List<CostDetailData> costDetailDataWhs);

	Map<String, List<Integer>> getFulfilmentTypeWithSkus(Map<String, List<ProductDefinition>> productsFulfilmentByType, Map<Integer, Pair<List<SkuDefinition>, BigDecimal>> productSkuMapWithPrice);

	Map<Integer, Pair<List<SkuDefinition>, BigDecimal>> getProductSkuMapWithPrice(List<Integer> recipeProductIds);

	Map<String, List<ProductDefinition>> getProductsFulfilmentByType(List<Integer> recipeProductIds);

	Pair<Integer, Integer> getFulfilmentKitchenWHOfRegion(String region);

    /**
	 * @param recipe
	 * @param region
	 * @return
	 */
	RecipeCost calculateRecipeCost(ProductRecipeKey recipe, String region);

	public PriceUpdateEvent getSkuPriceUpdateEvent(int eventId);

	/**
	 * @param updatedEventId
	 * @return
	 */
	boolean calculateRecipeCost(Integer updatedEventId);

	/**
	 * @param updatedEventId
	 * @return
	 */
	boolean calculateProductCost(Integer updatedEventId);

	public void failEvent(Integer updatedEventId);

	public void saveRecipeCost(ProductionBooking booking) throws SumoException, DataNotFoundException;

	Map<Integer, Map<Integer, ProductPriceData>> getUnitPriceMap();

	boolean recalculateRecipePrices(int unitId, Map<Integer, ProductPriceData> priceMap) throws IOException, SumoException;

	public List<CostDetail> fetchUnitInventory(int unitId);

    List<TrimmedCostDetail> fetchNewUnitInventory(int unitId, List<Integer> ids);

	RecipeCost calculateRecipeCostNew(RecipeDetail detail, String region);
}

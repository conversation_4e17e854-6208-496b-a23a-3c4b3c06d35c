package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "VARIANCE_EXPIRY_DRILL_DOWN_DATA")
public class VarianceExpiryDrillDownData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "VARIANCE_EXPIRY_DRILL_DOWN_DATA_ID", nullable = false, unique = true)
    private Integer varianceExpiryDrillDownDataId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "DAY_CLOSE_EVENT_ID")
    private SCMDayCloseEventData dayCloseEventData;

    @Column(name = "KEY_ID", nullable = false)
    private Integer keyId;

    @Column(name = "KEY_TYPE", nullable = false)
    private String keyType;

    @Column(name = "EXPIRY_DATE")
    private Date expiryDate;

    @Column(name = "CREATED_BY")
    private Integer createdBy;

    @Column(name = "CREATED_AT")
    private Date createdAt;
}

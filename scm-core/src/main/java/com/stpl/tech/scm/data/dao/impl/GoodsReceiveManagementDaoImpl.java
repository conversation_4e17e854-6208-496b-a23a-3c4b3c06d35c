package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.data.dao.GoodsReceiveManagementDao;
import com.stpl.tech.scm.data.model.AssetDefinitionData;
import com.stpl.tech.scm.data.model.GoodsReceivedData;
import com.stpl.tech.scm.data.model.InvoiceExcessQuantity;
import com.stpl.tech.scm.data.model.PurchaseOrderData;
import com.stpl.tech.scm.data.model.PurchaseOrderVendorGRMappingData;
import com.stpl.tech.scm.data.model.RequestOrderData;
import com.stpl.tech.scm.data.model.SpecializedOrderInvoiceData;
import com.stpl.tech.scm.data.model.VendorGoodsReceivedData;
import com.stpl.tech.scm.domain.model.AssetStatusType;
import com.stpl.tech.scm.domain.model.GrItemQuantityUpdation;
import com.stpl.tech.scm.domain.model.InvoiceDocType;
import com.stpl.tech.scm.domain.model.PurchaseOrderStatus;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.VendorGrType;
import com.stpl.tech.scm.domain.model.VendorPoGRItems;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * Created by Rahul Singh on 22-06-2016.
 */
@Repository
public class GoodsReceiveManagementDaoImpl extends SCMAbstractDaoImpl implements GoodsReceiveManagementDao {

    private static final Logger LOG = LoggerFactory.getLogger(GoodsReceiveManagementDaoImpl.class);

    @Override
    public List<GoodsReceivedData> getPendingGrs(Integer unitId, Date updatedAt, Boolean fetchRejected) {
        StringBuilder queryString = new StringBuilder("FROM GoodsReceivedData g WHERE g.generatedForUnitId = :unitId AND g.status IN(:statusList) ");
        if (Objects.isNull(fetchRejected) || fetchRejected.equals(Boolean.FALSE)) {
            queryString.append(" and g.isRejectedGR != :rejectedGR ");
        }
        if (Objects.nonNull(updatedAt)) {
            queryString.append(" AND g.lastUpdateTime <= :updatedAt");
        }
       /* String queryString = "FROM GoodsReceivedData g WHERE g.generatedForUnitId = :unitId "
                            + "AND g.status IN(:statusList) " + ( (fetchRejected == null || !fetchRejected) ? " and g.isRejectedGR != :rejectedGR " :  "")
                            + (updatedAt !=null ? " AND g.lastUpdateTime <= :updatedAt" : "");*/
        Query query = manager.createQuery(queryString.toString());
        if (unitId != null) {
            query.setParameter("unitId", unitId);
        }
        if (updatedAt != null) {
            query.setParameter("updatedAt", updatedAt);
        }
        List<String> statusList = Arrays.asList(SCMOrderStatus.CREATED.value(), SCMOrderStatus.INITIATED.value());
        query.setParameter("statusList", statusList);
        if (Objects.isNull(fetchRejected) || fetchRejected.equals(Boolean.FALSE)) {
            query.setParameter("rejectedGR", SCMServiceConstants.SCM_CONSTANT_YES);
        }
        return query.getResultList();
    }

    @Override
    public List<GoodsReceivedData> getRaisedDisputedGrs(Integer unitId) {
        Query query = manager.createQuery("FROM GoodsReceivedData g WHERE g.generationUnitId = :unitId AND g.status IN(:statusList) and g.isRejectedGR = :rejectedGR ");
        if (unitId != null) {
            query.setParameter("unitId", unitId);
        }
        List<String> statusList = Arrays.asList(SCMOrderStatus.CREATED.value(), SCMOrderStatus.INITIATED.value());
        query.setParameter("statusList", statusList);
        query.setParameter("rejectedGR", SCMServiceConstants.SCM_CONSTANT_YES);
        return query.getResultList();
    }

    @Override
    public List<GoodsReceivedData> getRejectedPendingGrs(Integer unitId, Date updatedAt) {
        String queryString = "FROM GoodsReceivedData g WHERE g.generatedForUnitId = :unitId AND g.status IN(:statusList)"
                + " and g.isRejectedGR=:rejectedGR " + (updatedAt != null ? " AND g.lastUpdateTime <= :updatedAt" : "");
        Query query = manager.createQuery(queryString);
        if (unitId != null) {
            query.setParameter("unitId", unitId);
        }
        if (updatedAt != null) {
            query.setParameter("updatedAt", updatedAt);
        }
        query.setParameter("rejectedGR", SCMServiceConstants.SCM_CONSTANT_YES);
        List<String> statusList = Arrays.asList(SCMOrderStatus.CREATED.value(), SCMOrderStatus.INITIATED.value());
        query.setParameter("statusList", statusList);
        return query.getResultList();
    }

    @Override
    public List<GoodsReceivedData> findGoodsReceived(Integer generationUnitId, Integer generatedForUnitId, Date startDate, Date endDate, SCMOrderStatus status, Integer goodsReceiveOrderId) {
        String queryString = "FROM GoodsReceivedData g WHERE ";
        if (goodsReceiveOrderId != null) {
            queryString += "g.id = :goodsReceiveOrderId ";
        } else {
            queryString += "g.lastUpdateTime >= :startDate and g.lastUpdateTime < :endDate ";
        }
        if (generationUnitId != null) {
            queryString += "and g.generationUnitId = :generationUnitId ";
        }
        if (generatedForUnitId != null) {
            queryString += "and g.generatedForUnitId = :generatedForUnitId ";
        }
        if (status != null) {
            queryString += "and g.status = :status ";
        }
        queryString += "order by g.generationTime";
        Query query = manager.createQuery(queryString);
        if (goodsReceiveOrderId != null) {
            query.setParameter("goodsReceiveOrderId", goodsReceiveOrderId);
        } else {
            query.setParameter("startDate", startDate);
            query.setParameter("endDate", endDate);
        }
        if (generationUnitId != null) {
            query.setParameter("generationUnitId", generationUnitId);
        }
        if (generatedForUnitId != null) {
            query.setParameter("generatedForUnitId", generatedForUnitId);
        }
        if (status != null) {
            query.setParameter("status", status.value());
        }
        return query.getResultList();
    }

    @Override
    public List<SpecializedOrderInvoiceData> findMilkInvoicesForPayment(Integer unitId, Integer vendorId, Date startDate, Date endDate) {
        StringBuilder queryString = new StringBuilder("SELECT inv FROM SpecializedOrderInvoiceData inv where inv.isPrRaised = :no and inv.vendorId = :vendorId and ");
        queryString.append(" inv.unitId = :unitId and inv.generationTime >= :startDate and inv.generationTime <= :endDate ");
        return manager.createQuery(queryString.toString()).setParameter("no", AppConstants.NO).setParameter("vendorId", vendorId).setParameter("unitId", unitId).
                setParameter("startDate", startDate).setParameter("endDate", endDate).getResultList();

    }

    @Override
    public List<GoodsReceivedData> findGrsByInvoiceId(Integer unitId, Integer invoiceId) {
        List<GoodsReceivedData> goodsReceivedDataList = new ArrayList<>();
        try {
            StringBuilder queryString = new StringBuilder("SELECT gr FROM GoodsReceivedData gr where gr.generatedForUnitId = :unitId and gr.invoiceId = :invoiceId  ");
            Query query = manager.createQuery(queryString.toString());
            query.setParameter("unitId", unitId).setParameter("invoiceId", invoiceId);
            goodsReceivedDataList = query.getResultList();
        } catch (Exception e) {
            LOG.error("Error While Getting Grs For Invoice Id : {} :::::", invoiceId, e);
        }


        return goodsReceivedDataList;
    }

    @Override
    public List<InvoiceExcessQuantity> findPrExcessQuantity(Integer unitId, Integer invoiceId) {
        List<InvoiceExcessQuantity> invoiceExcessQuantityList = new ArrayList<>();
        try {
            StringBuilder queryString = new StringBuilder("SELECT inv FROM InvoiceExcessQuantity inv " +
                    "where inv.unitId = :unitId and inv.invoiceId = :invoiceId  ");
            Query query = manager.createQuery(queryString.toString());
            query.setParameter("unitId", unitId).setParameter("invoiceId", invoiceId);
            invoiceExcessQuantityList = query.getResultList();
        } catch (Exception e) {
            LOG.error("Error While Getting Excess Pr Quantites For Invoice Id : {} :::::", invoiceId, e);
        }


        return invoiceExcessQuantityList;
    }

    @Override
    public List<PurchaseOrderVendorGRMappingData> findGrsWithPoId(Integer poId) {
        Query query = manager.createQuery("FROM PurchaseOrderVendorGRMappingData s WHERE s.purchaseOrderData.id =:poId");
        query.setParameter("poId",poId);
        return query.getResultList();
    }

    @Override
    public List<PurchaseOrderVendorGRMappingData> findPoWithGrId(Integer grId) {
        Query query = manager.createQuery("FROM PurchaseOrderVendorGRMappingData s WHERE s.vendorGoodsReceivedData.goodsReceivedId =:grId");
        query.setParameter("grId",grId);
        return query.getResultList();
    }

    @Override
    public Boolean checkIfSpecialOrder(Integer grId, List<Integer> vendorIds) {
        Query query = manager.createQuery("SELECT gr.id FROM GoodsReceivedData gr " +
                "LEFT JOIN gr.requestOrderData ro " +
                "WHERE gr.id = :grId " +
                "AND gr.generationUnitId = gr.generatedForUnitId " +
                "AND gr.status IN (:grStatus)" +
                "AND ro.status IN (:roStatus)" +
                "AND ro.vendorId IN (:vendorIds)");
        query.setParameter("grId", grId)
                .setParameter("grStatus", List.of(SCMOrderStatus.SETTLED.value()))
                .setParameter("roStatus", List.of(SCMOrderStatus.SETTLED.value()))
                .setParameter("vendorIds", vendorIds);
        return !query.getResultList().isEmpty();
    }


    @Override
    public List<VendorGoodsReceivedData> findRegularVendorsGrs(Integer unitId, Integer vendorId) {
        StringBuilder queryString = new StringBuilder("SELECT DISTINCT g FROM VendorGoodsReceivedData g, VendorGoodsReceivedItemData r");
        queryString.append(" WHERE g.deliveryUnitId =:unitId");
        queryString.append(" AND g.grStatus =:status");
        queryString.append(" AND g.vendorGRType =:vendorGRType");
        if (vendorId != null) {
            queryString.append(" AND g.generatedForVendor = :vendorId ");
            Query query = manager.createQuery(queryString.toString());
            query.setParameter("vendorId", vendorId);
            query.setParameter("unitId", unitId);
            query.setParameter("status", SCMOrderStatus.INITIATED.name());
            query.setParameter("vendorGRType", VendorGrType.REGULAR_ORDER.name());
            return query.getResultList();
        }
        return manager.createQuery(queryString.toString())
                .setParameter("unitId", unitId)
                .setParameter("vendorGRType", VendorGrType.REGULAR_ORDER.name())
                .setParameter("status", SCMOrderStatus.INITIATED.name()).getResultList();
    }

    @Override
    public List<GrItemQuantityUpdation> grItemQuantityDeviations() {
        List<GrItemQuantityUpdation> grItemQuantityUpdationList = new ArrayList<>();
        Query query = manager.createNativeQuery("select * from GR_ITEM_QUANTITY_DEVIATION");
        List<Object[]> results = query.getResultList();
        results.stream().forEach((record) -> {
            GrItemQuantityUpdation grItemQuantityUpdation = new GrItemQuantityUpdation();
            grItemQuantityUpdation.setGrItemDeviationId((Integer) record[0]);
            grItemQuantityUpdation.setDeviationReason((String) record[1]);
            grItemQuantityUpdationList.add(grItemQuantityUpdation);
        });
        return grItemQuantityUpdationList;
    }

    @Override

    public List<VendorPoGRItems> mapping(Integer grId) {
        List<VendorPoGRItems> list = new ArrayList<>();
        Query query = manager.createQuery(
                "select pr.id, pri.receivedQuantity,pri.requestedQuantity, pri.packagingQuantity, gri.packagingQuantity, gri.receivedQty, " +
                        "gri.skuName, gri.skuId, grip.id, grip.packagingQty ,grip.acceptedQty, grip.grItem.itemId, grip.poItem.id, pri.conversionRatio from VendorGoodsReceivedData gr, VendorGoodsReceivedItemData gri, " +
                        "VendorGrPoItemMappingData grip, PurchaseOrderItemData pri, PurchaseOrderData pr where gr.goodsReceivedId = gri.goodsReceivedData.id and gri.itemId = grip.grItem.itemId " +
                        "and pri.id =grip.poItem.id " +
                        "and pri.purchaseOrderData.id = pr.id " +
                        "and gr.goodsReceivedId = :grId");
        query.setParameter("grId", grId);
        List<Object[]> results = query.getResultList();
        results.stream().forEach((record) -> {
            VendorPoGRItems o = new VendorPoGRItems();
            o.setPurchaseOrderId((Integer) record[0]);
            o.setPoItemReceivedQuantity((BigDecimal) record[1]);
            o.setPoItemRequestedQuantity((BigDecimal) record[2]);
            o.setPoItemPackagingQuantity((BigDecimal) record[3]);
            o.setGrItemPackagingQuantity((BigDecimal) record[4]);
            o.setGrItemReceivedQuantity((BigDecimal) record[5]);
            o.setSkuName((String) record[6]);
            o.setSkuId((Integer) record[7]);
            o.setMappingId((Integer) record[8]);
            if (record[10] != null) {
                o.setGrItemAcceptedQuantity((BigDecimal) record[10]);
            } else {
                o.setGrItemAcceptedQuantity((BigDecimal) record[9]);
            }
            o.setGrItemId((Integer) record[11]);
            o.setPoItemId((Integer) record[12]);
            o.setConversionRatio((BigDecimal) record[13]);
            list.add(o);
        });
        return list;
    }

    @Override
    public List<VendorPoGRItems> rejectedGr(Integer grId) {
        List<VendorPoGRItems> list = new ArrayList<>();
        Query query = manager.createQuery(
                "select pr.id, pri.receivedQuantity,pri.requestedQuantity, pri.packagingQuantity, gri.packagingQuantity, gri.receivedQty, " +
                        "gri.skuName, gri.skuId, grip.id, grip.packagingQty ,grip.acceptedQty, grip.grItem.itemId, grip.poItem.id, pri.conversionRatio from VendorGoodsReceivedData gr, VendorGoodsReceivedItemData gri, " +
                        "VendorGrPoItemMappingData grip, PurchaseOrderItemData pri, PurchaseOrderData pr where gr.goodsReceivedId = gri.goodsReceivedData.id and gri.itemId = grip.grItem.itemId " +
                        "and pri.id = grip.poItem.id " +
                        "and pri.purchaseOrderData.id = pr.id " +
                        "and gr.goodsReceivedId = :grId " +
                        "and grip.acceptedQty < grip.packagingQty");
        query.setParameter("grId", grId);
        List<Object[]> results = query.getResultList();
        results.stream().forEach((record) -> {
            VendorPoGRItems o = new VendorPoGRItems();
            o.setPurchaseOrderId((Integer) record[0]);
            o.setPoItemReceivedQuantity((BigDecimal) record[1]);
            o.setPoItemRequestedQuantity((BigDecimal) record[2]);
            o.setPoItemPackagingQuantity((BigDecimal) record[3]);
            o.setGrItemPackagingQuantity((BigDecimal) record[4]);
            o.setGrItemReceivedQuantity((BigDecimal) record[5]);
            o.setSkuName((String) record[6]);
            o.setSkuId((Integer) record[7]);
            o.setMappingId((Integer) record[8]);
            o.setActualPackagingQty((BigDecimal) record[9]);
            if (record[10] != null) {
                o.setGrItemAcceptedQuantity((BigDecimal) record[10]);
            }
            o.setGrItemId((Integer) record[11]);
            o.setPoItemId((Integer) record[12]);
            o.setConversionRatio((BigDecimal) record[13]);
            list.add(o);
        });
        return list;
    }

    @Override
    public List<VendorGoodsReceivedData> findVendorGRs(Integer vendorId, Integer dispatchId, int deliveryUnitId,
                                                       List<Integer> skus, Date startDate, Date endDate, Integer goodReceivedId) {
        StringBuilder queryString = new StringBuilder("SELECT DISTINCT g FROM VendorGoodsReceivedData g, VendorGoodsReceivedItemData r");
        queryString.append(" WHERE g.goodsReceivedId = r.goodsReceivedData.goodsReceivedId");
        queryString.append(" AND g.deliveryUnitId =:unitId and g.invalidGR=:invalidGR");
        if (goodReceivedId != null) {
            queryString.append(" AND g.goodsReceivedId = :goodReceivedId ");
            Query query = manager.createQuery(queryString.toString())
                    .setParameter("goodReceivedId", goodReceivedId)
                    .setParameter("unitId", deliveryUnitId)
                    .setParameter("invalidGR", SCMServiceConstants.SCM_CONSTANT_NO);
            return query.getResultList();
        } else {
            if (dispatchId != null) {
                queryString.append(" AND g.dispatchId=:dispatchId ");
            }
            if (vendorId != null) {
                queryString.append(" AND g.generatedForVendor = :vendorId ");
            }
//        if(goodReceivedId!=null){
//            queryString.append(" AND g.goodsReceivedId = :goodReceivedId ");
//        }
            if (skus != null && !skus.isEmpty()) {
                queryString.append(" AND r.skuId IN (:skus)");
            }

            queryString.append(" AND g.createdAt>=:startDate AND g.createdAt<=:endDate ORDER BY g.createdAt DESC");
            Query query = manager.createQuery(queryString.toString())
                    .setParameter("unitId", deliveryUnitId)
                    .setParameter("invalidGR", SCMServiceConstants.SCM_CONSTANT_NO)
                    .setParameter("startDate", startDate)
                    .setParameter("endDate", endDate);
            if (dispatchId != null) {
                query.setParameter("dispatchId", dispatchId);
            }
            if (vendorId != null) {
                query.setParameter("vendorId", vendorId);
            }
//            if (goodReceivedId != null) {
//                query.setParameter("goodReceivedId", goodReceivedId);
//            }
            if (skus != null && !skus.isEmpty()) {
                query.setParameter("skus", skus);
            }
            return query.getResultList();
        }
    }

    @Override
    public List<VendorGoodsReceivedData> findVendorGRsForPo(Integer vendorId, Integer dispatchId, Integer deliveryUnitId,
                                                            List<Integer> skus, Date startDate, Date endDate, Integer paymentRequestId, Integer goodReceivedId) {
        StringBuilder queryString = new StringBuilder("SELECT DISTINCT g FROM VendorGoodsReceivedData g, VendorGoodsReceivedItemData r");
        if (paymentRequestId != null) {
            queryString.append(" WHERE g.paymentRequestData.id = :paymentRequestId");
            Query query = manager.createQuery(queryString.toString()).setParameter("paymentRequestId", paymentRequestId);
            return query.getResultList();
        } else if (goodReceivedId != null) {
            queryString.append(" WHERE g.goodsReceivedId = :goodReceivedId");
            Query query = manager.createQuery(queryString.toString()).setParameter("goodReceivedId", goodReceivedId);
            return query.getResultList();
        } else {
            queryString.append(" WHERE g.goodsReceivedId = r.goodsReceivedData.goodsReceivedId");
            queryString.append(" AND g.deliveryUnitId =:unitId and g.invalidGR=:invalidGR");
            if (dispatchId != null) {
                queryString.append(" AND g.dispatchId=:dispatchId ");
            }
            if (vendorId != null) {
                queryString.append(" AND g.generatedForVendor = :vendorId ");
            }
//        if(paymentRequestId!=null){
//            queryString.append(" WHERE g.paymentRequestData.id = :paymentRequestId");
//        }
//            if(goodReceivedId!=null){
//                queryString.append(" AND g.goodsReceivedId = :goodReceivedId ");
//            }
            if (skus != null && !skus.isEmpty()) {
                queryString.append(" AND r.skuId IN (:skus)");
            }

            queryString.append(" AND g.createdAt>=:startDate AND g.createdAt<=:endDate ORDER BY g.createdAt DESC");
            Query query = manager.createQuery(queryString.toString())
                    .setParameter("unitId", deliveryUnitId)
                    .setParameter("invalidGR", SCMServiceConstants.SCM_CONSTANT_NO)
                    .setParameter("startDate", startDate)
                    .setParameter("endDate", endDate);
            if (dispatchId != null) {
                query.setParameter("dispatchId", dispatchId);
            }
            if (vendorId != null) {
                query.setParameter("vendorId", vendorId);
            }
//        if(paymentRequestId!=null){
//            query.setParameter("paymentRequestId",paymentRequestId);
//        }
//            if(goodReceivedId!=null){
//                query.setParameter("goodReceivedId",goodReceivedId);
//            }
            if (skus != null && !skus.isEmpty()) {
                query.setParameter("skus", skus);
            }
            return query.getResultList();
        }
    }

    @Override
    public List<VendorGoodsReceivedData> findVendorGRsForPayment(Integer vendorId, Integer dispatchId, int deliveryUnitId, Date startDate, Date endDate) {
        Query query = manager.createQuery("FROM VendorGoodsReceivedData g WHERE g.grStatus = :grStatus and g.toBePaid = :toBePaid " +
                        "and g.deliveryUnitId =:unitId and invalidGR=:invalidGR and g.generatedForVendor = :vendorId and g.createdAt>=:startDate " +
                        "and g.createdAt<=:endDate and g.paymentRequestData IS NULL order by g.createdAt desc ")
                .setParameter("unitId", deliveryUnitId)
                .setParameter("invalidGR", SCMServiceConstants.SCM_CONSTANT_NO)
                .setParameter("startDate", startDate)
                .setParameter("endDate", endDate)
                .setParameter("endDate", endDate)
                .setParameter("vendorId", vendorId)
                .setParameter("toBePaid", SCMServiceConstants.SCM_CONSTANT_YES)
                .setParameter("grStatus", PurchaseOrderStatus.CREATED.value());
        return query.getResultList();
    }

    @Override
    public List<Integer> getExistingGrInFinancialYear(int unitId, Integer vendorId, String docNumber) {
        Query query = manager.createQuery(
                        "select goodsReceivedId FROM VendorGoodsReceivedData g WHERE g.grStatus not in (:grStatus) and g.createdAt >= :startOfFinancialYear "
                                + "and g.deliveryUnitId = :unitId and g.invalidGR=:invalidGR and g.generatedForVendor = :vendorId and g.docNumber = :docNumber "
                                + " and g.docType = :docType and g.vendorGRType = :vendorGRType " + " order by g.createdAt desc")
                .setParameter("unitId", unitId).setParameter("invalidGR", SCMServiceConstants.SCM_CONSTANT_NO)
                .setParameter("docNumber", docNumber).setParameter("docType", InvoiceDocType.INVOICE.name())
                .setParameter("startOfFinancialYear",
                        AppUtils.getStartOfMonth(AppUtils.getCurrentMonthFromDate() >= 3 ? AppUtils.getCurrentYearFromDate()
                                : AppUtils.getCurrentYearFromDate() - 1, 3))
                .setParameter("vendorId", vendorId).setParameter("vendorGRType", VendorGrType.REGULAR_ORDER.name()).setParameter("grStatus",
                        Arrays.asList(PurchaseOrderStatus.CANCELLED.value(), PurchaseOrderStatus.REJECTED.value()));
        return query.getResultList();
    }

    public static void main(String[] args) {
        System.out.println(AppUtils.getStartOfMonth(AppUtils.getCurrentMonthFromDate() >= 3 ? AppUtils.getCurrentYearFromDate()
                : AppUtils.getCurrentYearFromDate() - 1, 3));
    }

    @Override
    public String findStatusForPoSoRo(String handOverData, Integer unitId, String unitName) {
        StringBuilder status = new StringBuilder();
        String queryString = "FROM RequestOrderData g WHERE g.requestUnitId = :unitId and g.status IN(:statusList)";
        Query query = manager.createQuery(queryString);
        query.setParameter("unitId", unitId);
        List<String> statusList = Arrays.asList(SCMOrderStatus.CREATED.value(), SCMOrderStatus.INITIATED.value(),
                SCMOrderStatus.ACKNOWLEDGED.value());
        query.setParameter("statusList", statusList);
        List<RequestOrderData> ros = query.getResultList();
        if (!ros.isEmpty() && ros != null) {
            status.append("Pending RO's are in created state for this Unit,");
        }
//        removed service order closure check on handOver date
//		Query querySec = manager.createQuery(
//				"select serviceOrderId FROM ServiceOrderItemData si WHERE si.businessCostCenterName = :unitName");
//		querySec.setParameter("unitName", unitName);
//		List<Integer> soIds = querySec.getResultList();
//		if (!soIds.isEmpty()) {
//			Query queryThird = manager
//					.createQuery("FROM ServiceOrderData so WHERE so.id IN(:soIds) and so.status IN(:statusListSec)");
//			List<String> statusListSec = Arrays.asList(ServiceOrderStatus.CREATED.value(),
//					ServiceOrderStatus.APPROVED.value(), ServiceOrderStatus.PENDING_APPROVAL_L1.value(),
//					ServiceOrderStatus.PENDING_APPROVAL_L2.value(), ServiceOrderStatus.PENDING_APPROVAL_L3.value(),
//					ServiceOrderStatus.PENDING_APPROVAL_L4.value(), ServiceOrderStatus.PENDING_APPROVAL_L5.value(),
//					ServiceOrderStatus.PENDING_APPROVAL_L6.value(), ServiceOrderStatus.IN_PROGRESS.value());
//			queryThird.setParameter("statusListSec", statusListSec);
//			queryThird.setParameter("soIds", soIds);
//			List<ServiceOrderData> so = queryThird.getResultList();
//			if (!so.isEmpty() && so != null) {
//				status.append(" Pending SO's are in created state for this Unit,");
//			}
//		}

        Query queryForth = manager.createQuery(
                "FROM PurchaseOrderData po WHERE po.deliveryLocationId =:unitId and po.status IN(:statusListForth)");
        queryForth.setParameter("unitId", unitId);
        List<String> statusListForth = Arrays.asList(PurchaseOrderStatus.INITIATED.value(),
                PurchaseOrderStatus.APPROVED.value(), PurchaseOrderStatus.CREATED.value(),
                PurchaseOrderStatus.IN_PROGRESS.value());
        ;
        queryForth.setParameter("statusListForth", statusListForth);
        List<PurchaseOrderData> po = queryForth.getResultList();
        if (!po.isEmpty() && po != null) {
            status.append("Pending PO's are in created state for this Unit");
        }

        return status.toString();
    }

    @Override
    public List<AssetDefinitionData> getInitiatedAssetForGr(List<Integer> grIds) {
        Query query = manager.createQuery("FROM AssetDefinitionData a" +
                " WHERE a.grId in :grIds and a.assetStatus = :assetStatus ");
        query.setParameter("grIds", grIds);
        query.setParameter("assetStatus", AssetStatusType.INITIATED.value());
        List<AssetDefinitionData> assets = query.getResultList();
        if (Objects.isNull(assets)) {
            return new ArrayList<>();
        }
        return assets;
    }

    @Override
    public List<AssetDefinitionData> getAssetForGr(List<Integer> grIds) {
        Query query = manager.createQuery("FROM AssetDefinitionData a" +
                " WHERE a.grId in :grIds ");
        query.setParameter("grIds", grIds);
        List<AssetDefinitionData> assets = query.getResultList();
        if (Objects.isNull(assets)) {
            return new ArrayList<>();
        }
        return assets;
    }
}

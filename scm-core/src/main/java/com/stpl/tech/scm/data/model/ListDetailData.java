package com.stpl.tech.scm.data.model;


import java.util.Collection;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

/**
 * Copyright (C) , Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential Created by <PERSON><PERSON><PERSON> on 23-01-2020.
 */

@Entity
@Table(name = "LIST_DETAIL")
public class ListDetailData {

	private Integer listDetailId;
	private String type;
	private String code;
	private String name;
	private String description;
	private String status;
	private String alias;
	private String baseType;
	private List<ListTypeData> listType;
	private Collection<CostElementData> costElementDataCategory;
	private Collection<CostElementData> costElementDataDepartment;
	private Collection<CostElementData> costElementDataDivision;
	private String isAccountable;
	private String email ;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "LIST_DETAIL_ID", unique = true, nullable = false)
	public Integer getId() {
		return listDetailId;
	}

	public void setId(Integer listDetailId) {
		this.listDetailId = listDetailId;
	}

	@Column(name = "CODE", nullable = false)
	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	@Column(name = "NAME", nullable = false)
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Column(name = "DESCRIPTION", nullable = false)
	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	@Column(name = "STATUS", nullable = false)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "ALIAS", nullable = false)
	public String getAlias() {
		return alias;
	}

	public void setAlias(String alias) {
		this.alias = alias;
	}

	@Column(name = "TYPE", nullable = false)
	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "listDetail")
	public List<ListTypeData> getListType() {
		return listType;
	}

	public void setListType(List<ListTypeData> listType) {
		this.listType = listType;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "category")
	public Collection<CostElementData> getCostElementDataCategory() {
		return costElementDataCategory;
	}

	public void setCostElementDataCategory(Collection<CostElementData> costElementDataCategory) {
		this.costElementDataCategory = costElementDataCategory;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "department")
	public Collection<CostElementData> getCostElementDataDepartment() {
		return costElementDataDepartment;
	}

	public void setCostElementDataDepartment(Collection<CostElementData> costElementDataDepartment) {
		this.costElementDataDepartment = costElementDataDepartment;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "division")
	public Collection<CostElementData> getCostElementDataDivision() {
		return costElementDataDivision;
	}

	public void setCostElementDataDivision(Collection<CostElementData> costElementDataDivision) {
		this.costElementDataDivision = costElementDataDivision;
	}

	@Column(name = "BASE_TYPE", nullable = false)
	public String getBaseType() {
		return baseType;
	}

	public void setBaseType(String baseType) {
		this.baseType = baseType;
	}

	@Column(name = "IS_ACCOUNTABLE", nullable = false)
	public String getIsAccountable() {
		return isAccountable;
	}

	public void setIsAccountable(String isAccountable) {
		this.isAccountable = isAccountable;
	}

	@Column(name = "EMAIL",nullable = true)
	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}
}

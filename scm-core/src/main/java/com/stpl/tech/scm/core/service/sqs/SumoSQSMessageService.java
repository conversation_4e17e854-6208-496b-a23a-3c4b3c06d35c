package com.stpl.tech.scm.core.service.sqs;

import com.amazon.sqs.javamessaging.SQSSession;
import com.amazonaws.regions.Regions;
import com.stpl.tech.master.notification.SQSNotification;
import com.stpl.tech.scm.core.service.MonkWastageManagementService;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.jms.JMSException;
import javax.jms.MessageConsumer;
import javax.jms.Session;

@Slf4j
@Service
public class SumoSQSMessageService {

    @Autowired
    private Environment env;

    @Autowired
    private MonkWastageManagementService monkWastageManagementService;

    private SQSSession session;
    private MessageConsumer consumer;

    @PostConstruct
    public void init() throws JMSException {
        EnvType envType = EnvType.valueOf(env.getProperty("environment.type", "LOCAL"));
        Regions currentRegion = AppUtils.getRegion(envType);
        session = SQSNotification.getInstance().getSession(currentRegion, Session.CLIENT_ACKNOWLEDGE);
        SumoListener listener = new SumoListener(monkWastageManagementService);
        consumer = SQSNotification.getInstance().getConsumer(session, envType.name(),
                "_SUMO_QUEUE.fifo");
        consumer.setMessageListener(listener);
        startQueueProcessing(currentRegion);
    }

    @PreDestroy
    public void cleanup() {
        try {
            log.info("Shutting down Sumo SQS Message Service...");
            if (consumer != null) {
                stopQueueProcessing();
            }
            if (session != null) {
                session.close();
                log.info("Sumo SQS session closed successfully");
            }
            log.info("Sumo SQS Message Service shutdown completed");
        } catch (Exception e) {
            log.error("Error during Sumo SQS Message Service shutdown", e);
        }
    }

    /**
     * Method to stop Sumo queue listener.
     * <p>
     * Stops only the specific consumer for STAGE_SUMO_QUEUE.fifo
     * 
     * @throws JMSException
     */
    public void stopQueueProcessing() throws JMSException {
        log.info("Stopping Sumo Queue Consumer for STAGE_SUMO_QUEUE.fifo");
        if (consumer != null) {
            consumer.close();
            log.info("Stopped Sumo Queue Consumer for STAGE_SUMO_QUEUE.fifo");
        }
    }

    /**
     * Method to start Sumo queue listener.
     * <p>
     * To be used in pair with stopQueueProcessing
     *
     * @throws JMSException
     */
    public void startQueueProcessing(Regions region) throws JMSException {
        log.info("Starting Sumo Queueing Service");
        SQSNotification.getInstance().getSqsConnection(region).start();
        log.info("Started Sumo Queueing Service");
    }

} 
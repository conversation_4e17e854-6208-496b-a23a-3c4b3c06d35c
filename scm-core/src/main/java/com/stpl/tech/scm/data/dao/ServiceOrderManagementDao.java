package com.stpl.tech.scm.data.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.stpl.tech.scm.data.model.BusinessCostCenterData;
import com.stpl.tech.scm.data.model.CapexAuditDetailData;
import com.stpl.tech.scm.data.model.CapexBudgetDetailData;
import com.stpl.tech.scm.data.model.CostCenterData;
import com.stpl.tech.scm.data.model.CostElementData;
import com.stpl.tech.scm.data.model.BusinessCostCenterMappingData;
import com.stpl.tech.scm.data.model.EmployeeCostCenterMappingData;
import com.stpl.tech.scm.data.model.EmployeeCostCenterMappingData;

import com.stpl.tech.scm.data.model.ServiceOrderData;
import com.stpl.tech.scm.data.model.ServiceOrderStatusEventData;
import com.stpl.tech.scm.data.model.ServiceReceivedItemData;
import com.stpl.tech.scm.data.model.VendorCostCenterCostElementMapping;
import com.stpl.tech.scm.data.model.VendorDetailData;
import com.stpl.tech.scm.data.transport.model.UnitCapexDataSummary;
import com.stpl.tech.scm.domain.model.BusinessCostCenterMappingDomain;
import com.stpl.tech.scm.domain.model.PaymentRequest;
import com.stpl.tech.scm.domain.model.ServiceOrderStatus;

public interface ServiceOrderManagementDao extends SCMAbstractDao {

    public List<ServiceOrderData> findServiceOrders(Integer vendorId, Integer dispatchId, List<ServiceOrderStatus> statusList,  List<Integer> costElements, boolean showAll );

    public List<ServiceOrderData> findServiceOrders(Integer bccId, Integer vendorId, Integer serviceOrderId, List<ServiceOrderStatus> statusList,
													Date startDate, Date endDate, List<Integer> costElements, boolean showAll , boolean isShort,
													Integer costCenterId,String type);

	public List<Integer> findCostCentersForEmployee(Integer empId);
	public List<Integer> findBusinessCostCentersForEmployee(Integer costCenterId);

	public List<CostCenterData> findCostCenters(List<Integer> costCenterIds);
	public List<BusinessCostCenterData> findBusinessCostCenters(List<Integer> costCenterIds);

	public List<Integer> findCostElementsForEmployee(Integer empId);

	public String getCostElementStatus(Integer costId);

	public List<Integer> findVendorIds(Integer costCenterId);

	public List<VendorDetailData> findAllVendors(List<Integer> vendorIds);

	public List<Integer> findCostElementIds(Integer costCenterId, Integer vendorId);

	public List<CostElementData> findCostElements(List<Integer> costElementIds);

	public VendorCostCenterCostElementMapping findCostElement(Integer costCenterId, Integer vendorId, Integer costElementId);

	public List<ServiceOrderData> getTagDataList(Integer costCenterId, String tagName);

	public List<String> getTagNamesList(Integer costCenterId);

	public boolean updateStatusInMappings(Integer costId);

	public ServiceOrderStatusEventData findServiceOrderStatus(Integer soId);

	public boolean findBudgetDetail(Integer unitId);

	public BigDecimal findUnitAmount(Integer unitId, String departmentName);

	public CapexBudgetDetailData findBudgetUnit(Integer unitId, String departmentName);

	public List<CapexBudgetDetailData> findBudgetUnitV2(Integer unitId, List<Integer> departmentIdList);

	public CapexAuditDetailData findCapexAuditData(int parseInt);

	public BigDecimal getReceivedQuantity(Integer id);

	Map<String, BigDecimal> getServiceReceivedData(Date endDate, Integer unitId);

	public List<ServiceReceivedItemData> getServiceReceivedItemData(Integer soId);

	List<Integer> getServiceOrderIdsForFinalizedUpdation(Date endDate,Integer unitId);

	void setAccountableForPnlServiceOrder(List<Integer> ids,boolean  status);

	List<Integer> getServiceReceivedDataIds(Integer soId, String status);

	Date getStartDateOfCapexBudget(Integer capexRequestId);

	Map<Integer,BigDecimal> getPaidAmount(List<Integer> SoIds);

	List<ServiceOrderData> findSOsByDepartment(Integer bccId,List<ServiceOrderStatus> statusList, Date startDate, Date endDate, Integer departmentId);

	List<Integer> findCostElementsForDepartment(Integer departmentId);

	public List<UnitCapexDataSummary> findBudgetDetails(Set<Integer> unitIds , Date validDate);

    public List<Integer> getExistingSrInFinancialYear(PaymentRequest paymentRequest);

	Date getStartDate(Integer capexRequestId);

	List<String> showRequiredDocuments(List<Integer> costElementIds);

    public List<ServiceOrderData> getSosForAdvance(Integer vendorId);

    BusinessCostCenterMappingData findBusinessCostCentersByCostCenter(Integer id, Integer integer);

	public EmployeeCostCenterMappingData findCostCentersEmployeeActiveMapping(Integer empId , Integer costCenterId);

    List<Integer> findVendorIdsForCostElements(Integer costCenterId, Integer costElementId);

	Map<Integer,BigDecimal> getSOLevelPaidAmount(List<Integer> SoIds);



	Date getVendorSOApprovalDate(Integer soId);



    List<BusinessCostCenterMappingDomain> findBusinessCostCentersData(Integer costCenterId);



	Date getLastOperationDate(Integer unitId);
}

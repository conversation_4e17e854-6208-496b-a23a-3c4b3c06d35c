package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.mapper.DomainDataMapper;
import com.stpl.tech.scm.core.service.MonkWastageManagementService;
import com.stpl.tech.scm.core.service.StockManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.MonkWastageManagementDao;
import com.stpl.tech.scm.data.model.DuplicateMonkWastageDetailData;
import com.stpl.tech.scm.data.model.MonkWastageDetailData;
import com.stpl.tech.scm.domain.model.Fatal;
import com.stpl.tech.scm.domain.model.MonkWastageDetailDto;
import com.stpl.tech.scm.domain.model.MonkWastageProcessingEnum;
import com.stpl.tech.scm.domain.model.ProductBasicDetail;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.StockEventStatus;
import com.stpl.tech.scm.domain.model.WastageData;
import com.stpl.tech.scm.domain.model.WastageEvent;
import com.stpl.tech.scm.domain.model.WastageEventType;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MonkWastageManagementServiceImpl implements MonkWastageManagementService {

    @Autowired
    private MonkWastageManagementDao monkWastageManagementDao;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private StockManagementService stockManagementService;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED, isolation = Isolation.REPEATABLE_READ)
    public void saveMonkWastageDetail(MonkWastageDetailDto wastageDetailDto, Integer unitId) {
        try {
            log.info("Saving monk wastage detail for unit: {}, product: {}, taskId: {}, monkEvent: {}",
                    wastageDetailDto.getUnitId(), wastageDetailDto.getMilkProductId(), 
                    wastageDetailDto.getTaskId(), wastageDetailDto.getMonkEvent());
            
            MonkWastageDetailData entityData = DomainDataMapper.INSTANCE.toMonkWastageDetailData(wastageDetailDto);
            entityData.setUnitId(unitId);
            if (Objects.isNull(entityData.getLogAddTime())) {
                entityData.setLogAddTime(AppUtils.getCurrentTimestamp());
            }
            entityData.setLogAddTimeAtServer(AppUtils.getCurrentTimestamp());
            if (entityData.getQuantity().compareTo(BigDecimal.ZERO) < 0) {
                log.info("Quantity for monk wastage detail is negative"
                        + " for unit: {}, product: {}, quantity: {}",
                        entityData.getUnitId(), entityData.getMilkProductId(), entityData.getQuantity());
                entityData.setOriginalQuantity(entityData.getQuantity());
                entityData.setQuantity(BigDecimal.ZERO);
            }

            if (Objects.nonNull(entityData.getExpectedMilkQuantity())) {

                if (entityData.getExpectedMilkQuantity().compareTo(BigDecimal.ZERO) == 0) {
                    log.info("Expected milk quantity is zero for unit: {}, product: {}, resetting to 0",
                            entityData.getUnitId(), entityData.getMilkProductId());
                    entityData.setOriginalQuantity(entityData.getQuantity());
                    entityData.setQuantity(BigDecimal.ZERO);
                } else {

                    // Calculate quantity received * 1000
                    BigDecimal quantityReceivedTimes1000 = SCMUtil.multiplyWithScale10(entityData.getQuantity(), BigDecimal.valueOf(1000));

                    // Calculate expected milk quantity with 5% buffer (105% of expected)
                    BigDecimal expectedWithBuffer = SCMUtil.multiplyWithScale10(entityData.getExpectedMilkQuantity(), BigDecimal.valueOf(105));
                    BigDecimal expectedWithBufferDividedBy100 = SCMUtil.divideWithScale10(expectedWithBuffer, BigDecimal.valueOf(100));

                    // Check if quantity received * 1000 is greater than expected milk quantity with 5% buffer
                    if (quantityReceivedTimes1000.compareTo(expectedWithBufferDividedBy100) > 0) {
                        // Store the original received quantity
                        BigDecimal originalReceivedQuantity = entityData.getQuantity();

                        // Reset quantity to expected milk quantity / 1000
                        BigDecimal resetQuantity = SCMUtil.divideWithScale10(entityData.getExpectedMilkQuantity(), BigDecimal.valueOf(1000));
                        entityData.setQuantity(resetQuantity);

                        // Set original quantity as the quantity we received
                        entityData.setOriginalQuantity(originalReceivedQuantity);

                        log.info("Quantity received * 1000 ({}) is greater than expected milk quantity with 5% buffer ({}). " +
                                        "Resetting quantity to expected milk quantity / 1000: {} and setting original quantity to: {}",
                                quantityReceivedTimes1000, expectedWithBufferDividedBy100, resetQuantity, originalReceivedQuantity);
                    }
                }
            }
            
            // Check for duplicate remake events if this is not a manual task and has a task ID
            if (entityData.getTaskId() != null && 
                !AppConstants.YES.equals(entityData.getIsManualTask()) &&
                    Fatal.REASSIGN.getName().equals(entityData.getMonkEvent())) {
                
                log.info("Checking for duplicate remake event for taskId: {}, chaiMonk: {}",
                        entityData.getTaskId(), entityData.getChaiMonk());
                
                MonkWastageDetailData latestEntry = monkWastageManagementDao.findLatestWastageDetailWithErrorAndRemake(entityData.getTaskId(), entityData.getLogAddTime());
                
                if (latestEntry != null && 
                    Objects.equals(latestEntry.getChaiMonk(), entityData.getChaiMonk())
                    && Fatal.REMAKE.getName().equals(latestEntry.getMonkEvent())
                    && Objects.nonNull(latestEntry.getErrorCode())
                    && Math.abs(SCMUtil.getMinutesDifference(latestEntry.getLogAddTime(), entityData.getLogAddTime())) <= 10
                    ) {
                    
                    long timeDiffMinutes = Math.abs(SCMUtil.getMinutesDifference(latestEntry.getLogAddTime(), entityData.getLogAddTime()));
                    log.info("Duplicate remake event detected for taskId: {}, chaiMonk: {}. " +
                            "Latest entry has errorCode: {} and monkEvent: {}. " +
                            "Time difference: {} minutes (within 10 min limit). " +
                            "Saving to duplicate table instead of main table.", 
                            entityData.getTaskId(), entityData.getChaiMonk(), 
                            latestEntry.getErrorCode(), latestEntry.getMonkEvent(), timeDiffMinutes);
                    
                    // Convert entity to duplicate entity using mapper
                    DuplicateMonkWastageDetailData duplicateData = DomainDataMapper.INSTANCE.toDuplicateMonkWastageDetailData(entityData);
                    monkWastageManagementDao.saveDuplicateMonkWastageDetail(duplicateData);
                    
                    log.info("Successfully saved duplicate monk wastage detail to duplicate table for taskId: {}, chaiMonk: {}", 
                            entityData.getTaskId(), entityData.getChaiMonk());
                    return; // Exit early, don't save to main table
                }
                
                log.info("No duplicate remake event found for taskId: {}, chaiMonk: {}. Proceeding with normal save.",
                        entityData.getTaskId(), entityData.getChaiMonk());
            }
            
            monkWastageManagementDao.saveMonkWastageDetail(entityData);
        } catch (Exception e) {
            log.error("Error saving monk wastage detail: ", e);
            throw new RuntimeException("Failed to save monk wastage detail", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED, isolation = Isolation.REPEATABLE_READ)
    public void processUnprocessedWastageDetailsForUnit(Integer unitId, boolean bypassThreshold) {
        try {
            String logPrefix = unitId != null ? "for unit: " + unitId : "for all units";
            log.info("Starting processing of unprocessed monk wastage details {} (bypassThreshold: {})", logPrefix, bypassThreshold);

            // Get unprocessed wastage details grouped by unit
            Map<Integer, List<MonkWastageDetailData>> unprocessedDataByUnit =
                    monkWastageManagementDao.getUnprocessedWastageDetailsGroupedByUnit(unitId);

            if (unprocessedDataByUnit.isEmpty()) {
                log.info("No unprocessed wastage details found {}", logPrefix);
                return;
            }

            log.info("Processing unprocessed wastage details for {} units {} (bypassThreshold: {})", unprocessedDataByUnit.size(), logPrefix, bypassThreshold);

            for (Map.Entry<Integer, List<MonkWastageDetailData>> entry : unprocessedDataByUnit.entrySet()) {
                Integer currentUnitId = entry.getKey();
                List<MonkWastageDetailData> unprocessedDetails = entry.getValue();

                processWastageDetailsForUnit(currentUnitId, unprocessedDetails, bypassThreshold);
            }

            log.info("Completed processing of unprocessed monk wastage details {} (bypassThreshold: {})", logPrefix, bypassThreshold);
        } catch (Exception e) {
            log.error("Error processing unprocessed wastage details{}: ", unitId != null ? " for unit: " + unitId : "", e);
            throw new RuntimeException("Failed to process unprocessed wastage details", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED, isolation = Isolation.READ_COMMITTED)
    public List<MonkWastageDetailData> getUnprocessedWastageDetails(Integer unitId) {
        try {
            log.info("Getting unprocessed wastage details for unit: {}", unitId);
            return monkWastageManagementDao.getUnprocessedWastageDetails(unitId);
        } catch (Exception e) {
            log.error("Error getting unprocessed wastage details for unit {}: ", unitId, e);
            throw new RuntimeException("Failed to get unprocessed wastage details for unit: " + unitId, e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED, isolation = Isolation.READ_COMMITTED)
    public Map<Integer, List<MonkWastageDetailData>> getAllUnprocessedWastageDetailsGroupedByUnit() {
        try {
            log.info("Getting all unprocessed wastage details grouped by unit");
            return monkWastageManagementDao.getAllUnprocessedWastageDetailsGroupedByUnit();
        } catch (Exception e) {
            log.error("Error getting all unprocessed wastage details: ", e);
            throw new RuntimeException("Failed to get all unprocessed wastage details", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED, isolation = Isolation.READ_COMMITTED)
    public Map<Integer, List<MonkWastageDetailData>> getUnprocessedWastageDetailsGroupedByUnit(Integer unitId) {
        try {
            String logPrefix = unitId != null ? "for unit: " + unitId : "for all units";
            log.info("Getting unprocessed wastage details grouped by unit {}", logPrefix);
            return monkWastageManagementDao.getUnprocessedWastageDetailsGroupedByUnit(unitId);
        } catch (Exception e) {
            log.error("Error getting unprocessed wastage details grouped by unit{}: ", 
                    unitId != null ? " for unit: " + unitId : "", e);
            throw new RuntimeException("Failed to get unprocessed wastage details grouped by unit", e);
        }
    }

    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED, isolation = Isolation.REPEATABLE_READ)
    public void processWastageDetailsForUnit(Integer unitId, List<MonkWastageDetailData> unprocessedDetails, boolean bypassThreshold) {
        try {
            log.info("Processing {} wastage details for unit: {} (bypassThreshold: {})", unprocessedDetails.size(), unitId, bypassThreshold);

            if (unprocessedDetails.isEmpty()) {
                log.info("No unprocessed wastage details found for unit: {}", unitId);
                return;
            }

            // Group by milk product ID
            Map<Integer, List<MonkWastageDetailData>> groupedByProduct = unprocessedDetails.stream()
                    .filter(detail -> detail.getMilkProductId() != null)
                    .collect(Collectors.groupingBy(MonkWastageDetailData::getMilkProductId));

            List<WastageEvent> wastageEvents = new ArrayList<>();

            for (Map.Entry<Integer, List<MonkWastageDetailData>> entry : groupedByProduct.entrySet()) {
                Integer productId = entry.getKey();
                List<MonkWastageDetailData> productWastageDetails = entry.getValue();

                // Calculate total quantity for this product
                BigDecimal totalQuantity = productWastageDetails.stream()
                        .map(MonkWastageDetailData::getQuantity)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                log.info("Total wastage quantity for product {} in unit {}: {}", productId, unitId, totalQuantity);

                // Check quantity threshold (500) unless bypassed
                if (!bypassThreshold && totalQuantity.compareTo(SCMServiceConstants.MONK_WASTAGE_THRESHOLD) < 0) {
                    log.info("Wastage quantity {} for product {} in unit {} is below threshold {}. Skipping wastage processing.",
                            totalQuantity, productId, unitId, SCMServiceConstants.MONK_WASTAGE_THRESHOLD);
                    continue;
                }

                // Create wastage event
                WastageEvent wastageEvent = createWastageEvent(unitId, productId, totalQuantity, productWastageDetails);
                wastageEvents.add(wastageEvent);
            }

            if (!wastageEvents.isEmpty()) {
                // Use stock management service to process wastage events
                List<WastageEvent> processedWastageEvents = stockManagementService.addManualWastageEvent(wastageEvents, false, unitId, AppConstants.SYSTEM_EMPLOYEE_ID);

                // Update the wastage details with the wastage item IDs and mark as processed
                for (int i = 0; i < wastageEvents.size(); i++) {
                    WastageEvent originalEvent = wastageEvents.get(i);
                    WastageEvent processedEvent = processedWastageEvents.get(i);

                    if (processedEvent != null && !processedEvent.getItems().isEmpty()) {
                        Integer wastageItemId = processedEvent.getItems().get(0).getId();

                        // Find the corresponding product details
                        Integer productId = originalEvent.getItems().get(0).getProductId();
                        List<MonkWastageDetailData> productWastageDetails = groupedByProduct.get(productId);

                        if (productWastageDetails != null) {
                            List<Integer> wastageDetailIds = productWastageDetails.stream()
                                    .map(MonkWastageDetailData::getId)
                                    .collect(Collectors.toList());

                            monkWastageManagementDao.updateWastageItemIdAndProcessingStatus(wastageDetailIds, wastageItemId, MonkWastageProcessingEnum.PROCESSED);

                            log.info("Successfully processed {} wastage details for product {} in unit {}",
                                    wastageDetailIds.size(), productId, unitId);
                        }
                    }
                }
            } else {
                log.info("No wastage events created for unit {} (all quantities below threshold or no valid data)", unitId);
            }
        } catch (Exception e) {
            log.error("Error processing wastage details for unit {}: ", unitId, e);
            throw new RuntimeException("Failed to process wastage details for unit: " + unitId, e);
        }
    }

    private WastageEvent createWastageEvent(Integer unitId, Integer productId, BigDecimal totalQuantity,
                                            List<MonkWastageDetailData> wastageDetails) {
        WastageEvent wastageEvent = new WastageEvent();
        wastageEvent.setUnitId(unitId);
        wastageEvent.setBusinessDate(SCMUtil.getCurrentBusinessDate());
        wastageEvent.setGenerationTime(AppUtils.getCurrentTimestamp());
        wastageEvent.setStatus(StockEventStatus.SETTLED);
        wastageEvent.setType(WastageEventType.PRODUCT);
        wastageEvent.setGeneratedBy(AppConstants.SYSTEM_EMPLOYEE_ID);

        // Create wastage data for the product
        WastageData wastageData = new WastageData();
        ProductDefinition productDef = scmCache.getProductDefinition(productId);
        if (productDef != null) {
            ProductBasicDetail product = SCMDataConverter.convert(productDef);
            wastageData.setProduct(product);
            wastageData.setProductId(productId);
        }

        wastageData.setQuantity(totalQuantity);
        wastageData.setComment(SCMServiceConstants.MONK_WASTAGE);
        wastageData.setCost(BigDecimal.ZERO);

        wastageEvent.getItems().add(wastageData);

        return wastageEvent;
    }
}
package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.List;

@Entity
@Table(name = "MONK_STATUS_DAY_CLOSE")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MonkStatusDayCloseData {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MONK_STATUS_DAY_CLOSE_ID")
    private Long monkStatusDayCloseId;
    
    @Column(name = "EVENT_STATUS_ID", nullable = false)
    private Long eventStatusId;
    
    @Column(name = "MONK_NAME", nullable = false, length = 100)
    private String monkName;
    
    @Column(name = "MONK_STATUS", nullable = false, length = 50)
    private String monkStatus;
    
    @Column(name = "UPDATED_AT")
    private Timestamp updatedAt;
    
    @Column(name = "CREATED_AT")
    private Timestamp createdAt;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "EVENT_STATUS_ID", insertable = false, updatable = false)
    private MonkDayCloseEventStatusData eventStatus;
    
    // One-to-Many relationship with MonkStatusDayCloseHistoryData
    @OneToMany(mappedBy = "monkStatusDayClose", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<MonkStatusDayCloseHistoryData> statusHistory;
    
    @PrePersist
    protected void onCreate() {
        createdAt = new Timestamp(System.currentTimeMillis());
        updatedAt = new Timestamp(System.currentTimeMillis());
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = new Timestamp(System.currentTimeMillis());
    }
}

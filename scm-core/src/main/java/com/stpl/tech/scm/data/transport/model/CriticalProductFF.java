package com.stpl.tech.scm.data.transport.model;

import lombok.Data;

@Data
public class CriticalProductFF {

    Long productId;
    String productName;
    String productLevel;

    // full fill ment IFF Percentage
    Double lastDayFF = 0.0;
    Double lastSevenDaysFF = 0.0;
    Double mtdFF =  0.0;

    // On Time full fill ment IFF Percentage
    Double lastDayOnTimeFF = 0.0;
    Double lastSevenDaysOnTimeFF = 0.0;
    Double mtdOnTimeFF = 0.0;

    // On Date full fill ment IFF Percentage
    Double lastDayOnDateFF = 0.0;
    Double lastSevenDaysOnDateFF = 0.0;
    Double mtdOnDateFF = 0.0;

}

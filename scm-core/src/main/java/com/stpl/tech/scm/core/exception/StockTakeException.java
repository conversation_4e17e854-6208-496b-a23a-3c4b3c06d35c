package com.stpl.tech.scm.core.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 26-10-2016.
 */

@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR, reason = "Negative Stock Entered")
public class StockTakeException extends Exception {

    private static final long serialVersionUID = 616315625637405710L;

    public StockTakeException() {
    }

    public StockTakeException(String message) {
        super(message);
    }

    public StockTakeException(Throwable cause) {
        super(cause);
    }

    public StockTakeException(String message, Throwable cause) {
        super(message, cause);
    }

    public StockTakeException(String message, Throwable cause, boolean enableSuppression,
                                 boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
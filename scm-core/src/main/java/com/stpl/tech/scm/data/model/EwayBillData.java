package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

@Entity
@Table(name = "EWAY_BILL_DATA")
public class EwayBillData {

	private Integer id;
	private String ewayBillNumber;
	private TransferOrderData transferOrder;
	private String status;
	private BigDecimal distance;
	private List<EWayStatusUpdateEvent> eventStatusList = new ArrayList<>();
	private Integer consignmentId;
	private VehicleData vehicle;
	private String ewayRequired;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "EWAY_DATA_ID", unique = true, nullable = false)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "EBN", nullable = true)
	public String getEwayBillNumber() {
		return ewayBillNumber;
	}

	public void setEwayBillNumber(String ewayBillNumber) {
		this.ewayBillNumber = ewayBillNumber;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "TRANSFER_ORDER_ID", nullable = true)
	public TransferOrderData getTransferOrder() {
		return transferOrder;
	}

	public void setTransferOrder(TransferOrderData transferOrder) {
		this.transferOrder = transferOrder;
	}

	@Column(name = "EVENT_STATUS", nullable = false)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "DISTANCE", nullable = false)
	public BigDecimal getDistance() {
		return distance;
	}

	public void setDistance(BigDecimal distance) {
		this.distance = distance;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "eventId")
	public List<EWayStatusUpdateEvent> getEventStatusList() {
		return eventStatusList;
	}

	public void setEventStatusList(List<EWayStatusUpdateEvent> eventStatusList) {
		this.eventStatusList = eventStatusList;
	}

	@Column(name = "CONSIGNMENT_ID", nullable = false)
	public Integer getConsignmentId() {
		return consignmentId;
	}

	public void setConsignmentId(Integer consignmentId) {
		this.consignmentId = consignmentId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "VEHICLE_ID", nullable = true)
	public VehicleData getVehicle() {
		return vehicle;
	}

	public void setVehicle(VehicleData vehicle) {
		this.vehicle = vehicle;
	}

	@Column(name = "EWAY_REQUIRED", nullable = false)
	public String getEwayRequired() {
		return ewayRequired;
	}

	public void setEwayRequired(String ewayRequired) {
		this.ewayRequired = ewayRequired;
	}
	
}

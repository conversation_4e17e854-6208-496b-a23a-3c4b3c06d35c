/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.core.service;

import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.readonly.domain.model.ProductVO;
import com.stpl.tech.scm.data.model.KettleProductDataClone;
import com.stpl.tech.scm.data.model.KettleStockOutPercentageData;
import com.stpl.tech.scm.data.model.KettleUnitDetailDataClone;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

public interface KettleStockOutAsyncService {

    /**
     * Asynchronously creates and persists kettle unit detail data clone for a specific unit and brand
     *
     * @param unit the unit for which data needs to be created
     * @param brand the brand associated with the unit
     * @param productDetails map of product details
     * @param productVOS collection of product value objects
     * @return CompletableFuture<Void> representing the async operation
     */
    CompletableFuture<Void> createKettleUnitDetailDataCloneAsync(Unit unit, Brand brand,
                                                                 Map<Integer, Product> productDetails,
                                                                 Collection<ProductVO> productVOS);

    /**
     * Asynchronously processes stock out percentage data for a specific unit
     *
     * @param unitId the unit ID to process
     * @param kettleUnitDetailDataClones list of kettle unit detail data clones for the unit
     * @param productDataCloneMap map of product data clones
     * @return CompletableFuture<Void> representing the async operation
     */
    CompletableFuture<Void> processUnitStockOutPercentageAsync(Integer unitId,
                                                               List<KettleUnitDetailDataClone> kettleUnitDetailDataClones,
                                                               Map<Integer, KettleProductDataClone> productDataCloneMap);

    /**
     * Asynchronously creates and persists kettle unit detail data clone for a specific unit and brand with business date
     *
     * @param unit the unit for which data needs to be created
     * @param brand the brand associated with the unit
     * @param productDetails map of product details
     * @param productVOS collection of product value objects
     * @param businessDate the business date to use for calculations
     * @return CompletableFuture<Void> representing the async operation
     */
    CompletableFuture<Void> createKettleUnitDetailDataCloneAsyncByDate(Unit unit, Brand brand,
                                                                       Map<Integer, Product> productDetails,
                                                                       Collection<ProductVO> productVOS,
                                                                       Date businessDate);

    /**
     * Asynchronously processes stock out percentage data for a specific unit with business date
     *
     * @param unitId the unit ID to process
     * @param kettleUnitDetailDataClones list of kettle unit detail data clones for the unit
     * @param productDataCloneMap map of product data clones
     * @param businessDate the business date to use for calculations
     * @return CompletableFuture<Void> representing the async operation
     */
    CompletableFuture<Void> processUnitStockOutPercentageAsyncByDate(Integer unitId,
                                                                     List<KettleUnitDetailDataClone> kettleUnitDetailDataClones,
                                                                     Map<Integer, KettleProductDataClone> productDataCloneMap,
                                                                     Date businessDate);
}

package com.stpl.tech.scm.notification.email;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.notification.email.template.VendorInvoiceRequestEmailNotificationTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by Chaayos on 05-09-2016.
 */
public class VendorInvoiceRequestEmailNotification extends EmailNotification {

	private VendorInvoiceRequestEmailNotificationTemplate vendorInvoiceRequestEmailNotificationTemplate;
	private EnvType envType;
	private String[] emails;

	public VendorInvoiceRequestEmailNotification() {

	}

	public VendorInvoiceRequestEmailNotification(VendorInvoiceRequestEmailNotificationTemplate vendorInvoiceRequestEmailNotificationTemplate,
                                                 EnvType envType, String[] emails) {
		this.vendorInvoiceRequestEmailNotificationTemplate = vendorInvoiceRequestEmailNotificationTemplate;
		this.envType = envType;
		this.emails = emails;
	}

	@Override
	public String[] getToEmails() {
		List<String> mails = new ArrayList<>();
		Arrays.asList(emails).forEach(email -> {
			mails.add(email);
		});
		VendorDetail vendorDetail = vendorInvoiceRequestEmailNotificationTemplate.getVendorDetail();
		if (vendorDetail.getPrimaryEmail() != null) {
			mails.add(vendorDetail.getPrimaryEmail());
		}
		if (vendorDetail.getSecondaryEmail() != null) {
			mails.add(vendorDetail.getSecondaryEmail());
		}
		String[] simpleArray = new String[mails.size()];
		return SCMUtil.isDev(envType) ? new String[] { "<EMAIL>" } : mails.toArray(simpleArray);
	}

	@Override
	public String getFromEmail() {
		return "<EMAIL>";
	}

	@Override
	public String subject() {
		String subject;
		subject = "Invoice Request From Chaayos for " +vendorInvoiceRequestEmailNotificationTemplate.getVendorDetail().getEntityName()+" "
				+ SCMUtil.getFormattedTime(SCMUtil.getCurrentDateIST(), "EEE dd MMM yyyy");
		if (SCMUtil.isDev(envType)) {
			subject = "Dev " + subject;
		}
		return subject;
	}

	@Override
	public String body() throws EmailGenerationException {
		try {
			return vendorInvoiceRequestEmailNotificationTemplate.getContent();
		} catch (TemplateRenderingException e) {
			throw new EmailGenerationException("Failed to render the template", e);
		}
	}

	@Override
	public EnvType getEnvironmentType() {
		return envType;
	}
}

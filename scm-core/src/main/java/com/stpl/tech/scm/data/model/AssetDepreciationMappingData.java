package com.stpl.tech.scm.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "ASSET_DEPRECIATION_MAPPING")
public class AssetDepreciationMappingData {

    private Integer assetDepreciationMappingId;

	private AssetDefinitionData assetDefinition;

    private Integer ownerId;

    private Integer unitId;

    private String assetStatus;

    private Date startDate;

    private Date endDate;

    private BigDecimal depreciationAmount;

    private Integer subCategoryId;

    public AssetDepreciationMappingData() {
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ASSET_DEPRECIATION_MAPPING_ID", unique = true, nullable = false)
    public Integer getAssetDepreciationMappingId() {
        return assetDepreciationMappingId;
    }

    public void setAssetDepreciationMappingId(Integer assetDepreciationMappingId) {
        this.assetDepreciationMappingId = assetDepreciationMappingId;
    }

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ASSET_ID", nullable = false)
    public AssetDefinitionData getAssetDefinition() {
		return assetDefinition;
	}

	public void setAssetDefinition(AssetDefinitionData assetDefinition) {
		this.assetDefinition = assetDefinition;
	}

    @Column(name = "UNIT_ID", nullable = false)
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "ASSET_STATUS", nullable = false)
    public String getAssetStatus() {
        return assetStatus;
    }

    public void setAssetStatus(String assetStatus) {
        this.assetStatus = assetStatus;
    }

    @Column(name = "OWNER_ID", nullable = false)
    public Integer getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }


    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "START_DATE", length = 19,  nullable = false)
    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "END_DATE", length = 19,  nullable = false)
    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }


    @Column(name = "DEPRECIATION_AMOUNT",  nullable = false)
    public BigDecimal getDepreciationAmount() {
        return depreciationAmount;
    }

    public void setDepreciationAmount(BigDecimal depreciationAmount) {
        this.depreciationAmount = depreciationAmount;
    }

    @Column(name = "SUB_CATEGORY_ID", nullable = false)
    public Integer getSubCategoryId() {
        return subCategoryId;
    }

    public void setSubCategoryId(Integer subCategoryId) {
        this.subCategoryId = subCategoryId;
    }
}

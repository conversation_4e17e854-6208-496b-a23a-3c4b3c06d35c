package com.stpl.tech.scm.data.dao.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.NoResultException;
import javax.persistence.NonUniqueResultException;
import javax.persistence.Query;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.data.model.PlanOrderItemData;
import com.stpl.tech.scm.data.model.RequestOrderItemData;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.UnitPlanItemRequest;
import com.stpl.tech.util.AppUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.ProductionPlanManagementDao;
import com.stpl.tech.scm.data.model.ProductionPlanEventData;
import com.stpl.tech.scm.domain.model.ProductionPlanEvent;

@Repository
public class ProductionPlanManagementDaoImpl extends SCMAbstractDaoImpl implements ProductionPlanManagementDao {

	@Autowired
	private SCMCache scmCache;

	@Autowired
	private MasterDataCache masterDataCache;

	@Override
	public List<ProductionPlanEvent> getPlansByFulfilmentDate(Date start, Date end, int unitId) {

		List<ProductionPlanEvent> eventList = new ArrayList<>();
		Query query = manager.createQuery(
				"FROM ProductionPlanEventData E WHERE E.unitId = :unitId AND E.generationTime >= :startDate AND E.generationTime <= :endDate AND E.status = :status");
		query.setParameter("unitId", unitId);
		query.setParameter("startDate", SCMUtil.getDate(start));
		query.setParameter("endDate", SCMUtil.getNextDate(SCMUtil.getDate(end)));
		query.setParameter("status", SCMOrderStatus.CREATED.value());

		List<ProductionPlanEventData> dataList = query.getResultList();
		if (dataList != null && !dataList.isEmpty()) {
			for (ProductionPlanEventData data : dataList) {
				eventList.add(SCMDataConverter.convert(data, scmCache, masterDataCache));
			}
		}

		return eventList;
	}

	@Override
	public List<RequestOrderItemData> findRoItemByPlanEvent(int eventId, int productId) {
		Query query = manager.createQuery(
			"SELECT ROI FROM PlanOrderMappingData M, RequestOrderItemData ROI, RequestOrderData RO " +
				"WHERE M.event.id = :eventId AND RO.id = M.requestOrder.id AND RO.id = ROI.requestOrderData.id AND ROI.productId = :productId");
		query.setParameter("eventId", eventId);
		query.setParameter("productId", productId);
		return query.getResultList();
	}

	@Override
	public PlanOrderItemData findItemByProductAndEvent(int productId, int eventId, String itemType) {
		Query query = manager.createQuery(
			"FROM PlanOrderItemData E WHERE E.eventId = :eventId  AND E.productId = :productId AND E.itemType =:itemType");
		query.setParameter("eventId", eventId);
		query.setParameter("productId", productId);
		query.setParameter("itemType", itemType);
		try {
			return (PlanOrderItemData) query.getSingleResult();
		} catch (NoResultException e) {
			return null;
		} catch (NonUniqueResultException e) {
			return (PlanOrderItemData) query.getResultList().get(0);
		}
	}

	@Override
	public List<UnitPlanItemRequest> findRoItemByPlanItem(List<Integer> productIds, int eventId, Date fulfilmentDate) {
		List<UnitPlanItemRequest> sortedList = new ArrayList<>();
		Query query = manager.createNativeQuery("SELECT " +
			" A.UNIT_ID, A.PRODUCT_ID, A.REQUEST_ORDER_ID, A.REQUEST_ORDER_ITEM_ID, A.REQUESTED_QUANTITY, TRUNCATE(SUM(COALESCE(B.SUGGESTED_SALE, 0)) / A.NO_OF_DAYS, 0) AS AVG_SALES" +
			"    FROM" +
			"    (SELECT" +
			"        MAP.REQUEST_ORDER_ID, ROITEM.PRODUCT_ID AS PRODUCT_ID, " +
			"RO.REQUEST_UNIT_ID AS UNIT_ID, ROITEM.REQUEST_ORDER_ITEM_ID, " +
			"ROITEM.REQUESTED_QUANTITY, RO.FULFILLMENT_DATE AS START_DATE, " +
			"RO.NUMBER_OF_DAYS AS NO_OF_DAYS, DATE_ADD(RO.FULFILLMENT_DATE, INTERVAL RO.NUMBER_OF_DAYS-1 DAY) AS END_DATE" +
			"    FROM" +
			"        PLAN_ORDER_MAPPING MAP, REQUEST_ORDER_ITEM ROITEM, REQUEST_ORDER RO" +
			"    WHERE" +
			"        MAP.PLAN_EVENT_ID =:eventId" +
			"            AND MAP.REQUEST_ORDER_ID = ROITEM.REQUEST_ORDER_ID" +
			"            AND RO.REQUEST_ORDER_ID = MAP.REQUEST_ORDER_ID" +
			"            AND ROITEM.PRODUCT_ID  IN (:productIds)) A" +
			"        LEFT OUTER JOIN" +
			"    (SELECT " +
			"        UNIT_ID, TARGET_DATE, SUM(SUGGESTED_SALE) SUGGESTED_SALE" +
			"    FROM" +
			"        ESTIMATE_QUERY_REQUEST" +
			"    WHERE" +
			"        ORDERING_TYPE = 'ORDERING_DAY'" +
			"            AND STATUS = 'ACTIVE'" +
			"            AND TARGET_DATE >= :fulfilmentDate" +
			"    GROUP BY UNIT_ID , TARGET_DATE) B ON A.UNIT_ID = B.UNIT_ID" +
			"        AND B.TARGET_DATE >= A.START_DATE" +
			"        AND B.TARGET_DATE <= A.END_DATE" +
			" GROUP BY A.UNIT_ID , A.PRODUCT_ID , A.REQUEST_ORDER_ITEM_ID , A.REQUESTED_QUANTITY" +
			" ORDER BY A.REQUESTED_QUANTITY DESC , AVG_SALES DESC;");
		query.setParameter("eventId", eventId);
		query.setParameter("productIds", productIds);
		query.setParameter("fulfilmentDate", AppUtils.getSQLFormattedDate(fulfilmentDate));
		try {
			List<Object[]> list = query.getResultList();
			if (list != null) {
				for (Object[] obj : list) {
					UnitPlanItemRequest unitPlanItemRequest = new UnitPlanItemRequest();
					unitPlanItemRequest.setUnitId((Integer) obj[0]);
					unitPlanItemRequest.setRoProductId((Integer) obj[1]);
					unitPlanItemRequest.setRoId((Integer) obj[2]);
					unitPlanItemRequest.setRoItemId((Integer) obj[3]);
					unitPlanItemRequest.setRequestedQuantity((BigDecimal) obj[4]);
					if (obj[5] != null) {
						unitPlanItemRequest.setAverageSales((BigDecimal) obj[5]);
					} else {
						unitPlanItemRequest.setAverageSales(BigDecimal.ZERO);
					}
					sortedList.add(unitPlanItemRequest);
				}
			}
			return sortedList;
		}catch (Exception e){
			return null;
		}
	}
}

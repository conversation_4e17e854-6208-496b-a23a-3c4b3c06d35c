/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "CATEGORY_SUB_CATEGORY_DEBIT_NOTE")
public class CategorySubCategoryDebitNoteData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CATEGORY_SUB_CATEGORY_DEBIT_NOTE_ID",nullable = false, unique = true)
    private Integer categorySubCategoryDebitNoteId;

    @Column(name = "CATEGORY_ID",nullable = false)
    private Integer categoryId;

    @Column(name = "SUB_CATEGORY_ID",nullable = false)
    private Integer subCategoryId;

    @Column(name = "AMOUNT",nullable = false)
    private BigDecimal amount;

    @Column(name = "PAYMENT_REQUEST_ID",nullable = false)
    private Integer paymentRequestId;

    @Column(name = "DEBIT_NOTE_ID",nullable = false)
    private Integer debitNoteId;
}

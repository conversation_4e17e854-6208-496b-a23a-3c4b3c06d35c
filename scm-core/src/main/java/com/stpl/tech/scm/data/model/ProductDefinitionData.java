/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;
// Generated 4 May, 2016 12:06:56 AM by Hibernate Tools 3.2.2.GA

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * ProductDefinitionData generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "PRODUCT_DEFINITION", uniqueConstraints = @UniqueConstraint(columnNames = "PRODUCT_NAME"))
public class ProductDefinitionData implements java.io.Serializable {

    private Integer productId;
    private String productName;
    private String productDescription;
    private CategoryDefinitionData categoryDefinition;
    private SubCategoryDefinitionData subCategoryDefinition;
    private ProfileDefinitionData profileDefinitionData;
    private String supportsLooseOrdering;
    private Date creationDate;
    private int createdBy;
    private String hasInner;
    private String recipeRequired = "N";
    private String hasCase;
    private String stockKeepingFrequency;
    private String productCode;
    private int shelfLifeInDays;
    private String productStatus;
    private String unitOfMeasure;
    private String productImage;
    private BigDecimal unitPrice;
    private BigDecimal negotiatedUnitPrice;
    private String participatesInRecipe = "N";
    private String participatesInCafeRecipe = "N";
    private String assetOrdering = "N";
    private String variantLevelOrdering = "N";
    private String supportsSpecializedOrdering = "N";
    //private List<ProductFulfillmentTypeData> fulfillmentType = new ArrayList<ProductFulfillmentTypeData>(0);
    private String taxCategoryCode;
    private String fulfillmentType;
    private String defaultFulfillmentType;
    private String availableForCafe;
    private String interCafeTransfer;
    private String availableForCafeInventory;
    private String varianceType;
    private String kitchenVarianceType;
    private List<DerivedMappingData> derivedMappingDataList = new ArrayList<DerivedMappingData>(0);
    private Set<SkuDefinitionData> skuDefinitionDataList = new HashSet<>();
    private String autoProduction = "N";
    private String participatesInPnl = "Y";
    private String isBulkGRAllowed = "N";
    private Integer divisionId;
    private Integer departmentId;
    private Integer classificationId;
    private Integer subClassificationId;

    private String productType;

    private String categoryLevel;


    private Integer productCreationId;
    private Integer brandId;
    private  Integer companyId;
    private Integer approvalDocumentId;
    private UOMConversionMappingData uomConversionMappingData;

    public ProductDefinitionData() {
    }


    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "PRODUCT_ID", unique = true, nullable = false)
    public Integer getProductId() {
        return this.productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    @Column(name = "PRODUCT_NAME", unique = true, nullable = false)
    public String getProductName() {
        return this.productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @Column(name = "PRODUCT_DESCRIPTION", length = 1000)
    public String getProductDescription() {
        return this.productDescription;
    }

    public void setProductDescription(String productDescription) {
        this.productDescription = productDescription;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CATEGORY_ID", nullable = false)
    public CategoryDefinitionData getCategoryDefinition() {
        return this.categoryDefinition;
    }

    public void setCategoryDefinition(CategoryDefinitionData categoryDefinition) {
        this.categoryDefinition = categoryDefinition;
    }



    @Column(name = "SUPPORTS_LOOSE_ORDERING", nullable = false, length = 1)
    public String getSupportsLooseOrdering() {
        return this.supportsLooseOrdering;
    }

    public void setSupportsLooseOrdering(String supportsLooseOrdering) {
        this.supportsLooseOrdering = supportsLooseOrdering;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_DATE", length = 19)
    public Date getCreationDate() {
        return this.creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    @Column(name = "CREATED_BY", nullable = false)
    public int getCreatedBy() {
        return this.createdBy;
    }

    public void setCreatedBy(int createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "HAS_INNER", nullable = false, length = 1)
    public String getHasInner() {
        return this.hasInner;
    }

    public void setHasInner(String hasInner) {
        this.hasInner = hasInner;
    }

    @Column(name = "HAS_CASE", nullable = false, length = 1)
    public String getHasCase() {
        return this.hasCase;
    }

    public void setHasCase(String hasCase) {
        this.hasCase = hasCase;
    }

    @Column(name = "STOCK_KEEPING_FREQUENCY", nullable = false, length = 15)
    public String getStockKeepingFrequency() {
        return this.stockKeepingFrequency;
    }

    public void setStockKeepingFrequency(String stockKeepingFrequency) {
        this.stockKeepingFrequency = stockKeepingFrequency;
    }

    @Column(name = "PRODUCT_CODE", nullable = true, length = 30)
    public String getProductCode() {
        return this.productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    @Column(name = "TAX_CATEGORY_CODE", nullable = false, length = 30)
    public String getTaxCategoryCode() {
        return this.taxCategoryCode;
    }

    public void setTaxCategoryCode(String hsnCode) {
        this.taxCategoryCode = hsnCode;
    }



    @Column(name = "SHELF_LIFE_IN_DAYS", nullable = false)
    public int getShelfLifeInDays() {
        return this.shelfLifeInDays;
    }

    public void setShelfLifeInDays(int shelfLifeInDays) {
        this.shelfLifeInDays = shelfLifeInDays;
    }

    @Column(name = "PRODUCT_STATUS", nullable = false, length = 15)
    public String getProductStatus() {
        return this.productStatus;
    }

    public void setProductStatus(String productStatus) {
        this.productStatus = productStatus;
    }

    @Column(name = "UNIT_OF_MEASURE", nullable = false, length = 15)
    public String getUnitOfMeasure() {
        return this.unitOfMeasure;
    }

    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }

    /*@OneToMany(fetch = FetchType.LAZY, mappedBy = "productDefinitionData")
    public List<ProductFulfillmentTypeData> getFulfillmentType() {
        return this.fulfillmentType;
    }

    public void setFulfillmentType(List<ProductFulfillmentTypeData> fulfillmentType) {
        this.fulfillmentType = fulfillmentType;
    }*/

    @Column(name = "PARTICIPATES_IN_RECIPE", nullable = false, length = 1)
    public String getParticipatesInRecipe() {
        return participatesInRecipe;
    }

    public void setParticipatesInRecipe(String participatesInRecipe) {
        this.participatesInRecipe = participatesInRecipe;
    }

    @Column(name = "PARTICIPATES_IN_CAFE_RECIPE", nullable = false, length = 1)
    public String getParticipatesInCafeRecipe() {
        return participatesInCafeRecipe;
    }

    public void setParticipatesInCafeRecipe(String participatesInCafeRecipe) {
        this.participatesInCafeRecipe = participatesInCafeRecipe;
    }

    @Column(name = "ASSET_ORDERING", nullable = false, length = 1)
    public String getAssetOrdering() {
		return assetOrdering;
	}

	public void setAssetOrdering(String assetOrdering) {
		this.assetOrdering = assetOrdering;
	}

	@Column(name = "VARIANT_LEVEL_ORDERING", nullable = false, length = 1)
    public String getVariantLevelOrdering() {
        return variantLevelOrdering;
    }

    public void setVariantLevelOrdering(String variantLevelOrdering) {
        this.variantLevelOrdering = variantLevelOrdering;
    }

    @Column(name = "PRODUCT_IMAGE", nullable = true, length = 255)
    public String getProductImage() {
        return productImage;
    }

    public void setProductImage(String productImage) {
        this.productImage = productImage;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SUB_CATEGORY_ID", nullable = true)
    public SubCategoryDefinitionData getSubCategoryDefinition() {
        return subCategoryDefinition;
    }

    public void setSubCategoryDefinition(SubCategoryDefinitionData subCategoryDefinition) {
        this.subCategoryDefinition = subCategoryDefinition;
    }

    @Column(name = "SUPPORTS_SPECIALIZED_ORDERING", nullable = false, length = 1)
    public String getSupportsSpecializedOrdering() {
        return supportsSpecializedOrdering;
    }

    public void setSupportsSpecializedOrdering(String supportsSpecializedOrdering) {
        this.supportsSpecializedOrdering = supportsSpecializedOrdering;
    }

    @Column(name = "UNIT_PRICE", nullable = true)
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    @Column(name = "NEGOTIATED_UNIT_PRICE", nullable = true)
    public BigDecimal getNegotiatedUnitPrice() {
        return negotiatedUnitPrice;
    }

    public void setNegotiatedUnitPrice(BigDecimal negotiatedUnitPrice) {
        this.negotiatedUnitPrice = negotiatedUnitPrice;
    }

    @Column(name = "FULFILLMENT_TYPE", nullable = false)
    public String getFulfillmentType() {
        return fulfillmentType;
    }

    public void setFulfillmentType(String fulfillmentType) {
        this.fulfillmentType = fulfillmentType;
    }

    @Column(name = "DEFAULT_FULFILLMENT_TYPE", nullable = true)
    public String getDefaultFulfillmentType() {
        return defaultFulfillmentType;
    }

    public void setDefaultFulfillmentType(String defaultFulfillmentType) {
        this.defaultFulfillmentType = defaultFulfillmentType;
    }

    @Column(name = "AVAILABLE_AT_CAFE", nullable = false)
    public String getAvailableForCafe() {
        return availableForCafe;
    }

    public void setAvailableForCafe(String availableForCafe) {
        this.availableForCafe = availableForCafe;
    }

    @Column(name = "AVAILABLE_FOR_CAFE_INVENTORY", nullable = true)
    public String getAvailableForCafeInventory() {
        return availableForCafeInventory;
    }

    public void setAvailableForCafeInventory(String availableForCafeInventory) {
        this.availableForCafeInventory = availableForCafeInventory;
    }

    @Column(name = "VARIANCE_TYPE", nullable = true)
    public String getVarianceType() {
        return varianceType;
    }

    public void setVarianceType(String varianceType) {
        this.varianceType = varianceType;
    }

    @Column(name = "KITCHEN_VARIANCE_TYPE", nullable = true)
    public String getKitchenVarianceType() {
        return kitchenVarianceType;
    }

    public void setKitchenVarianceType(String kitchenVarianceType) {
        this.kitchenVarianceType = kitchenVarianceType;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "product")
    public List<DerivedMappingData> getDerivedMappingDataList() {
        return this.derivedMappingDataList;
    }

    public void setDerivedMappingDataList(List<DerivedMappingData> derivedMappingDataList) {
        this.derivedMappingDataList = derivedMappingDataList;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "linkedProduct")
    public Set<SkuDefinitionData> getSkuDefinitionDataList() {
        return skuDefinitionDataList;
    }

    public void setSkuDefinitionDataList(Set<SkuDefinitionData> skuDefinitionDataList) {
        this.skuDefinitionDataList = skuDefinitionDataList;
    }

    @Column(name = "AUTO_PRODUCTION", length = 1, nullable = false)
    public String getAutoProduction() {
        return autoProduction;
    }

    public void setAutoProduction(String autoProduction) {
        this.autoProduction = autoProduction;
    }

    @Column(name = "PARTICIPATES_IN_PNL", length = 1, nullable = false)
	public String getParticipatesInPnl() {
		return participatesInPnl;
	}

	public void setParticipatesInPnl(String participatesInPnl) {
		this.participatesInPnl = participatesInPnl;
	}

    @Column(name = "IS_BULK_GR_ALLOWED", nullable = false, length = 1)
    public String getIsBulkGRAllowed() {
        return isBulkGRAllowed;
    }

    public void setIsBulkGRAllowed(String isBulkGRAllowed) {
        this.isBulkGRAllowed = isBulkGRAllowed;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PROFILE_ID", nullable = true)
    public ProfileDefinitionData getProfileDefinitionData() {
        return profileDefinitionData;
    }

    public void setProfileDefinitionData(ProfileDefinitionData profileDefinitionData) {
        this.profileDefinitionData = profileDefinitionData;
    }

    @Column(name = "DIVISION_ID", nullable = true)
    public Integer getDivisionId() {
        return divisionId;
    }

    public void setDivisionId(Integer divisionId) {
        this.divisionId = divisionId;
    }



    @Column(name = "DEPARTMENT_ID", nullable = true)
    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }



    @Column(name = "CLASSIFICATION_ID", nullable = true)
    public Integer getClassificationId() {
        return classificationId;
    }

    public void setClassificationId(Integer classificationId) {
        this.classificationId = classificationId;
    }


    @Column(name = "SUB_CLASSIFICATION_ID", nullable = true)
    public Integer getSubClassificationId() {
        return subClassificationId;
    }

    public void setSubClassificationId(Integer subClassificationId) {
        this.subClassificationId = subClassificationId;
    }

    @Column(name = "RECIPE_REQUIRED", nullable = false, length = 1)
    public String getRecipeRequired() {
        return recipeRequired;
    }

    public void setRecipeRequired(String recipeRequired) {
        this.recipeRequired = recipeRequired;
    }

    @Column(name = "INTER_CAFE_TRANSFER", nullable = false)

    public String getInterCafeTransfer() {
        return interCafeTransfer;
    }

    public void setInterCafeTransfer(String interCafeTransfer) {
        this.interCafeTransfer = interCafeTransfer;
    }

    @Column(name = "PRODUCT_TYPE")
    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    @Column(name = "CATEGORY_LEVEL")
    public String getCategoryLevel() {
        return categoryLevel;
    }

    public void setCategoryLevel(String categoryLevel) {
        this.categoryLevel = categoryLevel;
    }

    @Column(name = "PRODUCT_CREATION_ID")
    public Integer getProductCreationId() {
        return productCreationId;
    }

    public void setProductCreationId(Integer productCreationId) {
        this.productCreationId = productCreationId;
    }

    @Column(name = "BRAND_ID")
    public Integer getBrandId() {
        return brandId;
    }
    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    @Column(name = "COMPANY_ID")
    public Integer getCompanyId() {
        return companyId;
    }
    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    @Column(name = "APPROVAL_DOCUMENT_ID")
    public Integer getApprovalDocumentId() {
        return approvalDocumentId;
    }
    public void setApprovalDocumentId(Integer approvalDocumentId) {
        this.approvalDocumentId = approvalDocumentId;
    }

    @OneToOne(fetch = FetchType.LAZY, mappedBy = "productDefinition")
    public UOMConversionMappingData getUomConversionMappingData() {
        return uomConversionMappingData;
    }

    public void setUomConversionMappingData(UOMConversionMappingData uomConversionMappingData) {
        this.uomConversionMappingData = uomConversionMappingData;
    }

}
package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.sql.Time;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Entity for KETTLE_UNIT_DETAIL_DATA_CLONE table
 */
@Setter
@Getter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "KETTLE_UNIT_DETAIL_DATA_CLONE")
public class KettleUnitDetailDataClone {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "UNIT_ID", nullable = false)
    private Integer unitId;

    @Column(name = "CAFE_OPENING")
    private Date cafeOpening;

    @Column(name = "CAFE_CLOSING")
    private Date cafeClosing;

    @Column(name = "CAFE_OPERATIONAL")
    private String cafeOperational;

    @Column(name = "UNIT_STATUS")
    private String unitStatus;

    @Column(name = "IS_LIVE")
    private String isLive;

    @Column(name = "IS_LIVE_INVENTORY_ENABLED")
    private String isLiveInventoryEnabled;

    @Column(name = "BRAND_ID")
    private Integer brandId;

    @Column(name = "INVENTORY_LEVEL")
    private String inventoryLevel;

    @Column(name = "TOTAL_PRODUCTS")
    private Integer totalProducts;

    @OneToMany(mappedBy = "unitDetailDataClone", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private Set<KettleProductDimensionCloneData> kettleProductDimensionCloneDataSet;


    public void addProductDimension(KettleProductDimensionCloneData pd) {
        if (kettleProductDimensionCloneDataSet == null) {
            kettleProductDimensionCloneDataSet = new HashSet<>();
        }
        kettleProductDimensionCloneDataSet.add(pd);
        pd.setUnitDetailDataClone(this);
    }

}

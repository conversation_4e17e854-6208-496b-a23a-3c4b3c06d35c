/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;
// Generated 4 May, 2016 12:06:56 AM by Hibernate Tools 3.2.2.GA

import static javax.persistence.GenerationType.IDENTITY;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * UnitSkuMapping generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "CAFE_SKU_MAPPING")
public class CafeSkuMapping implements java.io.Serializable {

    private Integer cafeSkuMappingId;
    private int skuId;
    private String mappingStatus;
    private Integer inventoryList;
    private Integer packagingId;

    public CafeSkuMapping() {
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "CAFE_SKU_MAPPING_ID", unique = true, nullable = false)
    public Integer getUnitSkuMappingId() {
        return this.cafeSkuMappingId;
    }

    public void setUnitSkuMappingId(Integer unitProductMappingId) {
        this.cafeSkuMappingId = unitProductMappingId;
    }


    @Column(name = "SKU_ID", nullable = false)
    public int getSkuId() {
        return this.skuId;
    }

    public void setSkuId(int productId) {
        this.skuId = productId;
    }

    @Column(name = "MAPPING_STATUS", nullable = false, length = 15)
    public String getMappingStatus() {
        return this.mappingStatus;
    }

    public void setMappingStatus(String mappingStatus) {
        this.mappingStatus = mappingStatus;
    }

    @Column(name = "INVENTORY_LIST_ID", nullable = false)
    public Integer getInventoryList() {
        return inventoryList;
    }

    public void setInventoryList(Integer inventoryList) {
        this.inventoryList = inventoryList;
    }


    @Column(name ="PACKAGING_ID")
    public Integer getPackagingId() {
        return packagingId;
    }

    public void setPackagingId(Integer packagingId) {
        this.packagingId = packagingId;
    }
}

package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "PR_PAYMENT_DETAIL")
public class PRPaymentDetailData {

    private Integer paymentDetailId;
    private Integer vendorId;
    private String vendorName;
    private String beneficiaryAccountNumber;
    private String beneficiaryIFSCode;
    private String debitAccountNumber;
    private String debitBank;
    private String paymentType;
    private BigDecimal proposedAmount;
    private BigDecimal paidAmount;
    private Date paymentDate;
    private Date actualDate;
    private String remarks;
    private Integer createdBy;
    private String utrNumber;
    private Set<PaymentRequestData> paymentRequests = new HashSet<>(0);
    private Date vendorPaymentDate;
    private String utrUploadedBy;
    private Date utrUploadedTime;
    private String isForcedUtrUpload;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PAYMENT_DETAIL_ID", nullable = false, unique = true)
    public Integer getPaymentDetailId() {
        return paymentDetailId;
    }

    public void setPaymentDetailId(Integer paymentDetailId) {
        this.paymentDetailId = paymentDetailId;
    }

    @Column(name = "VENDOR_ID", nullable = false)
    public Integer getVendorId() {
        return vendorId;
    }

    public void setVendorId(Integer vendorId) {
        this.vendorId = vendorId;
    }

    @Column(name = "VENDOR_NAME", nullable = false)
    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    @Column(name = "BENEFICIARY_IFSC_CODE", nullable = false)
    public String getBeneficiaryIFSCode() {
        return beneficiaryIFSCode;
    }

    public void setBeneficiaryIFSCode(String beneficiaryIFSCode) {
        this.beneficiaryIFSCode = beneficiaryIFSCode;
    }

    @Column(name = "BENEFICIARY_ACCOUNT_NUMBER", nullable = false)
    public String getBeneficiaryAccountNumber() {
        return beneficiaryAccountNumber;
    }

    public void setBeneficiaryAccountNumber(String beneficiaryAccountNumber) {
        this.beneficiaryAccountNumber = beneficiaryAccountNumber;
    }

    @Column(name = "DEBIT_ACCOUNT_NUMBER")
    public String getDebitAccountNumber() {
        return debitAccountNumber;
    }

    public void setDebitAccountNumber(String debitAccountNumber) {
        this.debitAccountNumber = debitAccountNumber;
    }

    @Column(name = "DEBIT_BANK_NAME")
    public String getDebitBank() {
        return debitBank;
    }

    public void setDebitBank(String debitBank) {
        this.debitBank = debitBank;
    }

    @Column(name = "PAYMENT_TYPE")
    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    @Column(name = "PAID_AMOUNT", nullable = false)
    public BigDecimal getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(BigDecimal paidAmount) {
        this.paidAmount = paidAmount;
    }

    @Column(name = "PAYMENT_DATE", nullable = false)
    public Date getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(Date paymentDate) {
        this.paymentDate = paymentDate;
    }

    @Column(name = "PAYMENT_REMARKS")
    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "prPaymentDetailData")
    public Set<PaymentRequestData> getPaymentRequests() {
        return paymentRequests;
    }

    public void setPaymentRequests(Set<PaymentRequestData> paymentRequests) {
        this.paymentRequests = paymentRequests;
    }

    @Column(name = "PROPOSED_AMOUNT", nullable = false)
    public BigDecimal getProposedAmount() {
        return proposedAmount;
    }

    public void setProposedAmount(BigDecimal proposedAmount) {
        this.proposedAmount = proposedAmount;
    }

    @Column(name = "CREATED_BY", nullable = false)
    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "UTR_NUMBER", nullable = false)
    public String getUtrNumber() {
        return utrNumber;
    }

    public void setUtrNumber(String utrNumber) {
        this.utrNumber = utrNumber;
    }

    @Column(name = "ACTUAL_DATE", nullable = true)
    public Date getActualDate() {
        return actualDate;
    }

    public void setActualDate(Date actualDate) {
        this.actualDate = actualDate;
    }

    @Column(name = "VENDOR_PAYMENT_DATE", nullable = true)
    public Date getVendorPaymentDate() {
        return vendorPaymentDate;
    }

    public void setVendorPaymentDate(Date vendorPaymentDate) {
        this.vendorPaymentDate = vendorPaymentDate;
    }

    @Column(name = "UTR_UPLOADED_BY", nullable = true)
    public String getUtrUploadedBy() {
        return utrUploadedBy;
    }

    public void setUtrUploadedBy(String utrUploadedBy) {
        this.utrUploadedBy = utrUploadedBy;
    }

    @Column(name = "UTR_UPLOADED_TIME", nullable = true)
    public Date getUtrUploadedTime() {
        return utrUploadedTime;
    }

    public void setUtrUploadedTime(Date utrUploadedTime) {
        this.utrUploadedTime = utrUploadedTime;
    }

    @Column(name = "IS_FORCED_UTR_UPLOAD", nullable = true)
    public String getIsForcedUtrUpload() {
        return isForcedUtrUpload;
    }

    public void setIsForcedUtrUpload(String isForcedUtrUpload) {
        this.isForcedUtrUpload = isForcedUtrUpload;
    }
}

package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "MONK_DAY_CLOSE_EVENT_STATUS")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MonkDayCloseEventStatusData {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MONK_DAY_CLOSE_EVENT_STATUS_ID")
    private Long monkDayCloseEventStatusId;
    
    @Column(name = "UNIT_ID", nullable = false)
    private Integer unitId;
    
    @Column(name = "BUSINESS_DATE", nullable = false)
    @Temporal(TemporalType.DATE)
    private Date businessDate;
    
    @Column(name = "EVENT_TYPE", nullable = false, length = 50)
    private String eventType;
    
    @Column(name = "EVENT_STATUS", nullable = false, length = 50)
    private String eventStatus;
    
    @Column(name = "KETTLE_DAY_CLOSE")
    private Integer kettleDayCloseId;
    
    @Column(name = "SUMO_DAY_CLOSE")
    private Integer sumoDayCloseId;
    
    @Column(name = "UPDATED_AT")
    private Timestamp updatedAt;
    
    @Column(name = "CREATED_AT")
    private Timestamp createdAt;
    
    @OneToMany(mappedBy = "eventStatus", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<MonkStatusDayCloseData> monkStatuses;
    
    // Add foreign key relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "KETTLE_DAY_CLOSE", referencedColumnName = "EVENT_ID", insertable = false, updatable = false)
    private SCMDayCloseEventData kettleDayCloseEvent;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SUMO_DAY_CLOSE", referencedColumnName = "EVENT_ID", insertable = false, updatable = false)
    private SCMDayCloseEventData sumoDayCloseEvent;
    
    @PrePersist
    protected void onCreate() {
        createdAt = new Timestamp(System.currentTimeMillis());
        updatedAt = new Timestamp(System.currentTimeMillis());
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = new Timestamp(System.currentTimeMillis());
    }
}

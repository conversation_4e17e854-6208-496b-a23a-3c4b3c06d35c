package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.Gatepass;
import com.stpl.tech.scm.domain.model.GatepassStatus;

public class EmailTemplateBuilder {

    public static String getGatePassCreationEmailBody(Gatepass gatePass) {
        StringBuilder sb = new StringBuilder();

        sb.append("<!DOCTYPE html>")
                .append("<html>")
                .append("<head>")
                .append("<meta charset=\"UTF-8\">")
                .append("<style>")
                .append("body { font-family: Arial, sans-serif; color: #333333; line-height: 1.5; }")
                .append(".container { border: 1px solid #e0e0e0; border-radius: 6px; padding: 20px; max-width: 600px; margin: auto; background-color: #fafafa; }")
                .append("h2 { color: #2c3e50; margin-bottom: 10px; }")
                .append(".info-table { width: 100%; border-collapse: collapse; }")
                .append(".info-table th, .info-table td { text-align: left; padding: 8px 12px; border-bottom: 1px solid #ddd; }")
                .append(".info-table th { background-color: #f4f6f8; font-weight: bold; }")
                .append(".footer { margin-top: 20px; font-size: 13px; color: #777777; }")
                .append("</style>")
                .append("</head>")
                .append("<body>")
                .append("<div class=\"container\">")
                .append("<h2> New GatePass Created</h2>")
                .append("<p>A New GatePass has been created. Please review the details below:</p>")
                .append("<table class=\"info-table\">");

        sb.append("<tr><th>GatePass ID</th><td>").append(gatePass.getId()).append("</td></tr>")
                .append("<tr><th>Vendor</th><td>").append(gatePass.getVendor() != null ? gatePass.getVendor().getName() : "-").append("</td></tr>")
                .append("<tr><th>Operation Type</th><td>").append(gatePass.getOperationType().name()).append("</td></tr>")
                .append("<tr><th>Returnable</th><td>").append(gatePass.getReturnable() != null && gatePass.getReturnable() ? "Yes" : "No").append("</td></tr>")
                .append("<tr><th>Expected Return (days)</th><td>").append(gatePass.getExpectedReturn() != null ? gatePass.getExpectedReturn() : "-").append("</td></tr>")
                .append("<tr><th>Status</th><td>").append(gatePass.getStatus()).append("</td></tr>")
                .append("<tr><th>Additional Charges</th><td>").append(gatePass.getAdditionalCharges()).append("</td></tr>")
                .append("<tr><th>Created By</th><td>").append(gatePass.getCreatedBy() != null ? gatePass.getCreatedBy().getName() : "-").append("</td></tr>")
                .append("<tr><th>Created At</th><td>").append(gatePass.getCreatedAt()).append("</td></tr>")
                .append("<tr><th>Sending Unit</th><td>").append(gatePass.getSendingUnit() != null ? gatePass.getSendingUnit().getName() : "-").append("</td></tr>")
                .append("<tr><th>Issue Date</th><td>").append(gatePass.getIssueDate()).append("</td></tr>")
                .append("<tr><th>Has Loss</th><td>").append(gatePass.getHasLoss() != null && gatePass.getHasLoss() ? "Yes" : "No").append("</td></tr>")
                .append("<tr><th>Asset GatePass</th><td>").append(gatePass.isAssetOrder() ? "Yes" : "No").append("</td></tr>");

        sb.append("</table>")
                .append("<p class=\"footer\">This is an automated notification. Please do not reply directly to this email.</p>")
                .append("</div>")
                .append("</body>")
                .append("</html>");

        return sb.toString();
    }

    public static String getGatePassDecisionEmailBody(Gatepass gatePass, String comment, boolean approved) {

        // Choose colors and heading dynamically
        String bgColor = approved ? "#e6fae6" : "#fce3e3";
        String headerColor = approved ? "#6bf2a4" : "#f5887d";
        String headerText = approved ? "GatePass Approved" : "GatePass Rejected";
        String tableHeaderColor = approved ? "#ecf9f1" : "#fdeaea";

        StringBuilder sb = new StringBuilder();

        sb.append("<!DOCTYPE html>")
                .append("<html>")
                .append("<head>")
                .append("<meta charset=\"UTF-8\">")
                .append("<style>")
                .append("body { font-family: Arial, sans-serif; color: #333333; line-height: 1.5; }")
                .append(".container { border: 1px solid #e0e0e0; border-radius: 6px; padding: 20px; max-width: 600px; margin: auto; background-color: ")
                .append(bgColor).append("; }")
                .append("h2 { color: ").append(headerColor).append("; margin-bottom: 10px; }")
                .append(".info-table { width: 100%; border-collapse: collapse; }")
                .append(".info-table th, .info-table td { text-align: left; padding: 8px 12px; border-bottom: 1px solid #ddd; }")
                .append(".info-table th { background-color: ").append(tableHeaderColor).append("; font-weight: bold; }")
                .append(".footer { margin-top: 20px; font-size: 13px; color: #777777; }")
                .append("</style>")
                .append("</head>")
                .append("<body>")
                .append("<div class=\"container\">")
                .append("<h2>").append(headerText).append("</h2>")
                .append("<p>The following GatePass has been ").append(approved ? "approved" : "rejected").append(":</p>")
                .append("<table class=\"info-table\">");

        sb.append("<tr><th>GatePass ID</th><td>").append(gatePass.getId()).append("</td></tr>")
                .append("<tr><th>Vendor</th><td>").append(gatePass.getVendor() != null ? gatePass.getVendor().getName() : "-").append("</td></tr>")
                .append("<tr><th>").append(approved ? "Approved By" : "Reviewed By").append("</th><td>")
                .append(gatePass.getApprovalRequestedTo() != null ? gatePass.getApprovalRequestedTo().getName() : "-")
                .append("</td></tr>")
                .append("<tr><th>").append(approved ? "Approved At" : "Reviewed At").append("</th><td>")
                .append(SCMUtil.getCurrentTimeISTString()).append("</td></tr>")
                .append("<tr><th>").append(approved ? "Approve Comment" : "Reviewed Comment").append("</th><td>")
                .append(comment).append("</td></tr>")
                .append("<tr><th>Operation Type</th><td>").append(gatePass.getOperationType().name()).append("</td></tr>")
                .append("<tr><th>Returnable</th><td>").append(gatePass.getReturnable() != null && gatePass.getReturnable() ? "Yes" : "No").append("</td></tr>")
                .append("<tr><th>Expected Return (days)</th><td>").append(gatePass.getExpectedReturn() != null ? gatePass.getExpectedReturn() : "-").append("</td></tr>")
                .append("<tr><th>Status</th><td>").append(gatePass.getStatus()).append("</td></tr>")
                .append("<tr><th>Additional Charges</th><td>").append(gatePass.getAdditionalCharges()).append("</td></tr>")
                .append("<tr><th>Created By</th><td>").append(gatePass.getCreatedBy() != null ? gatePass.getCreatedBy().getName() : "-").append("</td></tr>")
                .append("<tr><th>Created At</th><td>").append(gatePass.getCreatedAt()).append("</td></tr>")
                .append("<tr><th>Sending Unit</th><td>").append(gatePass.getSendingUnit() != null ? gatePass.getSendingUnit().getName() : "-").append("</td></tr>")
                .append("<tr><th>Issue Date</th><td>").append(gatePass.getIssueDate()).append("</td></tr>")
                .append("<tr><th>Has Loss</th><td>").append(gatePass.getHasLoss() != null && gatePass.getHasLoss() ? "Yes" : "No").append("</td></tr>")
                .append("<tr><th>Asset GatePass</th><td>").append(gatePass.isAssetOrder() ? "Yes" : "No").append("</td></tr>");

        sb.append("</table>")
                .append("<p class=\"footer\">This is an automated notification. Please do not reply directly to this email.</p>")
                .append("</div>")
                .append("</body>")
                .append("</html>");

        return sb.toString();
    }



}

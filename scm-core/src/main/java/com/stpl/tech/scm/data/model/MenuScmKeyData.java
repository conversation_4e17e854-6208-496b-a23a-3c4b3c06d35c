package com.stpl.tech.scm.data.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "MENU_SCM_KEY_DATA")
public class MenuScmKeyData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "KEY_ID", unique = true, nullable = false)
    private Integer keyId;
    @Column(name = "REFERENCE_ORDER_KEY_ITEM_ID")
    private Integer referenceOrderKeyItemId;
    @Column(name = "REFERENCE_ORDER_KEY_ITEM_TYPE")
    private String referenceOrderKeyItemType;
    @Column(name = "DATE")
    private Date date;
    @Column(name = "DATE_TYPE")
    private String dateType;
    @Column(name = "QUANTITY")
    private BigDecimal quantity;
    @Column(name = "SUGGESTED_QUANTITY")
    private BigDecimal suggestedQuantity;
    @Column(name = "ORDERING_PERCENTAGE")
    private BigDecimal orderingPercentage;

    @Override
    public int hashCode(){
    	return keyId.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        MenuScmKeyData that = (MenuScmKeyData) obj;
        return keyId.equals(that.keyId);
    }

}

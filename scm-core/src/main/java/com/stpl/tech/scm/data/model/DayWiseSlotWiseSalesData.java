package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "DAY_WISE_SLOT_WISE_SALES_DATA", schema = "KETTLE_SCM_DUMP")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DayWiseSlotWiseSalesData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CAFE_OPENING_TIME")
    private Date cafeOpeningTime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CAFE_CLOSING_TIME")
    private Date cafeClosingTime;

    @Column(name = "TOTAL_CAFE_OPENING_TIME_IN_MINUTES_FOR_THAT_DAY")
    private Integer totalCafeOpeningTimeInMinutesForThatDay;

    @Column(name = "PRODUCT_NAME", nullable = false)
    private String productName;

    @Column(name = "PRODUCT_TYPE", nullable = false)
    private Integer productType;

    @Column(name = "INVENTORY_TRACK_LEVEL", length = 50)
    private String inventoryTrackLevel;

    @Temporal(TemporalType.DATE)
    @Column(name = "BUSINESS_DATE", nullable = false)
    private Date businessDate;

    @Column(name = "IS_OPERATIONAL", nullable = false)
    private String isOperational;

    @Column(name = "UNIT_ID", nullable = false)
    private Integer unitId;

    @Column(name = "BRAND_ID")
    private Integer brandId;

    @Column(name = "PRODUCT_ID", nullable = false)
    private Integer productId;

    @Column(name = "DIMENSION")
    private String dimension;

    @Column(name = "TOTAL_SALE_AMOUNT", nullable = false, precision = 32, scale = 2)
    private BigDecimal totalSaleAmount;

    @Column(name = "PRICE", nullable = false, precision = 10, scale = 2)
    private BigDecimal price;

    @Column(name = "TOTAL_STOCK_OUT_IN_MINUTES_FOR_THAT_DAY", nullable = false, precision = 42, scale = 0)
    private BigDecimal totalStockOutInMinutesForThatDay;

    @Column(name = "PRODUCT_IN_STOCK_TIME", nullable = false, precision = 43, scale = 0)
    private BigDecimal productInStockTime;

    @Column(name = "ORDER_LEVEL_INCIDENCE", precision = 36, scale = 4)
    private BigDecimal orderLevelIncidence;

    @Column(name = "PRODUCT_ITEM_LEVEL_INCIDENCE", precision = 36, scale = 4)
    private BigDecimal productItemLevelIncidence;

    @Column(name = "TOTAL_QUANTITY_SOLD", nullable = false, precision = 32, scale = 0)
    private BigDecimal totalQuantitySold;

    @Column(name = "TOTAL_QUANTITY_SOLD_DINE_IN", nullable = false, precision = 32, scale = 0)
    private BigDecimal totalQuantitySoldDineIn;

    @Column(name = "TOTAL_QUANTITY_SOLD_DELIVERY", nullable = false, precision = 32, scale = 0)
    private BigDecimal totalQuantitySoldDelivery;

    @Column(name = "TOTAL_ORDERS", nullable = false)
    private Integer totalOrders;

    @Column(name = "TOTAL_ITEMS_SOLD", precision = 54, scale = 0)
    private BigDecimal totalItemsSold;

    @Column(name = "TOTAL_DINE_IN_ITEMS_SOLD", precision = 54, scale = 0)
    private BigDecimal totalDineInItemsSold;

    @Column(name = "TOTAL_DELIVERY_IN_ITEMS_SOLD", precision = 54, scale = 0)
    private BigDecimal totalDeliveryInItemsSold;

    @Column(name = "NEW_CUSTOMER_INTEREST", precision = 36, scale = 4)
    private BigDecimal newCustomerInterest;

    @Column(name = "OLD_CUSTOMER_INTEREST", precision = 36, scale = 4)
    private BigDecimal oldCustomerInterest;

    @Column(name = "COUNT_OF_OLD_CUSTOMERS")
    private Integer countOfOldCustomers;

    @Column(name = "COUNT_OF_NEW_CUSTOMERS")
    private Integer countOfNewCustomers;

    @Column(name = "BREAKFAST_SALES", columnDefinition = "int DEFAULT '0' COMMENT 'Quantity sold during breakfast slot (5-11 AM)'")
    private Integer breakfastSales;

    @Column(name = "LUNCH_SALES", columnDefinition = "int DEFAULT '0' COMMENT 'Quantity sold during lunch slot (12-14 PM)'")
    private Integer lunchSales;

    @Column(name = "EVENING_SALES", columnDefinition = "int DEFAULT '0' COMMENT 'Quantity sold during evening slot (15-19 PM)'")
    private Integer eveningSales;

    @Column(name = "DINNER_SALES", columnDefinition = "int DEFAULT '0' COMMENT 'Quantity sold during dinner slot (20-21 PM)'")
    private Integer dinnerSales;

    @Column(name = "POST_DINNER_SALES", columnDefinition = "int DEFAULT '0' COMMENT 'Quantity sold during post-dinner slot (22-23 PM)'")
    private Integer postDinnerSales;

    @Column(name = "OVERNIGHT_SALES", columnDefinition = "int DEFAULT '0' COMMENT 'Quantity sold during overnight slot (0-4 AM)'")
    private Integer overnightSales;

    @Column(name = "BREAKFAST_SALE_AMOUNT", precision = 10, scale = 2, columnDefinition = "decimal(10,2) DEFAULT '0.00' COMMENT 'Revenue from breakfast slot'")
    private BigDecimal breakfastSaleAmount;

    @Column(name = "LUNCH_SALE_AMOUNT", precision = 10, scale = 2, columnDefinition = "decimal(10,2) DEFAULT '0.00' COMMENT 'Revenue from lunch slot'")
    private BigDecimal lunchSaleAmount;

    @Column(name = "EVENING_SALE_AMOUNT", precision = 10, scale = 2, columnDefinition = "decimal(10,2) DEFAULT '0.00' COMMENT 'Revenue from evening slot'")
    private BigDecimal eveningSaleAmount;

    @Column(name = "DINNER_SALE_AMOUNT", precision = 10, scale = 2, columnDefinition = "decimal(10,2) DEFAULT '0.00' COMMENT 'Revenue from dinner slot'")
    private BigDecimal dinnerSaleAmount;

    @Column(name = "POST_DINNER_SALE_AMOUNT", precision = 10, scale = 2, columnDefinition = "decimal(10,2) DEFAULT '0.00' COMMENT 'Revenue from post dinner slot'")
    private BigDecimal postDinnerSaleAmount;

    @Column(name = "OVERNIGHT_SALE_AMOUNT", precision = 10, scale = 2, columnDefinition = "decimal(10,2) DEFAULT '0.00' COMMENT 'Revenue from overnight slot'")
    private BigDecimal overnightSaleAmount;

    @Column(name = "BREAKFAST_ORDERS")
    private Integer breakfastOrders;

    @Column(name = "LUNCH_ORDERS")
    private Integer lunchOrders;

    @Column(name = "EVENING_ORDERS")
    private Integer eveningOrders;

    @Column(name = "DINNER_ORDERS")
    private Integer dinnerOrders;

    @Column(name = "POST_DINNER_ORDERS")
    private Integer postDinnerOrders;

    @Column(name = "OVERNIGHT_ORDERS")
    private Integer overnightOrders;

    @Column(name = "BREAKFAST_STOCK_TIME", columnDefinition = "int DEFAULT '0' COMMENT 'Minutes product was in stock during breakfast slot (5-11 AM)'")
    private Integer breakfastStockTime;

    @Column(name = "LUNCH_STOCK_TIME", columnDefinition = "int DEFAULT '0' COMMENT 'Minutes product was in stock during lunch slot (12-14 PM)'")
    private Integer lunchStockTime;

    @Column(name = "EVENING_STOCK_TIME", columnDefinition = "int DEFAULT '0' COMMENT 'Minutes product was in stock during evening slot (15-19 PM)'")
    private Integer eveningStockTime;

    @Column(name = "DINNER_STOCK_TIME", columnDefinition = "int DEFAULT '0' COMMENT 'Minutes product was in stock during dinner slot (20-21 PM)'")
    private Integer dinnerStockTime;

    @Column(name = "POST_DINNER_STOCK_TIME", columnDefinition = "int DEFAULT '0' COMMENT 'Minutes product was in stock during post-dinner slot (22-23 PM)'")
    private Integer postDinnerStockTime;

    @Column(name = "OVERNIGHT_STOCK_TIME", columnDefinition = "int DEFAULT '0' COMMENT 'Minutes product was in stock during overnight slot (0-4 AM)'")
    private Integer overnightStockTime;

    @Column(name = "BREAKFAST_STOCK_OUT_MINUTES", columnDefinition = "int DEFAULT '0' COMMENT 'Minutes product was out of stock during breakfast slot'")
    private Integer breakfastStockOutMinutes;

    @Column(name = "LUNCH_STOCK_OUT_MINUTES", columnDefinition = "int DEFAULT '0' COMMENT 'Minutes product was out of stock during lunch slot'")
    private Integer lunchStockOutMinutes;

    @Column(name = "EVENING_STOCK_OUT_MINUTES", columnDefinition = "int DEFAULT '0' COMMENT 'Minutes product was out of stock during evening slot'")
    private Integer eveningStockOutMinutes;

    @Column(name = "DINNER_STOCK_OUT_MINUTES", columnDefinition = "int DEFAULT '0' COMMENT 'Minutes product was out of stock during dinner slot'")
    private Integer dinnerStockOutMinutes;

    @Column(name = "POST_DINNER_STOCK_OUT_MINUTES", columnDefinition = "int DEFAULT '0' COMMENT 'Minutes product was out of stock during post-dinner slot'")
    private Integer postDinnerStockOutMinutes;

    @Column(name = "OVERNIGHT_STOCK_OUT_MINUTES", columnDefinition = "int DEFAULT '0' COMMENT 'Minutes product was out of stock during overnight slot'")
    private Integer overnightStockOutMinutes;

    @Column(name = "BREAKFAST_TIME_IN_MINUTES", columnDefinition = "int DEFAULT '0' COMMENT 'Actual breakfast slot duration in minutes (considering cafe hours)'")
    private Integer breakfastTimeInMinutes;

    @Column(name = "LUNCH_TIME_IN_MINUTES", columnDefinition = "int DEFAULT '0' COMMENT 'Actual lunch slot duration in minutes (considering cafe hours)'")
    private Integer lunchTimeInMinutes;

    @Column(name = "EVENING_TIME_IN_MINUTES", columnDefinition = "int DEFAULT '0' COMMENT 'Actual evening slot duration in minutes (considering cafe hours)'")
    private Integer eveningTimeInMinutes;

    @Column(name = "DINNER_TIME_IN_MINUTES", columnDefinition = "int DEFAULT '0' COMMENT 'Actual dinner slot duration in minutes (considering cafe hours)'")
    private Integer dinnerTimeInMinutes;

    @Column(name = "POST_DINNER_TIME_IN_MINUTES", columnDefinition = "int DEFAULT '0' COMMENT 'Actual post-dinner slot duration in minutes (considering cafe hours)'")
    private Integer postDinnerTimeInMinutes;

    @Column(name = "OVERNIGHT_TIME_IN_MINUTES", columnDefinition = "int DEFAULT '0' COMMENT 'Actual overnight slot duration in minutes (considering cafe hours)'")
    private Integer overnightTimeInMinutes;

    @Column(name = "DAY_NAME", length = 10)
    private String dayName;
}

package com.stpl.tech.scm.data.model;


import javax.persistence.*;
import java.math.BigDecimal;


@Entity
@Table(name = "FA_TRANSFER_DATA")
public class FaTransferData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "FA_REQUEST_ID")
    private Integer faRequestId ;
    @Column(name = "EVENT_ID")
    private Integer eventId ;
    @Column(name = "PRODUCT_ID")
    private Integer productId ;
    @Column(name = "SCANNED_QTY")
    private BigDecimal scannedQty ;
    @Column(name = "TRANSFERRED_QTY")
    private BigDecimal transferredQty ;


    public Integer getFaRequestId() {
        return faRequestId;
    }

    public void setFaRequestId(Integer faRequestId) {
        this.faRequestId = faRequestId;
    }

    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public BigDecimal getScannedQty() {
        return scannedQty;
    }

    public void setScannedQty(BigDecimal scannedQty) {
        this.scannedQty = scannedQty;
    }

    public BigDecimal getTransferredQty() {
        return transferredQty;
    }

    public void setTransferredQty(BigDecimal transferredQty) {
        this.transferredQty = transferredQty;
    }

}
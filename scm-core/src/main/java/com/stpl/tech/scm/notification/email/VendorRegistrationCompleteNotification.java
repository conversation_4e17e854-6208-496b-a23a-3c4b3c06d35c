package com.stpl.tech.scm.notification.email;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.notification.email.template.VendorRegistrationCompleteNotificationTemplate;
import com.stpl.tech.scm.notification.email.template.VendorRegistrationRequestNotificationTemplate;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class VendorRegistrationCompleteNotification extends EmailNotification {

    private VendorRegistrationCompleteNotificationTemplate template;
    private EnvType envType;
    private List<String> emails;
    private Boolean isOld;

    public VendorRegistrationCompleteNotification() {
    }

    public VendorRegistrationCompleteNotification(VendorRegistrationCompleteNotificationTemplate template,
                                                 EnvType envType, List<String> emails, Boolean isOld
//                                                  ,boolean isNew
    ) {
        this.template = template;
        this.envType = envType;
        this.emails = emails;
        this.isOld = isOld;
//        this.isNew = isNew;
    }

    @Override
    public String[] getToEmails() {
        List<String> devEmails = new ArrayList<>();
        devEmails.add("<EMAIL>");
        if (!AppUtils.isProd(envType)) {
            return devEmails.toArray(new String[devEmails.size()]);
        }
        return emails.toArray(new String[emails.size()]);
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        Map<String, Object> dataMap = new HashMap<>();
        dataMap = template.getData();
        VendorDetail vendorDetail = (VendorDetail) dataMap.get("vendor");
        String subject = "";

        if(isOld){
            subject = vendorDetail.getEntityName() + " (" + vendorDetail.getVendorId() + ")"
                    + " [" + vendorDetail.getType() + "]"+" [EDIT] "+"is approved by " + vendorDetail.getRequestedBy().getName() ;
        }else{
            subject = vendorDetail.getEntityName() + " (" + vendorDetail.getVendorId() + ")"
                    + " [" + vendorDetail.getType() + "]"+" [NEW] "+"is approved by " + vendorDetail.getRequestedBy().getName() ;
        }
        if (AppUtils.isDev(envType)) {
            subject = "[Dev] " + subject;
        }
        return subject;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return template.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}

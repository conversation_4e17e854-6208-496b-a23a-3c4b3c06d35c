package com.stpl.tech.scm.data.dao.impl;

import java.util.List;

import javax.persistence.NoResultException;
import javax.persistence.Query;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import com.stpl.tech.scm.data.dao.VehicleMasterDao;
import com.stpl.tech.scm.data.model.GoodsReceivedData;
import com.stpl.tech.scm.data.model.VehicleData;
import com.stpl.tech.scm.domain.model.Vehicle;

@Repository
public class VehicleMasterDaoImpl extends SCMAbstractDaoImpl implements VehicleMasterDao {

	private static final Logger LOG = LoggerFactory.getLogger(TransportManagementDaoImpl.class);

	@SuppressWarnings("unchecked")
	@Override
	public List<Vehicle> getVehicleData() {
		String queryString = "FROM VehicleData g";
		Query query = manager.createQuery(queryString);
		return query.getResultList();
	}

	@Override
	public void addVehicleData(VehicleData vehicleData) {
		manager.persist(vehicleData);
		manager.flush();
		
	}

	@Override
	public VehicleData getSingleVehicleData(Integer vehicleId) {
		try {
			Query query = manager
					.createQuery("FROM VehicleData g WHERE g.vehicleId = :vehicleId");
			query.setParameter("vehicleId", vehicleId);
			return (VehicleData) query.getSingleResult();
		} catch (NoResultException e) {
			LOG.info("Vehicle for id {} not found!", vehicleId);
			return null;
		}
	}

	@Override
	public void updateVehicleData(VehicleData vehicleData) {
		manager.merge(vehicleData);
		manager.flush();
		
	}

}

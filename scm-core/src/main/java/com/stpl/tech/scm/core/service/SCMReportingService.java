/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.core.service;

import com.stpl.tech.scm.data.model.SCMDayCloseEventData;
import com.stpl.tech.scm.data.model.SCMProductInventoryData;

import java.util.Date;
import java.util.Map;

public interface SCMReportingService {

	public boolean sendVarianceReport(Date businessDate, Integer unitId, boolean isFixedAssets, Map<Integer, SCMProductInventoryData> dailyInventoryData);

	public boolean sendWhVarianceReport(SCMDayCloseEventData eventData);

	public boolean sendStockTakeReport(Date businessDate, Integer unitId, Integer eventId, String eventSubtype, String initiatorEmail,String auditorEmail ,Boolean submit);

}

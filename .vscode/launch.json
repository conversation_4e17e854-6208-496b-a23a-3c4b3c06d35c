{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "java",
            "name": "Current File",
            "request": "launch",
            "mainClass": "${file}"
        },
        {
            "type": "java",
            "name": "SCMCategoryFieldEnum",
            "request": "launch",
            "mainClass": "com.stpl.tech.scm.core.SCMCategoryFieldEnum",
            "projectName": "scm-core"
        },
        {
            "type": "java",
            "name": "RequestOrderManagementServiceImpl",
            "request": "launch",
            "mainClass": "com.stpl.tech.scm.core.service.impl.RequestOrderManagementServiceImpl",
            "projectName": "scm-core"
        },
        {
            "type": "java",
            "name": "TransportManagementServiceImpl",
            "request": "launch",
            "mainClass": "com.stpl.tech.scm.core.service.impl.TransportManagementServiceImpl",
            "projectName": "scm-core"
        },
        {
            "type": "java",
            "name": "SCMUtil",
            "request": "launch",
            "mainClass": "com.stpl.tech.scm.core.util.SCMUtil",
            "projectName": "scm-core"
        },
        {
            "type": "java",
            "name": "CafePriceManagementDaoImp",
            "request": "launch",
            "mainClass": "com.stpl.tech.scm.data.dao.impl.CafePriceManagementDaoImp",
            "projectName": "scm-core"
        },
        {
            "type": "java",
            "name": "GoodsReceiveManagementDaoImpl",
            "request": "launch",
            "mainClass": "com.stpl.tech.scm.data.dao.impl.GoodsReceiveManagementDaoImpl",
            "projectName": "scm-core"
        },
        {
            "type": "java",
            "name": "WHPriceManagementDaoImpl",
            "request": "launch",
            "mainClass": "com.stpl.tech.scm.data.dao.impl.WHPriceManagementDaoImpl",
            "projectName": "scm-core"
        },
        {
            "type": "java",
            "name": "ScmServiceConfig",
            "request": "launch",
            "mainClass": "com.stpl.tech.scm.service.config.ScmServiceConfig",
            "projectName": "scm-service"
        }
    ]
}
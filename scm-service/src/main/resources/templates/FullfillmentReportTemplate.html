<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
    <title>Fulfillment Report</title>
    <style>
        /* Global Styles */
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }

        .container {
            padding: 0 30px 20px 30px;
        }

        .section {
            padding: 15px 5px;
            margin-bottom: 20px;
        }

        /* Table Styles */
        .data-table {
            border: 1px solid black;
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 20px;
        }

        .data-table th,
        .data-table td {
            border: 1px solid black;
            border-collapse: collapse;
            padding: 5px;
            text-align: left;
        }

        .data-table th {
            background-color: #e3e3e3;
            font-weight: bold;
        }

        /* Header Styles */
        h2 {
            color: #333;
            border-bottom: 2px solid #ddd;
            padding-bottom: 5px;
            margin-top: 30px;
            margin-bottom: 15px;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .container {
                padding: 0 10px 20px 10px;
            }

            .data-table {
                font-size: 12px;
            }
        }
    </style>
</head>

<body>
<div class="container">

    <!-- Velocity Macros -->
    #macro(formatPercentage $value)
        #if($value == -1.0)
            N/A
        #else
            ${data.mathTool.roundTo(2,$value)}%
        #end
    #end

    <!-- Daily Fulfillment Report -->
    <div class="section">
        <h2>Fulfillment Percentage - Kitchen and Warehouse ($data.prevDay)</h2>
        <table class="data-table">
            <tr>
                <th>Transferring Unit</th>
                <th>Total Fulfillment %</th>
                <th>Critical Products %</th>
                <th>Negative Impact %</th>
                <th>Bakery Fulfillment %</th>
                <th>Level 1 Products %</th>
                <th>Level 2 Products %</th>
                <th>Level 3 Products %</th>
                <th>Level 4 Products %</th>
                <th>Level 5 Products %</th>
                <th>Level N/A Products %</th>
            </tr>
            #foreach($item in $data.ffDetailLastDay)
            <tr>
                <td>$item.transferringUnit</td>
                <td>${data.mathTool.roundTo(2,$item.avgFPer)}%</td>
                <td>#formatPercentage($item.criticalProductFF)</td>
                <td>${data.mathTool.roundTo(2,$data.mathTool.sub($item.avgFPer,$item.avgImFPer))}%</td>
                <td>#formatPercentage($item.bakeryFP)</td>
                <td>#formatPercentage($item.productLevel1FFPer)</td>
                <td>#formatPercentage($item.productLevel2FFPer)</td>
                <td>#formatPercentage($item.productLevel3FFPer)</td>
                <td>#formatPercentage($item.productLevel4FFPer)</td>
                <td>#formatPercentage($item.productLevel5FFPer)</td>
                <td>#formatPercentage($item.productLevelNAFFPer)</td>
            </tr>
            #end
        </table>
    </div>

    <!-- Daily On-Time and On-Date and delayed Report -->
    <div class="section">
        <h2>Percentage of On-Time, On-Date, and Delayed RO's Raised – Kitchen & Warehouse ($data.prevDay)</h2>
        <table class="data-table">
            <tr>
                <th>Transferring Unit</th>
                <th>Total On-Time %</th>
                <th>Total On-Date %</th>
                <th>Total Delayed %</th>
                <th>Bakery On-Time %</th>
                <th>Bakery On-Date %</th>
                <th>Bakery Delayed %</th>
                <th>Critical On-Time %</th>
                <th>Critical On-Date %</th>
                <th>Critical Delayed %</th>
                <th>Level 1 On-Time %</th>
                <th>Level 1 On-Date %</th>
                <th>Level 1 Delayed %</th>
                <th>Level 2 On-Time %</th>
                <th>Level 2 On-Date %</th>
                <th>Level 2 Delayed %</th>
                <th>Level 3 On-Time %</th>
                <th>Level 3 On-Date %</th>
                <th>Level 3 Delayed %</th>
                <th>Level 4 On-Time %</th>
                <th>Level 4 On-Date %</th>
                <th>Level 4 Delayed %</th>
                <th>Level 5 On-Time %</th>
                <th>Level 5 On-Date %</th>
                <th>Level 5 Delayed %</th>
                <th>Level N/A On-Time %</th>
                <th>Level N/A On-Date %</th>
                <th>Level N/A Delayed %</th>
            </tr>
            #foreach($item in $data.ffDetailLastDay)
            <tr>
                <td>$item.transferringUnit</td>
                <td>${data.mathTool.roundTo(2,$item.avgOnTimeRoRaisedPer)}%</td>
                <td>${data.mathTool.roundTo(2,$item.avgOnDateRoRaisedPer)}%</td>
                <td>${data.mathTool.roundTo(2,$item.avgDelayRoRaisedPer)}%</td>
                <td>#formatPercentage($item.bakeryOnTimeRoRaisedPer)</td>
                <td>#formatPercentage($item.bakeryOnDateRoRaisedPer)</td>
                <td>#formatPercentage($item.bakeryRoRaisedDelayPer)</td>
                <td>#formatPercentage($item.criticalOnTimeRaisedFF)</td>
                <td>#formatPercentage($item.criticalOnDateRaisedFF)</td>
                <td>#formatPercentage($item.criticalDelayRaisedFF)</td>
                <td>#formatPercentage($item.productLevel1RaisedOnTimePer)</td>
                <td>#formatPercentage($item.productLevel1RaisedOnDatePer)</td>
                <td>#formatPercentage($item.productLevel1DelayRaisedPer)</td>
                <td>#formatPercentage($item.productLevel2RaisedOnTimePer)</td>
                <td>#formatPercentage($item.productLevel2RaisedOnDatePer)</td>
                <td>#formatPercentage($item.productLevel2DelayRaisedPer)</td>
                <td>#formatPercentage($item.productLevel3RaisedOnTimePer)</td>
                <td>#formatPercentage($item.productLevel3RaisedOnDatePer)</td>
                <td>#formatPercentage($item.productLevel3DelayRaisedPer)</td>
                <td>#formatPercentage($item.productLevel4RaisedOnTimePer)</td>
                <td>#formatPercentage($item.productLevel4RaisedOnDatePer)</td>
                <td>#formatPercentage($item.productLevel4DelayRaisedPer)</td>
                <td>#formatPercentage($item.productLevel5RaisedOnTimePer)</td>
                <td>#formatPercentage($item.productLevel5RaisedOnDatePer)</td>
                <td>#formatPercentage($item.productLevel5DelayRaisedPer)</td>
                <td>#formatPercentage($item.productLevelNARaisedOnTimePer)</td>
                <td>#formatPercentage($item.productLevelNARaisedOnDatePer)</td>
                <td>#formatPercentage($item.productLevelNADelayRaisedPer)</td>
            </tr>
            #end
        </table>
    </div>

    <!-- Daily On-Time and On-Date Report -->
    <div class="section">
        <h2>On-Time and On-Date Fulfillment Percentage - Kitchen and Warehouse ($data.prevDay)</h2>
        <table class="data-table">
            <tr>
                <th>Transferring Unit</th>
                <th>Total On-Time %</th>
                <th>Total On-Date %</th>
                <th>Bakery On-Time %</th>
                <th>Bakery On-Date %</th>
                <th>Critical On-Time %</th>
                <th>Critical On-Date %</th>
                <th>Level 1 On-Time %</th>
                <th>Level 1 On-Date %</th>
                <th>Level 2 On-Time %</th>
                <th>Level 2 On-Date %</th>
                <th>Level 3 On-Time %</th>
                <th>Level 3 On-Date %</th>
                <th>Level 4 On-Time %</th>
                <th>Level 4 On-Date %</th>
                <th>Level 5 On-Time %</th>
                <th>Level 5 On-Date %</th>
                <th>Level N/A On-Time %</th>
                <th>Level N/A On-Date %</th>
            </tr>
            #foreach($item in $data.ffDetailLastDay)
            <tr>
                <td>$item.transferringUnit</td>
                <td>${data.mathTool.roundTo(2,$item.avgOnTimeFFPer)}%</td>
                <td>${data.mathTool.roundTo(2,$item.avgOnDateFFPer)}%</td>
                <td>#formatPercentage($item.bakeryOnTimeFP)</td>
                <td>#formatPercentage($item.bakeryOnDateFP)</td>
                <td>#formatPercentage($item.criticalOnTimeFF)</td>
                <td>#formatPercentage($item.criticalOnDateFF)</td>
                <td>#formatPercentage($item.productLevel1OnTimeFFPer)</td>
                <td>#formatPercentage($item.productLevel1OnDateFFPer)</td>
                <td>#formatPercentage($item.productLevel2OnTimeFFPer)</td>
                <td>#formatPercentage($item.productLevel2OnDateFFPer)</td>
                <td>#formatPercentage($item.productLevel3OnTimeFFPer)</td>
                <td>#formatPercentage($item.productLevel3OnDateFFPer)</td>
                <td>#formatPercentage($item.productLevel4OnTimeFFPer)</td>
                <td>#formatPercentage($item.productLevel4OnDateFFPer)</td>
                <td>#formatPercentage($item.productLevel5OnTimeFFPer)</td>
                <td>#formatPercentage($item.productLevel5OnDateFFPer)</td>
                <td>#formatPercentage($item.productLevelNAOnTimeFFPer)</td>
                <td>#formatPercentage($item.productLevelNAOnDateFFPer)</td>
            </tr>
            #end
        </table>
    </div>

    <!-- Monthly Fulfillment Report -->
    <div class="section">
        <h2>Monthly Fulfillment Percentage - Kitchen and Warehouse ($data.thisMonth)</h2>
        <table class="data-table">
            <tr>
                <th>Transferring Unit</th>
                <th>Total Fulfillment %</th>
                <th>Critical Products %</th>
                <th>Negative Impact %</th>
                <th>Bakery Fulfillment %</th>
                <th>Level 1 Products %</th>
                <th>Level 2 Products %</th>
                <th>Level 3 Products %</th>
                <th>Level 4 Products %</th>
                <th>Level 5 Products %</th>
                <th>Level N/A Products %</th>
            </tr>
            #foreach($item in $data.ffDetailLastThD)
            <tr>
                <td>$item.transferringUnit</td>
                <td>${data.mathTool.roundTo(2,$item.avgFPer)}%</td>
                <td>#formatPercentage($item.criticalProductFF)</td>
                <td>${data.mathTool.roundTo(2,$data.mathTool.sub($item.avgFPer,$item.avgImFPer))}%</td>
                <td>#formatPercentage($item.bakeryFP)</td>
                <td>#formatPercentage($item.productLevel1FFPer)</td>
                <td>#formatPercentage($item.productLevel2FFPer)</td>
                <td>#formatPercentage($item.productLevel3FFPer)</td>
                <td>#formatPercentage($item.productLevel4FFPer)</td>
                <td>#formatPercentage($item.productLevel5FFPer)</td>
                <td>#formatPercentage($item.productLevelNAFFPer)</td>
            </tr>
            #end
        </table>
    </div>

    <!-- Monthly On-Time and On-Date Report -->
    <div class="section">
        <h2>Monthly On-Time and On-Date Fulfillment Percentage - Kitchen and Warehouse ($data.thisMonth)</h2>
        <table class="data-table">
            <tr>
                <th>Transferring Unit</th>
                <th>Total On-Time %</th>
                <th>Total On-Date %</th>
                <th>Bakery On-Time %</th>
                <th>Bakery On-Date %</th>
                <th>Critical On-Time %</th>
                <th>Critical On-Date %</th>
                <th>Level 1 On-Time %</th>
                <th>Level 1 On-Date %</th>
                <th>Level 2 On-Time %</th>
                <th>Level 2 On-Date %</th>
                <th>Level 3 On-Time %</th>
                <th>Level 3 On-Date %</th>
                <th>Level 4 On-Time %</th>
                <th>Level 4 On-Date %</th>
                <th>Level 5 On-Time %</th>
                <th>Level 5 On-Date %</th>
                <th>Level N/A On-Time %</th>
                <th>Level N/A On-Date %</th>
            </tr>
            #foreach($item in $data.ffDetailLastThD)
            <tr>
                <td>$item.transferringUnit</td>
                <td>${data.mathTool.roundTo(2,$item.avgOnTimeFFPer)}%</td>
                <td>${data.mathTool.roundTo(2,$item.avgOnDateFFPer)}%</td>
                <td>#formatPercentage($item.bakeryOnTimeFP)</td>
                <td>#formatPercentage($item.bakeryOnDateFP)</td>
                <td>#formatPercentage($item.criticalOnTimeFF)</td>
                <td>#formatPercentage($item.criticalOnDateFF)</td>
                <td>#formatPercentage($item.productLevel1OnTimeFFPer)</td>
                <td>#formatPercentage($item.productLevel1OnDateFFPer)</td>
                <td>#formatPercentage($item.productLevel2OnTimeFFPer)</td>
                <td>#formatPercentage($item.productLevel2OnDateFFPer)</td>
                <td>#formatPercentage($item.productLevel3OnTimeFFPer)</td>
                <td>#formatPercentage($item.productLevel3OnDateFFPer)</td>
                <td>#formatPercentage($item.productLevel4OnTimeFFPer)</td>
                <td>#formatPercentage($item.productLevel4OnDateFFPer)</td>
                <td>#formatPercentage($item.productLevel5OnTimeFFPer)</td>
                <td>#formatPercentage($item.productLevel5OnDateFFPer)</td>
                <td>#formatPercentage($item.productLevelNAOnTimeFFPer)</td>
                <td>#formatPercentage($item.productLevelNAOnDateFFPer)</td>
            </tr>
            #end
        </table>
    </div>

    <!-- Monthly On-Time and On-Date, and delayed Report -->
    <div class="section">
        <h2>Monthly Percentage of On-Time, On-Date, and Delayed RO's Raised – Kitchen & Warehouse ($data.thisMonth)</h2>
        <table class="data-table">
            <tr>
                <th>Transferring Unit</th>
                <th>Total On-Time %</th>
                <th>Total On-Date %</th>
                <th>Total Delayed %</th>
                <th>Bakery On-Time %</th>
                <th>Bakery On-Date %</th>
                <th>Bakery Delayed %</th>
                <th>Critical On-Time %</th>
                <th>Critical On-Date %</th>
                <th>Critical Delayed %</th>
                <th>Level 1 On-Time %</th>
                <th>Level 1 On-Date %</th>
                <th>Level 1 Delayed %</th>
                <th>Level 2 On-Time %</th>
                <th>Level 2 On-Date %</th>
                <th>Level 2 Delayed %</th>
                <th>Level 3 On-Time %</th>
                <th>Level 3 On-Date %</th>
                <th>Level 3 Delayed %</th>
                <th>Level 4 On-Time %</th>
                <th>Level 4 On-Date %</th>
                <th>Level 4 Delayed %</th>
                <th>Level 5 On-Time %</th>
                <th>Level 5 On-Date %</th>
                <th>Level 5 Delayed %</th>
                <th>Level N/A On-Time %</th>
                <th>Level N/A On-Date %</th>
                <th>Level N/A Delayed %</th>
            </tr>
            #foreach($item in $data.ffDetailLastDay)
            <tr>
                <td>$item.transferringUnit</td>
                <td>${data.mathTool.roundTo(2,$item.avgOnTimeRoRaisedPer)}%</td>
                <td>${data.mathTool.roundTo(2,$item.avgOnDateRoRaisedPer)}%</td>
                <td>${data.mathTool.roundTo(2,$item.avgDelayRoRaisedPer)}%</td>
                <td>#formatPercentage($item.bakeryOnTimeRoRaisedPer)</td>
                <td>#formatPercentage($item.bakeryOnDateRoRaisedPer)</td>
                <td>#formatPercentage($item.bakeryRoRaisedDelayPer)</td>
                <td>#formatPercentage($item.criticalOnTimeRaisedFF)</td>
                <td>#formatPercentage($item.criticalOnDateRaisedFF)</td>
                <td>#formatPercentage($item.criticalDelayRaisedFF)</td>
                <td>#formatPercentage($item.productLevel1RaisedOnTimePer)</td>
                <td>#formatPercentage($item.productLevel1RaisedOnDatePer)</td>
                <td>#formatPercentage($item.productLevel1DelayRaisedPer)</td>
                <td>#formatPercentage($item.productLevel2RaisedOnTimePer)</td>
                <td>#formatPercentage($item.productLevel2RaisedOnDatePer)</td>
                <td>#formatPercentage($item.productLevel2DelayRaisedPer)</td>
                <td>#formatPercentage($item.productLevel3RaisedOnTimePer)</td>
                <td>#formatPercentage($item.productLevel3RaisedOnDatePer)</td>
                <td>#formatPercentage($item.productLevel3DelayRaisedPer)</td>
                <td>#formatPercentage($item.productLevel4RaisedOnTimePer)</td>
                <td>#formatPercentage($item.productLevel4RaisedOnDatePer)</td>
                <td>#formatPercentage($item.productLevel4DelayRaisedPer)</td>
                <td>#formatPercentage($item.productLevel5RaisedOnTimePer)</td>
                <td>#formatPercentage($item.productLevel5RaisedOnDatePer)</td>
                <td>#formatPercentage($item.productLevel5DelayRaisedPer)</td>
                <td>#formatPercentage($item.productLevelNARaisedOnTimePer)</td>
                <td>#formatPercentage($item.productLevelNARaisedOnDatePer)</td>
                <td>#formatPercentage($item.productLevelNADelayRaisedPer)</td>
            </tr>
            #end
        </table>
    </div>

    <!-- Product Summary Macro -->
    #macro(productSummaryTable $products $level)
    <div class="section">
        <h2>Level $level Products Summary</h2>
        <table class="data-table">
            <tr>
                <th>Product Name</th>
                <th>Last Day On-Time %</th>
                <th>Last Day On-Date %</th>
                <th>Last Day Fulfillment %</th>
                <th>Last 7 Days On-Time %</th>
                <th>Last 7 Days On-Date %</th>
                <th>Last 7 Days Fulfillment %</th>
                <th>Month-to-Date On-Time %</th>
                <th>Month-to-Date On-Date %</th>
                <th>Month-to-Date Fulfillment %</th>
            </tr>
            #foreach($item in $products)
            <tr>
                <td>$item.productName</td>
                <td>${data.mathTool.roundTo(2,$item.lastDayOnTimeFF)}%</td>
                <td>${data.mathTool.roundTo(2,$item.lastDayOnDateFF)}%</td>
                <td>${data.mathTool.roundTo(2,$item.lastDayFF)}%</td>
                <td>${data.mathTool.roundTo(2,$item.lastSevenDaysOnTimeFF)}%</td>
                <td>${data.mathTool.roundTo(2,$item.lastSevenDaysOnDateFF)}%</td>
                <td>${data.mathTool.roundTo(2,$item.lastSevenDaysFF)}%</td>
                <td>${data.mathTool.roundTo(2,$item.mtdOnTimeFF)}%</td>
                <td>${data.mathTool.roundTo(2,$item.mtdOnDateFF)}%</td>
                <td>${data.mathTool.roundTo(2,$item.mtdFF)}%</td>
            </tr>
            #end
        </table>
    </div>
    #end

    <!-- Product Summary Tables -->
    #productSummaryTable($data.criticalProducts1, "1")
    #productSummaryTable($data.criticalProducts2, "2")
    #productSummaryTable($data.criticalProducts3, "3")

</div>
</body>
</html>
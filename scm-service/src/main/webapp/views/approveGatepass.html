<head>
    <link rel="stylesheet" href="css/multiselect.css">
</head>

<style>
    .chip {
        font-size: 11px;
    }

    .btn i {
        font-size: 1rem !important;
    }

    .btn.btn-xs-small.vBtn {
        width: 70px !important;
        font-size: 10px;
        padding: 5px !important;
        height: 40px;
        line-height: 16px;
        text-align: center;
    }

    .status-chip {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: bold;
        color: white;
    }

    .status-initiated { background-color: #ff9800; }
    .status-approved { background-color: #4caf50; }
    .status-rejected { background-color: #f44336; }
    .status-pending { background-color: #2196f3; }
</style>

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h4 class="left">Gatepass Approval</h4>
            </div>
        </div>
    </div>

    <!-- Search Filters -->
    <div class="row">
        <div class="col s12 m3 l3">
            <label>Select Operation Type:</label>
            <select data-ng-model="searchCriteria.operationType"
                    data-ng-options="ops.name as ops.label for ops in operationList"
                    data-ng-change="operationCheck(searchCriteria.operationType)">
                <option value="">All Operations</option>
            </select>
        </div>

        <div class="col s12 m3 l3">
            <label>Select Status:</label>
            <div id="status"
                 ng-dropdown-multiselect=""
                 extra-settings="multiSelectStatusSettings"
                 options="statusList"
                 selected-model="selectedStatus"
                 translation-texts="{buttonDefaultText: 'Select status'}"
                 checkboxes="true">
            </div>
        </div>

        <div class="col s12 m3 l3" data-ng-if="showVendorFilter">
            <label>Select Vendor:</label>
            <select id="vendors" ui-select2="{allowClear:true, placeholder: 'Select Vendor'}"
                    ng-options="vendor as vendor.name for vendor in vendors"
                    data-ng-change="searchCriteria.vendorSelected = vendorSelected.id"
                    data-ng-model="vendorSelected">
            </select>
        </div>

        <div class="col s12 m3 l3">
            <label>Gatepass ID:</label>
            <input type="number" data-ng-model="searchCriteria.gatePassId" placeholder="Enter Gatepass ID"/>
        </div>
    </div>

    <div class="row">
        <div class="col s12 m3 l3">
            <label for="startDate">Start Date:</label>
            <input input-date type="text" id="startDate"
                   data-ng-model="searchCriteria.startDate"
                   container="" format="yyyy-mm-dd"/>
        </div>

        <div class="col s12 m3 l3">
            <label for="endDate">End Date:</label>
            <input input-date type="text" id="endDate"
                   data-ng-model="searchCriteria.endDate"
                   container="" format="yyyy-mm-dd"/>
        </div>

        <div class="col s12 m3 l3">
            <label>Generated By:</label>
            <input type="text" data-ng-model="searchCriteria.generatedBy" placeholder="Enter user name"/>
        </div>

        <div class="col s12 m3 l3">
            <input type="button" class="btn margin-top-20" value="Search"
                   data-ng-click="searchGatepasses()" data-ng-disabled="isLoading"/>
            <div data-ng-if="isLoading" class="progress" style="margin-top: 10px;">
                <div class="indeterminate"></div>
            </div>
        </div>
    </div>

    <div class="row" data-ng-if="gatepasses.length > 0">
        <div class="col s12 refOrderListTable">
            <ul class="collection menuItemList">
                <li class="collection-item z-depth-1 list-head">
                    <div class="row">
                        <div class="col s1">ID</div>
                        <div class="col s2">Operation Type</div>
                        <div class="col s2">Generated By</div>
                        <div class="col s2">Generation Time</div>
                        <div class="col s2">Status</div>
                        <div class="col s3">Actions</div>
                    </div>
                </li>
                <li class="z-depth-1" data-ng-repeat="gatepass in gatepasses track by gatepass.id"
                    style="margin-top: 15px;">
                    <div class="row" style="min-height: 40px; padding: 10px;">
                        <div class="col s1">
                            <span class="clickable" data-ng-click="viewGatepassDetails(gatepass)"
                                  style="border-bottom: 1px dashed #000">{{gatepass.id}}</span>
                        </div>
                        <div class="col s2">{{gatepass.operationType}}</div>
                        <div class="col s2">{{gatepass.createdBy.name}}</div>
                        <div class="col s2">
                            <span>{{gatepass.createdAt | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</span>
                        </div>
                        <div class="col s2">
                            <span class="status-chip"
                                  data-ng-class="{'status-initiated': gatepass.status === 'INITIATED',
                                                 'status-approved': (gatepass.status === 'APPROVED' || gatepass.status === 'CLOSED'),
                                                 'status-rejected': (gatepass.status === 'APPROVER_REJECTED' || gatepass.status === 'CANCELLED'),
                                                 'status-pending': gatepass.status === 'PENDING_RETURN'}">
                                {{gatepass.status}}
                            </span>
                        </div>
                        <div class="col s3">
                            <button class="btn btn-small green"
                                    data-ng-click="approveGatepass(gatepass)"
                                    data-ng-if="gatepass.status === 'INITIATED'"
                                    tooltipped data-tooltip="Approve Gatepass">
                                Approve
                            </button>
                            <button class="btn btn-small red"
                                    data-ng-click="rejectGatepass(gatepass)"
                                    data-ng-if="gatepass.status === 'INITIATED'"
                                    tooltipped data-tooltip="Reject Gatepass">
                                Reject
                            </button>
                            <button class="btn btn-small blue"
                                    data-ng-click="viewGatepassDetails(gatepass)"
                                    tooltipped data-tooltip="View Details">
                                View
                            </button>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>

    <!-- No Results Message -->
    <div class="row" data-ng-if="gatepasses.length === 0 && searchPerformed">
        <div class="col s12" style="padding: 30px; color: gray; font-size: 20px; text-align: center;">
            No gatepasses found for the selected criteria
        </div>
    </div>
</div>

<!-- View Gatepass Details Modal -->
<div class="modal" id="viewModal" style="width: 85%; max-height: 90%;">
    <div class="modal-content">
        <div class="col s12">
            <div class="row">
                <div class="col s10">
                    <h5 class="left">Gatepass Details</h5>
                </div>
                <div class="col s2">
                    <div data-ng-if="isLoadingDetails" class="progress" style="margin-top: 20px;">
                        <div class="indeterminate"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col s12" data-ng-if="!isLoadingDetails">
            <!-- Basic Information -->
            <div class="row sku-pkg-row">
                <div class="col s3">
                    <label>Gatepass ID:</label> {{selectedGatepass.id}}
                </div>
                <div class="col s3">
                    <label>Operation Type:</label> {{selectedGatepass.operationType}}
                </div>
                <div class="col s3">
                    <label>Status:</label>
                    <span class="status-chip"
                          data-ng-class="{'status-initiated': selectedGatepass.status === 'INITIATED',
                                                 'status-approved': (selectedGatepass.status === 'APPROVED' || selectedGatepass.status === 'CLOSED'),
                                                 'status-rejected': (selectedGatepass.status === 'APPROVER_REJECTED' || selectedGatepass.status === 'CANCELLED'),
                                                 'status-pending': selectedGatepass.status === 'PENDING_RETURN'}">
                        {{selectedGatepass.status}}
                    </span>
                </div>
                <div class="col s3" data-ng-if="selectedGatepass.assetGatePass">
                    <label>Asset Gatepass:</label>
                    <span class="chip green white-text">Yes</span>
                </div>
            </div>

            <div class="row sku-pkg-row">
                <div class="col s3">
                    <label>Generated By:</label> {{selectedGatepass.createdBy.name}}
                </div>
                <div class="col s3">
                    <label>Generation Time:</label> {{selectedGatepass.createdAt | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}
                </div>
                <div class="col s3">
                    <label>Sending Unit:</label> {{selectedGatepass.sendingUnit.name}}
                </div>
                <div class="col s3" data-ng-if="selectedGatepass.returnable">
                    <label>Returnable:</label>
                    <span class="chip blue white-text">{{selectedGatepass.expectedReturn}} days</span>
                </div>
            </div>

            <div class="row sku-pkg-row">
                <div class="col s4" data-ng-if="selectedGatepass.vendor">
                    <label>Vendor:</label> {{selectedGatepass.vendor.name}}
                </div>
                <div class="col s4" data-ng-if="selectedGatepass.additionalCharges">
                    <label>Additional Charges:</label> ₹{{selectedGatepass.additionalCharges}}
                </div>
                <div class="col s4" data-ng-if="selectedGatepass.issueDate">
                    <label>Issue Date:</label> {{selectedGatepass.issueDate | date:'dd-MM-yyyy'}}
                </div>
            </div>

            <div class="row" data-ng-if="selectedGatepass.comment">
                <div class="col s12">
                    <label>Comments:</label>
                    <div style="background: #f5f5f5; padding: 10px; border-radius: 4px; margin-top: 5px;">
                        {{selectedGatepass.comment}}
                    </div>
                </div>
            </div>

            <!-- Items Section -->
            <div class="row" data-ng-if="selectedGatepass.itemDatas && selectedGatepass.itemDatas.length > 0">
                <div class="col s12">
                    <h6>Items ({{selectedGatepass.itemDatas.length}}):</h6>
                    <ul class="collection menuItemList z-depth-1-half">
                        <li class="collection-item z-depth-1 list-head">
                            <div class="row" style="margin-bottom: 0;">
                                <div class="col s3">SKU Name</div>
                                <div class="col s2">Quantity</div>
                                <div class="col s2">Unit of Measure</div>
                                <div class="col s2">Transaction Type</div>
                                <div class="col s3">Additional Info</div>
                            </div>
                        </li>
                        <li class="collection-item" data-ng-repeat="item in selectedGatepass.itemDatas"
                            style="margin-top: 5px; border-bottom: 1px solid #ddd;">
                            <div class="row" style="margin-bottom: 0; padding: 5px 0;">
                                <div class="col s3">
                                    <strong>{{item.sku.name}}</strong>
                                    <br><small style="color: #666;">ID: {{item.sku.id}}</small>
                                </div>
                                <div class="col s2">
                                    <span class="chip">{{item.quantity}}</span>
                                </div>
                                <div class="col s2">{{item.uom}}</div>
                                <div class="col s2">
                                    <span class="chip"
                                          data-ng-class="{'blue white-text': item.transType === 'TRANSFER',
                                                         'green white-text': item.transType === 'RETURN',
                                                         'red white-text': item.transType === 'LOST'}">
                                        {{item.transType}}
                                    </span>
                                </div>
                                <div class="col s3">
                                    <div data-ng-if="item.createdBy">
                                        <small>Created by: {{item.createdBy.name}}</small>
                                    </div>
                                    <div data-ng-if="item.createdAt">
                                        <small>{{item.createdAt | date:'dd-MM-yyyy hh:mm':'+0530'}}</small>
                                    </div>
                                    <div data-ng-if="item.gatepassItemAssetMappings && item.gatepassItemAssetMappings.length > 0">
                                        <small style="color: #2196f3;">
                                            <i class="material-icons tiny">local_offer</i>
                                            {{item.gatepassItemAssetMappings.length}} asset(s)
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Asset Details for Asset Gatepasses -->
                            <div class="row"
                                 data-ng-if="selectedGatepass.assetGatePass && item.gatepassItemAssetMappings && item.gatepassItemAssetMappings.length > 0"
                                 style="background: #f8f9fa; margin: 5px 0; padding: 10px; border-radius: 4px;">
                                <div class="col s12">
                                    <strong style="color: #2196f3;">Asset Tags:</strong>
                                    <div style="margin-top: 5px;">
                                        <span data-ng-repeat="asset in item.gatepassItemAssetMappings"
                                              class="chip"
                                              data-ng-class="{'green white-text': asset.isReturned === 'Y', 'blue white-text': asset.isReturned !== 'Y'}"
                                              style="margin: 2px;">
                                            {{asset.assetTagValue}}
                                            <span data-ng-if="asset.isReturned === 'Y'"> ✓</span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Summary Information -->
            <div class="row" data-ng-if="selectedGatepass.totalCost || selectedGatepass.totalTax">
                <div class="col s12">
                    <div class="card-panel grey lighten-4">
                        <h6>Financial Summary:</h6>
                        <div class="row" style="margin-bottom: 0;">
                            <div class="col s4" data-ng-if="selectedGatepass.totalCost">
                                <label>Total Cost:</label> ₹{{selectedGatepass.totalCost | number:2}}
                            </div>
                            <div class="col s4" data-ng-if="selectedGatepass.totalTax">
                                <label>Total Tax:</label> ₹{{selectedGatepass.totalTax | number:2}}
                            </div>
                            <div class="col s4" data-ng-if="selectedGatepass.totalCost && selectedGatepass.totalTax">
                                <label>Grand Total:</label>
                                <strong>₹{{(selectedGatepass.totalCost + selectedGatepass.totalTax) | number:2}}</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col s12 center">
                    <a class="modal-action modal-close waves-effect btn blue" data-ng-click="closeViewModal()">Close</a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Approval Confirmation Modal -->
<div class="modal" id="approvalModal" style="width: 50%; max-height: 70%;">
    <div class="modal-content">
        <div class="col s12">
            <div class="row">
                <div class="col s12">
                    <h5 class="left">{{actionType}} Gatepass</h5>
                </div>
            </div>
        </div>
        <div class="col s12">
            <div class="row sku-pkg-row">
                <div class="col s6">
                    <label>Gatepass ID:</label> {{selectedGatepass.id}}
                </div>
                <div class="col s6">
                    <label>Operation Type:</label> {{selectedGatepass.operationType}}
                </div>
            </div>
            <div class="row sku-pkg-row">
                <div class="col s6">
                    <label>Generated By:</label> {{selectedGatepass.createdBy.name}}
                </div>
                <div class="col s6">
                    <label>Generation Time:</label> {{selectedGatepass.createdAt | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}
                </div>
            </div>
            <div class="row">
                <div class="col s12">
                    <label>Comments:</label>
                    <textarea data-ng-model="approvalComments"
                              placeholder="Enter comments for {{actionType.toLowerCase()}}..."
                              rows="3"></textarea>
                </div>
            </div>
            <div class="row">
                <div class="col s6 form-element">
                    <a class="modal-action modal-close waves-effect btn grey"
                       data-ng-click="closeActionModal()">Cancel</a>
                </div>
                <div class="col s6 right-align form-element">
                    <a class="modal-action modal-close waves-effect btn"
                       data-ng-class="{'green': actionType === 'Approve', 'red': actionType === 'Reject'}"
                       data-ng-click="confirmAction()">{{actionType}}</a>
                </div>
            </div>
        </div>
    </div>
</div>
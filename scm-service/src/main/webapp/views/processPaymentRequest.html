<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    td, th {
        padding: 10px 5px !important;
    }

    .select2.select2-container {
        width: 100% !important;
    }

    #images {
        text-align: center;
    }

    img#scanned {
        height: 400px; /** Sets the display size */
        margin-right: 12px;
        margin-top: 20px;
    }

    .prBlocked {
        background: red;
        padding: 5px;
    }

    .popeye-modal-container .popeye-modal {
        width: 900px;
    }
       .modal1 {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 999; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100vh; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgb(0,0,0); /* Fallback color */
    background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

/* modal1 Content/Box */
.modal1-content {
    background-color: #fefefe;
    margin: 15% auto; /* 15% from the top and centered */
    padding: 20px;
    border: 1px solid #888;
    width: 50%; /* Could be more or less, depending on screen size */
}
.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.close:hover,
.close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}


</style>

<div class="searchingCard row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h5 class="left">Process Payment Request</h5>
            </div>
        </div>
    </div>

    <div data-ng-show="selectView==true">
        <div class="row">
            <div class="col s12">
                <label>Select Request Type</label>
                <select data-ng-model="prRequestType"
                        data-ng-options="item as item.name for item in prRequestTypes"
                        data-ng-change="setRequestType(prRequestType)"></select>
            </div>
        </div>

        <div class="row">
            <div class="col s12">
                <label>Select Date Type</label>
                <select ui-select2 id="dateType" name="dateType" data-ng-model="selectedDateType" data-placeholder="Select Date Type"
                        data-ng-change="changeSelectedDate(selectedDateType)">
                    <option value=""></option>
                    <option ng-repeat="dateType in availableDate" value="{{dateType}}">{{dateType}}</option>
                </select>
            </div>
        </div>


        <div class="row">
            <div class="col s3">
                <label>Start date</label>
                <input input-date type="text" data-ng-model="startDate" container="" format="yyyy-mm-dd"/>
            </div>
            <div class="col s3">
                <label>End date</label>
                <input input-date type="text" data-ng-model="endDate" container="" format="yyyy-mm-dd"/>
            </div>
            <div class="col s3">
                <label>Select Company</label>
                <select ui-select2 id="companyList" name="companyList" data-ng-model="selectedCompany"
                        data-ng-change="getCompanyMappedUnits()"
                        data-ng-options="company as company.name for company in companyList track by company.id"></select>
            </div>
            <div class="col s3" data-ng-if="prRequestType.code != 'ADVANCE_PAYMENT'">
                <label>Select Unit</label>
                <select ui-select2 id="unitListProcessPayment" name="unitList" data-ng-model="selectedUnit"
                        data-ng-change="selectUnit(selectedUnit)"
                        data-ng-options="item as item.name for item in unitList track by item.id"></select>
            </div>
        </div>
        <div class="row" style="margin-bottom: 0px;">
            <div class="col s3">
                <label>Select Vendor</label>
                <select ui-select2 id="vendorListProcessPayment" name="vendorList" data-ng-model="selectedVendor"
                        data-ng-options="item as item.name for item in vendorList track by item.id"></select>
            </div>
            <div class="col s3">
                <label>Select Status</label>
                <select ui-select2 id="prStatusList" name="prStatusList" data-ng-model="selectedPRStatus"
                        data-ng-options="status as status.name for status in prStatusList track by status.id"></select>
            </div>
            <div class="col s3">
                <label>PR number</label>
                <input type="text" data-ng-model="prId"/>
            </div>
            <div class="col s3">
                <label>Invoice number</label>
                <input type="text" data-ng-model="invoiceNumber" data-ng-disabled="prRequestType.code == 'ADVANCE_PAYMENT'"/>
            </div>
        </div>

        <div class="row">
            <div class="col s2">
                <input type="button" class="btn" value="Find" data-ng-click="findPrs()"/>
                <input type="button" class="btn" value="Adhoc Payment sheet download" style="display: none;"
                       data-target="adhocPaymentSheetModal" modal/>
            </div>
        </div>


        <div class="row">
            <div class="col s12">
                <input type="button" class="btn" data-ng-if="prs.length>0" value="Start Payment Process"
                       acl-action="PRSPP" data-target="paymentSheetModal" modal/>
                <input type="button" class="btn" data-ng-if="prs.length>0" value="Force close"
                       acl-action="PRFCP" data-ng-click="setAction(null,'FORCE_CLOSE')" data-target="reasonModal"
                       modal/>
                <input type="button" class="btn" data-ng-if="prs.length>0" value="Un-block requests"
                       acl-action="PRUBL" data-ng-click="setAction(null,'UNBLOCK')" data-target="reasonModal"
                       modal/>
                <input type="button" class="btn" data-ng-if="prs.length>0" value="Block requests"
                       acl-action="PRBLK" data-ng-click="setAction(null,'BLOCK')" data-target="reasonModal" modal/>

                <div class="row" style="margin-top: 15px;">
                    <div class="col s3">
                        <p data-ng-if="prs.length>0" style="margin:4px;">
                            <span style="padding: 5px; background: red; display: inline-block;" /> Blocked
                            &emsp;
                            <!--<span style="padding: 5px; background: orange; display: inline-block;"></span> Debit limit
                            exceeded-->
                        </p>
                    </div>
                    <div class="col s3">
                        <input type="checkbox" id="showBlocked" data-ng-model="blockFilter"
                               data-ng-checked="blockFilter==true" data-ng-change="applySpecialFilters()"/>
                        <label for="showBlocked">Show blocked</label>
                    </div>
                    <div class="col s3">
                        <input type="checkbox" id="showValid" data-ng-model="validFilter"
                               data-ng-checked="validFilter==true" data-ng-change="applySpecialFilters()"/>
                        <label for="showValid">Show valid</label>
                    </div>
                </div>
                <div class="vendorPRContainer"
                     data-ng-repeat="item in vendorPRs | orderBy : 'vendorId.name' track by item.vendorId.id">
                    <div class="row vendorHeader" ng-class="{red: hover}" ng-mouseenter="hover = true"
                         ng-mouseleave="hover = false" data-ng-click="getVendorPRSummary(item)">
                        <div class="col s7">{{item.vendorId.name}}</div>
                        <div class="col s2">Credit Cycle {{item.vendorCreditPeriod}}</div>
                        <div class="col s3">To be paid: {{item.toBePaid | number : 2}}</div>
                    </div>
                    <div data-ng-class="{'row vendorSummary':item.balance <= 0, 'row vendorSummary redBg':item.balance>0}"
                         data-ng-if="item.showPRList">
                        <div class="col s4">
                            <p>Ledger Balance:</p>
                            <div data-ng-repeat="balance in item.vendorSummary.debitBalances">
                                {{balance.companyName}} : {{balance.debitBalance | number : 2}}
                            </div>
                            <div>Total : {{item.vendorSummary.totalDebitBalance | number : 2}}</div>
                        </div>
                        <div class="col s4">
                            <p>Under payment: {{item.vendorSummary.paymentSentCount}}</p>
                            <p>Sum under payment: {{item.vendorSummary.paymentSentAmount | number : 2}}</p>
                            <p>Net Balance
                                {{item.vendorSummary.paymentSentAmount+item.vendorSummary.totalDebitBalance | number
                                : 2}}</p>
                        </div>
                        <div class="col s4">
                            <p>Selected for payment: {{item.selectedForPayment}}</p>
                            <p>Selected for payment sum: {{item.selectedForPaymentSum | number : 2}}</p>
                            <p>Balance: {{item.balance | number : 2}}</p>
                        </div>
                    </div>
                    <div class="respTable vendorPRItems standardView" data-ng-if="item.showPRList">
                        <table class="table row bordered striped" style="border:#ccc 1px solid;"
                               data-ng-if="item.prList.length>0">
                            <thead>
                            <tr>
                                <th>
                                    <span>
                                        <input id="{{item.vendorId.id}}allPrs" type="checkbox"
                                               data-ng-change="selectAllPaymentRequests(item)"
                                               data-ng-model="item.selectAllPrs"
                                               data-ng-checked="item.selectAllPrs==true"/>
                                        <label for="{{item.vendorId.id}}allPrs">Select All</label>
                                    </span>
                                </th>
                                <th>PR Id</th>
                                <th>Company Name</th>
                                <th>Invoice #</th>
                                <th>Creation Date</th>
                                <th>Unit</th>
                                <th>Proposed Amount</th>
                                <th>Paid Amount</th>
                                <th title="Payment Date caluclated after removing holidays,non-working saturdays and sundays">Payment Date</th>
                                <th title="Actual Vendor Payment Date">Vendor Payment Date</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                            </thead>
                            <tbody>
                            <!--<tr data-ng-repeat="pr in filtered = (filteredPrs | filter:search | orderBy : 'vendorId.name') track by pr.paymentRequestId">-->
                            <tr data-ng-repeat="pr in item.prList track by pr.paymentRequestId">
                                <td>
                                    <span>
                                        <input id="pr-{{pr.paymentRequestId}}" type="checkbox"
                                               data-ng-model="pr.selected" data-ng-change="selectPR(pr, item)"/>
                                        <label for="pr-{{pr.paymentRequestId}}"/>
                                    </span>
                                </td>
                                <td>
                                    <span data-ng-class="{'prBlocked':pr.blocked==true}">{{pr.paymentRequestId}}</span>
                                </td>
                                <td>{{companyMap[pr.companyId].name}}</td>
                                <td>{{pr.invoiceNumber != null ? pr.invoiceNumber : '-'}}</td>
                                <td>{{pr.creationTime | date:'dd-MM-yyyy hh:mm:ss a'}}</td>
                                <td ng-if="prRequestType.code != 'ADVANCE_PAYMENT' && !isMulitpleUnit(pr.businessCostDetailData)">{{pr.businessCostDetailData}}</td>
                                <td ng-if="prRequestType.code != 'ADVANCE_PAYMENT' && isMulitpleUnit(pr.businessCostDetailData)">
                                    <div class="col-xs-1" style="align:right" ng-click="showBccData(pr.businessCostDetailData)">
                                        <p style="color: #00b0ff; text-decoration: underline">MULTIPLE[{{pr.businessCostDetailData.split(",").length}}]</p>
                                    </div>
                                </td>
                                <td data-ng-if="prRequestType.code == 'ADVANCE_PAYMENT'"> - </td>
                                <td>{{pr.proposedAmount}}</td>
                                <td>{{pr.paidAmount}}</td>
                                <!--<td>{{(pr.paymentCycle != null? pr.paymentCycle.paymentDate:pr.paymentDate) |-->
                                    <!--date:'dd-MM-yyyy'}}-->
                                <td>{{pr.paymentDate != null ? (pr.paymentDate | date:'dd-MM-yyyy') : '-'}}</td>
                                <td>{{pr.paymentCycle != null ? (pr.paymentCycle.paymentDate | date:'dd-MM-yyyy') : pr.vendorPaymentDate != null ? (pr.vendorPaymentDate | date:'dd-MM-yyyy') : '-'}}
                                </td>
                                <td>{{pr.currentStatus}}</td>
                                <td>
                                    <input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button"
                                           value="View"
                                           data-ng-click="viewPaymentRequest(pr,'VIEW')">
                                    <!--<input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button"-->
                                    <!--value="Acknowledge" data-ng-if="pr.currentStatus=='CREATED'"-->
                                    <!--acl-action="PRACK" data-ng-click="changeStatus(pr,'ACKNOWLEDGED')">-->
                                    <input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button"
                                           value="Acknowledge" data-ng-if="pr.currentStatus=='CREATED'"
                                           acl-action="PRACK" data-ng-click="viewPaymentRequest(pr,'ACKNOWLEDGED')">
                                    <input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button"
                                           value="FILING DONE"
                                           data-ng-if="pr.currentStatus=='PAID' && pr.blocked==false"
                                           acl-action="PRACK" value="Filing Done"
                                           data-ng-click="filingPaymentRequest(pr)" data-target="filingModal" modal>
                                    <input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button"
                                           value="Approve" data-ng-if="pr.currentStatus=='ACKNOWLEDGED'"
                                           acl-action="PRAPR" data-ng-click="viewPaymentRequest(pr,'APPROVE')">
                                    <input type="button" class="btn btn-xs-small" value="Settle"
                                           data-ng-if="pr.currentStatus=='SENT_FOR_PAYMENT' && pr.blocked==false"
                                           acl-action="PRSTL" data-target="paymentSettleModal" modal
                                           data-ng-click="resetPaymentDetail(pr)"/>
                                    <input type="button" class="btn btn-xs-small" value="Pay Adhoc"
                                           data-ng-if="pr.currentStatus=='APPROVED' && pr.blocked==false && pr.advancePayment == null"
                                           acl-action="PRADP" data-target="reasonModal" modal
                                           data-ng-click="setAction(pr,'ADHOC')"/>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class = "modal1" id = "showBccModal">
                        <div class="modal1-content">
                            <div class="modal-header">
                                <button type="button" class="close"  ng-click="hideBccData()" data-dismiss="modal">&times;</button>
                                <h4 class="modal-title">Requesting Units : {{selectedpartnerID.name}}</h4>
                            </div>
                            <div class="modal-body" style="text-align: center">
                                <div data-ng-repeat="data in bccList track by $index" >
                                    <p style="text-align: start">{{data}}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="TableMobileView vendorPRItems" data-ng-if="item.showPRList">
                        <span>
                            <input id="{{item.vendorId.id}}allPrs" type="checkbox" data-ng-change="selectAllPaymentRequests(item)"
                                   data-ng-model="item.selectAllPrs" data-ng-checked="item.selectAllPrs==true" />
                            <label for="{{item.vendorId.id}}allPrs">Select All</label>
                        </span>
                        <ul class="collection center" data-ng-if="item.prList.length>0">
                            <li class="collection-item" data-ng-repeat="pr in item.prList track by pr.paymentRequestId">
                                <div class="row">
                                    <div class="col">Select</div>
                                    <div class="col">
                                        <span>
                                            <input id="pr-{{pr.paymentRequestId}}" type="checkbox" data-ng-model="pr.selected"
                                                   data-ng-change="selectPR(pr, item)" />
                                            <label for="pr-{{pr.paymentRequestId}}" />
                                        </span>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col">PR Id</div>
                                    <div class="col">
                                        <span data-ng-class="{'prBlocked':pr.blocked==true}">{{pr.paymentRequestId}}</span>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col">Company Name</div>
                                    <div class="col">{{companyMap[pr.companyId].name}}</div>
                                </div>
                                <div class="row">
                                    <div class="col">Invoice #</div>
                                    <div class="col">{{pr.invoiceNumber}}</div>
                                </div>
                                <div class="row">
                                    <div class="col">Creation Date</div>
                                    <div class="col">{{pr.creationTime | date:'dd-MM-yyyy hh:mm:ss a'}}</div>
                                </div>
                                <div class="row">
                                        <div class="col">Unit</div>
                                    <div class="col" >{{pr.requestingUnit.name}}</div>
                                </div>
                                <div class="row">
                                    <div class="col">Proposed Amount</div>
                                    <div class="col">{{pr.proposedAmount}}</div>
                                </div>
                                <div class="row">
                                    <div class="col">Paid Amount</div>
                                    <div class="col">{{pr.paidAmount}}</div>
                                </div>
                                <div class="row">
                                    <div class="col">Payment Date</div>
                                    <div class="col">{{pr.paymentDate | date:'dd-MM-yyyy'}}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col">Vendor Payment Date</div>
                                    <div class="col">{{(pr.paymentCycle != null? pr.paymentCycle.paymentDate:pr.vendorPaymentDate) |
                                        date:'dd-MM-yyyy'}}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col">Status</div>
                                    <div class="col">{{pr.currentStatus}}</div>
                                </div>
                                <div class="row">
                                    <div class="col">Actions</div>
                                    <div class="col">
                                        <input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button" value="View"
                                               data-ng-click="viewPaymentRequest(pr,'VIEW')">
                                        <!--<input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button"-->
                                        <!--value="Acknowledge" data-ng-if="pr.currentStatus=='CREATED'"-->
                                        <!--acl-action="PRACK" data-ng-click="changeStatus(pr,'ACKNOWLEDGED')">-->
                                        <input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button" value="Acknowledge"
                                               data-ng-if="pr.currentStatus=='CREATED'" acl-action="PRACK"
                                               data-ng-click="viewPaymentRequest(pr,'ACKNOWLEDGED')">
                                        <input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button" value="FILING DONE"
                                               data-ng-if="pr.currentStatus=='PAID' && pr.blocked==false" acl-action="PRACK"
                                               value="Filing Done" data-ng-click="filingPaymentRequest(pr)" data-target="filingModal" modal>
                                        <input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button" value="Approve"
                                               data-ng-if="pr.currentStatus=='ACKNOWLEDGED'" acl-action="PRAPR"
                                               data-ng-click="viewPaymentRequest(pr,'APPROVE')">
                                        <input type="button" class="btn btn-xs-small" value="Settle"
                                               data-ng-if="pr.currentStatus=='SENT_FOR_PAYMENT' && pr.blocked==false" acl-action="PRSTL"
                                               data-target="paymentSettleModal" modal data-ng-click="resetPaymendivetail(pr)" />
                                        <input type="button" class="btn btn-xs-small" value="Pay Adhoc"
                                               data-ng-if="pr.currentStatus=='APPROVED' && pr.blocked==false && pr.advancePayment == null" acl-action="PRADP"
                                               data-target="reasonModal" modal data-ng-click="setAction(pr,'ADHOC')" />
                                    </div>
                                </div>
                                <!--<tr data-ng-repeat="pr in filtered = (filteredPrs | filter:search | orderBy : 'vendorId.name') track by pr.paymentRequestId">-->
                            </li>
                        </ul>
                    </div>
                </div>
                <!--<table class="table bordered striped" style="border:#ccc 1px solid;margin-bottom: 10px;"
                       data-ng-if="prs.length>0">
                    <thead>
                    <tr>
                        <th>
                            <span>
                                <input id="allPrs" type="checkbox" data-ng-change="selectAllPaymentRequests()"
                                       data-ng-model="selectAllPrs" data-ng-checked="selectAllPrs==true"/>
                                <label for="allPrs">Select All</label>
                            </span>
                        </th>
                        <th>PR Id</th>
                        <th>Company Name</th>
                        <th>Invoice #</th>
                        <th>Creation Date</th>
                        <th>Vendor</th>
                        <th>Credit Cycle</th>
                        <th>Ledger Balance</th>
                        <th>Unit</th>
                        <th>Proposed Amount</th>
                        <th>Paid Amount</th>
                        <th>Payment Date</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr data-ng-repeat="pr in filtered = (filteredPrs | filter:search | orderBy : 'vendorId.name') track by pr.paymentRequestId">
                        <td>
                            <span>
                                <input id="pr-{{pr.paymentRequestId}}" type="checkbox" data-ng-model="pr.selected"/>
                                <label for="pr-{{pr.paymentRequestId}}"></label>
                            </span>
                        </td>
                        <td>
                            <span data-ng-class="{'prBlocked':pr.blocked==true,'debitExceeded':pr.debitExceeded}">{{pr.paymentRequestId}}</span>
                        </td>
                        <td>{{companyMap[pr.companyId].name}}</td>
                        <td>{{pr.invoiceNumber}}</td>
                        <td>{{pr.creationTime | date:'dd-MM-yyyy hh:mm:ss a'}}</td>
                        <td>{{pr.vendorId.name}}</td>
                        <td>{{pr.vendorCreditPeriod}}</td>
                        <td>{{pr.vendorDebitBalance}}</td>
                        <td>{{pr.requestingUnit.name}}</td>
                        <td>{{pr.proposedAmount}}</td>
                        <td>{{pr.paidAmount}}</td>
                        <td>{{pr.paymentCycle.paymentDate | date:'dd-MM-yyyy'}}</td>
                        <td>{{pr.currentStatus}}</td>
                        <td>
                            <input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button" value="View"
                                   data-ng-click="viewPaymentRequest(pr,'VIEW')">
                            &lt;!&ndash;    <input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button" value="Update Invoice"
                                      data-ng-if="pr.currentStatus=='INITIATED'"
                                      acl-action="PRUPI" data-ng-click="viewPaymentRequest(pr,'UPDATE_INVOICE')">
                                      <input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button" value="Cancel"
                                      data-ng-if="pr.currentStatus=='CREATED' || pr.currentStatus=='INITIATED'"
                                      acl-action="PRCNL" data-ng-click="changeStatus(pr,'CANCELLED')"> &ndash;&gt;
                            <input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button"
                                   value="Acknowledge" data-ng-if="pr.currentStatus=='CREATED'"
                                   acl-action="PRACK" data-ng-click="changeStatus(pr,'ACKNOWLEDGED')">
                            <input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button"
                                   value="Approve" data-ng-if="pr.currentStatus=='ACKNOWLEDGED'"
                                   acl-action="PRAPR" data-ng-click="viewPaymentRequest(pr,'APPROVE')">
                            <input type="button" class="btn btn-xs-small" value="Settle"
                                   data-ng-if="pr.currentStatus=='SENT_FOR_PAYMENT' && pr.blocked==false && pr.debitExceeded==false"
                                   acl-action="PRSTL" data-target="paymentSettleModal" modal
                                   data-ng-click="resetPaymentDetail(pr)"/>
                            <input type="button" class="btn btn-xs-small" value="Pay Adhoc"
                                   data-ng-if="pr.currentStatus=='APPROVED' && pr.blocked==false && pr.debitExceeded==false"
                                   acl-action="PRADP" data-target="reasonModal" modal
                                   data-ng-click="setAction(pr,'ADHOC')"/>
                        </td>
                    </tr>
                    </tbody>
                </table>-->
                <div data-ng-if="showNoPR"
                     style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">No payment
                    requests found.
                </div>
                <input type="button" class="btn" data-ng-if="prs.length>0" value="Start Payment Process"
                       acl-action="PRSPP" data-target="paymentSheetModal" modal/>
                <input type="button" class="btn" data-ng-if="prs.length>0" value="Un-block requests"
                       acl-action="PRUBL" data-ng-click="setAction(null,'UNBLOCK')" data-target="reasonModal"
                       modal/>
                <input type="button" class="btn" data-ng-if="prs.length>0" value="Block requests"
                       acl-action="PRBLK" data-ng-click="setAction(null,'BLOCK')" data-target="reasonModal" modal/>
            </div>
        </div>
    </div>

    <div data-ng-show="selectView==false">
        <div class="row">
            <div class="col s12">
                <input type="button" class="btn btn-xs-small" data-ng-click="backToSelectView()" value="Back"/>
            </div>
        </div>
        <div class="row">
            <div class="col s12"><b>Company Name:</b> {{companyMap[viewPr.companyId].name}}</div>
        </div>
        <!-- <div class="row" data-ng-if="viewPr.section206 != null">
            <div class="col s12 red white-text"><b>TDS will be deducted at twice of rate prescribed in section or 5% whichever is higher</b></div>
        </div> -->
        <div class="row">
            <div class="col s6">
               <!-- <p><b>Unit Id:</b> {{selectedUnit.id}}</p>
                <p><b>Unit Name:</b> {{selectedUnit.name}}</p>-->
                <p data-ng-if="viewPr.type == 'SERVICE_RECEIVED'"><b>Department :</b> {{viewPr.paymentInvoice.paymentInvoiceItems[0].departmentName}}</p>
                <p>
                    <b>PR Id:</b> {{viewPr.paymentRequestId}}
                    <i class="pointer" data-ng-if="viewPr.type == 'SERVICE_RECEIVED'"
                       data-ng-click="viewSRs(viewPr)">(View SRs)</i>
                </p>
                <p><b>Vendor:</b> {{viewPr.vendorId.name}}</p>
                <p><b>Vendor State:</b> {{viewPr.vendorState}}</p>
                <p><b>Billing State:</b> {{viewPr.paymentState}}</p>
                <p><b>Pan Number:</b> {{viewPr.pan}}</p>
                <p><b>Invoice Number:</b> {{viewPr.paymentInvoice.invoiceNumber}}</p>
                <p><b>Status:</b> {{viewPr.currentStatus}}</p>
                <p><b>Deviation Count:</b> {{viewPr.deviationCount}}</p>
                <div data-ng-if="viewPr.type=='SERVICE_RECEIVED' && (actionType=='ACKNOWLEDGED' || actionType=='APPROVE' || actionType=='VIEW')" style="width: 300px !important;">
                    <label>Select Recipient State :</label>
                    <select  id="RecipientState" data-ng-disabled="actionType=='APPROVE' || actionType=='VIEW'" name="RecipientState" data-ng-model="selectedGstOfStpl" data-ng-change="setSelectedGstOfStpl(selectedGstOfStpl)" data-ng-options="gst as gst.name for gst in gstOfStpl" ></select>
                    <p><b>Selected Recipient State : {{selectedGstOfStpl.name}} </b></p> 
                </div>
               
               
            </div>
            <div class="col s6">
                <p><b>Creation time:</b> {{viewPr.creationTime | date:'dd-MM-yyyy hh:mm:ss a'}}</p>
                <p><b>Invoice Date:</b> {{viewPr.paymentInvoice.invoiceDate | date: 'dd-MM-yyyy hh:mm:ss a'}}</p>
                <p><b>Amount match:</b> {{viewPr.amountsMatch==true?'Yes':'No'}}</p>
                <p><b>Doc Type:</b> {{viewPr.grDocType}}</p>
                <p><b>Last update:</b> {{viewPr.lastUpdated | date:'dd-MM-yyyy hh:mm:ss a'}}</p>
                <p data-ng-if="viewPr.blocked"><b>Blocked by:</b> {{viewPr.blockedBy.name}}</p>
                <p><b>Vendor Credit Days:</b> {{viewPr.vendorCreditPeriod}}</p>
                <p><b>Blocked:</b> {{viewPr.blocked==true?'Yes':'No'}}</p>
                <p data-ng-if="viewPr.type=='SERVICE_RECEIVED'"><b>Sr Type:</b> {{srType}}</p>
                <p data-ng-if="viewPr.type=='SERVICE_RECEIVED' && viewPr.tds!=null"><b>Tds Applicable:</b> <span data-ng-if="viewPr.tds">YES</span>
                    <span data-ng-if=" !viewPr.tds">NO</span>
                </p>
                <div data-ng-if="viewPr.type=='SERVICE_RECEIVED' && (actionType=='ACKNOWLEDGED' || actionType=='APPROVE' || actionType=='VIEW')" style="width: 300px !important;">
                    <label>Select Supplier State :</label>
                    <select  id="SupplierState" data-ng-disabled="actionType=='APPROVE' || actionType=='VIEW'" name="SupplierState" data-ng-model="selectedGstStateData" data-ng-change="setSelectedGstStateData(selectedGstStateData)" data-ng-options="state as state.stateName for state in gstStateData"></select>  
                    <p><b>Selected Supplier State : {{selectedGstStateData.stateName}} </b></p> 
                </div>

            </div>
        </div>
        <div data-ng-if="viewPr.type=='SERVICE_RECEIVED'" style="padding: 10px;"> 
            <div data-ng-repeat="msg in loggedMessages">
                <p style="color: red;"><b>{{msg}}</b></p> <br>      
            </div>
            
        </div>

        
      
        <button class="btn" style="float: right" data-ng-click="exportCsv(viewPr)" data-ng-if="prRequestType.code != 'ADVANCE_PAYMENT'">download</button><br><br>
        <div id="tally-uploader-metadata" style="margin-top: 50px;" data-ng-if="viewPr.type=='SERVICE_RECEIVED' && (actionType=='ACKNOWLEDGED' || actionType=='APPROVE' || actionType=='VIEW')" >
            <div class="row" >
                <div class="col s2">
                   <div data-ng-if="viewPr.isPrCCVendor==='Y' || actionType=='APPROVE' || actionType=='VIEW'"> <input id="gstInput"  type="checkbox" data-ng-model="isGstInputAvailed" data-ng-change="setIsGstInputAvailed(isGstInputAvailed)" data-ng-disabled="actionType=='APPROVE' || actionType=='VIEW'" ><label for="gstInput">GST input availed</label>  </div> 
                    <span data-ng-if="showRcm===true || actionType=='APPROVE' || actionType=='VIEW'">
                        <input id="isRcm" type="checkbox" data-ng-model="isRcm" data-ng-change="setIsRcm(isRcm)" data-ng-disabled="actionType=='APPROVE' || actionType=='VIEW'" ><label for="isRcm">Whether RCM applicable</label>
                     </span>   
                </div>
                 <div data-ng-if="viewPr.advanceAmount === null">
                    <div class="col s2" data-ng-if="selectedTdsLedger === null && selectedTdsRate === null ">
                        <input type="button" value="Apply LDC" class="btn" data-target="applyLdc" data-ng-disabled="actionType=='APPROVE' || actionType=='VIEW'" modal/>
                        <p>Applied Ldc Tds Rate : {{selectedLdc.ldcTdsRate}} %</p>
                        <p>Applied Ldc Tds Section : {{selectedLdc.ldcTdsSection}}</p>
                        <p>Applied Ldc Tds Certificate No : {{selectedLdc.ldcCertificateNo}}</p>
                       </div>
                 </div>  
                
                   <div class="col s2">
                    <label>Select GST Rate :</label>
                    <select   data-ng-disabled="actionType=='APPROVE' || actionType=='VIEW'"  id="gstRate" name="gstRate" data-ng-model="selectedGstRate" data-ng-change="applyGstRate(selectedGstRate,false,false)" data-ng-options="g as g.name for g in gstRates"></select>
                     <p><b>Selected Gst Rate : {{selectedGstRate.name}}</b></p>   
                </div> 
                <div data-ng-if="viewPr.advanceAmount === null">
                    <div class="col s3"  data-ng-if="selectedLdc === null">
                        <label>Select TDS Section :</label>
                        <select  id="TDS" name="TDS" data-ng-disabled="actionType=='VIEW'"  data-ng-model="selectedTdsLedger" data-ng-change="applySelectedTds(selectedTdsLedger)"  data-ng-options="tds as tds.ledgerName for tds in tdsLedger"></select>
                        <p><b>Selected Tds : {{selectedTdsLedger.ledgerName}} </b></p>     
                    </div>
                </div>
                  <div data-ng-if="viewPr.advanceAmount === null">
                    <div class="col s3"  data-ng-if="selectedLdc === null">
                        <label>Select TDS Rate :</label>
                        <select  id="TDS" name="TDS" data-ng-disabled=" actionType=='VIEW'"  data-ng-model="selectedTdsRate" data-ng-change="applySelectedTdsRate(selectedTdsRate)"  data-ng-options="t as t.name for t in tdsRates"></select>
                        <p><b>Selected Tds Rate: {{selectedTdsRate.name}} </b></p>
                    </div>
                  </div>
               
                </div>
        </div>
        <div class="row" data-ng-if="prRequestType.code != 'ADVANCE_PAYMENT'">
            <div class="col s12 standardView">
                <table class="table bordered striped" style="border: #ccc 1px solid;" id="downloadedData">
                    <tr>
                        <th>Sku Id</th>
                        <th>Sku Name</th>
                        <th data-ng-if="viewPr.type == 'GOODS_RECEIVED'">Asset Tag</th>
                        <th data-ng-if="viewPr.type=='SERVICE_RECEIVED'">BCC Id</th>
                        <th data-ng-if="viewPr.type=='SERVICE_RECEIVED'">Unit Id</th>
                        <th data-ng-if="viewPr.type=='SERVICE_RECEIVED'">Unit Name</th>
                        <th data-ng-if="viewPr.type=='SERVICE_RECEIVED'">Budget Category</th>
                        <th data-ng-if="viewPr.type=='SERVICE_RECEIVED'">Section</th>
                        <th data-ng-if="viewPr.type!='SERVICE_RECEIVED'">Category</th>
                        <th data-ng-if="viewPr.type!='SERVICE_RECEIVED'">Sub Category</th>
                        <th data-ng-if="viewPr.type=='SERVICE_RECEIVED'">Description</th>
                        <th>Packaging</th>
                        <th>Quantity</th>
                        <th>Price</th>
                        <th>Tax</th>
                        <th>Total Tax</th>
                        <th>Total Amount</th>
                        <th>Deviations</th>
                    </tr>
                    <tr data-ng-repeat="item in viewPr.paymentInvoice.paymentInvoiceItems track by item.paymentInvoiceItemId">
                        <td>{{item.skuId}}</td>
                        <td>{{item.skuName}}[{{item.hsn}}]({{item.skuDate | date:'dd-MM-yyyy'}} To {{item.toSkuDate | date:'dd-MM-yyyy'}})</td>
                        <td data-ng-if="viewPr.type == 'GOODS_RECEIVED'">
                            <a data-ng-click="showAssetTags(item,item.assetTags)" data-ng-if="item.assetTags.length==1">{{item.assetTags[0]}}</a>
                            <a data-ng-click="showAssetTags(item,item.assetTags)" data-ng-if="item.assetTags.length>1">{{item.assetTags[0]}} +{{item.assetTags.length-1}}More</a>
                            <a data-ng-if="item.assetTags==null || item.assetTags==undefined || item.assetTags.length<1"> - </a>
                        </td>
                        <td data-ng-if="viewPr.type=='SERVICE_RECEIVED'">{{item.businessCostCenterId}}</td>
                        <td data-ng-if="viewPr.type=='SERVICE_RECEIVED'">{{item.bccCode}}</td>
                        <td data-ng-if="viewPr.type=='SERVICE_RECEIVED'">{{item.businessCostCenterName}}</td>
                        <td data-ng-if="viewPr.type=='SERVICE_RECEIVED'">{{item.budgetCategory}}
                            <br>
                                              
                        </td>
                        <td data-ng-if="viewPr.type=='SERVICE_RECEIVED'">{{item.section}}</td>
                        <td data-ng-if="viewPr.type!='SERVICE_RECEIVED'">{{item.category}}</td>
                        <td data-ng-if="viewPr.type!='SERVICE_RECEIVED'">{{item.subCategory}}</td>
                        <td data-ng-if="viewPr.type=='SERVICE_RECEIVED'">{{item.costDescription}}</td>
                        <td>{{item.packagingName}}</td>
                        <td>{{item.quantity}}</td>
                        <td>{{item.unitPrice}}</td>
                        <td>
                            <span data-ng-repeat="tax in item.taxes">{{tax.taxType}}@{{tax.taxPercentage}}%,</span>
                        </td>
                        <td>{{item.totalTax}}</td>
                        <td>{{item.totalAmount}}</td>
                        <td>
                                <span data-ng-repeat="d in item.deviations" class="deviationTag {{d.currentStatus}}"
                                      tooltipped
                                      data-position="bottom" data-delay="10"
                                      data-tooltip="{{d.deviationRemark}} ## {{d.actionRemark}}">
                                    {{d.paymentDeviation.deviationDetail}}
                                    <!--<span data-ng-if="actionType=='UPDATE_INVOICE'" style="cursor: pointer;"
                                          data-ng-click="deleteDeviation(item.deviations, $index)">&times</span>-->
                                </span>
                            <input type="button" value="Manage" class="btn btn-small"
                                   data-ng-show="(actionType=='APPROVE' || actionType=='ACKNOWLEDGED')"
                                   data-ng-click="setAvailableDeviations(item, 'INVOICE_ITEM')"
                                   data-target="manageDevModal" modal/>
                            <!--<input type="button" value="Add" class="btn btn-small"
                                   data-ng-show="actionType=='UPDATE_INVOICE'"
                                   data-ng-click="setAvailableDeviations(item, 'INVOICE_ITEM')"
                                   data-target="addDevModal" modal/>-->
                        </td>
                    </tr>
                </table>
            </div>
            <div class="TableMobileView">
                <ul class="collection center" id="downloadedData">
                    <li class="collection-item"
                        data-ng-repeat="item in viewPr.paymentInvoice.paymentInvoiceItems track by item.paymentInvoiceItemId">
                        <div class="row">
                            <div class="col">Sku Id</div>
                            <div class="col">{{item.skuId}}</div>
                        </div>
                        <div class="row">
                            <div class="col">Sku Name</div>
                            <div class="col">{{item.skuName}}[{{item.hsn}}]({{item.skuDate | date:'dd-MM-yyyy'}} To {{item.toSkuDate | date:'dd-MM-yyyy'}})</div>
                        </div>
                        <div class="row" data-ng-if="viewPr.type=='SERVICE_RECEIVED'">
                            <div class="col" data-ng-if="viewPr.type=='SERVICE_RECEIVED'">Unit Id</div>
                            <div class="col" data-ng-if="viewPr.type=='SERVICE_RECEIVED'">{{item.businessCostCenterId}}</div>
                        </div>
                        <div class="row" data-ng-if="viewPr.type=='SERVICE_RECEIVED'">
                            <div class="col" data-ng-if="viewPr.type=='SERVICE_RECEIVED'">Unit Name</div>
                            <div class="col" data-ng-if="viewPr.type=='SERVICE_RECEIVED'">{{item.businessCostCenterName}}</div>
                        </div>
                        <div class="row" data-ng-if="viewPr.type=='SERVICE_RECEIVED'">
                            <div class="col" data-ng-if="viewPr.type=='SERVICE_RECEIVED'">Budget Category</div>
                            <div class="col" data-ng-if="viewPr.type=='SERVICE_RECEIVED'">{{item.budgetCategory}}</div>
                        </div>
                        <div class="row" data-ng-if="viewPr.type=='SERVICE_RECEIVED'">
                            <div class="col" data-ng-if="viewPr.type=='SERVICE_RECEIVED'">Section</div>
                            <div class="col" data-ng-if="viewPr.type=='SERVICE_RECEIVED'">{{item.section}}</div>
                        </div>
                        <div class="row" data-ng-if="viewPr.type=='SERVICE_RECEIVED'">
                            <div class="col" data-ng-if="viewPr.type!='SERVICE_RECEIVED'">Category</div>
                            <div class="col" data-ng-if="viewPr.type!='SERVICE_RECEIVED'">{{item.category}}</div>
                        </div>
                        <div class="row" data-ng-if="viewPr.type=='SERVICE_RECEIVED'">
                            <div class="col" data-ng-if="viewPr.type!='SERVICE_RECEIVED'">Sub Category</div>
                            <div class="col" data-ng-if="viewPr.type!='SERVICE_RECEIVED'">{{item.subCategory}}</div>
                        </div>
                        <div class="row" data-ng-if="viewPr.type=='SERVICE_RECEIVED'">
                            <div class="col" data-ng-if="viewPr.type=='SERVICE_RECEIVED'">Description</div>
                            <div class="col" data-ng-if="viewPr.type=='SERVICE_RECEIVED'">{{item.cosdivescription}}</div>
                        </div>
                        <div class="row">
                            <div class="col">Packaging</div>
                            <div class="col">{{item.packagingName}}</div>
                        </div>
                        <div class="row">
                            <div class="col">Quantity</div>
                            <div class="col">{{item.quantity}}</div>
                        </div>
                        <div class="row">
                            <div class="col">Price</div>
                            <div class="col">{{item.unitPrice}}</div>
                        </div>
                        <div class="row">
                            <div class="col">Tax</div>
                            <div class="col">
                                <span data-ng-repeat="tax in item.taxes">{{tax.taxType}}@{{tax.taxPercentage}}%,</span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">Total Tax</div>
                            <div class="col">{{item.totalTax}}</div>
                        </div>
                        <div class="row" data-ng-if="viewPr.type=='SERVICE_RECEIVED' && (actionType=='APPROVE')">
                            <div class="col" data-ng-if="viewPr.type=='SERVICE_RECEIVED' && (actionType=='APPROVE')">
                                <table style="widdiv: 100%;">
                                    <tr>
                                        <div class="col" colspan="2" align="center">divS Rate</div>
                                    </tr>
                                    <tr>
                                        <div class="col">
                                            <input type="number" class="form-control" style="widdiv: 40px;"
                                                   data-ng-model="checkBoxModal.updateddivsRate" />
                                        </div>
                                        <div class="col">
                                            <input type="checkbox" style="widdiv: 33px; height: 20px; position:inherit; opacity:1"
                                                   data-ng-model="checkBoxModal.checkAlldivsRate"
                                                   data-ng-click="changeAlldivsRate()" />
                                        </div>
                                    </tr>
                                </table>
                            </div>
                            <div class="col" data-ng-if="viewPr.type=='SERVICE_RECEIVED' && (actionType=='APPROVE')">
                                <input data-ng-change="changeProposedAmount(item)" style="widdiv:40px;" type="number"
                                       data-ng-model="item.divsRate" step="0.01" />
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">Total Amount</div>
                            <div class="col">{{item.totalAmount}}</div>
                        </div>
                        <div class="row">
                            <div class="col">Deviations</div>
                            <div class="col">
                    <span data-ng-repeat="d in item.deviations" class="deviationTag {{d.currentStatus}}" tooltipped
                          data-position="bottom" data-delay="10"
                          data-tooltip="{{d.deviationRemark}} ## {{d.actionRemark}}">
                        {{d.paymendiveviation.deviationDetail}}
                        <!--<span data-ng-if="actionType=='UPDATE_INVOICE'" style="cursor: pointer;"
                          data-ng-click="deleteDeviation(item.deviations, $index)">&times</span>-->
                    </span>
                                <input type="button" value="Manage" class="btn btn-small"
                                       data-ng-show="(actionType=='APPROVE' || actionType=='ACKNOWLEDGED')"
                                       data-ng-click="setAvailableDeviations(item, 'INVOICE_ITEM')" data-target="manageDevModal"
                                       modal />
                                <!--<input type="button" value="Add" class="btn btn-small"
                               data-ng-show="actionType=='UPDATE_INVOICE'"
                               data-ng-click="setAvailableDeviations(item, 'INVOICE_ITEM')"
                               data-target="addDevModal" modal/>-->
                            </div>
                    </li>
                </ul>
            </div>
        </div>
        <div class="row" data-ng-if="isGstInputAvailed && !isRcm">
            <div class="col s6">
                <div style="background: #efefef; border: #ddd 1px solid; padding: 10px;font-size: 14px;">
                    <p><b><u>Gst Input Availed Amounts </u></b></p>
                    <p><b>Basic Amount : </b> {{reverseBasicAmount}}</p>
                    <p><b>Total Tax : </b> {{reverseTax}}</p>
                    <p><b>Total Amount : </b> {{reverseTotalAmount}}</p>
                </div>
            </div>
          </div>  
        <div class="row">
            <div class="col s6">
                <div style="background: #efefef; border: #ddd 1px solid; padding: 10px;font-size: 14px;">
                    <p><b>Created By:</b> {{viewPr.createdBy.name}}</p>
                    <p data-ng-if="prRequestType.shortCode=='GR'"><b>Requesting Unit:</b> {{viewPr.requestingUnit.name}}</p>
                    <p data-ng-if="prRequestType.shortCode=='SR'"><b>Requesting Unit:</b>{{requestingUnitsForView}}</p>
                    <p><b>Basic Amount:</b> {{viewPr.paymentInvoice.calculatedInvoiceAmount}}</p>
                    <p><b>Other Charges:</b> {{viewPr.paymentInvoice.extraCharges}}</p>
                    <p data-ng-if="viewPr.extraChargesType != null"><b>Other Charges Type:</b> {{viewPr.extraChargesType}}</p>
                    <p><b>Total Bill Amount:</b> {{viewPr.paymentInvoice.paymentAmount}}</p>
                    <p><b>Proposed Amount:</b> {{viewPr.proposedAmount}}</p>
                    <p data-ng-if="isRcm"><b>Paid Amount (RCM Applicable) : </b> {{rcmPaidAmount}}</p>
                </div>
            </div>
            <div class="col s6" data-ng-show="actionType!='UPDATE_INVOICE'">
                <label data-ng-show=" prRequestType.code != 'ADVANCE_PAYMENT'">Category Wise Amount:</label>
                <input type="button" value="Summary" class="btn btn-small" data-ng-show=" prRequestType.code != 'ADVANCE_PAYMENT'"
                       data-ng-click="clubAmountModal(viewPr)"/>
                <p data-ng-show=" prRequestType.code != 'ADVANCE_PAYMENT'"><label>Invoice Number:</label> {{viewPr.paymentInvoice.invoiceNumber}}</p>
                <p><label>Invoice Date:</label> {{viewPr.paymentInvoice.invoiceDate | date: 'dd-MM-yyyy hh:mm:ss a'}}</p>
                <p>
                    <label>Load Invoice:</label>
                    <input data-ng-if="uploadedDocData==null" type="button" value="Load" class="btn btn-small"
                           data-ng-click="loadInvoiceDoc()"/>
                    <input type="button" value="Preview Invoice" data-ng-if="uploadedDocData!=null"
                           data-ng-click="previewPRInvoice(uploadedDocData)" class="btn btn-small"
                           data-target="invoicePreviewModal" modal/>
                    <input type="button" value="Download Invoice" data-ng-if="uploadedDocData!=null"
                           data-ng-click="downloadPRInvoice(uploadedDocData)" class="btn btn-small"/>
                </p>
                <p>
                    <label data-ng-if="viewPr.type=='SERVICE_RECEIVED'"> Load Documents:</label>
                    <input data-ng-if="viewPr.type=='SERVICE_RECEIVED'" type="button" value="Load Mandatory Documents" class="btn btn-large-medium" data-target="openMandatoryReqDocModal" modal />
                </p>
            </div>
            <!--<div class="col s6" data-ng-show="actionType=='UPDATE_INVOICE'">
                <p><label>Invoice Number:</label> <input type="text"
                                                         data-ng-model="viewPr.paymentInvoice.invoiceNumber"/></p>
                <p><label>Invoice Date:</label> <input data-ng-if="actionType=='UPDATE_INVOICE'" input-date
                                                       type="text" ng-model="viewPr.paymentInvoice.invoiceDate"
                                                       format="yyyy-mm-dd"/></p>
                <p>
                    <label>Attach Invoice:</label>
                    <input type="button" value="Scan Doc" class="btn btn-small" data-target='scanModal' modal
                           data-ng-click="resetScanModal()"/>
                    <input type="button" value="Snapshot" class="btn btn-small" data-target='snapModal' modal
                           data-ng-click="resetSnapModal()"/>
                    <input type="button" value="Upload File" data-ng-click="uploadDoc()" class="btn btn-small"/>
                    <a data-ng-if="uploadedDocData!=null" target="_blank"
                       data-ng-href="{{uploadedDocData.fileUrl}}">Invoice View</a>
                </p>
            </div>-->
        </div>
        <div class="row" data-ng-if="viewPr.paymentCard != null">
            <div class="col s3">
                <u><label for="paymentCard">Payment Card</label></u>
                <p id="paymentCard">{{viewPr.paymentCard}}</p>
            </div>
            <div class="col s3">
                <u><label for="cardPaymentTransactionNumber">Transaction Number</label></u>
                <p id="cardPaymentTransactionNumber">{{viewPr.cardPaymentTransactionNumber}}</p>
            </div>
            <div class="col s3">
                <u><label>Card Payment Comment</label></u>
                <textarea data-ng-model="viewPr.cardPaymentComment" style="resize: none" data-ng-disabled="true" data-ng-if="viewPr.cardPaymentComment != null"></textarea>
                <p data-ng-if="viewPr.cardPaymentComment == null">-</p>
            </div>
            <div class="col s3" style="margin-top: 5px">
                <button class="btn btn-xs-small vBtn margin-right-5" style="margin-top: 15px"
                        data-ng-click="downloadDocumentById(viewPr.cardPaymentProof)">Download Proof</button>
            </div>
        </div>
        <div class="row">
            <div class="col s6">
                <p data-ng-if="viewPr.paymentInvoice.deviations.length>0 || actionType!='VIEW'">Deviations:</p>
                <span data-ng-repeat="d in viewPr.paymentInvoice.deviations"
                      class="deviationTag {{d.currentStatus}}" tooltipped
                      data-position="bottom" data-delay="10"
                      data-tooltip="{{d.deviationRemark}} ## {{d.actionRemark}}">
                        {{d.paymentDeviation.deviationDetail}}
                    <!--<span data-ng-if="actionType=='UPDATE_INVOICE'" style="cursor: pointer;"
                          data-ng-click="deleteDeviation(viewPr.paymentInvoice.deviations, $index)">&times</span>-->
                    </span>
                <input type="button" data-ng-show="(actionType=='APPROVE' || actionType=='ACKNOWLEDGED')"
                       value="Manage deviation" class="btn"
                       data-target='manageDevModal'
                       data-ng-click="setAvailableDeviations(viewPr.paymentInvoice, 'INVOICE')" modal/>
                <!--<input type="button" data-ng-show="actionType=='UPDATE_INVOICE'" value="Add deviation" class="btn"
                       data-target='addDevModal'
                       data-ng-click="setAvailableDeviations(viewPr.paymentInvoice, 'INVOICE')" modal/>-->
            </div>
            <div class="col s6">
                <p data-ng-if="viewPr.paymentInvoice.rejections.length>0 || actionType!='VIEW'">Rejections:</p>
                <span class="deviationTag"
                      data-ng-repeat="r in viewPr.paymentInvoice.rejections">
                            {{r.paymentDeviation.deviationDetail}} :  {{r.paymentDeviation.deviationRemark}}
                            <span style="cursor: pointer;font-size: 24px;"
                                  data-ng-show="(actionType=='APPROVE' || actionType=='ACKNOWLEDGED')"
                                  data-ng-click="deleteRejection(viewPr.paymentInvoice.rejections, $index)">&times;</span>
                        </span>
                <input type="button" value="Manage rejection" class="btn"
                       data-ng-show="(actionType=='APPROVE' || actionType=='ACKNOWLEDGED')"
                       data-ng-click="openAddRejectionModal()"/>
            </div>
        </div>
        <!--<div class="row">
            <div class="col s6" data-ng-show="actionType=='APPROVE'">
                <input type="button" class="btn" value="Get Payment Dates" data-ng-click="getProposedPaymentDates()"
                       data-target="calendarModal" modal/>
                <label data-ng-if="selectedPaymentDate!=null">Selected Payment Cycle</label>
                <table class="table striped" style="border:#ddd 1px solid;" data-ng-if="selectedPaymentDate!=null">
                    <thead>
                    <tr>
                        <th>Cycle</th>
                        <th>Payment Date</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>{{selectedPaymentDate.name}} - {{selectedPaymentDate.cycleTag}}</td>
                        <td>{{selectedPaymentDate.paymentDate | date:'dd-MM-yyyy hh:mm:ss a'}}</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>-->
        <div class="row">
            <div class="col s6">
                <span data-ng-if="isRcm">Paid Amount (RCM applicable) : {{rcmPaidAmount}}</span>
                <label>Paid Amount</label>
                <input type="text" data-ng-model="viewPr.paidAmount" data-ng-disabled="viewPr.type == 'ADVANCE_PAYMENT'" data-ng-change="setPaidAmount(viewPr.paidAmount)"
                       data-ng-show="(actionType=='APPROVE' || actionType=='ACKNOWLEDGED') && viewPr.debitNote==null"/>
                <span data-ng-show="viewPr.debitNote!=null || actionType=='VIEW'">{{viewPr.paidAmount}}</span>
                <button class="btn btn-xs-small"
                        data-ng-show="(actionType=='APPROVE' || actionType=='ACKNOWLEDGED') && viewPr.paidAmount!=viewPr.proposedAmount && viewPr.debitNote==null"
                        data-ng-click="resetPaidAmount()">Reset
                </button>
                <a href="#advancePaymentModal" modal data-ng-click="openAdvancePaymentModal()" data-ng-show="advancePayment != null" data-ng-if="!useAdvance && viewPr.debitNote == null && viewPr.type != 'ADVANCE_PAYMENT'">
                    <input type="button" value="Use Advance" class="btn btn-xs-small" data-ng-if="!useAdvance && viewPr.debitNote == null && viewPr.type != 'ADVANCE_PAYMENT'"/> </a>
                <input type="button" value="Clear Advance" class="btn btn-xs-small" data-ng-if="useAdvance && viewPr.debitNote == null && viewPr.type != 'ADVANCE_PAYMENT'" data-ng-click="clearUseAdvance()"/>
                <span data-ng-show="advancePayment != null" data-ng-if="useAdvance && viewPr.advanceAmount != null && advanceUsageType != null && viewPr.debitNote == null && advancePayment != null">
                    Advance type : {{advanceUsageType}} - {{viewPr.advanceAmount}}
                </span>
            </div>
            <div class="col s6" data-ng-show="(actionType=='APPROVE' || actionType=='ACKNOWLEDGED')">
                <div class="col s4" data-ng-if="(viewPr.debitNote == null && viewPr.paidAmount<viewPr.proposedAmount)">
                    <label>Upload Debit Note</label>
                    <input type="button" value="Upload File" data-ng-click="uploadDebitNote()" class="btn btn-small" />
                </div>
                <div class="col s3" data-ng-if="(uploadedDebitDoc != null && viewPr.debitNote == null)">
                    <label>Download Debit Note</label>
                    <input type="button" value="Download"
                           data-ng-click="downloadDebitNote(uploadedDebitDoc)" class="btn btn-small" />
                </div>
                <div class="col s4" data-ng-if="(viewPr.debitNote != null && viewPr.debitNoteDocumentDetail != null)">
                    <label>Print Debit Note</label>
                    <input type="button" value="Print"
                           data-ng-click="printDebitNote(viewPr.debitNoteDocumentDetail)" class="btn btn-small" />
                </div>
                <input type="button" class="btn" value="Add debit note"
                       style="margin-top: 20px;"
                       data-ng-if="(viewPr.debitNote == null && viewPr.paidAmount<viewPr.proposedAmount && debitNoteDocId != null)"
                       data-ng-click="addDebitNoteModal()"/>
            </div>
        </div>
        <div class="row">
            <div class="col s6" data-ng-show="actionType=='VIEW'">
                <label>Remarks:</label>
                {{viewPr.remarks}}
            </div>
            <div class="col s6" data-ng-if="viewPr.debitNote!=null">
                <label style="margin-top: 5px;">Debit Note Detail:</label>
                <table class="table striped" style="border: #ccc 1px solid;">
                    <thead>
                    <tr>
                        <th>Amount</th>
                        <th>Tax</th>
                        <th>Total</th>
                        <th>Advance Amount</th>
                        <th>Extra Debit Note Amount</th>
                        <th>Created By</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>{{viewPr.debitNote.amount}}</td>
                        <td>{{viewPr.debitNote.totalTaxes}}</td>
                        <td>{{viewPr.debitNote.totalAmount}}</td>
                        <td data-ng-if="viewPr.debitNote.advanceAmount != null">{{viewPr.debitNote.advanceAmount}}</td>
                        <td data-ng-if="viewPr.debitNote.advanceAmount == null">-</td>
                        <td data-ng-if="viewPr.debitNote.advanceAmount != null">{{viewPr.debitNote.totalAmount - viewPr.debitNote.advanceAmount}}</td>
                        <td data-ng-if="viewPr.debitNote.advanceAmount == null">-</td>
                        <td>{{viewPr.debitNote.generatedBy.name}}</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="row">
            <div class="col s3">
                <input type="button" class="btn" value="View logs" data-target='viewLogsModal' modal/>
                <input type="button" class="btn" value="Payment Detail"
                       data-ng-if="actionType=='VIEW' && viewPr.currentStatus=='PAID'"
                       data-target='paymentViewModal' modal/>
            </div>
            <div class="col s3">
                <button class="btn" data-ng-click="printSelected(viewPr)"
                        data-ng-if="(actionType=='APPROVE'|| actionType=='ACKNOWLEDGED' || actionType=='VIEW') && viewPr.type=='GOODS_RECEIVED'">
                    Print PO/GR
                </button>
                <button class="btn" data-ng-click="printSelected(viewPr)"
                        data-ng-if="(actionType=='APPROVE' || actionType=='ACKNOWLEDGED' || actionType=='VIEW') && viewPr.type=='SERVICE_RECEIVED'">
                    Print SO/SR
                </button>
                <button type="button" class="btn" id="printDiv" print-btn data-ng-show="false">Print</button>
            </div>
            <!--<div class="col s6" data-ng-if="actionType=='UPDATE_INVOICE'">
                <span>
                    <input id="amountsMatch" type="checkbox" data-ng-model="viewPr.amountsMatch"/>
                    <label for="amountsMatch">Amounts match</label>
                </span>
            </div>-->
<!--            <div class="col s6" data-ng-if="actionType=='VIEW'">-->
<!--                <div data-ng-repeat="item in viewPr.requestItemMappings track by item.id"-->
<!--                     class="prItemMapping {{item.status}}">-->
<!--                    {{item.paymentRequestType}} #{{item.paymentRequestItemId}} - {{item.status}}-->
<!--                </div>-->
<!--            </div>-->
            <div class="col s3" data-ng-if="viewPr.paymentRequestQueries!= null && viewPr.paymentRequestQueries.length > 0">
                <input type="button" class="btn btn-medium" value="{{viewPr.paymentRequestQueries[0].queryResolvedByName != null ? 'Resolved Query' : 'Raised Query'}}" data-ng-click="openQueryModalView(false)"/>
            </div>
        </div>
        <div class="row" data-ng-if="viewPr.vendorAdvancePayments != null && viewPr.type != 'ADVANCE_PAYMENT'">
            <div style="background: #efefef; border: #ddd 1px solid; padding: 10px;font-size: 14px;" class="col s6">
                <p class="center"><b><u>Advance Details</u></b></p>
                <p><b>Advance Amount Used :</b> {{ viewPr.advancePayment !=null && viewPr.advancePayment.usedAmount !=null ? viewPr.advancePayment.usedAmount :  viewPr.advanceAmount}}</p>
                <p><b>{{viewPr.vendorAdvancePayments[0].advanceType == 'SO_ADVANCE' ? 'SO' : 'PO'}} Closed :</b> {{(isAdjusted || refundSelectedDate != null) ? 'Yes' : 'No'}}</p>
                <p data-ng-if="isAdjusted"><b>Adjusted With {{viewPr.vendorAdvancePayments[0].advanceType == 'SO_ADVANCE' ? 'SO' : 'PO'}} :</b> {{selectedSoPo}}</p>
                <p data-ng-if="isAdjusted"><b>Adjusted Amount :</b> {{amount}}</p>
                <p data-ng-if="refundSelectedDate != null"><b>Refund Date :</b> {{refundSelectedDate}}</p>
                <p data-ng-if="refundSelectedDate != null"><b>Refund Amount :</b> {{amount}}</p>
            </div>
        </div>
        <div class="row" data-ng-if="poSrDetails.length > 0">
            <div class="col s12">
                <table class="bordered striped">
                    <thead>
                        <tr data-ng-if="viewPr.type=='GOODS_RECEIVED'">
                            <th>S.No</th>
                            <th>GR Id</th>
                            <th>GR Creation Date</th>
                            <th>Status</th>
                            <th>PO Id</th>
                            <th>PO Creation Date</th>
                        </tr>
                        <tr data-ng-if="viewPr.type=='SERVICE_RECEIVED'">
                            <th>S.No</th>
                            <th>SR Id</th>
                            <th>SR Creation Date</th>
                            <th>Status</th>
                            <th>SO Id</th>
                            <th>SO Creation Date</th>
                            <th>Service Proof Doc</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr data-ng-if="viewPr.type=='GOODS_RECEIVED'" data-ng-repeat="item in poSrDetails track by $index">
                            <td>{{$index+1}}</td>
                            <td>{{item.grId}}</td>
                            <td>{{item.grGenerationTime| date:'dd/MM/yyyyhh:mm:ss'}}</td>
                            <td>{{item.status}}</td>
                            <td data-ng-if="item.multiplePo.length == 0">{{item.poId}}</td>
                            <td data-ng-if="item.multiplePo.length == 0">{{item.poGenerationTime| date:'dd/MM/yyyy hh:mm:ss'}}</td>
                            <td colspan="2" data-ng-if="item.multiplePo.length > 0">
                                <button class="btn btn-medium" data-ng-click="viewMultipleSoPos(item.multiplePo)">View Po's({{item.multiplePo.length}})</button>
                            </td>
                        </tr>
                        <tr data-ng-if="viewPr.type=='SERVICE_RECEIVED'" data-ng-repeat="item in poSrDetails track by $index">
                            <td>{{$index+1}}</td>
                            <td>{{item.srId}}</td>
                            <td>{{item.srGenerationTime| date:'dd/MM/yyyyhh:mm:ss'}}</td>
                            <td>{{item.status}}</td>
                            <td data-ng-if="item.multipleSo.length == 0">{{item.soId}}</td>
                            <td data-ng-if="item.multipleSo.length == 0">{{item.soGenerationTime| date:'dd/MM/yyyy hh:mm:ss'}}</td>
                            <td data-ng-if="item.multipleSo.length == 0">
                                <a class="btn btn-xs-small vBtn margin-right-5 margin-top-10"
                                   href="#descriptionModal" data-toggle="modal" data-ng-click="setSelectedSO(item)" modal>Doc Preview</a>
                            </td>
                            <td colspan="2" data-ng-if="item.multipleSo.length > 0">
                                <button class="btn btn-medium" data-ng-click="viewMultipleSoPos(item.multipleSo)">View So's({{item.multipleSo.length}})</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

        </div>
        <div class="row" ng-if="actionType=='ACKNOWLEDGED' && viewPr.currentStatus=='CREATED'"> 
            <div class="col" data-ng-if="expireSoPo.length != 0 && viewPr.type == 'SERVICE_RECEIVED'">
                 <p style="color: red;">These SO are belong to expire capex budget, SO Id's : {{expireSoPo}}</p>
            </div>
            <div class="col" data-ng-if="expireSoPo.length != 0 && viewPr.type != 'SERVICE_RECEIVED'">
               <p style="color: red;">These PO are belong to expire capex budget, PO Id's : {{expireSoPo}} </p>    
            </div>

            </div>
           <div class="row" ng-if="(actionType==='ACKNOWLEDGED' || actionType=='APPROVE')  && viewPr.isSoContractBreach==='Y'" style="padding: 20px;">
            <h5 style="color: red;" >Warning :  "SO Breach of Process", Invoice Date cannot be less than Vendor So Approval Date.</h5>
             <button class="btn" style="margin-top: 20px;" data-ng-click="downloadSoBreachApprovalDoc(viewPr.soContractBreachApprovalDoc)">Download Approval</button>
           </div> 
        </div>
        <div class="row">
            <div class="col s12" data-ng-show="(actionType=='APPROVE' || actionType=='ACKNOWLEDGED')">
                <div class="col s2">
                    <input type="button" class="btn red" value="Reject Request" data-ng-click="rejectPaymentRequest()"/>
                </div>
                <div class="col s2">
                    <input type="button" class="btn red" value="Query Request" data-ng-click="openQueryModal(true)"/>
                </div>
                <div class="col s6" data-ng-if="viewPr.paymentInvoice.extraCharges > 0 && actionType=='APPROVE'">
                    <div class="col s12">
                        <div class="col s6">
                            <label for="extraCharge">Extra Charge Type* (Rs. {{viewPr.paymentInvoice.extraCharges}})</label>
                            <select id="extraCharge" data-ng-model="extraChargeType" data-ng-options="type as type for type in extraChargeTypes" data-ng-change="setExtraChargeType(extraChargeType)"></select>
                        </div>
                        <div class="col s6" data-ng-if="extraChargeType === 'FREIGHTAGE_WITH_TAX'">
                            <span class="bold">Freightage Without Tax is Rs. {{freightageWithoutTax}}</span>
                            <hr>
                            <table class="bordered striped">
                                <thead class="bold header">
                                    <tr>
                                        <td>Tax Type</td>
                                        <td>Tax Amount</td>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr data-ng-repeat="extraChargeTax in extraChargeTaxes">
                                        <td>
                                            {{extraChargeTax.type}}
                                        </td>
                                        <td>
                                            <input type="number" data-ng-model="extraChargeTax.taxAmount" data-ng-change="changeFreightageTax(extraChargeTax.taxAmount)">
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="col {{actionType=='APPROVE' && viewPr.paymentInvoice.extraCharges > 0  ? 's2' : 's8'}}">
                <input type="button" style="float: right;" class="btn green" value="Approve Request"
                       ng-if="actionType=='APPROVE'"
                       ng-debounce-click="approvePaymentRequest()"
                       debounce-delay="1500"/>
                <input type="button" style="float: right;" class="btn green" data-ng-disabled="" value="ACKNOWLEDGED"
                       ng-if="actionType=='ACKNOWLEDGED' && viewPr.currentStatus=='CREATED' && allowAck === true "
                       data-ng-click="changeStatus(viewPr,'ACKNOWLEDGED')"/>
                </div>
            </div>
            <!--<div class="col s12" data-ng-show="actionType=='UPDATE_INVOICE'">
                <input type="button" class="btn" value="Update Request" data-ng-click="updatePaymentRequest()"/>
            </div>-->
        </div>
    </div>

</div>
<div id="descriptionModal" class="modal fade" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Service Proof Doc</h4>
            </div>
            <div class="modal-body">
                <table>
                    <tbody>
                    <tr data-ng-repeat="d in serviceProof.serviceProofDoc track by $index">
                        <td> <b>DOC ID :: {{$index+1}}</b></td>
                        <td><a href="{{d.fileUrl}}" target="_blank">
                            <div>
                                Preview
                            </div>
                        </a>
                        </td>
                    </tr>
                    </tbody>
                </table>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default modal-close" data-dismiss="modal">Close</button>
            </div>
        </div>

    </div>
</div>
<div id="manageDevModal" class="modal">
    <div class="modal-content">
        <h5>Item deviations</h5>
        <table data-ng-if="selectedItemForDev.deviations.length>0">
            <thead>
            <tr>
                <th>Deviation</th>
                <th>Action Remarks</th>
                <th>Action</th>
            </tr>
            </thead>
            <tbody>
            <tr data-ng-repeat="d in selectedItemForDev.deviations track by $index">
                <td>{{d.paymentDeviation.deviationDetail}} - {{d.deviationRemark}} [{{d.currentStatus}}]</td>
                <td><input type="text" data-ng-model="d.actionRemark"/></td>
                <td>
                    <input type="button" class="btn btn-xs-small" value="Accept"
                           data-ng-if="d.currentStatus!='ACCEPTED'" data-ng-click="deviationAction(d,'APPROVE')"/>
                    <input type="button" class="btn btn-xs-small" value="Reject"
                           data-ng-if="d.currentStatus!='REJECTED'" data-ng-click="deviationAction(d,'REJECT')"/>
                    <input type="button" class="btn btn-xs-small" value="Remove"
                           data-ng-if="d.currentStatus!='REMOVED' && d.addType!='NEW'"
                           data-ng-click="deviationAction(d,'REMOVE')"/>
                    <input type="button" class="btn btn-xs-small" value="Delete" data-ng-if="d.addType=='NEW'"
                           data-ng-click="deleteDeviation(selectedItemForDev.deviations,$index)"/>
                </td>
            </tr>
            </tbody>
        </table>
        <p>Available deviations:</p>
        <div class="row" data-ng-repeat="d in availableDevs track by $index">
            <div class="col s4">{{d.data.deviationDetail}}</div>
            <div class="col s7"><input type="text" data-ng-model="d.remark"/></div>
            <div class="col s1">
                <span>
                    <input type="checkbox" id="id-{{$index}}" data-ng-model="d.checked"/>
                    <label for="id-{{$index}}"></label>
                </span>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close"
                data-ng-click="addDeviations(selectedItemForDev, selectedItemForDevType)">Add
        </button>
        <button class="btn btn-small modal-action modal-close red" style="margin-right: 20px;">Close</button>
    </div>
</div>
<div id="applyLdc" class="modal">
    <div class="modal-content">
          <table>
            <thead>
                <th>LDC ID</th>
                <th>LDC Limit</th>
                <th>LDC Remaining Limit</th>
                <th>LDC Validity</th>
                <th>Tds Section</th>
                <th>Tds Rate</th>
                <th>Certificate No</th>
                <th>Action</th>
            </thead>
            <tbody> 
                <tr data-ng-repeat="item in viewPr.applicableLdc" > 
                    <td>{{item.ldcId}}</td>
                    <td>{{item.ldcLimit}}</td>
                    <td>{{item.remainingLimit}}</td>
                    <td>{{item.ldcTenureFrom | date:'yyyy-MM-dd' }} TO {{item. ldcTenureTo | date:'yyyy-MM-dd'}}</td>
                    <td>{{item.ldcTdsSection}}</td>
                    <td>{{item.ldcTdsRate}}</td>
                    <td>{{item.ldcCertificateNo}}</td>
                    <td><button class="btn btn-primary" data-ng-click="applySelectedLdc(item)" >Apply</button></td>
                </tr>
            </tbody>
          </table>
          <div data-ng-if="selectedLdc !=null">
            <h5>Applied LDC : </h5>
            <table>
              <thead>
                  <th>LDC ID</th>
                  <th>LDC Limit</th>
                  <th>LDC Remaining Limit</th>
                  <th>LDC Validity</th>
                  <th>Tds Section</th>
                  <th>Tds Rate</th>
                  <th>Certificate No</th>
              </thead>
              <tbody> 
                  <tr> 
                      <td>{{selectedLdc.ldcId}}</td>
                      <td>{{selectedLdc.ldcLimit}}</td>
                      <td>{{selectedLdc.remainingLimit}}</td>
                      <td>{{selectedLdc.ldcTenureFrom | date:'yyyy-MM-dd' }} TO {{selectedLdc. ldcTenureTo | date:'yyyy-MM-dd'}}</td>
                      <td>{{selectedLdc.ldcTdsSection}}</td>
                      <td>{{selectedLdc.ldcTdsRate}}</td>
                      <td>{{selectedLdc.ldcCertificateNo}}</td>
                  </tr>
              </tbody>
            </table>
          </div>
          
         
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close red" style="margin-right: 20px;">Close</button>
    </div>
</div>
<div id="openMandatoryReqDocModal" class="modal">
    <div class="modal-content">
        <h5>Required Documents</h5>
        <div class="row" data-ng-repeat="doc in mandatoryDocuments">
            <div class="col s3">{{doc.name}}</div>  <a href={{doc.code}} target="_blank" download><button class="btn btn-small">Download</button></a>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close red" style="margin-right: 20px;">Close</button>
    </div>

</div>
<!--<div id="addDevModal" class="modal">
    <div class="modal-content">
        <h5>Item deviations</h5>
        <div class="row" data-ng-repeat="d in availableDevs track by $index">
            <div class="col s4">{{d.data.deviationDetail}}</div>
            <div class="col s7"><input type="text" data-ng-model="d.remark"/></div>
            <div class="col s1">
                <span>
                    <input type="checkbox" id="ad-{{$index}}" data-ng-model="d.checked"/>
                    <label for="ad-{{$index}}"></label>
                </span>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close"
                data-ng-click="addDeviations(selectedItemForDev)">Submit
        </button>
        <button class="btn btn-small modal-action modal-close red" style="margin-right: 20px;">Close</button>
    </div>
</div>-->

<script type="text/ng-template" id="addRejectionModal.html">
    <div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title" id="modal-title-1" style="margin-top: 0px;">Add deviations</h3>
        <hr>
    </div>
    <div class="modal-body" id="modal-body-2">
        <div class="col" data-ng-repeat="d in availableRejections">
            <div class="row">
            <div class="col s6">{{d.data.deviationDetail}}:</div>
            <div class="col s3">
                <span>
                    <input id="ir-{{$index}}" type="checkbox" data-ng-model="d.checked"/>
                    <label for="ir-{{$index}}"></label>
                </span>
                </div>
            </div>

            <div data-ng-if="d.checked && d.data.paymentDeviationId===34" class="row" data-ng-repeat="m in mandatoryDocuments">
                <div class="col s6">{{m.name}}:</div>
                <div class="col s3">
                <span>
                    <input id="sub-ir-{{$index}}" type="checkbox" data-ng-model="m.checked" data-ng-click="addToDeviationRemark(d,m)"     />
                    <label for="sub-ir-{{$index}}"></label>
                </span>
        </div>
    </div>
        </div>
        </div>
    <div >
        <button class="btn btn-small " data-ng-click="addRejections()">Submit</button>
<!--        <button class="btn btn-small modal-action modal-close red" data-ng-click="addRejections()"-->
<!--                style="margin-right: 20px;">Close-->
<!--        </button>-->
    </div>
</div>
</script>

<div id="viewLogsModal" class="modal">
    <div class="modal-content">
        <h4>Log detail</h4>
        <div class="row">
            <div class="col s12">
                Logs:
                <ul>
                    <li data-ng-repeat="log in viewPr.requestLogs"
                        style="background: #a5e9fd;border:1px solid #8fd5f7;padding:5px;margin: 5px 0;">
                        {{log.updateTime | date:'dd-MM-yyyy hh:mm:ss a'}} : {{log.logData}}
                    </li>
                </ul>
            </div>
            <div class="col s12">
                Status logs:
                <ul>
                    <li data-ng-repeat="log in viewPr.statusLogs"
                        style="background: #78ece8;border:#06b5ab 1px solid;padding:5px;margin: 5px 0;">
                        {{log.updateTime | date:'dd-MM-yyyy hh:mm:ss a'}} : {{log.fromStatus}} to {{log.toStatus}} by
                        {{log.updatedBy.name}}
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close">Close</button>
    </div>
</div>
<!--
<div id="snapModal" class="modal">
    <div class="modal-content">
        <h4>Take Snapshot</h4>
        <video data-ng-show="snapRunning" id="video" width="640" height="480" autoplay></video>
        <br/>
        <button data-ng-click="startSnap()" class="btn btn-small">Start</button>
        <button data-ng-click="snapPicture()" class="btn btn-small">Snap Photo</button>
        <br/>
        <canvas data-ng-hide="snapRunning" id="canvas" width="640" height="480"></canvas>
        <br/>
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close" data-ng-click="uploadFile()">Upload</button>
        <button class="btn btn-small modal-action modal-close red" style="margin-right: 20px;">Cancel</button>
    </div>
</div>

<div id="scanModal" class="modal">
    <div class="modal-content">
        <h3>Scan document</h3>
        <button type="button" data-ng-click="scanToPng()">Scan</button>
        <div id="images" style="margin-top: 20px;"></div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close" data-ng-click="uploadScannedFile()">Upload</button>
        <button class="btn btn-small modal-action modal-close red" style="margin-right: 20px;">Cancel</button>
    </div>
</div>-->

<div id="calendarModal" class="modal">
    <div class="modal-content">
        <label>Select Payment Cycle</label>
        <select ui-select2 id="getProposedPaymentDates" name="getProposedPaymentDates"
                data-ng-model="selectedPaymentDate"
                data-ng-options="item as item.name for item in proposedPaymentDates track by item.id"></select>
        <table data-ng-if="selectedPaymentDate!=null">
            <thead>
            <tr>
                <th>Cycle</th>
                <th>Payment Date</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td>{{selectedPaymentDate.name}} - {{selectedPaymentDate.cycleTag}}</td>
                <td>{{selectedPaymentDate.paymentDate | date:'dd-MM-yyyy hh:mm:ss a'}}</td>
            </tr>
            </tbody>
        </table>
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close">Close</button>
    </div>
</div>

<div id="paymentSheetModal" class="modal">
    <div class="modal-content">
        <div class="row">
            <div class="col s12">
                <p>
                    <label>Selected Company : </label> {{selectedCompany.name}}
                </p>
            </div>

            <div class="col s12">
                <label class="black-text" for="selectedBank">Select Bank</label> <select
                    ui-select2="selectedBank" id="selectedBank" name="bankList"
                    data-ng-model="selectedBank" data-ng-change="changeBank()"
                    data-ng-options="bank as bank.name + ' - ' + bank.accountNumber for bank in bankList"></select>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn modal-action modal-close" data-ng-click="downloadPaymentSheet()">SEND Payment Sheet
        </button>
        <button class="btn modal-action modal-close red" style="margin-right: 20px;">Close</button>
    </div>
</div>

<div id="paymentSettleModal" class="modal">
    <div class="modal-content">
        <div class="row">
            <div class="col s12">
                <p>
                    <label>Selected Company : </label> {{selectedCompany.name}}
                </p>
                <p>
                    <label class="black-text" for="selectedBank">Select Payment Bank*</label> <select
                        ui-select2="selectedBank" id="selectedBankADHoc" name="bankList"
                        data-ng-model="selectedBank" data-ng-change="changeBank()"
                        data-ng-options="bank as bank.name + ' - ' + bank.accountNumber for bank in bankList"></select>
                </p>
                <p>
                    <label>Beneficiary Account Number*</label>
                    {{paymentDetail.beneficiaryAccountNumber}}
                </p>
                <p>
                    <label>Beneficiary IFSC Code*</label>
                    {{paymentDetail.beneficiaryIfscCode}}
                </p>
                <p>
                    <label>Debit Account Number*</label>
                    {{paymentDetail.debitAccount}}
                </p>
                <p>
                    <label>Payment Type*</label>
                    <select ui-select2 id="ptList" name="ptList" data-ng-model="selectedPaymentType"
                            data-ng-options="item as item.name for item in paymentTypeList track by item.id"></select>
                </p>
                <p>
                    <label>Paid Amount*</label>
                    <input type="text" data-ng-model="paymentDetail.paidAmount"/>
                </p>
                <p>
                    <label>Payment Date*</label>
                    <input input-date type="text" data-ng-model="paymentDetail.paymentDate" container=""
                           format="yyyy-mm-dd"/>
                </p>
                <p>
                    <label>Remarks</label>
                    <input type="text" data-ng-model="paymentDetail.remarks"/>
                </p>
                <p>
                    <label>UTR Number*</label>
                    <input type="text" data-ng-model="paymentDetail.utrNumber"/>
                </p>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn modal-action modal-close" data-ng-click="settlePaymentRequestSingle()">Settle Payment
            Requests
        </button>
        <button class="btn modal-action modal-close red" style="margin-right: 20px;">Close</button>
    </div>
</div>

<div id="paymentViewModal" class="modal">
    <div class="modal-content">
        <div class="row">
            <div class="col s12">
                <p>
                    <label>Payment Bank</label>
                    {{viewPr.paymentDetail.debitBank}}
                </p>
                <p>
                    <label>Debit Account Number</label>
                    {{viewPr.paymentDetail.debitAccount}}
                </p>
                <p>
                    <label>Beneficiary Name</label>
                    {{viewPr.paymentDetail.vendorName}}
                </p>
                <p>
                    <label>Beneficiary Account Number</label>
                    {{viewPr.paymentDetail.beneficiaryAccountNumber}}
                </p>
                <p>
                    <label>Beneficiary IFSC Code</label>
                    {{viewPr.paymentDetail.beneficiaryIfscCode}}
                </p>
                <p>
                    <label>Payment Type</label>
                    {{viewPr.paymentDetail.paymentType}}
                </p>
                <p>
                    <label>Paid Amount</label>
                    {{viewPr.paymentDetail.paidAmount}}
                </p>
                <p>
                    <label>Payment Date</label>
                    {{viewPr.paymentDetail.paymentDate | date:'dd-MM-yyyy hh:mm:ss a'}}
                </p>
                <p>
                    <label>Remarks</label>
                    {{viewPr.paymentDetail.remarks}}
                </p>
                <p>
                    <label>UTR Number</label>
                    {{viewPr.paymentDetail.utrNumber}}
                </p>
                <p>
                    <label>TDS</label>
                    {{viewPr.paymentDetail.tdsAmount}}
                </p>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn modal-action modal-close red">Close</button>
    </div>
</div>
<div id="filingModal" class="modal">
    <div class="modal-content">
        <div class="row">
            <div class="col s12">
                <label>Filing Number</label>
                <input type="text" data-ng-model="filingNumber"/>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn modal-action modal-close" ng-click="updateFileNumber(filingNumber)">Submit</button>
        <button class="btn modal-action modal-close red" style="margin-right: 20px;">Close</button>
    </div>
</div>

<div id="reasonModal" class="modal">
    <div class="modal-content">
        <div class="row">
            <div class="col s12">
                <label>Reason*</label>
                <input type="text" data-ng-model="actionReason"/>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn modal-action modal-close" data-ng-click="submitAction()">Submit</button>
        <button class="btn modal-action modal-close red" style="margin-right: 20px;">Close</button>
    </div>
</div>

<div id="invoicePreviewModal" class="modal">
    <div class="modal-content">
        <div class="row" style="margin-bottom: 0px;">
            <div class="col s12">
                <div id="invoicePreview" style="max-height: 370px; overflow: auto;"></div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn modal-action modal-close" style="margin-right: 20px;">Close</button>
    </div>
</div>

<div id="adhocPaymentSheetModal" class="modal">
    <div class="modal-content">
        <div class="row" style="margin-bottom: 0px;">
            <div class="col s12">
                <label>PR Ids (comma separated)</label>
                <input type="text" data-ng-model="adhocPrIds"/>
            </div>
            <div class="col s12">
                <label>Bank Name*</label>
                <input type="text" data-ng-model="bankNameAdhoc"/>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn modal-action modal-close" data-ng-click="downloadPaymentSheetAdhoc()">Submit</button>
        <button class="btn modal-action modal-close" style="margin-right: 20px;">Close</button>
    </div>
</div>

<div id="advancePaymentModal" class="modal">
    <div class="modal-content">
        <h4>Total Available Advance Payment : {{advancePayment.availableAmount}}</h4>
        <div class="row">
            <div class="col s5">
                <label for="advanceUsageType">Select Advance Usage Type :*</label>
                <select id="advanceUsageType" data-ng-model="advanceUsageType" data-ng-change="setAdvanceUsageType(advanceUsageType)">
                    <option value="Amount">Amount</option>
                    <option value="Percentage">Percentage</option>
                    <option value="Complete">Complete</option>
                </select>
            </div>
            <div class="col s7">
                <input type="number" style="margin-top: 20px;" id="percentage" placeholder="Enter Percentage" data-ng-model="viewPr.percentage" data-ng-change="setEnteredPercentage(viewPr.percentage)" data-ng-if="advanceUsageType != null && advanceUsageType == 'Percentage'">
                <input type="number" style="margin-top: 20px;" id="advance" placeholder="Enter Advance Amount" data-ng-model="viewPr.advanceAmount" data-ng-change="setEnteredAmountPercent(viewPr.advanceAmount)" data-ng-disabled="advanceUsageType != 'Amount'">
            </div>
        </div>
<!--        <p data-ng-if="viewPr.advanceAmount != null && viewPr.advanceAmount != ''">Balance Amount After Using Advance: {{parseFloat(parseFloat(advancePayment.availableAmount) - parseFloat(viewPr.advanceAmount)).toFixed(6)}}</p>-->
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action left red modal-close" data-ng-click="cancelAdvancePayment()">Cancel</button>
        <button class="btn btn-small modal-action modal-close" data-ng-click="submitAdvance()">Submit</button>
    </div>
</div>

<!--printable section for service received and goods received-->
<div style="width:100%" id="printSection">
    <div ng-show="srFlag">
        <div class="col s12">
            <div class="row" style="margin-bottom: 5px;">
                <div class="col s12">
                    <p style="text-align: center;"><b><span
                            style="font-size: 16.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">CHAAYOS<br/>
					</span></b><b><span
                            style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">{{currentPrintSR.company.name}}<br/>
					</span></b><span
                            style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">
                        {{currentPrintSR.companyAddress.line1}},
                        {{currentPrintSR.companyAddress.line2}}, <br/> {{currentPrintSR.companyAddress.city}},
						{{currentPrintSR.companyAddress.state}}, <br/> {{currentPrintSR.companyAddress.country}},
						{{currentPrintSR.companyAddress.zipCode}}<br/>
					</span><b><span style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">Service Receipt (SR)</span></b>
                    </p>
                </div>
            </div>
            <div id="expandedPOView1" class="row custom-listing-li" style="padding:5px;">
                <div class="col s12" data-ng-if="currentPrintSR.serviceReceiveItems!=null">
                    <table style="margin-bottom: 20px;">
                        <thead class="itemsTable">
                        <tr>
                            <th style="width: 20%">Vendor Details</th>
                            <th style="width: 30%"></th>
                            <th style="width: 20%">Dispatch Details</th>
                            <th style="width: 30%"></th>
                        </tr>
                        </thead>
                        <tbody class="itemsTable borderLess" style="border: #000 1px solid;">
                        <tr>
                            <td>Vendor Name:</td>
                            <td>{{currentPrintSR.vendor.name}}</td>
                            <td>Dispatch Location</td>
                            <td>{{currentPrintSR.location.name}}</td>
                        </tr>
                        <tr>
                            <td>Vendor Id:</td>
                            <td>{{currentPrintSR.vendor.id}}</td>
                            <td>Dispatch Address</td>
                            <td>{{currentPrintSR.dispatchAddress.line1}}, {{currentPrintSR.dispatchAddress.line2}},
                                {{currentPrintSR.dispatchAddress.city}},
                                {{currentPrintSR.dispatchAddress.state}}, {{currentPrintSR.dispatchAddress.country}},
                                {{currentPrintSR.dispatchAddress.zipcode}}
                            </td>
                        </tr>
                        <tr>
                            <td>Date of SR:</td>
                            <td>{{currentPrintSR.creationTime | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</td>
                            <td>Dispatch State</td>
                            <td>{{currentPrintSR.dispatchAddress.state}}</td>
                        </tr>
                        <tr>
                            <td>SR No.:</td>
                            <td>{{currentPrintSR.id}}</td>
                            <td>Dispatch State Code</td>
                            <td>{{currentPrintSR.dispatchAddress.stateCode}}</td>
                        </tr>
                        </tbody>
                    </table>
                    <table style="margin-bottom: 20px;">
                        <thead class="itemsTable">
                        <tr>
                            <th style="width: 20%">Receiving Company Name</th>
                            <th style="width: 80%">{{currentPrintSR.company.name}}</th>
                        </tr>
                        </thead>
                        <tbody class="itemsTable borderLess" style="border: #000 1px solid;">
                        <tr>
                            <td>Received By:</td>
                            <td>{{currentPrintSR.createdBy.name}}</td>
                        </tr>
                        <tr>
                            <td>Receiving Time:</td>
                            <td>{{currentPrintSR.creationTime | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</td>
                        </tr>
                        <tr>
                            <td>Delivery State:</td>
                            <td>{{currentPrintSR.deliveryState.name}}</td>
                        </tr>
                        <tr>
                            <td>Delivery State Code:</td>
                            <td>{{currentPrintSR.deliveryState.code}}</td>
                        </tr>
                        </tbody>
                    </table>
                    <h5 style="margin-top:0px;font-size: 18px;">List of Elements Received</h5>
                    <div class="row margin0 itemsTable">
                        <table class="bordered" style="border-top: 1px solid #d0d0d0;">
                            <thead>
                            <tr>
                                <th class="center-align">Element ID</th>
                                <th class="center-align">Element Name</th>
                                <th class="center-align">Cost Center</th>
                                <th class="center-align">Description</th>
                                <th class="center-align">Price</th>
                                <th class="center-align">Qty</th>
                                <th class="center-align">Total</th>
                                <th class="center-align">Taxes</th>
                                <th class="center-align">Amount</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr data-ng-repeat="item in currentPrintSR.serviceReceiveItems track by $index">
                                <td class="center-align">{{item.costElementId}}</td>
                                <td class="center-align">{{item.costElementName}} [{{item.ascCode}}]</td>
                                <td class="center-align">{{item.businessCostCenterName}}</td>
                                <td class="center-align">{{item.serviceDescription}}</td>
                                <td class="center-align">{{item.unitPrice}}</td>
                                <td class="center-align">{{item.receivedQuantity}}</td>
                                <td class="center-align">{{item.totalCost}}</td>
                                <td class="center-align">
                                    <span>{{item.totalTax}}</span>
                                    (<span ng-repeat="tax in item.taxes">{{tax.taxName}}@{{tax.percentage}}%</span>)
                                </td>
                                <td class="center-align">{{item.totalAmount}}</td>
                            </tr>
                            <tr>
                                <th></th>
                                <th><b>Total</b></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th class="center-align">{{currentPrintSR.totalAmount}}</th>
                            </tr>
                            <tr>
                                <th>Created By</th>
                                <th>{{currentPrintSR.createdBy.name}}</th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="col s12">
                    <div class="row margin0">
                        <ul class="col s12">
                            <h5 style="margin-top:0px;font-size: 18px;">List of Attached Service Orders</h5>
                            <li class="row margin0"
                                data-ng-repeat="so in currentPrintSR.serviceOrderList track by so.id">
                                <div class="poNumber">
                                    {{so.id}}
                                    <span style="margin-left: 50px;font-weight:bold;">Date: {{so.generationTime | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</span>
                                    <span class="chip right">{{so.status}}</span>
                                </div>
                                <table class="bordered itemsTable" style="margin-bottom: 20px;">
                                    <thead>
                                    <tr>
                                        <th class="center-align">Cost Element ID</th>
                                        <th class="center-align">Cost Element Name</th>
                                        <th class="center-align">UOM</th>
                                        <th class="center-align">Pending</th>
                                        <th class="center-align">Received</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr data-ng-repeat="item in so.orderItems | filter : filteredByReceivedQuantity track by $index ">
                                        <td>{{item.costElementId}}</td>
                                        <td class="">{{item.costElementName}} [{{item.ascCode}}]</td>
                                        <td class="center-align">{{item.unitOfMeasure}}</td>
                                        <td class="center-align">
                                            {{(item.requestedQuantity - item.receivedQuantity).toFixed(2)}}
                                        </td>
                                        <td class="center-align">
                                            {{item.receivedQuantity!=null ? item.receivedQuantity.toFixed(2) : 0}}
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </li>
                        </ul>
                    </div>
                </div>
                <p style="font-size:12px;">Certified that the particulars and the amount indicated given above are true
                    and correct.</p>
                <div style="width: 250px;border:#000 1px solid;float:right;">
                    <div style="height:150px"></div>
                    <div style="border-top:#000 1px solid;text-align:center;">Authorised Signatory</div>
                </div>
            </div>
        </div>
    </div>
    <div ng-show="grFlag">
        <div class="col s12">
            <div class="row" style="margin-bottom: 5px;">
                <div class="col s12">
                    <p style="text-align: center;"><b><span
                            style="font-size: 16.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">CHAAYOS<br/>
					</span></b><b><span

                            style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">{{companyMap[currentPrintGR.companyId].name}}<br/>
					</span></b><span
                            style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">{{companyMap[currentPrintGR.companyId].registeredAddress.line1}},
						{{companyMap[currentPrintGR.companyId].registeredAddress.line2}}, <br/> {{companyMap[currentPrintGR.companyId].registeredAddress.city}},
						{{companyMap[currentPrintGR.companyId].registeredAddress.state}}, <br/> {{companyMap[currentPrintGR.companyId].registeredAddress.country}},
						{{companyMap[currentPrintGR.companyId].registeredAddress.zipCode}}<br/>
					</span><b><span style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">Goods Receipt (GR)</span></b>
                    </p>
                </div>
            </div>
            <p style="text-align:center;font-size:21px;margin:0;font-weight:bold;"
               data-ng-if="currentPrintGR.dispatchLocation.gstStatus!='REGISTERED'">Vendor Not Registered</p>
            <div id="expandedPOView" class="row custom-listing-li" style="padding:5px;">
                <div class="col s12" data-ng-if="currentPrintGR.grItems!=null">
                    <table style="margin-bottom: 20px;">
                        <thead class="itemsTable">
                        <tr>
                            <th style="width: 20%">Vendor Details</th>
                            <th style="width: 30%"></th>
                            <th style="width: 20%">Dispatch Details</th>
                            <th style="width: 30%"></th>
                        </tr>
                        </thead>
                        <tbody class="itemsTable borderLess" style="border: #000 1px solid;">
                        <tr>
                            <td>Vendor Name:</td>
                            <td>{{currentPrintGR.generatedForVendor.name}}</td>
                            <td>Dispatch Location</td>
                            <td>{{currentPrintGR.dispatchLocation.city}}</td>
                        </tr>
                        <tr>
                            <td>Vendor Id:</td>
                            <td>{{currentPrintGR.generatedForVendor.id}}</td>
                            <td>Dispatch Address</td>
                            <td>{{currentPrintGR.dispatchLocation.address.line1}},
                                {{currentPrintGR.dispatchLocation.address.line2}},
                                {{currentPrintGR.dispatchLocation.address.city}},
                                {{currentPrintGR.dispatchLocation.address.state}},
                                {{currentPrintGR.dispatchLocation.address.country}},
                                {{currentPrintGR.dispatchLocation.address.zipcode}}
                            </td>
                        </tr>
                        <tr>
                            <td>Date of GR:</td>
                            <td>{{currentPrintGR.generationTime | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</td>
                            <td>Dispatch State</td>
                            <td>{{currentPrintGR.dispatchLocation.state}}</td>
                        </tr>
                        <tr>
                            <td>GR No.:</td>
                            <td>{{currentPrintGR.id}}</td>
                            <td>Dispatch State Code</td>
                            <td>{{currentPrintGR.dispatchLocation.address.stateCode}}</td>
                        </tr>
                        </tbody>
                    </table>
                    <table style="margin-bottom: 20px;">
                        <thead class="itemsTable">
                        <tr>
                            <th style="width: 20%">Receiving Unit Name</th>
                            <th style="width: 20%">{{currentPrintGR.deliveryUnitId.name}}</th>
                            <th style="width: 20%">Receiving Company Name</th>
                            <th style="width: 40%">{{companyMap[currentPrintGR.companyId].name}}</th>
                        </tr>
                        <tr>
                            <th style="width: 20%">Receiving Details</th>
                            <th style="width: 30%"></th>
                            <th style="width: 20%">Document Details</th>
                            <th style="width: 30%"></th>
                        </tr>
                        </thead>
                        <tbody class="itemsTable borderLess" style="border: #000 1px solid;">
                        <tr>
                            <td>Received By:</td>
                            <td>{{currentPrintGR.generatedBy.name}}</td>
                            <td>Document Type</td>
                            <td>{{currentPrintGR.receiptType}}</td>
                        </tr>
                        <tr>
                            <td>Receiving Time:</td>
                            <td>{{currentPrintGR.generationTime | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</td>
                            <td>Document No.</td>
                            <td>{{currentPrintGR.receiptNumber}}</td>
                        </tr>
                        <tr>
                            <td>Delivery State:</td>
                            <td>{{currentPrintGR.deliveryUnitId.state}}</td>
                            <td>Document Date</td>
                            <td>{{currentPrintGR.grDocumentDate | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</td>
                        </tr>
                        <tr>
                            <td>Delivery State Code:</td>
                            <td>{{currentPrintGR.deliveryUnitId.stateCode}}</td>
                            <td></td>
                            <td></td>
                        </tr>
                        </tbody>
                    </table>
                    <h5 style="margin-top:0px;font-size: 18px;">List of Items Received</h5>
                    <div class="row margin0 itemsTable">
                        <table class="bordered" style="border-top: 1px solid #d0d0d0;">
                            <thead>
                            <tr>
                                <th class="center-align">SKU ID</th>
                                <th class="center-align">SKU</th>
                                <th class="center-align">Category</th>
                                <th class="center-align">Sub Category</th>
                                <th class="center-align">Price</th>
                                <th class="center-align">Pkg</th>
                                <th class="center-align">Qty</th>
                                <th class="center-align">Total</th>
                                <th class="center-align">Taxes</th>
                                <th class="center-align">Amount</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr data-ng-repeat="(skuId,item) in currentPrintGR.grItems track by $index">
                                <td>{{item.skuId}}</td>
                                <td>{{item.skuName}} [{{item.packagingName}}]</td>
                                <td class="center-align">{{item.category}}</td>
                                <td class="center-align">{{item.subCategory}}</td>
                                <td class="center-align">{{item.unitPrice}}</td>
                                <td class="center-align">{{item.packagingName}}</td>
                                <td class="center-align">{{(item.receivedQuantity/item.conversionRatio).toFixed(2)}}
                                </td>
                                <td class="center-align">{{item.totalCost}}</td>
                                <td class="center-align">
                                    <span>{{item.totalTax}}</span>
                                    (<span ng-repeat="tax in item.taxes">{{tax.taxName}}@{{tax.percentage}}%</span>)
                                </td>
                                <td class="center-align">{{item.amountPaid}}</td>
                            </tr>
                            <tr>
                                <th></th>
                                <th><b>Extra Charges</b></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th class="center-align">{{currentPrintGR.extraCharges.toFixed(2)}}</th>
                            </tr>
                            <tr>
                                <th></th>
                                <th><b>Total</b></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th class="center-align">{{currentPrintGR.total}}</th>
                            </tr>
                            <tr>
                                <th></th>
                                <th><b>Created By</b></th>
                                <th>{{currentPrintGR.generatedBy.name}}</th>
                                <th><b>Approved By</b></th>
                                <th>{{currentPrintGR.approvedBy.name}}</th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <p style="text-align:right;margin-right:20px;font-weight:bold;">Amounts Match:
                        {{currentPrintGR.amountMatched?'Yes':'No'}}</p>
                </div>

                <div class="col s12">
                    <div class="row margin0">
                        <ul class="col s12">
                            <h5 style="margin-top:0px;font-size: 18px;">List of PO</h5>
                            <li class="row margin0"
                                data-ng-repeat="po in currentPrintGR.purchaseOrderList track by po.id">
                                <div class="poNumber">
                                    {{po.receiptNumber}}
                                    <span style="margin-left: 50px;font-weight:bold;">Date: {{po.initiationTime | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</span>
                                    <span class="chip right">{{po.status}}</span>
                                </div>
                                <table class="bordered itemsTable" style="margin-bottom: 20px;">
                                    <thead>
                                    <tr>
                                        <th class="center-align">SKU ID</th>
                                        <th class="center-align">SKU</th>
                                        <th class="center-align">UOM</th>
                                        <th class="center-align">Pending</th>
                                        <th class="center-align">Received Packaging</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr data-ng-repeat="item in po.orderItems | filter : filteredByReceivedQuantity track by $index ">
                                        <td>{{item.skuId}}</td>
                                        <td class="">{{item.skuName}} [{{item.packagingName}}]</td>
                                        <td class="center-align">{{item.unitOfMeasure}}</td>
                                        <td class="center-align">
                                            {{((item.requestedQuantity -
                                            item.receivedQuantity)/item.conversionRatio).toFixed(2)}}
                                        </td>
                                        <td class="center-align">
                                            {{item.receivedQuantity!=null ?
                                            (item.receivedQuantity/item.conversionRatio).toFixed(2) : 0}}
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </li>
                        </ul>
                    </div>
                </div>
                <p style="font-size:12px;">Certified that the particulars and the amount indicated given above are true
                    and correct.</p>
                <div style="width: 250px;border:#000 1px solid;float:right;">
                    <div style="height:150px"></div>
                    <div style="border-top:#000 1px solid;text-align:center;">Authorised Signatory</div>
                </div>
            </div>
        </div>
    </div>
</div>


<script type="text/ng-template" id="clubAmountModal.html">
    <div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title" id="modal-title" style="margin-top: 0px;">Summary</h3>
        <hr>
    </div>
    <div class="modal-body" id="modal-body">
        <div class="row">
            <div class="col s12">
                <table class="table bordered striped" style="border: #ccc 1px solid;">
                    <thead>
                    <tr>
                        <th>Category</th>
                        <th data-ng-if="summaryInvoicesList.type!='SERVICE_RECEIVED'">Sub Category</th>
                        <th data-ng-if="summaryInvoicesList.type=='SERVICE_RECEIVED'">Budget Category</th>
                        <th>Total Tax</th>
                        <th>Amount</th>
                        <th>Total Amount</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr data-ng-repeat="items in summaryInvoicesList">
                        <td >{{items.category}}</td>
                        <td data-ng-if="summaryInvoicesList.type!='SERVICE_RECEIVED'">{{items.subCategory}}</td>
                        <td data-ng-if="summaryInvoicesList.type=='SERVICE_RECEIVED'">{{items.budgetCategory}}</td>
                        <td >{{items.tdsRate}}</td>
                        <td >{{items.totalPrice}}</td>
                        <td>{{items.packagingPrice}}</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</script>


<script type="text/ng-template" id="addDebitNoteModal.html">
    <div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title" style="margin-top: 0px;" >Add debit note</h3> <span data-ng-if="viewPr.type == 'GOODS_RECEIVED'">Remaining Amount for Category,SubCategory division : {{remainingAmount}}</span>
        <hr>
    </div>
    <div class="modal-body">
        <div class="row">
                        <div class="col s12">
                            <label>Amount:</label>
                            <input type="number" id="debitNoteAmount" data-ng-model="debitNote.amount" data-ng-change="calculateTotalAmount()" data-ng-disabled="true"/>
                        </div>
                        <div class="col s12">
                            <label>Tax:</label>
                            <input type="number" id="debitNoteTaxes" data-ng-model="debitNote.totalTaxes" data-ng-change="calculateTotalAmount()" data-ng-disabled="true"/>
                        </div>
                        <div class="col s12">
                            <label>Busy Reference Number*:</label>
                            <input type="text" data-ng-model="debitNote.busyReferenceNumber"/>
                        </div>
                        <div class="col s12">
                            <label>Total Amount:</label> {{debitNote.totalAmount}}
                        </div>
                    </div>
        <hr>
        <div class="row" data-ng-if="viewPr.type == 'GOODS_RECEIVED'">
            <div class="row">
            <div class="col s6">
                <label>Select Category And Sub Category</label>
                <select data-ng-model="debitedFrom"
                        data-ng-options="item as item.cat_subCat for item in categorySubCategoryList"
                        data-ng-change="setDebitedFrom(debitedFrom)"></select>
            </div>
            <div class="col s3">
                <label>Enter Amount</label>
                <input type="number" data-ng-model="enteredAmount" />
            </div>
            <div class="col s3">
                <button class="btn btn-medium right" style="margin-top: 15px;" data-ng-click="addCategorySubCategory(debitedFrom,enteredAmount)">Add</button>
            </div>
            </div>
            <div class="row" data-ng-if="addedCategorySubCategory != {} && addedItems.length > 0">
                <div class="col s12">
                    <table class="table bordered striped" style="border: #ccc 1px solid;">
                        <thead>
                        <tr>
                            <th>S.No</th>
                            <th>Category SubCategory</th>
                            <th>Amount</th>
                            <th>Action</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat="(catKey,item) in addedCategorySubCategory track by $index">
                            <td>{{$index+1}}</td>
                            <td>{{item.cat_subCat}}</td>
                            <td>{{item.amount}}</td>
                            <td><button class="btn btn-medium red" style="margin-top: 15px;" data-ng-click="removeItem(item)" data-ng-disabled="isAutoDistributed">Remove</button></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        </div>
        <div class="row">
            <button class="btn red" data-ng-click="closeModal({'isSubmitted' : false})">Close</button>
            <button class="btn btn-medium right" data-ng-click="submitDebitNote()">Submit
            </button>
        </div>

</script>

<script type="text/ng-template" id="viewPoSo.html">
    <div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title" id="modal-title" style="margin-top: 0px;" data-ng-if="typeReceived.type == 'GOODS_RECEIVED'">List Of PO's</h3>
        <h3 class="modal-title" id="modal-title" style="margin-top: 0px;" data-ng-if="typeReceived.type == 'SERVICE_RECEIVED'">List Of SO's</h3>
        <hr>
    </div>
    <div class="modal-body" id="modal-body">
        <div class="row">
            <div class="col s12">
                <table class="table bordered striped" style="border: #ccc 1px solid;">
                    <thead>
                    <tr data-ng-if="typeReceived.type == 'SERVICE_RECEIVED'">
                        <th>S.No</th>
                        <th>SO Id</th>
                        <th>So Generation Time</th>
                    </tr>
                    <tr data-ng-if="typeReceived.type == 'GOODS_RECEIVED'">
                        <th>S.No</th>
                        <th>PO Id</th>
                        <th>PO Generation Time</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr data-ng-if="typeReceived.type == 'SERVICE_RECEIVED'" data-ng-repeat="item in poSo track by $index">
                        <td>{{$index+1}}</td>
                        <td>{{item.soId}}</td>
                        <td>{{item.soGenerationTime | date:'dd/MM/yyyy hh:mm:ss'}}</td>
                    </tr>
                    <tr data-ng-if="typeReceived.type == 'GOODS_RECEIVED'" data-ng-repeat="item in poSo track by $index">
                        <td>{{$index+1}}</td>
                        <td>{{item.poId}}</td>
                        <td>{{item.poGenerationTime | date:'dd/MM/yyyy hh:mm:ss'}}</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="row">
            <button class="right btn red" data-ng-click="closeModal()">Close</button>
        </div>
    </div>

</script>

<script type="text/ng-template" id="prQueryModal.html">
    <div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title" id="pr-query-title" style="margin-top: 0px;">Payment request Query ..!</h3>
        <hr>
    </div>
    <div class="modal-body" id="pr-query-body" data-ng-if="isEdit">
        <div class="col" data-ng-repeat="query in queryReasons track by $index">
            <div class="row">
                <div class="col s5">{{query.deviationDetail}}:</div>
                <div class="col s2">
                <span>
                    <input id="query-{{$index}}" type="checkbox" data-ng-model="query.checked" data-ng-change="setSelectedQuery(query, query.checked)"/>
                    <label for="query-{{$index}}"></label>
                </span>
                </div>
                <div class="col s5">
                    <textarea data-ng-model="query.raisedByComment" style="resize: none" data-ng-disabled="!query.checked"></textarea>
                    <span data-ng-if="query.raisedByComment.length>0">{{500-query.raisedByComment.length}} Characters Remaining</span>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-body" id="pr-query-body-2" data-ng-if="!isEdit">
        <div class="row">
            <p style="font-weight: bold">Raised By : {{pr.paymentRequestQueries[0].queryRaisedByName}} on {{pr.lastQueriedDate | date: 'yyyy-MM-dd hh:mm:ss'}}</p>
            <p style="font-weight: bold" data-ng-if="pr.paymentRequestQueries[0].queryResolvedByName != null">Resolved By : {{pr.paymentRequestQueries[0].queryResolvedByName}}
             on {{pr.lastQueryResolvedDate | date: 'yyyy-MM-dd hh:mm:ss'}}</p>
        </div>
        <div class="row" data-ng-repeat="query in pr.paymentRequestQueries track by $index">

            <div class="row">
                <p style="font-weight: bold"><u>{{query.paymentDeviationDetail}}</u></p>
            </div>
            <div class="col s12">
                <div class="col s4">
                    <label>Raised By comment: </label>
                    <textarea data-ng-model="query.raisedByComment" style="resize: none" data-ng-disabled="true"></textarea>
                </div>
                <div class="col s4">
                    <label>Resolved By Response : </label>
                    <textarea data-ng-model="query.resolvedByComment" style="resize: none" data-ng-disabled="true"></textarea>
                </div>
                <div class="col s4 center margin-top-20" data-ng-if="query.documentDetail !=null">
                    <button class="btn btn-small" data-ng-click="downloadQueryDoc(query.documentDetail)">Download</button>
                </div>
            </div>
            <hr>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-medium red left" data-ng-click="close()">Close</button>
        <button class="btn btn-medium right" data-ng-click="raiseQuery()" data-ng-if="isEdit">Raise Query</button>
    </div>
    </div>
</script>

<script type="text/ng-template" id="showAssetTagsModal.html">
    <div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title" style="margin-top: 0px;">Asset Tags</h3>
        <hr>
    </div>
    <div class="modal-body">
        <div class="row" data-ng-if="tagIds.length>0" data-ng-repeat="tag in tagIds track by $index">
            <h6>{{tag.toString()}}</h6>
        </div>
        <div class="row" data-ng-if="tagIds.length==0" >
            <h5>No Tags Available</h5>
        </div>
    </div>
    <div class="modal-footer">
        <div class="row">
            <button class="right btn red" data-ng-click="closeAssetModal()">Close</button>
        </div>
    </div>
</script>


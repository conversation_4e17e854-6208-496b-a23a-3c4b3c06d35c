<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row" data-ng-init="init()">
    <div class="col s12">
        <div class="card">
            <div class="card-content">
                <div class="card-title">
                    <h5>Monk Day Close Management</h5>
                    <p>Manage monk diagnosis status for day close operations</p>
                </div>
                
                <!-- Controls Row -->
                <div class="row">
                    <div class="col s12 m4">
                        <div class="input-field">
                            <input type="text" id="businessDate" ng-model="currentBusinessDate" readonly class="validate">
                            <label for="businessDate" class="active">Current Business Date</label>
                            <i class="material-icons prefix">date_range</i>
                        </div>
                    </div>
                    <div class="col s12 m8">
                        <div style="margin-top: 25px;" class="right-align">
                            <button class="btn waves-effect waves-light blue" ng-click="refreshMonkStatus()">
                                <i class="material-icons left">refresh</i>Refresh
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Monk Status Display -->
                <div class="row" ng-if="monkDayCloseEvent">
                    <div class="col s12">
                        <div class="card-panel">
                            <h6>Event Status: 
                                <span class="chip" ng-class="{'green': monkDayCloseEvent.eventStatus === 'COMPLETED', 'orange': monkDayCloseEvent.eventStatus === 'INITIATED'}">
                                    {{monkDayCloseEvent.eventStatus}}
                                </span>
                            </h6>
                            <p>Event Type: {{monkDayCloseEvent.eventType}}</p>
                            <p>Business Date: {{monkDayCloseEvent.businessDate | date:'dd-MM-yyyy'}}</p>
                        </div>
                    </div>
                </div>

                <!-- Monk Status Cards -->
                <div class="row" ng-if="monkDayCloseEvent && monkDayCloseEvent.monkStatuses">
                    <div class="col s12">
                        <h6>Monk Status Details</h6>
                        <div ng-repeat="monkStatus in monkDayCloseEvent.monkStatuses" class="card-panel" style="margin-bottom: 10px;">
                            <div class="row valign-wrapper" style="margin-bottom: 0;">
                                <div class="col s12 m3">
                                    <strong>{{monkStatus.monkName}}</strong>
                                </div>
                                <div class="col s12 m3">
                                    <span class="chip" ng-class="{
                                        'grey': monkStatus.monkStatus === 'MONK_DOWN',
                                        'red': monkStatus.monkStatus === 'FAILED',
                                        'green': monkStatus.monkStatus !== 'MONK_DOWN' && monkStatus.monkStatus !== 'FAILED'
                                    }">
                                        {{getMonkStatusDisplayName(monkStatus.monkStatus)}}
                                    </span>
                                </div>
                                <div class="col s12 m3">
                                    <select class="browser-default" 
                                            ng-model="monkStatus.selectedStatus" 
                                            ng-disabled="monkStatus.monkStatus === 'MONK_DOWN' && !canUpdateMonkDown">
                                        <option ng-repeat="status in availableStatuses" 
                                                value="{{status.name}}">
                                            {{status.displayName}}
                                        </option>
                                    </select>
                                </div>
                                <div class="col s12 m3">
                                    <button class="btn waves-effect waves-light orange" 
                                            ng-click="updateMonkStatus(monkStatus)"
                                            ng-disabled="!monkStatus.selectedStatus || monkStatus.selectedStatus === '' || monkStatus.selectedStatus === monkStatus.monkStatus"
                                            style="white-space: nowrap;">
                                        <i class="material-icons left">update</i>Update
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- No Event Message -->
                <div class="row" ng-if="!monkDayCloseEvent">
                    <div class="col s12">
                        <div class="card-panel orange lighten-4">
                            <p><i class="material-icons left">warning</i>No monk day close event found for current business date. Please punch Kettle Day Close first to auto-initialize the monk day close event.</p>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    .modal1 {
        display: none; /* Hidden by default */
        position: fixed; /* Stay in place */
        z-index: 999; /* Sit on top */
        left: 0;
        top: 0;
        width: 100%; /* Full width */
        height: 100vh; /* Full height */
        overflow: auto; /* Enable scroll if needed */
        background-color: rgb(0,0,0); /* Fallback color */
        background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
    }

    /* modal1 Content/Box */
    .modal1-content {
        background-color: #fefefe;
        margin: 15% auto; /* 15% from the top and centered */
        padding: 20px;
        border: 1px solid #888;
        width: 50%; /* Could be more or less, depending on screen size */
    }

    /* The Close Button */
    .close {
        color: #aaa;
        float: right;
        font-size: 28px;
        font-weight: bold;
    }

    .close:hover,
    .close:focus {
        color: black;
        text-decoration: none;
        cursor: pointer;
    }
	.scrollabe-table {
		display: block !important;
		overflow-x: auto !important;
		white-space: nowrap !important;
	}
	.scrollabe-table thead tr th{
		min-width: 150px;
	}
    .greyBackground {
        background-color: lightgrey;
    }
</style>
<div
	class="row"
	data-ng-init="init()">
	<div class="col s12">
		<div class="row white z-depth-3 custom-listing-li">
			<div class="col s12">
				<h4>Select Stock Keeping Unit (SKU)</h4>
				<!-- <div class="input-field">
					<select
						ui-select2="selectOptions"
						data-ng-model="productId"
						data-ng-change="selectProduct(productId)"
						data-placeholder="Enter name of a product">
						<option
							data-ng-repeat="product in products"
							value="{{product.productId}}">{{product.productName}}</option>
					</select>
				</div> -->
				<div class="input-field">
                    <input type="text"
                           ng-model="searchText"
                           ng-change="filterProducts()"
                           placeholder="Enter name of a product"
                           class="autocomplete-input"/>
        
                    <!-- filtered list of products -->
                    <ul class="collection" ng-show="filteredProducts.length > 0 && searchText.length > 0" style="max-height: 250px; overflow-y: auto;">
                        <li class="collection-item"
                            ng-repeat="product in filteredProducts"
                            ng-click="selectProduct(product, product.id)">
                            {{product.name}}
                        </li>
                    </ul>
					<!-- Suggestions list -->
					<ul class="collection" ng-show="searchText.length == 0" style="max-height: 250px; overflow-y: auto;">
                        <li class="collection-item"
                            ng-repeat="product in allProducts"
                            ng-click="selectProduct(product, product.id)">
                            {{product.name}}
                        </li>
                    </ul>
                </div>
			</div>
		</div>
	</div>
	<div class="col s12">
		<ul
			class="collapsible popout"
			data-collapsible="accordion"
			watch>
			<li data-ng-repeat="skuDetail in skuList | filter: filterBasedOnStatus">
				<div
					class="collapsible-header"
					style="padding: 0px 0px 5px 0px;">
					<div class="row margin0">
						<div class="col s4">
							<h5>{{skuDetail.skuName}}</h5>
						</div>
						<div class="col s8">
							<div class="right-align">
								<button
									class="btn"
									data-ng-click="editSkuData($event,skuDetail)" acl-action="SPSUSL">EDIT</button>
								<button
									class="btn"
									data-ng-if="skuDetail.skuStatus == 'IN_ACTIVE'"
									data-ng-click="changeStatus($event,skuDetail,true)" acl-action="SPSACS">Activate</button>
								<button
									class="btn"
									data-ng-if="skuDetail.skuStatus == 'ACTIVE'"
									data-ng-click="validateForDeactivation($event,skuDetail,false)" acl-action="SPSDSL">De-Activate</button>
								<button class="btn"
										data-ng-click="setSkuImageViaUploadDoc(skuDetail)" acl-action="SPSUSL">SET IMAGE</button>
<!--								<button class="btn" data-ng-if="selectedProduct.categoryDefinition.id == 3"-->
<!--										data-ng-click="registerAsset(skuDetail)" acl-action="SPSUSL">Register Asset</button>-->
							</div>
						</div>
					</div>
				</div>
				<div
					class="collapsible-body"
					style="padding: 10px;">
					<div class="row">
						<div class="col s12">
							<blockquote
								class="flow-text"
								style="word-break: break-all;">{{skuDetail.skuDescription}}</blockquote>
						</div>
						<div class="col s12">
							<div class="row">
								<p
									class="col s4"
									style="padding: 10px 0;">
									<strong>Created On: {{skuDetail.creationDate |
										date:'dd/MM/yyyy'}}</strong>
								</p>
								<p
									class="col s4"
									style="padding: 10px 0;">
									<strong>Created By: </strong> {{skuDetail.createdBy.name}}
								</p>
								<p
									class="col s4"
									style="padding: 10px 0;">
									<strong>Unit Of Measure: </strong>{{skuDetail.unitOfMeasure}}
								</p>
								<p
									class="col s4"
									style="padding: 10px 0;">
									<strong>Unit Price: </strong>{{skuDetail.unitPrice}}
								</p>
								<p
									class="col s4"
									style="padding: 10px 0;">
									<strong>Negotiated Unit Price: </strong>{{skuDetail.negotiatedUnitPrice}}
								</p>
								<p
										class="col s4"
										style="padding: 10px 0;">
									<strong>Inventory List No.: </strong>{{skuDetail.inventoryList}}
								</p>
								<p
										class="col s4"
										style="padding: 10px 0;">
									<strong>Torqus SKU Name: </strong>{{skuDetail.torqusSkuName}}
								</p>
								<p
									class="col s4"
									style="padding: 10px 0;">
									<strong>Shelf Life (in days): </strong>{{skuDetail.shelfLifeInDays}}
								</p>
								<p
									class="col s4"
									style="padding: 10px 0;">
									<strong>SKU Description: </strong>{{skuDetail.skuDescription}}
								</p>
								<p
									class="col s4"
									style="padding: 10px 0;">
									<i
										data-ng-if="skuDetail.supportsLooseOrdering"
										class="material-icons left">done</i> <i
										data-ng-if="!skuDetail.supportsLooseOrdering"
										class="material-icons left">error</i> Supports Loose Ordering
								</p>
								<p
									class="col s4"
									style="padding: 10px 0;">
									<i
										data-ng-if="skuDetail.hasCase"
										class="material-icons left">done</i> <i
										data-ng-if="!skuDetail.hasCase"
										class="material-icons left">error</i> Contains Case Profile
								</p>
								<p
									class="col s4"
									style="padding: 10px 0;">
									<i
										data-ng-if="skuDetail.hasInner"
										class="material-icons left">done</i> <i
										data-ng-if="!skuDetail.hasInner"
										class="material-icons left">error</i> Contains Inner Profile
								</p>
								<p
									class="col s4"
									style="padding: 10px 0;">
									<i
										data-ng-if="skuDetail.isDefault"
										class="material-icons left">done</i> <i
										data-ng-if="!skuDetail.isDefault"
										class="material-icons left">error</i>Is Default SKU
								</p>
								<p
										class="col s4"
										style="padding: 10px 0;">
									<i
											data-ng-if="skuDetail.isBranded"
											class="material-icons left">done</i> <i
										data-ng-if="!skuDetail.isBranded"
										class="material-icons left">error</i>Is Branded
								</p>
								<p
										class="col s4"
										style="padding: 10px 0;">
									<strong>Vendor Ordering Discontinued From : </strong>
									<span data-ng-if="skuDetail.voDisContinuedFrom != null">{{skuDetail.voDisContinuedFrom | date:'dd-MM-yyyy'}} </span>
									<span data-ng-if="skuDetail.voDisContinuedFrom == null"> - </span>
								</p>
								<p
										class="col s4"
										style="padding: 10px 0;">
									<strong>RO Discontinued From : </strong>
									<span data-ng-if="skuDetail.roDisContinuedFrom != null">{{skuDetail.roDisContinuedFrom | date:'dd-MM-yyyy'}} </span>
									<span data-ng-if="skuDetail.roDisContinuedFrom == null"> - </span>
								</p>
							</div>
							<div class="row">
								<div class="col s12">
									<blockquote
										class="flow-text"
										style="word-break: break-all;">Attributes</blockquote>
								</div>
								<div class="col s12">
									<p
										class="col s4"
										style="padding: 10px 0;"
										ng-repeat="attribute in skuDetail.skuAttributes">
										{{getAttributeName(attribute.attributeValueId)}}</p>
								</div>
							</div>
							<div class="row">
								<div class="col s12">
									<blockquote
										class="flow-text"
										style="word-break: break-all;">Packaging List</blockquote>
								</div>
								<div class="col s12">
									<p
										class="col s4"
										style="padding: 10px 0;"
										ng-repeat="packaging in skuDetail.skuPackagings">
										{{getProfile(packaging.packagingId).packagingName}}</p>
								</div>
							</div>
							<div class="row">
								<div class="col s12">
									<blockquote
											class="flow-text"
											style="word-break: break-all;">SKU Image</blockquote>
								</div>
								<div class="col s12">
									<image data-ng-src="{{skuBaseUrl}}{{skuDetail.skuImage}}" style="max-width:500px;" />
								</div>
							</div>
						</div>
					</div>
				</div>
			</li>
		</ul>
	</div>
</div>

<div id="myModal" class="modal1">

    <div class="modal1-content" style="width: 90%;">
        <div class="modal1-header">
			<button style="background: none;border: none;float: right;" data-ng-click="reset()"><span class="close">&times;</span></button>
            <h5>Fill details to create Back Dated Asset</h5>
        </div>
        <div class="modal1-body">
            <p style="font-size: 18px;">Add Asset For Profile : {{selectedProfile.profileName}}

            </p>
			<p style="font-size: 18px;">
				Product: {{selectedProduct.productName}}
			</p>
			<p style="font-size: 18px;">
				SKU : {{selectedSKU.skuName}}
			</p>
            <div class="row">
            Asset Count -  {{assetList.length}}
				<table class="table table-bordered scrollabe-table">
					<thead>
					<tr style="background-color: darkseagreen;">
						<th>
							<div style="    text-align: center;">Select Asset:</div>
							<div style="    padding-left: 54px;">
								<input id="selectedAll"
									   data-ng-model="selectedAll" data-ng-change="selectAll(selectedAll)" type="checkbox" />
								<label class="black-text "  for="selectedAll"></label>
							</div>

						</th>
						<th data-ng-if="selectedProfile.uniqueFieldName != null">
							{{selectedProfile.uniqueFieldName}}
						</th>
						<th data-ng-repeat="entity in assetList[0].profileAttributeMappingList">
							<label class="black-text "  for="{{entity.attributeId }}">
								{{getAttributeName(entity.attributeId)}} :
							</label>
							<select data-ng-if="!entity.standAlone" id="{{entity.attributeId }}"
									data-ng-change="setValueForAll('entityAttributeValueMappings', entity.attributeValueId, true, entity.attributeId)"
									name="{{entity.attributeId }}" data-ng-model="entity.attributeValueId">
								<option data-ng-repeat="value in entity.valueList" value="{{value.attributeValueId}}">{{value.attributeValue}}</option>
							</select>
						</th>
						<th>Action: </th>
					</tr>
					</thead>
					<tbody>
					<tr ng-class="{greyBackground : asset.selected}" data-ng-repeat="asset in assetList track by $index">
						<ng-form name="innerForm">
                            <td style="padding-left: 60px;">
                                <input id="{{'selected' + $index }}"
                                       data-ng-model="asset.selected" ng-disabled="!(asset.assetStatus == 'INITIATED')" type="checkbox" />
                                <label class="black-text "  for="{{'selected' + $index }}">{{asset.assetId}}</label>
                            </td>
							<td data-ng-if="selectedProfile.uniqueFieldName != null" style="padding-left: 60px;">
								<input type="text" id="{{asset.assetId + '_' + 'unique_field_name' }}" name="uniqueFieldValue"
									   ng-readonly="!(asset.assetStatus == 'INITIATED')"
									   data-ng-model="asset.uniqueFieldValue" required />
								<p ng-show="asset.uniqueFieldValue == null" class="errorMessage">Unique Field Value is required.</p>
							</td>
							<td data-ng-repeat="entity in asset.entityAttributeValueMappings">
								<div data-ng-if="!entity.isMandatory">
									<div data-ng-if="!entity.standAlone">
										<select id="{{asset.assetId + '_' + entity.attributeId }}" ng-disabled="!(asset.assetStatus == 'INITIATED')"
												name="{{asset.assetId + '_' + entity.attributeId }}" data-ng-model="entity.attributeValueId">
											<option data-ng-repeat="value in entity.valueList" value="{{value.attributeValueId}}">{{value.attributeValue}}</option>
										</select>
									</div>
									<div data-ng-if="entity.standAlone">
										<input data-ng-model="entity.attributeValue" ng-disabled="!(asset.assetStatus == 'INITIATED')" type="text" />
									</div>
								</div>
								<div data-ng-if="entity.isMandatory">
									<!--data-ng-options="value.attributeValueId as value.attributeValue for value in entity.valueList"-->
									<div data-ng-if="!entity.standAlone">
										<select id="{{asset.assetId + '_' + entity.attributeId}}" ng-disabled="!(asset.assetStatus == 'INITIATED')"
												name="{{asset.assetId + '_' + entity.attributeId}}" data-ng-model="entity.attributeValueId"
												required>
											<option data-ng-repeat="value in entity.valueList" value="{{value.attributeValueId}}">{{value.attributeValue}}</option>
										</select>
										<p ng-show="entity.attributeValueId == null && asset.assetStatus == 'INITIATED'" class="errorMessage">Please Select a value</p>
									</div>

									<div data-ng-if="entity.standAlone">
										<input data-ng-model="entity.attributeValue" ng-disabled="!(asset.assetStatus == 'INITIATED')" type="text" required/>
										<p ng-show="entity.attributeValue == null && asset.assetStatus == 'INITIATED'" class="errorMessage">Please enter a value</p>
									</div>
								</div>
							</td>

							<td>
								<button data-ng-if="asset.assetStatus == 'INITIATED'" data-ng-click="addBackDatedAsset(asset)"
										class="btn right" acl-action="SPAAPD">
									Generate Barcode
								</button>
								<button data-ng-if="asset.assetStatus == 'CREATED'" ng-disabled="true"
										class="btn right" acl-action="SPAAPD">
									Generated
								</button>
							</td>
						</ng-form>
					</tr>


					</tbody>
				</table>
            </div>

        </div>
        <div class="modal1-footer">
            <div class="right-align">
                <button  class=" waves-effect waves-green btn" data-ng-click="reset()">
                    Cancel
                </button>
                <button data-ng-if="selectedProduct.bulkGRAllowed == true" class=" waves-green btn" data-ng-click="bulkGenerate()">
                    Bulk Generate
                </button>
            </div>
        </div>
    </div>

</div>
<div id="_assetCountModal" class="modal1">

    <div class="modal1-content">
        <div class="modal1-header">
            <button style="background: none;border: none;float: right;" data-ng-click="closeCountModal()"><span class="close">&times;</span></button>
            <h5>Enter number of Back Dated Assets you want to create</h5>
        </div>
        <div class="modal1-body">
            <p style="font-size: 18px;">Add Asset For Profile : {{selectedProfile.profileName}}

            </p>
            <p style="font-size: 18px;">
                Product: {{selectedProduct.productName}}
            </p>
            <p style="font-size: 18px;">
                SKU : {{selectedSKU.skuName}}
            </p>
            <div class="row" >
                <span>Enter number of Asset</span> <input type="number" id="_numberOfAsset" data-ng-model="assetCount.count">
            </div>
        </div>
        <div class="modal1-footer">
            <div class="right-align">
                <button  class=" waves-effect waves-green btn" data-ng-click="closeCountModal()">
                    Cancel
                </button>
                <button class=" waves-green btn" data-ng-click="createAssets()">
                    Submit
                </button>
            </div>
        </div>
    </div>

</div>

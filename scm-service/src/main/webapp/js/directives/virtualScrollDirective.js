// Virtual Scroll Directive for Large Lists Performance
angular.module('scmApp').directive('virtualScroll', function() {
    return {
        restrict: 'A',
        scope: {
            items: '=virtualScroll',
            itemHeight: '@',
            containerHeight: '@'
        },
        template: `
            <div class="virtual-scroll-container" style="height: {{containerHeight}}px; overflow-y: auto;">
                <div class="virtual-scroll-spacer-before" style="height: {{spacerBefore}}px;"></div>
                <div class="virtual-scroll-content">
                    <div ng-repeat="item in visibleItems track by $index" 
                         style="height: {{itemHeight}}px;" 
                         ng-transclude-slot="item">
                    </div>
                </div>
                <div class="virtual-scroll-spacer-after" style="height: {{spacerAfter}}px;"></div>
            </div>
        `,
        transclude: {
            item: 'virtualScrollItem'
        },
        link: function(scope, element) {
            var itemHeight = parseInt(scope.itemHeight) || 50;
            var containerHeight = parseInt(scope.containerHeight) || 400;
            var visibleCount = Math.ceil(containerHeight / itemHeight) + 2; // Buffer
            
            scope.visibleItems = [];
            scope.spacerBefore = 0;
            scope.spacerAfter = 0;
            
            var container = element.find('.virtual-scroll-container')[0];
            
            function updateVisibleItems() {
                if (!scope.items || scope.items.length === 0) {
                    scope.visibleItems = [];
                    return;
                }
                
                var scrollTop = container.scrollTop;
                var startIndex = Math.floor(scrollTop / itemHeight);
                var endIndex = Math.min(startIndex + visibleCount, scope.items.length);
                
                scope.visibleItems = scope.items.slice(startIndex, endIndex);
                scope.spacerBefore = startIndex * itemHeight;
                scope.spacerAfter = (scope.items.length - endIndex) * itemHeight;
                
                scope.$apply();
            }
            
            container.addEventListener('scroll', updateVisibleItems);
            
            scope.$watch('items', function() {
                updateVisibleItems();
            });
            
            // Initial load
            updateVisibleItems();
        }
    };
});

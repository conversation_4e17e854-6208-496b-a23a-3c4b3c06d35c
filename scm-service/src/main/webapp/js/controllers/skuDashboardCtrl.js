'use strict';

angular.module('scmApp')
.controller('skuDashboardCtrl', ['$rootScope', '$scope', 'authService', '$location', '$state',
    '$stateParams', 'apiJson', 'appUtil', '$http', '$toastService','Popeye','metaDataService','$alertService', '$fileUploadService',
    function ($rootScope, $scope, authService, $location, $state, $stateParams, apiJson, appUtil, $http, $toastService, Popeye, metaDataService, $alertService, $fileUploadService) {
        $scope.init = function() {
            $scope.skusGrid = $scope.gridOptions();
            $scope.skusGrid.data = [];
            $scope.currentUser = appUtil.getCurrentUser().userId;
            $scope.allStatus = ['INITIATED', 'CANCELLED', 'REJECTED', 'ACTIVE'];
            $scope.selectedStatus = ['INITIATED'];
            var currentDate = appUtil.getCurrentBusinessDate();
            $scope.startDate = appUtil.formatDate(currentDate, "yyyy-MM-dd");
            $scope.endDate = appUtil.formatDate(currentDate, "yyyy-MM-dd");
            $scope.getUserCreatedSkus();
        }
        $scope.multiSelectSettingStatus = {
            template: '<b> {{option}} </b>'
        };

        $scope.getUserCreatedSkus = function() {
            if($scope.startDate == "" || $scope.startDate == null) {
                $toastService.create("Start Date is mandatory");
                return;
            }
            if($scope.endDate == "" || $scope.endDate == null) {
                $toastService.create("End Date is mandatory");
                return;
            }
            var payLoad = {
                status: $scope.selectedStatus,
                startDate: $scope.startDate,
                endDate: $scope.endDate
            }
            $scope.skusGrid = $scope.gridOptions();
            $scope.skusGrid.data = [];
            $http({
                method: "POST",
                url: apiJson.urls.productManagement.getAllSkuRequests,
                data: payLoad
            }).then(function success(response) {
                if(response.data != null && response.data.length > 0) {
                    $scope.skusGrid.data = response.data;
                } else {
                    $toastService.create("No SKUs found");
                    return;
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        }

        $scope.getSkudefinition = function(skuId, callback) {
            $http({
                method: "GET",
                url: apiJson.urls.productManagement.skuDetail,
                params:{
                    skuId: skuId
                } 
            }).then (function sucess(response) {
                if(response.data != null) {
                    $scope.skuDefinition = response.data;
                    if (callback) callback();
                } else {
                    $toastService.create("Error : No SKU found");
                }
            }, function error(response) {
                $alertService.alert("Error : ", response.data.errorMsg, null, true);
            })
        }

        $scope.openSkuPage = function(sku) {
            $scope.skuDefinition = null;
            $scope.getSkudefinition(sku.skuId, function() {
                if( $scope.skuDefinition == null) {
                    return;
                }
                var role = 'VIEW_ONLY';
                if(sku.skuStatus == 'INITIATED') {
                    role = 'FINANCE'
                }
                $state.go('menu.addSKU', {
                    skuDef: angular.copy($scope.skuDefinition), 
                    empType: role
                });
            });
        }

        $scope.cancelSku = function(sku) {
            $alertService.confirm('Are you sure want to cancel SKU?', '', function(result) {
                if(result) {
                    $http({
                        method: "POST",
                        url: apiJson.urls.productManagement.cancelSkuRequest,
                        data: sku
                    }).then(function success(response) {
                        if(response.data) {
                            $toastService.create("SKU Cancelled Successfully");
                            $scope.getUserCreatedSkus();
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }
            })
        }

        $scope.isShowUpdateOrReject = function(sku) {
            return (sku.skuStatus == 'INITIATED' && $scope.hasAccess("SURNPRFA"));
        };

        $scope.hasAccess = function(key){
            if($rootScope.aclData!=null && $rootScope.aclData.action != null){
                return Object.keys($rootScope.aclData.action).length > 0 && $rootScope.aclData.action[key] != undefined;
            }else {
                return false;
            }
        };

        $scope.showCanelBtn = function(sku) {
            if(sku.skuStatus == 'INITIATED') {
                return(sku.createdBy.id == $scope.currentUser);
            }
            return false;
        }

        $scope.isRejected = function(status) {
            return (status == 'ARCHIVED');
        }

        $scope.setStartDate = function(startDate) {
            $scope.startDate = startDate;
        }

        $scope.setEndDate = function(endDate) {
            if (new Date(endDate) >= new Date($scope.startDate)) {
                $scope.endDate = endDate;
                return;
            }
            $scope.endDate = null;
            $toastService.create("End Date Should be greater than Start Date");
        }

        $scope.gridOptions = function () {
            return {
                enableColumnMenus: false,
                enableFiltering: true,
                enableCellEditOnFocus: true,
                enableColumnResizing: true,
                rowHeight: 40,
                columnDefs: [{
                    field: 'skuName',
                    displayName: 'SKU Name',
                    enableCellEdit: false
                }, {
                    field: 'skuStatus',
                    displayName: 'SKU Status',
                    cellTemplate: 'statusStyle.html',
                    enableCellEdit: false,
                    width: 150
                }, {
                    field: 'createdBy.name',
                    displayName: 'Created By',
                    enableCellEdit: false,
                    cellTemplate: '<span class="ui-grid-cell-contents">{{row.entity.createdBy.name}} [{{row.entity.createdBy.id}}]</span>',
                }, {
                    field: 'creationDate',
                    displayName: 'Created At',
                    enableCellEdit: false,
                    width: 150,
                    cellTemplate: '<div class="ui-grid-cell-contents">{{row.entity.creationDate | date:"dd-MM-yyyy"}}</div>'
                }, {
                    field: 'action',
                    displayName: 'Action',
                    cellTemplate: 'viewAndChange.html',
                    enableCellEdit: false,
                }],
                onRegisterApi: function (gridApi) {
                    $scope.gridApi = gridApi;
                    gridApi.edit.on.afterCellEdit($scope, function (rowEntity, colDef, newValue, oldValue) {
                        if (colDef.field == 'leadTime'.toString()) {
                            rowEntity.update = false;
                        }
                        else {
                            rowEntity.update = true;
                        }
                        console.log("JSON.stringify(rowEntity)", JSON.stringify(rowEntity));
                        $scope.$apply();
                    });
                }
            };
        };

    }
]
);

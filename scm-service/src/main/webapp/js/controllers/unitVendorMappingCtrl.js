/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
.controller('unitVendorMappingCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil','$location','$toastService','$alertService','metaDataService',
	function ($rootScope, $scope, apiJson, $http, appUtil,$location, $toastService,$alertService,metaDataService) {
	
	$scope.init = function () {
		$scope.timeSlots=["1:00","2:00","3:00","4:00","5:00","6:00","7:00","8:00","9:00","10:00","11:00","12:00","13:00","14:00","15:00","16:00"
			,"17:00","18:00","19:00","20:00","21:00","22:00","23:00","24:00"];
		$scope.fullFilmentType= ["SELF","WAREHOUSE","KITCHEN"];
		$scope.getVendors();
		$scope.unitList = [];
		/*$scope.unitList.push({
                    id: null,
                    name: ""
                });*/
		$scope.unitList = appUtil.filterUnitList($scope.getAllUnitList());
		$scope.showVendorAddForm=false;
		$scope.selectedVendor = null;
		$scope.selectedLocation = null;
		$scope.locationList = [];
		$scope.editType="Add";
		$scope.mappedVendors = [];
	}
	$scope.getAllUnitList = function () {
        metaDataService.getUnitList(function (response) {
            $scope.unitList = response;
        });
    };
	$scope.addNewForm=function(value){
		$scope.editType="Add";
		$scope.showVendorAddForm=value;
		createVendorMapping();
	};
	
	$scope.cancelMapping=function(){
		$scope.addNewForm(false);
	};
	
	$scope.changeUnit  = function(){
		if($scope.mappedVendors.length>0){
			$alertService.confirm("Are you sure to change unit, Your all data will be resetted ?","",function(result){
				if(result){
					$scope.selectedVendor = null;
					$scope.selectedLocation = null;
					$scope.locationList = [];
					$scope.addNewForm(false);
					$scope.mappedVendors = [];
					$scope.$apply();
				}
			});
		}
	};

	$scope.selectTime = function (time) {
		$scope.vendorMapping.notificationTime = time;
	};

	$scope.selectDeliveryPromiseTime = function (time) {
		$scope.vendorMapping.deliveryPromiseTime = time;
	};
	

	$scope.editMapping=function(vendorMapping){
		$scope.editType="Update";
		$scope.showVendorAddForm=true;
		$scope.locationList = null;
		$scope.selectedLocation = null;
		$scope.vendorMapping=angular.copy(vendorMapping);
		if(appUtil.isEmptyObject($scope.vendorMapping.noOfDays)){
			$scope.vendorMapping.noOfDays=1;
		}
		if(appUtil.isEmptyObject($scope.vendorMapping.fulfillmentLeadDays)){
			$scope.vendorMapping.fulfillmentLeadDays=0;
		}
		$scope.vendorList.forEach(function(vendor){
			if(vendor.id == $scope.vendorMapping.vendor.id){
				$scope.selectedLocation = null;
				$scope.selectedVendor=vendor;
				metaDataService.getVendorLocations($scope.selectedVendor.id, function (locations) {
					 $scope.locationList = locations;
					 if($scope.locationList != null && $scope.locationList.length > 0){
						 $scope.locationList.forEach(function(location){
							 if(location.id == $scope.vendorMapping.dispatchLocationId){
								 $scope.selectedLocation = location;
							 }
						 });
					 }
					 
			     });
			}
		});
	};
	
	$scope.updateMapping = function(){
		if(appUtil.isEmptyObject($scope.vendorMapping.fulFillmentType)){
			$toastService.create("Please Select Fulfillment Type!");
			return;
		}
		
		if(($scope.vendorMapping.emailNotification  || $scope.vendorMapping.smsNotification ) && appUtil.isEmptyObject($scope.vendorMapping.notificationTime)){
			$toastService.create("Please Select Notification Time!");
			return;
		}
		if($scope.vendorMapping.fulFillmentType != "SELF" && appUtil.isEmptyObject($scope.vendorMapping.dispatchLocationId)){
			$toastService.create("Please Select Dispatch Location!");
			return;
		}
		if(!$scope.vendorMapping.emailNotification && !$scope.vendorMapping.smsNotification){
			$scope.vendorMapping.notificationTime = null;
		}
		if (appUtil.isEmptyObject($scope.vendorMapping.deliveryPromiseTime)) {
			$toastService.create("Please Select Delivery Promise Time..!");
			return;
		}
		$scope.vendorMapping.lastUpdatedBy = appUtil.createGeneratedBy();
		$http({
			method: "POST",
			url: apiJson.urls.vendorManagement.unitVendorUpdate,
			data: $scope.vendorMapping
		}).then(function success(response) {
			console.log("response.data "+response.data);
			if(response.data){
				$toastService.create("Vendor "+$scope.selectedVendor.name+" details updated successfully for unit  "+$scope.selectedUnit.name+"!");
				$scope.addNewForm(false);
			}else{
				$toastService.create("Something went wrong. Please try again.");
			}
			$scope.getMappedVendors();
		}, function error(response) {
			$toastService.create("Something went wrong. Please try again.");
			console.log("error:" , response);
		});
	};

	
	$scope.getVendors = function () {
		$http({
			method: "GET",
			url: apiJson.urls.vendorManagement.vendorsTrimmed
		}).then(function success(response) {
			$scope.vendorList = response.data;
			$scope.vendorList = $scope.vendorList.filter(function(vendor){
				return vendor.status=="ACTIVE";
			});
		}, function error(response) {
			console.log("error:" + response);
		});
	};

	$scope.getMappedVendors = function () {
		console.log($scope.selectedUnit);
		$http({
			method: "GET",
			url: apiJson.urls.vendorManagement.unitVendors+"?unitId="+$scope.selectedUnit.id
		}).then(function success(response) {
			$scope.mappedVendors = response.data;
			console.log("$scope.mappedVendors",$scope.mappedVendors);
		}, function error(response) {
			console.log("error:" + response);
		});
	};
	
	function createVendorMapping(){
		$scope.vendorMapping={};
		$scope.vendorMapping.createdBy = appUtil.createGeneratedBy();
		$scope.vendorMapping.unit='';
		$scope.vendorMapping.vendor='';
		$scope.vendorMapping.mappingStatus="ACTIVE";
		$scope.vendorMapping.fulFillmentType='';
		$scope.vendorMapping.smsNotification=false;
		$scope.vendorMapping.emailNotification=false;
		$scope.vendorMapping.noOfDays=1;
		$scope.vendorMapping.fulfillmentLeadDays=0;
		$scope.vendorMapping.notificationTime='';
		$scope.vendorMapping.dispatchLocationId = null;
	}

	

	$scope.addMapping = function () {
		console.log($scope.vendorMapping);
		if(appUtil.isEmptyObject($scope.vendorMapping.vendor)){
			$toastService.create("Please Select Vendor!");
			return;
		}
		if(appUtil.isEmptyObject($scope.vendorMapping.fulFillmentType)){
			$toastService.create("Please Select Fulfilment Type!");
			return;
		}
		if($scope.vendorMapping.fulFillmentType != "SELF" && appUtil.isEmptyObject($scope.vendorMapping.dispatchLocationId)){
			$toastService.create("Please Select Dispatch Location!");
			return;
		}
		/*if(!$scope.vendorMapping.emailNotification && !$scope.vendorMapping.smsNotification){
			$toastService.create("Please Select RO Notification Types!");
			return;
		}*/
		if(($scope.vendorMapping.emailNotification  || $scope.vendorMapping.smsNotification ) && appUtil.isEmptyObject($scope.vendorMapping.notificationTime)){
			$toastService.create("Please Select Notification Time!");
			return;
		}
		if (appUtil.isEmptyObject($scope.vendorMapping.deliveryPromiseTime)) {
			$toastService.create("Please Select Delivery Promise Time..!");
			return;
		}
		$scope.vendorMapping.unit={id:$scope.selectedUnit.id};
		$http({
			method: "POST",
			url: apiJson.urls.vendorManagement.unitVendorAdd,
			data: $scope.vendorMapping
		}).then(function success(response) {
			if(response.data!=null){
				$toastService.create("Vendor "+$scope.selectedVendor.name+" added successfully for unit  "+$scope.selectedUnit.name+"!");
				$scope.addNewForm(false);
				$scope.mappedVendors.push(response.data);
			}else{
				$toastService.create("Something went wrong. Please try again.");
			}
		}, function error(response) {
			$toastService.create("Something went wrong. Please try again.");
			console.log("error:" , response);
		});
	};

	$scope.activateMapping = function (mappingId) {
		$http({
			method: "PUT",
			url: apiJson.urls.vendorManagement.unitVendorActivate,
			data: mappingId
		}).then(function success(response) {
			if(response.data==true){
				$scope.mappedVendors.forEach(function (item) {
					if(item.unitVendorMappingId==mappingId){
						item.mappingStatus = "ACTIVE";
					}
				});
			}else{
				$toastService.create("Something went wrong. Please try again.");
			}
		}, function error(response) {
			console.log("error:" , response);
		});
	};

	$scope.deactivateMapping = function (mappingId) {
		$http({
			method: "PUT",
			url: apiJson.urls.vendorManagement.unitVendorDeactivate,
			data: mappingId
		}).then(function success(response) {
			if (response.data == true) {
				$scope.mappedVendors.forEach(function (item) {
					if (item.unitVendorMappingId == mappingId) {
						item.mappingStatus = "IN_ACTIVE";
					}
				});
			} else {
				$toastService.create("Something went wrong. Please try again.");
			}
		}, function error(response) {
			console.log("error:", response);
		});
	};

	$scope.selectVendor = function(selectedVendor){
		$scope.locationList = null;
		$scope.selectedLocation = null;
		 metaDataService.getVendorLocations(selectedVendor.id, function (locations) {
			 $scope.locationList = locations;
			 $scope.selectedVendor = selectedVendor;
			 $scope.vendorMapping.vendor=$scope.selectedVendor;
	     });
		$scope.selectedVendor = selectedVendor;
		$scope.vendorMapping.vendor=$scope.selectedVendor;
	};

	$scope.selectLocation = function(selectedLocation){
		$scope.selectedLocation = selectedLocation;
		$scope.vendorMapping.dispatchLocationId=$scope.selectedLocation.id;
	};

	$scope.selectType = function(type){
		console.log(type);
	};
}]);

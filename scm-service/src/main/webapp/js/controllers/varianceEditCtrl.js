'use strict';

angular.module('scmApp')
    .controller('varianceEditCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$toastService', 'metaDataService', 'Popeye', 'previewModalService', '$alertService',
        function ($rootScope, $scope, apiJson, $http, appUtil, $toastService, metaDataService, Popeye, previewModalService, $alertService) {
            $scope.init = function () {
                $scope.currentUser = appUtil.getCurrentUser();
                $scope.editVariance = false;
                $scope.canEditVariance = false;
                $scope.searchProduct = null;
                $scope.varianceEdit = null;
                $scope.showContactZmMessage = false;
                $scope.getVarianceEditProducts();
                $scope.showPreview = previewModalService.showPreview;
            };

            $scope.setSearchProduct = function (text) {
                $scope.searchProduct = text;
            };

            $scope.editVarianceCall = function () {
                if (!$scope.editVariance) {
                    $alertService.confirm("Are you sure?", "Do you want to Edit the Variance..?", function (result) {
                        if (result) {
                           setEditVariance();
                        }
                    });
                }
            };

            function setEditVariance() {
                $scope.$apply(function() {
                    $scope.editVariance = true;
                });
            }

            $scope.getVarianceEditProducts = function () {
                $http({
                    method: "GET",
                    url: apiJson.urls.stockManagement.getCafeVarianceEditProducts,
                    params: {
                        "unitId": $scope.currentUser.unitId
                    }
                }).then(function success(response) {
                    if (response != null && response.data != null) {
                        $scope.varianceEdit = response.data;
                        if (!$scope.varianceEdit.canEditVariance) {
                            $toastService.create("You Cannot Edit Variance Now..!");
                        } else {
                            var aclData = $rootScope.aclData;
                            if (aclData != null) {
                                if (aclData.action != null) {
                                    if (aclData.action["VEDS"] == undefined || aclData.action["VEDS"] == null) {
                                        $scope.showContactZmMessage = true;
                                    }
                                } else {
                                    $scope.showContactZmMessage = true;
                                }
                            }
                        }
                        $scope.setSearchProduct(null);
                    } else {
                        $toastService.create("Something went wrong while getting the Variance Edit Products. Please try again...!");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $scope.varianceEdit = null;
                });
            }

            $scope.setFinalClosing = function (item, quantity) {
                if (quantity < 0) {
                    $toastService.create("Please Enter a value greater than or equal to zero");
                    item.finalClosing = null;
                    item.finalVariance = null;
                    item.finalQuantity = null;
                    return;
                }
                if (appUtil.isEmptyObject(quantity)) {
                    item.finalClosing = null;
                    item.finalVariance = null;
                    item.finalQuantity = null;
                    return;
                }
                if (item.unitOfMeasure == "PC" || item.unitOfMeasure == "SACHET") {
                    if (appUtil.isFloat(quantity)) {
                        item.finalClosing = null;
                        item.finalVariance = null;
                        item.finalQuantity = null;
                        $toastService.create("Closing Can not be in decimal for the Product, as the Product's UOM is : " + item.unitOfMeasure);
                        return;
                    }
                }
                item.finalClosing = quantity;
                item.finalQuantity = item.originalClosing - quantity - item.originalVariance;
                item.finalVariance = item.expectedClosing - quantity;
            };

            $scope.previewVarianceEdit = function () {
                $scope.searchProduct = null;
                var check = false;
                for (var i = 0; i < $scope.varianceEdit.varianceEditItems.length; i++) {
                    if (!appUtil.isEmptyObject($scope.varianceEdit.varianceEditItems[i].finalClosing)) {
                        check = true;
                        break;
                    }
                }
                if (!check) {
                    $toastService.create("Please Enter Final Closing Of at least 1 Product to Proceed...!");
                    return;
                }
                var varianceEditModal = Popeye.openModal({
                    templateUrl: "varianceEditModal.html",
                    controller: "varianceEditModalCtrl",
                    modalClass: 'custom-modal',
                    resolve: {
                        varianceEdit: function () {
                            return angular.copy($scope.varianceEdit);
                        }
                    },
                    click: false,
                    keyboard: false
                });

                varianceEditModal.closed.then(function (result) {
                    if (result.isSubmitted) {
                        $scope.searchProduct = null;
                        $scope.submitVarianceEdit(result.data);
                    }
                });
            };

            $scope.acknowledgeVariance = function () {
                $alertService.confirm("Again Are you sure?", "Do you want to ACKNOWLEDGE the Variance..?", function (result) {
                    if (result) {
                        $scope.varianceEdit.varianceEditStatus = "ACKNOWLEDGED";
                        $scope.submitVarianceEdit($scope.varianceEdit);
                    }
                });
            };

            $scope.submitVarianceEdit = function (varianceObj) {
                varianceObj.varianceUpdatedBy = $scope.currentUser.userId;
                $http({
                    method: "POST",
                    url: apiJson.urls.stockManagement.submitCafeVarianceEdit,
                    data: varianceObj
                }).then(function success(response) {
                    if (response != null && response.data != null) {
                        $scope.varianceEdit = response.data;
                    } else {
                        $toastService.create("Something went wrong while Editing the Variance Edit Products. Please try again...!");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    if (response.data.errorMsg != null) {
                        $alertService.alert(response.data.errorTitle, response.data.errorMsg, $scope.init(), true);
                    } else {
                        $toastService.create("Error Occurred while Editing the Variance Edit Products. Please try again...!");
                    }
                });
            };

        }]).controller('varianceEditModalCtrl', ['$scope', 'varianceEdit', 'Popeye', 'appUtil',
    function ($scope, varianceEdit, Popeye, appUtil) {

        $scope.varianceEdit = varianceEdit;
        $scope.varianceEdit.varianceEditItems = $scope.varianceEdit.varianceEditItems.filter(function (item) {
            return !appUtil.isEmptyObject(item.finalClosing);
        });
        $scope.gridOptions = appUtil.getGridOptions($scope);

        $scope.gridColumns = function () {
            return [{
                field: 'productId',
                enableCellEdit: false,
                displayName: 'Product Id',
            }, {
                field: 'productName',
                enableCellEdit: false,
                displayName: 'Product Name'
            }, {
                field: 'originalClosing',
                enableCellEdit: false,
                displayName: 'Original Closing',
            }, {
                field: 'originalVariance',
                enableCellEdit: false,
                displayName: 'Original Variance'
            }, {
                field: 'finalClosing',
                enableCellEdit: false,
                displayName: 'Final Closing'
            }, {
                field: 'finalVariance',
                enableCellEdit: false,
                displayName: 'Final Variance'
            }
            ]
        };

        $scope.gridOptions.columnDefs = $scope.gridColumns();
        $scope.gridOptions.data = $scope.varianceEdit.varianceEditItems;

        $scope.close = function () {
            Popeye.closeCurrentModal({
                "isSubmitted": false,
                "data": null
            });
        };

        $scope.submit = function () {
            Popeye.closeCurrentModal({
                "isSubmitted": true,
                "data": $scope.varianceEdit
            });
        };
    }])
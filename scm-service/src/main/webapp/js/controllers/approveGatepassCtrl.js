'use strict';

angular.module('scmApp')
    .controller('approveGatepassCtrl', ['$scope', 'apiJson', '$http', 'appUtil', '$toastService', 'metaDataService', 'previewModalService',
        function ($scope, apiJson, $http, appUtil, $toastService, metaDataService, previewModalService) {

            $scope.init = function () {
                $scope.operationList = [
                    { name: "QUALITY_CHECK", label: "QUALITY CHECK" },
                    { name: "REPAIR", label: "REPAIR" },
                    { name: "NPD", label: "NPD" },
                    { name: "SAMP<PERSON>", label: "SAMPLE" },
                    { name: "INTERNAL_TXN", label: "INTERNAL TXN" },
                    { name: "INVENTORY_MODEL", label: "INVENTORY MODEL" }
                ];

               $scope.statusList = [
                    { id: 1, label: "INITIATED" },
                    { id: 3, label: "APPROVER_REJECTED" },
                    { id: 4, label: "PENDING_RETURN" },
                    { id: 5, label: "CLOSED" },
                    { id: 6, label: "CANCELLED" }
                ];

                $scope.selectedStatus = [{ id: 1, label: "INITIATED" }];

                $scope.gatepasses = [];
                $scope.vendors = [];
                $scope.showVendorFilter = false;
                $scope.searchPerformed = false;
                $scope.selectedGatepass = null;
                $scope.actionType = null;
                $scope.approvalComments = '';
                $scope.isLoading = false;
                $scope.isLoadingDetails = false;

                $scope.showPreview = previewModalService.showPreview;

                $scope.searchCriteria = {
                    unitId: null,
                    vendorSelected: null,
                    statuses: [],
                    gatePassId: null
                }

                $scope.loadVendors();

                // Initialize modals after DOM is ready
                setTimeout(function () {
                    $('#approvalModal').modal();
                    $('#viewModal').modal();
                }, 100);
            };

            $scope.loadVendors = function () {
                var url = apiJson.urls.vendorManagement.allVendorName;
                $http({
                    url: url,
                    method: 'GET'
                }).then(function (response) {
                    $scope.vendors = response.data;
                }, function (response) {
                    console.log("Error loading vendors:", response);
                    $toastService.create("Error loading vendors. Please try again!");
                });
            };

            $scope.operationCheck = function (operationType) {
                if (operationType === "REPAIR") {
                    $scope.showVendorFilter = true;
                } else {
                    $scope.showVendorFilter = false;
                    $scope.searchCriteria.vendorSelected = null;
                    $scope.vendorSelected = null;
                }
            };

            $scope.multiSelectStatusSettings = {
                enableSearch: true,
                template: '<b> {{option.label}} </b>',
                scrollable: true,
                idProperty: 'id',
                scrollableHeight: '250px',
                clearSearchOnClose: true,
            };

            $scope.searchGatepasses = function () {
                $scope.searchCriteria.unitId = appUtil.getCurrentUser().unitId;

                // Prepare search payload
                var searchPayload = angular.copy($scope.searchCriteria);

                searchPayload.statuses = $scope.selectedStatus.map(function(s) {
                    return s.label;
                });
                 searchPayload.forApproval = true;

                 $scope.isLoading = true;
                $http({
                    method: "POST",
                    url: apiJson.urls.gatepassManagement.searchGatepass,
                    data: searchPayload
                }).then(function success(response) {
                    $scope.gatepasses = response.data || [];
                    $scope.searchPerformed = true;
                    $scope.isLoading = false;

                    if ($scope.gatepasses.length === 0) {
                        $toastService.create("No gatepasses found for the selected criteria.");
                    }
                }, function error(response) {
                    console.log("Error searching gatepasses:", response);
                    $toastService.create("Error searching gatepasses. Please try again!");
                    $scope.gatepasses = [];
                    $scope.searchPerformed = true;
                    $scope.isLoading = false;
                });
            };

            $scope.viewGatepassDetails = function (gatepass) {
                $scope.isLoadingDetails = true;

                // Fetch detailed gatepass information
                var searchPayload = {
                    gatePassId: gatepass.id
                };

                $http({
                    method: "POST",
                    url: apiJson.urls.gatepassManagement.gatepassDetails,
                    data: searchPayload
                }).then(function success(response) {
                    if (response.data && response.data.length > 0) {
                        $scope.selectedGatepass = response.data[0];
                    } else {
                        $scope.selectedGatepass = gatepass;
                    }
                    $scope.isLoadingDetails = false;
                    $('#viewModal').openModal();
                }, function error(response) {
                    console.log("Error fetching gatepass details:", response);
                    $scope.selectedGatepass = gatepass;
                    $scope.isLoadingDetails = false;
                    $('#viewModal').openModal();
                    $toastService.create("Could not fetch detailed information, showing basic details.");
                });
            };

            $scope.approveGatepass = function (gatepass) {
                $scope.selectedGatepass = gatepass;
                $scope.actionType = 'Approve';
                $scope.approvalComments = '';
                $('#approvalModal').openModal();
            };

            $scope.rejectGatepass = function (gatepass) {
                $scope.selectedGatepass = gatepass;
                $scope.actionType = 'Reject';
                $scope.approvalComments = '';
                $('#approvalModal').openModal();
            };

            $scope.closeViewModal = function () {
                $('#viewModal').closeModal();
            };

            $scope.closeActionModal = function () {
                $('#approvalModal').closeModal();
            };

            $scope.confirmAction = function () {
                if (!$scope.approvalComments || $scope.approvalComments.trim() === '') {
                    $toastService.create("Please enter comments for " + $scope.actionType.toLowerCase() + "!");
                    return;
                }

                var actionPayload = {
                    gatepassId: $scope.selectedGatepass.id,
                    action: $scope.actionType.toUpperCase(),
                    comments: $scope.approvalComments.trim()
                };

                var apiUrl = $scope.actionType === 'Approve' ?
                    apiJson.urls.gatepassManagement.approveGatepass :
                    apiJson.urls.gatepassManagement.rejectGatepass;

                $http({
                    method: "PUT",
                    url: apiUrl,
                    data: actionPayload
                }).then(function success(response) {
                    if (response.data === true || response.data.success === true) {
                        $toastService.create("Gatepass " + $scope.actionType.toLowerCase() + "d successfully!");

                        // Update the status in the local array
//                        var index = $scope.gatepasses.findIndex(g => g.id === $scope.selectedGatepass.id);
//                        if (index !== -1) {
//                            $scope.gatepasses[index].status = $scope.actionType.toUpperCase() + 'D';
//                            $scope.gatepasses[index].actionComments = $scope.approvalComments;
//                            $scope.gatepasses[index].actionBy = appUtil.getCurrentUser();
//                            $scope.gatepasses[index].actionAt = new Date();
//                        }
                    } else {
                        $toastService.create("Failed to " + $scope.actionType.toLowerCase() + " gatepass. Please try again!");
                    }
                }, function error(response) {
                    console.log("Error processing gatepass action:", response);
                    $toastService.create("Error processing gatepass action. Please try again!");
                });
            };
        }
    ]);
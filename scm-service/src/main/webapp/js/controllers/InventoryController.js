/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 22-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('inventoryCtrl', ['$rootScope','$scope','$interval','authService','appUtil','apiJson',
        '$toastService','$http','$alertService','packagingService','pagerService','$state','Popeye','previewModalService',
        'metaDataService','uiGridConstants', 'ScmApiService', 'toast',
        function ($rootScope,$scope,$interval,authService,appUtil,apiJson,$toastService,$http,$alertService,
                  packagingService,pagerService,$state,Popeye,previewModalService,metaDataService,uiGridConstants, ScmApiService, toast) {

                $scope.currentUser = appUtil.getCurrentUser();

                $scope.frequencies = ["DAILY", "WEEKLY", "MONTHLY"];
                $scope.checkEmpty = appUtil.checkEmpty;
            $scope.unitData = appUtil.getUnitData();

                $scope.productList = [];
                $scope.submitted = false;
                $scope.isPreview = false;
                $scope.pendingGR = {};
                $scope.pendingWastage = 0;
                $scope.packagingMappings = {};
                $scope.showPreview = previewModalService.showPreview;

                $scope.preview = function () {
                    $scope.isPreview = !$scope.isPreview;
                };


                // $scope.$on("$destroy", function () {
                //     saveInventory($scope.getFinalProductList());
                //     $interval.cancel($scope.refreshInterval);
                // });

            $scope.getDayCloseTxnItems = function (inventoryItems) {
                var result = [];
                angular.forEach(inventoryItems, function (item) {
                    if (item.stockValue > 0 && item.dailyVariance < 0 && item.categoryId === 4) {
                        var obj = {
                            "keyId" : item.productId,
                            "expiryDate" : item.selectedExpiryDate
                        };
                        result.push(obj);
                    }
                });
                return result;
            };


            $scope.finalSubmission = function () {
                $scope.submitted = true;
                var finalProductsList = $scope.getFinalProductList();
                // saveInventory(finalProductsList);
                var inventoryList = angular.copy(finalProductsList);
                inventoryList = inventoryList.filter(function (product) {
                    return appUtil.isEmptyObject(product.notAvl) && !appUtil.isEmptyObject(product.stockValue);
                }).map(function (product) {
                    delete product.packagingMappings;
                    return product;
                });
                var negativeVariances = $scope.getDayCloseTxnItems(inventoryList);
                var currentUser = appUtil.getCurrentUser();
                var requestObject = {
                    unit: currentUser.unitId,
                    generatedBy: currentUser.userId,
                    eventType: $scope.isOpening ? "OPENING" : "CLOSING",
                    inventoryResponse: inventoryList,
                    negativeVarianceList: negativeVariances
                };
                var businessDate = null;
                if ($scope.dayCloseBusinessDate != null) {
                    businessDate = appUtil.formatDate($scope.dayCloseBusinessDate, "yyyy-MM-dd");
                }
                if ($scope.unitsForStockTakeThroughApp.indexOf($scope.unitId) >= 0) {
                    requestObject.stockTakeSumoDayCloseEventId = $scope.submittedStockTakeEvent.stockTakeSumoDayCloseEventId;
                }

                $http.post(apiJson.urls.stockManagement.updateInventory + "?stockType=" + $scope.frequencyValue + "&businessDate=" + businessDate, requestObject)
                        .success(function (response) {
                            if (response && (appUtil.isEmptyObject(response.data) || appUtil.isEmptyObject(response.data.errorType))) {
                                if (response == 1) {
                                    $scope.productList = [];
                                    $scope.resetFinalProductList();
                            $scope.inventoryUpdated = true;
                                    appUtil.removeSavedInventoryList();
                                    $interval.cancel($scope.refreshInterval);
                                    $toastService.create("You have successfully updated inventory");
                                    //metaDataService.getRegularOrderingEvents(appUtil.getCurrentUser().unitId, function (events) {
                                    //    if (events.length > 0) {
                                    //        $state.go("menu.refOrderCreateV1", {orderingEvents: events});
                                    //    }
                                    //});
                                if (!$scope.isOpening) {
                                $state.go("menu.varianceEdit");
                            }} else if (response == 0) {
                                    $toastService.create("You are trying to upload wrong inventory list");
                                } else if (response == -1) {
                                    $toastService.create("Some exception has occurred. Please try again!");
                                } else if (response == 404) {
                                $alertService.alert("Kettle Day Close Failed..!", "Please Check the Kettle Day Close Of the Unit..! <br> All the Data Will be Cleared..!", function (result) {
                                    $scope.resetFinalProductList();
                                    appUtil.removeSavedInventoryList();
                                    $scope.submitted = false;
                                    $scope.init();
                                }, false);
                            } else if (response == 405) {
                                $alertService.alert("Kettle Day Close Not Found..!", "Please Check the Kettle Day Close Of the Unit..! <br> All the Data Will be Cleared..!", function (result) {
                                    $scope.resetFinalProductList();
                                    appUtil.removeSavedInventoryList();
                                    $scope.submitted = false;
                                    $scope.init();
                                }, false);
                            }
                            } else if (!appUtil.isEmptyObject(response.data) && !appUtil.isEmptyObject(response.data.errorType)) {
                                $toastService.create("You have entered negative stock for some products. Please check and submit again.");
                            } else {
                                $toastService.create("Upload failed. Try again!");
                                $scope.submitted = false;
                            }
                        }).error(function (response) {
                        if (!appUtil.isEmptyObject(response.payload)) {
                            var message = "Following Products are not available at your Cafe: <br/>";
                            var products = response.payload;
                            var productIds = products.map(function (p) {
                                return p.productId;
                            });
                            var names = [];
                            $scope.getFinalProductList().forEach(function (p) {
                                if (productIds.indexOf(p.productId) != -1) {
                                    names.push(p.productName);
                                    p.stockValue = null;
                                    p["notAvl"] = true;
                                }
                            });
                            message += names.join(", ");
                            message += "<br><b>Removing them from the list. Click Yes to submit again!</b><br>";
                            $alertService.confirm(response.errorType, message, function (result) {
                                if (result) {
                                    $scope.finalSubmission();
                                }
                            }, false);
                        } else if (response.errorMsg != null) {
                            $alertService.alert(response.errorTitle, response.errorMsg, function (result) {
                            }, false);
                        } else {
                            $toastService.create("Upload Failed! Please try again");
                        }
                        $scope.submitted = false;
                    });
                };

            $scope.submit = function(){
                // (closingInitiated || isOpening) && !submitted && !inventoryUpdated && !varianceBlocking
                if(!$scope.closingInitiated && !$scope.isOpening) {
                    toast.warning("Please initiate the closing event first.", Infinity);
                    return;
                }
                if($scope.inventoryUpdated) {
                    toast.warning("Inventory already submitted for the day.", Infinity);
                    return;
                }
                if($scope.varianceBlocking) {
                    toast.warning("Variance acknowledgement is pending for the day.", Infinity);
                    return;
                }
                if($scope.submitted) {
                    toast.warning("Already submitted for the day.", Infinity);
                    return;
                }

                if (!appUtil.isEmptyObject($scope.dayCloseBusinessDate) && appUtil.getCurrentBusinessDate() != appUtil.formatDate($scope.dayCloseBusinessDate,"yyyy-MM-dd")) {
                    $alertService.confirm("Are you sure?", "You Cannot Edit Variance after the submission of Day close ..!", function (result) {
                        if (result) {
                            $scope.finalSubmission();
                        }
                    });
                } else {
                    $scope.finalSubmission();
                }
            };

                $scope.fillZeroForAllProducts = function () {
                    angular.forEach($scope.getFinalProductList(), function (product) {
                        angular.forEach(product.packagingMappings, function (packaging) {
                            packaging.value = 0;
                            $scope.removeMapping(product.packagingMappings, packaging, product, true);
                        });
                    });
                };

                $scope.autoFillInv = function(){
                angular.forEach($scope.getFinalProductList(), function (product) {
                    angular.forEach(product.packagingMappings, function (packaging) {
                       if(packaging.packagingType === "LOOSE"){
                        packaging.value = product.stockValue;
                       }
                });
                });

            }

            $scope.clearMappings = function () {
                    $scope.resetFinalProductList();
                $scope.selectedPackagings = [];
                    appUtil.removeSavedInventoryList();
                    predictExpectedValues($scope.frequencyValue, true);
                    $scope.getFinalProductList().forEach(function (product) {
                        product.stockValue = product.expectedValue;
                        product.variance = 0;
                        product.dailyVariance = 0;
                    });
                $scope.currentProductFillingType = "MANDATORY";
            };

                function checkForPendingGR(unitId) {
                    var promise = $http.get(apiJson.urls.stockManagement.checkPendingGR + "?unit=" + unitId)
                        .success(function (response) {
                            console.log("response checkForPendingGR", response);
                            $scope.pendingGR = response;
                            return response.data;
                        }).error(function (response) {
                            console.log("Could not get pending GR count");
                        });
                    return promise;
                }

                function checkVarianceAcknowledgement(unitId) {
                $http.get(apiJson.urls.stockManagement.checkVarianceAcknowledgement+"?id="+unitId)
                    .success(function(response){
                        console.log("response checkForVarianceAcknowledgeemnt",response);
                        $scope.varianceBlocking = response.varianceWeeklyBlocking;
                    }).error(function(response){
                        console.log("Could not get acknowledgement data");
                    });
            }

            function checkWastageEntered(unitId) {
                    $http.get(apiJson.urls.stockManagement.checkWastageEntered + "?unit=" + unitId)
                        .success(function (response) {
                            if (!$scope.checkEmpty(response) && response > 0) {
                                $scope.pendingWastage = response;
                            }
                        }).error(function (response) {
                        console.log("Could not get pending GR count");
                    });
                }

                function checkTransfersCreated(unitId) {
                    $http.get(apiJson.urls.stockManagement.checkPendingTransfers + "?unitId=" + unitId)
                        .success(function (response) {
                            if (!$scope.checkEmpty(response) && response > 0) {
                                $scope.pendingTransfers = response;
                                $toastService.create("Found " + response + " unsettled special order(s). " +
                                    "Please settle all special order(s) first.");
                                $state.go('menu.acknowledgeRO');
                            }
                        }).error(function (err) {
                        console.log("Could not get pending special order count", err);
                    });
                }

            $scope.init = function(){
                $scope.currentDate = appUtil.formatDate(appUtil.getDate(new Date().getHours() < 5 ? 0 : 1), "yyyy-MM-dd");
                $scope.varianceWaringProducts = [103282,100330,100196,103321];
                $scope.setProductMap();
                getScmProductsFromCache();
                $scope.unitsForStockTakeThroughApp = [];
                $scope.getUnitsForStockTakeThroughApp();
                $scope.productFillingTypes = ["MANDATORY", "OPTIONAL", "ALL"];
                $scope.resetFinalProductList();
                $scope.searchProduct = null;
                $scope.currentProductFillingType = "MANDATORY";
                $scope.dayCloseBusinessDate = null;
                $scope.unitId = appUtil.getCurrentUser().unitId;
                $scope.currentUserId = appUtil.getCurrentUser().userId;
                $scope.isOpening = false;
                checkTransfersCreated($scope.unitId);
                checkInventoryUpdated($scope.unitId);
                checkIfOpeningNeeded($scope.unitId);
                checkForPendingGR($scope.unitId);
                checkWastageEntered($scope.unitId);
                checkVarianceAcknowledgement($scope.unitId);
                $scope.edited = false;             //edited stock flag initialized to false
                $scope.packagingMappings = appUtil.getMetadata().packagingMappings;
                $scope.inventoryUpdated = false;
                $scope.varianceBlocking = false;
                $scope.lastSaved = null;
                var savedInventory = appUtil.getSavedInventoryList();
                if(!$scope.checkEmpty(savedInventory) && (savedInventory.unit == $scope.unitId)){
                    $scope.productList = savedInventory.products;
                    $scope.frequencyValue = savedInventory.stockType;
                    $scope.fixedAssetsOnly = (savedInventory.stockType == "FIXED_ASSETS");
                    $scope.lastSaved = savedInventory.lastSaved;
                    setProducts($scope.productList, false);
                    $scope.seggregateMandatoryOptionalProducts($scope.productList);
                }else{
                    appUtil.removeSavedInventoryList();
                    // $scope.selectFrequency("DAILY");
                }
                // $scope.refreshInterval = $interval(function(){
                //     if(!$scope.checkEmpty($scope.getFinalProductList())){
                //         saveInventory($scope.getFinalProductList(),$scope.selectedPackagings);
                //     }
                // },10000);
                $scope.inventoryEventValid = true;
                $scope.dayClosePending = false;
                $scope.showGrid = false;
                $scope.setMaxDateForProducts();

                // check for monk day close, before initiating sumo-day close
                if($scope.unitData.monkDayCloseEnabled) {
                    checkForMonkDayClose();
                }

            };

            function getScmProductsFromCache() {
                $scope.productsCache = [];
                ScmApiService
                    .get(apiJson.urls.productManagement.getProducts)
                    .then(function (responseData) {
                        if(!appUtil.checkEmpty(responseData) || appUtil.checkEmpty(responseData.data)) {
                            $scope.productsCache = responseData.data;
                        }
                    })
            }

            function checkForMonkDayClose() {
                var url = apiJson.urls.monkDayClose.getEvent;
                var params = {
                    unitId: $scope.unitData.id
                }
                ScmApiService.get(url, params).then(function(responseData) {
                    var monkDayCloseEvent = responseData.payload;
                    if(!monkDayCloseEvent) {
                        return;
                    }
                    if (!monkDayCloseEvent.eventStatus ||
                        monkDayCloseEvent.eventStatus.toUpperCase() !== "COMPLETED") {
                        $toastService.create("Please Complete Monk Day Close.");
                        $state.go('menu.monkDayClose');
                        return;
                    }
                });
            }

            $scope.getUnitsForStockTakeThroughApp = function () {
                $http({
                    method: "GET",
                    url: apiJson.urls.stockManagement.getUnitsForStockTakeThroughApp,
                    headers: {"Content-Type": "application/json"}
                }).then(function (response) {
                    if (response != null && response.data != null) {
                        $scope.unitsForStockTakeThroughApp = response.data;
                        if ($scope.unitsForStockTakeThroughApp.indexOf(appUtil.getCurrentUser().unitId) >= 0) {
                            $scope.checkForStockTakeEventSubmission();
                        } else {
                            $scope.checkClosingInitiated($scope.unitId);
                        }
                    } else {
                        $scope.unitsForStockTakeThroughApp = [];
                        $scope.checkClosingInitiated($scope.unitId);
                    }
                }, function (response) {
                    console.log(response);
                    $scope.unitsForStockTakeThroughApp = [];
                    $scope.checkClosingInitiated($scope.unitId);
                });
            };

            $scope.checkForStockTakeEventSubmission = function () {
                $scope.submittedStockTakeEvent = {};
                $scope.submittedStockTakeEventProductsMap = {};
                $http({
                    method: "POST",
                    url: apiJson.urls.stockManagement.checkForStockTakeSumoDayClose,
                    params:{
                        "unitId" : appUtil.getCurrentUser().unitId,
                        "status" : "SUBMITTED"
                    },
                    headers: {"Content-Type": "application/json"}
                }).then(function (response) {
                    if (response != null && response.data != null) {
                        $scope.submittedStockTakeEvent = response.data;
                        if ($scope.submittedStockTakeEvent.stockTakeSumoDayCloseEventId === undefined || $scope.submittedStockTakeEvent.stockTakeSumoDayCloseEventId === null) {
                            $alertService.alert("Fill Closing Inventory Through App..!", "<b>Please Fill the Closing Inventory through Stock Take App..!</b><br><b></b>", null , true);
                        } else {
                            for (var i=0;i<$scope.submittedStockTakeEvent.stockTakeSumoDayCloseProductsDTOS.length;i++) {
                                var filledProduct = $scope.submittedStockTakeEvent.stockTakeSumoDayCloseProductsDTOS[i];
                                for (var j =0 ; j< filledProduct.dayCloseProductPackagingMappings.length;j++) {
                                    var filledPackaging = filledProduct.dayCloseProductPackagingMappings[j];
                                    if (filledPackaging.quantity !== undefined && filledPackaging.quantity !== null) {
                                        var key = filledProduct.productId + "_" + filledPackaging.packagingId;
                                        $scope.submittedStockTakeEventProductsMap[key] = filledPackaging.quantity;
                                    }
                                }
                            }
                        }
                        $scope.checkClosingInitiated($scope.unitId);
                    } else {
                        $scope.unitsForStockTakeThroughApp = [];
                        $scope.checkClosingInitiated($scope.unitId);
                    }
                }, function (response) {
                    console.log(response);
                    $toastService.create("Something Went Wrong While Getting the Submitted Stock Event..!");
                    $scope.submittedStockTakeEvent = {};
                    $scope.checkClosingInitiated($scope.unitId);
                });
            };

            $scope.setProductMap = function () {
                $scope.productMap = {};
                var prodsList = appUtil.getScmProductDetails();
                angular.forEach(prodsList, function (prod) {
                     $scope.productMap[prod.productId] = prod;
                });
            };

            $scope.setSearchProduct = function (text) {
                $scope.searchProduct = text;
            };

            $scope.validateUpdatedExpiryDates = function (currentProductList) {
                var missingExpiryDates = [];
                angular.forEach(currentProductList, function (inventoryListItem) {
                    if (inventoryListItem.stockValue > 0 && inventoryListItem.dailyVariance < 0 && inventoryListItem.categoryId === 4) {
                        if (inventoryListItem.selectedExpiryDate === undefined || inventoryListItem.selectedExpiryDate === null) {
                            missingExpiryDates.push(inventoryListItem.productName);
                        }
                    }
                });
                if (missingExpiryDates.length > 0) {
                    $toastService.create("Please Update the Expiry Date Of the Product's " + missingExpiryDates.join(","));
                    return false;
                }
                return true;
            };

            $scope.setProductFillingType = function (currentType , type) {
                $scope.searchProduct = null;
                if (type == 'previous') {
                    if (currentType == 'OPTIONAL') {
                        $scope.currentProductFillingType = "MANDATORY";
                    }
                    if (currentType == 'ALL') {
                        $scope.currentProductFillingType = "OPTIONAL";
                    }
                } else {
                    if (currentType == 'MANDATORY') {
                        var missedProductQuantity = [];
                        var varianceReverificationProducts = [];
                        var currentProductList = $scope.productsByFillingType.MANDATORY;
                        if (currentProductList.length > 0) {
                            for (var i=0;i<currentProductList.length;i++) {
                                var packCheck = false;
                                for (var j=0;j<currentProductList[i].packagingMappings.length ;j++) {
                                    if (!appUtil.isEmptyObject(currentProductList[i].packagingMappings[j].value)) {
                                        packCheck = true;
                                        break;
                                    }
                                }
                                if (!appUtil.isEmptyObject(currentProductList[i].varianceWarningStatus) && currentProductList[i].varianceWarningStatus === "RE_VERIFICATION_REQUIRED") {
                                    varianceReverificationProducts.push(currentProductList[i].productName);
                                }
                                if (!packCheck) {
                                    missedProductQuantity.push(currentProductList[i].productName);
                                }
                            }
                            if (missedProductQuantity.length > 0) {
                                $toastService.create("Please Enter Quantity for the Products : " + missedProductQuantity.join(","));
                                return false;
                            }
                            if (varianceReverificationProducts.length > 0) {
                                $toastService.create("Please Re Verify the Quantity for the Products : " + varianceReverificationProducts.join(","));
                                return false;
                            }
                            if (!$scope.validateUpdatedExpiryDates(currentProductList)) {
                                return;
                            }
                        }
                        $scope.currentProductFillingType = "OPTIONAL";
                    }
                    if (currentType == 'OPTIONAL') {
                        var varianceReverificationProducts = [];
                        var currentProductList = $scope.productsByFillingType.OPTIONAL;
                        if (currentProductList.length > 0) {
                            for (var i=0;i<currentProductList.length;i++) {
                                if (!appUtil.isEmptyObject(currentProductList[i].varianceWarningStatus) && currentProductList[i].varianceWarningStatus === "RE_VERIFICATION_REQUIRED") {
                                    varianceReverificationProducts.push(currentProductList[i].productName);
                                }
                            }
                            if (varianceReverificationProducts.length > 0) {
                                $toastService.create("Please Re Verify the Quantity for the Products : " + varianceReverificationProducts.join(","));
                                return false;
                            }
                            if (!$scope.validateUpdatedExpiryDates(currentProductList)) {
                                return;
                            }
                        }
                        $scope.currentProductFillingType = "ALL";
                    }
                }
                $scope.productsByFillingType.ALL = $scope.productsByFillingType.MANDATORY.concat($scope.productsByFillingType.OPTIONAL);
                window.scrollTo({ top: 0, behavior: 'smooth' });
            };

                $scope.clearSavedHistory = function () {
                    appUtil.removeSavedInventoryList();
                    $scope.productList = null;
                    $scope.lastSaved = null;
                    $scope.resetFinalProductList();// $scope.selectFrequency("DAILY");
                };

                $scope.getProducts = function () {
                    if (isEmpty($scope.frequencyValue) || $scope.frequencyValue == undefined) {
                        $toastService.create("Please select stock take type event first");
                        return;
                    }
                    predictExpectedValues($scope.frequencyValue, true);
                };

                $scope.selectFrequencyAtIndex = function (index) {
                    var savedInventory = appUtil.getSavedInventoryList();
                    if ($scope.checkEmpty(savedInventory)) {
                        $scope.frequencyValue = $scope.frequencies[index];
                    } else {
                        $toastService.create("You cannot change the list once saved, Click on Clear Saved history to reset", 10000);
                        $scope.frequencyValue = savedInventory.stockType;
                    }
                    $scope.inventoryEventValid = true;
                    $scope.checkStockEventAvailable();
                };

                function checkAllStockEventsAvailable() {
                    if (!appUtil.isWarehouseOrKitchen()) {
                        var currentUser = appUtil.getCurrentUser();
                        var requestObject = {
                            unit: currentUser.unitId,
                            generatedBy: currentUser.userId,
                            eventType: $scope.isOpening ? "OPENING" : "CLOSING",
                            inventoryResponse: null
                        };
                        var  businessDate = null;
                    if($scope.dayCloseBusinessDate != null){
                        businessDate = appUtil.formatDate($scope.dayCloseBusinessDate,"yyyy-MM-dd");
                    }$http({
                            method: "POST",
                            url: apiJson.urls.stockManagement.validateStockEvents,
                            data: requestObject,
                        params : {
                            eventBusinessDate : businessDate
                        }
                        }).then(function (response) {
                            if (response != null) {
                                $scope.availableFrequencies = [];
                                $scope.stockTakeEventTypes = response.data;
                                var flag1 = false;
                                var flag2 = false;
                                for (var i = 0; i < $scope.stockTakeEventTypes.length; i++) {
                                    if ($scope.stockTakeEventTypes[i].valid == true && i == 0) {
                                        $scope.availableFrequencies[0] = $scope.frequencies[1];
                                        flag1 = true;
                                    } else if ($scope.stockTakeEventTypes[i].valid == true && i == 1) {
                                        if (flag1 == false) {
                                            $scope.availableFrequencies[0] = $scope.frequencies[2];
                                            flag2 = true;
                                        } else {
                                            $scope.availableFrequencies[1] = $scope.frequencies[2];
                                            flag2 = true;
                                        }
                                    }
                                }
                                if (flag1 == false && flag2 == false) {
                                    $scope.availableFrequencies[0] = $scope.frequencies[0];
                                }
                                $scope.frequencies = $scope.availableFrequencies;
//                                if ($scope.currentUser.userId == 125200) {
//                                    $scope.frequencies.push("FIXED_ASSETS")
//                                }
                            }
                        }, function (response) {
                            console.log(response);
                        })
                    }
                }

                $scope.checkStockEventAvailable = function () {
                    if (!appUtil.isWarehouseOrKitchen() && ($scope.frequencyValue == "WEEKLY" || $scope.frequencyValue == 'MONTHLY')) {
                        var currentUser = appUtil.getCurrentUser();
                        var requestObject = {
                            unit: currentUser.unitId,
                            generatedBy: currentUser.userId,
                            eventType: $scope.isOpening ? "OPENING" : "CLOSING",
                            inventoryResponse: null
                        };
                        $http.post(apiJson.urls.stockManagement.validateStockEvent
                            + "?stockType=" + $scope.frequencyValue, requestObject).success(function (response) {
                            console.log(response);
                            if (response != null) {
                                if (response.valid != null) {
                                    $scope.inventoryEventValid = response.valid;
                                    $scope.inventoryValidationErrorMsg = response.errorMsg;
                                } else {
                                    $scope.inventoryEventValid = false;
                                    $scope.inventoryValidationErrorMsg = "Error checking inventory event validity";
                                }
                            } else {
                                $scope.inventoryEventValid = false;
                                $scope.inventoryValidationErrorMsg = "Error checking inventory event validity";
                            }
                        }).error(function (response) {
                            $scope.inventoryEventValid = false;
                            if (response.errorMsg != null) {
                                $scope.inventoryValidationErrorMsg = response.errorMsg;
                            } else {
                                $scope.inventoryValidationErrorMsg = "Error checking inventory event validity";
                            }
                        })
                    } else {
                        $scope.inventoryEventValid = true;
                        $scope.inventoryValidationErrorMsg = null;
                    }
                };

                $scope.selectFrequency = function (frequency) {
                    $scope.frequencyValue = frequency;
                };

                $scope.checkClosingInitiated = function (unitId) {
                    $http({
                        method: "POST",
                        url: apiJson.urls.stockManagement.closingInitiated,
                        data: {
                            unitId: unitId,
                            businessDate: appUtil.getCurrentBusinessDate()
                        }
                    }).then(function (response) {
                        $scope.dayCloseEvent = response.data;
                        if ($scope.dayCloseEvent != null && $scope.dayCloseEvent != "") {
                            $scope.closingInitiated = true;
                            $scope.dayCloseBusinessDate = $scope.dayCloseEvent.businessDate;
                            checkAllStockEventsAvailable();
                        if(!isEmpty($scope.frequencyValue) && $scope.frequencyValue != undefined){
                            predictExpectedValues($scope.frequencyValue, false);
                        }
                    } else {
                            checkAllStockEventsAvailable();
                            $scope.closingInitiated = false;
                            $toastService.create("Closing Event Not Initiated !!");
                        }
                    }, function () {
                        console.log("Insert");
                    })
                };

                $scope.removeMapping = function (selectedPackagings, selected, product, makeZero) {
                    selected.value = null;
                    if (makeZero != undefined && makeZero != null && makeZero) {
                        selected.value = 0;
                    }
                    calculateVariance(selectedPackagings, product);
                };

                $scope.showFixedAssets = function (frequency) {
                    return frequency == "FIXED_ASSETS";
                };

            $scope.changeStock = function(product,value,packaging){
                if (!appUtil.isEmptyObject(value)) {
                    // if (appUtil.isFloat(value)) {
                    //     if (packaging.unitOfMeasure == "PC" || packaging.unitOfMeasure == "SACHET") {
                    //         $toastService.create("Quantity Can not be in Decimal value for : " + packaging.unitOfMeasure);
                    //         packaging.value = null;
                    //         value = 0;
                    //     }
                    //     if (packaging.packagingType != "LOOSE") {
                    //         $toastService.create("Quantity Can not be in Decimal value as the packaging is Not Loose");
                    //         packaging.value = null;
                    //         value = 0;
                    //     }
                    // }
                    if ((!$scope.checkEmpty(value) && parseFloat(value) >= 0)) {
                        var mappings = product.packagingMappings;
                        if (!$scope.checkEmpty(value) && !$scope.checkEmpty(packaging)) {
                            calculateVariance(mappings, product);
                            $scope.edited = true;                   //edited stock flag turn to true
                        }
                    } else if (!$scope.checkEmpty(value) && parseFloat(value) < 0) {
                        $toastService.create("Please select a value greater than or equal to zero");
                    }
                }else {
                    $scope.removeMapping(product.packagingMappings,packaging,product,false)
                }
            };

                function checkIfOpeningNeeded(unitId) {
                    $http({
                        method: 'GET',
                        url: apiJson.urls.stockManagement.checkOpening,
                        params: {
                            unitId: unitId
                        }
                    }).then(function (response) {
                        $scope.isOpening = response.data;
                        if ($scope.isOpening) {
                            $scope.selectFrequency("MONTHLY");
                        }
                    }, function (response) {
                        console.log(response);
                        $scope.isOpening = false;
                    });
                }

                function saveInventory(products) {
                    var finalList = $scope.getFinalProductList();if (!$scope.checkEmpty(finalList) && finalList.length > 0 && $scope.edited) {
                        $toastService.create("Auto saving the inventory list");
                        $scope.lastSaved = Date.now();
                        appUtil.setSavedInventoryList({
                            unit: $scope.unitId,
                            products: !$scope.checkEmpty(products) ? products : finalList,
                            stockType: $scope.frequencyValue,
                            lastSaved: $scope.lastSaved
                        });
                        $scope.edited = false; // reset edited flag so that it can be re-saved on edit
                    }
                }

                $scope.getFinalProductList = function () {
                console.log("data is : ", $scope.productsByFillingType);
                var mandatoryProducts = !appUtil.isEmptyObject($scope.productsByFillingType.MANDATORY) ? $scope.productsByFillingType.MANDATORY : [];
                var optionalProducts = !appUtil.isEmptyObject($scope.productsByFillingType.OPTIONAL) ? $scope.productsByFillingType.OPTIONAL : [];
                $scope.productsByFillingType.ALL = mandatoryProducts.concat(optionalProducts);
                return $scope.productsByFillingType.ALL;
            };

            $scope.resetFinalProductList = function () {
                $scope.currentProductFillingType = "MANDATORY";
                $scope.productsByFillingType = {};
                $scope.mandatoryFillProducts = {};
                $scope.optionalFillProducts = {};
                $scope.productsByFillingType.MANDATORY = Object.values($scope.mandatoryFillProducts);
                $scope.productsByFillingType.OPTIONAL = Object.values($scope.optionalFillProducts);
                $scope.productsByFillingType.ALL = $scope.productsByFillingType.MANDATORY.concat($scope.productsByFillingType.OPTIONAL);
            };

            function checkZeroVarianceOrSMProduct(product) {
                if (!appUtil.isEmptyObject(product.varianceType) && product.varianceType === "ZERO_VARIANCE") {
                    return true;
                }
                if (!appUtil.isEmptyObject(product.categoryDefinition) && !appUtil.isEmptyObject(product.categoryDefinition.id) && product.categoryDefinition.id === 4) {
                    return true;
                }
                return false;
            }

            $scope.seggregateMandatoryOptionalProducts = function (products) {
                if (!appUtil.isEmptyObject(products)) {
                    $scope.productsByFillingType = {};
                    $scope.mandatoryFillProducts = {};
                    $scope.optionalFillProducts = {};
                    for (var i = 0; i < products.length; i++) {
                        var product = products[i];
                        if ($scope.varianceWaringProducts.indexOf(product.productId) >= 0) {
                            if (product.varianceWarningStatus === undefined || product.varianceWarningStatus === null) {
                                product.varianceWarningStatus = "NO_WARNING";
                            }
                        }
                        var productData = $scope.productMap[product.productId];
                        if (!appUtil.isEmptyObject(productData)) {
//                            var zeroVarianceOrSMProduct = checkZeroVarianceOrSMProduct(productData);
//                            if (zeroVarianceOrSMProduct) {
                                if ($scope.frequencyValue != undefined && $scope.frequencyValue != null) {
                                    if ($scope.frequencyValue === "MONTHLY") {
                                        if (!appUtil.isEmptyObject(product.opening) && product.opening !== 0) {
                                            $scope.mandatoryFillProducts[product.productId] = product;
                                            continue;
                                        }
                                        if (!appUtil.isEmptyObject(product.transferred) && product.transferred !== 0) {
                                            $scope.mandatoryFillProducts[product.productId] = product;
                                            continue;
                                        }
                                        if (!appUtil.isEmptyObject(product.wasted) && product.wasted !== 0) {
                                            $scope.mandatoryFillProducts[product.productId] = product;
                                            continue;
                                        }
                                        if (!appUtil.isEmptyObject(product.received) && product.received !== 0) {
                                            $scope.mandatoryFillProducts[product.productId] = product;
                                            continue;
                                        }
                                        if (!appUtil.isEmptyObject(product.consumption) && product.consumption !== 0) {
                                            $scope.mandatoryFillProducts[product.productId] = product;
                                            continue;
                                        }
                                        $scope.optionalFillProducts[product.productId] = product;
                                    } else if($scope.frequencyValue === "WEEKLY") {
                                        if (productData.stockKeepingFrequency === "WEEKLY" || productData.stockKeepingFrequency === "DAILY") {
                                            if (!appUtil.isEmptyObject(product.transferred) && product.transferred !== 0) {
                                                $scope.mandatoryFillProducts[product.productId] = product;
                                                continue;
                                            }
                                            if (!appUtil.isEmptyObject(product.opening) && product.opening !== 0) {
                                                $scope.mandatoryFillProducts[product.productId] = product;
                                                continue;
                                            }
                                            if (!appUtil.isEmptyObject(product.received) && product.received !== 0) {
                                                $scope.mandatoryFillProducts[product.productId] = product;
                                                continue;
                                            }
                                            if (!appUtil.isEmptyObject(product.wasted) && product.wasted !== 0) {
                                                $scope.mandatoryFillProducts[product.productId] = product;
                                                continue;
                                            }
                                            if (!appUtil.isEmptyObject(product.consumption) && product.consumption !== 0) {
                                                $scope.mandatoryFillProducts[product.productId] = product;
                                                continue;
                                            }
                                            $scope.optionalFillProducts[product.productId] = product;
                                        } else {
                                            $scope.optionalFillProducts[product.productId] = product;
                                        }
                                    } else {
                                        if (productData.stockKeepingFrequency === $scope.frequencyValue) {
                                            if (!appUtil.isEmptyObject(product.transferred) && product.transferred !== 0) {
                                                $scope.mandatoryFillProducts[product.productId] = product;
                                                continue;
                                            }
                                            if (!appUtil.isEmptyObject(product.opening) && product.opening !== 0) {
                                                $scope.mandatoryFillProducts[product.productId] = product;
                                                continue;
                                            }
                                            if (!appUtil.isEmptyObject(product.received) && product.received !== 0) {
                                                $scope.mandatoryFillProducts[product.productId] = product;
                                                continue;
                                            }
                                            if (!appUtil.isEmptyObject(product.wasted) && product.wasted !== 0) {
                                                $scope.mandatoryFillProducts[product.productId] = product;
                                                continue;
                                            }
                                            if (!appUtil.isEmptyObject(product.consumption) && product.consumption !== 0) {
                                                $scope.mandatoryFillProducts[product.productId] = product;
                                                continue;
                                            }
                                            $scope.optionalFillProducts[product.productId] = product;
                                        } else {
                                            $scope.optionalFillProducts[product.productId] = product;
                                        }
                                    }
                                } else {
                                    $scope.optionalFillProducts[product.productId] = product;
                                }
//                            }
//                             else {
//                                $scope.optionalFillProducts[product.productId] = product;
//                            }
                        } else {
                            $scope.optionalFillProducts[product.productId] = product;
                        }
                    }
                    $scope.productsByFillingType.MANDATORY = Object.values($scope.mandatoryFillProducts);
                    $scope.productsByFillingType.OPTIONAL = Object.values($scope.optionalFillProducts);
                    $scope.productsByFillingType.ALL = $scope.productsByFillingType.MANDATORY.concat($scope.productsByFillingType.OPTIONAL);
                }
            };

            $scope.setMaxDateForProducts = function () {
                $scope.maxDatesOfProducts = {};
                angular.forEach(Object.values($scope.productMap), function (productItem) {
                    var product = $scope.productMap[productItem.productId];
                    var maxDate = null;
                    if (product !== undefined && product !== null) {
                        if (product.shelfLifeInDays > 0) {
                            maxDate = appUtil.formatDate(appUtil.getDate(product.shelfLifeInDays), "yyyy-MM-dd");
                        } else {
                            maxDate = appUtil.formatDate(appUtil.getDate(1), "yyyy-MM-dd");
                        }
                    } else {
                        maxDate = appUtil.formatDate(appUtil.getDate(1), "yyyy-MM-dd");
                    }
                    $scope.maxDatesOfProducts[productItem.productId] = maxDate;
                });
            };

            $scope.setExpiryDate = function (selectedExpiry, expiryTime, item) {
                var parsedDate = new Date(selectedExpiry);
                parsedDate.setHours(parseInt(expiryTime));
                parsedDate.setMinutes(0);
                parsedDate.setSeconds(0);
                item.selectedExpiryDate = parsedDate;
            };

            function predictExpectedValues(frequencyValue, refresh) {
                if ($scope.unitsForStockTakeThroughApp.indexOf($scope.unitId) >= 0 && ($scope.submittedStockTakeEvent === undefined || $scope.submittedStockTakeEvent === null || $scope.submittedStockTakeEvent.stockTakeSumoDayCloseEventId == null)) {
                    $alertService.alert("Fill Closing Inventory Through App..!", "<b>Please Fill the Closing Inventory through Stock Take App..!</b><br><b></b>", null, true);
                    return;
                }
                var currentUser = appUtil.getCurrentUser();
                $alertService.alert("Please Wait", "<b>Please Wait it May Take a Few Minutes To get the Data....!</b><br><b>This Will be Closed Automatically..!</b>", function (result) {
                }, false);
                $http({
                    method: 'POST',
                    url: apiJson.urls.stockManagement.predictExpectedValues,
                    data: {
                        unitId: currentUser.unitId,
                        stockTakeType: frequencyValue,
                        userId: currentUser.userId,
                        stockEventType: $scope.isOpening ? "OPENING" : "CLOSING",
                        businessDate: $scope.dayCloseBusinessDate
                    }
                }).then(function (response) {
                    if (!appUtil.checkEmpty(response)) {
                        $scope.fixedAssetsOnly = (frequencyValue == "FIXED_ASSETS");
                        var productList = response.data.inventoryResponse;
                        packagingService.getAllPackagingMappings(function (packagingMappings) {
                            $scope.packagingMappings = packagingMappings;
                            setProducts(productList, $scope.unitsForStockTakeThroughApp.indexOf($scope.unitId) >= 0);
                        });
                        var saved = appUtil.getSavedInventoryList();
                        if (!$scope.checkEmpty(saved) && !$scope.checkEmpty(saved.products)) {
                            var savedList = angular.copy(saved.products);
                            var savedProductMap = [];

                            savedList.forEach(function (product) {
                                savedProductMap[product.productId] = product;
                            });

                            productList.forEach(function (product) {
                                var savedProduct = savedProductMap[product.productId];
                                if (!$scope.checkEmpty(savedProduct) &&
                                    !$scope.checkEmpty(savedProduct.packagingMappings)) {
                                    product.packagingMappings = savedProduct.packagingMappings;
                                    calculateVariance(savedProduct.packagingMappings, product);
                                }
                            });
                        }

                        $scope.productList = productList; // assigning products after massaging
                        $scope.seggregateMandatoryOptionalProducts($scope.productList);
                    }
                    $alertService.closeAlert();
                }, function (response) {
                    console.log(response);
                    $alertService.closeAlert();
                    if (response.data.errorMsg != null) {
                        $alertService.alert(response.data.errorTitle, response.data.errorMsg, function (result) {
                        }, false);
                    } else {
                        $toastService.create("Upload Failed! Please try again");
                    }
                });
            }

                function getProductName(scmProductMap, toFind) {

                    var product = scmProductMap[toFind.productId];
                    if(!appUtil.isEmptyObject(product)) {
                        toFind.productName = product.productName;
                        toFind.unitOfMeasure = product.unitOfMeasure;
                        return toFind;
                    }
                    product = $scope.productsCache[toFind.productId];
                    if(!appUtil.isEmptyObject(product)) {
                        toFind.productName = product.name;
                        toFind.unitOfMeasure = product.code;
                        return toFind;
                    }
                    console.log("Product not found in the list  : : : ", toFind.productId);

                }





                function findValue(packaging, selectedPackagings) {
                    if (!appUtil.isEmptyObject(selectedPackagings)) {
                        for (var i = 0; i < selectedPackagings.length; i++) {
                            var selected = selectedPackagings[i];
                            if (packaging.packagingId == selected.packagingId) {
                                return selected.value;
                            }
                        }
                    }
                }

                function setProducts(productList , syncQuantitiesFromStockTakeApp) {
                    if (productList == undefined) {
                        productList = $scope.productList;
                    }
                    if ($scope.checkEmpty(packagingService.definitions)) {
                        $scope.packagings = appUtil.getPackagingMap();
                        packagingService.setAllProfiles($scope.packagings);
                    }
                    $rootScope.showSpinner = true;
                    var scmProductMap = appUtil.getScmProductDetailMap();
                    productList.forEach(function (product) {
                        var productId = product.productId;
                        product = getProductName(scmProductMap, product);
                        if (!appUtil.isEmptyObject(product)) {
                            var pkgValueMap = {};
                            if (!appUtil.isEmptyObject(product.packagingMappings)) {
                                product.packagingMappings.forEach(function (mpg) {
                                    if (!appUtil.isEmptyObject(mpg) && !appUtil.isEmptyObject(mpg.packagingId)) {
                                    pkgValueMap[mpg.packagingId] = mpg.value;
                                    var key = product.productId + "_" + mpg.packagingId;
                                    if (syncQuantitiesFromStockTakeApp && $scope.submittedStockTakeEventProductsMap[key] !== undefined && $scope.submittedStockTakeEventProductsMap[key] !== null) {
                                        pkgValueMap[mpg.packagingId] = $scope.submittedStockTakeEventProductsMap[key];
                                    }
                                }
                                });
                            }
                            product = packagingService.setMappingsByProduct(product, $scope.packagingMappings[productId]);
                            var autoFilled = false;product.packagingMappings.forEach(function (pkg) {
                            if (!appUtil.isEmptyObject(pkg)) {
                                pkg.value = (!appUtil.isEmptyObject(pkg.packagingId) && !appUtil.isEmptyObject(pkgValueMap[pkg.packagingId])) ? pkgValueMap[pkg.packagingId] : null;
                                var key = product.productId + "_" + pkg.packagingId;
                                if (syncQuantitiesFromStockTakeApp && $scope.submittedStockTakeEventProductsMap[key] !== undefined && $scope.submittedStockTakeEventProductsMap[key] !== null) {
                                    pkg.value = $scope.submittedStockTakeEventProductsMap[key];
                                    autoFilled = true;
                                }
                            }
                        });
                        if (autoFilled) {
                            calculateVariance(product.packagingMappings,product);
                            $scope.edited = true;
                        }
                    }
                });
                $rootScope.showSpinner = false;
            }

                function addToMappings(mappings, packaging, value) {
                    /* var added = false;
                     mappings.forEach(function(mapping){
                         if(mapping.packaging.packagingCode == packaging.packagingCode){
                             added = true;
                             mapping.value = value;
                         }
                     });
                     if(!added){
                         mappings.push({value:value,packaging:packaging});
                     }
                     return mappings;*/
                }

                function checkIfMappingsHaveValues(mappings) {
                    return mappings.filter(function (mapping) {
                        return !appUtil.isEmptyObject(mapping) && !appUtil.isEmptyObject(mapping.value);
                    }).length > 0;
                }

                $scope.getOrderBy = function () {
                if ($scope.currentProductFillingType == 'ALL') {
                    return ['duplicateStockValue', 'productName'];
                } else {
                    return ['productName'];
                }
            };function calculateVariance(mappings, product) {
                    if (mappings.length > 0 && checkIfMappingsHaveValues(mappings)) {
                        product.stockValue = 0;

                        mappings.forEach(function (mapping) {
                            if (!appUtil.isEmptyObject(mapping) && !appUtil.isEmptyObject(mapping.value)) {
                                product.stockValue = parseFloat(product.stockValue)
                                    + (parseFloat(mapping.value) * parseFloat(mapping.conversionRatio));
                            }
                        });
                        product.variance = parseFloat(product.expectedValue - product.stockValue);
                        if (appUtil.checkForNotUndefinedOrNotNull(product.dailyExpectedValue)) {
                            product.dailyVariance = parseFloat(product.dailyExpectedValue - product.stockValue);
                        }
                    } else {
                        product.stockValue = !appUtil.isEmptyObject(product.expectedValue) ? parseFloat(product.expectedValue) : 0;product.variance = 0;
                }
                    product.duplicateStockValue =null;
                angular.forEach(product.packagingMappings, function (mapping) {
                    if (!appUtil.isEmptyObject(mapping) && !appUtil.isEmptyObject(mapping.value)) {
                        if (product.duplicateStockValue == null) {
                            product.duplicateStockValue = 0;
                        }
                        product.duplicateStockValue +=mapping.value;
                    }
                    });
                product.specialProductDeviation = false;
                product.semiFinishedDeviation = false;
                product.highValueDeviation = false;
                if ($scope.varianceWaringProducts.indexOf(product.productId) >= 0) {
                    product.variancePerecentageDiff = (Math.abs((product.stockValue - product.expectedValue)) / product.expectedValue) * 100;
                    if(product.reVerifiedCount == undefined || product.reVerifiedCount == null) {
                        if (product.variancePerecentageDiff > 200) {
                            product.varianceWarningStatus = "RE_VERIFICATION_REQUIRED";
                            product.specialProductDeviation = true;
                        } else {
                            product.varianceWarningStatus = "NO_WARNING";
                        }
                    }
            }
            // checking the amount checks and Quantity Checks
               if(product.reVerifiedCount == undefined || product.reVerifiedCount == null) {
                    var productData = $scope.productMap[product.productId];
                    if(productData != undefined && productData != null && !appUtil.isEmptyObject(product.categoryDefinition) && !appUtil.isEmptyObject(product.categoryDefinition.id) && product.categoryDefinition.id === 4) {
                        var itemCountDiff = Math.abs(product.stockValue - product.expectedValue);
                        if (itemCountDiff > 3) {
                            product.varianceWarningStatus = "RE_VERIFICATION_REQUIRED";
                            product.semiFinishedDeviation = true;
                        }
                    }

                    var itemCostDiff = Math.abs((product.stockValue * product.unitPrice) - (product.expectedValue * product.unitPrice));
                    if (itemCostDiff > 300) {
                        product.varianceWarningStatus = "RE_VERIFICATION_REQUIRED";
                        product.highValueDeviation = true;
                    }
               }

               if (product.specialProductDeviation || product.semiFinishedDeviation || product.highValueDeviation) {
                    product.varianceWarningStatus = "RE_VERIFICATION_REQUIRED";
               } else{
                    product.varianceWarningStatus = "NO_WARNING";
               }
           }


            $scope.openVarianceWarningModal = function(product) {
                var varianceWarningModal = Popeye.openModal({
                    templateUrl: "varianceEditWarning.html",
                    controller: "varianceEditWarningCtrl",
                    resolve: {
                        mappings : function (){
                            return product.packagingMappings;
                        }, product : function (){
                            return product;
                        },
                        currentUserId : function () {
                            return $scope.currentUserId
                        }
                    },
                    modalClass:'custom-modal',
                    click: false,
                    keyboard: false
                });

                varianceWarningModal.closed.then(function(data){
                    if(appUtil.isEmptyObject(data)) {
                        angular.forEach(product.packagingMappings, function (packaging) {
                            packaging.value = null;
                            $scope.removeMapping(product.packagingMappings, packaging, product, false);
                        });
                    } else {
                        product.varianceWarningStatus = "RE_VERIFIED";
                        product.reVerifiedCount = 1;
                    }
                });
            };

                function checkInventoryUpdated(unitId) {
                    $http({
                        method: 'POST',
                        url: apiJson.urls.stockManagement.inventoryUpdated,
                        data: {
                            unitId: unitId,
                            businessDate: appUtil.getCurrentBusinessDate()
                        }
                    }).then(function (response) {
                        $scope.inventoryUpdated = response.data;

                        if (response.data && notFixedAssets()) {
                            appUtil.removeSavedInventoryList();
                            $interval.cancel($scope.refreshInterval);
                            $scope.lastSaved = null;
                        }
                    }, function (response) {
                        console.log("got error", response);
                    });
                }

                $scope.refreshDayclose = function () {
                    $http({
                        method: 'POST',
                        url: apiJson.urls.stockManagement.kettleDayCloseFromSumo,
                        params: {
                            unitId: $scope.unitId,
                        }
                    }).then(function (response) {
                        console.log(response);
                        if (response.data == true) {
                            $scope.checkClosingInitiated($scope.unitId)
                            $toastService.create("Day Closed Successfully Completed From Kettle!");
                        } else {
                            $toastService.create("Day Closed Cannot Be Completed Because Of Some Error!");
                        }
                    });
                }


                function notFixedAssets() {
                    var inventory = appUtil.getSavedInventoryList();
                    return !appUtil.isEmptyObject(inventory) && inventory.stockType != "FIXED_ASSETS";
                }
            }
        ]
    ).controller('varianceEditWarningCtrl', ['$scope', 'mappings','product','currentUserId','appUtil', '$toastService', 'apiJson', '$http', 'Popeye',
        function ($scope, mappings,product ,currentUserId,appUtil, $toastService, apiJson, $http, Popeye) {
            $scope.init = function () {
                $scope.mappings = mappings;
                $scope.duplicateMappings = angular.copy(mappings);
                $scope.duplicateProduct = angular.copy(product);
                $scope.currentUserId = currentUserId;
                $scope.roi = angular.copy(product);
                $scope.roi.stockValue = 0;
                angular.forEach($scope.roi.packagingMappings, function (packaging) {
                    packaging.value = null;
                });
            };

            $scope.submit = function (){
                if($scope.roi.stockValue != $scope.duplicateProduct.stockValue) {
                    $toastService.create("Re-Entered Quantity Doesn't Match With Previously Entered Quantity!!!!");
                    Popeye.closeCurrentModal();
                } else {
                    $scope.roi.varianceWarningStatus = "RE-VERIFIED";
                    Popeye.closeCurrentModal({success : true})
                }
            };

            $scope.changeStock = function (product,value,packaging) {
                if (!appUtil.isEmptyObject(value)) {
                    if ((!appUtil.checkEmpty(value) && parseFloat(value) >= 0)) {
                        var mappings = product.packagingMappings;
                        if (!appUtil.checkEmpty(value) && !appUtil.checkEmpty(packaging)) {
                            calculateVariance(mappings, product, true);
                        }
                    } else if (!appUtil.checkEmpty(value) && parseFloat(value) < 0) {
                        $toastService.create("Please select a value greater than or equal to zero");
                    }
                } else {
                    $scope.removeMapping(product.packagingMappings,packaging,product,false)
                }
            };

            $scope.removeMapping = function(selectedPackagings,selected,product, makeZero){
                selected.value = null;
                if (makeZero != undefined && makeZero != null && makeZero) {
                    selected.value = 0;
                }
                calculateVariance(selectedPackagings,product, false);
            };

            function checkIfMappingsHaveValues(mappings) {
                return mappings.filter(function(mapping){
                    return !appUtil.isEmptyObject(mapping) && !appUtil.isEmptyObject(mapping.value);
                }).length>0;
            }

            function calculateVariance(mappings,product) {
                if(mappings.length > 0 && checkIfMappingsHaveValues(mappings)) {
                    product.stockValue = 0;
                    mappings.forEach(function (mapping) {
                        if(!appUtil.isEmptyObject(mapping) && !appUtil.isEmptyObject(mapping.value)){
                            product.stockValue = parseFloat(product.stockValue)
                                + (parseFloat(mapping.value) * parseFloat(mapping.conversionRatio));
                        }
                    });
                    product.variance = parseFloat(product.expectedValue - product.stockValue);
                    if (appUtil.checkForNotUndefinedOrNotNull(product.dailyExpectedValue)) {
                        product.dailyVariance = parseFloat(product.dailyExpectedValue - product.stockValue);
                    }
                }else{
                    product.stockValue = !appUtil.isEmptyObject(product.expectedValue) ? parseFloat(product.expectedValue) : 0;
                    product.variance = 0;
                }

                product.duplicateStockValue = null;
                angular.forEach(product.packagingMappings, function (mapping) {
                    if (!appUtil.isEmptyObject(mapping) && !appUtil.isEmptyObject(mapping.value)) {
                        if (product.duplicateStockValue == null) {
                            product.duplicateStockValue = 0;
                        }
                        product.duplicateStockValue +=mapping.value;
                    }
                });
            }

        }
    ]
);

'use strict';
angular.module('scmApp').controller('viewSRCtrl', ['$rootScope', '$stateParams', '$scope', 'apiJson', '$http',
    'appUtil', '$toastService','$alertService','metaDataService','Popeye','$timeout',
    function ($rootScope, $stateParams, $scope, apiJson, $http,
              appUtil, $toastService,$alertService,metaDataService, Popeye, $timeout) {


        $scope.getCreatedSRs = function(startDate,endDate, vendor, location) {
            $scope.fetchSrs(startDate,endDate,vendor,location,false,null);
        };

         $scope.getCreatedSRsShort = function (startDate,endDate,vendor,location){
             $scope.fetchSrs(startDate,endDate,vendor,location,true,null);
         }

         $scope.setSelectedSO = function (v) {
             $scope.serviceProof = v;
         }

        $scope.fetchSrs = function (startDate,endDate, vendor, location,isShort,callback){

            if(appUtil.isEmptyObject(startDate)){
                $toastService.create("Please select a start date first");
                return;
            }
            if(appUtil.isEmptyObject(endDate)){
                $toastService.create("Please select a end date first");
                return;
            }



            var params = {
                startDate:startDate,
                endDate:endDate,
                userId: appUtil.getCurrentUser().userId,
                serviceOrderId: callback!=null ? $scope.selectedSrId : $scope.srId
            };

            if(!appUtil.isEmptyObject(vendor)){
                params["vendorId"] = vendor.id;
            }


            if(!appUtil.isEmptyObject(location)){
                params["locationId"] = location.id;
            }

            if(!appUtil.isEmptyObject($scope.bccSelected)){
                params["bccId"] = $scope.bccSelected.id;
            }

            $scope.endPoint = apiJson.urls.serviceReceivedManagement.findReceivings;
            if(isShort){
                $scope.endPoint = apiJson.urls.serviceReceivedManagement.findReceivingsShort;
            }

            $http({
                method: "GET",
                url: $scope.endPoint,
                params: params
            }).then(function (response) {
                if (appUtil.isEmptyObject(response)) {
                    $toastService.create("No Receiving found!");
                } else {
                    if(callback!=null){
                        callback(response.data[0]);
                    }
                    else{
                        if(isShort){
                            $scope.srRequestShort = response.data;
                        }
                        else
                            $scope.srRequest = response.data;
                    }

                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });

        }


        function updateSr(url, callback) {
            $alertService.confirm("Are you sure?","",function(result){
                if(result){
                    $http({
                        method: "POST",
                        url: url
                    }).then(function (response) {
                        if (!appUtil.isEmptyObject(response)) {
                            callback(response.data);
                        }
                    }, function (err) {
                        console.log("Encountered error at backend", err);
                    });
                }
            });

        }

        $scope.printSRWithCallBack = function (sr){
            $scope.selectedSrId = sr.id;
            $scope.fetchSrs(appUtil.convertToDateWithoutTime(sr.creationTime),appUtil.convertToDateWithoutTime(sr.creationTime),
                sr.vendor,sr.location,false,$scope.printSR);

        }

        $scope.printSR = function (sr) {
            $scope.currentPrintSR = sr;
            $scope.currentPrintSR["companyAddress"] = $scope.companyMap[sr.company.id].registeredAddress;
            $timeout(function() {
                angular.element('#printDiv').trigger('click');
            });
        };

        $scope.approveSR = function (srId){
            $http({
                method: "POST",
                url: apiJson.urls.serviceReceivedManagement.approveSR,
                params: {
                    srId : srId
                }
            }).then(function success(response) {
                if (response.data) {
                    $toastService.create("SR Approved successfully.");
                    $scope.getSRsShort();

                } else {
                    $toastService.create("Error in Approving Service Receive");
                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });

        };

        $scope.init = function () {
            var currentDate = appUtil.getCurrentBusinessDate();
            if(!appUtil.isEmptyObject(currentDate)){
                $scope.startDate = appUtil.formatDate(appUtil.calculatedDate(-3, currentDate), "yyyy-MM-dd");
                $scope.endDate = appUtil.formatDate(currentDate, "yyyy-MM-dd");
                $scope.vendorSelected = $stateParams.selectedVendor;
                $scope.bccIds = [];
                $scope.locationSelected = $stateParams.selectedDispatchLocation;
                $scope.srRequest = [];
                $scope.srRequestShort = [];
                $scope.currentUser = appUtil.getCurrentUser();

                metaDataService.getCompanyList(function(companies){
                    $scope.companyMap = {};
                    for(var i in companies){
                        $scope.companyMap[companies[i].id] = companies[i];
                    }
                });

                metaDataService.getServiceVendors(function(vendors){
                    $scope.vendors = vendors;
                	$scope.getCreatedSRsShort($scope.startDate, $scope.endDate, $scope.vendors[0], $scope.locationSelected);
                });
                metaDataService.getAllBusinessCostCenters(function(bcc){
                    $scope.bcc = bcc;
                });

            }
        };

        $scope.reset = function () {
          $scope.vendorSelected = null;
          $scope.locationSelected = null;
        };

        $scope.filteredByReceivedQuantity = function(item){
            return item.receivedQuantity != null && item.receivedQuantity > 0;
        };

        $scope.selectVendor = function (vendor) {
            $scope.vendorSelected = vendor;
            metaDataService.getVendorLocations($scope.vendorSelected.id, function (locations) {
                $scope.locationList = locations;
            });
        };

        $scope.selectDispatchLocation = function (location) {
            $scope.locationSelected = location;
        };

        $scope.getSRs = function(){
          $scope.getCreatedSRs($scope.startDate,$scope.endDate, $scope.vendorSelected, $scope.locationSelected);
        };

        $scope.getSRsShort = function (){
            $scope.getCreatedSRsShort($scope.startDate,$scope.endDate, $scope.vendorSelected, $scope.locationSelected);
        }

        $scope.cancel = function (approveID, index) {
            var url = apiJson.urls.serviceReceivedManagement.cancel + "/" + approveID + "/" + $scope.currentUser.userId;
            updateSr(url, function (updated) {
                if(updated){
                    $toastService.create("Service Receiving cancelled successfully");
                    $scope.srRequestShort[index].status = "CANCELLED";
                }else{
                    $toastService.create("Service Receiving cancellation failed! Please try again later..");
                }
            });
        };

        $scope.viewDetailSR = function (sr){
            $scope.selectedSrId = sr.id;
            $scope.fetchSrs(appUtil.convertToDateWithoutTime(sr.creationTime),appUtil.convertToDateWithoutTime(sr.creationTime),sr.vendor,
                sr.location, false,$scope.viewDetail);
        }

        $scope.viewDetail = function (sr) {
            var viewDetailModal = Popeye.openModal({
                templateUrl: "viewSRDetail.html",
                controller: "viewSRDetailCtrl",
                resolve: {
                    sr: function(){
                        return sr;
                    },
                    approveSR : function (){
                        return $scope.approveSR;
                    }
                },
                modalClass:'custom-modal',
                click: false,
                keyboard: false
            });
        };

        $scope.emailPopup = function (srId) {
            var sendMbEmailModal = Popeye.openModal({
                templateUrl: "vendorMbEmail.html",
                controller: "vendorMbEmailCtrl",
                resolve: {
                    srId : function (){
                        return srId;
                    }
                },
                modalClass:'custom-modal',
                click: false,
                keyboard: false
            });
            sendMbEmailModal.closed.then(function (){
                $scope.initViewModal()
            })
        };



    }
]).controller('viewSRDetailCtrl', ['$scope','appUtil','sr','approveSR','Popeye',function($scope,appUtil,sr,approveSR,Popeye){
    $scope.initViewModal = function () {
      $scope.sr = sr;
      $scope.billAmount = 0;
  	$scope.totalTaxes = 0;
  	$scope.paidAmount = 0;
      $scope.getSummaryData();
      $scope.selectedSrItem = null;
      $scope.approve = approveSR;
    };


    $scope.approveSR = function (srId){
        $scope.approve(srId);
        $scope.closeModal(false);
    }

    $scope.closeModal = function closeModal(check) {
        Popeye.closeCurrentModal(check);
    }

    $scope.showDrillDowns = function (srItem){
        if(srItem.expanded!=null && srItem.expanded != undefined){
            srItem.expanded = !srItem.expanded;
        }
        else{
            srItem.expanded = true;
        }
        //srItem.sourceUom = srItem.serviceReceivedItemDrillDown[0].sourceUom;


    }

    $scope.isSelectedItem = function (srItem){
        return srItem.expanded == true;
    }

    $scope.getSummaryData = function (){
    	for(var x = 0 ; x < $scope.sr.serviceReceiveItems.length ; x++){
    		$scope.billAmount = $scope.sr.serviceReceiveItems[x].totalCost + $scope.billAmount;
    		$scope.totalTaxes = $scope.sr.serviceReceiveItems[x].totalTax + $scope.totalTaxes;
    		$scope.paidAmount = $scope.sr.serviceReceiveItems[x].totalCost + $scope.sr.serviceReceiveItems[x].totalTax + $scope.paidAmount;
    	}
    }


}]).controller('vendorMbEmailCtrl', ['$scope','$http','$toastService','appUtil','apiJson','srId',function($scope,$http,$toastService,appUtil,apiJson,srId){
    $scope.init = function () {
        $scope.srId = srId;
        $scope.toEmails = [];
        $scope.ccEmails = [];
        $scope.enteredToEmail = null;
        $scope.enteredCcEmail = null;
        $scope.userEmail = appUtil.currentUser.user.employeeEmail;
    };

    $scope.removeToEmail = function (email){
        var index = $scope.toEmails.indexOf(email);
        $scope.toEmails.splice(index,1);
    }

    $scope.removeCcEmail = function (email){
        var index = $scope.ccEmails.indexOf(email);
        $scope.ccEmails.splice(index,1);
    }

    $scope.addToEmail = function(){
        if(!appUtil.isEmptyObject($scope.enteredToEmail)){
            $scope.toEmails.push($scope.enteredToEmail);
            $scope.enteredToEmail = null;
        }
        console.log("to emails :",$scope.toEmails);
    }
    $scope.addCcEmail = function(){
        if(!appUtil.isEmptyObject($scope.enteredCcEmail)){
            $scope.ccEmails.push($scope.enteredCcEmail);
            $scope.enteredCcEmail = null;
        }
        console.log("CC emails :",$scope.ccEmails);
    }
    $scope.sendMbEmail = function (){
        //adds user email in cc
        $scope.ccEmails.push($scope.userEmail);
        $http({
            method: "POST",
            url: apiJson.urls.serviceReceivedManagement.sendMbEmail ,
            data: {
                srId : $scope.srId,
                toEmails : $scope.toEmails,
                ccEmails : $scope.ccEmails,
                initiatedBy : {
                    id : appUtil.currentUser.user.id,
                    code : null,
                    name : appUtil.currentUser.user.name
                }
            }
        }).then(function (response) {

            $toastService.create("Successfully Send Email");
            $scope.init();

        }, function (err) {
                $toastService.create("error while sending email");
        });
    }

}]).controller('viewSRsDetailCtrl',['$http','$scope','pr','appUtil','apiJson','$toastService','metaDataService',
    function ($http, $scope, pr, appUtil, apiJson, $toastService, metaDataService) {
        $scope.initViewModal = function(){
            $scope.pr = pr;
            $scope.srs = null;
            $scope.companyMap = {};
            metaDataService.getCompanyList(function(companies){
                for(var i in companies){
                    $scope.companyMap[companies[i].id] = companies[i];
                }
            });
            getServiceReceivings(pr.paymentRequestId);
        };

        function getServiceReceivings(reqId) {
            $http({
                method: "GET",
                url: apiJson.urls.serviceReceivedManagement.findReceivingsByPR,
                params: {paymentRequestId: reqId}
            }).then(function (response) {
                if (appUtil.isEmptyObject(response)) {
                    $toastService.create("No Receiving found!");
                } else {
                    $scope.srs = response.data;
                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });
        }
}]);

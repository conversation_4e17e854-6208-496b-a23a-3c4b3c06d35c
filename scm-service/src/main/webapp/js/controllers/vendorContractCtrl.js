'use strict';
angular.module('scmApp').controller('vendorContractCtrl', ['$rootScope', '$stateParams', '$scope', 'apiJson', '$http', '$state',
    'appUtil', '$toastService', '$alertService', 'metaDataService', '$fileUploadService', '$window', 'previewModalService', '$timeout',
    'Popeye', 'ScmApiService', 'toast',
    function ($rootScope, $stateParams, $scope, apiJson, $http, $state, appUtil,
              $toastService, $alertService, metaDataService, $fileUploadService, $window,
              previewModalService, $timeout,Popeye, ScmApiService, toast) {


        $scope.init = function () {
            $scope.unitData = appUtil.getUnitData();
            $scope.companyMap = appUtil.getCompanyMap();
            var currentDate = appUtil.getCurrentBusinessDate();
            if (!appUtil.isEmptyObject(currentDate)) {
                // Initialize date filters
                $scope.startDate = appUtil.formatDate(currentDate, "yyyy-MM-dd");
                $scope.endDate = $stateParams.createdInvoice != null
                    ? appUtil.formatDate($stateParams.createdInvoice.dispatchDate, "yyyy-MM-dd")
                    : $scope.startDate;

                // Initialize view state
                $scope.showViewActions = $stateParams.viewInvoice;
                $scope.createdInvoice = $stateParams.createdInvoice;
                $scope.invRequest = [];

                // Initialize user data
                $scope.currentUser = appUtil.getCurrentUser();

                // Initialize filter values
                $scope.selectedStatus = null;
                $scope.vendorSelected = null;
                $scope.selectedUnit = null;
                $scope.locationSelected = null;

                // Initialize contract status options
                $scope.contractStatus = [
                    "CREATED",
                    "PARTIALLY_REJECTED",
                    "PENDING_VENDOR_APPROVAL",
                    "VENDOR_APPROVED",
                    "VENDOR_REJECTED",
                    "APPROVER_BY_PASSED",
                    "APPROVER_REJECTED",
                    "APPLIED",
                    "CANCELLED",
                    "EXPIRED",
                    "DEACTIVATED",
                    "REMOVED_ALL"
                ];

                // Initialize hierarchy data
                $scope.workOrders = null;
                $scope.items = null;
                $scope.workOrdersLoaded = false;
                $scope.itemsLoaded = false;

                // Initialize utility functions
                $scope.showPreview = previewModalService.showPreview;
                $scope.byPassContracts = false;

                // Load initial data
                $scope.getAllVendors();
                $scope.currentUserHod = false;
                getApproversList();

                $scope.rejectedStatuses = ["VENDOR_REJECTED", "APPROVER_REJECTED", "CANCELLED", "EXPIRED", "DEACTIVATED", "REMOVED_ALL"];
            }
        };

        $scope.getAllVendors = function (skuId) {
            $scope.showAuthSigned = false;
            var listOfVendorTypes = ["VENDOR", "CUSTOMER"];
            $http({
                method: "POST",
                dataType: 'json',
                data: listOfVendorTypes,
                headers: {
                    "Content-Type": "application/json"
                },
                url: apiJson.urls.skuMapping.getAllVendorsByBusinessType,
            }).then(function success(response) {
                $scope.allVendorDataList = response.data;
            }, function error(response) {
                console.log("error:" + response);
            });
        };

        $scope.isShowApprove = function(data) {
            var currentUser = appUtil.getCurrentUser();
            if(data.approvalRequestFrom == currentUser.userId && data.status == 'VENDOR_APPROVED') {
                return true;
            }
            return false
        }

        function getCreatedInvoices(view, startDate, endDate) {
            var params = {};
            if(!appUtil.isEmptyObject(startDate)) {
                params["startDate"] = startDate;
            }
            if(!appUtil.isEmptyObject(endDate)) {
                params["endDate"] = endDate;
            }
            if(!appUtil.isEmptyObject($scope.selectedStatus)) {
                params["status"] = $scope.selectedStatus;
            }
            if (!appUtil.isEmptyObject($scope.vendorSelected)) {
                params["vendorId"] = $scope.vendorSelected.id;
            }
            if (!appUtil.isEmptyObject($scope.vendorContractId)) {
                params["vendorContractId"] = $scope.vendorContractId;
            }

            if(appUtil.isEmptyObject(params)) {
                $toastService.create("Please select any one filter to search");
                return;
            }

            $http({
                method: "GET",
                url: apiJson.urls.skuMapping.getVendorContractV2,
                params: params
            }).then(function (response) {
                if (appUtil.isEmptyObject(response)) {
                    $toastService.create("No Contract found!");
                } else {
                    $scope.contractRequest = response.data;
                    // $scope.contractRequest = $scope.contractRequest.sort(function (a, b) {
                    //     return b.id - a.id;
                    // });
                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });
        }

        $scope.getWorkOrdersByContractId = function(invR) {
            var contractId = invR.contractId;
            invR.expanded = !invR.expanded;
            if( (invR.expanded != undefined && invR.expanded != null) && invR.expanded == false) {
                return;
            }
            $scope.workOrders = null;
            $scope.items = null;
            $scope.workOrdersLoaded = false;
            $scope.itemsLoaded = false;

            var url = apiJson.urls.skuMapping.getWorkOrdersByContractId + "?contractId=" + contractId;
            ScmApiService
                .get(url)
                .then(function (responseData) {
                    $scope.workOrdersLoaded = true;
                    if (appUtil.isEmptyObject(responseData) || appUtil.isEmptyObject(responseData.data)) {
                        $scope.workOrders = [];
                        toast.warning("No work orders found for this contract");
                    } else {
                        $scope.workOrders = responseData.data;
                        angular.forEach($scope.contractRequest, function(wo) {
                            wo.expanded = false;
                        });
                        invR.expanded = true;
                    }
                })
                .catch(function(error) {
                    $scope.workOrdersLoaded = true;
                    $scope.workOrders = [];
                    console.error("Error fetching work orders:", error);
                    toast.error("Failed to load work orders");
                });
        }

        $scope.getItemsByWoId = function(wo) {

            wo.items = null;
            wo.itemsLoaded = false;
            wo.expanded = !wo.expanded;
            if(!wo.expanded) {
                return;
            }

            var url = apiJson.urls.skuMapping.getItemsByWoId + "?workOrderId=" + wo.workOrderId;
            ScmApiService.get(url).then(function (responseData) {
                wo.itemsLoaded = true;
                if (appUtil.isEmptyObject(responseData) || appUtil.isEmptyObject(responseData.data)) {
                    wo.items = [];
                    toast.warning("No items found for this work order");
                } else {
                    wo.items = responseData.data;
                }
            }).catch(function (error) {
                wo.itemsLoaded = true;
                wo.items = [];
                toast.error("Failed to load items");
            });
        };

        $scope.downloadDoc = function (id) {
            metaDataService.downloadDocumentById(id);
        }

        $scope.showCancelBtn = function(status) {
            if( $scope.rejectedStatuses.includes(status) || status == 'VENDOR_APPROVED' || status == 'APPLIED') {
                return false;
            }
            return true;
        }

        $scope.cancelContract = function(workOrderId) {
            $alertService.confirm("Are you sure want to <b>CANCEL CONTRACT</b>?", null, function (result) {
                 if (result) {
                    $http({
                        method: 'POST',
                        url: apiJson.urls.skuMapping.cancelVendorContractV2,
                        params: { workOrderId: workOrderId }
                    }).then(function (response) {
                        if (response.data) {
                            $toastService.create("WorkOrder Cancelled successfully");
                            $scope.getContracts();
                        }
                    }, function (err) {
                        $toastService.create("Encountered error at backend");
                    });
                    Popeye.closeCurrentModal(true);
                 }
            });
        }

        $scope.approveContract = function(url) {
            $window.open(url, '_blank');
        }

        $scope.copyVendorApprovalPage = function(copyText) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(copyText);
            } else {
                var textArea = document.createElement("textarea");
                textArea.value = copyText;
                // Avoid scrolling to bottom
                textArea.style.top = "0";
                textArea.style.left = "0";
                textArea.style.position = "fixed";
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                try {
                    var successful = document.execCommand('copy');
                    var msg = successful ? 'successful' : 'unsuccessful';
                    console.log('Fallback: Copying text command was ' + msg);
                } catch (err) {
                    console.error('Fallback: Oops, unable to copy', err);
                }
                document.body.removeChild(textArea);
            }
            $scope.copiedText = copyText;
            $toastService.create("Successfully Copied");
        }


        $scope.reset = function () {
            $scope.selectedSKU = null;
            $scope.vendorSelected = null;
        };

        $scope.selectVendor = function (vendor) {
            $scope.vendorSelected = vendor;
        };

        $scope.bypassContractIdChange = function(val) {
            $scope.vendorContractId = val;
        }

        $scope.toggleBypassContracts = function(val) {
            $scope.byPassContracts = val;
        }

        $scope.getContracts = function () {
            // Reset hierarchy data when loading new contracts
            $scope.workOrders = null;
            $scope.items = null;
            $scope.workOrdersLoaded = false;
            $scope.itemsLoaded = false;
            $scope.contractRequest = null;

            getCreatedInvoices($scope.showViewActions, $scope.startDate, $scope.endDate);
        };

        function getApproversList() {
            var data = {
                aclCode : ['SCM_VEN_MAP_APP', 'SCM_CUS_MAP_APP']
            }
            var url = apiJson.urls.users.getEmpByAcl;
            ScmApiService.post(url, data)
                .then(function (responseData) {
                    if (!appUtil.isEmptyObject(responseData) && !appUtil.isEmptyObject(responseData.data)) {
                        var approversMap = responseData.data;
                        $scope.customerMappingApproval = false;
                        $scope.vendorMappingApproval = false;
                        if( $scope.currentUser.user.designation.name == 'Admin' ) {
                            $scope.customerMappingApproval = true;
                            $scope.vendorMappingApproval = true;
                            return;
                        }

                        // Check for SCM_VEN_MAP_APP
                        if (approversMap['SCM_VEN_MAP_APP']) {
                            for (var i = 0; i < approversMap['SCM_VEN_MAP_APP'].length; i++) {
                                if (approversMap['SCM_VEN_MAP_APP'][i].id == $scope.currentUser.userId) {
                                    $scope.vendorMappingApproval = true;
                                    break;
                                }
                            }
                        }

                        // Check for SCM_CUS_MAP_APP
                        if (approversMap['SCM_CUS_MAP_APP']) {
                            for (var j = 0; j < approversMap['SCM_CUS_MAP_APP'].length; j++) {
                                if (approversMap['SCM_CUS_MAP_APP'][j].id == $scope.currentUser.userId) {
                                    $scope.customerMappingApproval = true;
                                    break;
                                }
                            }
                        }
                    }
                });
        }

        $scope.canCancelWo = function(vendorType) {
            return vendorType == 'CUSTOMER' ? $scope.customerMappingApproval : $scope.vendorMappingApproval;
        }

        function openUrl(documentId) {
            $http({
                method: "GET",
                url: apiJson.urls.skuMapping.getUrl,
                params: {
                    documentId: documentId
                }
            }).then(function (response) {
                if (!appUtil.isEmptyObject(response.data)) {
                    $window.open(response.data, '_blank');
                } else {
                    $toastService.create("Please check if the selected contract is correct!!");
                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });
        }

        $scope.showButtons = function(status) {
            if(status == 'VENDOR_REJECTED' || status == 'REMOVED_ALL' || status == 'APPROVER_REJECTED' ||
               status == 'CANCELLED' || status == 'EXPIRED' || status == 'DEACTIVATED') {
                return false;
            }
            return true;
        }

        $scope.printContract = function (invR, index) {
            openUrl(invR);
        };

        $scope.closeModal = function(){
            $scope.uploadedDocData = null;
        }

        // Need to change according to Work order...
        $scope.mailVendor = function (val) {
            $alertService.confirm("Are you sure, the details are correct?", null, function (result) {
                 if (result) {
                      var payload = {
                          bypassContractId : val,
                          employeeId : appUtil.getCurrentUser().userId,
                          employeeName : appUtil.getCurrentUser().user.name
                      }
                      $http({
                          url: apiJson.urls.skuMapping.triggerMailV2,
                          method: 'POST',
                          data : payload
                      }).then(function success(response) {
                          if (response.data != null && response.status == 200) {
                              $toastService.create("Mail Triggered To Vendor Successfully");
                              $scope.getContracts();
                          }
                      }, function error(response) {
                          $toastService.create("Issue With Template Preview");
                      });
                     Popeye.closeCurrentModal(true);
                 }
            });
        }

        // Need to change according to Work order...
        $scope.applyContract = function (val) {
            $alertService.confirm("Are you sure want to <b> APPLY CONTRACT</b>?", null, function (result) {
                if (result) {
                    $rootScope.showFullScreenLoader = true;
                    var payload = {
                        bypassContractId : val,
                        status :'APPLIED'
                    }
                    $http({
                        url: apiJson.urls.skuMapping.applyContract,
                        method: 'POST',
                        data : payload
                    }).then(function success(response) {
                        if (response.data != null && response.status == 200) {
                            $toastService.create("Contract Applied Successfully");
                        }
                    }, function error(response) {
                        $toastService.create("Issue With Template Preview");
                    });
                    $scope.getContracts();
                    $rootScope.showFullScreenLoader = false;
                }
            });

        }
    }
]);

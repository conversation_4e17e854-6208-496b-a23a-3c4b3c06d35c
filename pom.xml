<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.6.2</version>
        <relativePath /> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.stpl.tech.kettle.scm</groupId>
    <artifactId>kettle-scm</artifactId>
    <packaging>pom</packaging>
    <version>4.1.0-SNAPSHOT</version>

    <name>kettle-scm</name>
    <url>http://maven.apache.org</url>

    <properties>
        <java.version>17</java.version>
    </properties>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.3</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <modules>
        <module>scm-db</module>
        <module>scm-domain</module>
        <module>scm-core</module>
        <module>scm-service</module>
<!--        <module>scm-ui</module>-->
    </modules>
</project>

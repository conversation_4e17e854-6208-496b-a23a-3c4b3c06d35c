package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class SkuPriceDetail {

	protected Integer keyId;
	protected IdCodeName sku;
	protected PackagingData pkg;
	protected IdCodeName vendor;
	protected IdCodeName dispatch;
	protected IdCodeName delivery;
	protected String status;
	protected DateAndValue current;
	protected DateAndValue updated;
	protected Integer leadTime;
	private List<IdCodeName> dispatchLocations;

	public Integer getKeyId() {
		return keyId;
	}

	public void setKeyId(Integer keyId) {
		this.keyId = keyId;
	}

	public IdCodeName getSku() {
		return sku;
	}

	public void setSku(IdCodeName sku) {
		this.sku = sku;
	}

	public PackagingData getPkg() {
		return pkg;
	}

	public void setPkg(PackagingData pkg) {
		this.pkg = pkg;
	}

	public IdCodeName getVendor() {
		return vendor;
	}

	public void setVendor(IdCodeName vendor) {
		this.vendor = vendor;
	}

	public IdCodeName getDispatch() {
		return dispatch;
	}

	public void setDispatch(IdCodeName dispatch) {
		this.dispatch = dispatch;
	}

	public IdCodeName getDelivery() {
		return delivery;
	}

	public void setDelivery(IdCodeName delivery) {
		this.delivery = delivery;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public DateAndValue getCurrent() {
		if (current == null) {
			current = new DateAndValue();
		}
		return current;
	}

	public void setCurrent(DateAndValue current) {
		this.current = current;
	}

	public DateAndValue getUpdated() {
		if (updated == null) {
			updated = new DateAndValue();
		}
		return updated;
	}

	public void setUpdated(DateAndValue updated) {
		this.updated = updated;
	}

	public Integer getLeadTime() {
		return leadTime;
	}

	public void setLeadTime(Integer leadTime) {
		this.leadTime = leadTime;
	}
}
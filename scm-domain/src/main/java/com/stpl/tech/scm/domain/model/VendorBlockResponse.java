/*
 * Created By Shanmu<PERSON>
 */

package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VendorBlockResponse {

    private Integer vendorId;
    private String vendorName;
    private Map<String, List<VendorBlockItem>> details;
}

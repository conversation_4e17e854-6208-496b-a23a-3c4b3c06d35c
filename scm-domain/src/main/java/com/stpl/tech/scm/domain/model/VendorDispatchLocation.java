//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2017.04.20 at 07:12:44 PM IST
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>Java class for VendorDispatchLocation complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="VendorDispatchLocation"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="dispatchId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="vendorDetail" type="{http://www.w3schools.com}VendorDetail"/&gt;
 *         &lt;element name="applyTax" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="locationName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="state" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="country" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="address" type="{http://www.w3schools.com}AddressDetail"/&gt;
 *         &lt;element name="notificationType" type="{http://www.w3schools.com}NotificationType"/&gt;
 *         &lt;element name="updateTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="updatedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "VendorDispatchLocation", propOrder = {
        "dispatchId",
    "vendorDetail",
    "applyTax",
    "locationName",
    "city",
    "state",
    "country",
    "address",
    "notificationType",
    "updateTime",
    "updatedBy",
    "tin",
    "gstin",
    "gstStatus"
})
public class VendorDispatchLocation {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer dispatchId;
    @XmlElement(required = true)
    protected IdCodeName vendorDetail;
    @XmlElement(defaultValue = "true")
    protected boolean applyTax;
    @XmlElement(required = true, nillable = true)
    protected String locationName;
    @XmlElement(required = true)
    protected String city;
    @XmlElement(required = true)
    protected String state;
    @XmlElement(required = true)
    protected String country;
    @XmlElement(required = true)
    protected AddressDetail address;
    @XmlElement(required = true, nillable = true)
    @XmlSchemaType(name = "string")
    protected List<NotificationType> notificationType;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date updateTime;
    @XmlElement(required = true)
    protected IdCodeName updatedBy;
    protected String tin;
    protected String gstin;
    protected GstApplicationStatus gstStatus;
    protected String locationId;
    protected String contactEmail;
    protected DocumentDetail gstinDocument;
    private SwitchStatus status;
    protected LocationType locationType;


    /**
     * Gets the value of the dispatchId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getDispatchId() {
        return dispatchId;
    }

    /**
     * Sets the value of the dispatchId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setDispatchId(Integer value) {
        this.dispatchId = value;
    }

    /**
     * Gets the value of the vendorDetail property.
     *
     * @return
     *     possible object is
     *     {@link VendorDetail }
     *
     */
    public IdCodeName getVendorDetail() {
        return vendorDetail;
    }

    /**
     * Sets the value of the vendorDetail property.
     *
     * @param value
     *     allowed object is
     *     {@link VendorDetail }
     *
     */
    public void setVendorDetail(IdCodeName value) {
        this.vendorDetail = value;
    }

    /**
     * Gets the value of the applyTax property.
     *
     */
    public boolean isApplyTax() {
        return applyTax;
    }

    /**
     * Sets the value of the applyTax property.
     *
     */
    public void setApplyTax(boolean value) {
        this.applyTax = value;
    }

    /**
     * Gets the value of the locationName property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getLocationName() {
        return locationName;
    }

    /**
     * Sets the value of the locationName property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setLocationName(String value) {
        this.locationName = value;
    }

    /**
     * Gets the value of the city property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getCity() {
        return city;
    }

    /**
     * Sets the value of the city property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setCity(String value) {
        this.city = value;
    }

    /**
     * Gets the value of the state property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getState() {
        return state;
    }

    /**
     * Sets the value of the state property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setState(String value) {
        this.state = value;
    }

    /**
     * Gets the value of the country property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getCountry() {
        return country;
    }

    /**
     * Sets the value of the country property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setCountry(String value) {
        this.country = value;
    }

    /**
     * Gets the value of the address property.
     *
     * @return
     *     possible object is
     *     {@link AddressDetail }
     *
     */
    public AddressDetail getAddress() {
        return address;
    }

    /**
     * Sets the value of the address property.
     *
     * @param value
     *     allowed object is
     *     {@link AddressDetail }
     *
     */
    public void setAddress(AddressDetail value) {
        this.address = value;
    }

    /**
     * Gets the value of the notificationType property.
     *
     * @return
     *     possible object is
     *     {@link NotificationType }
     *
     */
    public List<NotificationType> getNotificationType() {
        return notificationType;
    }

    /**
     * Sets the value of the notificationType property.
     *
     * @param values
     *     allowed object is
     *     {@link NotificationType }
     *
     */
    public void setNotificationType(List<String> values) {
        this.notificationType = values.stream().map(NotificationType::valueOf).collect(Collectors.toList());
    }

    /**
     * Gets the value of the updateTime property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * Sets the value of the updateTime property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUpdateTime(Date value) {
        this.updateTime = value;
    }

    /**
     * Gets the value of the updatedBy property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getUpdatedBy() {
        return updatedBy;
    }

    /**
     * Sets the value of the updatedBy property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setUpdatedBy(IdCodeName value) {
        this.updatedBy = value;
    }

    public String getTin() {
        return tin;
    }

    public void setTin(String tin) {
        this.tin = tin;
    }

    public String getGstin() {
        return gstin;
    }

    public void setGstin(String gstin) {
        this.gstin = gstin;
    }

    public GstApplicationStatus getGstStatus() {
        return gstStatus;
    }

    public void setGstStatus(GstApplicationStatus gstStatus) {
        this.gstStatus = gstStatus;
    }

    public String getLocationId() {
        return locationId;
    }

    public void setLocationId(String locationId) {
        this.locationId = locationId;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

	public DocumentDetail getGstinDocument() {
		return gstinDocument;
	}

	public void setGstinDocument(DocumentDetail gstinDocument) {
		this.gstinDocument = gstinDocument;
	}

    public void setStatus(SwitchStatus status) {
        this.status = status;
    }

    public SwitchStatus getStatus() {
        return status;
    }

    public LocationType getLocationType() {
        return locationType;
    }

    public void setLocationType(LocationType locationType) {
        this.locationType = locationType;
    }

    public boolean isActive() {
        return SwitchStatus.ACTIVE.equals(status) ? true : false;
    }

}

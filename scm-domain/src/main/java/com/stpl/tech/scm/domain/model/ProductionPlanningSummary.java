package com.stpl.tech.scm.domain.model;

import com.stpl.tech.master.domain.model.UnitBasicDetail;

import java.util.List;
import java.util.Set;

public class ProductionPlanningSummary {
    private Integer totalActiveUnit;
    private Integer totalRoReceived;
    private Integer pendingRo;
    private Set<String> cityRegionWise;
    private List<UnitBasicDetail> allActiveUnit;
    private Integer allGntCount;
    private Integer allChaayosCount;

    public List<UnitBasicDetail> getAllActiveUnit() {
        return allActiveUnit;
    }

    public void setAllActiveUnit(List<UnitBasicDetail> allActiveUnit) {
        this.allActiveUnit = allActiveUnit;
    }

    public Integer getTotalActiveUnit() {
        return totalActiveUnit;
    }

    public void setTotalActiveUnit(Integer totalActiveUnit) {
        this.totalActiveUnit = totalActiveUnit;
    }

    public Integer getTotalRoReceived() {
        return totalRoReceived;
    }

    public void setTotalRoReceived(Integer totalRoReceived) {
        this.totalRoReceived = totalRoReceived;
    }

    public Integer getPendingRo() {
        return pendingRo;
    }

    public void setPendingRo(Integer pendingRo) {
        this.pendingRo = pendingRo;
    }

    public Set<String> getCityRegionWise() {
        return cityRegionWise;
    }

    public void setCityRegionWise(Set<String> cityRegionWise) {
        this.cityRegionWise = cityRegionWise;
    }

    public Integer getAllGntCount() {
        return allGntCount;
    }

    public void setAllGntCount(Integer allGntCount) {
        this.allGntCount = allGntCount;
    }

    public Integer getAllChaayosCount() {
        return allChaayosCount;
    }

    public void setAllChaayosCount(Integer allChaayosCount) {
        this.allChaayosCount = allChaayosCount;
    }
}

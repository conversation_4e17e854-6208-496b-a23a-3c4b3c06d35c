package com.stpl.tech.scm.domain.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class DayCloseProductPackagingMappingsDTO {

    private Integer productPackagingMappingId;

    private Integer sumoDayCloseProductItemId;

    private Integer packagingId;

    private String packagingName;

    private BigDecimal quantity;

    private BigDecimal conversionRatio;

    public DayCloseProductPackagingMappingsDTO(Integer packagingId, String packagingName, float conversionRatio) {
        this.packagingId = packagingId;
        this.packagingName = packagingName;
        this.conversionRatio = BigDecimal.valueOf(conversionRatio);
    }
}

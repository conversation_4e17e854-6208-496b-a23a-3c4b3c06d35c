package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RiderRoutePlanDataDto implements Serializable {
    private Integer RiderRoutePlanDataId;
    private RiderInfoDataDto riderInfoData;
    private Integer stockRedistributionRouteInfoDataStockRedistributionRouteInfoDataId;
    private RiderRoutePlanStatusEnum riderRoutePlanStatus;
    private RiderRouteTypeEnum riderRouteType;
    private Date creationTime;
    private String rideStartTime;
    private Date rideEndTime;
    private Set<RiderRoutePlanStepDataDto> riderRoutePlanStepDataSet;
}

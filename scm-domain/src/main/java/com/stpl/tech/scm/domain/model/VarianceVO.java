package com.stpl.tech.scm.domain.model;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stpl.tech.scm.domain.vo.ConsumptionVO;
import com.stpl.tech.scm.domain.vo.InventoryItemVO;
import com.stpl.tech.scm.domain.vo.ReceivingVO;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 17-08-2017.
 */
public class VarianceVO extends AbstractInventoryVO implements ReceivingVO, ConsumptionVO {

    private int eventId;
    private int unitId;
    private List<ProductStockForUnit> stockList;
    private PriceUpdateEntryType inventoryType;

    public VarianceVO(Integer eventId, Integer unitId, PriceUpdateEntryType inventoryType) {
        this.eventId = eventId;
        this.unitId = unitId;
        this.inventoryType = inventoryType;
    }


    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }

    public List<ProductStockForUnit> getStockList() {
        if(this.stockList == null){
            this.stockList = new ArrayList<>();
        }
        return this.stockList;
    }

    @Override
    public int getKeyId() {
        return eventId;
    }

    @Override
    public StockEventType getKeyType() {
        return StockEventType.VARIANCE;
    }

    @Override
    public int getUnitId() {
        return unitId;
    }

    @Override
    public PriceUpdateEntryType getInventoryType() {
        return this.inventoryType;
    }

    @Override
    @JsonIgnore
    public List<InventoryItemVO> getInventoryItems() {
        return new ArrayList<>(getStockList());
    }
    
	@Override
	public boolean isAssetOrder() {
		return false;
	}

}

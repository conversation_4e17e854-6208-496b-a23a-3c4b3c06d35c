package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ListType", propOrder = { "listDataId", "type", "code", "name", "description", "status", "alias", "listType"
		 })
public class ListData {

	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer listDataId;

	@XmlElement(required = true)
	protected String type;

	@XmlElement(required = true)
	protected String code;

	@XmlElement(required = true)
	protected String name;

	@XmlElement(required = true)
	protected String description;

	@XmlElement(required = true)
	protected String status;

	@XmlElement(required = true)
	protected String alias;
	
	@XmlElement(required = true)
    @XmlSchemaType(name = "string")
	protected ListType listType;

	public Integer getListDataId() {
		return listDataId;
	}

	public void setListDataId(Integer listDataId) {
		this.listDataId = listDataId;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getAlias() {
		return alias;
	}

	public void setAlias(String alias) {
		this.alias = alias;
	}

	public ListType getListType() {
		return listType;
	}

	public void setListType(ListType listType) {
		this.listType = listType;
	}
	
	

}

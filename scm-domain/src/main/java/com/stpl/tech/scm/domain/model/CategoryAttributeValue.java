//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.06.10 at 02:43:25 PM IST
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for CategoryAttributeValue complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="CategoryAttributeValue"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="categoryAttributeValueId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="categoryAttributeMappingId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="attributeValue" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="mappingStatus" type="{http://www.w3schools.com}SwitchStatus"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CategoryAttributeValue", propOrder = {
    "categoryAttributeValueId",
    "categoryAttributeMappingId",
    "attributeValue",
    "mappingStatus"
})
public class CategoryAttributeValue {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer categoryAttributeValueId;
    protected int categoryAttributeMappingId;
    @XmlElement(required = true)
    protected IdCodeName attributeValue;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SwitchStatus mappingStatus;

    /**
     * Gets the value of the categoryAttributeValueId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getCategoryAttributeValueId() {
        return categoryAttributeValueId;
    }

    /**
     * Sets the value of the categoryAttributeValueId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setCategoryAttributeValueId(Integer value) {
        this.categoryAttributeValueId = value;
    }

    /**
     * Gets the value of the categoryAttributeMappingId property.
     * 
     */
    public int getCategoryAttributeMappingId() {
        return categoryAttributeMappingId;
    }

    /**
     * Sets the value of the categoryAttributeMappingId property.
     * 
     */
    public void setCategoryAttributeMappingId(int value) {
        this.categoryAttributeMappingId = value;
    }

    /**
     * Gets the value of the attributeValue property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getAttributeValue() {
        return attributeValue;
    }

    /**
     * Sets the value of the attributeValue property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setAttributeValue(IdCodeName value) {
        this.attributeValue = value;
    }

    /**
     * Gets the value of the mappingStatus property.
     * 
     * @return
     *     possible object is
     *     {@link SwitchStatus }
     *     
     */
    public SwitchStatus getMappingStatus() {
        return mappingStatus;
    }

    /**
     * Sets the value of the mappingStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link SwitchStatus }
     *     
     */
    public void setMappingStatus(SwitchStatus value) {
        this.mappingStatus = value;
    }

}

package com.stpl.tech.scm.domain.model;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class SkuToProductResponse {
    Map<Integer,Map<Integer,RequestOrderItem>> orderItems;
    Map<Integer,List<SkuDefinition>> productToSkus;
    Map<Integer,Map<Integer,List<SCMOrderPackaging>>> productUnitPackaging;

    public Map<Integer, Map<Integer,RequestOrderItem>> getOrderItems() {
        if(Objects.nonNull(this.orderItems)){
            return orderItems;
        }
        return  new HashMap<>();
    }

    public void setOrderItems(Map<Integer, Map<Integer,RequestOrderItem>> orderItems) {
        this.orderItems = orderItems;
    }

    public Map<Integer, List<SkuDefinition>> getProductToSkus() {
        if(Objects.nonNull(this.productToSkus)){
            return productToSkus;
        }
        return new HashMap<>();
    }

    public void setProductToSkus(Map<Integer, List<SkuDefinition>> productToSkus) {
        this.productToSkus = productToSkus;
    }

    public Map<Integer, Map<Integer, List<SCMOrderPackaging>>> getProductUnitPackaging() {
        if(Objects.nonNull(productUnitPackaging)){
            return productUnitPackaging;
        }else{
            return new HashMap<>();
        }

    }

    public void setProductUnitPackaging(Map<Integer, Map<Integer, List<SCMOrderPackaging>>> productUnitPackaging) {
        this.productUnitPackaging = productUnitPackaging;
    }
}

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.06.11 at 12:23:12 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;


/**
 * <p>Java class for ReferenceOrderScmItem complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ReferenceOrderScmItem"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="productId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="productName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="requestedQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="requestedAbsoluteQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="transferredQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="receivedQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="fulfillmentType" type="{http://www.w3schools.com}FulfillmentType"/&gt;
 *         &lt;element name="unitOfMeasure" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ReferenceOrderScmItem", propOrder = {
    "id",
    "productId",
    "productName",
    "requestedQuantity",
    "requestedAbsoluteQuantity",
    "transferredQuantity",
    "receivedQuantity",
    "fulfillmentType",
    "unitOfMeasure"
})
public class ReferenceOrderScmItem {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer id;
    protected int productId;
    @XmlElement(required = true)
    protected String productName;
    protected float requestedQuantity;
    protected float requestedAbsoluteQuantity;
    @XmlElement(required = true, type = Float.class, nillable = true)
    protected Float transferredQuantity;
    @XmlElement(required = true, type = Float.class, nillable = true)
    protected Float receivedQuantity;
    @XmlElement(required = true, nillable = true)
    @XmlSchemaType(name = "string")
    protected FulfillmentType fulfillmentType;
    @XmlElement(required = true)
    protected String unitOfMeasure;
    protected float suggestedQuantity;
    protected Float predictedQuantity;
    protected BigDecimal suggestedQuantityBeforeMoq;
    protected Map<String, BigDecimal> expiryDrillDown;
    protected Map<String, Map<String, BigDecimal>> acknowledgedRoExpiryDrillDown;
    protected String reason;

    protected BigDecimal originalConsumption;
    protected BigDecimal stockOutPercentage;
    protected  BigDecimal stockOutRaw;
    protected BigDecimal wastagePercentage;

    protected BigDecimal wastageRaw;

    protected BigDecimal adjustedQuantity;

    protected BigDecimal bufferQuantity;

    protected BigDecimal cafeTotalHours;

    protected String comments;

    protected BigDecimal totalConsumption;

    protected Map<String,Float> dateOrderings;

    protected Map<String,Float> dateRemaining;

    protected Map<String,Float> originalDateOrderings;

    protected Map<String,Float> originalDateRemaining;

    protected Integer packagingId;

    /**
     * Gets the value of the id property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setId(Integer value) {
        this.id = value;
    }

    /**
     * Gets the value of the productId property.
     *
     */
    public int getProductId() {
        return productId;
    }

    /**
     * Sets the value of the productId property.
     *
     */
    public void setProductId(int value) {
        this.productId = value;
    }

    /**
     * Gets the value of the productName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getProductName() {
        return productName;
    }

    /**
     * Sets the value of the productName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProductName(String value) {
        this.productName = value;
    }

    /**
     * Gets the value of the requestedQuantity property.
     * 
     */
    public float getRequestedQuantity() {
        return requestedQuantity;
    }

    /**
     * Sets the value of the requestedQuantity property.
     * 
     */
    public void setRequestedQuantity(float value) {
        this.requestedQuantity = value;
    }

    /**
     * Gets the value of the requestedAbsoluteQuantity property.
     * 
     */
    public float getRequestedAbsoluteQuantity() {
        return requestedAbsoluteQuantity;
    }

    /**
     * Sets the value of the requestedAbsoluteQuantity property.
     * 
     */
    public void setRequestedAbsoluteQuantity(float value) {
        this.requestedAbsoluteQuantity = value;
    }

    /**
     * Gets the value of the transferredQuantity property.
     * 
     * @return
     *     possible object is
     *     {@link Float }
     *     
     */
    public Float getTransferredQuantity() {
        return transferredQuantity;
    }

    /**
     * Sets the value of the transferredQuantity property.
     * 
     * @param value
     *     allowed object is
     *     {@link Float }
     *     
     */
    public void setTransferredQuantity(Float value) {
        this.transferredQuantity = value;
    }

    /**
     * Gets the value of the receivedQuantity property.
     * 
     * @return
     *     possible object is
     *     {@link Float }
     *     
     */
    public Float getReceivedQuantity() {
        return receivedQuantity;
    }

    /**
     * Sets the value of the receivedQuantity property.
     * 
     * @param value
     *     allowed object is
     *     {@link Float }
     *     
     */
    public void setReceivedQuantity(Float value) {
        this.receivedQuantity = value;
    }

    /**
     * Gets the value of the fulfillmentType property.
     * 
     * @return
     *     possible object is
     *     {@link FulfillmentType }
     *     
     */
    public FulfillmentType getFulfillmentType() {
        return fulfillmentType;
    }

    /**
     * Sets the value of the fulfillmentType property.
     * 
     * @param value
     *     allowed object is
     *     {@link FulfillmentType }
     *     
     */
    public void setFulfillmentType(FulfillmentType value) {
        this.fulfillmentType = value;
    }

    /**
     * Gets the value of the unitOfMeasure property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    /**
     * Sets the value of the unitOfMeasure property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUnitOfMeasure(String value) {
        this.unitOfMeasure = value;
    }

    public float getSuggestedQuantity() {
        return suggestedQuantity;
    }

    public void setSuggestedQuantity(float suggestedQuantity) {
        this.suggestedQuantity = suggestedQuantity;
    }

    public Float getPredictedQuantity() {
        return predictedQuantity;
    }

    public void setPredictedQuantity(Float predictedQuantity) {
        this.predictedQuantity = predictedQuantity;
    }

    public Map<String, BigDecimal> getExpiryDrillDown() {
        return expiryDrillDown;
    }

    public void setExpiryDrillDown(Map<String, BigDecimal> expiryDrillDown) {
        this.expiryDrillDown = expiryDrillDown;
    }

    public Map<String, Map<String, BigDecimal>> getAcknowledgedRoExpiryDrillDown() {
        return acknowledgedRoExpiryDrillDown;
    }

    public void setAcknowledgedRoExpiryDrillDown(Map<String, Map<String, BigDecimal>> acknowledgedRoExpiryDrillDown) {
        this.acknowledgedRoExpiryDrillDown = acknowledgedRoExpiryDrillDown;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public BigDecimal getOriginalConsumption() {
        return originalConsumption;
    }

    public void setOriginalConsumption(BigDecimal originalConsumption) {
        this.originalConsumption = originalConsumption;
    }

    public BigDecimal getStockOutPercentage() {
        return stockOutPercentage;
    }

    public void setStockOutPercentage(BigDecimal stockOutPercentage) {
        this.stockOutPercentage = stockOutPercentage;
    }

    public BigDecimal getStockOutRaw() {
        return stockOutRaw;
    }

    public void setStockOutRaw(BigDecimal stockOutRaw) {
        this.stockOutRaw = stockOutRaw;
    }

    public BigDecimal getWastagePercentage() {
        return wastagePercentage;
    }

    public void setWastagePercentage(BigDecimal wastagePercentage) {
        this.wastagePercentage = wastagePercentage;
    }

    public BigDecimal getWastageRaw() {
        return wastageRaw;
    }

    public void setWastageRaw(BigDecimal wastageRaw) {
        this.wastageRaw = wastageRaw;
    }

    public BigDecimal getAdjustedQuantity() {
        return adjustedQuantity;
    }

    public void setAdjustedQuantity(BigDecimal adjustedQuantity) {
        this.adjustedQuantity = adjustedQuantity;
    }

    public BigDecimal getBufferQuantity() {
        return bufferQuantity;
    }

    public void setBufferQuantity(BigDecimal bufferQuantity) {
        this.bufferQuantity = bufferQuantity;
    }

    public BigDecimal getCafeTotalHours() {
        return cafeTotalHours;
    }

    public void setCafeTotalHours(BigDecimal cafeTotalHours) {
        this.cafeTotalHours = cafeTotalHours;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public BigDecimal getTotalConsumption() {
        return totalConsumption;
    }

    public void setTotalConsumption(BigDecimal totalConsumption) {
        this.totalConsumption = totalConsumption;
    }

    public BigDecimal getSuggestedQuantityBeforeMoq() {
        return suggestedQuantityBeforeMoq;
    }

    public void setSuggestedQuantityBeforeMoq(BigDecimal suggestedQuantityBeforeMoq) {
        this.suggestedQuantityBeforeMoq = suggestedQuantityBeforeMoq;
    }

    public Map<String, Float> getDateOrderings() {
        return dateOrderings;
    }

    public void setDateOrderings(Map<String, Float> dateOrderings) {
        this.dateOrderings = dateOrderings;
    }

    public Map<String, Float> getDateRemaining() {
        return dateRemaining;
    }

    public void setDateRemaining(Map<String, Float> dateRemaining) {
        this.dateRemaining = dateRemaining;
    }

    public Map<String, Float> getOriginalDateRemaining() {
        if(Objects.isNull(originalDateRemaining)) {
            return new HashMap<>();
        }
        return originalDateRemaining;
    }

    public void setOriginalDateRemaining(Map<String, Float> originalDateRemaining) {
        this.originalDateRemaining = originalDateRemaining;
    }

    public Map<String, Float> getOriginalDateOrderings() {
        if(Objects.isNull(originalDateOrderings)) {
            return new HashMap<>();
        }
        return originalDateOrderings;
    }

    public void setOriginalDateOrderings(Map<String, Float> originalDateOrderings) {
        this.originalDateOrderings = originalDateOrderings;
    }

    public Integer getPackagingId() {
        return packagingId;
    }

    public void setPackagingId(Integer packagingId) {
        this.packagingId = packagingId;
    }
}

package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MonkStatusDayCloseHistory {

    private Long monkStatusDayCloseHistoryId;
    private Date createdAt;
    private Long monkStatusDayCloseId;
    private String monkStatus;
    private String comment;

    // Enum for Monk Status (same as in MonkStatusDayClose)
    public enum MonkStatus {
        INITIATED("Initiated"),
        MONK_DOWN("Monk Down"),
        FAILED("Failed"),
        COMPLETED("Completed");

        private final String displayName;

        MonkStatus(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }
}

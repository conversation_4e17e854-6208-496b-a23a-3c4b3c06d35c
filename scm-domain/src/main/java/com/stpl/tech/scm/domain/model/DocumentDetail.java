//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.04.20 at 07:12:44 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.Date;


/**
 * <p>Java class for DocumentDetail complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="DocumentDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="documentId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="documentLink" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="fileType" type="{http://www.w3schools.com}FileType"/&gt;
 *         &lt;element name="mimeType" type="{http://www.w3schools.com}MimeType"/&gt;
 *         &lt;element name="uploadType" type="{http://www.w3schools.com}DocUploadType"/&gt;
 *         &lt;element name="uploadTypeId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="updateTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="updatedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DocumentDetail", propOrder = {
    "documentId",
    "documentLink",
    "fileType",
    "mimeType",
    "uploadType",
    "uploadTypeId",
    "updateTime",
    "updatedBy"
})
public class DocumentDetail {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer documentId;
    @XmlElement(required = true)
    protected String documentLink;
    @XmlElement(required = true, nillable = true)
    @XmlSchemaType(name = "string")
    protected FileType fileType;
    @XmlElement(required = true, nillable = true)
    @XmlSchemaType(name = "string")
    protected MimeType mimeType;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected DocUploadType uploadType;
    protected int uploadTypeId;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date updateTime;
    @XmlElement(required = true)
    protected IdCodeName updatedBy;
    protected String s3Bucket;
    protected String s3Key;
    protected String fileUrl;

    /**
     * Gets the value of the documentId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getDocumentId() {
        return documentId;
    }

    /**
     * Sets the value of the documentId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setDocumentId(Integer value) {
        this.documentId = value;
    }

    /**
     * Gets the value of the documentLink property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDocumentLink() {
        return documentLink;
    }

    /**
     * Sets the value of the documentLink property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDocumentLink(String value) {
        this.documentLink = value;
    }

    /**
     * Gets the value of the fileType property.
     * 
     * @return
     *     possible object is
     *     {@link FileType }
     *     
     */
    public FileType getFileType() {
        return fileType;
    }

    /**
     * Sets the value of the fileType property.
     * 
     * @param value
     *     allowed object is
     *     {@link FileType }
     *     
     */
    public void setFileType(FileType value) {
        this.fileType = value;
    }

    /**
     * Gets the value of the mimeType property.
     * 
     * @return
     *     possible object is
     *     {@link MimeType }
     *     
     */
    public MimeType getMimeType() {
        return mimeType;
    }

    /**
     * Sets the value of the mimeType property.
     * 
     * @param value
     *     allowed object is
     *     {@link MimeType }
     *     
     */
    public void setMimeType(MimeType value) {
        this.mimeType = value;
    }

    /**
     * Gets the value of the uploadType property.
     * 
     * @return
     *     possible object is
     *     {@link DocUploadType }
     *     
     */
    public DocUploadType getUploadType() {
        return uploadType;
    }

    /**
     * Sets the value of the uploadType property.
     * 
     * @param value
     *     allowed object is
     *     {@link DocUploadType }
     *     
     */
    public void setUploadType(DocUploadType value) {
        this.uploadType = value;
    }

    /**
     * Gets the value of the uploadTypeId property.
     * 
     */
    public int getUploadTypeId() {
        return uploadTypeId;
    }

    /**
     * Sets the value of the uploadTypeId property.
     * 
     */
    public void setUploadTypeId(int value) {
        this.uploadTypeId = value;
    }

    /**
     * Gets the value of the updateTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * Sets the value of the updateTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUpdateTime(Date value) {
        this.updateTime = value;
    }

    /**
     * Gets the value of the updatedBy property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getUpdatedBy() {
        return updatedBy;
    }

    /**
     * Sets the value of the updatedBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setUpdatedBy(IdCodeName value) {
        this.updatedBy = value;
    }

    public String getS3Bucket() {
        return s3Bucket;
    }

    public void setS3Bucket(String s3Bucket) {
        this.s3Bucket = s3Bucket;
    }

    public String getS3Key() {
        return s3Key;
    }

    public void setS3Key(String s3Key) {
        this.s3Key = s3Key;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }
}

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.06.11 at 12:23:12 PM IST
//


package com.stpl.tech.scm.domain.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;


/**
 * <p>Java class for RequestOrder complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="RequestOrder"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="generationTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="lastUpdateTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="requestUnit" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="generatedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="lastUpdatedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="fulfillmentUnit" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="fulfillmentDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="specialOrder" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="referenceOrderId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="status" type="{http://www.w3schools.com}SCMOrderStatus"/&gt;
 *         &lt;element name="comment" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="totalAmount" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="purchaseOrderId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="transferOrderId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="goodsReceivedId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="requestOrderItems" type="{http://www.w3schools.com}RequestOrderItem" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RequestOrder", propOrder = {
    "id",
    "generationTime",
    "lastUpdateTime",
    "requestUnit",
    "generatedBy",
    "lastUpdatedBy",
    "fulfillmentUnit",
    "fulfillmentDate",
    "specialOrder",
    "referenceOrderId",
    "status",
    "comment",
    "totalAmount",
    "purchaseOrderId",
    "transferOrderId",
    "goodsReceivedId",
    "requestOrderItems"
})
public class RequestOrder {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer id;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date generationTime;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date lastUpdateTime;
    @XmlElement(required = true)
    protected IdCodeName requestUnit;
    @XmlElement(required = true)
    protected IdCodeName requestCompany;
    @XmlElement(required = true)
    protected IdCodeName generatedBy;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName lastUpdatedBy;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName fulfillmentUnit;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName fulfillmentCompany;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date fulfillmentDate;
    @XmlElement(required = true, type = Boolean.class, nillable = true)
    protected Boolean specialOrder;
    @XmlElement(required = true, type = Boolean.class, nillable = true)
    protected Boolean assetOrder;
    protected String alternateF9Order;
    protected int referenceOrderId;
    protected String referenceOrderType;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SCMOrderStatus status;
    @XmlElement(required = true)
    protected String comment;
    protected float totalAmount;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer purchaseOrderId;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer transferOrderId;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer goodsReceivedId;
    protected List<RequestOrderItem> requestOrderItems;
    protected Set<Integer> parentROs;
    @XmlElement(required = true, type = Boolean.class, nillable = true)
    protected Boolean applyBudget;
    @XmlElement(required = true)
    protected String budgetReason;
    protected OrderTransferType transferType;
    protected String searchTag;
    private Integer vendorId;
    private String vendorName;
    private Date notificationTime;
    private boolean notified;
    private String notificationTypes;
    protected Integer numberOfDays;
    protected Boolean raiseBy;
    protected  String type;
    protected Boolean bulkOrder;
    protected Boolean specializedUrgentOrder;
    /**
     * Gets the value of the id property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setId(Integer value) {
        this.id = value;
    }

    /**
     * Gets the value of the generationTime property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getGenerationTime() {
        return generationTime;
    }

    /**
     * Sets the value of the generationTime property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setGenerationTime(Date value) {
        this.generationTime = value;
    }

    /**
     * Gets the value of the lastUpdateTime property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * Sets the value of the lastUpdateTime property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setLastUpdateTime(Date value) {
        this.lastUpdateTime = value;
    }

    /**
     * Gets the value of the requestUnit property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getRequestUnit() {
        return requestUnit;
    }

    /**
     * Sets the value of the requestUnit property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setRequestUnit(IdCodeName value) {
        this.requestUnit = value;
    }

    /**
     * Gets the value of the generatedBy property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getGeneratedBy() {
        return generatedBy;
    }

    /**
     * Sets the value of the generatedBy property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setGeneratedBy(IdCodeName value) {
        this.generatedBy = value;
    }

    /**
     * Gets the value of the lastUpdatedBy property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    /**
     * Sets the value of the lastUpdatedBy property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setLastUpdatedBy(IdCodeName value) {
        this.lastUpdatedBy = value;
    }

    /**
     * Gets the value of the fulfillmentUnit property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getFulfillmentUnit() {
        return fulfillmentUnit;
    }

    /**
     * Sets the value of the fulfillmentUnit property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setFulfillmentUnit(IdCodeName value) {
        this.fulfillmentUnit = value;
    }

    /**
     * Gets the value of the fulfillmentDate property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getFulfillmentDate() {
        return fulfillmentDate;
    }

    /**
     * Sets the value of the fulfillmentDate property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setFulfillmentDate(Date value) {
        this.fulfillmentDate = value;
    }

    /**
     * Gets the value of the specialOrder property.
     *
     * @return
     *     possible object is
     *     {@link Boolean }
     *
     */
    public Boolean isSpecialOrder() {
        return specialOrder;
    }

    /**
     * Sets the value of the specialOrder property.
     *
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *
     */
    public void setSpecialOrder(Boolean value) {
        this.specialOrder = value;
    }

    public String getReferenceOrderType() {
        return referenceOrderType;
    }

    public void setReferenceOrderType(String referenceOrderType) {
        this.referenceOrderType = referenceOrderType;
    }

    /**
     * Gets the value of the referenceOrderId property.
     *
     */
    public int getReferenceOrderId() {
        return referenceOrderId;
    }

    /**
     * Sets the value of the referenceOrderId property.
     *
     */
    public void setReferenceOrderId(int value) {
        this.referenceOrderId = value;
    }

    /**
     * Gets the value of the status property.
     *
     * @return
     *     possible object is
     *     {@link SCMOrderStatus }
     *
     */
    public SCMOrderStatus getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     *
     * @param value
     *     allowed object is
     *     {@link SCMOrderStatus }
     *
     */
	public void setStatus(SCMOrderStatus value) {
        this.status = value;
    }

    /**
     * Gets the value of the comment property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getComment() {
        return comment;
    }

    /**
     * Sets the value of the comment property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setComment(String value) {
        this.comment = value;
    }

    /**
     * Gets the value of the totalAmount property.
     *
     */
    public float getTotalAmount() {
        return totalAmount;
    }

    /**
     * Sets the value of the totalAmount property.
     *
     */
    public void setTotalAmount(float value) {
        this.totalAmount = value;
    }

    /**
     * Gets the value of the purchaseOrderId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getPurchaseOrderId() {
        return purchaseOrderId;
    }

    /**
     * Sets the value of the purchaseOrderId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setPurchaseOrderId(Integer value) {
        this.purchaseOrderId = value;
    }

    /**
     * Gets the value of the transferOrderId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getTransferOrderId() {
        return transferOrderId;
    }

    /**
     * Sets the value of the transferOrderId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setTransferOrderId(Integer value) {
        this.transferOrderId = value;
    }

    /**
     * Gets the value of the goodsReceivedId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getGoodsReceivedId() {
        return goodsReceivedId;
    }

    /**
     * Sets the value of the goodsReceivedId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setGoodsReceivedId(Integer value) {
        this.goodsReceivedId = value;
    }

    /**
     * Gets the value of the requestOrderItems property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the requestOrderItems property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getRequestOrderItems().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link RequestOrderItem }
     *
     *
     */
    public List<RequestOrderItem> getRequestOrderItems() {
        if (requestOrderItems == null) {
            requestOrderItems = new ArrayList<RequestOrderItem>();
        }
        return this.requestOrderItems;
    }


    public Set<Integer> getParentROs() {
        if (parentROs == null) {
            parentROs = new HashSet<>();
        }
        return this.parentROs;
    }

	public Boolean getAssetOrder() {
		return assetOrder;
	}

	public void setAssetOrder(Boolean assetOrder) {
		this.assetOrder = assetOrder;
	}

    public String getAlternateF9Order() {
        return alternateF9Order;
    }

    public void setAlternateF9Order(String alternateF9Order) {
        this.alternateF9Order = alternateF9Order;
    }

    public IdCodeName getRequestCompany() {
		return requestCompany;
	}

	public void setRequestCompany(IdCodeName requestCompany) {
		this.requestCompany = requestCompany;
	}

	public IdCodeName getFulfillmentCompany() {
		return fulfillmentCompany;
	}

	public void setFulfillmentCompany(IdCodeName fulfillmentCompany) {
		this.fulfillmentCompany = fulfillmentCompany;
	}

	 /**
     * Gets the value of the applyBudget property.
     *
     * @return
     *     possible object is
     *     {@link Boolean }
     *
     */
    public Boolean isApplyBudget() {
        return applyBudget;
    }

    /**
     * Sets the value of the applyBudget property.
     *
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *
     */
    public void setApplyBudget(Boolean value) {
        this.applyBudget = value;
    }

	public String getBudgetReason() {
		return budgetReason;
	}

	public void setBudgetReason(String budgetReason) {
		this.budgetReason = budgetReason;
	}

    public OrderTransferType getTransferType() {
        return transferType;
    }

    public void setTransferType(OrderTransferType transferType) {
        this.transferType = transferType;
    }

    public String getSearchTag() {
        return searchTag;
    }

    public void setSearchTag(String searchTag) {
        this.searchTag = searchTag;
    }

	public void setRequestOrderItems(List<RequestOrderItem> requestOrderItems) {
		this.requestOrderItems = requestOrderItems;
	}

	public Integer getVendorId() {
		return vendorId;
	}

	public void setVendorId(Integer vendorId) {
		this.vendorId = vendorId;
	}

	public Date getNotificationTime() {
		return notificationTime;
	}

	public void setNotificationTime(Date notificationTime) {
		this.notificationTime = notificationTime;
	}

	public boolean isNotified() {
		return notified;
	}

	public void setNotified(boolean notified) {
		this.notified = notified;
	}

	public String getNotificationTypes() {
		return notificationTypes;
	}

	public void setNotificationTypes(String notificationTypes) {
		this.notificationTypes = notificationTypes;
	}

	public String getVendorName() {
		return vendorName;
	}

	public void setVendorName(String vendorName) {
		this.vendorName = vendorName;
	}

    public Integer getNumberOfDays() {
        return numberOfDays;
    }

    public void setNumberOfDays(Integer numberOfDays) {
        this.numberOfDays = numberOfDays;
    }

    public Boolean getRaiseBy() {
        return raiseBy;
    }

    public void setRaiseBy(Boolean raiseBy) {
        this.raiseBy = raiseBy;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Boolean getBulkOrder() {
        return bulkOrder;
    }

    public void setBulkOrder(Boolean bulkOrder) {
        this.bulkOrder = bulkOrder;
    }

    public Boolean getSpecializedUrgentOrder() {
        return specializedUrgentOrder;
    }

    public void setSpecializedUrgentOrder(Boolean specializedUrgentOrder) {
        this.specializedUrgentOrder = specializedUrgentOrder;
    }
}

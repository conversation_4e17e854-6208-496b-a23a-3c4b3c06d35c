package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;


public class UnitInventoryData {
    private Integer keyId;
    private String keyType;
    private BigDecimal quantity;
    private BigDecimal  exQuantity = BigDecimal.ZERO;;
    private BigDecimal price  = BigDecimal.ZERO;;
    private String uom;

    public UnitInventoryData() {
    }

    public UnitInventoryData(Integer keyId, String keyType, BigDecimal quantity, String uom) {
        this.keyId = keyId;
        this.keyType = keyType;
        this.quantity = quantity;
        this.uom = uom;
    }

    public Integer getKeyId() {
        return keyId;
    }

    public void setKeyId(Integer keyId) {
        this.keyId = keyId;
    }

    public String getKeyType() {
        return keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getExQuantity() {
        return exQuantity;
    }

    public void setExQuantity(BigDecimal exQuantity) {
        this.exQuantity = exQuantity;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getUom() {
        return uom;
    }

    public void setUom(String uom) {
        this.uom = uom;
    }
}

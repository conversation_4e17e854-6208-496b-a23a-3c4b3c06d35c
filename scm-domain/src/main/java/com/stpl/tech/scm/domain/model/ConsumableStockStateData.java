package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;

public class ConsumableStockStateData {
    private Integer consumableStockStateId;
    private Integer unitId;
    private Integer productId;
    private Integer skuId;
    private BigDecimal brokenStock;
    private BigDecimal inUseStock;

    public Integer getConsumableStockStateId() {
        return this.consumableStockStateId;
    }

    public void setConsumableStockStateId(Integer consumableStockStateId) {
        this.consumableStockStateId = consumableStockStateId;
    }

    public Integer getUnitId() {
        return this.unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getProductId() {
        return this.productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getSkuId() {
        return this.skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    public BigDecimal getBrokenStock() {
        return this.brokenStock;
    }

    public void setBrokenStock(BigDecimal brokenStock) {
        this.brokenStock = brokenStock;
    }

    public BigDecimal getInUseStock() {
        return this.inUseStock;
    }

    public void setInUseStock(BigDecimal inUseStock) {
        this.inUseStock = inUseStock;
    }
}

package com.stpl.tech.scm.domain.model;

import java.util.Date;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DemandForecastResult extends ForecastResult {

    private List<DayWiseSlotWiseSalesDataDto> salesDataList;
    private Date businessDate;
    
}

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.06.20 at 05:17:20 PM IST
//


package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.scm.domain.vo.InventoryItemVO;
import com.stpl.tech.scm.domain.vo.ReceivingVO;


/**
 * <p>Java class for GoodsReceived complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="GoodsReceived"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="generationTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="lastUpdateTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="generationUnitId" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="generatedForUnitId" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="generatedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="receivedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="cancelledBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="status" type="{http://www.w3schools.com}SCMOrderStatus"/&gt;
 *         &lt;element name="comment" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="totalAmount" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="requestOrderId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="purchaseOrderId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="transferOrderId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="autoGenerated" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="parentGR" type="{http://www.w3schools.com}GoodsReceived"/&gt;
 *         &lt;element name="goodsReceivedItems" type="{http://www.w3schools.com}GoodsReceivedItem" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "GoodsReceived", propOrder = {
    "id",
    "generationTime",
    "lastUpdateTime",
    "generationUnitId",
    "generatedForUnitId",
    "generatedBy",
    "receivedBy",
    "cancelledBy",
    "status",
    "comment",
    "totalAmount",
    "requestOrderId",
    "purchaseOrderId",
    "transferOrderId",
    "autoGenerated",
    "parentGR",
    "goodsReceivedItems",
    "rejectGRComment",
    "isRejectedGR"
})
public class GoodsReceived extends AbstractInventoryVO implements ReceivingVO {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer id;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date generationTime;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date lastUpdateTime;
    @XmlElement(required = true)
    protected IdCodeName generationUnitId;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName generatedForUnitId;
    @XmlElement(required = true)
    protected IdCodeName sourceCompany;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName receivingCompany;
    @XmlElement(required = true)
    protected IdCodeName generatedBy;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName receivedBy;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName cancelledBy;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SCMOrderStatus status;
    @XmlElement(required = true)
    protected String comment;
    protected float totalAmount;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer requestOrderId;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer purchaseOrderId;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer transferOrderId;
    protected boolean autoGenerated;
    @XmlElement(required = true, nillable = true)
    protected Integer parentGR;
    protected boolean specialOrder;
    @XmlElement(required = true)
    protected String rejectGRComment;
    @XmlElement(required = true)
    protected String parentGRComment;
    protected boolean isRejectedGR;
    protected List<GoodsReceivedItem> goodsReceivedItems;
    @XmlElement(required = true, nillable = true)
    protected PriceUpdateEntryType inventoryType;
    @XmlSchemaType(name = "string")
    protected TransferOrderType transferOrderType;
    @XmlElement(required = false, type = Double.class, nillable = true)
    protected Double amountWithoutRejected;
    protected Integer originalGrForUnitId;
    protected Boolean updateRecievings;
    protected String type;
    protected Integer invoiceId;
    protected UnitCategory generationUnitCategory;
    protected Address generationUnitAddress;

    protected String docIdsPorImages;
    protected Boolean isRejectable;

    protected Integer eventId;

    public UnitCategory getGenerationUnitCategory() {
        return generationUnitCategory;
    }

    public void setGenerationUnitCategory(UnitCategory generationUnitCategory) {
        this.generationUnitCategory = generationUnitCategory;
    }

    public Address getGenerationUnitAddress() {
        return generationUnitAddress;
    }

    public void setGenerationUnitAddress(Address generationUnitAddress) {
        this.generationUnitAddress = generationUnitAddress;
    }

    public UnitCategory getGeneratedForUnitCategory() {
        return generatedForUnitCategory;
    }

    public void setGeneratedForUnitCategory(UnitCategory generatedForUnitCategory) {
        this.generatedForUnitCategory = generatedForUnitCategory;
    }

    public Address getGeneratedForUnitAddress() {
        return generatedForUnitAddress;
    }

    public void setGeneratedForUnitAddress(Address generatedForUnitAddress) {
        this.generatedForUnitAddress = generatedForUnitAddress;
    }

    protected UnitCategory generatedForUnitCategory;
    protected Address generatedForUnitAddress;

    /**
     * Gets the value of the id property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setId(Integer value) {
        this.id = value;
    }

    /**
     * Gets the value of the generationTime property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getGenerationTime() {
        return generationTime;
    }

    /**
     * Sets the value of the generationTime property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setGenerationTime(Date value) {
        this.generationTime = value;
    }

    /**
     * Gets the value of the lastUpdateTime property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * Sets the value of the lastUpdateTime property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setLastUpdateTime(Date value) {
        this.lastUpdateTime = value;
    }

    /**
     * Gets the value of the generationUnitId property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getGenerationUnitId() {
        return generationUnitId;
    }

    /**
     * Sets the value of the generationUnitId property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setGenerationUnitId(IdCodeName value) {
        this.generationUnitId = value;
    }

    /**
     * Gets the value of the generatedForUnitId property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getGeneratedForUnitId() {
        return generatedForUnitId;
    }

    /**
     * Sets the value of the generatedForUnitId property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setGeneratedForUnitId(IdCodeName value) {
        this.generatedForUnitId = value;
    }

    /**
     * Gets the value of the generatedBy property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getGeneratedBy() {
        return generatedBy;
    }

    /**
     * Sets the value of the generatedBy property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setGeneratedBy(IdCodeName value) {
        this.generatedBy = value;
    }

    /**
     * Gets the value of the receivedBy property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getReceivedBy() {
        return receivedBy;
    }

    /**
     * Sets the value of the receivedBy property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setReceivedBy(IdCodeName value) {
        this.receivedBy = value;
    }

    /**
     * Gets the value of the cancelledBy property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getCancelledBy() {
        return cancelledBy;
    }

    /**
     * Sets the value of the cancelledBy property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setCancelledBy(IdCodeName value) {
        this.cancelledBy = value;
    }

    /**
     * Gets the value of the status property.
     *
     * @return
     *     possible object is
     *     {@link SCMOrderStatus }
     *
     */
    public SCMOrderStatus getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     *
     * @param value
     *     allowed object is
     *     {@link SCMOrderStatus }
     *
     */
    public void setStatus(SCMOrderStatus value) {
        this.status = value;
    }

    /**
     * Gets the value of the comment property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getComment() {
        return comment;
    }

    /**
     * Sets the value of the comment property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setComment(String value) {
        this.comment = value;
    }

    /**
     * Gets the value of the totalAmount property.
     *
     */
    public float getTotalAmount() {
        return totalAmount;
    }

    /**
     * Sets the value of the totalAmount property.
     *
     */
    public void setTotalAmount(float value) {
        this.totalAmount = value;
    }

    /**
     * Gets the value of the requestOrderId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getRequestOrderId() {
        return requestOrderId;
    }

    /**
     * Sets the value of the requestOrderId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setRequestOrderId(Integer value) {
        this.requestOrderId = value;
    }

    /**
     * Gets the value of the purchaseOrderId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getPurchaseOrderId() {
        return purchaseOrderId;
    }

    /**
     * Sets the value of the purchaseOrderId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setPurchaseOrderId(Integer value) {
        this.purchaseOrderId = value;
    }

    /**
     * Gets the value of the transferOrderId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getTransferOrderId() {
        return transferOrderId;
    }

    /**
     * Sets the value of the transferOrderId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setTransferOrderId(Integer value) {
        this.transferOrderId = value;
    }

    /**
     * Gets the value of the autoGenerated property.
     *
     */
    public boolean isAutoGenerated() {
        return autoGenerated;
    }

    /**
     * Sets the value of the autoGenerated property.
     *
     */
    public void setAutoGenerated(boolean value) {
        this.autoGenerated = value;
    }

    /**
     * Gets the value of the parentGR property.
     *
     * @return
     *     possible object is
     *     {@link GoodsReceived }
     *
     */
    public Integer getParentGR() {
        return parentGR;
    }

    /**
     * Sets the value of the parentGR property.
     *
     * @param value
     *     allowed object is
     *     {@link GoodsReceived }
     *
     */
    public void setParentGR(Integer value) {
        this.parentGR = value;
    }

    /**
     * Gets the value of the goodsReceivedItems property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the goodsReceivedItems property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getGoodsReceivedItems().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link GoodsReceivedItem }
     *
     *
     */
    public List<GoodsReceivedItem> getGoodsReceivedItems() {
        if (goodsReceivedItems == null) {
            goodsReceivedItems = new ArrayList<GoodsReceivedItem>();
        }
        return this.goodsReceivedItems;
    }


    public boolean isSpecialOrder() {
        return specialOrder;
    }

    public void setSpecialOrder(boolean specialOrder) {
        this.specialOrder = specialOrder;
    }

	/* (non-Javadoc)
	 * @see com.stpl.tech.scm.domain.vo.InventoryVO#getKeyId()
	 */
	@Override
    @JsonIgnore
	public int getKeyId() {
		return this.getId();
	}

	/* (non-Javadoc)
	 * @see com.stpl.tech.scm.domain.vo.InventoryVO#getKeyType()
	 */
	@Override
	public StockEventType getKeyType() {
		return StockEventType.RECEIVED;
	}

	/* (non-Javadoc)
	 * @see com.stpl.tech.scm.domain.vo.InventoryVO#getUnitId()
	 */
	@Override
    @JsonIgnore
	public int getUnitId() {
		return this.getGeneratedForUnitId().getId();
	}

	/* (non-Javadoc)
	 * @see com.stpl.tech.scm.domain.vo.InventoryVO#getInventoryType()
	 */
	@Override
	public PriceUpdateEntryType getInventoryType() {
		return this.inventoryType;
	}


	public void setInventoryType(PriceUpdateEntryType inventoryType) {
		this.inventoryType = inventoryType;
	}

	/* (non-Javadoc)
	 * @see com.stpl.tech.scm.domain.model.AbstractInventoryVO#getInventoryItems()
	 */
	@Override
	@JsonIgnore
	public List<InventoryItemVO> getInventoryItems() {
		return new ArrayList<>(this.goodsReceivedItems);
	}
	public String getRejectGRComment() {
		return rejectGRComment;
	}

	public void setRejectGRComment(String rejectGRComment) {
		this.rejectGRComment = rejectGRComment;
	}

	public boolean isRejectedGR() {
		return isRejectedGR;
	}

	public void setRejectedGR(boolean isRejectedGR) {
		this.isRejectedGR = isRejectedGR;
	}

	public String getParentGRComment() {
		return parentGRComment;
	}

	public void setParentGRComment(String parentGRComment) {
		this.parentGRComment = parentGRComment;
	}

	public IdCodeName getSourceCompany() {
		return sourceCompany;
	}

	public void setSourceCompany(IdCodeName sourceCompany) {
		this.sourceCompany = sourceCompany;
	}

	public IdCodeName getReceivingCompany() {
		return receivingCompany;
	}

	public void setReceivingCompany(IdCodeName receivingCompany) {
		this.receivingCompany = receivingCompany;
	}

    public TransferOrderType getTransferOrderType() {
        return transferOrderType;
    }

    public void setTransferOrderType(TransferOrderType transferOrderType) {
        this.transferOrderType = transferOrderType;
    }

    public Double getAmountWithoutRejected() {
        return amountWithoutRejected;
    }

    public void setAmountWithoutRejected(Double amountWithoutRejected) {
        this.amountWithoutRejected = amountWithoutRejected;
    }

    public Integer getOriginalGrForUnitId() {
        return originalGrForUnitId;
    }

    public void setOriginalGrForUnitId(Integer originalGrForUnitId) {
        this.originalGrForUnitId = originalGrForUnitId;
    }

    public Boolean getUpdateRecievings() {
        return updateRecievings;
    }

    public void setUpdateRecievings(Boolean updateRecievings) {
        this.updateRecievings = updateRecievings;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Override
	public boolean isAssetOrder() {
		return transferOrderType.isAsset();
	}

    public void setGoodsReceivedItems(List<GoodsReceivedItem> goodsReceivedItems) {
        this.goodsReceivedItems = goodsReceivedItems;
    }

    public Integer getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Integer invoiceId) {
        this.invoiceId = invoiceId;
    }

    public Boolean getRejectable() {
        return isRejectable;
    }

    public void setRejectable(Boolean rejectable) {
        isRejectable = rejectable;
    }

    public String getDocIdsPorImages() {
        return docIdsPorImages;
    }

    public void setDocIdsPorImages(String docIdsPorImages) {
        this.docIdsPorImages = docIdsPorImages;
    }

    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }
}

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.04.20 at 07:12:44 PM IST 
//


package com.stpl.tech.scm.domain.model;

import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;


/**
 * <p>Java class for VendorAccountDetail complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="VendorAccountDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="accountId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="vendorDetail" type="{http://www.w3schools.com}VendorDetail"/&gt;
 *         &lt;element name="accountNumber" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ifscCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="accountType" type="{http://www.w3schools.com}VendorAccountType"/&gt;
 *         &lt;element name="micrCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="cancelledCheque" type="{http://www.w3schools.com}DocumentDetail"/&gt;
 *         &lt;element name="accountContact" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="accountContactName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="accountContactEmail" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="paymentCycle" type="{http://www.w3schools.com}PaymentCycle"/&gt;
 *         &lt;element name="kindOfAccount" type="{http://www.w3schools.com}BankAccountType"/&gt;
 *         &lt;element name="updateTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="updatedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="debitBalance" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="paymentBlocked" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "VendorAccountDetail", propOrder = {
    "accountId",
    "vendorDetail",
    "accountNumber",
    "ifscCode",
    "accountType",
    "micrCode",
    "cancelledCheque",
    "accountContact",
    "accountContactName",
    "accountContactEmail",
    "paymentCycle",
    "kindOfAccount",
    "updateTime",
    "updatedBy",
    "debitBalance",
    "paymentBlocked"
})
public class VendorAccountDetail {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer accountId;
    @XmlElement(required = true)
    protected IdCodeName vendorDetail;
    @XmlElement(required = true)
    protected String accountNumber;
    @XmlElement(required = true)
    protected String ifscCode;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected VendorAccountType accountType;
    @XmlElement(required = true)
    protected String micrCode;
    @XmlElement(required = true)
    protected DocumentDetail cancelledCheque;
    @XmlElement(required = true)
    protected String accountContact;
    @XmlElement(required = true)
    protected String accountContactName;
    @XmlElement(required = true)
    protected String accountContactEmail;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected PaymentCycle paymentCycle;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected BankAccountType kindOfAccount;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date updateTime;
    @XmlElement(required = true)
    protected IdCodeName updatedBy;
    @XmlElement(defaultValue = "false")
    protected boolean paymentBlocked;
    protected String section206;

    protected  Date section206UpdatedAt;

    /**
     * Gets the value of the accountId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getAccountId() {
        return accountId;
    }

    /**
     * Sets the value of the accountId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setAccountId(Integer value) {
        this.accountId = value;
    }

    /**
     * Gets the value of the vendorDetail property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getVendorDetail() {
        return vendorDetail;
    }

    /**
     * Sets the value of the vendorDetail property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setVendorDetail(IdCodeName value) {
        this.vendorDetail = value;
    }

    /**
     * Gets the value of the accountNumber property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAccountNumber() {
        return accountNumber;
    }

    /**
     * Sets the value of the accountNumber property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAccountNumber(String value) {
        this.accountNumber = value;
    }

    /**
     * Gets the value of the ifscCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIfscCode() {
        return ifscCode;
    }

    /**
     * Sets the value of the ifscCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIfscCode(String value) {
        this.ifscCode = value;
    }

    /**
     * Gets the value of the accountType property.
     * 
     * @return
     *     possible object is
     *     {@link VendorAccountType }
     *     
     */
    public VendorAccountType getAccountType() {
        return accountType;
    }

    /**
     * Sets the value of the accountType property.
     * 
     * @param value
     *     allowed object is
     *     {@link VendorAccountType }
     *     
     */
    public void setAccountType(VendorAccountType value) {
        this.accountType = value;
    }

    /**
     * Gets the value of the micrCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMicrCode() {
        return micrCode;
    }

    /**
     * Sets the value of the micrCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMicrCode(String value) {
        this.micrCode = value;
    }

    /**
     * Gets the value of the cancelledCheque property.
     * 
     * @return
     *     possible object is
     *     {@link DocumentDetail }
     *     
     */
    public DocumentDetail getCancelledCheque() {
        return cancelledCheque;
    }

    /**
     * Sets the value of the cancelledCheque property.
     * 
     * @param value
     *     allowed object is
     *     {@link DocumentDetail }
     *     
     */
    public void setCancelledCheque(DocumentDetail value) {
        this.cancelledCheque = value;
    }

    /**
     * Gets the value of the accountContact property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAccountContact() {
        return accountContact;
    }

    /**
     * Sets the value of the accountContact property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAccountContact(String value) {
        this.accountContact = value;
    }

    /**
     * Gets the value of the accountContactName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAccountContactName() {
        return accountContactName;
    }

    /**
     * Sets the value of the accountContactName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAccountContactName(String value) {
        this.accountContactName = value;
    }

    /**
     * Gets the value of the accountContactEmail property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAccountContactEmail() {
        return accountContactEmail;
    }

    /**
     * Sets the value of the accountContactEmail property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAccountContactEmail(String value) {
        this.accountContactEmail = value;
    }

    /**
     * Gets the value of the paymentCycle property.
     * 
     * @return
     *     possible object is
     *     {@link PaymentCycle }
     *     
     */
    public PaymentCycle getPaymentCycle() {
        return paymentCycle;
    }

    /**
     * Sets the value of the paymentCycle property.
     * 
     * @param value
     *     allowed object is
     *     {@link PaymentCycle }
     *     
     */
    public void setPaymentCycle(PaymentCycle value) {
        this.paymentCycle = value;
    }

    /**
     * Gets the value of the kindOfAccount property.
     * 
     * @return
     *     possible object is
     *     {@link BankAccountType }
     *     
     */
    public BankAccountType getKindOfAccount() {
        return kindOfAccount;
    }

    /**
     * Sets the value of the kindOfAccount property.
     * 
     * @param value
     *     allowed object is
     *     {@link BankAccountType }
     *     
     */
    public void setKindOfAccount(BankAccountType value) {
        this.kindOfAccount = value;
    }

    /**
     * Gets the value of the updateTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * Sets the value of the updateTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUpdateTime(Date value) {
        this.updateTime = value;
    }

    /**
     * Gets the value of the updatedBy property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getUpdatedBy() {
        return updatedBy;
    }

    /**
     * Sets the value of the updatedBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setUpdatedBy(IdCodeName value) {
        this.updatedBy = value;
    }

    /**
     * Gets the value of the paymentBlocked property.
     *
     */
    public boolean isPaymentBlocked() {
        return paymentBlocked;
    }

    /**
     * Sets the value of the paymentBlocked property.
     *
     */
    public void setPaymentBlocked(boolean value) {
        this.paymentBlocked = value;
    }

    public String getSection206() {
        return section206;
    }

    public void setSection206(String section206) {
        this.section206 = section206;
    }

    public Date getSection206UpdatedAt() {
        return section206UpdatedAt;
    }

    public void setSection206UpdatedAt(Date section206UpdatedAt) {
        this.section206UpdatedAt = section206UpdatedAt;
    }
}

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2017.09.11 at 11:20:21 AM IST
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for PaymentRequestType.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="PaymentRequestType"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="INDEPENDENT"/&gt;
 *     &lt;enumeration value="GOODS_RECEIVED"/&gt;
 *     &lt;enumeration value="SERVICE_ORDER"/&gt;
 *     &lt;enumeration value="ADVANCE_PAYMENT"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 *
 */
@XmlType(name = "PaymentRequestType")
@XmlEnum
public enum PaymentRequestType {

    INDEPENDENT,
    GOODS_RECEIVED,
    SERVICE_RECEIVED,
    ADVANCE_PAYMENT,
    MILK_BAKERY;

    public String value() {
        return name();
    }

    public static PaymentRequestType fromValue(String v) {
        return valueOf(v);
    }

}

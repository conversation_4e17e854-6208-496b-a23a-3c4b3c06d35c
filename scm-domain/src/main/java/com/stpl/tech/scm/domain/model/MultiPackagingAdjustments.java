package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;

public class MultiPackagingAdjustments {

    private Integer itemPackagingId;
    private Integer packagingId;
    private Boolean itemCheckPack;
    private BigDecimal itemQuantity;

    public MultiPackagingAdjustments() {
    }

    public MultiPackagingAdjustments(Integer itemPackagingId, Integer packagingId, Boolean itemCheckPack, BigDecimal itemQuantity) {
        this.itemPackagingId = itemPackagingId;
        this.packagingId = packagingId;
        this.itemCheckPack = itemCheckPack;
        this.itemQuantity = itemQuantity;
    }

    public Integer getItemPackagingId() {
        return itemPackagingId;
    }

    public void setItemPackagingId(Integer itemPackagingId) {
        this.itemPackagingId = itemPackagingId;
    }

    public Integer getPackagingId() {
        return packagingId;
    }

    public void setPackagingId(Integer packagingId) {
        this.packagingId = packagingId;
    }

    public Boolean getItemCheckPack() {
        return itemCheckPack;
    }

    public void setItemCheckPack(Boolean itemCheckPack) {
        this.itemCheckPack = itemCheckPack;
    }

    public BigDecimal getItemQuantity() {
        return itemQuantity;
    }

    public void setItemQuantity(BigDecimal itemQuantity) {
        this.itemQuantity = itemQuantity;
    }
}

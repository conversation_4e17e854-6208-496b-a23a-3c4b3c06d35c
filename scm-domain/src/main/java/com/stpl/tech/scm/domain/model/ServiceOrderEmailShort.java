package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.Date;

public class ServiceOrderEmailShort {
    private Integer soId;
    private String createdBy;
    private Date generatedDate;
    private String soType;
    private String vendorName;
    private VendorDispatchLocation dispatchLocation;
    private String costCenterName;
    private BigDecimal totalAmount;
    private BigDecimal pendingAmount;

    public Integer getSoId() {
        return soId;
    }

    public void setSoId(Integer soId) {
        this.soId = soId;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getGeneratedDate() {
        return generatedDate;
    }

    public void setGeneratedDate(Date generatedDate) {
        this.generatedDate = generatedDate;
    }

    public String getSoType() {
        return soType;
    }

    public void setSoType(String soType) {
        this.soType = soType;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public VendorDispatchLocation getDispatchLocation() {
        return dispatchLocation;
    }

    public void setDispatchLocation(VendorDispatchLocation dispatchLocation) {
        this.dispatchLocation = dispatchLocation;
    }

    public String getCostCenterName() {
        return costCenterName;
    }

    public void setCostCenterName(String costCenterName) {
        this.costCenterName = costCenterName;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getPendingAmount() {
        return pendingAmount;
    }

    public void setPendingAmount(BigDecimal pendingAmount) {
        this.pendingAmount = pendingAmount;
    }
}

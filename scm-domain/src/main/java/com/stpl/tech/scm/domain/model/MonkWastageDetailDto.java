package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MonkWastageDetailDto {
    private Integer id;
    private Integer wastageData;
    private Integer taskId;
    private Integer orderId;
    private BigDecimal quantity;
    private String monkEvent;
    private String chaiMonk;
    private Integer errorCode;
    private String remakeReason;
    private BigDecimal expectedMilkQuantity;
    private String recipeString;
    private Integer milkProductId;
    private Integer unitId;
    private String isClubbed;
    private Integer clubbedWithTask;
    private String isSplit;
    private Integer linkedTaskId;
    private String isManualTask;
    private MonkWastageProcessingEnum isProcessed;
    private Date logAddTime;
    private Date logAddTimeAtServer;
    private String className;
} 
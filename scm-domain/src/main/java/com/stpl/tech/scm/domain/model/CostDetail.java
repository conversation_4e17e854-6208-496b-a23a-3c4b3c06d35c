//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.08.18 at 02:16:18 PM IST 
//


package com.stpl.tech.scm.domain.model;

import com.stpl.tech.scm.domain.vo.InventoryItemVO;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * <p>Java class for CostDetail complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="CostDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="costDetailId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="unitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="quantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="oldPrice" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="price" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="uom" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="keyType" type="{http://www.w3schools.com}PriceUpdateEntryType"/&gt;
 *         &lt;element name="keyId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="lastUpdatedTimes" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CostDetail", propOrder = {
    "costDetailId",
    "unitId",
    "quantity",
    "oldPrice",
    "price",
    "uom",
    "keyType",
    "keyId",
    "lastUpdatedTimes"
})
public class CostDetail implements InventoryItemVO {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer costDetailId;
    protected String name;
    protected int unitId;
    @XmlElement(required = true, type = Float.class, nillable = true)
    protected BigDecimal quantity;
    @XmlElement(required = true, type = Float.class, nillable = true)
    protected BigDecimal oldPrice;
    @XmlElement(required = true, type = Float.class, nillable = true)
    protected BigDecimal price;
    @XmlElement(required = true)
    protected String uom;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected PriceUpdateEntryType keyType;
    protected int keyId;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date lastUpdatedTimes;
    protected boolean latest;
    protected List<InventoryItemDrilldown> drilldowns;
    @XmlElement(required = false, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "date")
	protected Date expiryDate;

    protected String creationReason;
    protected Integer creationItemId;
    protected StockEventType stockEventType;


    /**
     * Gets the value of the costDetailId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCostDetailId() {
        return costDetailId;
    }

    /**
     * Sets the value of the costDetailId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCostDetailId(Integer value) {
        this.costDetailId = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * Gets the value of the unitId property.
     * 
     */
    public int getUnitId() {
        return unitId;
    }

    /**
     * Sets the value of the unitId property.
     * 
     */
    public void setUnitId(int value) {
        this.unitId = value;
    }

 
    
    /**
     * Gets the value of the uom property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUom() {
        return uom;
    }

    /**
     * Sets the value of the uom property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUom(String value) {
        this.uom = value;
    }

    /**
     * Sets the value of the keyType property.
     * 
     * @param value
     *     allowed object is
     *     {@link PriceUpdateEntryType }
     *     
     */
    public void setKeyType(PriceUpdateEntryType value) {
        this.keyType = value;
    }

    /**
     * Gets the value of the keyId property.
     * 
     */
    public int getKeyId() {
        return keyId;
    }

    /**
     * Sets the value of the keyId property.
     * 
     */
    public void setKeyId(int value) {
        this.keyId = value;
    }

    /**
     * Gets the value of the lastUpdatedTimes property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getLastUpdatedTimes() {
        return lastUpdatedTimes;
    }

    /**
     * Sets the value of the lastUpdatedTimes property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLastUpdatedTimes(Date value) {
        this.lastUpdatedTimes = value;
    }

	@Override
	public int getItemKeyId() {
		return this.keyId;
	}

	@Override
	public StockEventType getItemKeyType() {
        if (Objects.isNull(stockEventType)) {
            return StockEventType.MANUAL_ENTRY;
        } else {
            return stockEventType;
        }
	}

	@Override
	public List<InventoryItemDrilldown> getDrillDowns() {
		drilldowns = new ArrayList<>();
        return drilldowns;
	}

	@Override
	public BigDecimal getQuantity() {
		return quantity;
	}



	@Override
	public void setQuantity(BigDecimal quantity) {
		this.quantity=quantity;
		
	}

    public BigDecimal getOldPrice() {
        return oldPrice;
    }

    public void setOldPrice(BigDecimal oldPrice) {
        this.oldPrice = oldPrice;
    }

    @Override
	public BigDecimal getPrice() {
		return price ;
	}

	@Override
	public void setPrice(BigDecimal price) {
		this.price=price;
		
	}
	
	@Override
	public PriceUpdateEntryType getKeyType() {
		return keyType;
	}

    public boolean isLatest() {
        return latest;
    }

    public void setLatest(boolean latest) {
        this.latest = latest;
    }

	public Date getExpiryDate() {
		return expiryDate;
	}

	public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}

	@Override
	public void setDrillDowns(List<InventoryItemDrilldown> drillDowns) {
		// TODO Auto-generated method stub
		
	}

    public String getCreationReason() {
        return creationReason;
    }

    public void setCreationReason(String creationReason) {
        this.creationReason = creationReason;
    }

    public Integer getCreationItemId() {
        return creationItemId;
    }

    public void setCreationItemId(Integer creationItemId) {
        this.creationItemId = creationItemId;
    }

    public StockEventType getStockEventType() {
        return stockEventType;
    }

    public void setStockEventType(StockEventType stockEventType) {
        this.stockEventType = stockEventType;
    }
}

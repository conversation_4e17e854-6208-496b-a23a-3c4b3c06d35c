package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.sql.Timestamp;


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class StockTakeReportSummaryObject {

    private Integer eventId;
    private String eventType;
    private String eventStatus;
    private Date eventCreationTime;
    private Date eventUpdationTime;
    private Integer unitId;
    private String unitName;
    private String unitType;
    private String initiatedBy;
    private String recipient;
    private Date lastStockTakeDate;
    private Number lastStockTakeBy;
    private Number totalAssetsinSystem;
    private Number scannedAssets;
    private Number notScannedAssets;
    private Number procurementNotScanned;
    private Number excessViaScan;
    private Number excessViaNoSticker;
    private Number procurementExcess;

}

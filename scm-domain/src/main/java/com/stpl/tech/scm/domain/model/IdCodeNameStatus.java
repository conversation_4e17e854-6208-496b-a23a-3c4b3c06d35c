/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.06.13 at 02:50:47 PM IST
// Generated on: 2016.06.10 at 03:09:17 PM IST

package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.util.Date;

/**
 * <p>
 * Java class for IdCodeName complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType name="IdCodeName"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="code" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "IdCodeNameStatus", propOrder = { "id", "code", "name", "category", "subCategory", "status",
		"mappingStatus", "profile" , "recipeRequired","productionUnit"})
public class IdCodeNameStatus {

	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer id;
	@XmlElement(required = true, nillable = true)
	protected String code;
	@XmlElement(required = true)
	protected String name;
	@XmlElement(required = true)
	protected String category;
	@XmlElement(required = true)
	protected String subCategory;
	@XmlElement(required = true)
	protected String status;
	@XmlElement(required = true)
	protected String mappingStatus;
	@XmlElement(required = true)
	protected String alias;
	@XmlElement(required = true)
	protected String profile;
	@XmlElement(required = true)
	protected boolean recipeRequired;

	protected  String inventoryList;
	protected String productionUnit;
	protected Integer packagingId;
	protected String taxCategoryCode;
	protected String voDisContinuedFrom; //vo -> Vendor Ordering
	protected String roDisContinuedFrom; //ro -> Request Order (adhoc)
	protected String vendorBlocked;
	protected String blockedReason;
	protected Date unblockedTillDate;
	protected String byPassContract;

	public IdCodeNameStatus() {

	}

	public IdCodeNameStatus(Integer id, String code, String name, String status) {
		super();
		this.id = id;
		this.code = code;
		this.name = name;
		this.status = status;
	}

	/**
	 * Gets the value of the id property.
	 *
	 * @return possible object is {@link Integer }
	 *
	 */
	public Integer getId() {
		return id;
	}

	/**
	 * Sets the value of the id property.
	 *
	 * @param value
	 *            allowed object is {@link Integer }
	 *
	 */
	public void setId(Integer value) {
		this.id = value;
	}

	/**
	 * Gets the value of the code property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getCode() {
		return code;
	}

	/**
	 * Sets the value of the code property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setCode(String value) {
		this.code = value;
	}

	/**
	 * Gets the value of the name property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getName() {
		return name;
	}

	/**
	 * Sets the value of the name property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setName(String value) {
		this.name = value;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getMappingStatus() {
		return mappingStatus;
	}

	public void setMappingStatus(String mappingStatus) {
		this.mappingStatus = mappingStatus;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getSubCategory() {
		return subCategory;
	}

	public void setSubCategory(String subCategory) {
		this.subCategory = subCategory;
	}

	public String getAlias() {
		return alias;
	}

	public void setAlias(String alias) {
		this.alias = alias;
	}

	public String getProfile() {
		return profile;
	}

	public void setProfile(String profile) {
		this.profile = profile;
	}

	public boolean isRecipeRequired() {
		return recipeRequired;
	}

	public void setRecipeRequired(boolean recipeRequired) {
		this.recipeRequired = recipeRequired;
	}

    public String getInventoryList() {
        return inventoryList;
    }

    public void setInventoryList(String inventoryList) {
        this.inventoryList = inventoryList;
    }

	public String getProductionUnit() {
		return productionUnit;
	}

	public void setProductionUnit(String productionUnit) {
		this.productionUnit = productionUnit;
	}

	public Integer getPackagingId() {
		return packagingId;
	}

	public void setPackagingId(Integer packagingId) {
		this.packagingId = packagingId;
	}


	public String getTaxCategoryCode() {
		return taxCategoryCode;
	}

	public void setTaxCategoryCode(String taxCategoryCode) {
		this.taxCategoryCode = taxCategoryCode;
	}

	public String getVoDisContinuedFrom() {
		return voDisContinuedFrom;
	}

	public void setVoDisContinuedFrom(String voDisContinuedFrom) {
		this.voDisContinuedFrom = voDisContinuedFrom;
	}

	public String getRoDisContinuedFrom() {
		return roDisContinuedFrom;
	}

	public void setRoDisContinuedFrom(String roDisContinuedFrom) {
		this.roDisContinuedFrom = roDisContinuedFrom;
	}

	public String getVendorBlocked() {
		return vendorBlocked;
	}

	public void setVendorBlocked(String vendorBlocked) {
		this.vendorBlocked = vendorBlocked;
	}

	public String getBlockedReason() {
		return blockedReason;
	}

	public void setBlockedReason(String blockedReason) {
		this.blockedReason = blockedReason;
	}

	public Date getUnblockedTillDate() {
		return unblockedTillDate;
	}

	public void setUnblockedTillDate(Date unblockedTillDate) {
		this.unblockedTillDate = unblockedTillDate;
	}

	public String getByPassContract() {
		return byPassContract;
	}

	public void setByPassContract(String byPassContract) {
		this.byPassContract = byPassContract;
	}

	@Override
	public String toString() {
		return "IdCodeNameStatus [id=" + id + ", code=" + code + ", name=" + name + ", category=" + category
				+ ", subCategory=" + subCategory + ", status=" + status + ", mappingStatus=" + mappingStatus + "]";
	}

}

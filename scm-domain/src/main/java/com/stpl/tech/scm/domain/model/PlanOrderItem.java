//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.06.03 at 10:40:51 AM IST 
//


package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for PlanOrderItem complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="PlanOrderItem"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="productId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="productName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="requestedQuantity" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="availableQuantity" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="unitOfMeasure" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="category" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="itemType" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="unitPrice" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="amount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="printCount" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PlanOrderItem", propOrder = {
    "id",
    "productId",
    "productName",
    "requestedQuantity",
    "availableQuantity",
    "unitOfMeasure",
    "category",
    "itemType",
    "unitPrice",
    "amount",
    "printCount",
    "recipeRequire",
    "excessQuantity",
    "expiryDate",
    "availableDates"
})
public class PlanOrderItem {

    protected int id;
    protected int productId;
    @XmlElement(required = true)
    protected String productName;
    @XmlElement(required = true)
    protected BigDecimal requestedQuantity;
    @XmlElement(required = true)
    protected BigDecimal availableQuantity;
    @XmlElement(required = true)
    protected String unitOfMeasure;
    @XmlElement(required = true)
    protected String category;
    @XmlElement(required = true)
    protected String itemType;
    @XmlElement(required = true)
    protected BigDecimal unitPrice;
    @XmlElement(required = true)
    protected BigDecimal amount;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer printCount;
    protected BigDecimal bufferedPercentage;
    protected BigDecimal bufferedQuantity;
    protected BigDecimal totalQuantity;
    protected Integer productionUnit;
    protected Boolean recipeRequire;
    protected BigDecimal excessQuantity;
    protected Date currentDate;
    protected Date expiryDate;
    protected Set<Date> availableDates = new TreeSet<Date>();
    protected String productionName;
    protected List<UnitRoMappingDetailData> unitDetail = new ArrayList<>();
    /**
     * Gets the value of the id property.
     * 
     */
    public int getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     */
    public void setId(int value) {
        this.id = value;
    }

    /**
     * Gets the value of the productId property.
     * 
     */
    public int getProductId() {
        return productId;
    }

    /**
     * Sets the value of the productId property.
     * 
     */
    public void setProductId(int value) {
        this.productId = value;
    }

    /**
     * Gets the value of the productName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getProductName() {
        return productName;
    }

    /**
     * Sets the value of the productName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProductName(String value) {
        this.productName = value;
    }

    /**
     * Gets the value of the requestedQuantity property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getRequestedQuantity() {
        return requestedQuantity;
    }

    /**
     * Sets the value of the requestedQuantity property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setRequestedQuantity(BigDecimal value) {
        this.requestedQuantity = value;
    }

    /**
     * Gets the value of the availableQuantity property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAvailableQuantity() {
        return availableQuantity;
    }

    /**
     * Sets the value of the availableQuantity property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAvailableQuantity(BigDecimal value) {
        this.availableQuantity = value;
    }

    /**
     * Gets the value of the unitOfMeasure property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    /**
     * Sets the value of the unitOfMeasure property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUnitOfMeasure(String value) {
        this.unitOfMeasure = value;
    }

    /**
     * Gets the value of the category property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCategory() {
        return category;
    }

    /**
     * Sets the value of the category property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCategory(String value) {
        this.category = value;
    }

    /**
     * Gets the value of the itemType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getItemType() {
        return itemType;
    }

    /**
     * Sets the value of the itemType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setItemType(String value) {
        this.itemType = value;
    }

    /**
     * Gets the value of the unitPrice property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    /**
     * Sets the value of the unitPrice property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setUnitPrice(BigDecimal value) {
        this.unitPrice = value;
    }

    /**
     * Gets the value of the amount property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * Sets the value of the amount property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAmount(BigDecimal value) {
        this.amount = value;
    }

    /**
     * Gets the value of the printCount property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getPrintCount() {
        return printCount;
    }

    /**
     * Sets the value of the printCount property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setPrintCount(Integer value) {
        this.printCount = value;
    }

    public BigDecimal getBufferedPercentage() {
        return bufferedPercentage;
    }

    public void setBufferedPercentage(BigDecimal bufferedPercentage) {
        this.bufferedPercentage = bufferedPercentage;
    }

    public BigDecimal getBufferedQuantity() {
        return bufferedQuantity;
    }

    public void setBufferedQuantity(BigDecimal bufferedQuantity) {
        this.bufferedQuantity = bufferedQuantity;
    }

    public BigDecimal getTotalQuantity() {
        return totalQuantity;
    }

    public void setTotalQuantity(BigDecimal totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    public Integer getProductionUnit() {
        return productionUnit;
    }

    public void setProductionUnit(Integer productionUnit) {
        this.productionUnit = productionUnit;
    }


    public Boolean getRecipeRequire() {
        return recipeRequire;
    }

    public void setRecipeRequire(Boolean recipeRequire) {
        this.recipeRequire = recipeRequire;
    }

    public BigDecimal getExcessQuantity() {
        return excessQuantity;
    }

    public void setExcessQuantity(BigDecimal excessQuantity) {
        this.excessQuantity = excessQuantity;
    }

    public Date getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    public Set<Date> getAvailableDates() {
        return availableDates;
    }

    public void setAvailableDates(Set<Date> availableDates) {
        this.availableDates = availableDates;
    }

    public Date getCurrentDate() {
        return currentDate;
    }

    public void setCurrentDate(Date currentDate) {
        this.currentDate = currentDate;
    }

    public String getProductionName() {
        return productionName;
    }

    public void setProductionName(String productionName) {
        this.productionName = productionName;
    }
    public List<UnitRoMappingDetailData> getUnitDetail(){
        return unitDetail;
    }
    public void addUnitDetail(List<UnitRoMappingDetailData> unitDetail){
        this.unitDetail = unitDetail;
    }
}

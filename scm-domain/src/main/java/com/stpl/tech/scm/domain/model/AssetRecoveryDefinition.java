/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.12 at 03:52:20 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.Date;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AssetDefinition", propOrder = {
        "assetRecoveryId",
        "assetId",
        "assetDefinition",
        "unitId",
        "assetStatus",
        "recoveryType",
        "recoveryEmpId",
        "recoveryEmp",
        "recoveryUnit",
        "recoveryUnitType",
        "recoveryAmount",
        "recoveryStatus",
        "recoveryDate",
        "createdBy",
        "creationDate",
        "recoveredAmount",
        "recoveredBy"
})
public class AssetRecoveryDefinition {


    protected Integer assetRecoveryId;

    protected Integer assetId;

    protected AssetDefinition assetDefinition;

    protected Integer unitId;

    protected String unitName;

    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected AssetStatusType assetStatus;

    protected String recoveryType;

    protected Integer recoveryEmpId;

    protected IdCodeName recoveryEmp;

    protected Integer recoveryUnit;

    protected String recoveryUnitType;

    protected Float recoveryAmount;

    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected AssetRecoveryStatusType recoveryStatus;

    @XmlElement(type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "date")
    protected Date salaryDeductionDate;

    @XmlElement(type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "date")
    protected Date recoveryDate;

    protected IdCodeName createdBy;

    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "date")
    protected Date creationDate;

    protected Float recoveredAmount;

    protected IdCodeName recoveredBy;

    protected Integer eventId;

    protected Boolean found;

    protected IdCodeName approvedBy;

    @XmlElement(required = false, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "date")
    protected Date approvalDate;

    protected IdCodeName auditedBy;

    public Integer getAssetRecoveryId() {
        return assetRecoveryId;
    }

    public void setAssetRecoveryId(Integer assetRecoveryId) {
        this.assetRecoveryId = assetRecoveryId;
    }

    public Integer getAssetId() {
        return assetId;
    }

    public void setAssetId(Integer assetId) {
        this.assetId = assetId;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public AssetStatusType getAssetStatus() {
        return assetStatus;
    }

    public void setAssetStatus(AssetStatusType assetStatus) {
        this.assetStatus = assetStatus;
    }

    public String getRecoveryType() {
        return recoveryType;
    }

    public void setRecoveryType(String recoveryType) {
        this.recoveryType = recoveryType;
    }

    public Integer getRecoveryEmpId() {
        return recoveryEmpId;
    }

    public void setRecoveryEmpId(Integer recoveryEmpId) {
        this.recoveryEmpId = recoveryEmpId;
    }

    public Integer getRecoveryUnit() {
        return recoveryUnit;
    }

    public void setRecoveryUnit(Integer recoveryUnit) {
        this.recoveryUnit = recoveryUnit;
    }

    public String getRecoveryUnitType() {
        return recoveryUnitType;
    }

    public void setRecoveryUnitType(String recoveryUnitType) {
        this.recoveryUnitType = recoveryUnitType;
    }

    public Float getRecoveryAmount() {
        return recoveryAmount;
    }

    public void setRecoveryAmount(Float recoveryAmount) {
        this.recoveryAmount = recoveryAmount;
    }

    public AssetRecoveryStatusType getRecoveryStatus() {
        return recoveryStatus;
    }

    public void setRecoveryStatus(AssetRecoveryStatusType recoveryStatus) {
        this.recoveryStatus = recoveryStatus;
    }

    public Date getSalaryDeductionDate() {
        return salaryDeductionDate;
    }

    public void setSalaryDeductionDate(Date salaryDeductionDate) {
        this.salaryDeductionDate = salaryDeductionDate;
    }

    public Date getRecoveryDate() {
        return recoveryDate;
    }

    public void setRecoveryDate(Date recoveryDate) {
        this.recoveryDate = recoveryDate;
    }


    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public Float getRecoveredAmount() {
        return recoveredAmount;
    }

    public void setRecoveredAmount(Float recoveredAmount) {
        this.recoveredAmount = recoveredAmount;
    }

    public IdCodeName getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(IdCodeName createdBy) {
        this.createdBy = createdBy;
    }

    public IdCodeName getRecoveredBy() {
        return recoveredBy;
    }

    public void setRecoveredBy(IdCodeName recoveredBy) {
        this.recoveredBy = recoveredBy;
    }

    public AssetDefinition getAssetDefinition() {
        return assetDefinition;
    }

    public void setAssetDefinition(AssetDefinition assetDefinition) {
        this.assetDefinition = assetDefinition;
    }

    public IdCodeName getRecoveryEmp() {
        return recoveryEmp;
    }

    public void setRecoveryEmp(IdCodeName recoveryEmp) {
        this.recoveryEmp = recoveryEmp;
    }

    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    public Boolean getFound() {
        return found;
    }

    public void setFound(Boolean found) {
        this.found = found;
    }

    public IdCodeName getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(IdCodeName approvedBy) {
        this.approvedBy = approvedBy;
    }

    public Date getApprovalDate() {
        return approvalDate;
    }

    public void setApprovalDate(Date approvalDate) {
        this.approvalDate = approvalDate;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public IdCodeName getAuditedBy() {
        return auditedBy;
    }

    public void setAuditedBy(IdCodeName auditedBy) {
        this.auditedBy = auditedBy;
    }
}

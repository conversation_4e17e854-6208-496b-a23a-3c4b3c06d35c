//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.06.20 at 05:17:20 PM IST
//

package com.stpl.tech.scm.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.domain.model.Adapter4;
import com.stpl.tech.scm.domain.vo.InventoryItemVO;
import com.stpl.tech.util.domain.adapter.BigDecimalSixPrecisionDeserializer;
import org.apache.commons.lang.NotImplementedException;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * Java class for GoodsReceivedItem complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType name="GoodsReceivedItem"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="productId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="skuId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="skuName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="category" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="subCategory" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="packagingDetails" type="{http://www.w3schools.com}SCMOrderPackaging" maxOccurs="unbounded"/&gt;
 *         &lt;element name="transferredQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="receivedQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="unitOfMeasure" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="unitPrice" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="negotiatedUnitPrice" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="calculatedAmount" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="requestOrderItemId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="purchaseOrderItemId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="TransferOrderItemId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="vendorId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="skuCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "GoodsReceivedItem", propOrder = {
    "id",
	"productId",
    "skuId",
    "skuName",
    "category",
    "subCategory",
    "packagingDetails",
		"transferredQuantity", "receivedQuantity", "unitOfMeasure", "unitPrice", "negotiatedUnitPrice",
    "calculatedAmount",
    "requestOrderItemId",
    "purchaseOrderItemId",
    "transferOrderItemId",
    "vendorId",
    "skuCode"
})
public class GoodsReceivedItem implements InventoryItemVO {

	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer id;
	protected int productId;
	protected int skuId;
	@XmlElement(required = true)
	protected String skuName;
    @XmlElement(required = true, nillable = true)
    protected String category;
    @XmlElement(required = true, nillable = true)
    protected String subCategory;
	@XmlElement(required = true)
	protected List<SCMOrderPackaging> packagingDetails;
	@XmlElement(required = true, type = Float.class, nillable = true)
	protected Float transferredQuantity;
	@XmlElement(required = true, type = Float.class, nillable = true)
	protected Float receivedQuantity;
	@XmlElement(required = true)
	protected String unitOfMeasure;
	@XmlElement(required = true, type = Double.class, nillable = true)
	protected Double unitPrice;
	@XmlElement(required = true, type = Double.class, nillable = true)
	protected Double negotiatedUnitPrice;
	@XmlElement(required = true, type = Float.class, nillable = true)
	protected Float calculatedAmount;
	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer requestOrderItemId;
	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer purchaseOrderItemId;
	@XmlElement(name = "TransferOrderItemId", required = true, type = Integer.class, nillable = true)
	protected Integer transferOrderItemId;
	@XmlElement(required = true, type = IdCodeName.class, nillable = true)
	protected IdCodeName vendor;
	protected PriceUpdateEntryType keyType;
	protected List<InventoryItemDrilldown> drillDowns;
	@JsonIgnore
	@JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal price;
	@XmlElement(required = true, nillable = true)
	protected String skuCode;
	@XmlElement(required = false, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "date")
	protected Date expiryDate;
	protected Integer associatedAssetId;
	protected String associatedAssetTagValue;
	protected Float excessQuantity;
	@XmlElement(required = true, type = Float.class, nillable = true)
	protected Float taxAmount;
	private List<TaxDetail> taxes;
	private String hsnCode;
	protected Integer categoryId;
	protected Integer subCategoryId;
	/**
	 * Gets the value of the id property.
	 *
     * @return
     *     possible object is
     *     {@link Integer }
	 *
	 */
	public Integer getId() {
		return id;
	}

	/**
	 * Sets the value of the id property.
	 *
	 * @param value
	 *            allowed object is {@link Integer }
	 *
	 */
	public void setId(Integer value) {
		this.id = value;
	}

	/**
	 * Gets the value of the skuId property.
	 *
	 */
	public int getSkuId() {
		return skuId;
	}

	/**
	 * Sets the value of the skuId property.
	 *
	 */
	public void setSkuId(int value) {
		this.skuId = value;
	}

	/**
	 * Gets the value of the skuName property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getSkuName() {
		return skuName;
	}

	/**
	 * Sets the value of the skuName property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setSkuName(String value) {
		this.skuName = value;
	}

    /**
     * Gets the value of the category property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getCategory() {
        return category;
    }

    /**
     * Sets the value of the category property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setCategory(String value) {
        this.category = value;
    }

    /**
     * Gets the value of the subCategory property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getSubCategory() {
        return subCategory;
    }

    /**
     * Sets the value of the subCategory property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setSubCategory(String value) {
        this.subCategory = value;
    }

	/**
	 * Gets the value of the packagingDetails property.
	 *
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the packagingDetails property.
	 *
	 * <p>
	 * For example, to add a new item, do as follows:
	 *
	 * <pre>
	 * getPackagingDetails().add(newItem);
	 * </pre>
	 *
	 *
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link SCMOrderPackaging }
	 *
	 *
	 */
	public List<SCMOrderPackaging> getPackagingDetails() {
		if (packagingDetails == null) {
			packagingDetails = new ArrayList<SCMOrderPackaging>();
		}
		return this.packagingDetails;
	}

	public void setPackagingDetails(List<SCMOrderPackaging> packagingDetails) {
		this.packagingDetails = packagingDetails;
	}

	/**
	 * Gets the value of the transferredQuantity property.
	 *
	 * @return possible object is {@link Float }
	 *
	 */
	public Float getTransferredQuantity() {
		return transferredQuantity;
	}

	/**
	 * Sets the value of the transferredQuantity property.
	 *
	 * @param value
	 *            allowed object is {@link Float }
	 *
	 */
	public void setTransferredQuantity(Float value) {
		this.transferredQuantity = value;
	}

	/**
	 * Gets the value of the receivedQuantity property.
	 *
	 * @return possible object is {@link Float }
	 *
	 */
	public Float getReceivedQuantity() {
		return receivedQuantity;
	}

	/**
	 * Sets the value of the receivedQuantity property.
	 *
	 * @param value
	 *            allowed object is {@link Float }
	 *
	 */
	public void setReceivedQuantity(Float value) {
		this.receivedQuantity = value;
	}

	/**
	 * Gets the value of the unitOfMeasure property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getUnitOfMeasure() {
		return unitOfMeasure;
	}

	/**
	 * Sets the value of the unitOfMeasure property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setUnitOfMeasure(String value) {
		this.unitOfMeasure = value;
	}

	/**
	 * Gets the value of the unitPrice property.
	 *
	 * @return possible object is {@link Double }
	 *
	 */
	public Double getUnitPrice() {
		return unitPrice;
	}

	/**
	 * Sets the value of the unitPrice property.
	 *
	 * @param unitPrice
	 *            allowed object is {@link Float }
	 *
	 */
	public void setUnitPrice(Double unitPrice) {
		this.unitPrice = unitPrice;
	}

	/**
	 * Gets the value of the negotiatedUnitPrice property.
	 *
	 * @return possible object is {@link Double }
	 *
	 */
	public Double getNegotiatedUnitPrice() {
		return negotiatedUnitPrice;
	}

	/**
	 * Sets the value of the negotiatedUnitPrice property.
	 *
	 * @param negotiatedUnitPrice
	 *            allowed object is {@link Double }
	 *
	 */
	public void setNegotiatedUnitPrice(Double negotiatedUnitPrice) {
		this.negotiatedUnitPrice = negotiatedUnitPrice;
	}

	/**
	 * Gets the value of the calculatedAmount property.
	 *
	 * @return possible object is {@link Float }
	 *
	 */
	public Float getCalculatedAmount() {
		return calculatedAmount;
	}

	/**
	 * Sets the value of the calculatedAmount property.
	 *
	 * @param value
	 *            allowed object is {@link Float }
	 *
	 */
	public void setCalculatedAmount(Float value) {
		this.calculatedAmount = value;
	}

	/**
	 * Gets the value of the requestOrderItemId property.
	 *
	 * @return possible object is {@link Integer }
	 *
	 */
	public Integer getRequestOrderItemId() {
		return requestOrderItemId;
	}

	/**
	 * Sets the value of the requestOrderItemId property.
	 *
	 * @param value
	 *            allowed object is {@link Integer }
	 *
	 */
	public void setRequestOrderItemId(Integer value) {
		this.requestOrderItemId = value;
	}

	/**
	 * Gets the value of the purchaseOrderItemId property.
	 *
	 * @return possible object is {@link Integer }
	 *
	 */
	public Integer getPurchaseOrderItemId() {
		return purchaseOrderItemId;
	}

	/**
	 * Sets the value of the purchaseOrderItemId property.
	 *
	 * @param value
	 *            allowed object is {@link Integer }
	 *
	 */
	public void setPurchaseOrderItemId(Integer value) {
		this.purchaseOrderItemId = value;
	}

	/**
	 * Gets the value of the transferOrderItemId property.
	 *
	 * @return possible object is {@link Integer }
	 *
	 */
	public Integer getTransferOrderItemId() {
		return transferOrderItemId;
	}

	/**
	 * Sets the value of the transferOrderItemId property.
	 *
	 * @param value
	 *            allowed object is {@link Integer }
	 *
	 */
	public void setTransferOrderItemId(Integer value) {
		this.transferOrderItemId = value;
	}

	/**
	 * Gets the value of the vendorId property.
	 *
	 * @return possible object is {@link Integer }
	 *
	 */
	public IdCodeName getVendor() {
		return vendor;
	}

	/**
	 * Sets the value of the vendorId property.
	 *
	 * @param value
	 *            allowed object is {@link Integer }
	 *
	 */
	public void setVendor(IdCodeName value) {
		this.vendor = value;
	}

	public int getProductId() {
		return productId;
	}

	public void setProductId(int productId) {
		this.productId = productId;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getKeyId()
	 */
	@Override
	@JsonIgnore
	public int getKeyId() {
		return PriceUpdateEntryType.SKU.equals(this.keyType) ? this.skuId : this.productId;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getKeyType()
	 */
	@Override
	@JsonIgnore
	public PriceUpdateEntryType getKeyType() {
		return keyType;
	}

	public void setKeyType(PriceUpdateEntryType keyType) {
		this.keyType = keyType;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getItemKeyId()
	 */
	@Override
	public int getItemKeyId() {
		return this.id;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getItemKeyType()
	 */
	@Override
	@JsonIgnore
	public StockEventType getItemKeyType() {
		return StockEventType.RECEIVED;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getDrillDowns()
	 */
	@Override
	public List<InventoryItemDrilldown> getDrillDowns() {
		if (this.drillDowns == null) {
			this.drillDowns = new ArrayList<>();
		}
		return this.drillDowns;
	}

	@Override
	public void setDrillDowns(List<InventoryItemDrilldown> drillDowns) {
		this.drillDowns = drillDowns;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getQuantity()
	 */
	@Override
	@JsonIgnore
	public BigDecimal getQuantity() {
		return this.receivedQuantity==null?BigDecimal.ZERO:new BigDecimal(this.receivedQuantity);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getPrice()
	 */
	@Override
	public BigDecimal getPrice() {
		if(this.price == null){
			this.price = new BigDecimal(this.negotiatedUnitPrice);
		}
		return this.price;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#setPrice(java.math.
	 * BigDecimal)
	 */
	@Override
	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#setQuantity(java.math.
	 * BigDecimal)
	 */
	@Override
	@JsonIgnore
	public void setQuantity(BigDecimal price) {
		throw new NotImplementedException("Not Implemeneted for GR item");
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getUom()
	 */
	@Override
	public String getUom() {
		return this.unitOfMeasure;
	}

	/**
	 * Gets the value of the skuCode property.
	 *
	 * @return
	 *     possible object is
	 *     {@link String }
	 *
	 */
	public String getSkuCode() {
		return skuCode;
	}

	/**
	 * Sets the value of the skuCode property.
	 *
	 * @param value
	 *     allowed object is
	 *     {@link String }
	 *
	 */
	public void setSkuCode(String value) {
		this.skuCode = value;
	}

	public Date getExpiryDate() {
		return expiryDate;
	}

	public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}

	public Integer getAssociatedAssetId() {
		return associatedAssetId;
	}

	public void setAssociatedAssetId(Integer associatedAssetId) {
		this.associatedAssetId = associatedAssetId;
	}

	public String getAssociatedAssetTagValue() {
		return associatedAssetTagValue;
	}

	public void setAssociatedAssetTagValue(String associatedAssetTagValue) {
		this.associatedAssetTagValue = associatedAssetTagValue;
	}

	public Float getExcessQuantity() {
		return excessQuantity;
	}

	public void setExcessQuantity(Float excessQuantity) {
		this.excessQuantity = excessQuantity;
	}

	public Float getTaxAmount() {
		return taxAmount;
	}

	public void setTaxAmount(Float taxAmount) {
		this.taxAmount = taxAmount;
	}

	public List<TaxDetail> getTaxes() {
		return taxes;
	}

	public void setTaxes(List<TaxDetail> taxes) {
		this.taxes = taxes;
	}

	public String getHsnCode() {
		return hsnCode;
	}

	public void setHsnCode(String hsnCode) {
		this.hsnCode = hsnCode;
	}

	public Integer getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Integer categoryId) {
		this.categoryId = categoryId;
	}

	public Integer getSubCategoryId() {
		return subCategoryId;
	}

	public void setSubCategoryId(Integer subCategoryId) {
		this.subCategoryId = subCategoryId;
	}
}

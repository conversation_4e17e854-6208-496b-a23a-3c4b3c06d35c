//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.04.21 at 05:10:24 PM IST 
//

package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.Date;
import java.util.Map;

/**
 * <p>
 * Java class for VendorRegistrationRequest complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="VendorRegistrationRequest"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="vendor" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="requestDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="requestBy" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="requestByName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="requestFor" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="requestForName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="email" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="copyEmails" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="vendorLink" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="requestStatus" type="{http://www.w3schools.com}VendorStatus"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "VendorRegistrationRequest", propOrder = { "id", "requestDate", "requestById", "requestByName",
		"requestForId", "requestForName", "email", "copyEmails", "requestStatus", "vendorType" })
public class VendorRegistrationRequest {

	protected int id;
	@XmlElement(required = true)
	protected String vendorName;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "date")
	protected Date requestDate;
	protected int requestById;
	@XmlElement(required = true)
	protected String requestByName;
	protected int requestForId;
	@XmlElement(required = true)
	protected String requestForName;
	@XmlElement(required = true)
	protected String email;
	@XmlElement(required = true)
	protected String copyEmails;
	protected Integer vendorId;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	protected VendorStatus requestStatus;
	protected VendorType vendorType;

	protected String link;

	protected String panCardNumber;
	protected Integer documentId;
	protected Map<String,String> disclaimerMap;

	protected Integer unitId;


	/**
	 * Gets the value of the id property.
	 *
	 */
	public int getId() {
		return id;
	}

	/**
	 * Sets the value of the id property.
	 *
	 */
	public void setId(int value) {
		this.id = value;
	}

	/**
	 * Gets the value of the vendor property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getVendorName() {
		return vendorName;
	}

	/**
	 * Sets the value of the vendor property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setVendorName(String value) {
		this.vendorName = value;
	}

	/**
	 * Gets the value of the requestDate property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public Date getRequestDate() {
		return requestDate;
	}

	/**
	 * Sets the value of the requestDate property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setRequestDate(Date value) {
		this.requestDate = value;
	}

	/**
	 * Gets the value of the requestBy property.
	 *
	 */
	public int getRequestById() {
		return requestById;
	}

	/**
	 * Sets the value of the requestBy property.
	 *
	 */
	public void setRequestById(int value) {
		this.requestById = value;
	}

	/**
	 * Gets the value of the requestByName property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getRequestByName() {
		return requestByName;
	}

	/**
	 * Sets the value of the requestByName property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setRequestByName(String value) {
		this.requestByName = value;
	}

	/**
	 * Gets the value of the requestFor property.
	 *
	 */
	public int getRequestForId() {
		return requestForId;
	}

	/**
	 * Sets the value of the requestFor property.
	 *
	 */
	public void setRequestForId(int value) {
		this.requestForId = value;
	}

	/**
	 * Gets the value of the requestForName property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getRequestForName() {
		return requestForName;
	}

	/**
	 * Sets the value of the requestForName property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setRequestForName(String value) {
		this.requestForName = value;
	}

	/**
	 * Gets the value of the email property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getEmail() {
		return email;
	}

	/**
	 * Sets the value of the email property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setEmail(String value) {
		this.email = value;
	}

	/**
	 * Gets the value of the copyEmails property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getCopyEmails() {
		return copyEmails;
	}

	/**
	 * Sets the value of the copyEmails property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setCopyEmails(String value) {
		this.copyEmails = value;
	}

	/**
	 * Gets the value of the vendorLink property.
	 *
	 */
	public Integer getVendorId() {
		return vendorId;
	}

	/**
	 * Sets the value of the vendorLink property.
	 *
	 */
	public void setVendorId(Integer value) {
		this.vendorId = value;
	}

	/**
	 * Gets the value of the requestStatus property.
	 *
	 * @return possible object is {@link VendorStatus }
	 *
	 */
	public VendorStatus getRequestStatus() {
		return requestStatus;
	}

	/**
	 * Sets the value of the requestStatus property.
	 *
	 * @param value
	 *            allowed object is {@link VendorStatus }
	 *
	 */
	public void setRequestStatus(VendorStatus value) {
		this.requestStatus = value;
	}

	public VendorType getVendorType() {
		return this.vendorType;
	}

	public void setVendorType(VendorType vendorType) {
		this.vendorType = vendorType;
	}

	public String getLink() {
		return link;
	}

	public void setLink(String link) {
		this.link = link;
	}


	public Map<String, String> getDisclaimerMap() {
		return disclaimerMap;
	}

	public void setDisclaimerMap(Map<String, String> disclaimerMap) {
		this.disclaimerMap = disclaimerMap;
	}

	public String getPanCardNumber() {
		return panCardNumber;
	}
	public void setPanCardNumber(String panCardNumber) {
		this.panCardNumber = panCardNumber;
	}

	public Integer getDocumentId() {
		return documentId;
	}
	public void setDocumentId(Integer value) {
		this.documentId = value;
	}


	public Integer getUnitId() {
		return unitId;
	}
	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

}

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.09.27 at 12:58:50 PM IST 
//


package com.stpl.tech.scm.domain.model;

import java.util.Date;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;


/**
 * <p>Java class for UnitVendorMapping complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="UnitVendorMapping"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="unitVendorMappingId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="unit" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="vendor" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="mappingStatus" type="{http://www.w3schools.com}SwitchStatus"/&gt;
 *         &lt;element name="fulFillmentType" type="{http://www.w3schools.com}FulfillmentType"/&gt;
 *         &lt;element name="smsNotification" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="emailNotification" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="noOfDays" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="notificationTime" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="creationTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="lastUpdateTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="createdBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="lastUpdatedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "UnitVendorMapping", propOrder = {
    "unitVendorMappingId",
    "unit",
    "vendor",
    "mappingStatus",
    "fulFillmentType",
    "smsNotification",
    "emailNotification",
    "noOfDays",
    "notificationTime",
    "creationTime",
    "lastUpdateTime",
    "createdBy",
    "lastUpdatedBy"
})
public class UnitVendorMapping {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer unitVendorMappingId;
    @XmlElement(required = true)
    protected IdCodeName unit;
    @XmlElement(required = true)
    protected IdCodeName vendor;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SwitchStatus mappingStatus;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected FulfillmentType fulFillmentType;
    @XmlElement(required = true, type = Boolean.class, nillable = true)
    protected Boolean smsNotification;
    @XmlElement(required = true, type = Boolean.class, nillable = true)
    protected Boolean emailNotification;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer noOfDays;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer fulfillmentLeadDays;
    @XmlElement(required = true)
    protected String notificationTime;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date creationTime;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date lastUpdateTime;
    @XmlElement(required = true)
    protected IdCodeName createdBy;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName lastUpdatedBy;
    @XmlElement(required = true)
    protected Integer dispatchLocationId;
    protected String deliveryPromiseTime;

    /**
     * Gets the value of the unitVendorMappingId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getUnitVendorMappingId() {
        return unitVendorMappingId;
    }

    /**
     * Sets the value of the unitVendorMappingId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setUnitVendorMappingId(Integer value) {
        this.unitVendorMappingId = value;
    }

    /**
     * Gets the value of the unit property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getUnit() {
        return unit;
    }

    /**
     * Sets the value of the unit property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setUnit(IdCodeName value) {
        this.unit = value;
    }

    /**
     * Gets the value of the vendor property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getVendor() {
        return vendor;
    }

    /**
     * Sets the value of the vendor property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setVendor(IdCodeName value) {
        this.vendor = value;
    }

    /**
     * Gets the value of the mappingStatus property.
     * 
     * @return
     *     possible object is
     *     {@link SwitchStatus }
     *     
     */
    public SwitchStatus getMappingStatus() {
        return mappingStatus;
    }

    /**
     * Sets the value of the mappingStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link SwitchStatus }
     *     
     */
    public void setMappingStatus(SwitchStatus value) {
        this.mappingStatus = value;
    }

    /**
     * Gets the value of the fulFillmentType property.
     * 
     * @return
     *     possible object is
     *     {@link FulfillmentType }
     *     
     */
    public FulfillmentType getFulFillmentType() {
        return fulFillmentType;
    }

    /**
     * Sets the value of the fulFillmentType property.
     * 
     * @param value
     *     allowed object is
     *     {@link FulfillmentType }
     *     
     */
    public void setFulFillmentType(FulfillmentType value) {
        this.fulFillmentType = value;
    }

    /**
     * Gets the value of the smsNotification property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isSmsNotification() {
        return smsNotification;
    }

    /**
     * Sets the value of the smsNotification property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setSmsNotification(Boolean value) {
        this.smsNotification = value;
    }

    /**
     * Gets the value of the emailNotification property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isEmailNotification() {
        return emailNotification;
    }

    /**
     * Sets the value of the emailNotification property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setEmailNotification(Boolean value) {
        this.emailNotification = value;
    }

    /**
     * Gets the value of the noOfDays property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getNoOfDays() {
        return noOfDays;
    }

    /**
     * Sets the value of the noOfDays property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setNoOfDays(Integer value) {
        this.noOfDays = value;
    }

    /**
     * Gets the value of the notificationTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNotificationTime() {
        return notificationTime;
    }

    /**
     * Sets the value of the notificationTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNotificationTime(String value) {
        this.notificationTime = value;
    }

    /**
     * Gets the value of the creationTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getCreationTime() {
        return creationTime;
    }

    /**
     * Sets the value of the creationTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCreationTime(Date value) {
        this.creationTime = value;
    }

    /**
     * Gets the value of the lastUpdateTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * Sets the value of the lastUpdateTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLastUpdateTime(Date value) {
        this.lastUpdateTime = value;
    }

    /**
     * Gets the value of the createdBy property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getCreatedBy() {
        return createdBy;
    }

    /**
     * Sets the value of the createdBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setCreatedBy(IdCodeName value) {
        this.createdBy = value;
    }

    /**
     * Gets the value of the lastUpdatedBy property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    /**
     * Sets the value of the lastUpdatedBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setLastUpdatedBy(IdCodeName value) {
        this.lastUpdatedBy = value;
    }

	public Integer getFulfillmentLeadDays() {
		return fulfillmentLeadDays;
	}

	public void setFulfillmentLeadDays(Integer fulfillmentLeadDays) {
		this.fulfillmentLeadDays = fulfillmentLeadDays;
	}

	public Integer getDispatchLocationId() {
		return dispatchLocationId;
	}

	public void setDispatchLocationId(Integer dispatchLocationId) {
		this.dispatchLocationId = dispatchLocationId;
	}

    public String getDeliveryPromiseTime() {
        return deliveryPromiseTime;
    }

    public void setDeliveryPromiseTime(String deliveryPromiseTime) {
        this.deliveryPromiseTime = deliveryPromiseTime;
    }
}

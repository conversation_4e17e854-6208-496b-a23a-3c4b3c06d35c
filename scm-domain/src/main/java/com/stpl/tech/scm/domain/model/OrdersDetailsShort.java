package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.Date;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OrdersDetailsShort", propOrder = {
    "id",
    "generationTime",
    "lastUpdateTime",
    "requestUnit",
    "generatedBy",
    "lastUpdatedBy",
    "fulfillmentUnit",
    "fulfillmentDate",
    "specialOrder",
    "transferType",
    "status",
    "assetOrder",
    "GNTFlag"
})
public class OrdersDetailsShort {


    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer id;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date generationTime;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date lastUpdateTime;
    @XmlElement(required = true)
    protected IdCodeName requestUnit;
    @XmlElement(required = true)
    protected IdCodeName generatedBy;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName lastUpdatedBy;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName fulfillmentUnit;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date fulfillmentDate;
    @XmlSchemaType(name = "string")
    protected SCMOrderStatus status;
    @XmlElement(required = true, type = Boolean.class, nillable = true)
    protected Boolean assetOrder;
    @XmlElement(required = true, type = Boolean.class, nillable = true)
    protected Boolean specialOrder;
    protected OrderTransferType transferType;
    protected Boolean GNTFlag;
    protected String raisedBy;
    private Boolean requestOrderType;
    private String type;

    public Boolean getGNTFlag() {
        return GNTFlag;
    }

    public void setGNTFlag(Boolean GNTFlag) {
        this.GNTFlag = GNTFlag;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Date getGenerationTime() {
        return generationTime;
    }

    public void setGenerationTime(Date generationTime) {
        this.generationTime = generationTime;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public IdCodeName getRequestUnit() {
        return requestUnit;
    }

    public void setRequestUnit(IdCodeName requestUnit) {
        this.requestUnit = requestUnit;
    }

    public IdCodeName getGeneratedBy() {
        return generatedBy;
    }

    public void setGeneratedBy(IdCodeName generatedBy) {
        this.generatedBy = generatedBy;
    }

    public IdCodeName getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(IdCodeName lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public IdCodeName getFulfillmentUnit() {
        return fulfillmentUnit;
    }

    public void setFulfillmentUnit(IdCodeName fulfillmentUnit) {
        this.fulfillmentUnit = fulfillmentUnit;
    }

    public Date getFulfillmentDate() {
        return fulfillmentDate;
    }

    public void setFulfillmentDate(Date fulfillmentDate) {
        this.fulfillmentDate = fulfillmentDate;
    }

    public Boolean getSpecialOrder() {
        return specialOrder;
    }

    public void setSpecialOrder(Boolean specialOrder) {
        this.specialOrder = specialOrder;
    }

    public OrderTransferType getTransferType() {
        return transferType;
    }

    public void setTransferType(OrderTransferType transferType) {
        this.transferType = transferType;
    }

    public SCMOrderStatus getStatus() {
        return status;
    }

    public void setStatus(SCMOrderStatus status) {
        this.status = status;
    }

    public Boolean getAssetOrder() {
        return assetOrder;
    }

    public void setAssetOrder(Boolean assetOrder) {
        this.assetOrder = assetOrder;
    }

    public String getRaisedBy() {
        return raisedBy;
    }

    public void setRaisedBy(String raisedBy) {
        this.raisedBy = raisedBy;
    }

    public Boolean getRequestOrderType() {
        return requestOrderType;
    }

    public void setRequestOrderType(Boolean requestOrderType) {
        this.requestOrderType = requestOrderType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}




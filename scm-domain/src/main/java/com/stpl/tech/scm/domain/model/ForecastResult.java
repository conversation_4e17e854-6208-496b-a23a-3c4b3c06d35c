package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ForecastResult {
    
    private Double predictedQuantity;
    private MeanResult meanResult;
    private ProductSaleClusterEnum productSaleCluster;
    private Integer productId;
    private String dimension;
    private String productName;
    private String productDimensionKey;
    private Boolean isEditable;
}
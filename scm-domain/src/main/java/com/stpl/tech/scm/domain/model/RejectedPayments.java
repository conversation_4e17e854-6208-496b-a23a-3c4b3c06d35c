package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.*;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.Date;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "rejectedPaymentRequest", propOrder = {
        "grId",
        "companyId",
        "prId",
        "status",
        "updateTime",
        "receiptType"

})
public class RejectedPayments {
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer grId;
    @XmlElement(required = true)
    protected Integer prId;
    @XmlElement(required = true)
    protected Integer companyId;
    @XmlElement(required = true)
    protected String status;
    @XmlElement(required = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date updateTime;
    @XmlSchemaType(name = "string")
    protected InvoiceDocType receiptType;

    public Integer getGrId() {
        return grId;
    }

    public void setGrId(Integer grId) {
        this.grId = grId;
    }

    public Integer getPrId() {
        return prId;
    }

    public void setPrId(Integer prId) {
        this.prId = prId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public InvoiceDocType getReceiptType() {
        return receiptType;
    }

    public void setReceiptType(InvoiceDocType receiptType) {
        this.receiptType = receiptType;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }
}


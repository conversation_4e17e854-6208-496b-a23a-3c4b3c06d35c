package com.stpl.tech.scm.domain.model;

public class BusinessCost<PERSON>enter {

	protected Integer id;
	protected String name;
	protected String code;
	protected String type;
	protected IdCodeName location;
	protected IdCodeName company;
	protected String status;
	private IdCodeName state;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public IdCodeName getLocation() {
		return location;
	}

	public void setLocation(IdCodeName location) {
		this.location = location;
	}

	public IdCodeName getCompany() {
		return company;
	}

	public void setCompany(IdCodeName company) {
		this.company = company;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public void setState(IdCodeName state) {
		this.state = state;
	}

	public IdCodeName getState() {
		return state;
	}
}

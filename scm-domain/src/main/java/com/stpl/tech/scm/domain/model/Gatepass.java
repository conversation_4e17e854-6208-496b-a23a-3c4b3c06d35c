package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stpl.tech.scm.domain.vo.ConsumptionVO;
import com.stpl.tech.scm.domain.vo.InventoryItemVO;
import com.stpl.tech.scm.domain.vo.ReceivingVO;

public class Gatepass extends AbstractInventoryVO implements ConsumptionVO, ReceivingVO {
	private Integer id;
	private IdCodeName vendor;
	private GatepassOperationType operationType;
	private Boolean returnable;
	private Integer expectedReturn;
	private GatepassStatus status;
	private GatepassReturnStatus returnStatus;
	private BigDecimal totalCost;
	private BigDecimal totalTax;
	private BigDecimal additionalCharges;
	private String comment;
	private String reason;
	private IdCodeName createdBy;
	private Date createdAt;
	private IdCodeName sendingUnit;
	private IdCodeName sendingCompany;
	private String needsApproval;
	private Date issueDate;
	private Boolean hasLoss;
	private IdCodeName dispatchLocation;
	private IdCodeName cancelledBy;
	private Date cancelledAt;
	private List<GatepassItem> itemDatas = new ArrayList<GatepassItem>();
	private boolean assetGatePass;
    private IdCodeName approvalRequestedTo;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public IdCodeName getVendor() {
		return vendor;
	}

	public void setVendor(IdCodeName vendor) {
		this.vendor = vendor;
	}

	public GatepassOperationType getOperationType() {
		return operationType;
	}

	public void setOperationType(GatepassOperationType operationType) {
		this.operationType = operationType;
	}

	public Boolean getReturnable() {
		return returnable;
	}

	public void setReturnable(Boolean returnable) {
		this.returnable = returnable;
	}

	public Integer getExpectedReturn() {
		return expectedReturn;
	}

	public void setExpectedReturn(Integer expectedReturn) {
		this.expectedReturn = expectedReturn;
	}

	public GatepassStatus getStatus() {
		return status;
	}

	public void setStatus(GatepassStatus status) {
		this.status = status;
	}

	public GatepassReturnStatus getReturnStatus() {
		return returnStatus;
	}

	public void setReturnStatus(GatepassReturnStatus returnStatus) {
		this.returnStatus = returnStatus;
	}

	public BigDecimal getTotalCost() {
		return totalCost;
	}

	public void setTotalCost(BigDecimal totalCost) {
		this.totalCost = totalCost;
	}

	public BigDecimal getTotalTax() {
		return totalTax;
	}

	public void setTotalTax(BigDecimal totalTax) {
		this.totalTax = totalTax;
	}

	public BigDecimal getAdditionalCharges() {
		return additionalCharges;
	}

	public void setAdditionalCharges(BigDecimal additionalCharges) {
		this.additionalCharges = additionalCharges;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public IdCodeName getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(IdCodeName createdBy) {
		this.createdBy = createdBy;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public IdCodeName getSendingUnit() {
		return sendingUnit;
	}

	public void setSendingUnit(IdCodeName sendingUnit) {
		this.sendingUnit = sendingUnit;
	}

	public IdCodeName getSendingCompany() {
		return sendingCompany;
	}

	public void setSendingCompany(IdCodeName sendingCompany) {
		this.sendingCompany = sendingCompany;
	}

	public String getNeedsApproval() {
		return needsApproval;
	}

	public void setNeedsApproval(String needsApproval) {
		this.needsApproval = needsApproval;
	}

	public Date getIssueDate() {
		return issueDate;
	}

	public void setIssueDate(Date issueDate) {
		this.issueDate = issueDate;
	}

	public Boolean getHasLoss() {
		return hasLoss;
	}

	public void setHasLoss(Boolean hasLoss) {
		this.hasLoss = hasLoss;
	}

	public List<GatepassItem> getItemDatas() {
		return itemDatas;
	}

	public void setItemDatas(List<GatepassItem> itemDatas) {
		this.itemDatas = itemDatas;
	}

	public IdCodeName getDispatchLocation() {
		return dispatchLocation;
	}

	public void setDispatchLocation(IdCodeName dispatchLocation) {
		this.dispatchLocation = dispatchLocation;
	}

	@Override
	@JsonIgnore
	public int getKeyId() {
		return id;
	}

	@Override
	@JsonIgnore
	public StockEventType getKeyType() {
		if (this.itemDatas.size() > 0) {
			switch (this.itemDatas.get(0).getTransType()) {
			case RETURN:
				return StockEventType.RECEIVED;
			case TRANSFER:
				return StockEventType.TRANSFER_OUT;
			case LOST:
				return StockEventType.WASTAGE;
			}
			return StockEventType.TRANSFER_OUT;
		}
		return null;
	}

	@Override
	@JsonIgnore
	public int getUnitId() {
		return sendingUnit.getId();
	}

	@Override
	public PriceUpdateEntryType getInventoryType() {
		return PriceUpdateEntryType.SKU;
	}

	@Override
	@JsonIgnore
	public List<InventoryItemVO> getInventoryItems() {
		return new ArrayList<>(this.itemDatas);
	}

	public IdCodeName getCancelledBy() {
		return cancelledBy;
	}

	public void setCancelledBy(IdCodeName cancelledBy) {
		this.cancelledBy = cancelledBy;
	}

	public Date getCancelledAt() {
		return cancelledAt;
	}

	public void setCancelledAt(Date cancelledAt) {
		this.cancelledAt = cancelledAt;
	}

	public boolean getAssetGatePass() {
		return assetGatePass;
	}

	public void setAssetGatePass(boolean assetGatePass) {
		this.assetGatePass = assetGatePass;
	}
	
	@Override
	public boolean isAssetOrder() {
		return assetGatePass;
	}

    public IdCodeName getApprovalRequestedTo() {
        return approvalRequestedTo;
    }
    public void setApprovalRequestedTo(IdCodeName approvalRequestedTo) {
        this.approvalRequestedTo = approvalRequestedTo;
    }

}

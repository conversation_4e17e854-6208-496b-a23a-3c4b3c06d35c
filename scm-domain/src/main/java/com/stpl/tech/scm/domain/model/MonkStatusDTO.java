package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MonkStatusDTO {
    
    private String name;
    private String displayName;
    
    public static MonkStatusDTO fromMonkStatus(MonkStatusDayClose.MonkStatus status) {
        return MonkStatusDTO.builder()
                .name(status.name())
                .displayName(status.getDisplayName())
                .build();
    }
}

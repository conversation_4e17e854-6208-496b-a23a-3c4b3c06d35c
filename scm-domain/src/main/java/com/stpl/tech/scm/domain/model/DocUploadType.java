//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2017.04.20 at 07:12:44 PM IST
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for DocUploadType.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="DocUploadType"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="COMPANY"/&gt;
 *     &lt;enumeration value="PURCHASE_ORDER"/&gt;
 *     &lt;enumeration value="ACCOUNT"/&gt;
 *     &lt;enumeration value="GR"/&gt;
 *     &lt;enumeration value="PAYMENT_REQUEST_INVOICE"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 *
 */
@XmlType(name = "DocUploadType")
@XmlEnum
public enum DocUploadType {

    COMPANY,
    PURCHASE_ORDER,
    ACCOUNT,
    GR,
    PAYMENT_REQUEST_INVOICE,
    GSTIN,
    SERVICE_ORDER,
    SERVICE_RECEIVED_PROOF,
    CANCEL_INVOICE,
    VENDOR_TDS,
    INVOICE_BARCODE,
    PROOF_OF_DELIVERY,
    DEBIT_NOTE,
    MANDATORY_UPLOADED_DOCUMENTS,
    PROOF_OF_REJECTION,
    PR_QUERY,
    APPROVAL_OF_HOD,
    ADVANCE_REFUND,
    CARD_PAYMENT_PROOF,
    VENDOR_INVOICE,
    VENDOR_CONTRACT,
    SO_BREACH_APPROVAL,
    PRODUCT_IMAGE;

    public String value() {
        return name();
    }

    public static DocUploadType fromValue(String v) {
        return valueOf(v);
    }

}

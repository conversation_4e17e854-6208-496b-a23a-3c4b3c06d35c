package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CorrectedSalesInvoiceDetails  {

    private Integer id;
    private Integer invoiceId;
    private String invoiceStatus;
    private String type;
    private String docId;
    private Integer creditNoteDocId;
    private String creditNoteDocUrl;
    private String generatedCreditNoteId;
    private Integer debitNoteDocId;
    private String debitNoteDocUrl;
    private String generatedDebitNoteId;
    private String irnNo;
    private String uploadedAckNo;
    private String uploadedEwayNo;
    private String signedQrCode;
    private Integer barcodeId;
    private List<CorrectedSalesInvoiceItemDetails> salesPerformaCorrectedItems;
}

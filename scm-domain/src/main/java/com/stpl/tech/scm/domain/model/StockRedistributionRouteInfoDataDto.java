package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class StockRedistributionRouteInfoDataDto implements Serializable {
    private Integer stockRedistributionRouteInfoDataId;
    private String routeName;
    private String routeStatus;
    private Date creationTime;
    private Date lastUpdateTime;
    private List<StockRedistributionRouteUnitsDataDto> stockRedistributionRouteUnitsDataList;
}

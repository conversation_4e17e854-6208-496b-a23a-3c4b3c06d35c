/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.12 at 03:52:20 PM IST 
//


package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.apache.commons.math3.util.Pair;

import javax.xml.bind.annotation.*;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "StockEventDefinition", propOrder = {
        "eventId",
        "parentId",
        "unitId",
        "unitType",
        "eventType",
        "eventStatus",
        "initiatedBy",
        "eventCreationDate",
        "auditedBy",
        "subType",
        "subCategory",
        "availableAssets"
})
@NoArgsConstructor
public class StockEventDefinition {

    public StockEventDefinition(Integer unitId, String unitType, String eventType, StockEventStatusType eventStatus, IdCodeName initiatedBy, IdCodeName auditedBy,
                              String subType, String isSplit) {
        this.eventId = null;
        this.unitId = unitId;
        this.unitType = unitType;
        this.eventType = eventType;
        this.eventStatus = eventStatus;
        this.initiatedBy = initiatedBy;
        this.eventCreationDate = null;
        this.auditedBy = auditedBy;
        this.subType = subType;
        this.isSplit = isSplit;
    }
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer eventId;
    /*
        UnitId signifies Location of Asset
     */
    @XmlElement(required = false)
    protected Integer parentId;

    @XmlElement(required = true)
    protected Integer unitId;

    @XmlElement(required = true)
    protected String unitType;

    @XmlElement(required = true)
    protected String eventType;

    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected StockEventStatusType eventStatus;

    @XmlElement(required = true)
    protected IdCodeName initiatedBy;

    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date eventCreationDate;



    @XmlElement(required = false)
    protected IdCodeName auditedBy;

   @XmlElement(required = true)
   protected String BudgetType ;

   @XmlElement(required = true)
   protected  Integer ReceivingUnitID ;

   @XmlElement(required = false)
   protected String subType;

   @XmlElement(required = false)
   protected String subCategory;



    @XmlElement(required = false)
    protected List<StockEventAssetMappingDefinition> availableAssets;

    @XmlElement(required = false)
    protected List<StockEventAssetMappingDefinition> scannedAssets;

    @XmlElement(required = false)
    protected Map<Integer, org.apache.commons.math3.util.Pair<BigDecimal,BigDecimal>> productRequestQtyMap;

    protected List<StockEventAssetMappingDefinition> extraScannedItems;

    @XmlElement(required = false)
    protected Integer roId;

    @XmlElement(required = false)
    protected Integer toId;

    @XmlElement(required = false)
    protected String isSplit;

    @XmlElement(required = false)
    protected String deviceInfo;

    @XmlElement(required = false)
    protected String appVersion;

    protected Map<String, String> stockTakeStatusSubCategoryMap;



    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getUnitType() {
        return unitType;
    }

    public void setUnitType(String unitType) {
        this.unitType = unitType;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public StockEventStatusType getEventStatus() {
        return eventStatus;
    }

    public void setEventStatus(StockEventStatusType eventStatus) {
        this.eventStatus = eventStatus;
    }

    public IdCodeName getInitiatedBy() {
        return initiatedBy;
    }

    public void setInitiatedBy(IdCodeName initiatedBy) {
        this.initiatedBy = initiatedBy;
    }

    public Date getEventCreationDate() {
        return eventCreationDate;
    }

    public void setEventCreationDate(Date eventCreationDate) {
        this.eventCreationDate = eventCreationDate;
    }

    public IdCodeName getAuditedBy() {
        return auditedBy;
    }

    public void setAuditedBy(IdCodeName auditedBy) {
        this.auditedBy = auditedBy;
    }





    public List<StockEventAssetMappingDefinition> getAvailableAssets() {
        return availableAssets;
    }

    public void setAvailableAssets(List<StockEventAssetMappingDefinition> availableAssets) {
        this.availableAssets = availableAssets;
    }

    public String getSubType() {
        return subType;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    public String getBudgetType() {
        return BudgetType;
    }


    public void setBudgetType(String budgetType) {
        BudgetType = budgetType;
    }

    public Integer getReceivingUnitID() {
        return ReceivingUnitID;
    }

    public void setReceivingUnitID(Integer receivingUnitID) {
        ReceivingUnitID = receivingUnitID;
    }

    public Map<Integer, org.apache.commons.math3.util.Pair<BigDecimal, BigDecimal>> getProductRequestQtyMap() {
        return productRequestQtyMap;
    }

    public void setProductRequestQtyMap(Map<Integer, Pair<BigDecimal, BigDecimal>> productRequestQtyMap) {
        this.productRequestQtyMap = productRequestQtyMap;
    }

    public List<StockEventAssetMappingDefinition> getScannedAssets() {
        return scannedAssets;
    }

    public void setScannedAssets(List<StockEventAssetMappingDefinition> scannedAssets) {
        this.scannedAssets = scannedAssets;
    }

    public Integer getRoId() {
        return roId;
    }

    public void setRoId(Integer roId) {
        this.roId = roId;
    }

    public Integer getToId() {
        return toId;
    }

    public void setToId(Integer toId) {
        this.toId = toId;
    }

    public List<StockEventAssetMappingDefinition> getExtraScannedItems() {
        return extraScannedItems;
    }

    public void setExtraScannedItems(List<StockEventAssetMappingDefinition> extraScannedItems) {
        this.extraScannedItems = extraScannedItems;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public String getSubCategory() {
        return subCategory;
    }

    public void setSubCategory(String subCategory) {
        this.subCategory = subCategory;
    }

    public String getIsSplit() {
        return isSplit;
    }

    public void setIsSplit(String isSplit) {
        this.isSplit = isSplit;
    }

    public Map<String, String> getStockTakeStatusSubCategoryMap() {
        return stockTakeStatusSubCategoryMap;
    }

    public void setStockTakeStatusSubCategoryMap(Map<String, String> stockTakeStatusSubCategoryMap) {
        this.stockTakeStatusSubCategoryMap = stockTakeStatusSubCategoryMap;
    }

    public String getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(String deviceInfo) {
        this.deviceInfo = deviceInfo;
    }
}

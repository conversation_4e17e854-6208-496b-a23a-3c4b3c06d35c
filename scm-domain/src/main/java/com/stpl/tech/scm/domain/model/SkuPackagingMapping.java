//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.12 at 01:35:23 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for SkuPackagingMapping complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="SkuPackagingMapping"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="skuPackagingMappingId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="packagingId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="skuId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="mappingStatus" type="{http://www.w3schools.com}SwitchStatus"/&gt;
 *         &lt;element name="isDefault" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SkuPackagingMapping", propOrder = {
    "skuPackagingMappingId",
    "packagingId",
    "skuId",
    "mappingStatus",
    "isDefault"
})
public class SkuPackagingMapping {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer skuPackagingMappingId;
    protected int packagingId;
    protected int skuId;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SwitchStatus mappingStatus;
    protected boolean isDefault;

    /**
     * Gets the value of the skuPackagingMappingId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getSkuPackagingMappingId() {
        return skuPackagingMappingId;
    }

    /**
     * Sets the value of the skuPackagingMappingId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setSkuPackagingMappingId(Integer value) {
        this.skuPackagingMappingId = value;
    }

    /**
     * Gets the value of the packagingId property.
     * 
     */
    public int getPackagingId() {
        return packagingId;
    }

    /**
     * Sets the value of the packagingId property.
     * 
     */
    public void setPackagingId(int value) {
        this.packagingId = value;
    }

    /**
     * Gets the value of the skuId property.
     * 
     */
    public int getSkuId() {
        return skuId;
    }

    /**
     * Sets the value of the skuId property.
     * 
     */
    public void setSkuId(int value) {
        this.skuId = value;
    }

    /**
     * Gets the value of the mappingStatus property.
     * 
     * @return
     *     possible object is
     *     {@link SwitchStatus }
     *     
     */
    public SwitchStatus getMappingStatus() {
        return mappingStatus;
    }

    /**
     * Sets the value of the mappingStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link SwitchStatus }
     *     
     */
    public void setMappingStatus(SwitchStatus value) {
        this.mappingStatus = value;
    }

    /**
     * Gets the value of the isDefault property.
     *
     */
    public boolean isIsDefault() {
        return isDefault;
    }

    /**
     * Sets the value of the isDefault property.
     *
     */
    public void setIsDefault(boolean value) {
        this.isDefault = value;
    }

}

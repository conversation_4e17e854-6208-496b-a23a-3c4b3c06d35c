package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RiderRoutePlanItemDataDto implements Serializable {
    private Integer riderRoutePlanItemDataId;
    private Integer riderRoutePlanStepDataRiderRoutePlanStepDataId;
    private RiderActionEnum riderAction;
    private Integer productId;
    private String productName;
    private Integer packagingId;
    private String packagingName;
    private Date expiryDate;
    private BigDecimal proposedQuantity;
    private BigDecimal finalQuantity;
    private StockType stockType;
    private String comment;
}

package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "unitSkuMappingDetail", propOrder = {
        "unitSkuMappingId",
        "unitId",
        "skuId",
        "mappingStatus",
        "profile"
})

public class unitSkuMappingDetail {
    @XmlElement(required = true, type=Integer.class, nillable = true)
    protected Integer unitSkuMappingId;

    @XmlElement(required = true)
    protected Integer skuId;

    @XmlElement(required = true)
    protected Integer unitId;

    @XmlElement(required = true)
    protected String mappingStatus;

    @XmlElement(required = true)
    protected String profile;

    public Integer getUnitSkuMappingId() {
        return unitSkuMappingId;
    }

    public void setUnitSkuMappingId(Integer unitSkuMappingId) {
        this.unitSkuMappingId = unitSkuMappingId;
    }

    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getMappingStatus() {
        return mappingStatus;
    }

    public void setMappingStatus(String mappingStatus) {
        this.mappingStatus = mappingStatus;
    }

    public String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        this.profile = profile;
    }
}

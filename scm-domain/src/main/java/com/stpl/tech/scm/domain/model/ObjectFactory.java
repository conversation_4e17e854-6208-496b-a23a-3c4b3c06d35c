//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.09.22 at 11:50:37 AM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.stpl.tech.scm.domain.model package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.stpl.tech.scm.domain.model
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link SCMMetadata }
     * 
     */
    public SCMMetadata createSCMMetadata() {
        return new SCMMetadata();
    }

    /**
     * Create an instance of {@link CategoryDefinition }
     * 
     */
    public CategoryDefinition createCategoryDefinition() {
        return new CategoryDefinition();
    }

    /**
     * Create an instance of {@link AttributeDefinition }
     * 
     */
    public AttributeDefinition createAttributeDefinition() {
        return new AttributeDefinition();
    }

    /**
     * Create an instance of {@link AttributeValue }
     * 
     */
    public AttributeValue createAttributeValue() {
        return new AttributeValue();
    }

    /**
     * Create an instance of {@link SubCategoryDefinition }
     * 
     */
    public SubCategoryDefinition createSubCategoryDefinition() {
        return new SubCategoryDefinition();
    }

    /**
     * Create an instance of {@link CategoryAttributeMapping }
     * 
     */
    public CategoryAttributeMapping createCategoryAttributeMapping() {
        return new CategoryAttributeMapping();
    }

    /**
     * Create an instance of {@link CategoryAttributeValue }
     * 
     */
    public CategoryAttributeValue createCategoryAttributeValue() {
        return new CategoryAttributeValue();
    }

    /**
     * Create an instance of {@link PackagingDefinition }
     * 
     */
    public PackagingDefinition createPackagingDefinition() {
        return new PackagingDefinition();
    }

    /**
     * Create an instance of {@link ProductDefinition }
     * 
     */
    public ProductDefinition createProductDefinition() {
        return new ProductDefinition();
    }

    public ProfileDefinition createProfileDefinition() { return new ProfileDefinition(); }

    public ProfileAttributeMapping createProfileAttributeMapping () { return new ProfileAttributeMapping();}

    public EntityAttributeValueMapping createEntityAttributeValueMapping() { return new EntityAttributeValueMapping(); }

    public AssetDefinition createAssetDefinition () { return new AssetDefinition(); }

    public StockEventDefinition createStockEventDefinition () { return new StockEventDefinition(); }

    public StockEventAssetMappingDefinition createStockEventAssetMappingDefinition() { return new StockEventAssetMappingDefinition(); }

    /**
     * Create an instance of {@link ProductFulfillmentType }
     * 
     */
    public ProductFulfillmentType createProductFulfillmentType() {
        return new ProductFulfillmentType();
    }

    /**
     * Create an instance of {@link ProductPackagingMapping }
     * 
     */
    public ProductPackagingMapping createProductPackagingMapping() {
        return new ProductPackagingMapping();
    }

    /**
     * Create an instance of {@link SkuAttributeValue }
     * 
     */
    public SkuAttributeValue createSkuAttributeValue() {
        return new SkuAttributeValue();
    }
    
    /**
     * Create an instance of {@link SkuAttributeValue }
     * 
     */
    public SkuPrice createSkuPrice() {
        return new SkuPrice();
    }
    
    /**
     * Create an instance of {@link SkuAttributeValue }
     * 
     */
    public SkuPriceDetail createSkuPriceDetail() {
        return new SkuPriceDetail();
    }

    public VendorContractItemVO createVendorContractItemVO() {
        return new VendorContractItemVO();
    }

    /**
     * Create an instance of {@link SkuDefinition }
     * 
     */
    public SkuDefinition createSkuDefinition() {
        return new SkuDefinition();
    }

    /**
     * Create an instance of {@link SkuPackagingMapping }
     * 
     */
    public SkuPackagingMapping createSkuPackagingMapping() {
        return new SkuPackagingMapping();
    }

    /**
     * Create an instance of {@link UnitDetail }
     * 
     */
    public UnitDetail createUnitDetail() {
        return new UnitDetail();
    }

    /**
     * Create an instance of {@link SCMUnitCategory }
     * 
     */
    public SCMUnitCategory createSCMUnitCategory() {
        return new SCMUnitCategory();
    }

    /**
     * Create an instance of {@link UnitProductMapping }
     * 
     */
    public UnitProductMapping createUnitProductMapping() {
        return new UnitProductMapping();
    }

    /**
     * Create an instance of {@link ReferenceOrder }
     * 
     */
    public ReferenceOrder createReferenceOrder() {
        return new ReferenceOrder();
    }

    /**
     * Create an instance of {@link ReferenceOrderMenuItem }
     * 
     */
    public ReferenceOrderMenuItem createReferenceOrderMenuItem() {
        return new ReferenceOrderMenuItem();
    }

    /**
     * Create an instance of {@link ReferenceOrderMenuVariant }
     * 
     */
    public ReferenceOrderMenuVariant createReferenceOrderMenuVariant() {
        return new ReferenceOrderMenuVariant();
    }

    /**
     * Create an instance of {@link ReferenceOrderScmItem }
     * 
     */
    public ReferenceOrderScmItem createReferenceOrderScmItem() {
        return new ReferenceOrderScmItem();
    }

    /**
     * Create an instance of {@link RequestScmItem }
     * 
     */
    public RequestScmItem createRequestScmItem() {
        return new RequestScmItem();
    }

    /**
     * Create an instance of {@link RequestOrder }
     * 
     */
    public RequestOrder createRequestOrder() {
        return new RequestOrder();
    }

    /**
     * Create an instance of {@link RequestOrderItem }
     * 
     */
    public RequestOrderItem createRequestOrderItem() {
        return new RequestOrderItem();
    }

    /**
     * Create an instance of {@link PurchaseOrder }
     * 
     */
    public PurchaseOrder createPurchaseOrder() {
        return new PurchaseOrder();
    }

    /**
     * Create an instance of {@link PurchaseOrderItem }
     * 
     */
    public PurchaseOrderItem createPurchaseOrderItem() {
        return new PurchaseOrderItem();
    }

    /**
     * Create an instance of {@link TransferOrder }
     * 
     */
    public TransferOrder createTransferOrder() {
        return new TransferOrder();
    }

    /**
     * Create an instance of {@link TransferOrderItem }
     * 
     */
    public TransferOrderItem createTransferOrderItem() {
        return new TransferOrderItem();
    }

    /**
     * Create an instance of {@link SCMOrderPackaging }
     * 
     */
    public SCMOrderPackaging createSCMOrderPackaging() {
        return new SCMOrderPackaging();
    }

    /**
     * Create an instance of {@link GoodsReceived }
     * 
     */
    public GoodsReceived createGoodsReceived() {
        return new GoodsReceived();
    }

    /**
     * Create an instance of {@link GoodsReceivedItem }
     * 
     */
    public GoodsReceivedItem createGoodsReceivedItem() {
        return new GoodsReceivedItem();
    }

    /**
     * Create an instance of {@link IdCodeName }
     * 
     */
    public IdCodeName createIdCodeName() {
        return new IdCodeName();
    }
    

    /**
     * Create an instance of {@link IdCodeNameType }
     * 
     */
    public IdCodeNameType createIdCodeNameType() {
        return new IdCodeNameType();
    }
    
    /**
     * Create an instance of {@link IdCodeNameStatus }
     * 
     */
    public IdCodeNameStatus createIdCodeNameStatus() {
        return new IdCodeNameStatus();
    }


    /**
     * Create an instance of {@link WastageEvent }
     * 
     */
    public WastageEvent createWastageEvent() {
        return new WastageEvent();
    }

    /**
     * Create an instance of {@link StockInventoryData }
     * 
     */
    public StockInventoryData createStockInventoryResponse() {
        return new StockInventoryData();
    }

    /**
     * Create an instance of {@link ProductStockForUnit }
     * 
     */
    public ProductStockForUnit createProductStockForUnit() {
        return new ProductStockForUnit();
    }

    /**
     * Create an instance of {@link ProductBasicDetail }
     * 
     */
    public ProductBasicDetail createProductBasicDetail() {
        return new ProductBasicDetail();
    }

    /**
     * Create an instance of {@link VendorDetail }
     * 
     */
    public VendorDetail createVendorDetail() {
        return new VendorDetail();
    }

	/**
	 * @return
	 */
	public PackagingData createPackagingData() {
		return new PackagingData();
	}

	public GatepassItemAssetMapping getGatepassItemAssetMapping() { return new GatepassItemAssetMapping(); }

	public EntityAssetMapping getEntityAssetMapping() { return new EntityAssetMapping(); }

	public AssetRecoveryDefinition createAssetRecovery() { return new AssetRecoveryDefinition(); }

	public AssetDefinitionSlimObject createAssetDefinitionSlimObject() { return new AssetDefinitionSlimObject(); }

}

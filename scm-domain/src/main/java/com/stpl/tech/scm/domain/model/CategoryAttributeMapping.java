//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.12 at 01:35:23 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for CategoryAttributeMapping complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="CategoryAttributeMapping"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="categoryAttributeMappingId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="categoryDefinition" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="attributeDefinition" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="mandatory" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="mappingOrder" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="usedInNaming" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="mappingStatus" type="{http://www.w3schools.com}SwitchStatus"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CategoryAttributeMapping", propOrder = {
    "categoryAttributeMappingId",
    "categoryDefinition",
    "attributeDefinition",
    "mandatory",
    "mappingOrder",
    "usedInNaming",
    "mappingStatus"
})
public class CategoryAttributeMapping {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer categoryAttributeMappingId;
    @XmlElement(required = true)
    protected IdCodeName categoryDefinition;
    @XmlElement(required = true)
    protected IdCodeName attributeDefinition;
    protected boolean mandatory;
    protected int mappingOrder;
    protected boolean usedInNaming;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SwitchStatus mappingStatus;

    /**
     * Gets the value of the categoryAttributeMappingId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCategoryAttributeMappingId() {
        return categoryAttributeMappingId;
    }

    /**
     * Sets the value of the categoryAttributeMappingId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCategoryAttributeMappingId(Integer value) {
        this.categoryAttributeMappingId = value;
    }

    /**
     * Gets the value of the categoryDefinition property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     * 
     */
    public IdCodeName getCategoryDefinition() {
        return categoryDefinition;
    }

    /**
     * Sets the value of the categoryDefinition property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     * 
     */
    public void setCategoryDefinition(IdCodeName value) {
        this.categoryDefinition = value;
    }

    /**
     * Gets the value of the attributeDefinition property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     * 
     */
    public IdCodeName getAttributeDefinition() {
        return attributeDefinition;
    }

    /**
     * Sets the value of the attributeDefinition property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     * 
     */
    public void setAttributeDefinition(IdCodeName value) {
        this.attributeDefinition = value;
    }

    /**
     * Gets the value of the mandatory property.
     * 
     */
    public boolean isMandatory() {
        return mandatory;
    }

    /**
     * Sets the value of the mandatory property.
     * 
     */
    public void setMandatory(boolean value) {
        this.mandatory = value;
    }

    /**
     * Gets the value of the mappingOrder property.
     * 
     */
    public int getMappingOrder() {
        return mappingOrder;
    }

    /**
     * Sets the value of the mappingOrder property.
     * 
     */
    public void setMappingOrder(int value) {
        this.mappingOrder = value;
    }

    /**
     * Gets the value of the usedInNaming property.
     * 
     */
    public boolean isUsedInNaming() {
        return usedInNaming;
    }

    /**
     * Sets the value of the usedInNaming property.
     * 
     */
    public void setUsedInNaming(boolean value) {
        this.usedInNaming = value;
    }

    /**
     * Gets the value of the mappingStatus property.
     * 
     * @return
     *     possible object is
     *     {@link SwitchStatus }
     *     
     */
    public SwitchStatus getMappingStatus() {
        return mappingStatus;
    }

    /**
     * Sets the value of the mappingStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link SwitchStatus }
     *     
     */
    public void setMappingStatus(SwitchStatus value) {
        this.mappingStatus = value;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        CategoryAttributeMapping that = (CategoryAttributeMapping) o;

        if (categoryDefinition.getId() != that.categoryDefinition.getId()) return false;
        if (attributeDefinition.getId() != that.attributeDefinition.getId()) return false;
        if (mappingOrder != that.mappingOrder) return false;
        if (!categoryAttributeMappingId.equals(that.categoryAttributeMappingId)) return false;
        if (mandatory != that.mandatory) return false;
        if (usedInNaming != that.usedInNaming) return false;
        return mappingStatus == that.mappingStatus;

    }

    @Override
    public int hashCode() {
        int result = categoryAttributeMappingId.hashCode();
        result = 31 * result + categoryDefinition.getId();
        result = 31 * result + attributeDefinition.getId();
        result = 31 * result + mappingOrder;
        result = 31 * result + mappingStatus.hashCode();
        return result;
    }
}

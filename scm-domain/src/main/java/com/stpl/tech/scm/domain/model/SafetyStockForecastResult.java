package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SafetyStockForecastResult extends ForecastResult {
    
    private List<DemandForecastResult> demandForecastResults;
    private Double safetyStockQuantity;
    private Double meanDemand;
    private Double standardDeviation;
    private Double safetyStockMultiplier;
}

package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class FixedAssetCompactDefinition {
    protected Integer assetId;
    protected String assetName;
    protected String assetStatus;
    protected String assetTag;
    protected Integer skuId;
    protected Integer productId;
    protected String subCategory;
    protected Integer classificationId;
}

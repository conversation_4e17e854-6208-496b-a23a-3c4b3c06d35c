//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.08.14 at 04:47:32 PM IST 
//


package com.stpl.tech.scm.domain.model;

import com.stpl.tech.master.recipe.calculator.model.SubUnitOfMeasure;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.awt.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <p>Java class for PlanOrderItemPrep complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="PlanOrderItemPrep"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="recipeId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="requestedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="preparationQuantity" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="requestingTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="planOrderItem" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="planOrderItemPrepItems" type="{http://www.w3schools.com}PlanOrderItemPrepItem" maxOccurs="unbounded"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PlanOrderItemPrep", propOrder = {
    "id",
    "recipeId",
    "requestedBy",
    "preparationQuantity",
    "requestingTime",
    "planOrderItem",
    "planOrderItemPrepItems"
})
public class PlanOrderItemPrep {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer id;
    protected int recipeId;
    @XmlElement(required = true)
    protected IdCodeName requestedBy;
    @XmlElement(required = true)
    protected BigDecimal preparationQuantity;
    protected SubUnitOfMeasure productUom;
    protected BigDecimal productConversion;
    protected SubUnitOfMeasure outputUom;
    protected BigDecimal outputConversion;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date requestingTime;
    @XmlElement(required = true)
    protected IdCodeName planOrderItem;
    @XmlElement(required = true)
    protected List<PlanOrderItemPrepItem> planOrderItemPrepItems;
    protected String recipeNotes;
    protected List<String> imagesURL;

    /**
     * Gets the value of the id property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setId(Integer value) {
        this.id = value;
    }

    /**
     * Gets the value of the recipeId property.
     * 
     */
    public int getRecipeId() {
        return recipeId;
    }

    /**
     * Sets the value of the recipeId property.
     * 
     */
    public void setRecipeId(int value) {
        this.recipeId = value;
    }

    /**
     * Gets the value of the requestedBy property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getRequestedBy() {
        return requestedBy;
    }

    /**
     * Sets the value of the requestedBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setRequestedBy(IdCodeName value) {
        this.requestedBy = value;
    }

    /**
     * Gets the value of the preparationQuantity property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getPreparationQuantity() {
        return preparationQuantity;
    }

    /**
     * Sets the value of the preparationQuantity property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setPreparationQuantity(BigDecimal value) {
        this.preparationQuantity = value;
    }

    /**
     * Gets the value of the requestingTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getRequestingTime() {
        return requestingTime;
    }

    /**
     * Sets the value of the requestingTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRequestingTime(Date value) {
        this.requestingTime = value;
    }

    /**
     * Gets the value of the planOrderItem property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getPlanOrderItem() {
        return planOrderItem;
    }

    /**
     * Sets the value of the planOrderItem property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setPlanOrderItem(IdCodeName value) {
        this.planOrderItem = value;
    }

    /**
     * Gets the value of the planOrderItemPrepItem property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the planOrderItemPrepItem property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPlanOrderItemPrepItem().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PlanOrderItemPrepItem }
     * 
     * 
     */
    public List<PlanOrderItemPrepItem> getPlanOrderItemPrepItems() {
        if (planOrderItemPrepItems == null) {
            planOrderItemPrepItems = new ArrayList<PlanOrderItemPrepItem>();
        }
        return this.planOrderItemPrepItems;
    }

    public String getRecipeNotes() {
        return recipeNotes;
    }

    public List<String> getImagesURL() {
        return imagesURL;
    }

    public void setImagesURL(List<String> imagesURL) {
        this.imagesURL = imagesURL;
    }

    public void setRecipeNotes(String recipeNotes) {
        this.recipeNotes = recipeNotes;
    }


    public SubUnitOfMeasure getProductUom() {
        return productUom;
    }

    public void setProductUom(SubUnitOfMeasure productUom) {
        this.productUom = productUom;
    }

    public BigDecimal getProductConversion() {
        return productConversion;
    }

    public void setProductConversion(BigDecimal productConversion) {
        this.productConversion = productConversion;
    }

    public SubUnitOfMeasure getOutputUom() {
        return outputUom;
    }

    public void setOutputUom(SubUnitOfMeasure outputUom) {
        this.outputUom = outputUom;
    }

    public BigDecimal getOutputConversion() {
        return outputConversion;
    }

    public void setOutputConversion(BigDecimal outputConversion) {
        this.outputConversion = outputConversion;
    }

}

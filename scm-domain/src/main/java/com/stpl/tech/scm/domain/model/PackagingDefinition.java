//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.12 at 01:35:23 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for PackagingDefinition complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="PackagingDefinition"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="packagingId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="packagingType" type="{http://www.w3schools.com}PackagingType"/&gt;
 *         &lt;element name="packagingCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="packagingName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="packagingStatus" type="{http://www.w3schools.com}SwitchStatus"/&gt;
 *         &lt;element name="conversionRatio" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="unitOfMeasure" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="subPackagingId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PackagingDefinition", propOrder = {
    "packagingId",
    "packagingType",
    "packagingCode",
    "packagingName",
    "packagingStatus",
    "conversionRatio",
    "unitOfMeasure",
    "subPackagingId"
})
public class PackagingDefinition {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer packagingId;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected PackagingType packagingType;
    @XmlElement(required = true)
    protected String packagingCode;
    @XmlElement(required = true)
    protected String packagingName;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SwitchStatus packagingStatus;
    protected float conversionRatio;
    @XmlElement(required = true)
    protected String unitOfMeasure;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer subPackagingId;

    /**
     * Gets the value of the packagingId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getPackagingId() {
        return packagingId;
    }

    /**
     * Sets the value of the packagingId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setPackagingId(Integer value) {
        this.packagingId = value;
    }

    /**
     * Gets the value of the packagingType property.
     * 
     * @return
     *     possible object is
     *     {@link PackagingType }
     *     
     */
    public PackagingType getPackagingType() {
        return packagingType;
    }

    /**
     * Sets the value of the packagingType property.
     * 
     * @param value
     *     allowed object is
     *     {@link PackagingType }
     *     
     */
    public void setPackagingType(PackagingType value) {
        this.packagingType = value;
    }

    /**
     * Gets the value of the packagingCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPackagingCode() {
        return packagingCode;
    }

    /**
     * Sets the value of the packagingCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPackagingCode(String value) {
        this.packagingCode = value;
    }

    /**
     * Gets the value of the packagingName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPackagingName() {
        return packagingName;
    }

    /**
     * Sets the value of the packagingName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPackagingName(String value) {
        this.packagingName = value;
    }

    /**
     * Gets the value of the packagingStatus property.
     * 
     * @return
     *     possible object is
     *     {@link SwitchStatus }
     *     
     */
    public SwitchStatus getPackagingStatus() {
        return packagingStatus;
    }

    /**
     * Sets the value of the packagingStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link SwitchStatus }
     *     
     */
    public void setPackagingStatus(SwitchStatus value) {
        this.packagingStatus = value;
    }

    /**
     * Gets the value of the conversionRatio property.
     * 
     */
    public float getConversionRatio() {
        return conversionRatio;
    }

    /**
     * Sets the value of the conversionRatio property.
     * 
     */
    public void setConversionRatio(float value) {
        this.conversionRatio = value;
    }

    /**
     * Gets the value of the unitOfMeasure property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    /**
     * Sets the value of the unitOfMeasure property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUnitOfMeasure(String value) {
        this.unitOfMeasure = value;
    }

    /**
     * Gets the value of the subPackagingId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getSubPackagingId() {
        return subPackagingId;
    }

    /**
     * Sets the value of the subPackagingId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setSubPackagingId(Integer value) {
        this.subPackagingId = value;
    }

}

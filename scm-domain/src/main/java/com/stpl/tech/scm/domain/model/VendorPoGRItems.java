package com.stpl.tech.scm.domain.model;


import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.math.BigDecimal;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "VendorPOGrItems", propOrder = {
        "purchaseOrderId",
        "poItemReceivedQuantity",
        "poItemRequestedQuantity",
        "poItemPackagingQuantity",
        "grItemPackagingQuantity",
        "grItemReceivedQuantity",
        "skuName",
        "skuId",
        "mappingId",
        "grItemAcceptedQuantity",
        "grItemId",
        "poItemId",
        "updationreason",
        "description",
        "rejectedQty",
        "actualPackagingQty"
})
public class VendorPoGRItems {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer purchaseOrderId;

    @XmlElement(required = true, type = Float.class, nillable = true)
    protected BigDecimal poItemReceivedQuantity;

    protected BigDecimal poItemRequestedQuantity;

    protected BigDecimal poItemPackagingQuantity;

    protected BigDecimal grItemPackagingQuantity;

    @XmlElement(required = true, type = Float.class, nillable = true)
    protected BigDecimal grItemReceivedQuantity;

    @XmlElement(required = true)
    protected String skuName;

    protected int skuId;

    protected int mappingId;

    protected int grItemId;

    protected int poItemId;

    protected BigDecimal grItemAcceptedQuantity;

    protected BigDecimal conversionRatio;

    protected BigDecimal rejectedQty;

    protected String updationreason;

    protected String description;

    protected BigDecimal actualPackagingQty;


    public Integer getPurchaseOrderId() {
        return purchaseOrderId;
    }

    public void setPurchaseOrderId(Integer purchaseOrderId) {
        this.purchaseOrderId = purchaseOrderId;
    }

    public BigDecimal getPoItemReceivedQuantity() {
        return poItemReceivedQuantity;
    }

    public void setPoItemReceivedQuantity(BigDecimal poItemReceivedQuantity) {
        this.poItemReceivedQuantity = poItemReceivedQuantity;
    }

    public BigDecimal getPoItemRequestedQuantity() {
        return poItemRequestedQuantity;
    }

    public void setPoItemRequestedQuantity(BigDecimal poItemRequestedQuantity) {
        this.poItemRequestedQuantity = poItemRequestedQuantity;
    }

    public BigDecimal getPoItemPackagingQuantity() {
        return poItemPackagingQuantity;
    }

    public void setPoItemPackagingQuantity(BigDecimal poItemPackagingQuantity) {
        this.poItemPackagingQuantity = poItemPackagingQuantity;
    }

    public BigDecimal getGrItemPackagingQuantity() {
        return grItemPackagingQuantity;
    }

    public void setGrItemPackagingQuantity(BigDecimal grItemPackagingQuantity) {
        this.grItemPackagingQuantity = grItemPackagingQuantity;
    }

    public BigDecimal getGrItemReceivedQuantity() {
        return grItemReceivedQuantity;
    }

    public void setGrItemReceivedQuantity(BigDecimal grItemReceivedQuantity) {
        this.grItemReceivedQuantity = grItemReceivedQuantity;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public int getSkuId() {
        return skuId;
    }

    public void setSkuId(int skuId) {
        this.skuId = skuId;
    }

    public int getMappingId() {
        return mappingId;
    }

    public void setMappingId(int mappingId) {
        this.mappingId = mappingId;
    }

    public BigDecimal getGrItemAcceptedQuantity() {
        return grItemAcceptedQuantity;
    }

    public void setGrItemAcceptedQuantity(BigDecimal grItemAcceptedQuantity) {
        this.grItemAcceptedQuantity = grItemAcceptedQuantity;
    }

    public int getGrItemId() {
        return grItemId;
    }

    public void setGrItemId(int grItemId) {
        this.grItemId = grItemId;
    }

    public int getPoItemId() {
        return poItemId;
    }

    public void setPoItemId(int poItemId) {
        this.poItemId = poItemId;
    }

    public BigDecimal getConversionRatio() {
        return conversionRatio;
    }

    public void setConversionRatio(BigDecimal conversionRatio) {
        this.conversionRatio = conversionRatio;
    }

    public String getUpdationreason() {
        return updationreason;
    }

    public void setUpdationreason(String updationreason) {
        this.updationreason = updationreason;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BigDecimal getRejectedQty() {
        return rejectedQty;
    }

    public void setRejectedQty(BigDecimal rejectedQty) {
        this.rejectedQty = rejectedQty;
    }

    public BigDecimal getActualPackagingQty() {
        return actualPackagingQty;
    }

    public void setActualPackagingQty(BigDecimal actualPackagingQty) {
        this.actualPackagingQty = actualPackagingQty;
    }
}

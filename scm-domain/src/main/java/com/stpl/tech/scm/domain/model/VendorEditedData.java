package com.stpl.tech.scm.domain.model;

public class VendorEditedData {
    private Integer vendorId;
    private  boolean entityName ;
    private  boolean firstName ;
    private  boolean lastName;
    private boolean primaryContact;
    private boolean secondaryContact;
    private boolean primaryEmail;
    private boolean secondaryEmail;
    private boolean vendorAddress;
    private boolean vendorState;
    private boolean vendorCITY;
    private boolean vendorCountry;
    private boolean vendorZipCode;
    private boolean vendorAddressContact ;
    private  boolean vendorAddressType;
    private boolean vendorStateCode;
    private boolean registeredName;
    private boolean companyName;
    private boolean companyAddress;
    private boolean companyState;
    private boolean companyCity;
    private boolean companyCountry;
    private boolean companyZipcode;
    private boolean companyAddressContact;
    private boolean companyAddressType;
    private boolean companyStateCode;
    private boolean companyType;
    private boolean businessType;
    private boolean cinDocument;
    private boolean cin;
    private boolean cst;
    private boolean entityType;
    private boolean arcDocument;
    private boolean panDocument;
    private  boolean vatDocument;
    private boolean serviceTaxDocument;
    private boolean exemptSupplier;
    private boolean msmeDocument;
    private boolean msmeRegistered;
    private boolean pan;
    private  boolean uploadedChequeDocumentID;

    private boolean accountNumber;
    private boolean ifscCode;
    private boolean accountType;
    private boolean micreCode;
    private boolean cancelledCheque;
    private boolean accountContact;
    private boolean accountContactNumber;
    private boolean accountContactName;
    private boolean accountContactEmail;
    private boolean accountKind;
    private boolean dispatchEmailID;
    private boolean address;
    private boolean tin;
    private boolean gstStatus;
    private boolean gstin;
    private boolean gstinDocument;
    private boolean notificationType;
    private boolean applyTax;
    private boolean cstDocument;
    private boolean arc;
    public Integer getVendorId() {
        return vendorId;
    }

    public void setVendorId(Integer vendorId) {
        this.vendorId = vendorId;
    }

    public boolean isEntityName() {
        return entityName;
    }

    public void setEntityName(boolean entityName) {
        this.entityName = entityName;
    }

    public boolean isFirstName() {
        return firstName;
    }

    public void setFirstName(boolean firstName) {
        this.firstName = firstName;
    }

    public boolean isLastName() {
        return lastName;
    }

    public void setLastName(boolean lastName) {
        this.lastName = lastName;
    }

    public boolean isPrimaryContact() {
        return primaryContact;
    }

    public void setPrimaryContact(boolean primaryContact) {
        this.primaryContact = primaryContact;
    }

    public boolean isSecondaryContact() {
        return secondaryContact;
    }

    public void setSecondaryContact(boolean secondaryContact) {
        this.secondaryContact = secondaryContact;
    }

    public boolean isPrimaryEmail() {
        return primaryEmail;
    }

    public void setPrimaryEmail(boolean primaryEmail) {
        this.primaryEmail = primaryEmail;
    }

    public boolean isSecondaryEmail() {
        return secondaryEmail;
    }

    public void setSecondaryEmail(boolean secondaryEmail) {
        this.secondaryEmail = secondaryEmail;
    }


    public boolean isVendorAddress() {
        return vendorAddress;
    }

    public void setVendorAddress(boolean vendorAddress) {
        this.vendorAddress = vendorAddress;
    }

    public boolean isVendorState() {
        return vendorState;
    }

    public void setVendorState(boolean vendorState) {
        this.vendorState = vendorState;
    }

    public boolean isVendorCITY() {
        return vendorCITY;
    }

    public void setVendorCITY(boolean vendorCITY) {
        this.vendorCITY = vendorCITY;
    }

    public boolean isVendorCountry() {
        return vendorCountry;
    }

    public void setVendorCountry(boolean vendorCountry) {
        this.vendorCountry = vendorCountry;
    }

    public boolean isVendorZipCode() {
        return vendorZipCode;
    }

    public void setVendorZipCode(boolean vendorZipCode) {
        this.vendorZipCode = vendorZipCode;
    }

    public boolean isVendorAddressContact() {
        return vendorAddressContact;
    }

    public void setVendorAddressContact(boolean vendorAddressContact) {
        this.vendorAddressContact = vendorAddressContact;
    }

    public boolean isVendorAddressType() {
        return vendorAddressType;
    }

    public void setVendorAddressType(boolean vendorAddressType) {
        this.vendorAddressType = vendorAddressType;
    }

    public boolean isVendorStateCode() {
        return vendorStateCode;
    }

    public void setVendorStateCode(boolean vendorStateCode) {
        this.vendorStateCode = vendorStateCode;
    }





    public boolean isCompanyName() {
        return companyName;
    }

    public void setCompanyName(boolean companyName) {
        this.companyName = companyName;
    }


    public boolean isCompanyState() {
        return companyState;
    }

    public void setCompanyState(boolean companyState) {
        this.companyState = companyState;
    }

    public boolean isCompanyCity() {
        return companyCity;
    }

    public void setCompanyCity(boolean companyCity) {
        this.companyCity = companyCity;
    }

    public boolean isCompanyCountry() {
        return companyCountry;
    }

    public void setCompanyCountry(boolean companyCountry) {
        this.companyCountry = companyCountry;
    }

    public boolean isCompanyZipcode() {
        return companyZipcode;
    }

    public void setCompanyZipcode(boolean companyZipcode) {
        this.companyZipcode = companyZipcode;
    }

    public boolean isCompanyAddressContact() {
        return companyAddressContact;
    }

    public void setCompanyAddressContact(boolean companyAddressContact) {
        this.companyAddressContact = companyAddressContact;
    }

    public boolean isCompanyAddressType() {
        return companyAddressType;
    }

    public void setCompanyAddressType(boolean companyAddressType) {
        this.companyAddressType = companyAddressType;
    }

    public boolean isCompanyStateCode() {
        return companyStateCode;
    }

    public void setCompanyStateCode(boolean companyStateCode) {
        this.companyStateCode = companyStateCode;
    }


    public boolean isCompanyType() {
        return companyType;
    }

    public void setCompanyType(boolean companyType) {
        this.companyType = companyType;
    }



    public boolean isCinDocument() {
        return cinDocument;
    }

    public void setCinDocument(boolean cinDocument) {
        this.cinDocument = cinDocument;
    }

    public boolean isArcDocument() {
        return arcDocument;
    }

    public void setArcDocument(boolean arcDocument) {
        this.arcDocument = arcDocument;
    }

    public boolean isPanDocument() {
        return panDocument;
    }

    public void setPanDocument(boolean panDocument) {
        this.panDocument = panDocument;
    }

    public boolean isVatDocument() {
        return vatDocument;
    }

    public void setVatDocument(boolean vatDocument) {
        this.vatDocument = vatDocument;
    }

    public boolean isServiceTaxDocument() {
        return serviceTaxDocument;
    }

    public void setServiceTaxDocument(boolean serviceTaxDocument) {
        this.serviceTaxDocument = serviceTaxDocument;
    }

    public boolean isExemptSupplier() {
        return exemptSupplier;
    }

    public void setExemptSupplier(boolean exemptSupplier) {
        this.exemptSupplier = exemptSupplier;
    }

    public boolean isMsmeDocument() {
        return msmeDocument;
    }

    public void setMsmeDocument(boolean msmeDocument) {
        this.msmeDocument = msmeDocument;
    }

    public boolean isUploadedChequeDocumentID() {
        return uploadedChequeDocumentID;
    }

    public void setUploadedChequeDocumentID(boolean uploadedChequeDocumentID) {
        this.uploadedChequeDocumentID = uploadedChequeDocumentID;
    }

    public boolean isAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(boolean accountNumber) {
        this.accountNumber = accountNumber;
    }

    public boolean isIfscCode() {
        return ifscCode;
    }

    public void setIfscCode(boolean ifscCode) {
        this.ifscCode = ifscCode;
    }

    public boolean isAccountType() {
        return accountType;
    }

    public void setAccountType(boolean accountType) {
        this.accountType = accountType;
    }

    public boolean isMicreCode() {
        return micreCode;
    }

    public void setMicreCode(boolean micreCode) {
        this.micreCode = micreCode;
    }

    public boolean isAccountContactNumber() {
        return accountContactNumber;
    }

    public void setAccountContactNumber(boolean accountContactNumber) {
        this.accountContactNumber = accountContactNumber;
    }

    public boolean isAccountContactName() {
        return accountContactName;
    }

    public void setAccountContactName(boolean accountContactName) {
        this.accountContactName = accountContactName;
    }

    public boolean isAccountContactEmail() {
        return accountContactEmail;
    }

    public void setAccountContactEmail(boolean accountContactEmail) {
        this.accountContactEmail = accountContactEmail;
    }

    public boolean isAccountKind() {
        return accountKind;
    }

    public void setAccountKind(boolean accountKind) {
        this.accountKind = accountKind;
    }



    public boolean isDispatchEmailID() {
        return dispatchEmailID;
    }

    public void setDispatchEmailID(boolean dispatchEmailID) {
        this.dispatchEmailID = dispatchEmailID;
    }


    public boolean isAddress() {
        return address;
    }

    public void setAddress(boolean address) {
        this.address = address;
    }


    public boolean isRegisteredName() {
        return registeredName;
    }

    public void setRegisteredName(boolean registeredName) {
        this.registeredName = registeredName;
    }

    public boolean isCompanyAddress() {
        return companyAddress;
    }

    public void setCompanyAddress(boolean companyAddress) {
        this.companyAddress = companyAddress;
    }

    public boolean isBusinessType() {
        return businessType;
    }

    public void setBusinessType(boolean businessType) {
        this.businessType = businessType;
    }

    public boolean isCin() {
        return cin;
    }

    public void setCin(boolean cin) {
        this.cin = cin;
    }

    public boolean isCst() {
        return cst;
    }

    public void setCst(boolean cst) {
        this.cst = cst;
    }

    public boolean isEntityType() {
        return entityType;
    }

    public void setEntityType(boolean entityType) {
        this.entityType = entityType;
    }

    public boolean isMsmeRegistered() {
        return msmeRegistered;
    }

    public void setMsmeRegistered(boolean msmeRegistered) {
        this.msmeRegistered = msmeRegistered;
    }

    public boolean isPan() {
        return pan;
    }

    public void setPan(boolean pan) {
        this.pan = pan;
    }

    public boolean isCancelledCheque() {
        return cancelledCheque;
    }

    public void setCancelledCheque(boolean cancelledCheque) {
        this.cancelledCheque = cancelledCheque;
    }

    public boolean isAccountContact() {
        return accountContact;
    }

    public void setAccountContact(boolean accountContact) {
        this.accountContact = accountContact;
    }

    public boolean isTin() {
        return tin;
    }

    public void setTin(boolean tin) {
        this.tin = tin;
    }

    public boolean isGstStatus() {
        return gstStatus;
    }

    public void setGstStatus(boolean gstStatus) {
        this.gstStatus = gstStatus;
    }

    public boolean isGstin() {
        return gstin;
    }

    public void setGstin(boolean gstin) {
        this.gstin = gstin;
    }

    public boolean isGstinDocument() {
        return gstinDocument;
    }

    public void setGstinDocument(boolean gstinDocument) {
        this.gstinDocument = gstinDocument;
    }

    public boolean isNotificationType() {
        return notificationType;
    }

    public void setNotificationType(boolean notificationType) {
        this.notificationType = notificationType;
    }

    public boolean isApplyTax() {
        return applyTax;
    }

    public void setApplyTax(boolean applyTax) {
        this.applyTax = applyTax;
    }

    public boolean isCstDocument() {
        return cstDocument;
    }

    public void setCstDocument(boolean cstDocument) {
        this.cstDocument = cstDocument;
    }

    public boolean isArc() {
        return arc;
    }

    public void setArc(boolean arc) {
        this.arc = arc;
    }


}

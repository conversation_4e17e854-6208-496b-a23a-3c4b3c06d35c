/**
 * 
 */
package com.stpl.tech.scm.domain.vo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.stpl.tech.scm.domain.model.ConsumptionOrder;
import com.stpl.tech.scm.domain.model.InventoryItemDrilldown;
import com.stpl.tech.scm.domain.model.PriceUpdateEntryType;
import com.stpl.tech.scm.domain.model.StockEventType;

/**
 * <AUTHOR>
 *
 */
public interface InventoryItemVO {

	public int getKeyId();

	public PriceUpdateEntryType getKeyType();
	
	public void setKeyType(PriceUpdateEntryType type);

	public int getItemKeyId();

	public StockEventType getItemKeyType();

	public List<InventoryItemDrilldown> getDrillDowns();

	public BigDecimal getQuantity();

	public BigDecimal getPrice();

	public void setPrice(BigDecimal price);

	public void setQuantity(BigDecimal price);

	public String getUom();
	
	public Date getExpiryDate();
	
	public void setExpiryDate(Date expiryDate);
	
	public void setDrillDowns(List<InventoryItemDrilldown> drillDowns);
	
	default public ConsumptionOrder getConsumptionOrder() {
		return ConsumptionOrder.FIFO;	
	}
	
}

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.06.13 at 06:17:07 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for DayCloseEventLogType.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="DayCloseEventLogType"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="WASTAGES"/&gt;
 *     &lt;enumeration value="INVENTORY"/&gt;
 *     &lt;enumeration value="TRANSFERS"/&gt;
 *     &lt;enumeration value="BOOKINGS"/&gt;
 *     &lt;enumeration value="RECEIVINGS"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "DayCloseEventLogType")
@XmlEnum
public enum DayCloseEventLogType {

    WASTAGES,
    CONSUMPTION,
    TRANSFERS,
    GATEPASS,
    GATEPASS_RETURN,
    INVOICE,
    BOOKINGS,
    RECEIVINGS,
    INVENTORY,
    CORRECTION,
    REVERSE_BOOKING,
    REVERSE_CONSUMPTION,
    B2B_RETURN;

    public String value() {
        return name();
    }

    public static DayCloseEventLogType fromValue(String v) {
        return valueOf(v);
    }

}

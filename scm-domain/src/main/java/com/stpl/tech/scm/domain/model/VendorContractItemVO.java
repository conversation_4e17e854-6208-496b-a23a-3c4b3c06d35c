package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VendorContractItemVO {
    private Integer keyId;
    private IdCodeName sku;
    private PackagingData pkg;
    private IdCodeName vendor;
    private IdCodeName dispatch;
    private IdCodeName delivery;
    private String status;
    private String taxPercentage;
    private String isNewProduct;
    private String isNewItem;
    private DateAndValue current = new DateAndValue();
    private DateAndValue updated = new DateAndValue();
}

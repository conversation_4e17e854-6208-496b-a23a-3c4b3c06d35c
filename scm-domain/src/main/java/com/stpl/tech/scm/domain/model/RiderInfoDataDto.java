package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RiderInfoDataDto implements Serializable {
    private Integer riderInfoDataId;
    private Integer employeeId;
    private Integer vehicleId;
    private Integer unitId;
    private Date creationTime;
    private String riderStatus;
    private String lastUpdateTime;
}

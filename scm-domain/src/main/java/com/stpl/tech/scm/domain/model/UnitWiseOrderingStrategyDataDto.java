package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UnitWiseOrderingStrategyDataDto {

    private Integer id;
    private Integer unitId;
    private Integer strategyId;
    private DemandForecastingStrategyMetadataDto demandForecastingStrategyMetadata;
    private String status;
    private Integer proxyUnitId;
    private Integer orderingMultiplierDataId;
    private OrderingMultiplierDataDto orderingMultiplierData;
}

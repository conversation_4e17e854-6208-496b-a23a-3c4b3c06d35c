package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SkuPriceUpdate implements Serializable {

	protected String employeeName;
	protected int employeeId;
	SkuPriceDetail detail;

	public String getEmployeeName() {
		return employeeName;
	}

	public void setEmployeeName(String employeeName) {
		this.employeeName = employeeName;
	}

	public int getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(int employeeId) {
		this.employeeId = employeeId;
	}

	public SkuPriceDetail getDetail() {
		return detail;
	}

	public void setDetail(SkuPriceDetail detail) {
		this.detail = detail;
	}

}

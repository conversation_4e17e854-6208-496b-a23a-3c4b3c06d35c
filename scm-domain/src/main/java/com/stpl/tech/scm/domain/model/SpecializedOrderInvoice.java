package com.stpl.tech.scm.domain.model;

import java.sql.Timestamp;
import java.util.Date;

public class SpecializedOrderInvoice {
    private Integer specializedOrderInvoiceId;
    private Integer vendorId;
    private Integer unitId;
    private Date generationTime;
    private String isPrRaised;
    private String invoiceUrl;
    private String invoiceId;
    private Integer documentId;
    private GoodsReceived goodsReceived;
    private Integer prId;

    public Integer getSpecializedOrderInvoiceId() {
        return this.specializedOrderInvoiceId;
    }

    public void setSpecializedOrderInvoiceId(Integer specializedOrderInvoiceId) {
        this.specializedOrderInvoiceId = specializedOrderInvoiceId;
    }

    public Integer getVendorId() {
        return this.vendorId;
    }

    public void setVendorId(Integer vendorId) {
        this.vendorId = vendorId;
    }

    public Integer getUnitId() {
        return this.unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Date getGenerationTime() {
        return this.generationTime;
    }

    public void setGenerationTime(Date generationTime) {
        this.generationTime = generationTime;
    }

    public String getIsPrRaised() {
        return this.isPrRaised;
    }

    public void setIsPrRaised(String isPrRaised) {
        this.isPrRaised = isPrRaised;
    }

    public String getInvoiceUrl() {
        return this.invoiceUrl;
    }

    public void setInvoiceUrl(String invoiceUrl) {
        this.invoiceUrl = invoiceUrl;
    }

    public String getInvoiceId() {
        return this.invoiceId;
    }

    public void setInvoiceId(String invoiceId) {
        this.invoiceId = invoiceId;
    }

    public Integer getDocumentId() {
        return this.documentId;
    }

    public void setDocumentId(Integer documentId) {
        this.documentId = documentId;
    }

    public GoodsReceived getGoodsReceived() {
        return goodsReceived;
    }

    public void setGoodsReceived(GoodsReceived goodsReceived) {
        this.goodsReceived = goodsReceived;
    }

    public Integer getPrId() {
        return prId;
    }

    public void setPrId(Integer prId) {
        this.prId = prId;
    }
}

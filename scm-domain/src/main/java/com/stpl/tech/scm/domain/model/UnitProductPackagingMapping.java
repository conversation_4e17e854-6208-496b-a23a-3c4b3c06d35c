package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "UnitProductPackagingMapping", propOrder = {
    "productId",
    "packagingId",
    "fulfillmentUnitId",
    "fulfillmentType"
})
public class UnitProductPackagingMapping {
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer productId;
    protected int packagingId;
    protected int fulfillmentUnitId;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected FulfillmentType fulfillmentType;


    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public int getPackagingId() {
        return packagingId;
    }

    public void setPackagingId(int packagingId) {
        this.packagingId = packagingId;
    }

    public int getFulfillmentUnitId() {
        return fulfillmentUnitId;
    }

    public void setFulfillmentUnitId(int fulfillmentUnitId) {
        this.fulfillmentUnitId = fulfillmentUnitId;
    }

    public FulfillmentType getFulfillmentType() {
        return fulfillmentType;
    }

    public void setFulfillmentType(FulfillmentType fulfillmentType) {
        this.fulfillmentType = fulfillmentType;
    }
}

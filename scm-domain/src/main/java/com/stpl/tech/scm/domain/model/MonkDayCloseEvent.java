package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MonkDayCloseEvent {
    
    private Long monkDayCloseEventStatusId;
    private Integer unitId;
    private Date businessDate;
    private String eventType;
    private String eventStatus;
    
    // Foreign key references to DAY_CLOSE_EVENT table
    private Integer kettleDayCloseId;
    private Integer sumoDayCloseId;
    
    private List<MonkStatusDayClose> monkStatuses;
    
    public enum EventStatus {
        INITIATED, COMPLETED
    }
    
    public enum EventType {
        MINI_DIAGNOSIS
    }
}

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2017.05.18 at 10:50:14 AM IST
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for PurchaseOrderStatus.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="PurchaseOrderStatus"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="CREATED"/&gt;
 *     &lt;enumeration value="APPROVED"/&gt;
 *     &lt;enumeration value="CLOSED"/&gt;
 *     &lt;enumeration value="CANCELLED"/&gt;
 *     &lt;enumeration value="REJECTED"/&gt;
 *     &lt;enumeration value="IN_PROGRESS"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 *
 */
@XmlType(name = "BudgetAuditActions")
@XmlEnum
public enum BudgetAuditActions {

    CREATED,
    ADDITION,
    REDUCTION,
    APPROVED,
    PAID,
    CANCELLED,
    REJECTED,
    ORIGINAL_AMOUNT,
    BUDGET_AMOUNT,
    REMAINING_AMOUNT,
    RUNNING_AMOUNT,
    PAID_AMOUNT,
    RECEIVING_AMOUNT,
    CAPEX,
    SR_ID,
    SO_ID,
    PR_ID,
    CAPEX_AUDIT_ID,
    CLOSED,
    IN_PROGRESS,
    GR_ID,
    PO_ID,
    PO_ITEM_ID,
    VGR_ID,
    VGR_ITEM_ID,
    EXTRA_RECEIVING,
    TO_ID,
    EDITED ,
    RO_ID;


    public String value() {
        return name();
    }

    public static BudgetAuditActions fromValue(String v) {
        return valueOf(v);
    }

}

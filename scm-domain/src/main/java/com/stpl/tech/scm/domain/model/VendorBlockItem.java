/*
 * Created By Shanmu<PERSON>
 */

package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VendorBlockItem {
    private Integer poSoId;
    private String poSoStatus;
    private String poSoType;
    private Date poSoCreateDate;
    private String poSoCreatedBy;
    private List<VendorBlockGrSr> vendorBlockGrSrs;
}

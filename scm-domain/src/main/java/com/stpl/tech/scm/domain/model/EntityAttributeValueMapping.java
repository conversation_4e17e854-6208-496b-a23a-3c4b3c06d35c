/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.12 at 03:52:20 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.*;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.Date;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "EntityAttributeValueMapping", propOrder = {
        "entityAttributeValueMappingId",
        "profileId",
        "attributeId",
        "attributeValueId",
        "profileAttributeMappingId",
        "entityType",
        "entityId",
        "creationDate",
        "createdBy",
        "status"
})
public class EntityAttributeValueMapping {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer entityAttributeValueMappingId;
    @XmlElement(required = true)
    protected Integer profileId;
    @XmlElement(required = true)
    protected Integer attributeId;
    @XmlElement(required = true)
    protected Integer attributeValueId;
    @XmlElement(required = true)
    protected Integer profileAttributeMappingId;
    @XmlElement(required = true)
    protected String entityType;
    @XmlElement(required = true)
    protected Integer entityId;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "date")
    protected Date creationDate;
    @XmlElement(required = true)
    protected IdCodeName createdBy;
    @XmlElement(required = true)
    protected String status;
    protected String attributeName;
    protected String attributeValue;

    public Integer getEntityAttributeValueMappingId() {
        return entityAttributeValueMappingId;
    }

    public void setEntityAttributeValueMappingId(Integer entityAttributeValueMappingId) {
        this.entityAttributeValueMappingId = entityAttributeValueMappingId;
    }

    public Integer getAttributeValueId() {
        return attributeValueId;
    }

    public void setAttributeValueId(Integer attributeValueId) {
        this.attributeValueId = attributeValueId;
    }

    public Integer getProfileAttributeMappingId() {
        return profileAttributeMappingId;
    }

    public void setProfileAttributeMappingId(Integer profileAttributeMappingId) {
        this.profileAttributeMappingId = profileAttributeMappingId;
    }

    public String getEntityType() {
        return entityType;
    }

    public void setEntityType(String entityType) {
        this.entityType = entityType;
    }

    public Integer getEntityId() {
        return entityId;
    }

    public void setEntityId(Integer entityId) {
        this.entityId = entityId;
    }

    public Integer getProfileId() {
        return profileId;
    }

    public void setProfileId(Integer profileId) {
        this.profileId = profileId;
    }

    public Integer getAttributeId() {
        return attributeId;
    }

    public void setAttributeId(Integer attributeId) {
        this.attributeId = attributeId;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public IdCodeName getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(IdCodeName createdBy) {
        this.createdBy = createdBy;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAttributeName() {
        return attributeName;
    }

    public void setAttributeName(String attributeName) {
        this.attributeName = attributeName;
    }

    public String getAttributeValue() {
        return attributeValue;
    }

    public void setAttributeValue(String attributeValue) {
        this.attributeValue = attributeValue;
    }
}

package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ServiceOrderShort {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer id;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "date")
    protected Date generationTime;
    protected Integer generatedBy;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "date")
    protected Date lastUpdateTime;
    @XmlElement(required = true, nillable = true)
    protected String vendorName;
    @XmlElement(required = true, nillable = true)
    protected BigDecimal totalCost;
    @XmlElement(required = true, nillable = true)
    protected BigDecimal totalAmount;
    @XmlElement(required = true, nillable = true)
    protected String receiptNumber;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected ServiceOrderStatus status;
    @XmlElement(required = true)
    protected String comment;
    @XmlElement(required = true)
    protected BigDecimal totalTaxes;
    protected Integer costCenterId;
    @XmlElement(required = true)
    protected String dispatchLocationCity;
    protected List<ServiceOrderItem> orderItems = new ArrayList<>();
    protected BigDecimal paidAmount;
    protected String employeeName;
    protected VendorAdvancePayment vendorAdvancePayment;
    protected List<VendorAdvancePayment> vendorAdvancePayments;
    protected Integer approvalOfHodDocumentId;

    public BigDecimal getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(BigDecimal paidAmount) {
        this.paidAmount = paidAmount;
    }

    public void setOrderItems(List<ServiceOrderItem> orderItems) {
        this.orderItems = orderItems;
    }

    public List<ServiceOrderItem> getOrderItems() {
        return orderItems;
    }

    protected DocumentDetail soInvoiceDocument;
    protected String type;

    @XmlElement(required = true, type = Boolean.class, nillable = true)
    protected Boolean accountedForInPnl;

    protected Integer uploadedDocumentId;


    public void setAccountedForInPnl(Boolean accountedForInPnl) {
        this.accountedForInPnl = accountedForInPnl;
    }

    public Boolean getAccountedForInPnl() {
        return accountedForInPnl;
    }



    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Date getGenerationTime() {
        return generationTime;
    }

    public void setGenerationTime(Date generationTime) {
        this.generationTime = generationTime;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public BigDecimal getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(BigDecimal totalCost) {
        this.totalCost = totalCost;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getReceiptNumber() {
        return receiptNumber;
    }

    public void setReceiptNumber(String receiptNumber) {
        this.receiptNumber = receiptNumber;
    }

    public ServiceOrderStatus getStatus() {
        return status;
    }

    public void setStatus(ServiceOrderStatus status) {
        this.status = status;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public BigDecimal getTotalTaxes() {
        return totalTaxes;
    }

    public void setTotalTaxes(BigDecimal totalTaxes) {
        this.totalTaxes = totalTaxes;
    }

    public Integer getCostCenterId() {
        return costCenterId;
    }

    public void setCostCenterId(Integer costCenterId) {
        this.costCenterId = costCenterId;
    }

    public String getDispatchLocationCity() {
        return dispatchLocationCity;
    }

    public void setDispatchLocationCity(String dispatchLocationCity) {
        this.dispatchLocationCity = dispatchLocationCity;
    }

    public DocumentDetail getSoInvoiceDocument() {
        return soInvoiceDocument;
    }

    public void setSoInvoiceDocument(DocumentDetail soInvoiceDocument) {
        this.soInvoiceDocument = soInvoiceDocument;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }


    public Integer getUploadedDocumentId() {
        return uploadedDocumentId;
    }

    public void setUploadedDocumentId(Integer uploadedDocumentId) {
        this.uploadedDocumentId = uploadedDocumentId;
    }

    public Integer getGeneratedBy() {
        return generatedBy;
    }

    public void setGeneratedBy(Integer generatedBy) {
        this.generatedBy = generatedBy;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public VendorAdvancePayment getVendorAdvancePayment() {
        return vendorAdvancePayment;
    }

    public void setVendorAdvancePayment(VendorAdvancePayment vendorAdvancePayment) {
        this.vendorAdvancePayment = vendorAdvancePayment;
    }

    public List<VendorAdvancePayment> getVendorAdvancePayments() {
        return vendorAdvancePayments;
    }

    public void setVendorAdvancePayments(List<VendorAdvancePayment> vendorAdvancePayments) {
        this.vendorAdvancePayments = vendorAdvancePayments;
    }

    public Integer getApprovalOfHodDocumentId() {
        return approvalOfHodDocumentId;
    }

    public void setApprovalOfHodDocumentId(Integer approvalOfHodDocumentId) {
        this.approvalOfHodDocumentId = approvalOfHodDocumentId;
    }
}

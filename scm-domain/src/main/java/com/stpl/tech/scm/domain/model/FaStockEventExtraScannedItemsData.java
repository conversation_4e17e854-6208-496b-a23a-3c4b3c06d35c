package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class FaStockEventExtraScannedItemsData {

    private Integer faStockEventExtraScannedItemsId;

    private Integer assetId;

    private String assetStatus;

    private Integer eventId;

    private Date scannedTime;

    private String isSettled;

    private String comment;

    private String userDescription;

    private Integer docId;

    private Integer ownerUnitId;
}

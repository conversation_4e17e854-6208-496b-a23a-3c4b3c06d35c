package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.poi.ss.formula.eval.NotImplementedException;

import com.stpl.tech.scm.domain.vo.ConsumptionVO;
import com.stpl.tech.scm.domain.vo.InventoryItemVO;
import com.stpl.tech.scm.domain.vo.ReceivingVO;

public class Booking extends AbstractInventoryVO implements ReceivingVO, ConsumptionVO {

	private ProductionBooking booking;

	private boolean getItemDrillDown;

	/**
	 * Object for Inventory update for receiving via booking
	 * 
	 * @param booking
	 */
	public Booking(ProductionBooking booking) {
		super();
		this.booking = booking;
	}

	@Override
	public int getKeyId() {
		return booking.getBookingId();
	}

	@Override
	public StockEventType getKeyType() {
		return StockEventType.BOOKING_RECEIVED;
	}

	@Override
	public int getUnitId() {
		return booking.getUnitId();
	}

	@Override
	public PriceUpdateEntryType getInventoryType() {
		return PriceUpdateEntryType.SKU;
	}

	@Override
	public List<InventoryItemVO> getInventoryItems() {
		List<InventoryItemVO> inventoryList = new ArrayList<>();
		inventoryList.add(new ProductionBookingItem());
		return inventoryList;
	}

	public class ProductionBookingItem implements InventoryItemVO {

		private List<InventoryItemDrilldown> drillDownList = new ArrayList<>();

		@Override
		public int getKeyId() {
			return booking.getSkuId();
		}

		@Override
		public PriceUpdateEntryType getKeyType() {
			return PriceUpdateEntryType.SKU;
		}

		@Override
		public int getItemKeyId() {
			return booking.getBookingId();
		}

		@Override
		public StockEventType getItemKeyType() {
			return StockEventType.BOOKING_RECEIVED;
		}

		@Override
		public List<InventoryItemDrilldown> getDrillDowns() {
			List<InventoryItemDrilldown> list = drillDownList;
			if (!getItemDrillDown) {
				InventoryItemDrilldown inventoryItemDrilldown = new InventoryItemDrilldown();
				inventoryItemDrilldown.setKeyId(booking.getSkuId());
				inventoryItemDrilldown.setKeyType(booking.getKeyType().value());
				inventoryItemDrilldown.setPrice(booking.getUnitPrice());
				inventoryItemDrilldown.setQuantity(booking.getQuantity());
				inventoryItemDrilldown.setExpiryDate(booking.getExpiryDate());
				list.add(inventoryItemDrilldown);
			}
			return list;
		}

		@Override
		public BigDecimal getQuantity() {
			return booking.getQuantity();
		}

		@Override
		public BigDecimal getPrice() {
			return booking.getUnitPrice();
		}

		@Override
		public void setPrice(BigDecimal value) {
			booking.setUnitPrice(value);
		}

		@Override
		public void setQuantity(BigDecimal value) {
			booking.setQuantity(value);
		}

		@Override
		public String getUom() {
			return booking.getUnitOfMeasure();
		}

		@Override
		public void setKeyType(PriceUpdateEntryType type) {
			throw new NotImplementedException("This should not be called for ProductionBookingItem");
		}

		@Override
		public Date getExpiryDate() {
			return booking.getExpiryDate();
		}

		@Override
		public void setExpiryDate(Date expiryDate) {
			booking.setExpiryDate(expiryDate);
		}

		@Override
		public void setDrillDowns(List<InventoryItemDrilldown> drillDowns) {
			this.drillDownList = drillDowns;
		}

	}
	
	@Override
	public boolean isAssetOrder() {
		return false;
	}

	public ProductionBooking getBooking() {
		return booking;
	}

	public boolean isGetItemDrillDown() {
		return getItemDrillDown;
	}

	public void setGetItemDrillDown(boolean getItemDrillDown) {
		this.getItemDrillDown = getItemDrillDown;
	}
}

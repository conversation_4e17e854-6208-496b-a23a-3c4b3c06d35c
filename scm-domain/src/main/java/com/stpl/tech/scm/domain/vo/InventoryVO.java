/**
 * 
 */
package com.stpl.tech.scm.domain.vo;

import java.util.List;

import com.stpl.tech.scm.domain.model.PriceUpdateEntryType;
import com.stpl.tech.scm.domain.model.StockEventType;

/**
 * <AUTHOR>
 *
 */
public interface InventoryVO {

	public int getKeyId();

	public StockEventType getKeyType();

	public int getUnitId();

	public PriceUpdateEntryType getInventoryType();
	
	public List<InventoryItemVO> getInventoryItems();
	
	public boolean isAssetOrder();

}

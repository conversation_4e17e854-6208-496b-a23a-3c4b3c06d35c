package com.stpl.tech.scm.domain.model;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ScmProductConsumptionAverage {

    private Integer productId;
    private Date orderingDay;
    private BigDecimal orderingQuantity;
    private BigDecimal originalOrderingQuantity;
    private BigDecimal suggestedQuantity;
    private BigDecimal originalSuggestedQuantity;
    private BigDecimal averageConsumption;

    private BigDecimal bufferQuantity;
    private BigDecimal originalConsumption;

    private BigDecimal adjustedQuantity;

    private BigDecimal stockOutPercentage;

    private BigDecimal stockOutRaw;

    private BigDecimal cafeTotalHours;

    private BigDecimal wastagePercentage;

    private BigDecimal wastageRaw;

    private String comments;
    private BigDecimal totalConsumption;
}

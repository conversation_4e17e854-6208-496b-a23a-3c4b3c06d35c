package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class NsoEventAssetsResponse {

    protected List<AssetDefinition> assetDefinition;

    protected String eventStatus;

    protected Long hourDiff;

}

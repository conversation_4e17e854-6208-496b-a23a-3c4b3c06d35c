/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.domain.model;

import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class StockTakeSumoDayCloseEventDTO {
    private Integer stockTakeSumoDayCloseEventId;
    private Integer unitId;
    private Integer dayCloseEventId;
    private Integer sumoDayCloseEventId;

    private String stockTakeType;
    private String eventStatus;
    private Integer eventCreatedBy;
    private Date eventCreatedAt;
    private Date eventSubmittedAt;
    private String deviceInfo;
    private String eventCreatedByName;

    private List<StockTakeSumoDayCloseProductsDTO> stockTakeSumoDayCloseProductsDTOS = new ArrayList<>(0);
}

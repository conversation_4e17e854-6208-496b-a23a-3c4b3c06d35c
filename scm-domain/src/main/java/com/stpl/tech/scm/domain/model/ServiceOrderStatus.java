//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.05.18 at 10:50:14 AM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for PurchaseOrderStatus.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="PurchaseOrderStatus"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="CREATED"/&gt;
 *     &lt;enumeration value="APPROVED"/&gt;
 *     &lt;enumeration value="CLOSED"/&gt;
 *     &lt;enumeration value="CANCELLED"/&gt;
 *     &lt;enumeration value="REJECTED"/&gt;
 *     &lt;enumeration value="IN_PROGRESS"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "PurchaseOrderStatus")
@XmlEnum
public enum ServiceOrderStatus {

    PROVISIONAL,
    CREATED,
    PENDING_APPROVAL_L1,
    PENDING_APPROVAL_L2,
    PENDING_APPROVAL_L3,
    PENDING_APPROVAL_L4,
    PENDING_APPROVAL_L5,
    PENDING_APPROVAL_L6,
    APPROVED,
    CLOSED,
    CANCELLED,
    REJECTED,
    REJECTED_L1,
    REJECTED_L2,
    REJECTED_L3,
    REJECTED_L4,
    REJECTED_L5,
    REJECTED_L6,
    IN_PROGRESS,
    PENDING_HOD_APPROVAL,
    FIN_APPROVAL_L1,
    FIN_REJECTED_L1,
    PENDING_VENDOR_APPROVAL,
    REJECTED_VENDOR_APPROVAL

    ;

    public String value() {
        return name();
    }

    public static ServiceOrderStatus fromValue(String v) {
        return valueOf(v);
    }

}

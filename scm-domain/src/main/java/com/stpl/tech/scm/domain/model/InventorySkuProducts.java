package com.stpl.tech.scm.domain.model;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
public class InventorySkuProducts {

    public String skuName;

    public String skuId;

    public String unitOfMeasure;

    public BigDecimal stock;

    public String categoryDefinition;

    public String subCategoryDefinition;


}

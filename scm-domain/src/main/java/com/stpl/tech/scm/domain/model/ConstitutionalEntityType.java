//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.04.22 at 02:18:13 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ConstitutionalEntityType.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="ConstitutionalEntityType"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="PRIVATE_LTD"/&gt;
 *     &lt;enumeration value="PUBLIC_LTD"/&gt;
 *     &lt;enumeration value="CORPORATE"/&gt;
 *     &lt;enumeration value="INDIVIDUAL"/&gt;
 *     &lt;enumeration value="HUF"/&gt;
 *     &lt;enumeration value="PROPRIETARY"/&gt;
 *     &lt;enumeration value="LLP"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 *
 */
@XmlType(name = "ConstitutionalEntityType")
@XmlEnum
public enum ConstitutionalEntityType {

    INDIVIDUAL,
    PRIVATE_LTD,
    PUBLIC_LTD,
    CORPORATE,
    HUF,
    PROPRIETARY,
    LLP,
    TRUST,
    BODY_OF_INDIVIDUALS,
    ASSOCIATION_OF_PERSONS,
    LOCAL_AUTHORITY,
    GOVERNMENT_AGENCY,
    PARTNERSHIP,
    AJP; // ARTIFICIAL JUDICIAL PERSON

    public String value() {
        return name();
    }

    public static ConstitutionalEntityType fromValue(String v) {
        return valueOf(v);
    }

}

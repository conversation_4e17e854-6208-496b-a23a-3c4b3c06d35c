package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.Date;

public class OutwardRegister {

    private Date dateTime;
    private String challanNo;
    private String addressOfBuyer;
    private String detailsOfArticle;
    private BigDecimal quantity;
    private BigDecimal amount;
    private String nameOfDeliverer;
    private String vehicleNoType;
    private String signatureOfSecurity;
    private String remarks;
    private String businessType;
    private Integer invoiceId;
    private Integer id;
    private Date submissionDateTime;
    private Integer unitId;

    public Date getSubmissionDateTime() {
        return submissionDateTime;
    }

    public void setSubmissionDateTime(Date submissionDateTime) {
        this.submissionDateTime = submissionDateTime;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Date getDateTime() {
        return dateTime;
    }

    public void setDateTime(Date dateTime) {
        this.dateTime = dateTime;
    }

    public String getChallanNo() {
        return challanNo;
    }

    public void setChallanNo(String challanNo) {
        this.challanNo = challanNo;
    }

    public String getAddressOfBuyer() {
        return addressOfBuyer;
    }

    public void setAddressOfBuyer(String addressOfBuyer) {
        this.addressOfBuyer = addressOfBuyer;
    }

    public String getDetailsOfArticle() {
        return detailsOfArticle;
    }

    public void setDetailsOfArticle(String detailsOfArticle) {
        this.detailsOfArticle = detailsOfArticle;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getNameOfDeliverer() {
        return nameOfDeliverer;
    }

    public void setNameOfDeliverer(String nameOfDeliverer) {
        this.nameOfDeliverer = nameOfDeliverer;
    }

    public String getVehicleNoType() {
        return vehicleNoType;
    }

    public void setVehicleNoType(String vehicleNoType) {
        this.vehicleNoType = vehicleNoType;
    }

    public String getSignatureOfSecurity() {
        return signatureOfSecurity;
    }

    public void setSignatureOfSecurity(String signatureOfSecurity) {
        this.signatureOfSecurity = signatureOfSecurity;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public Integer getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Integer invoiceId) {
        this.invoiceId = invoiceId;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

}

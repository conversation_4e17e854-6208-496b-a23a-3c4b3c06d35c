package com.stpl.tech.scm.domain.vo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.stpl.tech.scm.domain.model.ConsumptionOrder;
import com.stpl.tech.scm.domain.model.InventoryItemDrilldown;
import com.stpl.tech.scm.domain.model.PriceUpdateEntryType;
import com.stpl.tech.scm.domain.model.StockEventType;

public class InventoryItemVOAdaptor implements InventoryItemVO {

	private int keyId;
	private PriceUpdateEntryType keyType;
	private int itemKeyId;
	private StockEventType itemKeyType;
	private BigDecimal quantity;
	private BigDecimal price;
	private Date expiryDate;
	private String uom;
	private ConsumptionOrder consumptionOrder;
	private List<InventoryItemDrilldown> drillDowns;

	public InventoryItemVOAdaptor(InventoryItemVO vo) {
		this.setKeyId(vo.getKeyId());
		this.setKeyType(vo.getKeyType());
		this.setExpiryDate(vo.getExpiryDate());
		this.setItemKeyId(vo.getItemKeyId());
		this.setItemKeyType(vo.getItemKeyType());
		this.setPrice(vo.getPrice());
		this.setQuantity(vo.getQuantity());
		this.setUom(vo.getUom());
		this.setConsumptionOrder(vo.getConsumptionOrder());
	}

	public int getKeyId() {
		return keyId;
	}

	public void setKeyId(int keyId) {
		this.keyId = keyId;
	}

	public PriceUpdateEntryType getKeyType() {
		return keyType;
	}

	public void setKeyType(PriceUpdateEntryType keyType) {
		this.keyType = keyType;
	}

	public int getItemKeyId() {
		return itemKeyId;
	}

	public void setItemKeyId(int itemKeyId) {
		this.itemKeyId = itemKeyId;
	}

	public StockEventType getItemKeyType() {
		return itemKeyType;
	}

	public void setItemKeyType(StockEventType itemKeyType) {
		this.itemKeyType = itemKeyType;
	}

	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public Date getExpiryDate() {
		return expiryDate;
	}

	public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}

	public String getUom() {
		return uom;
	}

	public void setUom(String uom) {
		this.uom = uom;
	}

	public List<InventoryItemDrilldown> getDrillDowns() {
		return drillDowns;
	}

	public void setDrillDowns(List<InventoryItemDrilldown> drillDowns) {
		this.drillDowns = drillDowns;
	}

	public ConsumptionOrder getConsumptionOrder() {
		return consumptionOrder;
	}

	public void setConsumptionOrder(ConsumptionOrder consumptionOrder) {
		this.consumptionOrder = consumptionOrder;
	}

}

package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class RequestOrderResponse {

	private Integer orderId;
	private BigDecimal requestedAmount;
	private BigDecimal currentAmount;
	private BigDecimal budgetAmount;
	private String category;
	private boolean budgetExceeded;
	private boolean budgetAvailable;
	private List<String> products = new ArrayList<>();

	private Boolean urgentSpecializedOrder;

	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	public BigDecimal getRequestedAmount() {
		return requestedAmount;
	}

	public void setRequestedAmount(BigDecimal requestedAmount) {
		this.requestedAmount = requestedAmount;
	}

	public BigDecimal getCurrentAmount() {
		return currentAmount;
	}

	public void setCurrentAmount(BigDecimal currentAmount) {
		this.currentAmount = currentAmount;
	}

	public BigDecimal getBudgetAmount() {
		return budgetAmount;
	}

	public void setBudgetAmount(BigDecimal budgetAmount) {
		this.budgetAmount = budgetAmount;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public boolean isBudgetExceeded() {
		return budgetExceeded;
	}

	public void setBudgetExceeded(boolean budgetExceeded) {
		this.budgetExceeded = budgetExceeded;
	}

	public List<String> getProducts() {
		return products;
	}

	public void setProducts(List<String> products) {
		this.products = products;
	}

	public boolean isBudgetAvailable() {
		return budgetAvailable;
	}

	public void setBudgetAvailable(boolean budgetAvailable) {
		this.budgetAvailable = budgetAvailable;
	}

	public Boolean getUrgentSpecializedOrder() {
		return urgentSpecializedOrder;
	}

	public void setUrgentSpecializedOrder(Boolean urgentSpecializedOrder) {
		this.urgentSpecializedOrder = urgentSpecializedOrder;
	}
}

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.11 at 12:07:18 PM IST 
//


package com.stpl.tech.scm.domain.model;

import com.stpl.tech.master.domain.model.IdCodeName;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="attributeTypes" type="{http://www.w3schools.com}AttributeType" maxOccurs="unbounded"/&gt;
 *         &lt;element name="fulFillmentTypes" type="{http://www.w3schools.com}FulfillmentType" maxOccurs="unbounded"/&gt;
 *         &lt;element name="packagingTypes" type="{http://www.w3schools.com}PackagingType" maxOccurs="unbounded"/&gt;
 *         &lt;element name="stockTakeType" type="{http://www.w3schools.com}StockTakeType" maxOccurs="unbounded"/&gt;
 *         &lt;element name="switchStatus" type="{http://www.w3schools.com}SwitchStatus" maxOccurs="unbounded"/&gt;
 *         &lt;element name="scmOrderStatus" type="{http://www.w3schools.com}SCMOrderStatus" maxOccurs="unbounded"/&gt;
 *         &lt;element name="categoryDefinitions" type="{http://www.w3schools.com}CategoryDefinition" maxOccurs="unbounded"/&gt;
 *         &lt;element name="subCategoryDefinitions" type="{http://www.w3schools.com}CategoryDefinition" maxOccurs="unbounded"/&gt;
 *         &lt;element name="paymentRequestTypes" type="{http://www.w3schools.com}PaymentRequestType" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="paymentDeviations" type="{http://www.w3schools.com}PaymentDeviation" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "attributeTypes",
    "fulFillmentTypes",
    "packagingTypes",
        "stockTakeType",
    "switchStatus",
    "scmOrderStatus",
    "paymentRequestStatus",
    "categoryDefinitions",
    "subCategoryDefinitions",
        "paymentRequestTypes",
        "paymentDeviations"
})
@XmlRootElement(name = "SCMMetadata")
public class SCMMetadata {

    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected List<AttributeType> attributeTypes;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected List<FulfillmentType> fulFillmentTypes;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected List<PackagingType> packagingTypes;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected List<StockTakeType> stockTakeType;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected List<SwitchStatus> switchStatus;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected List<SCMOrderStatus> scmOrderStatus;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected List<PaymentRequestStatus> paymentRequestStatus;
    @XmlElement(required = true)
    protected List<CategoryDefinition> categoryDefinitions;
    @XmlElement(required = true)
    protected List<SubCategoryDefinition> subCategoryDefinitions;
    @XmlElement
    protected List<VarianceType> varianceTypes;
    @XmlSchemaType(name = "string")
    protected List<IdCodeName> paymentRequestTypes;
    protected List<PaymentDeviation> paymentDeviations;
    protected List<String> tokenizedApis;
    protected List<ProductType> productTypes;

    /**
     * Gets the value of the attributeTypes property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the attributeTypes property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getAttributeTypes().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link AttributeType }
     * 
     * 
     */
    public List<AttributeType> getAttributeTypes() {
        if (attributeTypes == null) {
            attributeTypes = new ArrayList<AttributeType>();
        }
        return this.attributeTypes;
    }

    /**
     * Gets the value of the fulFillmentTypes property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the fulFillmentTypes property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getFulFillmentTypes().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link FulfillmentType }
     * 
     * 
     */
    public List<FulfillmentType> getFulFillmentTypes() {
        if (fulFillmentTypes == null) {
            fulFillmentTypes = new ArrayList<FulfillmentType>();
        }
        return this.fulFillmentTypes;
    }

    /**
     * Gets the value of the packagingTypes property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the packagingTypes property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPackagingTypes().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PackagingType }
     * 
     * 
     */
    public List<PackagingType> getPackagingTypes() {
        if (packagingTypes == null) {
            packagingTypes = new ArrayList<PackagingType>();
        }
        return this.packagingTypes;
    }

    /**
     * Gets the value of the stockTakeType property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the stockTakeType property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getStockTakeType().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link StockTakeType }
     * 
     * 
     */
    public List<StockTakeType> getStockTakeType() {
        if (stockTakeType == null) {
            stockTakeType = new ArrayList<StockTakeType>();
        }
        return this.stockTakeType;
    }

    /**
     * Gets the value of the switchStatus property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the switchStatus property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getSwitchStatus().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link SwitchStatus }
     * 
     * 
     */
    public List<SwitchStatus> getSwitchStatus() {
        if (switchStatus == null) {
            switchStatus = new ArrayList<SwitchStatus>();
        }
        return this.switchStatus;
    }

    /**
     * Gets the value of the scmOrderStatus property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the scmOrderStatus property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getScmOrderStatus().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link SCMOrderStatus }
     *
     *
     */
    public List<SCMOrderStatus> getScmOrderStatus() {
        if (scmOrderStatus == null) {
            scmOrderStatus = new ArrayList<SCMOrderStatus>();
        }
        return this.scmOrderStatus;
    }

    /**
     * Gets the value of the paymentRequestStatus property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the paymentRequestStatus property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPaymentRequestStatus().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PaymentRequestStatus }
     *
     *
     */
    public List<PaymentRequestStatus> getPaymentRequestStatus() {
        if (paymentRequestStatus == null) {
            paymentRequestStatus = new ArrayList<PaymentRequestStatus>();
        }
        return this.paymentRequestStatus;
    }

    /**
     * Gets the value of the categoryDefinitions property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the categoryDefinitions property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getCategoryDefinitions().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link CategoryDefinition }
     * 
     * 
     */
    public List<CategoryDefinition> getCategoryDefinitions() {
        if (categoryDefinitions == null) {
            categoryDefinitions = new ArrayList<CategoryDefinition>();
        }
        return this.categoryDefinitions;
    }

    /**
     * Gets the value of the categoryDefinitions property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the categoryDefinitions property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getCategoryDefinitions().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link CategoryDefinition }
     * 
     * 
     */
    public List<SubCategoryDefinition> getSubCategoryDefinitions() {
        if (subCategoryDefinitions == null) {
        	subCategoryDefinitions = new ArrayList<SubCategoryDefinition>();
        }
        return this.subCategoryDefinitions;
    }


    /**
     * Gets the value of the varianceTypes property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the varianceTypes property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getVarianceTypes().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link VarianceType }
     *
     *
     */
    public List<VarianceType> getVarianceTypes() {
        if (varianceTypes == null) {
            varianceTypes = new ArrayList<VarianceType>();
        }
        return this.varianceTypes;
    }

    /**
     * Gets the value of the paymentRequestTypes property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the paymentRequestTypes property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPaymentRequestTypes().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link IdCodeName }
     *
     *
     */
    public List<IdCodeName> getPaymentRequestTypes() {
        if (paymentRequestTypes == null) {
            paymentRequestTypes = new ArrayList<IdCodeName>();
        }
        return this.paymentRequestTypes;
    }

    /**
     * Gets the value of the paymentDeviations property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the paymentDeviations property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPaymentDeviations().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PaymentDeviation }
     *
     *
     */
    public List<PaymentDeviation> getPaymentDeviations() {
        if (paymentDeviations == null) {
            paymentDeviations = new ArrayList<PaymentDeviation>();
        }
        return this.paymentDeviations;
    }

    public List<String> getTokenizedApis() {
        if (tokenizedApis == null) {
            tokenizedApis = new ArrayList<String>();
        }
        return tokenizedApis;
    }

    public List<ProductType> getProductTypes() {
        if (productTypes == null) {
            productTypes = new ArrayList<>();
        }
        return productTypes;
    }
}

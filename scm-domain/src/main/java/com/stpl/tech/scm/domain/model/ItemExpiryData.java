package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.stpl.tech.scm.domain.vo.InventoryItemVO;

public class ItemExpiryData implements InventoryItemVO {

	protected Integer keyId;
	protected Integer itemKeyId;
	protected String keyName;
	protected PriceUpdateEntryType keyType;
	protected String uom;
	protected ItemExpiryType expiryType;
	protected BigDecimal quantity;
	protected BigDecimal price;
	protected Date expiryDate;
	protected List<InventoryItemDrilldown> drillDowns;
	protected ConsumptionOrder consumptionOrder;
	protected List<Date> availableDates = new ArrayList<>();

	public ItemExpiryData() {
		// TODO Auto-generated constructor stub
	}

	public String getKeyName() {
		return keyName;
	}

	public void setKeyName(String keyName) {
		this.keyName = keyName;
	}

	public ItemExpiryType getExpiryType() {
		return expiryType;
	}

	public void setExpiryType(ItemExpiryType expiryType) {
		this.expiryType = expiryType;
	}

	public void setKeyId(Integer keyId) {
		this.keyId = keyId;
	}

	public void setUom(String uom) {
		this.uom = uom;
	}

	@Override
	public int getKeyId() {
		return this.keyId;
	}

	@Override
	public PriceUpdateEntryType getKeyType() {
		return this.keyType;
	}

	@Override
	public void setKeyType(PriceUpdateEntryType type) {
		this.keyType = type;
	}

	@Override
	public int getItemKeyId() {
		return this.itemKeyId;
	}

	public void setItemKeyId(Integer itemKeyId) {
		this.itemKeyId = itemKeyId;
	}

	@Override
	public StockEventType getItemKeyType() {
		return StockEventType.TRANSFER_OUT;
	}

	@Override
	public List<InventoryItemDrilldown> getDrillDowns() {
		if(this.drillDowns == null) {
			this.drillDowns = new ArrayList<>();
		}
		return this.drillDowns;
	}

	@Override
	public BigDecimal getQuantity() {
		return this.quantity;
	}

	@Override
	public BigDecimal getPrice() {
		return this.price;
	}

	@Override
	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	@Override
	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	@Override
	public String getUom() {
		return this.uom;
	}

	@Override
	public Date getExpiryDate() {
		return this.expiryDate;
	}

	@Override
	public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}

	@Override
	public void setDrillDowns(List<InventoryItemDrilldown> drillDowns) {
		this.drillDowns = drillDowns;
	}

	@Override
	public ConsumptionOrder getConsumptionOrder() {
		return consumptionOrder;
	}

	public void setConsumptionOrder(ConsumptionOrder consumptionOrder) {
		this.consumptionOrder = consumptionOrder;
	}

	public List<Date> getAvailableDates() {
		return availableDates;
	}

	public void setAvailableDates(List<Date> availableDates) {
		this.availableDates = availableDates;
	}
	
}

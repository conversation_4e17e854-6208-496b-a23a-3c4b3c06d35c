//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.02.22 at 07:12:15 PM IST 
//


package com.stpl.tech.scm.domain.model;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for PriceUpdateEventVO complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="PriceUpdateEventVO"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="hasEvents" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="entries" type="{http://www.w3schools.com}PriceUpdateEvent" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PriceUpdateEventVO", propOrder = {
    "hasEvents",
    "entries"
})
public class PriceUpdateEventVO {

    protected boolean hasEvents;
    protected List<PriceUpdateEvent> entries;

    /**
     * Gets the value of the hasEvents property.
     * 
     */
    public boolean isHasEvents() {
        return hasEvents;
    }

    /**
     * Sets the value of the hasEvents property.
     * 
     */
    public void setHasEvents(boolean value) {
        this.hasEvents = value;
    }

    /**
     * Gets the value of the entries property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the entries property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getEntries().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PriceUpdateEvent }
     * 
     * 
     */
    public List<PriceUpdateEvent> getEntries() {
        if (entries == null) {
            entries = new ArrayList<PriceUpdateEvent>();
        }
        return this.entries;
    }

}

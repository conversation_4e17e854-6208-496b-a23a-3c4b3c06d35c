package com.stpl.tech.scm.domain.model;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class VendorContractSoPayload {

    String vendorIp;
    String vendorLoc;
    Integer vendorContractSoId;

    String vendorName;
    String venddorDesignation;
    Boolean isApproved;

}

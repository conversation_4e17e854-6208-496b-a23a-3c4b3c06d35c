//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2017.09.11 at 11:20:21 AM IST
//


package com.stpl.tech.scm.domain.model;

import com.stpl.tech.master.domain.model.State;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <p>Java class for PaymentRequest complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="PaymentRequest"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="paymentRequestId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="type" type="{http://www.w3schools.com}PaymentRequestType"/&gt;
 *         &lt;element name="invoiceNumber" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="vendorId" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="vendorCreditPeriod" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="createdBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="creationTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="currentStatus" type="{http://www.w3schools.com}PaymentRequestStatus"/&gt;
 *         &lt;element name="lastUpdated" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="updatedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="paymentInvoice" type="{http://www.w3schools.com}PaymentInvoice"/&gt;
 *         &lt;element name="paymentCycle" type="{http://www.w3schools.com}PaymentCalendar"/&gt;
 *         &lt;element name="proposedAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="paidAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="amountsMatch" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="blocked" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="debitExceeded" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="paidAdhoc" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="blockedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="requestingUnit" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="grDocType" type="{http://www.w3schools.com}GRDocType"/&gt;
 *         &lt;element name="deviationCount" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="remarks" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="debitNote" type="{http://www.w3schools.com}DebitNoteDetail"/&gt;
 *         &lt;element name="vendorDebitBalance" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="paymentDetail" type="{http://www.w3schools.com}PRPaymentDetail"/&gt;
 *         &lt;element name="requestItemMappings" type="{http://www.w3schools.com}PaymentRequestItemMapping" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="statusLogs" type="{http://www.w3schools.com}PaymentRequestStatusLog" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="requestLogs" type="{http://www.w3schools.com}PaymentRequestLog" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PaymentRequest", propOrder = {
    "paymentRequestId",
    "type",
    "invoiceNumber",
    "vendorId",
    "vendorCreditPeriod",
    "createdBy",
    "creationTime",
    "currentStatus",
    "lastUpdated",
    "updatedBy",
    "paymentInvoice",
    "paymentCycle",
    "proposedAmount",
    "paidAmount",
    "amountsMatch",
    "blocked",
    "paidAdhoc",
    "blockedBy",
    "requestingUnit",
    "grDocType",
    "deviationCount",
    "remarks",
    "debitNote",
    "vendorDebitBalance",
    "paymentDetail",
    "requestItemMappings",
    "statusLogs",
    "requestLogs",
    "state"
})
public class PaymentRequest {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer paymentRequestId;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected PaymentRequestType type;
    @XmlElement(required = true, nillable = true)
    protected String invoiceNumber;
    @XmlElement(required = true)
    protected IdCodeName vendorId;
    protected int vendorCreditPeriod;
    @XmlElement(required = true)
    protected IdCodeName createdBy;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date creationTime;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected PaymentRequestStatus currentStatus;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date lastUpdated;
    @XmlElement(required = true)
    protected IdCodeName updatedBy;
    @XmlElement(required = true)
    protected PaymentInvoice paymentInvoice;
    @XmlElement(required = true, nillable = true)
    protected PaymentCalendar paymentCycle;
    @XmlElement(required = true)
    protected BigDecimal proposedAmount;
    @XmlElement(required = true)
    protected BigDecimal paidAmount;
    protected BigDecimal duplicatePaidAmount;
    protected boolean amountsMatch;
    protected boolean blocked;
    //protected boolean debitExceeded;
    protected boolean paidAdhoc;
    @XmlElement(required = true)
    protected IdCodeName blockedBy;
    @XmlElement(required = true)
    protected IdCodeName requestingUnit;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected InvoiceDocType grDocType;
    protected int deviationCount;
    @XmlElement(required = true)
    protected String remarks;
    @XmlElement(required = true)
    protected DebitNoteDetail debitNote;
    @XmlElement(required = true)
    protected BigDecimal vendorDebitBalance;
    @XmlElement(required = true)
    protected PRPaymentDetail paymentDetail;
    protected List<PaymentRequestItemMapping> requestItemMappings;
    protected List<PaymentRequestStatusLog> statusLogs;
    protected List<PaymentRequestLog> requestLogs;
    protected int companyId;
    protected List<VendorDebitBalanceVO> vendorDebitBalanceVOS;
    protected Date paymentDate;
    protected boolean forceCreate;
    protected Date vendorPaymentDate;
    protected State state;
    private String vendorState;
    private String paymentState;
    private  String pan;
    private String panStatus;
    private Boolean tds;
    protected DocumentDetail debitNoteDocumentDetail;
    protected String extraChargesType;
    protected BigDecimal extraChargesSgst;
    protected BigDecimal extraChargesCgst;
    protected BigDecimal extraChargesIgst;
    protected BigDecimal extraChargesWithOutTax;


    protected List<String> requestingUnitsSR;
    protected String businessCostDetailData;
    private String mandatoryRequiredDocument;
    protected List<Integer> advanceSrs;
    protected VendorAdvancePayment advancePayment;
    protected List<VendorAdvancePayment> vendorAdvancePayments;
    protected Integer advancePaymentId;
    protected BigDecimal advanceAmount;
    protected List<PaymentRequestQuery> paymentRequestQueries;
    private Date lastQueriedDate;
    private Date lastQueryResolvedDate;
    private String paymentCard;
    private String cardPaymentTransactionNumber;
    private Integer cardPaymentProof;
    private String cardPaymentComment;
    private String section206;
    protected List<Integer> advancePaymentIds;
    protected String isSoContractBreach;

    protected Integer soContractBreachApprovalDoc;
    protected PrProcessMetaData allPrProcessMetaData;

    private String isPrCCVendor;
    private String isPrEcomParty;

    private List<LdcVendorDomain> applicableLdc;

    private PaymentRequestMetaDataDomain paymentRequestMetaData;

    private Date panStatusUpdatedAt;

    private Date section206UpdatedAt;

    private Boolean partialSr = false;
    /**
     * Gets the value of the paymentRequestId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getPaymentRequestId() {
        return paymentRequestId;
    }

    /**
     * Sets the value of the paymentRequestId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setPaymentRequestId(Integer value) {
        this.paymentRequestId = value;
    }

    /**
     * Gets the value of the type property.
     *
     * @return
     *     possible object is
     *     {@link PaymentRequestType }
     *
     */
    public PaymentRequestType getType() {
        return type;
    }

    /**
     * Sets the value of the type property.
     *
     * @param value
     *     allowed object is
     *     {@link PaymentRequestType }
     *
     */
    public void setType(PaymentRequestType value) {
        this.type = value;
    }

    /**
     * Gets the value of the invoiceNumber property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    /**
     * Sets the value of the invoiceNumber property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setInvoiceNumber(String value) {
        this.invoiceNumber = value;
    }

    /**
     * Gets the value of the vendorId property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getVendorId() {
        return vendorId;
    }

    /**
     * Sets the value of the vendorId property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setVendorId(IdCodeName value) {
        this.vendorId = value;
    }

    /**
     * Gets the value of the vendorCreditPeriod property.
     *
     */
    public int getVendorCreditPeriod() {
        return vendorCreditPeriod;
    }

    /**
     * Sets the value of the vendorCreditPeriod property.
     *
     */
    public void setVendorCreditPeriod(int value) {
        this.vendorCreditPeriod = value;
    }

    /**
     * Gets the value of the createdBy property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getCreatedBy() {
        return createdBy;
    }

    /**
     * Sets the value of the createdBy property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setCreatedBy(IdCodeName value) {
        this.createdBy = value;
    }

    /**
     * Gets the value of the creationTime property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getCreationTime() {
        return creationTime;
    }

    /**
     * Sets the value of the creationTime property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setCreationTime(Date value) {
        this.creationTime = value;
    }

    /**
     * Gets the value of the currentStatus property.
     *
     * @return
     *     possible object is
     *     {@link PaymentRequestStatus }
     *
     */
    public PaymentRequestStatus getCurrentStatus() {
        return currentStatus;
    }

    /**
     * Sets the value of the currentStatus property.
     *
     * @param value
     *     allowed object is
     *     {@link PaymentRequestStatus }
     *
     */
    public void setCurrentStatus(PaymentRequestStatus value) {
        this.currentStatus = value;
    }

    /**
     * Gets the value of the lastUpdated property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getLastUpdated() {
        return lastUpdated;
    }

    /**
     * Sets the value of the lastUpdated property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setLastUpdated(Date value) {
        this.lastUpdated = value;
    }

    /**
     * Gets the value of the updatedBy property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getUpdatedBy() {
        return updatedBy;
    }

    /**
     * Sets the value of the updatedBy property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setUpdatedBy(IdCodeName value) {
        this.updatedBy = value;
    }

    /**
     * Gets the value of the paymentInvoice property.
     *
     * @return
     *     possible object is
     *     {@link PaymentInvoice }
     *
     */
    public PaymentInvoice getPaymentInvoice() {
        return paymentInvoice;
    }

    /**
     * Sets the value of the paymentInvoice property.
     *
     * @param value
     *     allowed object is
     *     {@link PaymentInvoice }
     *
     */
    public void setPaymentInvoice(PaymentInvoice value) {
        this.paymentInvoice = value;
    }

    /**
     * Gets the value of the paymentCycle property.
     *
     * @return
     *     possible object is
     *     {@link PaymentCalendar }
     *
     */
    public PaymentCalendar getPaymentCycle() {
        return paymentCycle;
    }

    /**
     * Sets the value of the paymentCycle property.
     *
     * @param value
     *     allowed object is
     *     {@link PaymentCalendar }
     *
     */
    public void setPaymentCycle(PaymentCalendar value) {
        this.paymentCycle = value;
    }

    /**
     * Gets the value of the proposedAmount property.
     *
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *
     */
    public BigDecimal getProposedAmount() {
        return proposedAmount;
    }

    /**
     * Sets the value of the proposedAmount property.
     *
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *
     */
    public void setProposedAmount(BigDecimal value) {
        this.proposedAmount = value;
    }

    /**
     * Gets the value of the paidAmount property.
     *
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *
     */
    public BigDecimal getPaidAmount() {
        return paidAmount;
    }

    /**
     * Sets the value of the paidAmount property.
     *
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *
     */
    public void setPaidAmount(BigDecimal value) {
        this.paidAmount = value;
    }

    /**
     * Gets the value of the amountsMatch property.
     *
     */
    public boolean isAmountsMatch() {
        return amountsMatch;
    }

    /**
     * Sets the value of the amountsMatch property.
     *
     */
    public void setAmountsMatch(boolean value) {
        this.amountsMatch = value;
    }

    /**
     * Gets the value of the blocked property.
     *
     */
    public boolean isBlocked() {
        return blocked;
    }

    /**
     * Sets the value of the blocked property.
     *
     */
    public void setBlocked(boolean value) {
        this.blocked = value;
    }

    /**
     * Gets the value of the debitExceeded property.
     *
     */
    /*public boolean isDebitExceeded() {
        return debitExceeded;
    }*/

    /**
     * Sets the value of the debitExceeded property.
     *
     */
    /*public void setDebitExceeded(boolean value) {
        this.debitExceeded = value;
    }*/

    /**
     * Gets the value of the paidAdhoc property.
     *
     */
    public boolean isPaidAdhoc() {
        return paidAdhoc;
    }

    /**
     * Sets the value of the paidAdhoc property.
     *
     */
    public void setPaidAdhoc(boolean value) {
        this.paidAdhoc = value;
    }

    /**
     * Gets the value of the blockedBy property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getBlockedBy() {
        return blockedBy;
    }

    /**
     * Sets the value of the blockedBy property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setBlockedBy(IdCodeName value) {
        this.blockedBy = value;
    }

    /**
     * Gets the value of the requestingUnit property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getRequestingUnit() {
        return requestingUnit;
    }

    /**
     * Sets the value of the requestingUnit property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setRequestingUnit(IdCodeName value) {
        this.requestingUnit = value;
    }

    /**
     * Gets the value of the grDocType property.
     *
     * @return
     *     possible object is
     *     {@link InvoiceDocType }
     *
     */
    public InvoiceDocType getGrDocType() {
        return grDocType;
    }

    /**
     * Sets the value of the grDocType property.
     *
     * @param value
     *     allowed object is
     *     {@link InvoiceDocType }
     *
     */
    public void setGrDocType(InvoiceDocType value) {
        this.grDocType = value;
    }

    /**
     * Gets the value of the deviationCount property.
     *
     */
    public int getDeviationCount() {
        return deviationCount;
    }

    /**
     * Sets the value of the deviationCount property.
     *
     */
    public void setDeviationCount(int value) {
        this.deviationCount = value;
    }

    /**
     * Gets the value of the remarks property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getRemarks() {
        return remarks;
    }

    /**
     * Sets the value of the remarks property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setRemarks(String value) {
        this.remarks = value;
    }

    /**
     * Gets the value of the debitNote property.
     *
     * @return
     *     possible object is
     *     {@link DebitNoteDetail }
     *
     */
    public DebitNoteDetail getDebitNote() {
        return debitNote;
    }

    /**
     * Sets the value of the debitNote property.
     *
     * @param value
     *     allowed object is
     *     {@link DebitNoteDetail }
     *
     */
    public void setDebitNote(DebitNoteDetail value) {
        this.debitNote = value;
    }

    /**
     * Gets the value of the vendorDebitBalance property.
     *
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *
     */
    public BigDecimal getVendorDebitBalance() {
        return vendorDebitBalance;
    }

    /**
     * Sets the value of the vendorDebitBalance property.
     *
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *
     */
    public void setVendorDebitBalance(BigDecimal value) {
        this.vendorDebitBalance = value;
    }

    /**
     * Gets the value of the paymentDetail property.
     *
     * @return
     *     possible object is
     *     {@link PRPaymentDetail }
     *
     */
    public PRPaymentDetail getPaymentDetail() {
        return paymentDetail;
    }

    /**
     * Sets the value of the paymentDetail property.
     *
     * @param value
     *     allowed object is
     *     {@link PRPaymentDetail }
     *
     */
    public void setPaymentDetail(PRPaymentDetail value) {
        this.paymentDetail = value;
    }

    /**
     * Gets the value of the requestItemMappings property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the requestItemMappings property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getRequestItemMappings().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PaymentRequestItemMapping }
     *
     *
     */
    public List<PaymentRequestItemMapping> getRequestItemMappings() {
        if (requestItemMappings == null) {
            requestItemMappings = new ArrayList<PaymentRequestItemMapping>();
        }
        return this.requestItemMappings;
    }

    /**
     * Gets the value of the statusLogs property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the statusLogs property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getStatusLogs().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PaymentRequestStatusLog }
     *
     *
     */
    public List<PaymentRequestStatusLog> getStatusLogs() {
        if (statusLogs == null) {
            statusLogs = new ArrayList<PaymentRequestStatusLog>();
        }
        return this.statusLogs;
    }

    /**
     * Gets the value of the requestLogs property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the requestLogs property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getRequestLogs().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PaymentRequestLog }
     *
     *
     */
    public List<PaymentRequestLog> getRequestLogs() {
        if (requestLogs == null) {
            requestLogs = new ArrayList<PaymentRequestLog>();
        }
        return this.requestLogs;
    }

	public int getCompanyId() {
		return companyId;
	}

	public void setCompanyId(int companyId) {
		this.companyId = companyId;
	}

    public List<VendorDebitBalanceVO> getVendorDebitBalanceVOS() {
        if(vendorDebitBalanceVOS == null){
            vendorDebitBalanceVOS = new ArrayList<>();
        }
        return vendorDebitBalanceVOS;
    }

    public State getState() {
        return state;
    }

    public void setState(State state) {
        this.state = state;
    }

    public Date getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(Date paymentDate) {
        this.paymentDate = paymentDate;
    }

	public boolean isForceCreate() {
		return forceCreate;
	}

	public void setForceCreate(boolean forceCreate) {
		this.forceCreate = forceCreate;
	}

    public String getVendorState() {
        return vendorState;
    }

    public void setVendorState(String vendorState) {
        this.vendorState = vendorState;
    }

    public String getPaymentState() {
        return paymentState;
    }

    public void setPaymentState(String paymentState) {
        this.paymentState = paymentState;
    }

    public String getPan() {
        return pan;
    }

    public void setPan(String pan) {
        this.pan = pan;
    }

    public Boolean getTds() {
        return tds;
    }

    public void setTds(Boolean tds) {
        this.tds = tds;
    }

    public Date getVendorPaymentDate() {
        return vendorPaymentDate;
    }

    public DocumentDetail getDebitNoteDocumentDetail() {
        return debitNoteDocumentDetail;
    }

    public void setDebitNoteDocumentDetail(DocumentDetail debitNoteDocumentDetail) {
        this.debitNoteDocumentDetail = debitNoteDocumentDetail;
    }

    public void setVendorPaymentDate(Date vendorPaymentDate) {
        this.vendorPaymentDate = vendorPaymentDate;
    }

    public String getExtraChargesType() {
        return extraChargesType;
    }

    public void setExtraChargesType(String extraChargesType) {
        this.extraChargesType = extraChargesType;
    }

    public BigDecimal getExtraChargesSgst() {
        return extraChargesSgst;
    }

    public void setExtraChargesSgst(BigDecimal extraChargesSgst) {
        this.extraChargesSgst = extraChargesSgst;
    }

    public BigDecimal getExtraChargesCgst() {
        return extraChargesCgst;
    }

    public void setExtraChargesCgst(BigDecimal extraChargesCgst) {
        this.extraChargesCgst = extraChargesCgst;
    }

    public BigDecimal getExtraChargesIgst() {
        return extraChargesIgst;
    }

    public void setExtraChargesIgst(BigDecimal extraChargesIgst) {
        this.extraChargesIgst = extraChargesIgst;
    }

    public BigDecimal getExtraChargesWithOutTax() {
        return extraChargesWithOutTax;
    }

    public void setExtraChargesWithOutTax(BigDecimal extraChargesWithOutTax) {
        this.extraChargesWithOutTax = extraChargesWithOutTax;
    }

    public String getBusinessCostDetailData() {
        return businessCostDetailData;
    }

    public void setBusinessCostDetailData(String businessCostDetailData) {
        this.businessCostDetailData = businessCostDetailData;
    }

    public List<String> getRequestingUnitsSR() {
        return requestingUnitsSR;
    }

    public void setRequestingUnitsSR(List<String> requestingUnitsSR) {
        this.requestingUnitsSR = requestingUnitsSR;
    }

    public String getMandatoryRequiredDocument() {

        return mandatoryRequiredDocument;
    }

    public void setMandatoryRequiredDocument(String mandatoryRequiredDocument) {
        this.mandatoryRequiredDocument = mandatoryRequiredDocument;
    }

    public List<Integer> getAdvanceSrs() {
        return advanceSrs;
    }

    public void setAdvanceSrs(List<Integer> advanceSrs) {
        this.advanceSrs = advanceSrs;
    }

    public VendorAdvancePayment getAdvancePayment() {
        return advancePayment;
    }

    public void setAdvancePayment(VendorAdvancePayment advancePayment) {
        this.advancePayment = advancePayment;
    }

    public List<VendorAdvancePayment> getVendorAdvancePayments() {
        return vendorAdvancePayments;
    }

    public void setVendorAdvancePayments(List<VendorAdvancePayment> vendorAdvancePayments) {
        this.vendorAdvancePayments = vendorAdvancePayments;
    }

    public Integer getAdvancePaymentId() {
        return advancePaymentId;
    }

    public void setAdvancePaymentId(Integer advancePaymentId) {
        this.advancePaymentId = advancePaymentId;
    }

    public BigDecimal getAdvanceAmount() {
        return advanceAmount;
    }

    public void setAdvanceAmount(BigDecimal advanceAmount) {
        this.advanceAmount = advanceAmount;
    }

    public BigDecimal getDuplicatePaidAmount() {
        return duplicatePaidAmount;
    }

    public void setDuplicatePaidAmount(BigDecimal duplicatePaidAmount) {
        this.duplicatePaidAmount = duplicatePaidAmount;
    }

    public List<PaymentRequestQuery> getPaymentRequestQueries() {
        return paymentRequestQueries;
    }

    public void setPaymentRequestQueries(List<PaymentRequestQuery> paymentRequestQueries) {
        this.paymentRequestQueries = paymentRequestQueries;
    }

    public Date getLastQueriedDate() {
        return lastQueriedDate;
    }

    public void setLastQueriedDate(Date lastQueriedDate) {
        this.lastQueriedDate = lastQueriedDate;
    }

    public Date getLastQueryResolvedDate() {
        return lastQueryResolvedDate;
    }

    public void setLastQueryResolvedDate(Date lastQueryResolvedDate) {
        this.lastQueryResolvedDate = lastQueryResolvedDate;
    }

    public String getPaymentCard() {
        return paymentCard;
    }

    public void setPaymentCard(String paymentCard) {
        this.paymentCard = paymentCard;
    }

    public String getCardPaymentTransactionNumber() {
        return cardPaymentTransactionNumber;
    }

    public void setCardPaymentTransactionNumber(String cardPaymentTransactionNumber) {
        this.cardPaymentTransactionNumber = cardPaymentTransactionNumber;
    }

    public Integer getCardPaymentProof() {
        return cardPaymentProof;
    }

    public void setCardPaymentProof(Integer cardPaymentProof) {
        this.cardPaymentProof = cardPaymentProof;
    }

    public String getCardPaymentComment() {
        return cardPaymentComment;
    }

    public void setCardPaymentComment(String cardPaymentComment) {
        this.cardPaymentComment = cardPaymentComment;
    }

    public String getSection206() {
        return section206;
    }

    public void setSection206(String section206) {
        this.section206 = section206;
    }

    public List<Integer> getAdvancePaymentIds() {
        return advancePaymentIds;
    }

    public void setAdvancePaymentIds(List<Integer> advancePaymentIds) {
        this.advancePaymentIds = advancePaymentIds;
    }

    public String getIsSoContractBreach() {
        return isSoContractBreach;
    }

    public void setIsSoContractBreach(String isSoContractBreach) {
        this.isSoContractBreach = isSoContractBreach;
    }

    public Integer getSoContractBreachApprovalDoc() {
        return soContractBreachApprovalDoc;
    }

    public void setSoContractBreachApprovalDoc(Integer soContractBreachApprovalDoc) {
        this.soContractBreachApprovalDoc = soContractBreachApprovalDoc;
    }

    public String getIsPrCCVendor() {
        return isPrCCVendor;
    }

    public void setIsPrCCVendor(String isPrCCVendor) {
        this.isPrCCVendor = isPrCCVendor;
    }

    public String getIsPrEcomParty() {
        return isPrEcomParty;
    }

    public void setIsPrEcomParty(String isPrEcomParty) {
        this.isPrEcomParty = isPrEcomParty;
    }

    public PrProcessMetaData getAllPrProcessMetaData() {
        return allPrProcessMetaData;
    }

    public void setAllPrProcessMetaData(PrProcessMetaData allPrProcessMetaData) {
        this.allPrProcessMetaData = allPrProcessMetaData;
    }

    public List<LdcVendorDomain> getApplicableLdc() {
        return applicableLdc;
    }

    public void setApplicableLdc(List<LdcVendorDomain> applicableLdc) {
        this.applicableLdc = applicableLdc;
    }

    public PaymentRequestMetaDataDomain getPaymentRequestMetaData() {
        return paymentRequestMetaData;
    }

    public void setPaymentRequestMetaData(PaymentRequestMetaDataDomain paymentRequestMetaData) {
        this.paymentRequestMetaData = paymentRequestMetaData;
    }

    public String getPanStatus() {
        return panStatus;
    }

    public void setPanStatus(String panStatus) {
        this.panStatus = panStatus;
    }

    public Date getPanStatusUpdatedAt() {
        return panStatusUpdatedAt;
    }

    public void setPanStatusUpdatedAt(Date panStatusUpdatedAt) {
        this.panStatusUpdatedAt = panStatusUpdatedAt;
    }

    public Date getSection206UpdatedAt() {
        return section206UpdatedAt;
    }

    public void setSection206UpdatedAt(Date section206UpdatedAt) {
        this.section206UpdatedAt = section206UpdatedAt;
    }

    public Boolean getPartialSr() {
        return partialSr;
    }

    public void setPartialSr(Boolean partialSr) {
        this.partialSr = partialSr;
    }
}

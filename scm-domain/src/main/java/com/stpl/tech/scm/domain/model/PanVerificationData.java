package com.stpl.tech.scm.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PanVerificationData {

    @SerializedName("pan")
    String pan;


    @SerializedName("last_name")
    String lastName;

    @SerializedName("full_name")
    String fullName;

    @SerializedName("status")
    String status;

    @SerializedName("aadhaar_seeding_status")
    String aadhaarSeedingStatus;

    @SerializedName("category")
    String category;

    @SerializedName("last_updated")
    String  lastUpdated;

}

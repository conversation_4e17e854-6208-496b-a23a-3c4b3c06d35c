/**
 * 
 */
package com.stpl.tech.scm.domain.vo;

import java.util.Map;

import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.domain.model.InventoryItemDrilldown;
import com.stpl.tech.scm.domain.model.InventoryPrice;

/**
 * <AUTHOR>
 *
 */
public interface ReceivingVO extends InventoryVO {

	public Map<InventoryPrice, Pair<InventoryItemVO, InventoryItemDrilldown>> getReceiving();
}

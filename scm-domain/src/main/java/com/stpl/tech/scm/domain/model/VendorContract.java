package com.stpl.tech.scm.domain.model;

import com.stpl.tech.scm.domain.model.PriceStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VendorContract {
    private int vendorId;
    private int vendorContractId;
    private int documentId;
    private int digitalSignID;
    private int authDigitalSignID;
    private int templateId;
    private Date startDate;
    private Date endDate;
    private PriceStatus status;
    private List<SkuPriceDetail> skuPriceDetailList;
    private String employeeName;
    private String vendorUserName;
    private String vendorUserDesignation;
    private Integer employeeId;
    private String ipAddress;
    private String authIpAddress;
    private String otpVerified;
    private String contractRequestedBy;
    private String token;
}

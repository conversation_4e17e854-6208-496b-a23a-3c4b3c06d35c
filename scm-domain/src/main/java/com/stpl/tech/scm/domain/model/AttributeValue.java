//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.12 at 01:35:23 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for AttributeValue complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="AttributeValue"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="attributeValueId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="attributeDefinitionId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="attributeValue" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="attributeValueShortCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="attributeValueStatus" type="{http://www.w3schools.com}SwitchStatus"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AttributeValue", propOrder = {
    "attributeValueId",
    "attributeDefinitionId",
    "attributeValue",
    "attributeValueShortCode",
    "attributeValueStatus"
})
public class AttributeValue {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer attributeValueId;
    protected int attributeDefinitionId;
    @XmlElement(required = true)
    protected String attributeValue;
    @XmlElement(required = true)
    protected String attributeValueShortCode;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SwitchStatus attributeValueStatus;

    /**
     * Gets the value of the attributeValueId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getAttributeValueId() {
        return attributeValueId;
    }

    /**
     * Sets the value of the attributeValueId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setAttributeValueId(Integer value) {
        this.attributeValueId = value;
    }

    /**
     * Gets the value of the attributeDefinitionId property.
     * 
     */
    public int getAttributeDefinitionId() {
        return attributeDefinitionId;
    }

    /**
     * Sets the value of the attributeDefinitionId property.
     * 
     */
    public void setAttributeDefinitionId(int value) {
        this.attributeDefinitionId = value;
    }

    /**
     * Gets the value of the attributeValue property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAttributeValue() {
        return attributeValue;
    }

    /**
     * Sets the value of the attributeValue property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAttributeValue(String value) {
        this.attributeValue = value;
    }

    /**
     * Gets the value of the attributeValueShortCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAttributeValueShortCode() {
        return attributeValueShortCode;
    }

    /**
     * Sets the value of the attributeValueShortCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAttributeValueShortCode(String value) {
        this.attributeValueShortCode = value;
    }

    /**
     * Gets the value of the attributeValueStatus property.
     * 
     * @return
     *     possible object is
     *     {@link SwitchStatus }
     *     
     */
    public SwitchStatus getAttributeValueStatus() {
        return attributeValueStatus;
    }

    /**
     * Sets the value of the attributeValueStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link SwitchStatus }
     *     
     */
    public void setAttributeValueStatus(SwitchStatus value) {
        this.attributeValueStatus = value;
    }

}

package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessCostCenterMappingDomain {
    private Integer mappingId;
    private Integer costCenterId;
    private String ccName;
    private Integer businessCostCenterId;
    private String bccName;
    private String mappingStatus;
}

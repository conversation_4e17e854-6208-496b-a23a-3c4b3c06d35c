//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.12 at 01:35:23 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;

/*
    Created By : <PERSON><PERSON><PERSON><PERSON><PERSON>
 */

@XmlType(name = "TransferOrderNoteType")
@XmlEnum
public enum TransferOrderNoteType {

    CREDIT_NOTE("CN"), DEBIT_NOTE("DN");

    private final String shortCode;

    TransferOrderNoteType(String shortCode){
        this.shortCode = shortCode;
    }

    public String value() {
        return name();
    }

    public static TransferOrderNoteType fromValue(String v) {
        return valueOf(v);
    }

    public String getShortCode() { return shortCode; }

}

package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.Date;

public class ProductProjectionsUnitDetail {
    private Date startDate;
    private Date endDate;
    private String brandCode;
    private String orderSource;
    private Integer unitId;
    private String unitName;
    private String unitCostCenter;
    private Integer fulfillmentUnit;
    private String status;
    private Integer salesClonedFrom;
    private Date cafeOpeningDate;
    private Date businessDate;
    private Integer menuProductId;
    private String menuProductName;
    private String menuProductCategory;
    private Integer menuProductCategoryId;
    private String menuProductSubCategory;
    private String dimension;
    private BigDecimal quantity;
    private BigDecimal price;
    private BigDecimal sales;
    private Integer recipeId;
    private Integer scmProductId;
    private String scmProductName;
    private String scmProductCategory;
    private String scmProductSubCategory;
    private String uom;
    private Integer scmRecipeId;

    public ProductProjectionsUnitDetail() {
    }

    public ProductProjectionsUnitDetail(Integer unitId, Integer menuProductId, String menuProductName, Integer menuProductCategoryId, String dimension,BigDecimal quantity) {
        this.unitId = unitId;
        this.menuProductId = menuProductId;
        this.menuProductName = menuProductName;
        this.menuProductCategoryId = menuProductCategoryId;
        this.dimension = dimension;
        this.quantity = quantity;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getBrandCode() {
        return brandCode;
    }

    public void setBrandCode(String brandCode) {
        this.brandCode = brandCode;
    }

    public String getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(String orderSource) {
        this.orderSource = orderSource;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getUnitCostCenter() {
        return unitCostCenter;
    }

    public void setUnitCostCenter(String unitCostCenter) {
        this.unitCostCenter = unitCostCenter;
    }

    public Integer getFulfillmentUnit() {
        return fulfillmentUnit;
    }

    public void setFulfillmentUnit(Integer fulfillmentUnit) {
        this.fulfillmentUnit = fulfillmentUnit;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getSalesClonedFrom() {
        return salesClonedFrom;
    }

    public void setSalesClonedFrom(Integer salesClonedFrom) {
        this.salesClonedFrom = salesClonedFrom;
    }

    public Date getCafeOpeningDate() {
        return cafeOpeningDate;
    }

    public void setCafeOpeningDate(Date cafeOpeningDate) {
        this.cafeOpeningDate = cafeOpeningDate;
    }

    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    public Integer getMenuProductId() {
        return menuProductId;
    }

    public void setMenuProductId(Integer menuProductId) {
        this.menuProductId = menuProductId;
    }

    public String getMenuProductName() {
        return menuProductName;
    }

    public void setMenuProductName(String menuProductName) {
        this.menuProductName = menuProductName;
    }

    public String getMenuProductCategory() {
        return menuProductCategory;
    }

    public void setMenuProductCategory(String menuProductCategory) {
        this.menuProductCategory = menuProductCategory;
    }

    public Integer getMenuProductCategoryId() {
        return menuProductCategoryId;
    }

    public void setMenuProductCategoryId(Integer menuProductCategoryId) {
        this.menuProductCategoryId = menuProductCategoryId;
    }

    public String getMenuProductSubCategory() {
        return menuProductSubCategory;
    }

    public void setMenuProductSubCategory(String menuProductSubCategory) {
        this.menuProductSubCategory = menuProductSubCategory;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getSales() {
        return sales;
    }

    public void setSales(BigDecimal sales) {
        this.sales = sales;
    }

    public Integer getRecipeId() {
        return recipeId;
    }

    public void setRecipeId(Integer recipeId) {
        this.recipeId = recipeId;
    }

    public Integer getScmProductId() {
        return scmProductId;
    }

    public void setScmProductId(Integer scmProductId) {
        this.scmProductId = scmProductId;
    }

    public String getScmProductName() {
        return scmProductName;
    }

    public void setScmProductName(String scmProductName) {
        this.scmProductName = scmProductName;
    }

    public String getScmProductCategory() {
        return scmProductCategory;
    }

    public void setScmProductCategory(String scmProductCategory) {
        this.scmProductCategory = scmProductCategory;
    }

    public String getScmProductSubCategory() {
        return scmProductSubCategory;
    }

    public void setScmProductSubCategory(String scmProductSubCategory) {
        this.scmProductSubCategory = scmProductSubCategory;
    }

    public String getUom() {
        return uom;
    }

    public void setUom(String uom) {
        this.uom = uom;
    }

    public Integer getScmRecipeId() {
        return scmRecipeId;
    }

    public void setScmRecipeId(Integer scmRecipeId) {
        this.scmRecipeId = scmRecipeId;
    }
}


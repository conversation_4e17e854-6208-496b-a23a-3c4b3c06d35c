package com.stpl.tech.scm.domain.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SCMProductItem {

    Integer productId;
    String productName;
    String uom;
    BigDecimal quantity;

    List<String> paths = new ArrayList<>();

}

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2017.06.04 at 10:58:30 PM IST
//


package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.scm.domain.vo.ConsumptionVO;
import com.stpl.tech.scm.domain.vo.InventoryItemVO;
import com.stpl.tech.scm.domain.vo.ReceivingVO;


/**
 * <p>Java class for VendorGR complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="VendorGR"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="generationTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="deliveryUnitId" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="generatedForVendor" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="generatedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="billAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="extraCharges" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="receiptNumber" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="receiptType" type="{http://www.w3schools.com}GRDocType"/&gt;
 *         &lt;element name="status" type="{http://www.w3schools.com}PurchaseOrderStatus"/&gt;
 *         &lt;element name="grCreationType" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="totalTaxes" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="dispatchLocation" type="{http://www.w3schools.com}VendorDispatchLocation"/&gt;
 *         &lt;element name="purchaseOrderList" type="{http://www.w3schools.com}PurchaseOrder" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="grItems" type="{http://www.w3schools.com}VendorGRItem" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "VendorGR", propOrder = {
    "id",
    "generationTime",
    "deliveryUnitId",
    "generatedForVendor",
    "generatedBy",
    "billAmount",
    "extraCharges",
    "receiptNumber",
    "receiptType",
    "status",
    "grCreationType",
    "totalTaxes",
    "dispatchLocation",
    "purchaseOrderList",
    "grItems"
})
public class VendorGR extends AbstractInventoryVO implements ReceivingVO, ConsumptionVO{

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer id;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date generationTime;
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date updationTime;
    @XmlElement(required = true)
    protected UnitBasicDetail deliveryUnitId;
    @XmlElement(required = true, nillable = true)
    protected VendorBasicDetail generatedForVendor;
    @XmlElement(required = true)
    protected IdCodeName generatedBy;
    @XmlElement(required = true)
    protected IdCodeName approvedBy;
    @XmlElement(required = true)
    protected IdCodeName updatedBy;
    @XmlElement(required = true, nillable = true)
    protected BigDecimal billAmount;
    @XmlElement(required = true, nillable = true)
    protected BigDecimal extraCharges;
    @XmlElement(required = true, nillable = true)
    protected String receiptNumber;
    @XmlElement(required = true, nillable = true)
    @XmlSchemaType(name = "string")
    protected InvoiceDocType receiptType;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected PurchaseOrderStatus status;
    @XmlElement(required = true)
    protected String grCreationType;
    @XmlElement(required = true)
    protected BigDecimal totalTaxes;
    @XmlElement(required = true)
    protected VendorDispatchLocation dispatchLocation;
    protected List<PurchaseOrder> purchaseOrderList;
    protected List<VendorGRItem> grItems;

    protected boolean amountMatched;
    protected DocumentDetail grDocument;
    protected Date grDocumentDate;
    protected boolean invalid = false;
    protected int companyId;
    protected Integer paymentRequestId;
    protected String canBeCancelled;
    protected Boolean toBePaid;
    protected VendorGrType vendorGrType;
    protected List<VendorPoGRItems> vendorPoGRItems;
    protected String type;
    protected DocumentDetail debitNote;
    protected Boolean validForPR;
    protected String comment;
    protected VendorAdvancePayment vendorAdvancePayment;
    protected List<VendorAdvancePayment> vendorAdvancePayments;
    protected List<Integer> advanceGrs;
    protected String vendorInvoiceDocId;


    /**
     * Gets the value of the id property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setId(Integer value) {
        this.id = value;
    }

    /**
     * Gets the value of the updationTime property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getUpdationTime() {
        return updationTime;
    }

    /**
     * Sets the value of the updationTime property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUpdationTime(Date value) {
        this.updationTime = value;
    }

    /**
     * Gets the value of the generationTime property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getGenerationTime() {
        return generationTime;
    }

    /**
     * Sets the value of the generationTime property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setGenerationTime(Date value) {
        this.generationTime = value;
    }

    /**
     * Gets the value of the deliveryUnitId property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public UnitBasicDetail getDeliveryUnitId() {
        return deliveryUnitId;
    }

    /**
     * Sets the value of the deliveryUnitId property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setDeliveryUnitId(UnitBasicDetail value) {
        this.deliveryUnitId = value;
    }

    /**
     * Gets the value of the generatedForVendor property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public VendorBasicDetail getGeneratedForVendor() {
        return generatedForVendor;
    }

    /**
     * Sets the value of the generatedForVendor property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setGeneratedForVendor(VendorBasicDetail value) {
        this.generatedForVendor = value;
    }

    /**
     * Gets the value of the generatedBy property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getGeneratedBy() {
        return generatedBy;
    }

    /**
     * Sets the value of the generatedBy property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setGeneratedBy(IdCodeName value) {
        this.generatedBy = value;
    }


    /**
     * Gets the value of the updatedBy property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getUpdatedBy() {
        return updatedBy;
    }

    /**
     * Sets the value of the updatedBy property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setUpdatedBy(IdCodeName value) {
        this.updatedBy = value;
    }



    /**
     * Gets the value of the billAmount property.
     *
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *
     */
    public BigDecimal getBillAmount() {
        return billAmount;
    }

    /**
     * Sets the value of the billAmount property.
     *
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *
     */
    public void setBillAmount(BigDecimal value) {
        this.billAmount = value;
    }

    /**
     * Gets the value of the extraCharges property.
     *
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *
     */
    public BigDecimal getExtraCharges() {
        return extraCharges;
    }

    /**
     * Sets the value of the extraCharges property.
     *
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *
     */
    public void setExtraCharges(BigDecimal value) {
        this.extraCharges = value;
    }

    /**
     * Gets the value of the receiptNumber property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getReceiptNumber() {
        return receiptNumber;
    }

    /**
     * Sets the value of the receiptNumber property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setReceiptNumber(String value) {
        this.receiptNumber = value;
    }

    /**
     * Gets the value of the receiptType property.
     *
     * @return
     *     possible object is
     *     {@link InvoiceDocType }
     *
     */
    public InvoiceDocType getReceiptType() {
        return receiptType;
    }

    /**
     * Sets the value of the receiptType property.
     *
     * @param value
     *     allowed object is
     *     {@link InvoiceDocType }
     *
     */
    public void setReceiptType(InvoiceDocType value) {
        this.receiptType = value;
    }

    /**
     * Gets the value of the status property.
     *
     * @return
     *     possible object is
     *     {@link PurchaseOrderStatus }
     *
     */
    public PurchaseOrderStatus getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     *
     * @param value
     *     allowed object is
     *     {@link PurchaseOrderStatus }
     *
     */
    public void setStatus(PurchaseOrderStatus value) {
        this.status = value;
    }

    /**
     * Gets the value of the grCreationType property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getGrCreationType() {
        return grCreationType;
    }

    /**
     * Sets the value of the grCreationType property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setGrCreationType(String value) {
        this.grCreationType = value;
    }

    /**
     * Gets the value of the totalTaxes property.
     *
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *
     */
    public BigDecimal getTotalTaxes() {
        return totalTaxes;
    }

    /**
     * Sets the value of the totalTaxes property.
     *
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *
     */
    public void setTotalTaxes(BigDecimal value) {
        this.totalTaxes = value;
    }

    /**
     * Gets the value of the dispatchLocation property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public VendorDispatchLocation getDispatchLocation() {
        return dispatchLocation;
    }

    /**
     * Sets the value of the dispatchLocation property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setDispatchLocation(VendorDispatchLocation value) {
        this.dispatchLocation = value;
    }

    /**
     * Gets the value of the purchaseOrderList property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the purchaseOrderList property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPurchaseOrderList().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PurchaseOrder }
     *
     *
     */
    public List<PurchaseOrder> getPurchaseOrderList() {
        if (purchaseOrderList == null) {
            purchaseOrderList = new ArrayList<PurchaseOrder>();
        }
        return this.purchaseOrderList;
    }

    /**
     * Gets the value of the grItems property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the grItems property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getGrItems().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link VendorGRItem }
     *
     *
     */
    public List<VendorGRItem> getGrItems() {
        if (grItems == null) {
            grItems = new ArrayList<VendorGRItem>();
        }
        return this.grItems;
    }


    public DocumentDetail getGrDocument() {
        return grDocument;
    }

    public void setGrDocument(DocumentDetail grDocument) {
        this.grDocument = grDocument;
    }

    public Date getGrDocumentDate() {
        return grDocumentDate;
    }

    public void setGrDocumentDate(Date grDocumentDate) {
        this.grDocumentDate = grDocumentDate;
    }

    public boolean isAmountMatched() {
        return amountMatched;
    }

    public void setAmountMatched(boolean amountMatched) {
        this.amountMatched = amountMatched;
    }

	/* (non-Javadoc)
	 * @see com.stpl.tech.scm.domain.vo.InventoryVO#getKeyId()
	 */
	@Override
	@JsonIgnore
	public int getKeyId() {
		return this.id;
	}

	/* (non-Javadoc)
	 * @see com.stpl.tech.scm.domain.vo.InventoryVO#getKeyType()
	 */
	@Override
	@JsonIgnore
	public StockEventType getKeyType() {
		return StockEventType.VENDOR_RECEIVED;
	}

	/* (non-Javadoc)
	 * @see com.stpl.tech.scm.domain.vo.InventoryVO#getUnitId()
	 */
	@Override
	@JsonIgnore
	public int getUnitId() {
		return this.deliveryUnitId.getId();
	}

	/* (non-Javadoc)
	 * @see com.stpl.tech.scm.domain.vo.InventoryVO#getInventoryType()
	 */
	@Override
	@JsonIgnore
	public PriceUpdateEntryType getInventoryType() {
		return PriceUpdateEntryType.SKU;
	}

	/* (non-Javadoc)
	 * @see com.stpl.tech.scm.domain.vo.InventoryVO#getInventoryItems()
	 */
	@Override
	@JsonIgnore
	public List<InventoryItemVO> getInventoryItems() {
		return new ArrayList<>(this.grItems);
	}

    public boolean isInvalid() {
        return invalid;
    }

    public void setInvalid(boolean invalid) {
        this.invalid = invalid;
    }

	public int getCompanyId() {
		return companyId;
	}

	public void setCompanyId(int companyId) {
		this.companyId = companyId;
	}

    public Integer getPaymentRequestId() {
        return paymentRequestId;
    }

    public void setPaymentRequestId(Integer paymentRequestId) {
        this.paymentRequestId = paymentRequestId;
    }

    public String getCanBeCancelled() {
        return canBeCancelled;
    }

    public void setCanBeCancelled(String canBeCancelled) {
        this.canBeCancelled = canBeCancelled;
    }

    public Boolean getToBePaid() {
        return toBePaid;
    }

    public void setToBePaid(Boolean toBePaid) {
        this.toBePaid = toBePaid;
    }

    public VendorGrType getVendorGrType() {
        return vendorGrType;
    }

    public void setVendorGrType(VendorGrType vendorGrType) {
        this.vendorGrType = vendorGrType;
    }

	@Override
	public boolean isAssetOrder() {
		return vendorGrType.isAsset();
	}

    public List<VendorPoGRItems> getVendorPoGRItems() {
        return vendorPoGRItems;
    }

    public void setVendorPoGRItems(List<VendorPoGRItems> vendorPoGRItems) {
        this.vendorPoGRItems = vendorPoGRItems;
    }

    public IdCodeName getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(IdCodeName approvedBy) {
        this.approvedBy = approvedBy;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public DocumentDetail getDebitNote() {
        return debitNote;
    }

    public void setDebitNote(DocumentDetail debitNote) {
        this.debitNote = debitNote;
    }

    public void setPurchaseOrderList(List<PurchaseOrder> purchaseOrderList) {
        this.purchaseOrderList = purchaseOrderList;
    }

    public void setGrItems(List<VendorGRItem> grItems) {
        this.grItems = grItems;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Boolean getValidForPR() {
        return validForPR;
    }

    public void setValidForPR(Boolean validForPR) {
        this.validForPR = validForPR;
    }

    public VendorAdvancePayment getVendorAdvancePayment() {
        return vendorAdvancePayment;
    }

    public void setVendorAdvancePayment(VendorAdvancePayment vendorAdvancePayment) {
        this.vendorAdvancePayment = vendorAdvancePayment;
    }

    public List<Integer> getAdvanceGrs() {
        return advanceGrs;
    }

    public void setAdvanceGrs(List<Integer> advanceGrs) {
        this.advanceGrs = advanceGrs;
    }

    public List<VendorAdvancePayment> getVendorAdvancePayments() {
        return vendorAdvancePayments;
    }

    public void setVendorAdvancePayments(List<VendorAdvancePayment> vendorAdvancePayments) {
        this.vendorAdvancePayments = vendorAdvancePayments;
    }

    public String getVendorInvoiceDocId() {
        return vendorInvoiceDocId;
    }

    public void setVendorInvoiceDocId(String vendorInvoiceDocId) {
        this.vendorInvoiceDocId = vendorInvoiceDocId;
    }
}

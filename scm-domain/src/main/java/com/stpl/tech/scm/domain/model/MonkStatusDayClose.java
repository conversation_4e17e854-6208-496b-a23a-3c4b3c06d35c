package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MonkStatusDayClose {
    
    private Long monkStatusDayCloseId;
    private Long eventStatusId;
    private String monkName;
    private String monkStatus;
    private Boolean monkDayCloseEnabled;
    
    public enum MonkStatus {
        INITIATED("Initiated"),
        COMPLETED("Completed"),
        MONK_DOWN("Monk Down"),
        FAILED("Failed");
        
        private final String displayName;
        
        MonkStatus(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
}

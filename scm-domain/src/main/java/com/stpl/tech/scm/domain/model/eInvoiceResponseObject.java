package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.math.BigInteger;
import java.util.List;

@AllArgsConstructor
@Getter
@Setter
public class eInvoiceResponseObject {

    protected BigInteger totalTransferOrderCount;
    protected List<PendingTransferOrder> pendingTransferOrders;
    protected List<PendingTransferOrder> partiallyCompleteTransferOrders;
    protected List<PendingTransferOrder> lostExcelCompleteTransferOrders;

}

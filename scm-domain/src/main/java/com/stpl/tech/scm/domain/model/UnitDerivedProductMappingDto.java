package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UnitDerivedProductMappingDto {

    private Integer mappingId;

    private Integer unitId;
    private String unitName;
    private String unitCategory;

    private Integer productId;
    private String productName;
    private String productCategory;

    private String fulfillmentType;

    private String status;

}

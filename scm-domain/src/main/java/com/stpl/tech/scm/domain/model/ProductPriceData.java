/**
 * 
 */
package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;

/**
 * VO class that holds product prices, uom and the status
 * 
 * <AUTHOR>
 *
 */

public class ProductPriceData {

	private int productId;
	private String name;
	private String uom;
	private BigDecimal price;
	private ProductStatus status;

	/**
	 * @return the productId
	 */
	public int getProductId() {
		return productId;
	}

	/**
	 * @param productId
	 *            the productId to set
	 */
	public void setProductId(int productId) {
		this.productId = productId;
	}

	/**
	 * @return the upm
	 */
	public String getUom() {
		return uom;
	}

	/**
	 * @param upm
	 *            the upm to set
	 */
	public void setUom(String upm) {
		this.uom = upm;
	}

	/**
	 * @return the price
	 */
	public BigDecimal getPrice() {
		return price;
	}

	/**
	 * @param price
	 *            the price to set
	 */
	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	/**
	 * @return the name
	 */
	public String getName() {
		return name;
	}

	/**
	 * @param name
	 *            the name to set
	 */
	public void setName(String name) {
		this.name = name;
	}

	/**
	 * @return the status
	 */
	public ProductStatus getStatus() {
		return status;
	}

	/**
	 * @param status
	 *            the status to set
	 */
	public void setStatus(ProductStatus status) {
		this.status = status;
	}

}

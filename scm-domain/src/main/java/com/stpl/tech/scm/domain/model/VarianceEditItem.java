/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VarianceEditItem {
    private Integer stockingId;
    private Boolean isEdited;
    private Integer productId;
    private String productName;
    private BigDecimal expectedClosing;
    private BigDecimal actualClosing;
    private BigDecimal originalClosing;
    private BigDecimal finalClosing;
    private BigDecimal variance;
    private BigDecimal originalVariance;
    private BigDecimal finalVariance;
    private BigDecimal varianceCost;
    private BigDecimal originalVarianceCost;
    private BigDecimal variancePrice;
    private BigDecimal varianceTax;
    private BigDecimal originalVarianceTax;
    private String unitOfMeasure;
    private BigDecimal finalQuantity;
}

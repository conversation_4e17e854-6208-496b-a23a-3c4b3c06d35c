package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;

public class VendorDebitBalanceVO {
    private Integer vendorId;
    private String entityName;
    private BigDecimal debitBalance;
    private String companyName;
    private int companyId;
    private String vendorStatus;
    public VendorDebitBalanceVO() {
    }
    
	public VendorDebitBalanceVO(Integer vendorId, String entityName, BigDecimal debitBalance, String companyName,
			int companyId) {
		super();
		this.vendorId = vendorId;
		this.entityName = entityName;
		this.debitBalance = debitBalance;
		this.companyName = companyName;
		this.companyId = companyId;
	}

	public Integer getVendorId() {
        return vendorId;
    }

    public void setVendorId(Integer vendorId) {
        this.vendorId = vendorId;
    }

    public String getEntityName() {
        return entityName;
    }

    public void setEntityName(String entityName) {
        this.entityName = entityName;
    }

    public BigDecimal getDebitBalance() {
        return debitBalance;
    }

    public void setDebitBalance(BigDecimal debitBalance) {
        this.debitBalance = debitBalance;
    }

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public int getCompanyId() {
		return companyId;
	}

	public void setCompanyId(int companyId) {
		this.companyId = companyId;
	}

    public String getVendorStatus() {
        return vendorStatus;
    }

    public void setVendorStatus(String vendorStatus) {
        this.vendorStatus = vendorStatus;
    }
}

package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CorrectedSalesInvoiceItemDetails  {


    private Integer itemId;
    private Integer skuId;
    private String skuName;
    private BigDecimal price;
    private BigDecimal revisedPrice;
    private BigDecimal correctedPrice;
    private BigDecimal pkgQty;
    private BigDecimal revisedPkgQty;
    private BigDecimal correctedPkgQty;
    private BigDecimal amount;
    private BigDecimal revisedAmount;
    private BigDecimal tax;
    private BigDecimal revisedTax;
    private String taxCode;

    protected List<InventoryItemDrilldown> drillDowns;


}

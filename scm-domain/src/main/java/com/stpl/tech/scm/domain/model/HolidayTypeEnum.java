package com.stpl.tech.scm.domain.model;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Enum representing different types of holidays for demand forecasting
 */
public enum HolidayTypeEnum {
    CHAAYOS_BIG_DAY("Chaayos Big Day"),
    FESTIVE_DAY("Festive Day"),
    SALE_IGNORE_DAYS("Sale Ignore Days");
    
    private final String description;
    
    HolidayTypeEnum(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * Get all holiday type strings as a list
     * @return List of all holiday type strings
     */
    public static List<String> getAllHolidayTypeStrings() {
        return Arrays.stream(HolidayTypeEnum.values())
                .map(Enum::name)
                .collect(Collectors.toList());
    }
}

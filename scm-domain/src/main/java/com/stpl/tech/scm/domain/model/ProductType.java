package com.stpl.tech.scm.domain.model;

public enum ProductType {

    HOT,
    FOOD,
    COLD,
    HOUSE_KEEPING,
    SEMI_FINISHED,
    POS,
    BAKERY,
    GNT,
    COD_ASSEMBLY,
    MERCHANDISE,
    DISCONTINUED,
    PACKAGING,
    STATIONERY,
    SMALL_UTENSILS_EQPMT,
    MARKETING,
    OTHERS;

    public String value() {
        return name();
    }

    public static ProductType fromValue(String v) {
        return valueOf(v);
    }
}

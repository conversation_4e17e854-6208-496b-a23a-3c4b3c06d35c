package com.stpl.tech.scm.domain.model;

import java.util.Map;

public class DayWiseExpiryProduct {

    private Map<Integer, Map<String ,Float>> inStock;
    private Map<Integer, Map<String ,Float>> inTransit;
    private  Map<String, Map<Integer, Map<String, Float>>> acknowledgedRoInTransit;
    private Map<Integer, Map<String ,Float>> totalStock;

    public Map<Integer, Map<String, Float>> getInStock() {
        return inStock;
    }

    public void setInStock(Map<Integer, Map<String, Float>> inStock) {
        this.inStock = inStock;
    }

    public Map<Integer, Map<String, Float>> getInTransit() {
        return inTransit;
    }

    public void setInTransit(Map<Integer, Map<String, Float>> inTransit) {
        this.inTransit = inTransit;
    }

    public Map<String, Map<Integer, Map<String, Float>>> getAcknowledgedRoInTransit() {
        return acknowledgedRoInTransit;
    }

    public void setAcknowledgedRoInTransit(Map<String, Map<Integer, Map<String, Float>>> acknowledgedRoInTransit) {
        this.acknowledgedRoInTransit = acknowledgedRoInTransit;
    }

    public Map<Integer, Map<String, Float>> getTotalStock() {
        return totalStock;
    }

    public void setTotalStock(Map<Integer, Map<String, Float>> totalStock) {
        this.totalStock = totalStock;
    }
}

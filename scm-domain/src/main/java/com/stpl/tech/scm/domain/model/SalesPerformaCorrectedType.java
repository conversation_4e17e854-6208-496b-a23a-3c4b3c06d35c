package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;

@XmlType(name = "SalesPerformaCorrectedType")
@XmlEnum
public enum SalesPerformaCorrectedType {

    CREDIT_NOTE,
    DEBIT_NOTE;


    public String value() {
        return name();
    }

    public static SalesPerformaCorrectedType fromValue(String v) {
        return valueOf(v);
    }
}

package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;

public class GatepassItemAssetMapping  {

	protected Integer gatePassItemAssetId;

	protected Integer gatePassId;

	protected Integer gatePassItemId;

	@XmlElement(required = true)
	protected Integer assetId;

	@XmlElement(required = true)
	protected String assetTagValue;

	@XmlSchemaType(name = "string")
	protected GatepassOperationType gatePassType;

	protected boolean fixed;

	protected String isReturned;

	public Integer getGatePassItemAssetId() {
		return gatePassItemAssetId;
	}

	public void setGatePassItemAssetId(Integer gatePassItemAssetId) {
		this.gatePassItemAssetId = gatePassItemAssetId;
	}

	public Integer getGatePassId() {
		return gatePassId;
	}

	public void setGatePassId(Integer gatePassId) {
		this.gatePassId = gatePassId;
	}

	public Integer getGatePassItemId() {
		return gatePassItemId;
	}

	public void setGatePassItemId(Integer gatePassItemId) {
		this.gatePassItemId = gatePassItemId;
	}

	public Integer getAssetId() {
		return assetId;
	}

	public void setAssetId(Integer assetId) {
		this.assetId = assetId;
	}

	public String getAssetTagValue() {
		return assetTagValue;
	}

	public void setAssetTagValue(String assetTagValue) {
		this.assetTagValue = assetTagValue;
	}

	public GatepassOperationType getGatePassType() {
		return gatePassType;
	}

	public void setGatePassType(GatepassOperationType gatePassType) {
		this.gatePassType = gatePassType;
	}

	public boolean isFixed() {
		return fixed;
	}

	public void setFixed(boolean fixed) {
		this.fixed = fixed;
	}

	public String getIsReturned() {
		return isReturned;
	}

	public void setIsReturned(String isReturned) {
		this.isReturned = isReturned;
	}
}

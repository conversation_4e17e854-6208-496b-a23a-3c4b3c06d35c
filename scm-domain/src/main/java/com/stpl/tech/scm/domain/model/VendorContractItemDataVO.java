package com.stpl.tech.scm.domain.model;

import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.util.AppConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class VendorContractItemDataVO {


    private Integer keyId;
    private Integer contractItemId;
    private Integer contractId;
    private Integer workOrderId;

    private Integer vendorId;

    private IdCodeName skuId;

    private IdCodeName skuPackagingId;

    private PackagingData packagingData;

    private String dispatchLocation;

    private String deliveryLocation;

    private Integer dispatchLocationId;

    private Integer deliveryLocationId;

    private BigDecimal updatedPrice;

    private  BigDecimal currentPrice;

    private String status;

    private String rejectionReason;

    private String selectedRejectionReason;

    private Integer updatedBy;

    private Integer skuPriceDataId;

    private Integer documentId;

    private BigDecimal taxPercentage;

    private String isStatusActive = AppConstants.NO;

    private String isNew;

    private String isNewItem;

    private List<Integer> unitIds;

    private List<IdName> unitDetails;

    private IdCodeName vendor;

    private String skuPriceState;


}

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.09.11 at 11:20:21 AM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.Date;


/**
 * <p>Java class for InvoiceDeviationMapping complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="InvoiceDeviationMapping"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="mappingId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="paymentDeviation" type="{http://www.w3schools.com}PaymentDeviation"/&gt;
 *         &lt;element name="currentStatus" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="createdBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="acceptedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="rejectedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="removedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="deviationRemark" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="actionRemark" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="deviationItemId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="deviationItemType" type="{http://www.w3schools.com}PaymentDeviationLevel"/&gt;
 *         &lt;element name="actionTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "InvoiceDeviationMapping", propOrder = {
    "mappingId",
    "paymentDeviation",
    "currentStatus",
    "createdBy",
    "acceptedBy",
    "rejectedBy",
    "removedBy",
    "deviationRemark",
    "actionRemark",
    "deviationItemId",
    "deviationItemType",
    "actionTime"
})
public class InvoiceDeviationMapping {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer mappingId;
    @XmlElement(required = true)
    protected PaymentDeviation paymentDeviation;
    @XmlElement(required = true)
    protected String currentStatus;
    @XmlElement(required = true)
    protected IdCodeName createdBy;
    @XmlElement(required = true)
    protected IdCodeName acceptedBy;
    @XmlElement(required = true)
    protected IdCodeName rejectedBy;
    @XmlElement(required = true)
    protected IdCodeName removedBy;
    @XmlElement(required = true)
    protected String deviationRemark;
    @XmlElement(required = true)
    protected String actionRemark;
    protected int deviationItemId;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected PaymentDeviationLevel deviationItemType;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date actionTime;

    /**
     * Gets the value of the mappingId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getMappingId() {
        return mappingId;
    }

    /**
     * Sets the value of the mappingId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setMappingId(Integer value) {
        this.mappingId = value;
    }

    /**
     * Gets the value of the paymentDeviation property.
     * 
     * @return
     *     possible object is
     *     {@link PaymentDeviation }
     *     
     */
    public PaymentDeviation getPaymentDeviation() {
        return paymentDeviation;
    }

    /**
     * Sets the value of the paymentDeviation property.
     * 
     * @param value
     *     allowed object is
     *     {@link PaymentDeviation }
     *     
     */
    public void setPaymentDeviation(PaymentDeviation value) {
        this.paymentDeviation = value;
    }

    /**
     * Gets the value of the currentStatus property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCurrentStatus() {
        return currentStatus;
    }

    /**
     * Sets the value of the currentStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCurrentStatus(String value) {
        this.currentStatus = value;
    }

    /**
     * Gets the value of the createdBy property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getCreatedBy() {
        return createdBy;
    }

    /**
     * Sets the value of the createdBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setCreatedBy(IdCodeName value) {
        this.createdBy = value;
    }

    /**
     * Gets the value of the acceptedBy property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getAcceptedBy() {
        return acceptedBy;
    }

    /**
     * Sets the value of the acceptedBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setAcceptedBy(IdCodeName value) {
        this.acceptedBy = value;
    }

    /**
     * Gets the value of the rejectedBy property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getRejectedBy() {
        return rejectedBy;
    }

    /**
     * Sets the value of the rejectedBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setRejectedBy(IdCodeName value) {
        this.rejectedBy = value;
    }

    /**
     * Gets the value of the removedBy property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getRemovedBy() {
        return removedBy;
    }

    /**
     * Sets the value of the removedBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setRemovedBy(IdCodeName value) {
        this.removedBy = value;
    }

    /**
     * Gets the value of the deviationRemark property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getDeviationRemark() {
        return deviationRemark;
    }

    /**
     * Sets the value of the deviationRemark property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setDeviationRemark(String value) {
        this.deviationRemark = value;
    }

    /**
     * Gets the value of the actionRemark property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getActionRemark() {
        return actionRemark;
    }

    /**
     * Sets the value of the actionRemark property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setActionRemark(String value) {
        this.actionRemark = value;
    }

    /**
     * Gets the value of the deviationItemId property.
     *
     */
    public int getDeviationItemId() {
        return deviationItemId;
    }

    /**
     * Sets the value of the deviationItemId property.
     *
     */
    public void setDeviationItemId(int value) {
        this.deviationItemId = value;
    }

    /**
     * Gets the value of the deviationItemType property.
     * 
     * @return
     *     possible object is
     *     {@link PaymentDeviationLevel }
     *     
     */
    public PaymentDeviationLevel getDeviationItemType() {
        return deviationItemType;
    }

    /**
     * Sets the value of the deviationItemType property.
     * 
     * @param value
     *     allowed object is
     *     {@link PaymentDeviationLevel }
     *     
     */
    public void setDeviationItemType(PaymentDeviationLevel value) {
        this.deviationItemType = value;
    }

    /**
     * Gets the value of the actionTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getActionTime() {
        return actionTime;
    }

    /**
     * Sets the value of the actionTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setActionTime(Date value) {
        this.actionTime = value;
    }

}

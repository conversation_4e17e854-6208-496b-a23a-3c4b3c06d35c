package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PoItemLevelSummary {

   Integer purchaseOrderId;
   Integer purchaseOrderItemId;
   Integer skuId;
   String skuName;
   BigDecimal requestedQuantity;
   BigDecimal amountPaid;
   Integer goodsReceivedId;
   Integer goodsReceivedItemId;

   BigDecimal totalAmount;
   String currentStatus;
}

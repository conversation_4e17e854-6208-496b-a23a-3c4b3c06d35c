/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.12 at 03:52:20 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.Date;
import java.util.List;
import java.util.Map;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "StockEventAssetMappingDefinitionRequest", propOrder = {

        "eventId",
        "unitId",
        "status",
        "stockEventAssetMappingDefinitions",
})
public class StockEventAssetMappingDefinitionRequest {


    @XmlElement(required = true)
    protected Integer eventId;

    @XmlElement(required = true)
    protected Integer parentId;
    /*
        UnitId signifies Location of Asset
     */
    @XmlElement(required = true)
    protected Integer unitId;

    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected EventAssetMappingStatus status;

    @XmlElement(required = false)
    protected List<StockEventAssetMappingDefinition> stockEventAssetMappingDefinitions;

    protected List<StockEventAssetMappingDefinition> extraScannedItems;

    protected String subCategory;

    protected String deviceInfo;

    protected Map<Integer,Boolean> assetFoundMap;

    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public EventAssetMappingStatus getStatus() {
        return status;
    }

    public void setStatus(EventAssetMappingStatus status) {
        this.status = status;
    }

    public List<StockEventAssetMappingDefinition> getStockEventAssetMappingDefinitions() {
        return stockEventAssetMappingDefinitions;
    }

    public void setStockEventAssetMappingDefinitions(
            List<StockEventAssetMappingDefinition> stockEventAssetMappingDefinitions) {
        this.stockEventAssetMappingDefinitions = stockEventAssetMappingDefinitions;
    }

    public List<StockEventAssetMappingDefinition> getExtraScannedItems() {
        return extraScannedItems;
    }

    public void setExtraScannedItems(List<StockEventAssetMappingDefinition> extraScannedItems) {
        this.extraScannedItems = extraScannedItems;
    }

    public String getSubCategory() {
        return subCategory;
    }

    public void setSubCategory(String subCategory) {
        this.subCategory = subCategory;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public String getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(String deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    public Map<Integer, Boolean> getAssetFoundMap() {
        return assetFoundMap;
    }

    public void setAssetFoundMap(Map<Integer, Boolean> assetFoundMap) {
        this.assetFoundMap = assetFoundMap;
    }
}

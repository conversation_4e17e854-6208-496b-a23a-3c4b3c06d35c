//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.07.22 at 06:58:00 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for RequestScmItem complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="RequestScmItem"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="subCategoryName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="suggestedQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="orderingQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="stockAtHand" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="saleQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="packagingName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="conversionRatio" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="packagingQuantity" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="selectedFulfillmentType" type="{http://www.w3schools.com}FulfillmentType"/&gt;
 *         &lt;element name="unitOfMeasure" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RequestScmItem", propOrder = {
    "id",
    "name",
    "subCategoryName",
    "suggestedQuantity",
    "orderingQuantity",
    "stockAtHand",
    "inTransit",
    "saleQuantity",
    "packagingName",
    "conversionRatio",
    "packagingQuantity",
    "selectedFulfillmentType",
    "unitOfMeasure"
})
public class RequestScmItem {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer id;
    @XmlElement(required = true)
    protected String name;
    @XmlElement(required = true)
    protected String subCategoryName;
    protected float suggestedQuantity;
    protected float orderingQuantity;
    @XmlElement(required = true, type = Float.class, nillable = true)
    protected Float stockAtHand;
    @XmlElement(required = true, type = Float.class, nillable = true)
    protected Float inTransit;
    @XmlElement(required = true, type = Float.class, nillable = true)
    protected Float saleQuantity;
    @XmlElement(required = true, nillable = true)
    protected String packagingName;
    @XmlElement(required = true, type = Float.class, nillable = true)
    protected Float conversionRatio;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer packagingQuantity;
    @XmlElement(required = true, nillable = true)
    @XmlSchemaType(name = "string")
    protected FulfillmentType selectedFulfillmentType;
    @XmlElement(required = true)
    protected String unitOfMeasure;
    protected String categoryName;
    protected Boolean critical;
    protected Integer menuProductCount;
    protected String menuProducts;

    /**
     * Gets the value of the id property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setId(Integer value) {
        this.id = value;
    }

    /**
     * Gets the value of the name property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getName() {
        return name;
    }

    /**
     * Sets the value of the name property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setName(String value) {
        this.name = value;
    }

    /**
     * Gets the value of the subCategoryName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSubCategoryName() {
        return subCategoryName;
    }

    /**
     * Sets the value of the subCategoryName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSubCategoryName(String value) {
        this.subCategoryName = value;
    }

    /**
     * Gets the value of the suggestedQuantity property.
     * 
     */
    public float getSuggestedQuantity() {
        return suggestedQuantity;
    }

    /**
     * Sets the value of the suggestedQuantity property.
     * 
     */
    public void setSuggestedQuantity(float value) {
        this.suggestedQuantity = value;
    }

    /**
     * Gets the value of the orderingQuantity property.
     * 
     */
    public float getOrderingQuantity() {
        return orderingQuantity;
    }

    /**
     * Sets the value of the orderingQuantity property.
     * 
     */
    public void setOrderingQuantity(float value) {
        this.orderingQuantity = value;
    }

    /**
     * Gets the value of the stockAtHand property.
     * 
     * @return
     *     possible object is
     *     {@link Float }
     *     
     */
    public Float getStockAtHand() {
        return stockAtHand;
    }

    /**
     * Sets the value of the stockAtHand property.
     * 
     * @param value
     *     allowed object is
     *     {@link Float }
     *     
     */
    public void setStockAtHand(Float value) {
        this.stockAtHand = value;
    }

    /**
     * Gets the value of the saleQuantity property.
     * 
     * @return
     *     possible object is
     *     {@link Float }
     *     
     */
    public Float getSaleQuantity() {
        return saleQuantity;
    }

    /**
     * Sets the value of the saleQuantity property.
     * 
     * @param value
     *     allowed object is
     *     {@link Float }
     *     
     */
    public void setSaleQuantity(Float value) {
        this.saleQuantity = value;
    }

    /**
     * Gets the value of the packagingName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPackagingName() {
        return packagingName;
    }

    /**
     * Sets the value of the packagingName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPackagingName(String value) {
        this.packagingName = value;
    }

    /**
     * Gets the value of the conversionRatio property.
     * 
     * @return
     *     possible object is
     *     {@link Float }
     *     
     */
    public Float getConversionRatio() {
        return conversionRatio;
    }

    /**
     * Sets the value of the conversionRatio property.
     * 
     * @param value
     *     allowed object is
     *     {@link Float }
     *     
     */
    public void setConversionRatio(Float value) {
        this.conversionRatio = value;
    }

    /**
     * Gets the value of the packagingQuantity property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getPackagingQuantity() {
        return packagingQuantity;
    }

    /**
     * Sets the value of the packagingQuantity property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setPackagingQuantity(Integer value) {
        this.packagingQuantity = value;
    }

    /**
     * Gets the value of the selectedFulfillmentType property.
     * 
     * @return
     *     possible object is
     *     {@link FulfillmentType }
     *     
     */
    public FulfillmentType getSelectedFulfillmentType() {
        return selectedFulfillmentType;
    }

    /**
     * Sets the value of the selectedFulfillmentType property.
     * 
     * @param value
     *     allowed object is
     *     {@link FulfillmentType }
     *     
     */
    public void setSelectedFulfillmentType(FulfillmentType value) {
        this.selectedFulfillmentType = value;
    }

    /**
     * Gets the value of the unitOfMeasure property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    /**
     * Sets the value of the unitOfMeasure property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUnitOfMeasure(String value) {
        this.unitOfMeasure = value;
    }

	public Float getInTransit() {
		return inTransit;
	}

	public void setInTransit(Float inTransit) {
		this.inTransit = inTransit;
	}

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public Boolean getCritical() {
        return critical;
    }

    public void setCritical(Boolean critical) {
        this.critical = critical;
    }

    public Integer getMenuProductCount() {
        return menuProductCount;
    }

    public void setMenuProductCount(Integer menuProductCount) {
        this.menuProductCount = menuProductCount;
    }

    public String getMenuProducts() {
        return menuProducts;
    }

    public void setMenuProducts(String menuProducts) {
        this.menuProducts = menuProducts;
    }
}

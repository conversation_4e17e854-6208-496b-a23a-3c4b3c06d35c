//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.07.21 at 12:38:28 PM IST 
//


package com.stpl.tech.scm.domain.model;

import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for SCMOrderPackaging complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="SCMOrderPackaging"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="packagingDefinitionData" type="{http://www.w3schools.com}PackagingDefinition"/&gt;
 *         &lt;element name="conversionRatio" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="numberOfUnitsPacked" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="numberOfUnitsReceived" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="transferredQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="receivedQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="transferOrderItemId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="goodsReceivedItemId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="numberOfUnitsRejected" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="RejectionReason" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SCMOrderPackaging", propOrder = {
    "id",
    "packagingDefinitionData",
    "conversionRatio",
    "numberOfUnitsPacked",
    "numberOfUnitsReceived",
    "transferredQuantity",
    "receivedQuantity",
    "transferOrderItemId",
    "goodsReceivedItemId",
    "numberOfUnitsRejected",
    "rejectionReason"
})
public class SCMOrderPackaging {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer id;
    @XmlElement(required = true)
    protected PackagingDefinition packagingDefinitionData;
    protected float conversionRatio;
    protected float numberOfUnitsPacked;
    @XmlElement(required = true, type = Float.class, nillable = true)
    protected Float numberOfUnitsReceived;
    protected float transferredQuantity;
    @XmlElement(required = true, type = Float.class, nillable = true)
    protected Float receivedQuantity;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer transferOrderItemId;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer goodsReceivedItemId;
    @XmlElement(required = true, type = Float.class, nillable = true)
    protected Float numberOfUnitsRejected;
    @XmlElement(name = "RejectionReason", required = true, nillable = true)
    protected String rejectionReason;
    @XmlElement(required = true, type = Float.class, nillable = true)
    protected Float pricePerUnit;
    protected List<InventoryItemDrilldown> expiryDrillDown;

    /**
     * Gets the value of the id property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setId(Integer value) {
        this.id = value;
    }

    /**
     * Gets the value of the packagingDefinitionData property.
     * 
     * @return
     *     possible object is
     *     {@link PackagingDefinition }
     *     
     */
    public PackagingDefinition getPackagingDefinitionData() {
        return packagingDefinitionData;
    }

    /**
     * Sets the value of the packagingDefinitionData property.
     * 
     * @param value
     *     allowed object is
     *     {@link PackagingDefinition }
     *     
     */
    public void setPackagingDefinitionData(PackagingDefinition value) {
        this.packagingDefinitionData = value;
    }

    /**
     * Gets the value of the conversionRatio property.
     *
     */
    public float getConversionRatio() {
        return conversionRatio;
    }

    /**
     * Sets the value of the conversionRatio property.
     *
     */
    public void setConversionRatio(float value) {
        this.conversionRatio = value;
    }

    /**
     * Gets the value of the numberOfUnitsPacked property.
     * 
     */
    public float getNumberOfUnitsPacked() {
        return numberOfUnitsPacked;
    }

    /**
     * Sets the value of the numberOfUnitsPacked property.
     * 
     */
    public void setNumberOfUnitsPacked(float value) {
        this.numberOfUnitsPacked = value;
    }

    /**
     * Gets the value of the numberOfUnitsReceived property.
     * 
     * @return
     *     possible object is
     *     {@link Float }
     *     
     */
    public Float getNumberOfUnitsReceived() {
        return numberOfUnitsReceived;
    }

    /**
     * Sets the value of the numberOfUnitsReceived property.
     * 
     * @param value
     *     allowed object is
     *     {@link Float }
     *     
     */
    public void setNumberOfUnitsReceived(Float value) {
        this.numberOfUnitsReceived = value;
    }

    /**
     * Gets the value of the transferredQuantity property.
     * 
     */
    public float getTransferredQuantity() {
        return transferredQuantity;
    }

    /**
     * Sets the value of the transferredQuantity property.
     * 
     */
    public void setTransferredQuantity(float value) {
        this.transferredQuantity = value;
    }

    /**
     * Gets the value of the receivedQuantity property.
     * 
     * @return
     *     possible object is
     *     {@link Float }
     *     
     */
    public Float getReceivedQuantity() {
        return receivedQuantity;
    }

    /**
     * Sets the value of the receivedQuantity property.
     * 
     * @param value
     *     allowed object is
     *     {@link Float }
     *     
     */
    public void setReceivedQuantity(Float value) {
        this.receivedQuantity = value;
    }

    /**
     * Gets the value of the transferOrderItemId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getTransferOrderItemId() {
        return transferOrderItemId;
    }

    /**
     * Sets the value of the transferOrderItemId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setTransferOrderItemId(Integer value) {
        this.transferOrderItemId = value;
    }

    /**
     * Gets the value of the goodsReceivedItemId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getGoodsReceivedItemId() {
        return goodsReceivedItemId;
    }

    /**
     * Sets the value of the goodsReceivedItemId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setGoodsReceivedItemId(Integer value) {
        this.goodsReceivedItemId = value;
    }

    /**
     * Gets the value of the numberOfUnitsRejected property.
     * 
     * @return
     *     possible object is
     *     {@link Float }
     *     
     */
    public Float getNumberOfUnitsRejected() {
        return numberOfUnitsRejected;
    }

    /**
     * Sets the value of the numberOfUnitsRejected property.
     * 
     * @param value
     *     allowed object is
     *     {@link Float }
     *     
     */
    public void setNumberOfUnitsRejected(Float value) {
        this.numberOfUnitsRejected = value;
    }

    /**
     * Gets the value of the rejectionReason property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRejectionReason() {
        return rejectionReason;
    }

    /**
     * Sets the value of the rejectionReason property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRejectionReason(String value) {
        this.rejectionReason = value;
    }

    public Float getPricePerUnit() {
        return pricePerUnit;
    }

    public void setPricePerUnit(Float pricePerUnit) {
        this.pricePerUnit = pricePerUnit;
    }

	public List<InventoryItemDrilldown> getExpiryDrillDown() {
		return expiryDrillDown;
	}

	public void setExpiryDrillDown(List<InventoryItemDrilldown> expiryDrillDown) {
		this.expiryDrillDown = expiryDrillDown;
	}
    
}

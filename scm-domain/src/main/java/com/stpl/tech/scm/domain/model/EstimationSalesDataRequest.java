package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class EstimationSalesDataRequest {

	private String dayType;
	private Date date;
	private List<SalesAmountRequest> brands;
	private BigDecimal orderingPercentage;



	public String getDayType() {
		return dayType;
	}

	public void setDayType(String dayType) {
		this.dayType = dayType;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}


	public List<SalesAmountRequest> getBrands() {
		return brands;
	}

	public void setBrands(List<SalesAmountRequest> brands) {
		this.brands = brands;
	}

	public BigDecimal getOrderingPercentage() {
		return orderingPercentage;
	}

	public void setOrderingPercentage(BigDecimal orderingPercentage) {
		this.orderingPercentage = orderingPercentage;
	}
}

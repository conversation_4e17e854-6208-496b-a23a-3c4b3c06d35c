<?xml version="1.0"?>
<!DOCTYPE xs:schema [
        <!ELEMENT xs:schema (xs:element|xs:complexType|xs:simpleType)*>
        <!ATTLIST xs:schema
                xmlns:xs CDATA #REQUIRED
                targetNamespace CDATA #REQUIRED
                xmlns CDATA #REQUIRED
                elementFormDefault CDATA #REQUIRED>
        <!ELEMENT xs:element (xs:complexType)*>
        <!ATTLIST xs:element
                default CDATA #IMPLIED
                maxOccurs CDATA #IMPLIED
                minOccurs CDATA #IMPLIED
                name CDATA #REQUIRED
                nillable CDATA #IMPLIED
                type CDATA #IMPLIED>
        <!ELEMENT xs:complexType (xs:sequence)*>
        <!ATTLIST xs:complexType
                name CDATA #IMPLIED>
        <!ELEMENT xs:sequence (xs:element)*>
        <!ELEMENT xs:simpleType (xs:restriction)*>
        <!ATTLIST xs:simpleType
                final CDATA #REQUIRED
                name CDATA #REQUIRED>
        <!ELEMENT xs:restriction (xs:enumeration)*>
        <!ATTLIST xs:restriction
                base CDATA #REQUIRED>
        <!ELEMENT xs:enumeration (#PCDATA)>
        <!ATTLIST xs:enumeration
                value CDATA #REQUIRED>
        ]>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://www.w3schools.com" xmlns="http://www.w3schools.com"
           elementFormDefault="qualified">

    <xs:element name="SCMMetadata">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="attributeTypes" type="AttributeType"
                            minOccurs="1" maxOccurs="unbounded"/>
                <xs:element name="fulFillmentTypes" type="FulfillmentType"
                            minOccurs="1" maxOccurs="unbounded"/>
                <xs:element name="packagingTypes" type="PackagingType"
                            minOccurs="1" maxOccurs="unbounded"/>
                <xs:element name="timeFrequency" type="TimeFrequency"
                            minOccurs="1" maxOccurs="unbounded"/>
                <xs:element name="switchStatus" type="SwitchStatus"
                            minOccurs="1" maxOccurs="unbounded"/>
                <xs:element name="scmOrderStatus" type="SCMOrderStatus"
                            minOccurs="1" maxOccurs="unbounded"/>
                <xs:element name="paymentRequestStatus" type="PaymentRequestStatus"
                            minOccurs="1" maxOccurs="unbounded"/>
                <xs:element name="categoryDefinitions" type="CategoryDefinition"
                            minOccurs="1" maxOccurs="unbounded"/>
                <xs:element name="paymentRequestTypes" type="PaymentRequestType"
                            minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="paymentDeviations" type="PaymentDeviation"
                            minOccurs="0" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="AttributeDefinition">
        <xs:sequence>
            <xs:element name="attributeId" type="xs:int" nillable="true"/>
            <xs:element name="attributeName" type="xs:string"/>
            <xs:element name="attributeType" type="AttributeType"/>
            <xs:element name="attributeCode" type="xs:string"/>
            <xs:element name="attributeShortCode" type="xs:string"/>
            <xs:element name="attributeDescription" type="xs:string"/>
            <xs:element name="attributeStatus" type="SwitchStatus"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="AttributeValue">
        <xs:sequence>
            <xs:element name="attributeValueId" type="xs:int"
                        nillable="true"/>
            <xs:element name="attributeDefinitionId" type="xs:int"/>
            <xs:element name="attributeValue" type="xs:string"/>
            <xs:element name="attributeValueShortCode" type="xs:string"/>
            <xs:element name="attributeValueStatus" type="SwitchStatus"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CategoryDefinition">
        <xs:sequence>
            <xs:element name="categoryId" type="xs:int" nillable="true"/>
            <xs:element name="categoryName" type="xs:string"/>
            <xs:element name="categoryCode" type="xs:string"/>
            <xs:element name="categoryDescription" type="xs:string"/>
            <xs:element name="categoryStatus" type="SwitchStatus"/>
            <xs:element name="subCategories" type="SubCategoryDefinition"
                        minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="SubCategoryDefinition">
        <xs:sequence>
            <xs:element name="subCategoryId" type="xs:int" nillable="true"/>
            <xs:element name="subCategoryName" type="xs:string"/>
            <xs:element name="subCategoryCode" type="xs:string"/>
            <xs:element name="subCategoryDescription" type="xs:string"/>
            <xs:element name="subCategoryStatus" type="SwitchStatus"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CategoryAttributeMapping">
        <xs:sequence>
            <xs:element name="categoryAttributeMappingId" type="xs:int"
                        nillable="true"/>
            <xs:element name="categoryDefinition" type="IdCodeName"/>
            <xs:element name="attributeDefinition" type="IdCodeName"/>
            <xs:element name="mandatory" type="xs:boolean"/>
            <xs:element name="mappingOrder" type="xs:int"/>
            <xs:element name="usedInNaming" type="xs:boolean"/>
            <xs:element name="mappingStatus" type="SwitchStatus"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CategoryAttributeValue">
        <xs:sequence>
            <xs:element name="categoryAttributeValueId" type="xs:int"
                        nillable="true"/>
            <xs:element name="categoryAttributeMappingId" type="xs:int"/>
            <xs:element name="attributeValue" type="IdCodeName"/>
            <xs:element name="mappingStatus" type="SwitchStatus"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PackagingDefinition">
        <xs:sequence>
            <xs:element name="packagingId" type="xs:int" nillable="true"/>
            <xs:element name="packagingType" type="PackagingType"/>
            <xs:element name="packagingCode" type="xs:string"/>
            <xs:element name="packagingName" type="xs:string"/>
            <xs:element name="packagingStatus" type="SwitchStatus"/>
            <xs:element name="conversionRatio" type="xs:float"/>
            <xs:element name="unitOfMeasure" type="xs:string"/>
            <xs:element name="subPackagingId" type="xs:int" nillable="true"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ProductDefinition">
        <xs:sequence>
            <xs:element name="productId" type="xs:int" nillable="true"/>
            <xs:element name="productName" type="xs:string"/>
            <xs:element name="productDescription" type="xs:string"/>
            <xs:element name="categoryDefinition" type="IdCodeName"/>
            <xs:element name="subCategoryDefinition" type="IdCodeName"
                        nillable="true"/>
            <xs:element name="supportsLooseOrdering" type="xs:boolean"/>
            <xs:element name="creationDate" type="xs:date"/>
            <xs:element name="createdBy" type="IdCodeName"/>
            <xs:element name="hasInner" type="xs:boolean"/>
            <xs:element name="hasCase" type="xs:boolean"/>
            <xs:element name="stockKeepingFrequency" type="TimeFrequency"/>
            <xs:element name="productCode" type="xs:string" nillable="true"/>
            <xs:element name="shelfLifeInDays" type="xs:int" default="1"/>
            <xs:element name="productStatus" type="SwitchStatus"/>
            <xs:element name="unitOfMeasure" type="xs:string"/>
            <xs:element name="unitPrice" type="xs:float" nillable="true"/>
            <xs:element name="negotiatedUnitPrice" type="xs:float" nillable="true"/>
            <xs:element name="availableAtCafe" type="xs:boolean"/>
            <xs:element name="availableForCafeInventory" type="xs:boolean"/>
            <xs:element name="fulfillmentType" type="FulfillmentType"/>
            <xs:element name="defaultFulfillmentType" type="FulfillmentType"/>
            <xs:element name="derivedMappings" type="DerivedMapping" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="participatesInRecipe" type="xs:boolean"/>
            <xs:element name="variantLevelOrdering" type="xs:boolean"/>
            <xs:element name="productImage" type="xs:string" nillable="true"/>
            <xs:element name="supportsSpecialOrdering" type="xs:boolean" nillable="true"/>
            <xs:element name="varianceType" type="VarianceType" nillable="false"/>
            <xs:element name="autoProduction" type="xs:boolean" nillable="false"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="DerivedMapping">
        <xs:sequence>
            <xs:element name="unit" type="xs:int"/>
            <xs:element name="type" type="FulfillmentType"/>
        </xs:sequence>
    </xs:complexType>


    <xs:complexType name="ProductFulfillmentType">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="fulfillmentType" type="FulfillmentType"/>
            <xs:element name="status" type="SwitchStatus"/>
            <xs:element name="productId" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ProductPackagingMapping">
        <xs:sequence>
            <xs:element name="productPackagingMappingId" type="xs:int"
                        nillable="true"/>
            <xs:element name="packagingId" type="xs:int" nillable="false"/>
            <xs:element name="productId" type="xs:int" nillable="false"/>
            <xs:element name="mappingStatus" type="SwitchStatus"/>
            <xs:element name="isDefault" type="xs:boolean"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="SkuAttributeValue">
        <xs:sequence>
            <xs:element name="skuAttributeValueId" type="xs:int"
                        nillable="true"/>
            <xs:element name="skuId" type="xs:int"/>
            <xs:element name="attributeId" type="xs:int"/>
            <xs:element name="attributeValueId" type="xs:int"/>
            <xs:element name="mappingStatus" type="SwitchStatus"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="SkuDefinition">
        <xs:sequence>
            <xs:element name="skuId" type="xs:int" nillable="true"/>
            <xs:element name="skuName" type="xs:string"/>
            <xs:element name="skuDescription" type="xs:string"/>
            <xs:element name="supportsLooseOrdering" type="xs:boolean"/>
            <xs:element name="creationDate" type="xs:date"/>
            <xs:element name="createdBy" type="IdCodeName"/>
            <xs:element name="hasInner" type="xs:boolean"/>
            <xs:element name="hasCase" type="xs:boolean"/>
            <xs:element name="linkedProduct" type="IdCodeName"/>
            <xs:element name="shelfLifeInDays" type="xs:int"/>
            <xs:element name="skuStatus" type="SwitchStatus"/>
            <xs:element name="unitOfMeasure" type="xs:string"/>
            <xs:element name="skuImage" type="xs:string" nillable="true"/>
            <xs:element name="unitPrice" type="xs:float" nillable="true"/>
            <xs:element name="negotiatedUnitPrice" type="xs:float"
                        nillable="true"/>
            <xs:element name="priceLastUpdated" type="xs:date"
                        nillable="true"/>
            <xs:element name="torqusSkuName" type="xs:string"
                        nillable="true"/>
            <xs:element name="isDefault" type="xs:boolean" nillable="false"/>
            <xs:element name="skuAttributes" type="SkuAttributeValue"
                        minOccurs="1" maxOccurs="unbounded"/>
            <xs:element name="skuPackagings" type="SkuPackagingMapping"
                        minOccurs="1" maxOccurs="unbounded"/>
            <xs:element name="skuCode" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="SkuPackagingMapping">
        <xs:sequence>
            <xs:element name="skuPackagingMappingId" type="xs:int"
                        nillable="true"/>
            <xs:element name="packagingId" type="xs:int"/>
            <xs:element name="skuId" type="xs:int"/>
            <xs:element name="mappingStatus" type="SwitchStatus"/>
            <xs:element name="isDefault" type="xs:boolean"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="UnitDetail">
        <xs:sequence>
            <xs:element name="unitId" type="xs:int"/>
            <xs:element name="unitName" type="xs:string"/>
            <xs:element name="unitCategoryId" type="xs:int"/>
            <xs:element name="unitEmail" type="xs:string"/>
            <xs:element name="unitStatus" type="SwitchStatus"/>
            <xs:element name="tin" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="SCMUnitCategory">
        <xs:sequence>
            <xs:element name="categoryId" type="xs:int"/>
            <xs:element name="categoryName" type="xs:string" nillable="false"/>
            <xs:element name="categoryCode" type="xs:string" nillable="false"/>
            <xs:element name="categoryDescription" type="xs:string"
                        nillable="true"/>
            <xs:element name="categoryStatus" type="SwitchStatus"
                        nillable="false"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="UnitProductMapping">
        <xs:sequence>
            <xs:element name="unitProductMappingId" type="xs:int"
                        nillable="true"/>
            <xs:element name="unitId" type="xs:int"/>
            <xs:element name="productId" type="xs:int"/>
            <xs:element name="vendorId" type="xs:int"/>
            <xs:element name="mappingStatus" type="SwitchStatus"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ReferenceOrder">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="generationTime" type="xs:date" nillable="true"/>
            <xs:element name="initiationTime" type="xs:date" nillable="true"/>
            <xs:element name="lastUpdateTime" type="xs:date" nillable="true"/>
            <xs:element name="requestUnit" type="IdCodeName" nillable="false"/>
            <xs:element name="generatedBy" type="IdCodeName" nillable="false"/>
            <xs:element name="fulfillmentUnit" type="IdCodeName"
                        nillable="true"/>
            <xs:element name="fulfillmentDate" type="xs:date"
                        nillable="false"/>
            <xs:element name="status" type="SCMOrderStatus"/>
            <xs:element name="comment" type="xs:string"/>
            <xs:element name="referenceOrderMenuItems" type="ReferenceOrderMenuItem"
                        minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="referenceOrderScmItems" type="ReferenceOrderScmItem"
                        minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="requestOrderIds" type="xs:int"
                        minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ReferenceOrderMenuItem">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="productId" type="xs:int" nillable="false"/>
            <xs:element name="productName" type="xs:string" nillable="false"/>
            <xs:element name="dimension" type="xs:string" nillable="true"/>
            <xs:element name="requestedQuantity" type="xs:float"
                        nillable="false"/>
            <xs:element name="requestedAbsoluteQuantity" type="xs:float"
                        nillable="false"/>
            <xs:element name="transferredQuantity" type="xs:float"
                        nillable="true"/>
            <xs:element name="receivedQuantity" type="xs:float"
                        nillable="true"/>
            <xs:element name="quantity" type="xs:float" nillable="true"/>
            <xs:element name="dineInQuantity" type="xs:float"
                        nillable="true"/>
            <xs:element name="deliveryQuantity" type="xs:float"
                        nillable="true"/>
            <xs:element name="takeawayQuantity" type="xs:float"
                        nillable="true"/>
            <xs:element name="variants" type="ReferenceOrderMenuVariant"
                        nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ReferenceOrderMenuVariant">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="name" type="xs:string" nillable="false"/>
            <xs:element name="conversionQuantity" type="xs:float"
                        nillable="false"/>
            <xs:element name="orderedQuantity" type="xs:float"
                        nillable="false"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ReferenceOrderScmItem">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="productId" type="xs:int" nillable="false"/>
            <xs:element name="productName" type="xs:string" nillable="false"/>
            <xs:element name="requestedQuantity" type="xs:float"
                        nillable="false"/>
            <xs:element name="requestedAbsoluteQuantity" type="xs:float"
                        nillable="false"/>
            <xs:element name="transferredQuantity" type="xs:float"
                        nillable="true"/>
            <xs:element name="receivedQuantity" type="xs:float"
                        nillable="true"/>
            <xs:element name="fulfillmentType" type="FulfillmentType"
                        nillable="true"/>
            <xs:element name="unitOfMeasure" type="xs:string"
                        nillable="false"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="RequestScmItem">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="name" type="xs:string" nillable="false"/>
            <xs:element name="subCategoryName" type="xs:string"
                        nillable="false"/>
            <xs:element name="suggestedQuantity" type="xs:float"
                        nillable="false"/>
            <xs:element name="orderingQuantity" type="xs:float"
                        nillable="false"/>
            <xs:element name="stockAtHand" type="xs:float" nillable="true"/>
            <xs:element name="saleQuantity" type="xs:float" nillable="true"/>
            <xs:element name="packagingName" type="xs:string"
                        nillable="true"/>
            <xs:element name="conversionRatio" type="xs:float"
                        nillable="true"/>
            <xs:element name="packagingQuantity" type="xs:int"
                        nillable="true"/>
            <xs:element name="fulfillmentType" type="FulfillmentType"
                        nillable="true" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="selectedFulfillmentType" type="FulfillmentType"
                        nillable="true"/>
            <xs:element name="unitOfMeasure" type="xs:string"
                        nillable="false"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="RequestOrder">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="generationTime" type="xs:date" nillable="true"/>
            <xs:element name="lastUpdateTime" type="xs:date" nillable="true"/>
            <xs:element name="requestUnit" type="IdCodeName" nillable="false"/>
            <xs:element name="generatedBy" type="IdCodeName" nillable="false"/>
            <xs:element name="lastUpdatedBy" type="IdCodeName"
                        nillable="true"/>
            <xs:element name="fulfillmentUnit" type="IdCodeName"
                        nillable="true"/>
            <xs:element name="fulfillmentDate" type="xs:date"
                        nillable="false"/>
            <xs:element name="specialOrder" type="xs:boolean"
                        nillable="true"/>
            <xs:element name="referenceOrderId" type="xs:int"/>
            <xs:element name="status" type="SCMOrderStatus"/>
            <xs:element name="comment" type="xs:string"/>
            <xs:element name="totalAmount" type="xs:float"/>
            <xs:element name="purchaseOrderId" type="xs:int" nillable="true"/>
            <xs:element name="transferOrderId" type="xs:int" nillable="true"/>
            <xs:element name="goodsReceivedId" type="xs:int" nillable="true"/>
            <xs:element name="requestOrderItems" type="RequestOrderItem"
                        minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="RequestOrderItem">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="productId" type="xs:int" nillable="false"/>
            <xs:element name="productName" type="xs:string" nillable="false"/>
            <xs:element name="requestedQuantity" type="xs:float"
                        nillable="false"/>
            <xs:element name="requestedAbsoluteQuantity" type="xs:float"
                        nillable="false"/>
            <xs:element name="transferredQuantity" type="xs:float"
                        nillable="true"/>
            <xs:element name="receivedQuantity" type="xs:float"
                        nillable="true"/>
            <xs:element name="unitOfMeasure" type="xs:string"
                        nillable="false"/>
            <xs:element name="unitPrice" type="xs:float" nillable="true"/>
            <xs:element name="negotiatedUnitPrice" type="xs:float"
                        nillable="true"/>
            <xs:element name="calculatedAmount" type="xs:float"
                        nillable="true"/>
            <xs:element name="vendor" type="IdCodeName" nillable="true"/>
            <xs:element name="productCode" type="xs:string" nillable="true"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PurchaseOrder">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="generationTime" type="xs:date" nillable="true"/>
            <xs:element name="initiationTime" type="xs:date" nillable="true"/>
            <xs:element name="lastUpdateTime" type="xs:date" nillable="true"/>
            <xs:element name="deliveryUnitId" type="IdCodeName"
                        nillable="false"/>
            <xs:element name="generatedForVendor" type="IdCodeName"
                        nillable="true"/>
            <xs:element name="generatedBy" type="IdCodeName" nillable="false"/>
            <xs:element name="lastUpdatedBy" type="IdCodeName"
                        nillable="true"/>
            <xs:element name="approvedBy" type="IdCodeName" nillable="true"/>
            <xs:element name="fulfillmentDate" type="xs:date"
                        nillable="false"/>
            <xs:element name="billAmount" type="xs:decimal" nillable="true"/>
            <xs:element name="paidAmount" type="xs:decimal" nillable="true"/>
            <xs:element name="receiptNumber" type="xs:string"
                        nillable="true"/>
            <xs:element name="status" type="PurchaseOrderStatus"/>
            <xs:element name="comment" type="xs:string"/>
            <xs:element name="recieptNumber" type="xs:string"/>
            <xs:element name="totalTaxes" type="xs:decimal"/>
            <xs:element name="vendorNotified" type="xs:boolean"/>
            <xs:element name="forceClosed" type="xs:boolean"/>
            <xs:element name="dispatchLocation" type="VendorDispatchLocation"/>
            <xs:element name="poInvoice" type="DocumentDetail"/>
            <xs:element name="notifications" type="PurchaseOrderNotification"
                        minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="goodsReceivedList" type="VendorGR"
                        minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="orderItems" type="PurchaseOrderItem"
                        minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PurchaseOrderItem">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="skuId" type="xs:int" nillable="false"/>
            <xs:element name="skuName" type="xs:string" nillable="false"/>
            <xs:element name="hsn" type="xs:string" nillable="false"/>
            <xs:element name="requestedQuantity" type="xs:float"
                        nillable="false"/>
            <xs:element name="requestedAbsoluteQuantity" type="xs:float"
                        nillable="false"/>
            <xs:element name="transferredQuantity" type="xs:float"
                        nillable="true"/>
            <xs:element name="receivedQuantity" type="xs:float"
                        nillable="true"/>
            <xs:element name="unitOfMeasure" type="xs:string"
                        nillable="false"/>
            <xs:element name="unitPrice" type="xs:string" nillable="true"/>
            <xs:element name="negotiatedUnitPrice" type="xs:string"
                        nillable="true"/>
            <xs:element name="totalCost" type="xs:string" nillable="true"/>
            <xs:element name="amountPaid" type="xs:string" nillable="true"/>
            <xs:element name="packagingId" type="xs:integer" nillable="false"/>
            <xs:element name="packagingName" type="xs:string"
                        nillable="false"/>
            <xs:element name="conversionRatio" type="xs:decimal"
                        nillable="false"/>
            <xs:element name="exemptItem" type="xs:boolean"/>
            <xs:element name="packagingQty" type="xs:int" nillable="false"/>
            <xs:element name="igst" type="PercentageDetail" nillable="true"/>
            <xs:element name="cgst" type="PercentageDetail" nillable="true"/>
            <xs:element name="sgst" type="PercentageDetail" nillable="true"/>
            <xs:element name="totalTax" type="xs:decimal" nillable="false"/>
            <xs:element name="otherTaxes" type="PercentageDetail"
                        minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>


    <xs:complexType name="VendorGR">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="generationTime" type="xs:date" nillable="true"/>
            <xs:element name="deliveryUnitId" type="IdCodeName"
                        nillable="false"/>
            <xs:element name="generatedForVendor" type="IdCodeName"
                        nillable="true"/>
            <xs:element name="generatedBy" type="IdCodeName" nillable="false"/>
            <xs:element name="billAmount" type="xs:decimal" nillable="true"/>
            <xs:element name="extraCharges" type="xs:decimal"
                        nillable="true"/>
            <xs:element name="receiptNumber" type="xs:string"
                        nillable="true"/>
            <xs:element name="receiptType" type="GRDocType" nillable="true"/>
            <xs:element name="status" type="PurchaseOrderStatus"/>
            <xs:element name="grCreationType" type="xs:string"/>
            <xs:element name="totalTaxes" type="xs:decimal"/>
            <xs:element name="dispatchLocation" type="VendorDispatchLocation"/>
            <xs:element name="purchaseOrderList" type="PurchaseOrder"
                        minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="grItems" type="VendorGRItem" minOccurs="0"
                        maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>


    <xs:complexType name="VendorGRItem">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="skuId" type="xs:int" nillable="false"/>
            <xs:element name="skuName" type="xs:string" nillable="false"/>
            <xs:element name="hsn" type="xs:string" nillable="false"/>
            <xs:element name="requestedQuantity" type="xs:float"
                        nillable="false"/>
            <xs:element name="receivedQuantity" type="xs:float"
                        nillable="true"/>
            <xs:element name="unitOfMeasure" type="xs:string"
                        nillable="false"/>
            <xs:element name="unitPrice" type="xs:string" nillable="true"/>
            <xs:element name="totalCost" type="xs:string" nillable="true"/>
            <xs:element name="amountPaid" type="xs:string" nillable="true"/>
            <xs:element name="packagingId" type="xs:integer" nillable="false"/>
            <xs:element name="packagingName" type="xs:string"
                        nillable="false"/>
            <xs:element name="conversionRatio" type="xs:decimal"
                        nillable="false"/>
            <xs:element name="exemptItem" type="xs:boolean"/>
            <xs:element name="packagingQty" type="xs:int" nillable="false"/>
            <xs:element name="totalTax" type="xs:decimal" nillable="false"/>
            <xs:element name="taxes" type="TaxDetail" minOccurs="0"
                        maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="TaxDetail">
        <xs:sequence>
            <xs:element name="type" type="TaxCategory" nillable="false"/>
            <xs:element name="detail" type="PercentageDetail"
                        nillable="false"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="PurchaseOrderNotification">
        <xs:sequence>
            <xs:element name="id" type="xs:integer"/>
            <xs:element name="contact" type="xs:string"/>
            <xs:element name="message" type="xs:string"/>
            <xs:element name="carrier" type="xs:string"/>
            <xs:element name="type" type="NotificationType"/>
            <xs:element name="poId" type="xs:integer"/>
            <xs:element name="client" type="xs:string"/>
            <xs:element name="sent" type="xs:boolean"/>
            <xs:element name="date" type="xs:date"/>
        </xs:sequence>
    </xs:complexType>


    <xs:complexType name="TransferOrder">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="generationTime" type="xs:date" nillable="true"/>
            <xs:element name="initiationTime" type="xs:date" nillable="true"/>
            <xs:element name="lastUpdateTime" type="xs:date" nillable="true"/>
            <xs:element name="generationUnitId" type="IdCodeName"
                        nillable="false"/>
            <xs:element name="generatedForUnitId" type="IdCodeName"
                        nillable="true"/>
            <xs:element name="generatedBy" type="IdCodeName" nillable="false"/>
            <xs:element name="lastUpdatedBy" type="IdCodeName"
                        nillable="true"/>
            <xs:element name="status" type="SCMOrderStatus"/>
            <xs:element name="comment" type="xs:string"/>
            <xs:element name="totalAmount" type="xs:float"/>
            <xs:element name="requestOrderId" type="xs:int" nillable="true"/>
            <xs:element name="purchaseOrderId" type="xs:int" nillable="true"/>
            <xs:element name="goodsReceivedId" type="xs:int" nillable="true"/>
            <xs:element name="requestOrderTime" type="xs:date"
                        nillable="true"/>
            <xs:element name="transferOrderItems" type="TransferOrderItem"
                        minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TransferOrderItem">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="skuId" type="xs:int" nillable="false"/>
            <xs:element name="skuName" type="xs:string" nillable="false"/>
            <xs:element name="packagingDetails" type="SCMOrderPackaging"
                        minOccurs="1" maxOccurs="unbounded"/>
            <xs:element name="requestedQuantity" type="xs:float"
                        nillable="false"/>
            <xs:element name="requestedAbsoluteQuantity" type="xs:float"
                        nillable="false"/>
            <xs:element name="transferredQuantity" type="xs:float"
                        nillable="true"/>
            <xs:element name="receivedQuantity" type="xs:float"
                        nillable="true"/>
            <xs:element name="unitOfMeasure" type="xs:string"
                        nillable="false"/>
            <xs:element name="unitPrice" type="xs:double" nillable="true"/>
            <xs:element name="negotiatedUnitPrice" type="xs:double"
                        nillable="true"/>
            <xs:element name="calculatedAmount" type="xs:float"
                        nillable="true"/>
            <xs:element name="requestOrderItemId" type="xs:int"
                        nillable="true"/>
            <xs:element name="purchaseOrderItemId" type="xs:int"
                        nillable="true"/>
            <xs:element name="goodReceivedItemId" type="xs:int"
                        nillable="true"/>
            <xs:element name="skuCode" type="xs:string" nillable="true"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="SCMOrderPackaging">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="packagingDefinitionData" type="PackagingDefinition"
                        nillable="false"/>
            <xs:element name="conversionRatio" type="xs:float"
                        nillable="false"/>
            <xs:element name="numberOfUnitsPacked" type="xs:float"
                        nillable="false"/>
            <xs:element name="numberOfUnitsReceived" type="xs:float"
                        nillable="true"/>
            <xs:element name="transferredQuantity" type="xs:float"
                        nillable="false"/>
            <xs:element name="receivedQuantity" type="xs:float"
                        nillable="true"/>
            <xs:element name="transferOrderItemId" type="xs:int"
                        nillable="true"/>
            <xs:element name="goodsReceivedItemId" type="xs:int"
                        nillable="true"/>
            <xs:element name="numberOfUnitsRejected" type="xs:float"
                        nillable="true"/>
            <xs:element name="RejectionReason" type="xs:string"
                        nillable="true"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GoodsReceived">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="generationTime" type="xs:date" nillable="true"/>
            <xs:element name="lastUpdateTime" type="xs:date" nillable="true"/>
            <xs:element name="generationUnitId" type="IdCodeName"
                        nillable="false"/>
            <xs:element name="generatedForUnitId" type="IdCodeName"
                        nillable="true"/>
            <xs:element name="generatedBy" type="IdCodeName" nillable="false"/>
            <xs:element name="receivedBy" type="IdCodeName" nillable="true"/>
            <xs:element name="cancelledBy" type="IdCodeName" nillable="true"/>
            <xs:element name="status" type="SCMOrderStatus"/>
            <xs:element name="comment" type="xs:string"/>
            <xs:element name="totalAmount" type="xs:float"/>
            <xs:element name="requestOrderId" type="xs:int" nillable="true"/>
            <xs:element name="purchaseOrderId" type="xs:int" nillable="true"/>
            <xs:element name="transferOrderId" type="xs:int" nillable="true"/>
            <xs:element name="autoGenerated" type="xs:boolean"
                        nillable="false"/>
            <xs:element name="parentGR" type="xs:int" nillable="true"/>
            <xs:element name="goodsReceivedItems" type="GoodsReceivedItem"
                        minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GoodsReceivedItem">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="productId" type="xs:int" />
            <xs:element name="skuId" type="xs:int" nillable="false"/>
            <xs:element name="skuName" type="xs:string" nillable="false"/>
            <xs:element name="category" type="xs:string" nillable="true"/>
            <xs:element name="subCategory" type="xs:string" nillable="true"/>
            <xs:element name="packagingDetails" type="SCMOrderPackaging"
                        minOccurs="1" maxOccurs="unbounded"/>
            <xs:element name="transferredQuantity" type="xs:float"
                        nillable="true"/>
            <xs:element name="receivedQuantity" type="xs:float"
                        nillable="true"/>
            <xs:element name="unitOfMeasure" type="xs:string"
                        nillable="false"/>
            <xs:element name="unitPrice" type="xs:double" nillable="true"/>
            <xs:element name="negotiatedUnitPrice" type="xs:double"
                        nillable="true"/>
            <xs:element name="calculatedAmount" type="xs:float"
                        nillable="true"/>
            <xs:element name="requestOrderItemId" type="xs:int"
                        nillable="true"/>
            <xs:element name="purchaseOrderItemId" type="xs:int"
                        nillable="true"/>
            <xs:element name="TransferOrderItemId" type="xs:int"
                        nillable="true"/>
            <xs:element name="vendorId" type="xs:int" nillable="true"/>
            <xs:element name="skuCode" type="xs:string" nillable="true"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="IdCodeName">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="code" type="xs:string" nillable="true"/>
            <xs:element name="name" type="xs:string" nillable="true"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DateAndValue">
        <xs:sequence>
            <xs:element name="date" type="xs:date" nillable="true"/>
            <xs:element name="value" type="xs:decimal" nillable="true"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="WastageEvent">
        <xs:sequence>
            <xs:element name="wastageId" type="xs:int" nillable="true"/>
            <xs:element name="unitId" type="xs:int" nillable="false"/>
            <xs:element name="quantity" type="xs:float" nillable="false"/>
            <xs:element name="product" type="ProductDefinition"
                        nillable="false"/>
            <xs:element name="businessDate" type="xs:date" nillable="true"/>
            <xs:element name="generationTime" type="xs:date" nillable="true"/>
            <xs:element name="generatedBy" type="xs:int" nillable="false"/>
            <xs:element name="comment" type="xs:string" nillable="true"/>
            <xs:element name="status" type="StockEventStatus"
                        nillable="true"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="StockInventoryResponse">
        <xs:sequence>
            <xs:element name="unit" type="xs:int" nillable="true"/>
            <xs:element name="eventType" type="StockEventType"
                        nillable="false"/>
            <xs:element name="errorResponse" type="xs:string"
                        nillable="true"/>
            <xs:element name="inventoryResponse" type="ProductStockForUnit"
                        minOccurs="1" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ProductStockForUnit">
        <xs:sequence>
            <xs:element name="inventoryId" type="xs:int" nillable="true"/>
            <xs:element name="productId" type="xs:int"/>
            <xs:element name="opening" type="xs:double"/>
            <xs:element name="product" type="ProductBasicDetail"/>
            <xs:element name="unitId" type="xs:int"/>
            <xs:element name="generatedBy" type="xs:int"/>
            <xs:element name="stockValue" type="xs:double" nillable="true"/>
            <xs:element name="received" type="xs:double" nillable="true"/>
            <xs:element name="transferred" type="xs:double" nillable="true"/>
            <xs:element name="wasted" type="xs:double" nillable="true"/>
            <xs:element name="consumption" type="xs:double" nillable="true"/>
            <xs:element name="expectedValue" type="xs:double"
                        nillable="true"/>
            <xs:element name="variance" type="xs:double" nillable="true"/>
            <xs:element name="eventType" type="TimeFrequency"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ProductBasicDetail">
        <xs:sequence>
            <xs:element name="productId" type="xs:int"/>
            <xs:element name="unitOfMeasure" type="xs:string"/>
            <xs:element name="productName" type="xs:string"/>
            <xs:element name="subCategory" type="IdCodeName"/>
            <xs:element name="category" type="IdCodeName"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ExternalTransferDetail">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="vendorId" type="xs:int" nillable="true"/>
            <xs:element name="dispatchId" type="xs:int" nillable="true"/>
            <xs:element name="vendorName" type="xs:string"/>
            <xs:element name="locationName" type="xs:string"/>
            <xs:element name="status" type="SCMOrderStatus"/>
            <xs:element name="updatedBy" type="IdCodeName"/>
            <xs:element name="updatedAt" type="xs:date"/>
        </xs:sequence>
    </xs:complexType>

    <!--<xs:complexType name="VendorDetail"> <xs:sequence> <xs:element name="vendorId"
        type="xs:int" nillable="true" /> <xs:element name="vendorName" type="xs:string"
        nillable="false" /> <xs:element name="vendorDescription" type="xs:string"
        nillable="true" /> <xs:element name="tin" type="xs:string" nillable="true"
        /> <xs:element name="company" type="xs:string" nillable="true" /> <xs:element
        name="address" type="xs:string" nillable="true" /> <xs:element name="primaryContact"
        type="xs:string" nillable="true" /> <xs:element name="secondaryContact" type="xs:string"
        nillable="true" /> <xs:element name="primaryEmail" type="xs:string" nillable="true"
        /> <xs:element name="secondaryEmail" type="xs:string" nillable="true" />
        <xs:element name="paymentCycle" type="PaymentCycle" nillable="false" /> <xs:element
        name="paymentCycleDay" type="xs:int" nillable="false" /> <xs:element name="vendorStatus"
        type="SwitchStatus" nillable="false" /> </xs:sequence> </xs:complexType> -->
    <xs:complexType name="UnitVendorMapping">
        <xs:sequence>
            <xs:element name="unitVendorMappingId" type="xs:int"
                        nillable="true"/>
            <xs:element name="unit" type="IdCodeName" nillable="false"/>
            <xs:element name="vendor" type="IdCodeName" nillable="false"/>
            <xs:element name="mappingStatus" type="SwitchStatus"
                        nillable="false"/>
            <xs:element name="fulFillmentType" type="FulfillmentType"
                        nillable="false"/>
            <xs:element name="smsNotification" type="xs:boolean"
                        nillable="true"/>
            <xs:element name="emailNotification" type="xs:boolean"
                        nillable="true"/>
            <xs:element name="noOfDays" type="xs:int" nillable="true"/>
            <xs:element name="notificationTime" type="xs:string"
                        nillable="false"/>
            <xs:element name="creationTime" type="xs:date" nillable="true"/>
            <xs:element name="lastUpdateTime" type="xs:date" nillable="true"/>
            <xs:element name="createdBy" type="IdCodeName" nillable="false"/>
            <xs:element name="lastUpdatedBy" type="IdCodeName" nillable="true"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="PriceUpdateEvent">
        <xs:sequence>
            <xs:element name="eventId" type="xs:int" nillable="false"/>
            <xs:element name="createdBy" type="xs:int" nillable="false"/>
            <xs:element name="createdByName" type="xs:string"
                        nillable="false"/>
            <xs:element name="creationTime" type="xs:date" nillable="false"/>
            <xs:element name="finalizedBy" type="xs:int" nillable="false"/>
            <xs:element name="finalizationTime" type="xs:date"
                        nillable="false"/>
            <xs:element name="finalizedByName" type="xs:string"
                        nillable="false"/>
            <xs:element name="eventType" type="PriceUpdateEventType"
                        nillable="false"/>
            <xs:element name="eventActionType" type="PriceUpdateEventActionType"
                        nillable="false"/>
            <xs:element name="eventStatus" type="PriceUpdateEventStatus"
                        nillable="false"/>
            <xs:element name="noOfRecords" type="xs:int" nillable="false"/>
            <xs:element name="noOfErrors" type="xs:int" nillable="false"/>
            <xs:element name="dataFilePath" type="xs:string" nillable="false"/>
            <xs:element name="entries" type="PriceUpdateEntry"
                        minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PriceUpdateEntry">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="false"/>
            <xs:element name="keyType" type="PriceUpdateEntryType"
                        nillable="false"/>
            <xs:element name="keyId" type="xs:int" nillable="false"/>
            <xs:element name="keyName" type="xs:string" nillable="false"/>
            <xs:element name="unitOfMeasure" type="xs:string"
                        nillable="false"/>
            <xs:element name="unitPrice" type="xs:decimal" nillable="false"/>
            <xs:element name="updatedUnitPrice" type="xs:decimal"
                        nillable="false"/>
            <xs:element name="editedUnitPrice" type="xs:decimal"
                        nillable="false"/>
            <xs:element name="approvedUnitPrice" type="xs:decimal"
                        nillable="false"/>
            <xs:element name="entryStatus" type="PriceUpdateEventStatus"
                        nillable="false"/>
            <xs:element name="errorCode" type="xs:string" nillable="true"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PriceUpdateEventVO">
        <xs:sequence>
            <xs:element name="hasEvents" type="xs:boolean"/>
            <xs:element name="entries" type="PriceUpdateEvent"
                        minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="AddressDetail">
        <xs:sequence>
            <xs:element name="addressId" type="xs:int" nillable="true"/>
            <xs:element name="line1" type="xs:string" nillable="false"/>
            <xs:element name="line2" type="xs:string" nillable="true"/>
            <xs:element name="line3" type="xs:string" nillable="true"/>
            <xs:element name="city" type="xs:string" nillable="false"/>
            <xs:element name="state" type="xs:string" nillable="false"/>
            <xs:element name="country" type="xs:string" nillable="false"/>
            <xs:element name="zipcode" type="xs:string" nillable="false"/>
            <xs:element name="locationId" type="xs:integer" nillable="true"/>
            <xs:element name="addressContact" type="xs:string"
                        nillable="false"/>
            <xs:element name="addressType" type="AddressType"
                        nillable="false"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="PercentageDetail">
        <xs:sequence>
            <xs:element name="value" type="xs:decimal" nillable="true"/>
            <xs:element name="percentage" type="xs:decimal" nillable="true"/>
        </xs:sequence>
    </xs:complexType>


    <xs:complexType name="DocumentDetail">
        <xs:sequence>
            <xs:element name="documentId" type="xs:int" nillable="true"/>
            <xs:element name="documentLink" type="xs:string" nillable="false"/>
            <xs:element name="fileType" type="FileType" nillable="true"/>
            <xs:element name="mimeType" type="MimeType" nillable="true"/>
            <xs:element name="uploadType" type="DocUploadType"
                        nillable="false"/>
            <xs:element name="uploadTypeId" type="xs:int" nillable="false"/>
            <xs:element name="updateTime" type="xs:date" nillable="false"/>
            <xs:element name="updatedBy" type="IdCodeName" nillable="false"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="VendorDispatchLocation">
        <xs:sequence>
            <xs:element name="dispatchId" type="xs:int" nillable="true"/>
            <xs:element name="vendorDetail" type="VendorDetail"
                        nillable="false"/>
            <xs:element name="applyTax" type="xs:boolean" default="true"/>
            <xs:element name="locationName" type="xs:string" nillable="true"/>
            <xs:element name="city" type="xs:string" nillable="false"/>
            <xs:element name="state" type="xs:string" nillable="false"/>
            <xs:element name="country" type="xs:string" nillable="false"/>
            <xs:element name="address" type="AddressDetail" nillable="false"/>
            <xs:element name="notificationType" type="NotificationType"
                        nillable="true"/>
            <xs:element name="updateTime" type="xs:date" nillable="false"/>
            <xs:element name="updatedBy" type="IdCodeName" nillable="false"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="VendorAccountDetail">
        <xs:sequence>
            <xs:element name="accountId" type="xs:int" nillable="true"/>
            <xs:element name="vendorDetail" type="VendorDetail"
                        nillable="false"/>
            <xs:element name="accountNumber" type="xs:string"
                        nillable="false"/>
            <xs:element name="ifscCode" type="xs:string" nillable="false"/>
            <xs:element name="accountType" type="VendorAccountType"
                        nillable="false"/>
            <xs:element name="micrCode" type="xs:string" nillable="false"/>
            <xs:element name="cancelledCheque" type="DocumentDetail"
                        nillable="false"/>
            <xs:element name="accountContact" type="xs:string"
                        nillable="false"/>
            <xs:element name="accountContactName" type="xs:string"
                        nillable="false"/>
            <xs:element name="accountContactEmail" type="xs:string"
                        nillable="false"/>
            <xs:element name="paymentCycle" type="PaymentCycle"
                        nillable="false"/>
            <xs:element name="kindOfAccount" type="BankAccountType"
                        nillable="false"/>
            <xs:element name="updateTime" type="xs:date" nillable="false"/>
            <xs:element name="updatedBy" type="IdCodeName" nillable="false"/>
            <xs:element name="debitBalance" type="xs:decimal" nillable="true"/>
            <xs:element name="paymentBlocked" type="xs:boolean" default="false"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="VendorCompanyDetail">
        <xs:sequence>
            <xs:element name="companyId" type="xs:int" nillable="true"/>
            <xs:element name="vendorDetail" type="VendorDetail"
                        nillable="false"/>
            <xs:element name="companyName" type="xs:string" nillable="false"/>
            <xs:element name="registeredName" type="xs:string"
                        nillable="false"/>
            <xs:element name="cin" type="xs:string" nillable="false"/>
            <xs:element name="pan" type="xs:string" nillable="false"/>
            <xs:element name="status" type="VendorStatus" nillable="false"/>
            <xs:element name="companyAddress" type="AddressDetail"
                        nillable="false"/>
            <xs:element name="updateTime" type="xs:date" nillable="false"/>
            <xs:element name="updatedBy" type="IdCodeName" nillable="false"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="VendorDetail">
        <xs:sequence>
            <xs:element name="vendorId" type="xs:int" nillable="true"/>
            <xs:element name="entityName" type="xs:string" nillable="false"/>
            <xs:element name="firstName" type="xs:string" nillable="false"/>
            <xs:element name="lastName" type="xs:string" nillable="false"/>
            <xs:element name="primaryContact" type="xs:string"
                        nillable="false"/>
            <xs:element name="secondaryContact" type="xs:string"
                        nillable="false"/>
            <xs:element name="primaryEmail" type="xs:string" nillable="false"/>
            <xs:element name="secondaryEmail" type="xs:string"
                        nillable="false"/>
            <xs:element name="status" type="VendorStatus" nillable="false"/>
            <xs:element name="type" type="VendorType" nillable="false"/>
            <xs:element name="requestedBy" type="IdCodeName" nillable="false"/>
            <xs:element name="updatedBy" type="IdCodeName" nillable="false"/>
            <xs:element name="updateTime" type="xs:date" nillable="false"/>
            <xs:element name="vendorAddress" type="AddressDetail"
                        nillable="false"/>
            <xs:element name="companyDetails" type="VendorCompanyDetail"
                        nillable="false"/>
            <xs:element name="accountDetails" type="VendorAccountDetail"
                        nillable="false"/>
            <xs:element name="dispatchLocations" type="VendorDispatchLocation"
                        minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="VendorRegistrationRequest">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="false"/>
            <xs:element name="vendor" type="xs:string" nillable="false"/>
            <xs:element name="requestDate" type="xs:date" nillable="false"/>
            <xs:element name="requestBy" type="xs:int" nillable="false"/>
            <xs:element name="requestByName" type="xs:string"
                        nillable="false"/>
            <xs:element name="requestFor" type="xs:int" nillable="false"/>
            <xs:element name="requestForName" type="xs:string"
                        nillable="false"/>
            <xs:element name="email" type="xs:string" nillable="false"/>
            <xs:element name="copyEmails" type="xs:string" nillable="false"/>
            <xs:element name="vendorLink" type="xs:int" nillable="false"/>
            <xs:element name="requestStatus" type="VendorStatus"
                        nillable="false"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ProductionPlanEvent">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="false"/>
            <xs:element name="unitId" type="xs:int" nillable="false"/>
            <xs:element name="fulfillmentDate" type="xs:date"
                        nillable="false"/>
            <xs:element name="generationTime" type="xs:date" nillable="false"/>
            <xs:element name="lastUpdateTime" type="xs:date" nillable="false"/>
            <xs:element name="generatedBy" type="IdCodeName" nillable="false"/>
            <xs:element name="lastUpdatedBy" type="IdCodeName"
                        nillable="false"/>
            <xs:element name="status" type="xs:string" nillable="false"/>
            <xs:element name="requestOrders" type="xs:int" minOccurs="0"
                        maxOccurs="unbounded"/>
            <xs:element name="requestItems" type="PlanOrderItem"
                        minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PlanOrderItem">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="false"/>
            <xs:element name="productId" type="xs:int" nillable="false"/>
            <xs:element name="productName" type="xs:string" nillable="false"/>
            <xs:element name="requestedQuantity" type="xs:decimal"
                        nillable="false"/>
            <xs:element name="availableQuantity" type="xs:decimal"
                        nillable="false"/>
            <xs:element name="unitOfMeasure" type="xs:string"
                        nillable="false"/>
            <xs:element name="category" type="xs:string" nillable="false"/>
            <xs:element name="itemType" type="xs:string" nillable="false"/>
            <xs:element name="unitPrice" type="xs:decimal" nillable="false"/>
            <xs:element name="amount" type="xs:decimal" nillable="false"/>
            <xs:element name="printCount" type="xs:int" nillable="true"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PlanOrderItemPrep">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="recipeId" type="xs:int"/>
            <xs:element name="requestedBy" type="IdCodeName"/>
            <xs:element name="preparationQuantity" type="xs:decimal"/>
            <xs:element name="requestingTime" type="xs:date" nillable="true"/>
            <xs:element name="planOrderItem" type="IdCodeName"/>
            <xs:element name="planOrderItemPrepItems" type="PlanOrderItemPrepItem" minOccurs="1" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PlanOrderItemPrepItem">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="productId" type="xs:int"/>
            <xs:element name="productName" type="xs:string"/>
            <xs:element name="quantity" type="xs:decimal"/>
            <xs:element name="unitOfMeasure" type="xs:string"/>
            <xs:element name="planOrderItemPrepId" type="xs:int"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ProductionBooking">
        <xs:sequence>
            <xs:element name="bookingId" type="xs:int" nillable="false"/>
            <xs:element name="productId" type="xs:int" nillable="false"/>
            <xs:element name="productName" type="xs:string" nillable="false"/>
            <xs:element name="unitOfMeasure" type="xs:string"
                        nillable="false"/>
            <xs:element name="quantity" type="xs:decimal" nillable="false"/>
            <xs:element name="unitId" type="xs:int" nillable="false"/>
            <xs:element name="unitPrice" type="xs:decimal" nillable="false"/>
            <xs:element name="totalCost" type="xs:decimal" nillable="false"/>
            <xs:element name="generationTime" type="xs:date" nillable="false"/>
            <xs:element name="cancellationTime" type="xs:date"
                        nillable="true"/>
            <xs:element name="closureTime" type="xs:date" nillable="true"/>
            <xs:element name="generatedBy" type="IdCodeName" nillable="false"/>
            <xs:element name="cancelledBy" type="IdCodeName" nillable="true"/>
            <xs:element name="bookingStatus" type="BookingStatus"
                        nillable="false"/>
            <xs:element name="consumption" type="BookingConsumption"
                        minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="BookingConsumption">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="false"/>
            <xs:element name="productId" type="xs:int" nillable="false"/>
            <xs:element name="productName" type="xs:string" nillable="false"/>
            <xs:element name="skuId" type="xs:int" nillable="false"/>
            <xs:element name="skuName" type="xs:string" nillable="false"/>
            <xs:element name="unitOfMeasure" type="xs:string"
                        nillable="false"/>
            <xs:element name="calculatedQuantity" type="xs:decimal"
                        nillable="false"/>
            <xs:element name="unitPrice" type="xs:decimal" nillable="false"/>
            <xs:element name="totalCost" type="xs:decimal" nillable="false"/>
            <xs:element name="availableSkuList" type="IdCodeName"
                        minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>


    <xs:complexType name="DayCloseEvent">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="false"/>
            <xs:element name="type" type="StockEventType" nillable="false"/>
            <xs:element name="status" type="StockEventStatus" nillable="false"/>
            <xs:element name="unit" type="IdCodeName" nillable="false"/>
            <xs:element name="businessDate" type="xs:date" nillable="false"/>
            <xs:element name="eventLogs" type="DayCloseEventLog" minOccurs="1" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="DayClosureEventItems">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="false"/>
            <xs:element name="type" type="StockEventType" nillable="false"/>
            <xs:element name="status" type="StockEventStatus" nillable="false"/>
            <xs:element name="unit" type="IdCodeName" nillable="false"/>
            <xs:element name="businessDate" type="xs:date" nillable="false"/>
            <xs:element name="eventLogs" type="IdCodeName" minOccurs="1" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="DayCloseEventLog">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="false"/>
            <xs:element name="type" type="DayCloseEventLogType" nillable="false"/>
            <xs:element name="status" type="SwitchStatus" nillable="false"/>
            <xs:element name="createdBy" type="IdCodeName" nillable="false"/>
            <xs:element name="createdAt" type="xs:date" nillable="false"/>
        </xs:sequence>
    </xs:complexType>


    <xs:complexType name="DayCloseTxn">
        <xs:sequence>
            <xs:element name="event" type="xs:int" nillable="false"/>
            <xs:element name="time" type="xs:date" nillable="false"/>
            <xs:element name="status" type="xs:string" nillable="false"/>
            <xs:element name="items" type="DayCloseTxnItem" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>


    <xs:complexType name="DayCloseTxnItem">
        <xs:sequence>
            <xs:element name="event" type="xs:int" nillable="false"/>
            <xs:element name="name" type="xs:string" nillable="false"/>
            <xs:element name="price" type="xs:decimal" nillable="false"/>
            <xs:element name="qty" type="xs:decimal" nillable="false"/>
            <xs:element name="status" type="xs:string" nillable="false"/>
            <xs:element name="packagings" type="PackagingDefinition" minOccurs="1" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="CostDetail">
        <xs:sequence>
            <xs:element name="costDetailId" type="xs:int" nillable="true"/>
            <xs:element name="unitId" type="xs:int" nillable="false"/>
            <xs:element name="quantity" type="xs:decimal" nillable="true"/>
            <xs:element name="price" type="xs:decimal" nillable="true"/>
            <xs:element name="uom" type="xs:string" nillable="false"/>
            <xs:element name="keyType" type="PriceUpdateEntryType" nillable="false"/>
            <xs:element name="keyId" type="xs:int" nillable="false"/>
            <xs:element name="lastUpdatedTimes" type="xs:date" nillable="false"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="PaymentRequest">
        <xs:sequence>
            <xs:element name="paymentRequestId" type="xs:int" nillable="true"/>
            <xs:element name="type" type="PaymentRequestType"/>
            <xs:element name="invoiceNumber" type="xs:string" nillable="true"/>
            <xs:element name="vendorId" type="IdCodeName"/>
            <xs:element name="vendorCreditPeriod" type="xs:int"/>
            <xs:element name="createdBy" type="IdCodeName"/>
            <xs:element name="creationTime" type="xs:date"/>
            <xs:element name="currentStatus" type="PaymentRequestStatus"/>
            <xs:element name="lastUpdated" type="xs:date"/>
            <xs:element name="updatedBy" type="IdCodeName"/>
            <xs:element name="paymentInvoice" type="PaymentInvoice"/>
            <xs:element name="paymentCycle" type="PaymentCalendar" nillable="true"/>
            <xs:element name="proposedAmount" type="xs:decimal"/>
            <xs:element name="paidAmount" type="xs:decimal"/>
            <xs:element name="amountsMatch" type="xs:boolean"/>
            <xs:element name="blocked" type="xs:boolean"/>
            <xs:element name="debitExceeded" type="xs:boolean"/>
            <xs:element name="paidAdhoc" type="xs:boolean"/>
            <xs:element name="blockedBy" type="IdCodeName"/>
            <xs:element name="requestingUnit" type="IdCodeName"/>
            <xs:element name="grDocType" type="GRDocType"/>
            <xs:element name="deviationCount" type="xs:int"/>
            <xs:element name="remarks" type="xs:string"/>
            <xs:element name="debitNote" type="DebitNoteDetail"/>
            <xs:element name="vendorDebitBalance" type="xs:decimal"/>
            <xs:element name="paymentDetail" type="PRPaymentDetail"/>
            <xs:element name="requestItemMappings" type="PaymentRequestItemMapping" minOccurs="0"
                        maxOccurs="unbounded"/>
            <xs:element name="statusLogs" type="PaymentRequestStatusLog" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="requestLogs" type="PaymentRequestLog" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="PaymentInvoice">
        <xs:sequence>
            <xs:element name="paymentInvoiceId" type="xs:int" nillable="true"/>
            <xs:element name="invoiceNumber" type="xs:string"/>
            <xs:element name="invoiceDocumentHandle" type="xs:int"/>
            <xs:element name="calculatedInvoiceAmount" type="xs:decimal"/>
            <xs:element name="invoiceAmount" type="xs:decimal"/>
            <xs:element name="paymentAmount" type="xs:decimal"/>
            <xs:element name="extraCharges" type="xs:decimal"/>
            <xs:element name="invoiceDate" type="xs:date"/>
            <xs:element name="paymentInvoiceItems" type="PaymentInvoiceItem" minOccurs="1" maxOccurs="unbounded"/>
            <xs:element name="deviations" type="InvoiceDeviationMapping" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="rejections" type="InvoiceDeviationMapping" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="PaymentInvoiceItem">
        <xs:sequence>
            <xs:element name="paymentInvoiceItemId" type="xs:int" nillable="true"/>
            <xs:element name="skuId" type="xs:int"/>
            <xs:element name="skuName" type="xs:string"/>
            <xs:element name="hsn" type="xs:string"/>
            <xs:element name="uom" type="xs:string"/>
            <xs:element name="category" type="xs:string"/>
            <xs:element name="subCategory" type="xs:string"/>
            <xs:element name="packagingId" type="xs:int"/>
            <xs:element name="conversionRatio" type="xs:decimal"/>
            <xs:element name="quantity" type="xs:decimal"/>
            <xs:element name="totalAmount" type="xs:decimal"/>
            <xs:element name="totalTax" type="xs:decimal"/>
            <xs:element name="totalPrice" type="xs:decimal"/>
            <xs:element name="unitPrice" type="xs:decimal"/>
            <xs:element name="packagingPrice" type="xs:decimal"/>
            <xs:element name="packagingName" type="xs:string"/>
            <xs:element name="taxes" type="PaymentInvoiceItemTax" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="deviations" type="InvoiceDeviationMapping" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="PaymentInvoiceItemTax">
        <xs:sequence>
            <xs:element name="taxDetailId" type="xs:int" nillable="true"/>
            <xs:element name="taxType" type="xs:string"/>
            <xs:element name="taxPercentage" type="xs:decimal"/>
            <xs:element name="taxValue" type="xs:decimal"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="PaymentDeviation">
        <xs:sequence>
            <xs:element name="paymentDeviationId" type="xs:int" nillable="true"/>
            <xs:element name="deviationCode" type="xs:string"/>
            <xs:element name="deviationType" type="PaymentDeviationType"/>
            <xs:element name="deviationLevel" type="PaymentDeviationLevel"/>
            <xs:element name="deviationDetail" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="InvoiceDeviationMapping">
        <xs:sequence>
            <xs:element name="mappingId" type="xs:int" nillable="true"/>
            <xs:element name="paymentDeviation" type="PaymentDeviation"/>
            <xs:element name="currentStatus" type="xs:string"/>
            <xs:element name="createdBy" type="IdCodeName"/>
            <xs:element name="acceptedBy" type="IdCodeName"/>
            <xs:element name="rejectedBy" type="IdCodeName"/>
            <xs:element name="removedBy" type="IdCodeName"/>
            <xs:element name="deviationRemark" type="xs:string"/>
            <xs:element name="actionRemark" type="xs:string"/>
            <xs:element name="deviationItemId" type="xs:int"/>
            <xs:element name="deviationItemType" type="PaymentDeviationLevel"/>
            <xs:element name="actionTime" type="xs:date"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="PaymentRequestStatusLog">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="fromStatus" type="PaymentRequestStatus"/>
            <xs:element name="toStatus" type="PaymentRequestStatus"/>
            <xs:element name="updatedBy" type="IdCodeName"/>
            <xs:element name="updateTime" type="xs:date"/>
            <xs:element name="remarks" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="PaymentRequestLog">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="logData" type="xs:string"/>
            <xs:element name="paymentRequestId" type="xs:int"/>
            <xs:element name="updateTime" type="xs:date"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="DebitNoteDetail">
        <xs:sequence>
            <xs:element name="debitNoteId" type="xs:int" nillable="true"/>
            <xs:element name="paymentRequestId" type="xs:int"/>
            <xs:element name="invoiceNumber" type="xs:string"/>
            <xs:element name="busyReferenceNumber" type="xs:string"/>
            <xs:element name="amount" type="xs:decimal"/>
            <xs:element name="totalTaxes" type="xs:decimal"/>
            <xs:element name="totalAmount" type="xs:decimal"/>
            <xs:element name="creditNoteReceivingTime" type="xs:date"/>
            <xs:element name="generationTime" type="xs:date"/>
            <xs:element name="generatedBy" type="IdCodeName"/>
            <xs:element name="lastUpdatedBy" type="IdCodeName"/>
            <xs:element name="updateTime" type="xs:date"/>
            <xs:element name="creditNoteReceived" type="xs:boolean" default="false"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="PaymentCalendar">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="name" type="xs:string"/>
            <xs:element name="paymentDate" type="xs:date"/>
            <xs:element name="prCreationDate" type="xs:date"/>
            <xs:element name="invoiceDate" type="xs:date"/>
            <xs:element name="cycleTag" type="xs:int"/>
            <xs:element name="selected" type="xs:boolean" default="false"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="PaymentRequestItemMapping">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="paymentRequestId" type="xs:int"/>
            <xs:element name="paymentRequestType" type="PaymentRequestType"/>
            <xs:element name="paymentRequestItemId" type="xs:int"/>
            <xs:element name="linkedPaymentRequestId" type="xs:int" nillable="true"/>
            <xs:element name="status" type="xs:string" nillable="true"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="PRPaymentDetail">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="vendorId" type="xs:int"/>
            <xs:element name="vendorName" type="xs:string"/>
            <xs:element name="beneficiaryAccountNumber" type="xs:string"/>
            <xs:element name="beneficiaryIfscCode" type="xs:string"/>
            <xs:element name="debitAccount" type="xs:string"/>
            <xs:element name="debitBank" type="xs:string"/>
            <xs:element name="paymentType" type="PaymentType"/>
            <xs:element name="paidAmount" type="xs:decimal"/>
            <xs:element name="paymentDate" type="xs:date"/>
            <xs:element name="remarks" type="xs:string"/>
            <xs:element name="proposedAmount" type="xs:decimal"/>
            <xs:element name="createdBy" type="IdCodeName"/>
            <xs:element name="utrNumber" type="xs:string"/>
            <xs:element name="paymentRequests" type="PaymentRequest" minOccurs="1" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="SalesPerformaInvoice">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="vendor" type="IdCodeName"/>
            <xs:element name="dispatchLocation" type="IdCodeName"/>
            <xs:element name="type" type="SalesPerformaType"/>
            <xs:element name="status" type="SalesPerformaStatus"/>
            <xs:element name="invoice" type="DocumentDetail"/>
            <xs:element name="eWayBill" type="DocumentDetail"/>
            <xs:element name="vehicleRegdNumber" type="xs:string"/>
            <xs:element name="vehicleHandlerName" type="xs:string"/>
            <xs:element name="totalCost" type="xs:decimal" />
            <xs:element name="totalSellingCost" type="xs:decimal"/>
            <xs:element name="totalTax" type="xs:decimal"/>
            <xs:element name="additionalCharges" type="xs:decimal"/>
            <xs:element name="totalAmount" type="xs:decimal"/>
            <xs:element name="sendingUnit" type="IdCodeName"/>
            <xs:element name="needsApproval" type="xs:boolean"/>
            <xs:element name="comment" type="xs:string"/>
            <xs:element name="createdBy" type="IdCodeName"/>
            <xs:element name="createdAt" type="xs:date"/>
            <xs:element name="cancelledBy" type="IdCodeName"/>
            <xs:element name="cancelledAt" type="xs:date"/>
            <xs:element name="closureId" type="xs:int"/>
            <xs:element name="items" type="SalesPerformaInvoiceItem" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="SalesPerformaInvoiceItem">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="sku" type="IdCodeName"/>
            <xs:element name="uom" type="xs:string"/>
            <xs:element name="pkg" type="IdCodeName"/>
            <xs:element name="ratio" type="xs:decimal"/>
            <xs:element name="pkgQty" type="xs:decimal"/>
            <xs:element name="qty" type="xs:decimal"/>
            <xs:element name="currPrice" type="xs:decimal"/>
            <xs:element name="pkgPrice" type="xs:string"/>
            <xs:element name="sellPrice" type="xs:decimal" />
            <xs:element name="currAmount" type="xs:decimal"/>
            <xs:element name="pkgAmount" type="xs:decimal"/>
            <xs:element name="sellAmount" type="xs:decimal"/>
            <xs:element name="totalTax" type="xs:decimal"/>
            <xs:element name="taxes" type="SalesPerformaItemTax" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="SalesPerformaItemTax">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="code" type="xs:string"/>
            <xs:element name="type" type="TaxCategory"/>
            <xs:element name="percent" type="xs:decimal"/>
            <xs:element name="value" type="xs:decimal"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="SalesPerformaStatusEvent">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true"/>
            <xs:element name="fromStatus" type="xs:string"/>
            <xs:element name="toStatus" type="TaxCategory"/>
            <xs:element name="status" type="TransitionStatus"/>
            <xs:element name="updatedBy" type="IdCodeName"/>
            <xs:element name="updatedAt" type="xs:date" />
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="TransitionStatus" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="SUCCESS"/>
            <xs:enumeration value="FAILURE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="SalesPerformaType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="RETURN_TO_VENDOR" />
            <xs:enumeration value="B2B_SALES" />
            <xs:enumeration value="SCRAP" />
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="SalesPerformaStatus" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="INITIATED" />
            <xs:enumeration value="PERFORMA_GENERATED" />
            <xs:enumeration value="PENDING_DISPATCH" />
            <xs:enumeration value="CLOSED" />
            <xs:enumeration value="CANCELLED" />
        </xs:restriction>
    </xs:simpleType>


    <xs:simpleType name="DayCloseEventLogType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="WASTAGES"/>
            <xs:enumeration value="INVENTORY"/>
            <xs:enumeration value="TRANSFERS"/>
            <xs:enumeration value="BOOKINGS"/>
            <xs:enumeration value="RECEIVINGS"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="SwitchStatus" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="ACTIVE"/>
            <xs:enumeration value="IN_ACTIVE"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="PackagingType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="CASE"/>
            <xs:enumeration value="INNER"/>
            <xs:enumeration value="LOOSE"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="TimeFrequency" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="DAILY"/>
            <xs:enumeration value="WEEKLY"/>
            <xs:enumeration value="MONTHLY"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="AttributeType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="CATEGORY"/>
            <xs:enumeration value="DIMENSION"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="FulfillmentType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="KITCHEN"/>
            <xs:enumeration value="WAREHOUSE"/>
            <xs:enumeration value="EXTERNAL"/>
            <xs:enumeration value="INTERNAL"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="SCMOrderStatus" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="INITIATED"/>
            <xs:enumeration value="CREATED"/>
            <xs:enumeration value="ACKNOWLEDGED"/>
            <xs:enumeration value="TRANSFERRED"/>
            <xs:enumeration value="SETTLED"/>
            <xs:enumeration value="CANCELLED"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="PaymentCycle" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="DAILY"/>
            <xs:enumeration value="WEEKLY"/>
            <xs:enumeration value="FORTNIGHTLY"/>
            <xs:enumeration value="MONTHLY"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="NotificationType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="SMS"/>
            <xs:enumeration value="EMAIL"/>
            <xs:enumeration value="PUSH_NOTIFICATION"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="StockEventType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="CONSUMPTION"/>
            <xs:enumeration value="TRANSFER_OUT"/>
            <xs:enumeration value="WASTAGE"/>
            <xs:enumeration value="RECEIVED"/>
            <xs:enumeration value="OPENING"/>
            <xs:enumeration value="CLOSING"/>
            <xs:enumeration value="STOCK_TAKE"/>
            <xs:enumeration value="WH_CLOSING"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="StockEventStatus" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="INITIATED"/>
            <xs:enumeration value="CLOSED"/>
            <xs:enumeration value="SETTLED"/>
            <xs:enumeration value="CANCELLED"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="PriceUpdateEventStatus" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="INITIATED"/>
            <xs:enumeration value="APPROVED"/>
            <xs:enumeration value="CANCELLED"/>
            <xs:enumeration value="REJECTED"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="PriceUpdateEventType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="SKU_PRICE_UPDATE"/>
            <xs:enumeration value="PRODUCT_PRICE_UPDATE"/>
            <xs:enumeration value="RECIPE_PRICE_UPDATE"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="PriceUpdateEntryType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="SKU"/>
            <xs:enumeration value="PRODUCT"/>
            <xs:enumeration value="RECIPE"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="PriceUpdateEventActionType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="AUTOMATED"/>
            <xs:enumeration value="MANUAL"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="MimeType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="PDF"/>
            <xs:enumeration value="XLS/XLSX"/>
            <xs:enumeration value="IMG"/>
            <xs:enumeration value="CSV"/>
            <xs:enumeration value="TEXT"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="FileType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="PAN"/>
            <xs:enumeration value="CIN"/>
            <xs:enumeration value="SERVICE_TAX"/>
            <xs:enumeration value="CST"/>
            <xs:enumeration value="VAT"/>
            <xs:enumeration value="CHEQUE"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="AddressType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="REGISTERED_ADDRESS"/>
            <xs:enumeration value="DISPATCH_ADDRESS"/>
            <xs:enumeration value="VENDOR_ADDRESS"/>
            <xs:enumeration value="OTHER"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="DocUploadType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="COMPANY"/>
            <xs:enumeration value="PURCHASE_ORDER"/>
            <xs:enumeration value="ACCOUNT"/>
            <xs:enumeration value="GR"/>
            <xs:enumeration value="PAYMENT_REQUEST_INVOICE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="BankAccountType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="SAVINGS"/>
            <xs:enumeration value="CURRENT"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="VendorAccountType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="PRIMARY"/>
            <xs:enumeration value="SECONDARY"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="VendorStatus" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="INITIATED"/>
            <xs:enumeration value="IN_PROCESS"/>
            <xs:enumeration value="COMPLETED"/>
            <xs:enumeration value="CANCELLED"/>
            <xs:enumeration value="APPROVED"/>
            <xs:enumeration value="FAILED"/>
            <xs:enumeration value="EXPIRED"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="PurchaseOrderStatus" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="CREATED"/>
            <xs:enumeration value="APPROVED"/>
            <xs:enumeration value="CLOSED"/>
            <xs:enumeration value="CANCELLED"/>
            <xs:enumeration value="REJECTED"/>
            <xs:enumeration value="IN_PROGRESS"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="POCreationType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="MANUAL"/>
            <xs:enumeration value="SYSTEM_GENERATED"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="VendorType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="PRIMARY"/>
            <xs:enumeration value="SECONDARY"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="GstApplicationStatus" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="REGISTERED"/>
            <xs:enumeration value="UNREGISTERED"/>
            <xs:enumeration value="APPLIED_FOR"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="TaxCategory" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="IGST"/>
            <xs:enumeration value="CGST"/>
            <xs:enumeration value="SGST"/>
            <xs:enumeration value="CESS1"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="GRDocType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="INVOICE"/>
            <xs:enumeration value="DELIVERY_CHALLAN"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ProductionItemType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="REQUESTED"/>
            <xs:enumeration value="ADDITIONAL"/>
            <xs:enumeration value="SKU"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="BookingStatus" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="INITIATED"/>
            <xs:enumeration value="CREATED"/>
            <xs:enumeration value="CANCELLED"/>
            <xs:enumeration value="CLOSED"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="PriceTransactionType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="MANUAL_ADDITION"/>
            <xs:enumeration value="MANUAL_OVERRIDE"/>
            <xs:enumeration value="RECEIVING"/>
            <xs:enumeration value="CONSUMPTION"/>
        </xs:restriction>
    </xs:simpleType>


    <xs:simpleType name="VarianceType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="ZERO_VARIANCE"/>
            <xs:enumeration value="COGS_CONTROLLABLES"/>
            <xs:enumeration value="CONSUMABLES_CONTROLLABLES"/>
            <xs:enumeration value="TBD"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="PaymentRequestType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="INDEPENDENT"/>
            <xs:enumeration value="GOODS_RECEIVED"/>
            <xs:enumeration value="SERVICE_ORDER"/>
            <xs:enumeration value="ADVANCE_PAYMENT"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="PaymentDeviationType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="DEVIATION"/>
            <xs:enumeration value="REJECTION"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="PaymentDeviationLevel" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="INVOICE"/>
            <xs:enumeration value="INVOICE_ITEM"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="PaymentRequestStatus" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="INITIATED"/>
            <xs:enumeration value="CREATED"/>
            <xs:enumeration value="ACKNOWLEDGED"/>
            <xs:enumeration value="APPROVED"/>
            <xs:enumeration value="SENT_FOR_PAYMENT"/>
            <xs:enumeration value="REJECTED"/>
            <xs:enumeration value="PAID"/>
            <xs:enumeration value="CANCELLED"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="PaymentDeviationStatus" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="CREATED"/>
            <xs:enumeration value="ACCEPTED"/>
            <xs:enumeration value="REJECTED"/>
            <xs:enumeration value="REMOVED"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="VendorGRPaymentStatus" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="FRESH"/>
            <xs:enumeration value="ALREADY_CREATED"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="PaymentType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="NEFT"/>
            <xs:enumeration value="RTGS"/>
            <xs:enumeration value="IMPS"/>
            <xs:enumeration value="IFT"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="GRProcessingStatus" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="PENDING"/>
            <xs:enumeration value="IN_PROCESS"/>
            <xs:enumeration value="PROCESSED"/>
        </xs:restriction>
    </xs:simpleType>

</xs:schema>
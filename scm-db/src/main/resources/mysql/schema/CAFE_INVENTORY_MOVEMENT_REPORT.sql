DROP TABLE IF EXISTS `KETTLE_SCM_ARCHIVE`.`INVENTORY_MOVEMENT_REPORT_DUMP`;
-- CAFE_INVENTORY_MOVEMENT_REPORT_DUMP `INVENTORY_MOVEMENT_REPORT_DUMP`
-- FIRST DROP AND CREATE TABLE IF EXISTS
CREATE TABLE `KETTLE_SCM_ARCHIVE`.`INVENTORY_MOVEMENT_REPORT_DUMP` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `BUSINESS_DATE` date DEFAULT NULL,
  `UNIT_ID` int(11) DEFAULT NULL,
  `PRODUCT_ID` int(11) DEFAULT NULL,
  `SKU_ID` int(11) DEFAULT NULL,
  `PRODUCT_NAME` varchar(200) DEFAULT NULL,
  `CATEGORY_NAME` varchar(200) DEFAULT NULL,
  `STOCK_TYPE` varchar(100) DEFAULT NULL,
  `OPENING_DAY_CLOSE_EVENT_ID` int(11) DEFAULT NULL,
  `OPENING_GENERATION_TIME` datetime DEFAULT NULL,
  `OPENING_STOCK_QUANTITY` decimal(16,9) DEFAULT NULL,
  `OPENING_STOCK_PRICE` decimal(16,9) DEFAULT '0.000000',
  `OPENING_STOCK_TAX` decimal(16,9) DEFAULT '0.000000',
  `OPENING_STOCK_VALUE_WITHOUT_TAX` decimal(16,9) DEFAULT '0.000000',
  `OPENING_STOCK_VALUE_WITH_TAX` decimal(16,9) DEFAULT '0.000000',
  `CLOSING_DAY_CLOSE_EVENT_ID` int(11) DEFAULT NULL,
  `CLOSING_GENERATION_TIME` datetime DEFAULT NULL,
  `CLOSING_EVENT_FREQUENCY` VARCHAR(255) DEFAULT NULL,
  `CLOSING_STOCK_QUANTITY` decimal(16,9) DEFAULT NULL,
  `EXPECTED_CLOSING_VALUE_STOCK_INVENTORY` decimal(16,9) DEFAULT '0.000000',
  `EXPECTED_CLOSING_STOCK` decimal(16,9) DEFAULT '0.000000',
  `EXPECTED_CLOSING_STOCK_AFTER_VARIANCE` decimal(16,9) DEFAULT '0.000000',
  `CLOSING_STOCK_PRICE` decimal(16,9) DEFAULT '0.000000',
  `CLOSING_STOCK_TAX` decimal(16,9) DEFAULT '0.000000',
  `CLOSING_STOCK_VALUE_WITHOUT_TAX` decimal(16,9) DEFAULT '0.000000',
  `CLOSING_STOCK_VALUE_WITH_TAX` decimal(16,9) DEFAULT '0.000000',
  `VARIANCE_QUANTITY` decimal(16,9) DEFAULT '0.000000',
  `VARIANCE_PRICE` decimal(16,9) DEFAULT '0.000000',
  `VARIANCE_TAX` decimal(16,9) DEFAULT '0.000000',
  `VARIANCE_VALUE_WITHOUT_TAX` decimal(16,9) DEFAULT '0.000000',
  `VARIANCE_VALUE_WITH_TAX` decimal(16,9) DEFAULT '0.000000',
  `TRANSFERRED_QUANTITY` decimal(16,9) DEFAULT '0.000000',
  `TRANSFERRED_PRICE` decimal(16,9) DEFAULT '0.000000',
  `TRANSFERRED_TAX` decimal(16,9) DEFAULT '0.000000',
  `TRANSFERRED_VALUE_WITHOUT_TAX` decimal(16,9) DEFAULT '0.000000',
  `TRANSFERRED_VALUE_WITH_TAX` decimal(16,9) DEFAULT '0.000000',
  `GOOD_RECEIVED_QUANTITY` decimal(16,9) DEFAULT '0.000000',
  `GOOD_RECEIVED_PRICE` decimal(16,9) DEFAULT '0.000000',
  `GOOD_RECEIVED_TAX` decimal(16,9) DEFAULT '0.000000',
  `GOOD_RECEIVED_VALUE_WITHOUT_TAX` decimal(16,9) DEFAULT '0.000000',
  `GOOD_RECEIVED_VALUE_WITH_TAX` decimal(16,9) DEFAULT '0.000000',
  `WASTAGE_QUANTITY` decimal(16,9) DEFAULT '0.000000',
  `WASTAGE_PRICE` decimal(16,9) DEFAULT '0.000000',
  `WASTAGE_TAX` decimal(16,9) DEFAULT '0.000000',
  `WASTAGE_VALUE_WITHOUT_TAX` decimal(16,9) DEFAULT '0.000000',
  `WASTAGE_VALUE_WITH_TAX` decimal(16,9) DEFAULT '0.000000',
  `CONSUMPTION_QUANTITY` decimal(16,9) DEFAULT '0.*********',
  `CONSUMPTION_PRICE` decimal(16,9) DEFAULT '0.*********',
  `CONSUMPTION_TAX` decimal(16,9) DEFAULT '0.*********',
  `CONSUMPTION_VALUE_WITHOUT_TAX` decimal(16,9) DEFAULT '0.*********',
  `CONSUMPTION_VALUE_WITH_TAX` decimal(16,9) DEFAULT '0.*********',
  `VENDOR_GOOD_RECEIVED_QUANTITY` decimal(16,9) DEFAULT '0.*********',
  `VENDOR_GOOD_RECEIVED_PRICE` decimal(16,9) DEFAULT '0.*********',
  `VENDOR_GOOD_RECEIVED_TAX` decimal(16,9) DEFAULT '0.*********',
  `VENDOR_GOOD_RECEIVED_VALUE_WITHOUT_TAX` decimal(16,9) DEFAULT '0.*********',
  `VENDOR_GOOD_RECEIVED_VALUE_WITH_TAX` decimal(16,9) DEFAULT '0.*********',
  `MISSING_OPENING_EVENT` varchar(1) DEFAULT NULL,
  `MISSING_CLOSING_EVENT` varchar(1) DEFAULT NULL,
  `MAX_CLOSING_EVENT_ID` INT DEFAULT NULL,
  `FOUND_MONTHLY` varchar(1) DEFAULT NULL,
  PRIMARY KEY (`ID`),
  KEY `idx_INVENTORY_MOVEMENT_REPORT_BUSINESS_DATE` (`BUSINESS_DATE`),
  KEY `idx_INVENTORY_MOVEMENT_REPORT_UNIT_ID` (`UNIT_ID`),
  KEY `idx_INVENTORY_MOVEMENT_REPORT_DAY_CLOSE_EVENT_ID_O` (`OPENING_DAY_CLOSE_EVENT_ID`),
  KEY `idx_INVENTORY_MOVEMENT_REPORT_DAY_CLOSE_EVENT_ID_C` (`CLOSING_DAY_CLOSE_EVENT_ID`),
  KEY `idx_INVENTORY_MOVEMENT_REPORT_PRODUCT_ID` (`PRODUCT_ID`),
  KEY `idx_INVENTORY_MOVEMENT_REPORT_SKU_ID` (`SKU_ID`),
  KEY `idx_INVENTORY_MOVEMENT_REPORT_CATEGORY_NAME` (`CATEGORY_NAME`),
  KEY `idx_INVENTORY_MOVEMENT_REPORT_STOCK_TYPE` (`STOCK_TYPE`),
  KEY `IDX_FOUND_MONTHLY` (`FOUND_MONTHLY`)
);


-- CREATE TABLE OF CAFE INVENTORY MOVEMENT `CAFE_INVENTORY_MOVEMENT_REPORT_DUMP` AND CREATING STORED PROCEDURE
-- HARD CODED DATE RANGES CHANGE THIS WHEN EVER U WANT TO GENERATE MOVEMENT REPORT
DROP PROCEDURE IF EXISTS `KETTLE_SCM_ARCHIVE`.`CAFE_INVENTORY_MOVEMENT_PROCEDURE`;
DELIMITER $$
CREATE DEFINER=`rptusr`@`%` PROCEDURE `KETTLE_SCM_ARCHIVE`.`CAFE_INVENTORY_MOVEMENT_PROCEDURE`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE CURRENT_UNIT_ID INT;
    DECLARE cursor_units CURSOR FOR
        SELECT UNIT_ID
        FROM KETTLE_MASTER_DUMP.UNIT_DETAIL UD
        WHERE UD.UNIT_CATEGORY = "CAFE";

    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    OPEN cursor_units;

    read_loop: LOOP
        FETCH cursor_units INTO CURRENT_UNIT_ID;
        IF done THEN
            LEAVE read_loop;
        END IF;

DELETE FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP;

INSERT INTO KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP (UNIT_ID, PRODUCT_ID,PRODUCT_NAME,STOCK_TYPE,CATEGORY_NAME)
SELECT CURRENT_UNIT_ID,PD.PRODUCT_ID,PD.PRODUCT_NAME,PD.STOCK_KEEPING_FREQUENCY,CD.CATEGORY_NAME FROM
KETTLE_SCM_DUMP.PRODUCT_DEFINITION PD
INNER JOIN KETTLE_SCM_DUMP.CATEGORY_DEFINITION CD ON CD.CATEGORY_ID = PD.CATEGORY_ID
WHERE PD.CATEGORY_ID <> 3
and PD.STOCK_KEEPING_FREQUENCY <> 'FIXED_ASSETS';

-- SELECT * FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP;

-- GETTING THE OPENING EVENT
-- NEED TO KEEP A CHECK OF OPENING/STOCK_TAKE
UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IMR ,
(SELECT B.* FROM
		(SELECT DT.* FROM (
			SELECT
				SI.UNIT_ID,
                SI.PRODUCT_ID,
                MAX(SI.CURRENT_EVENT_ID) CURRENT_EVENT_ID,
                MAX(SI.GENERATION_TIME) GENERATION_TIME,
                MAX(DCE.BUSINESS_DATE) BUSINESS_DATE
			FROM
				KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IM INNER JOIN
				KETTLE_SCM_DUMP.STOCK_INVENTORY SI ON IM.PRODUCT_ID = SI.PRODUCT_ID INNER JOIN
                KETTLE_SCM_DUMP.DAY_CLOSE_EVENT DCE ON SI.CURRENT_EVENT_ID = DCE.EVENT_ID AND SI.STOCK_TYPE = DCE.CLOSURE_EVENT_FREQUENCY AND DCE.UNIT_ID = CURRENT_UNIT_ID and DCE.STATUS = 'CLOSED'
                AND DCE.CLOSURE_EVENT_TYPE = "STOCK_TAKE"
			and DCE.BUSINESS_DATE < '2023-04-01' AND DCE.BUSINESS_DATE > '2023-02-28'

			GROUP BY SI.UNIT_ID , SI.PRODUCT_ID) DT) B) A
	SET
		IMR.OPENING_DAY_CLOSE_EVENT_ID = A.CURRENT_EVENT_ID,
		IMR.OPENING_GENERATION_TIME= A.GENERATION_TIME,
        IMR.BUSINESS_DATE = A.BUSINESS_DATE
	WHERE
		IMR.PRODUCT_ID = A.PRODUCT_ID AND
		IMR.UNIT_ID = A.UNIT_ID ;

  -- UPDATING THE CLOSING EVENT ID
  UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IMR ,
(SELECT B.* FROM
		(SELECT DT.* FROM (
			SELECT
				SI.UNIT_ID,
                SI.PRODUCT_ID,
                MAX(SI.CURRENT_EVENT_ID) CURRENT_EVENT_ID,
                MAX(SI.GENERATION_TIME) GENERATION_TIME
			FROM
				KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IM INNER JOIN
				KETTLE_SCM_DUMP.STOCK_INVENTORY SI ON IM.PRODUCT_ID = SI.PRODUCT_ID INNER JOIN
                KETTLE_SCM_DUMP.DAY_CLOSE_EVENT DCE ON SI.CURRENT_EVENT_ID = DCE.EVENT_ID AND SI.STOCK_TYPE = DCE.CLOSURE_EVENT_FREQUENCY AND  DCE.UNIT_ID = CURRENT_UNIT_ID and DCE.STATUS = 'CLOSED'
                AND DCE.CLOSURE_EVENT_TYPE = "STOCK_TAKE"
			AND DCE.BUSINESS_DATE <= '2023-04-30' AND DCE.BUSINESS_DATE > '2023-04-01'
			GROUP BY SI.UNIT_ID , SI.PRODUCT_ID) DT) B) A
	SET
		IMR.CLOSING_DAY_CLOSE_EVENT_ID = A.CURRENT_EVENT_ID,
		IMR.CLOSING_GENERATION_TIME= A.GENERATION_TIME
	WHERE
		IMR.PRODUCT_ID = A.PRODUCT_ID AND
		IMR.UNIT_ID = A.UNIT_ID;



-- DELETING ENTRIES FROM DB
-- NEED TO TAKE ALL DUMPS AT START AFTER GETTING THE MIN AND MAX OF DAY CLOSE ID
-- 1. CHECKING PRODUCTIDS FROM DAY CLOSE PRODUCT VALUES
-- 2.STOCK INVENTORY
-- 3.GR
-- 4.TO
-- 5.WASTAGE



 -- NOW SETTING THE OPENING EVENT AS MINIMUM EVENT FOUND FOR THAT UNIT AND CALCULATING FROM THERE AND SETTING OPENING AS 0
  UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IM,
    (
    SELECT
    UNIT_ID,
    min(OPENING_DAY_CLOSE_EVENT_ID) MIN_OPENING_EVENT,
    coalesce(min(OPENING_GENERATION_TIME),"2023-04-01") MIN_OPENING_GENERATION_TIME
    FROM
    KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP
    GROUP BY UNIT_ID
    ) X
 SET
     IM.OPENING_DAY_CLOSE_EVENT_ID = X.MIN_OPENING_EVENT,
     IM.OPENING_STOCK_QUANTITY = 0,
     IM.OPENING_STOCK_TAX = 0,
     IM.OPENING_STOCK_VALUE_WITHOUT_TAX = 0,
     IM.OPENING_STOCK_VALUE_WITH_TAX = 0,
     IM.OPENING_GENERATION_TIME = X.MIN_OPENING_GENERATION_TIME,
     IM.MISSING_OPENING_EVENT = "Y"
 WHERE
	X.UNIT_ID = IM.UNIT_ID AND IM.OPENING_DAY_CLOSE_EVENT_ID IS NULL;

 -- NOW SETTING THE OPENING EVENT AS MINIMUM EVENT FOUND FOR THAT UNIT AND CALCULATING FROM THERE AND SETTING OPENING AS 0


    UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IM,
    (
    SELECT
    UNIT_ID,
    MAX(CLOSING_DAY_CLOSE_EVENT_ID) MAX_CLOSING_DAY_CLOSE_EVENT_ID,
    coalesce(MAX(CLOSING_GENERATION_TIME),"2023-04-30 23:59:59") MAX_CLOSING_GENERATION_TIME
    FROM
    KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP
    GROUP BY UNIT_ID
    ) X
 SET
     IM.CLOSING_DAY_CLOSE_EVENT_ID = X.MAX_CLOSING_DAY_CLOSE_EVENT_ID,
     IM.CLOSING_GENERATION_TIME = X.MAX_CLOSING_GENERATION_TIME,
     IM.CLOSING_STOCK_QUANTITY = 0,
     IM.CLOSING_STOCK_TAX = 0,
     IM.CLOSING_STOCK_VALUE_WITHOUT_TAX = 0,
     IM.CLOSING_STOCK_VALUE_WITH_TAX = 0,
     IM.MISSING_CLOSING_EVENT = "Y"
 WHERE
	X.UNIT_ID = IM.UNIT_ID AND IM.CLOSING_DAY_CLOSE_EVENT_ID IS NULL;

 UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IM,
    (
    SELECT
    UNIT_ID,
    MAX(CLOSING_DAY_CLOSE_EVENT_ID) MAX_CLOSING_DAY_CLOSE_EVENT_ID
    FROM
    KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP
    GROUP BY UNIT_ID
    ) X
 SET
     IM.MAX_CLOSING_EVENT_ID = X.MAX_CLOSING_DAY_CLOSE_EVENT_ID
 WHERE
	X.UNIT_ID = IM.UNIT_ID;


SET @DISTINCT_CLOSING_EVENT_IDS = "";
SET @DISTINCT_CLOSING_EVENT_IDS = (SELECT group_concat(DISTINCT CLOSING_DAY_CLOSE_EVENT_ID) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP);

 UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IM,
    (
    SELECT * FROM
    KETTLE_SCM_DUMP.DAY_CLOSE_EVENT WHERE FIND_IN_SET(EVENT_ID,@DISTINCT_CLOSING_EVENT_IDS)
    ) X
 SET
     IM.CLOSING_EVENT_FREQUENCY = X.CLOSURE_EVENT_FREQUENCY
 WHERE
	X.EVENT_ID = IM.CLOSING_DAY_CLOSE_EVENT_ID;



UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP SET FOUND_MONTHLY = "Y" WHERE CLOSING_EVENT_FREQUENCY = "MONTHLY";


-- CREATING DUMP OF DATA OF GR DATA
DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.GOODS_RECEIVED_CAFE;

CREATE TEMPORARY TABLE `KETTLE_SCM_ARCHIVE`.`GOODS_RECEIVED_CAFE` (
  `GOODS_RECEIVED_ID` int(11) NOT NULL AUTO_INCREMENT,
  `GENERATION_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `GENERATION_UNIT_ID` int(11) NOT NULL,
  `SOURCE_COMPANY_ID` int(11) NOT NULL,

  `GENERATED_FOR_UNIT_ID` int(11) NOT NULL,
  `RECEIVING_COMPANY_ID` int(11) NOT NULL,
  `GENERATED_BY` int(11) NOT NULL,
  `GOODS_RECEIVED_STATUS` varchar(30) NOT NULL,
  `COMMENT` varchar(1000) DEFAULT NULL,
  `REQUEST_ORDER_ID` int(11) DEFAULT NULL,
  `TRANSFER_ORDER_ID` int(11) DEFAULT NULL,
  `LAST_UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `PURCHASE_ORDER_ID` int(11) DEFAULT NULL,
  `RECEIVED_BY` int(11) DEFAULT NULL,
  `TOTAL_AMOUNT` decimal(16,6) DEFAULT NULL,
  `CANCELLED_BY` int(11) DEFAULT NULL,
  `IS_AUTO_GENERATED` varchar(1) DEFAULT 'N',
  `PARENT_GR` int(11) DEFAULT NULL,
  `CLOSURE_EVENT_ID` int(11) DEFAULT NULL,
  `IS_REJECTED_GR` varchar(1) DEFAULT 'N',
  `REJECT_GR_COMMENT` varchar(1000) DEFAULT NULL,
  `PARENT_GR_COMMENT` varchar(1000) DEFAULT NULL,
  `TO_TYPE` varchar(255) DEFAULT NULL,
  `INVOICE_ID` int(11) DEFAULT NULL,
  `POR_IMAGES_DOC_IDS` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`GOODS_RECEIVED_ID`)
);




-- CREATE TABLE COZ DEFAULT VALUE
INSERT INTO KETTLE_SCM_ARCHIVE.GOODS_RECEIVED_CAFE
SELECT * FROM KETTLE_SCM_DUMP.GOODS_RECEIVED WHERE LAST_UPDATE_TIME >= (select min(OPENING_GENERATION_TIME) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP) AND  LAST_UPDATE_TIME <= (SElect MAX(CLOSING_GENERATION_TIME) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP) AND GENERATED_FOR_UNIT_ID
IN (SELECT DISTINCT UNIT_ID FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP);

-- CREATING GR ITEM DATA
DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.GOODS_RECEIVED_ITEM_CAFE ;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.GOODS_RECEIVED_ITEM_CAFE
SELECT ID.* FROM KETTLE_SCM_DUMP.GOODS_RECEIVED_ITEM ID WHERE GOODS_RECEIVED_ID IN (select  DISTINCT GOODS_RECEIVED_ID FROM KETTLE_SCM_ARCHIVE.GOODS_RECEIVED_CAFE);


-- CREATING DUMP OF DATA OF GR DATA
DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.TRANSFER_ORDER_CAFE;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.TRANSFER_ORDER_CAFE
SELECT * FROM KETTLE_SCM_DUMP.TRANSFER_ORDER WHERE GENERATION_TIME >= (select min(OPENING_GENERATION_TIME) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP) AND  GENERATION_TIME <= (SElect MAX(CLOSING_GENERATION_TIME) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP) AND
GENERATION_UNIT_ID IN (SELECT DISTINCT UNIT_ID FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP);

-- CREATING GR ITEM DATA
DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.TRANSFER_ORDER_ITEM_CAFE ;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.TRANSFER_ORDER_ITEM_CAFE
SELECT ID.* FROM KETTLE_SCM_DUMP.TRANSFER_ORDER_ITEM ID WHERE TRANSFER_ORDER_ID IN (select distinct TRANSFER_ORDER_ID FROM KETTLE_SCM_ARCHIVE.TRANSFER_ORDER_CAFE);



-- CREATING DUMP OF DATA OF GR DATA
DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.VENDOR_GOODS_RECEIVED_DATA_CAFE;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.VENDOR_GOODS_RECEIVED_DATA_CAFE
SELECT * FROM KETTLE_SCM_DUMP.VENDOR_GOODS_RECEIVED_DATA WHERE CREATED_AT >= (select min(OPENING_GENERATION_TIME) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP) AND  CREATED_AT <= (SElect MAX(CLOSING_GENERATION_TIME) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP) AND
DELIVERY_UNIT_ID IN (SELECT DISTINCT UNIT_ID FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP);

-- CREATING GR ITEM DATA
DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.VENDOR_GR_ITEM_DETAIL_CAFE ;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.VENDOR_GR_ITEM_DETAIL_CAFE
SELECT ID.* FROM KETTLE_SCM_DUMP.VENDOR_GR_ITEM_DETAIL ID WHERE VENDOR_GR_ID IN (select DISTINCT GOODS_RECEIVED_ID FROM KETTLE_SCM_ARCHIVE.VENDOR_GOODS_RECEIVED_DATA_CAFE);



-- CREATING DUMP OF DATA OF WASTAGE DATA
DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.WASTAGE_EVENT_CAFE;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.WASTAGE_EVENT_CAFE
SELECT * FROM KETTLE_SCM_DUMP.WASTAGE_EVENT WHERE GENERATION_TIME >= (select min(OPENING_GENERATION_TIME) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP) AND  GENERATION_TIME <= (SElect MAX(CLOSING_GENERATION_TIME) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP) AND
UNIT_ID IN (SELECT DISTINCT UNIT_ID FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP);

-- CREATING GR ITEM DATA
DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.WASTAGE_ITEM_DATA_CAFE;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.WASTAGE_ITEM_DATA_CAFE
SELECT ID.* FROM KETTLE_SCM_DUMP.WASTAGE_ITEM_DATA ID WHERE WASTAGE_ID IN (select DISTINCT WASTAGE_ID FROM KETTLE_SCM_ARCHIVE.WASTAGE_EVENT_CAFE);



-- DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.DAY_CLOSE_EVENT_CAFE;
-- CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.DAY_CLOSE_EVENT_CAFE
-- SELECT * FROM KETTLE_SCM_DUMP.DAY_CLOSE_EVENT WHERE EVENT_ID >= (select min(OPENING_DAY_CLOSE_EVENT_ID) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP) AND  EVENT_ID <= (SElect MAX(CLOSING_DAY_CLOSE_EVENT_ID) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP) AND
-- UNIT_ID IN (SELECT DISTINCT UNIT_ID FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP);

SET @MIN_OPENING_DAY_CLOSE_EVENT_ID = NULL;
SET @MIN_OPENING_DAY_CLOSE_EVENT_ID = (select coalesce(min(OPENING_DAY_CLOSE_EVENT_ID), -1) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP);

SET @MAX_CLOSING_DAY_CLOSE_EVENT_ID = NULL;
SET @MAX_CLOSING_DAY_CLOSE_EVENT_ID = (select coalesce(MAX(CLOSING_DAY_CLOSE_EVENT_ID),-1) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP);

IF (@MIN_OPENING_DAY_CLOSE_EVENT_ID = -1 OR @MAX_CLOSING_DAY_CLOSE_EVENT_ID = -1) THEN
DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.DAY_CLOSE_EVENT_CAFE;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.DAY_CLOSE_EVENT_CAFE
SELECT * FROM KETTLE_SCM_DUMP.DAY_CLOSE_EVENT WHERE GENERATION_TIME >= (select min(OPENING_GENERATION_TIME) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP) AND  GENERATION_TIME <= (SElect MAX(CLOSING_GENERATION_TIME) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP) AND
UNIT_ID IN (SELECT DISTINCT UNIT_ID FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP);
ELSE
DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.DAY_CLOSE_EVENT_CAFE;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.DAY_CLOSE_EVENT_CAFE
SELECT * FROM KETTLE_SCM_DUMP.DAY_CLOSE_EVENT WHERE EVENT_ID >= (select min(OPENING_DAY_CLOSE_EVENT_ID) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP) AND  EVENT_ID <= (SElect MAX(CLOSING_DAY_CLOSE_EVENT_ID) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP) AND
UNIT_ID IN (SELECT DISTINCT UNIT_ID FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP);
END IF;


DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.DAY_CLOSE_PRODUCT_VALUES_CAFE;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.DAY_CLOSE_PRODUCT_VALUES_CAFE
SELECT * FROM KETTLE_SCM_DUMP.DAY_CLOSE_PRODUCT_VALUES WHERE EVENT_ID IN (SELECT DISTINCT EVENT_ID FROM KETTLE_SCM_ARCHIVE.DAY_CLOSE_EVENT_CAFE);


DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.STOCK_INVENTORY_CAFE;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.STOCK_INVENTORY_CAFE
SELECT * FROM KETTLE_SCM_DUMP.STOCK_INVENTORY WHERE CURRENT_EVENT_ID IN (SELECT DISTINCT EVENT_ID FROM KETTLE_SCM_ARCHIVE.DAY_CLOSE_EVENT_CAFE);

-- GETTING ALL THE UNIQUE PRODUCT IDS FOR THAT CAFE WHOSE TRANSACTION ACTUALLY HAPPEND


-- DELETE FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP WHERE PRODUCT_ID NOT IN (
-- select distinct PRODUCT_ID from (
-- SELECT distinct PRODUCT_ID FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP WHERE CLOSING_STOCK_QUANTITY IS NOT NULL
-- UNION ALL
-- SELECT distinct PRODUCT_ID FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP WHERE OPENING_STOCK_QUANTITY IS NOT NULL
-- UNION ALL
-- SELECT distinct PRODUCT_ID FROM KETTLE_SCM_ARCHIVE.DAY_CLOSE_PRODUCT_VALUES_CAFE
-- UNION ALL
-- SELECT DISTINCT PRODUCT_ID FROM KETTLE_SCM_ARCHIVE.STOCK_INVENTORY_CAFE
-- UNION ALL
-- (
-- 	SELECT distinct PD.PRODUCT_ID FROM
--     KETTLE_SCM_ARCHIVE.GOODS_RECEIVED_CAFE GR INNER JOIN
--     KETTLE_SCM_ARCHIVE.GOODS_RECEIVED_ITEM_CAFE GRI ON GRI.GOODS_RECEIVED_ID = GR.GOODS_RECEIVED_ID AND GR.GOODS_RECEIVED_STATUS = "SETTLED"
--             INNER JOIN KETTLE_SCM_DUMP.SKU_DEFINITION SD ON GRI.SKU_ID = SD.SKU_ID
--             INNER JOIN KETTLE_SCM_DUMP.PRODUCT_DEFINITION PD ON SD.LINKED_PRODUCT_ID = PD.PRODUCT_ID
-- )
-- UNION ALL
-- (
-- 	SELECT distinct PD.PRODUCT_ID FROM
--         KETTLE_SCM_ARCHIVE.TRANSFER_ORDER_CAFE TR
--     INNER JOIN KETTLE_SCM_ARCHIVE.TRANSFER_ORDER_ITEM_CAFE TOI ON TOI.TRANSFER_ORDER_ID = TR.TRANSFER_ORDER_ID
--     AND TR.TRANSFER_ORDER_STATUS IN ('CREATED','SETTLED','TRANSFERRED')
--     INNER JOIN KETTLE_SCM_DUMP.SKU_DEFINITION SD ON TOI.SKU_ID = SD.SKU_ID
--             INNER JOIN KETTLE_SCM_DUMP.PRODUCT_DEFINITION PD ON SD.LINKED_PRODUCT_ID = PD.PRODUCT_ID
-- )

-- UNION ALL
-- (
-- SELECT distinct PD.PRODUCT_ID
-- 	FROM KETTLE_SCM_ARCHIVE.VENDOR_GOODS_RECEIVED_DATA_CAFE VGR
-- 		INNER JOIN KETTLE_SCM_ARCHIVE.VENDOR_GR_ITEM_DETAIL_CAFE  VGRI ON VGR.GOODS_RECEIVED_ID = VGRI.VENDOR_GR_ID AND GOODS_RECEIVED_STATUS IN ("CREATED", "INITIATED")
--         INNER JOIN KETTLE_SCM_DUMP.SKU_DEFINITION SD ON VGRI.SKU_ID = SD.SKU_ID
--             INNER JOIN KETTLE_SCM_DUMP.PRODUCT_DEFINITION PD ON SD.LINKED_PRODUCT_ID = PD.PRODUCT_ID
-- )

-- UNION ALL
-- (
-- SELECT distinct WID.PRODUCT_ID
-- FROM
-- 		KETTLE_SCM_ARCHIVE.WASTAGE_EVENT_CAFE WE
-- 	INNER JOIN
-- 		KETTLE_SCM_ARCHIVE.WASTAGE_ITEM_DATA_CAFE WID ON WE.WASTAGE_ID=WID.WASTAGE_ID AND WE.STATUS = "SETTLED"
-- )) a);

-- SETTING THE OPENING STOCK OF THE OPENING EVNET THAT IS CLOSING OF OPENING

UPDATE
	KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IMR ,
	(SELECT B.* FROM
		(SELECT DT.* FROM (
			SELECT
				SI.UNIT_ID,
                SI.PRODUCT_ID,
                SI.CURRENT_EVENT_ID,
                SI.CLOSING_STOCK,
                SI.VARIANCE_COST,
                SI.VARIANCE_PRICE,
                SI.TAX_PERCENTAGE
			FROM
				KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IM
					INNER JOIN
				KETTLE_SCM_DUMP.STOCK_INVENTORY SI ON IM.UNIT_ID = SI.UNIT_ID
                 INNER JOIN
                KETTLE_SCM_DUMP.DAY_CLOSE_EVENT DCE ON SI.CURRENT_EVENT_ID = DCE.EVENT_ID AND SI.STOCK_TYPE = DCE.CLOSURE_EVENT_FREQUENCY
							AND IM.OPENING_DAY_CLOSE_EVENT_ID = SI.CURRENT_EVENT_ID
                            AND IM.PRODUCT_ID = SI.PRODUCT_ID
				group by SI.UNIT_ID, SI.PRODUCT_ID
                ) DT) B) A
	SET
		IMR.OPENING_STOCK_QUANTITY = A.CLOSING_STOCK,
		IMR.OPENING_STOCK_PRICE = A.VARIANCE_PRICE,
		IMR.OPENING_STOCK_TAX = (
			CASE WHEN A.TAX_PERCENTAGE IS NULL OR A.TAX_PERCENTAGE = 0 THEN 0
            ELSE ROUND(((A.TAX_PERCENTAGE/100) * (COALESCE(A.CLOSING_STOCK,0) * COALESCE(A.VARIANCE_PRICE,0))),6)
            END
        ),
		IMR.OPENING_STOCK_VALUE_WITHOUT_TAX = coalesce(A.CLOSING_STOCK,0) * coalesce(A.VARIANCE_PRICE, 0),
		IMR.OPENING_STOCK_VALUE_WITH_TAX = (coalesce(A.CLOSING_STOCK,0) * coalesce(A.VARIANCE_PRICE, 0)) + (
         CASE WHEN A.TAX_PERCENTAGE IS NULL OR A.TAX_PERCENTAGE = 0 THEN 0
            ELSE ROUND(((A.TAX_PERCENTAGE/100) * (COALESCE(A.CLOSING_STOCK,0) * COALESCE(A.VARIANCE_PRICE,0))),6)
            END
			)
	WHERE
		IMR.PRODUCT_ID = A.PRODUCT_ID
        AND IMR.UNIT_ID = A.UNIT_ID
        AND IMR.OPENING_DAY_CLOSE_EVENT_ID = A.CURRENT_EVENT_ID;



-- UPDATING THE CLOSING STOCK FROM THE CLOSING EVENT

UPDATE
	KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IMR ,
	(SELECT B.* FROM
		(SELECT DT.* FROM (
			SELECT
				SI.UNIT_ID,
                SI.PRODUCT_ID,
                SI.CURRENT_EVENT_ID,
                SI.EXPECTED_CLOSING_VALUE,
                SI.CLOSING_STOCK,
                SI.VARIANCE_COST,
                SI.VARIANCE_PRICE,
                SI.TAX_PERCENTAGE
			FROM
				KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IM
					INNER JOIN
				KETTLE_SCM_DUMP.STOCK_INVENTORY SI ON IM.UNIT_ID = SI.UNIT_ID
                 INNER JOIN
                KETTLE_SCM_DUMP.DAY_CLOSE_EVENT DCE ON SI.CURRENT_EVENT_ID = DCE.EVENT_ID AND SI.STOCK_TYPE = DCE.CLOSURE_EVENT_FREQUENCY
							AND IM.CLOSING_DAY_CLOSE_EVENT_ID = SI.CURRENT_EVENT_ID
                            AND IM.PRODUCT_ID = SI.PRODUCT_ID
				group by SI.UNIT_ID, SI.PRODUCT_ID
                ) DT) B) A
	SET
		IMR.EXPECTED_CLOSING_STOCK = A.EXPECTED_CLOSING_VALUE,
		IMR.CLOSING_STOCK_QUANTITY = A.CLOSING_STOCK,
		IMR.CLOSING_STOCK_PRICE = A.VARIANCE_PRICE,
		IMR.CLOSING_STOCK_TAX = (
			CASE WHEN A.TAX_PERCENTAGE IS NULL OR A.TAX_PERCENTAGE = 0 THEN 0
            ELSE ROUND(((A.TAX_PERCENTAGE/100)* (COALESCE(A.CLOSING_STOCK,0) * COALESCE(A.VARIANCE_PRICE,0))),6)
            END
        ),
		IMR.CLOSING_STOCK_VALUE_WITHOUT_TAX = coalesce(A.CLOSING_STOCK,0) * coalesce(A.VARIANCE_PRICE, 0),
		IMR.CLOSING_STOCK_VALUE_WITH_TAX = (coalesce(A.CLOSING_STOCK,0) * coalesce(A.VARIANCE_PRICE, 0)) + (
         CASE WHEN A.TAX_PERCENTAGE IS NULL OR A.TAX_PERCENTAGE = 0 THEN 0
            ELSE ROUND(((A.TAX_PERCENTAGE/100) * (COALESCE(A.CLOSING_STOCK,0) * COALESCE(A.VARIANCE_PRICE,0))),6)
            END
			)
	WHERE
		IMR.PRODUCT_ID = A.PRODUCT_ID
        AND IMR.UNIT_ID = A.UNIT_ID
        AND IMR.CLOSING_DAY_CLOSE_EVENT_ID = A.CURRENT_EVENT_ID;


 UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IM,
    (
    SELECT
    UNIT_ID,
    MAX(CLOSING_DAY_CLOSE_EVENT_ID) MAX_CLOSING_DAY_CLOSE_EVENT_ID
    FROM
    KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP
    GROUP BY UNIT_ID
    ) X
 SET
     IM.MAX_CLOSING_EVENT_ID = X.MAX_CLOSING_DAY_CLOSE_EVENT_ID
 WHERE
	X.UNIT_ID = IM.UNIT_ID;


UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP SET FOUND_MONTHLY = "Y" WHERE MAX_CLOSING_EVENT_ID = CLOSING_DAY_CLOSE_EVENT_ID;



 -- NOW SETTING THE OPENING EVENT AS MINIMUM EVENT FOUND FOR THAT UNIT AND CALCULATING FROM THERE AND SETTING OPENING AS 0
  UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IM,
    (
    SELECT
    UNIT_ID,
    min(OPENING_DAY_CLOSE_EVENT_ID) MIN_OPENING_EVENT,
    min(OPENING_GENERATION_TIME) MIN_OPENING_GENERATION_TIME
    FROM
    KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP
    GROUP BY UNIT_ID
    ) X
 SET
     IM.OPENING_DAY_CLOSE_EVENT_ID = X.MIN_OPENING_EVENT,
     IM.OPENING_STOCK_QUANTITY = 0,
     IM.OPENING_STOCK_TAX = 0,
     IM.OPENING_STOCK_VALUE_WITHOUT_TAX = 0,
     IM.OPENING_STOCK_VALUE_WITH_TAX = 0,
     IM.OPENING_GENERATION_TIME = X.MIN_OPENING_GENERATION_TIME,
     IM.MISSING_OPENING_EVENT = "Y"
 WHERE
	X.UNIT_ID = IM.UNIT_ID AND IM.OPENING_DAY_CLOSE_EVENT_ID IS NULL;

 -- NOW SETTING THE OPENING EVENT AS MINIMUM EVENT FOUND FOR THAT UNIT AND CALCULATING FROM THERE AND SETTING OPENING AS 0


    UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IM,
    (
    SELECT
    UNIT_ID,
    MAX(CLOSING_DAY_CLOSE_EVENT_ID) MAX_CLOSING_DAY_CLOSE_EVENT_ID,
    MAX(CLOSING_GENERATION_TIME) MAX_CLOSING_GENERATION_TIME
    FROM
    KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP
    GROUP BY UNIT_ID
    ) X
 SET
     IM.CLOSING_DAY_CLOSE_EVENT_ID = X.MAX_CLOSING_DAY_CLOSE_EVENT_ID,
     IM.CLOSING_GENERATION_TIME = X.MAX_CLOSING_GENERATION_TIME,
     IM.CLOSING_STOCK_QUANTITY = 0,
     IM.CLOSING_STOCK_TAX = 0,
     IM.CLOSING_STOCK_VALUE_WITHOUT_TAX = 0,
     IM.CLOSING_STOCK_VALUE_WITH_TAX = 0,
     IM.MISSING_CLOSING_EVENT = "Y"
 WHERE
	X.UNIT_ID = IM.UNIT_ID AND IM.CLOSING_DAY_CLOSE_EVENT_ID IS NULL;

-- GR DATA

UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IM,
    (select a.*
    from (
        select
            GR.GENERATED_FOR_UNIT_ID,
            GRI.SKU_ID,
            PD.PRODUCT_ID,
            AVG(GRI.UNIT_PRICE) GOOD_RECEIVED_PRICE,
            SUM(GRI.RECEIVED_QUANTITY) GOOD_RECEIVED_QUANTITY,
            SUM(GRI.RECEIVED_QUANTITY * GRI.UNIT_PRICE) GOOD_RECEIVED_VALUE_WITHOUT_TAX,
            SUM((GRI.RECEIVED_QUANTITY * GRI.UNIT_PRICE) + coalesce(GRI.TAX_AMOUNT, 0)) GOOD_RECEIVED_VALUE_WITH_TAX,
            SUM(GRI.TAX_AMOUNT) GOOD_RECEIVED_TAX
        from
            KETTLE_SCM_ARCHIVE.GOODS_RECEIVED_CAFE GR
        INNER JOIN
            KETTLE_SCM_ARCHIVE.GOODS_RECEIVED_ITEM_CAFE GRI ON GRI.GOODS_RECEIVED_ID = GR.GOODS_RECEIVED_ID AND GR.GOODS_RECEIVED_STATUS = "SETTLED"
            INNER JOIN KETTLE_SCM_DUMP.SKU_DEFINITION SD ON GRI.SKU_ID = SD.SKU_ID
            INNER JOIN KETTLE_SCM_DUMP.PRODUCT_DEFINITION PD ON SD.LINKED_PRODUCT_ID = PD.PRODUCT_ID
        INNER JOIN
            KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IM ON GR.GENERATED_FOR_UNIT_ID=IM.UNIT_ID and IM.PRODUCT_ID=PD.PRODUCT_ID
        where
			LAST_UPDATE_TIME > IM.OPENING_GENERATION_TIME AND LAST_UPDATE_TIME<= IM.CLOSING_GENERATION_TIME
        GROUP BY GR.GENERATED_FOR_UNIT_ID , PD.PRODUCT_ID) a) X
SET
    IM.GOOD_RECEIVED_QUANTITY = X.GOOD_RECEIVED_QUANTITY,
    IM.GOOD_RECEIVED_PRICE = X.GOOD_RECEIVED_PRICE,
    IM.GOOD_RECEIVED_TAX = X.GOOD_RECEIVED_TAX,
    IM.GOOD_RECEIVED_VALUE_WITHOUT_TAX = X.GOOD_RECEIVED_VALUE_WITHOUT_TAX,
    IM.GOOD_RECEIVED_VALUE_WITH_TAX = X.GOOD_RECEIVED_VALUE_WITH_TAX
WHERE
    X.PRODUCT_ID = IM.PRODUCT_ID
        AND X.GENERATED_FOR_UNIT_ID = IM.UNIT_ID;

-- TO DATA

UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IM,
    (SELECT
        a.*
    FROM
        (SELECT
        TR.GENERATION_UNIT_ID,
            TOI.SKU_ID,
            PD.PRODUCT_ID,
            AVG(TOI.UNIT_PRICE) TRANSFERRED_PRICE,
            SUM(TOI.TRANSFERRED_QUANTITY) TRANSFERRED_QUANTITY,
            SUM((TOI.TRANSFERRED_QUANTITY * TOI.UNIT_PRICE)) TRANSFERRED_VALUE_WITHOUT_TAX,
            SUM((TOI.TRANSFERRED_QUANTITY * TOI.UNIT_PRICE) + coalesce(TOI.TOTAL_TAX,0)) TRANSFERRED_VALUE_WITH_TAX,
            SUM(TOI.TOTAL_TAX) TRANSFERRED_TAX
    FROM
        KETTLE_SCM_ARCHIVE.TRANSFER_ORDER_CAFE TR
    INNER JOIN KETTLE_SCM_ARCHIVE.TRANSFER_ORDER_ITEM_CAFE TOI ON TOI.TRANSFER_ORDER_ID = TR.TRANSFER_ORDER_ID AND TR.TRANSFER_ORDER_STATUS IN ('CREATED','SETTLED','TRANSFERRED')
    INNER JOIN KETTLE_SCM_DUMP.SKU_DEFINITION SD ON TOI.SKU_ID = SD.SKU_ID
            INNER JOIN KETTLE_SCM_DUMP.PRODUCT_DEFINITION PD ON SD.LINKED_PRODUCT_ID = PD.PRODUCT_ID
    INNER JOIN KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IM ON TR.GENERATION_UNIT_ID = IM.UNIT_ID
        AND IM.PRODUCT_ID = PD.PRODUCT_ID AND TR.GENERATED_FOR_UNIT_ID <> TR.GENERATION_UNIT_ID
    WHERE
        TR.GENERATION_TIME >IM.OPENING_GENERATION_TIME AND TR.GENERATION_TIME<= IM.CLOSING_GENERATION_TIME
    GROUP BY TR.GENERATION_UNIT_ID , PD.PRODUCT_ID) a) X
SET
    IM.TRANSFERRED_QUANTITY = X.TRANSFERRED_QUANTITY,
    IM.TRANSFERRED_PRICE = X.TRANSFERRED_PRICE,
    IM.TRANSFERRED_TAX = X.TRANSFERRED_TAX,
    IM.TRANSFERRED_VALUE_WITHOUT_TAX = X.TRANSFERRED_VALUE_WITHOUT_TAX,
    IM.TRANSFERRED_VALUE_WITH_TAX = X.TRANSFERRED_VALUE_WITH_TAX
WHERE
X.PRODUCT_ID = IM.PRODUCT_ID
AND X.GENERATION_UNIT_ID = IM.UNIT_ID;

-- VENDOR GR


UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IM,
    (select a.*
    from (
		SELECT
			VGR.DELIVERY_UNIT_ID,
            VGRI.SKU_ID,
            PD.PRODUCT_ID,
            AVG(VGRI.UNIT_PRICE) VENDOR_GOOD_RECEIVED_PRICE,
            SUM(VGRI.RECEIVED_QUANTITY) VENDOR_GOOD_RECEIVED_QUANTITY,
            SUM(VGRI.TOTAL_TAX) VENDOR_GOOD_RECEIVED_TAX,
            SUM(VGRI.TOTAL_PRICE) VENDOR_GOOD_RECEIVED_VALUE_WITHOUT_TAX,
            SUM(VGRI.TOTAL_AMOUNT) VENDOR_GOOD_RECEIVED_VALUE_WITH_TAX
		FROM KETTLE_SCM_ARCHIVE.VENDOR_GOODS_RECEIVED_DATA_CAFE VGR
		INNER JOIN KETTLE_SCM_ARCHIVE.VENDOR_GR_ITEM_DETAIL_CAFE  VGRI ON VGR.GOODS_RECEIVED_ID = VGRI.VENDOR_GR_ID AND GOODS_RECEIVED_STATUS IN ("CREATED", "INITIATED")
        INNER JOIN KETTLE_SCM_DUMP.SKU_DEFINITION SD ON VGRI.SKU_ID = SD.SKU_ID
            INNER JOIN KETTLE_SCM_DUMP.PRODUCT_DEFINITION PD ON SD.LINKED_PRODUCT_ID = PD.PRODUCT_ID
        INNER JOIN
            KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IM ON VGR.DELIVERY_UNIT_ID=IM.UNIT_ID and IM.PRODUCT_ID=PD.PRODUCT_ID
        where
        VGR.CREATED_AT >IM.OPENING_GENERATION_TIME AND VGR.CREATED_AT<= IM.CLOSING_GENERATION_TIME
        GROUP BY VGR.DELIVERY_UNIT_ID , PD.PRODUCT_ID
        ) a) X
SET
    IM.VENDOR_GOOD_RECEIVED_QUANTITY = X.VENDOR_GOOD_RECEIVED_QUANTITY,
    IM.VENDOR_GOOD_RECEIVED_PRICE = X.VENDOR_GOOD_RECEIVED_PRICE,
    IM.VENDOR_GOOD_RECEIVED_VALUE_WITHOUT_TAX = X.VENDOR_GOOD_RECEIVED_VALUE_WITHOUT_TAX,
    IM.VENDOR_GOOD_RECEIVED_VALUE_WITH_TAX = X.VENDOR_GOOD_RECEIVED_VALUE_WITH_TAX,
    IM.VENDOR_GOOD_RECEIVED_TAX = X.VENDOR_GOOD_RECEIVED_TAX
WHERE
    X.SKU_ID = IM.SKU_ID
        AND X.DELIVERY_UNIT_ID = IM.UNIT_ID;


-- wastage

UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IM,
    (SELECT
		WID.PRODUCT_ID,
        WE.UNIT_ID,
        AVG(WID.PRICE) WASTAGE_PRICE,
		SUM(WID.QUANTITY) WASTAGE_QUANTITY,
        SUM(WID.QUANTITY*PRICE)  WASTAGE_VALUE_WITHOUT_TAX,
        SUM(WID.QUANTITY*PRICE + coalesce(WID.TAX,0))  WASTAGE_VALUE_WITH_TAX,
        SUM(WID.TAX)  WASTAGE_TAX
    FROM
		KETTLE_SCM_ARCHIVE.WASTAGE_EVENT_CAFE WE
	INNER JOIN
		KETTLE_SCM_ARCHIVE.WASTAGE_ITEM_DATA_CAFE WID ON WE.WASTAGE_ID=WID.WASTAGE_ID AND WE.STATUS = "SETTLED"
	INNER JOIN
		KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IM ON IM.UNIT_ID=WE.UNIT_ID and IM.PRODUCT_ID=WID.PRODUCT_ID
	where
        WE.GENERATION_TIME >IM.OPENING_GENERATION_TIME AND WE.GENERATION_TIME  <= IM.CLOSING_GENERATION_TIME
	GROUP BY WE.UNIT_ID, WID.PRODUCT_ID) X
SET
    IM.WASTAGE_QUANTITY = X.WASTAGE_QUANTITY,
    IM.WASTAGE_PRICE = X.WASTAGE_PRICE,
    IM.WASTAGE_VALUE_WITHOUT_TAX = X.WASTAGE_VALUE_WITHOUT_TAX,
    IM.WASTAGE_VALUE_WITH_TAX = X.WASTAGE_VALUE_WITH_TAX,
    IM.WASTAGE_TAX = X.WASTAGE_TAX
WHERE
    X.PRODUCT_ID = IM.PRODUCT_ID
        AND X.UNIT_ID = IM.UNIT_ID;

-- consumption

DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.CONSUMPTION_TEMP_DATA ;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.CONSUMPTION_TEMP_DATA
SELECT * FROM KETTLE_SCM_ARCHIVE.DAY_CLOSE_PRODUCT_VALUES_CAFE WHERE 1=0;

INSERT INTO KETTLE_SCM_ARCHIVE.CONSUMPTION_TEMP_DATA
SELECT DISTINCT DCPV.* FROM
			KETTLE_SCM_ARCHIVE.DAY_CLOSE_PRODUCT_VALUES_CAFE DCPV
        INNER JOIN
    KETTLE_SCM_ARCHIVE.DAY_CLOSE_EVENT_CAFE DCE ON DCPV.EVENT_ID = DCE.EVENT_ID AND DCE.CLOSURE_EVENT_TYPE = "STOCK_TAKE"
		AND DCE.STATUS = "CLOSED" AND DCPV.STOCK_TYPE = 'DAILY'
     GROUP BY DCPV.PRODUCT_ID,DCPV.EVENT_ID;


DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.STOCK_TEMP_DATA ;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.STOCK_TEMP_DATA
SELECT * FROM KETTLE_SCM_ARCHIVE.STOCK_INVENTORY_CAFE WHERE 1=0;


INSERT INTO KETTLE_SCM_ARCHIVE.STOCK_TEMP_DATA
(
    SELECT DISTINCT SI.* FROM
		KETTLE_SCM_ARCHIVE.STOCK_INVENTORY_CAFE SI
    inner join KETTLE_SCM_ARCHIVE.DAY_CLOSE_EVENT_CAFE dce on SI.CURRENT_EVENT_ID = dce.EVENT_ID AND SI.STOCK_TYPE = "MONTHLY" AND dce.STATUS = 'CLOSED'
    inner join KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IMR ON IMR.UNIT_ID=dce.UNIT_ID and IMR.PRODUCT_ID=SI.PRODUCT_ID
    GROUP BY SI.CURRENT_EVENT_ID,SI.PRODUCT_ID
    );

 UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IM,
    (SELECT
    DCPV.CALCULATED_PRODUCT_ID,
    DCPV.PRODUCT_ID,
    DCPV.UNIT_ID,
    AVG(DCPV.CONSUMPTION_PRICE) CONSUMPTION_PRICE,
    MAX(DCPV.TAX_PERCENTAGE) CONSUMPTION_TAX_PERCENTAGE,
    SUM(DCPV.CONSUMPTION) CONSUMPTION_QUANTITY,
         ROUND(((MAX(DCPV.TAX_PERCENTAGE)/100) * COALESCE(SUM(DCPV.CONSUMPTION),0) * COALESCE(AVG(DCPV.CONSUMPTION_PRICE),0)),6)
            CONSUMPTION_TAX,
    SUM(DCPV.CONSUMPTION_PRICE * DCPV.CONSUMPTION) CONSUMPTION_VALUE_WITHOUT_TAX,
    (SUM(DCPV.CONSUMPTION_PRICE * DCPV.CONSUMPTION) + ROUND(((MAX(DCPV.TAX_PERCENTAGE)/100) * COALESCE(SUM(DCPV.CONSUMPTION),0) * COALESCE(AVG(DCPV.CONSUMPTION_PRICE),0)),6)) CONSUMPTION_VALUE_WITH_TAX
FROM
		KETTLE_SCM_ARCHIVE.CONSUMPTION_TEMP_DATA DCPV
        INNER JOIN
    KETTLE_SCM_ARCHIVE.DAY_CLOSE_EVENT_CAFE DCE ON DCPV.EVENT_ID = DCE.EVENT_ID AND DCE.CLOSURE_EVENT_TYPE = "STOCK_TAKE" AND DCE.STATUS = "CLOSED"
        INNER JOIN
    KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IM ON DCPV.UNIT_ID = IM.UNIT_ID
        AND DCPV.PRODUCT_ID = IM.PRODUCT_ID
        AND DCPV.STOCK_TYPE = 'DAILY'
WHERE
    DCE.GENERATION_TIME > IM.OPENING_GENERATION_TIME
        AND DCE.GENERATION_TIME <= IM.CLOSING_GENERATION_TIME
	GROUP BY DCPV.UNIT_ID, DCPV.PRODUCT_ID
    ) X
 SET
     IM.CONSUMPTION_QUANTITY = X.CONSUMPTION_QUANTITY,
     IM.CONSUMPTION_PRICE = X.CONSUMPTION_PRICE,
     IM.CONSUMPTION_TAX = X.CONSUMPTION_TAX,
     IM.CONSUMPTION_VALUE_WITHOUT_TAX = X.CONSUMPTION_VALUE_WITHOUT_TAX,
     IM.CONSUMPTION_VALUE_WITH_TAX = X.CONSUMPTION_VALUE_WITH_TAX
 WHERE
     X.PRODUCT_ID = IM.PRODUCT_ID
         AND X.UNIT_ID = IM.UNIT_ID;


 -- FOR VARIANCE USING MONTHLY AND DAILY


 UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IM,
    (
    SELECT
    SI.PRODUCT_ID,
    dce.UNIT_ID,
    AVG(SI.VARIANCE_PRICE) VARIANCE_PRICE,
    SUM(SI.VARIANCE) VARIANCE_QUANTITY,
    MAX(SI.TAX_PERCENTAGE) MAX_TAX_PERCENTAGE,
    ROUND(((MAX(SI.TAX_PERCENTAGE)/100) * COALESCE(SUM(SI.VARIANCE),0) * COALESCE(SUM(SI.VARIANCE_PRICE),0)),6)
            VARIANCE_TAX,
	SUM(SI.VARIANCE) * AVG(SI.VARIANCE_PRICE) VARIANCE_VALUE_WITHOUT_TAX,
	(SUM(SI.VARIANCE) * AVG(SI.VARIANCE_PRICE)) + (ROUND(((MAX(SI.TAX_PERCENTAGE)/100) * COALESCE(SUM(SI.VARIANCE),0) * COALESCE(AVG(SI.VARIANCE_PRICE),0)),6))  VARIANCE_VALUE_WITH_TAX
FROM
    KETTLE_SCM_ARCHIVE.STOCK_TEMP_DATA SI
    inner join KETTLE_SCM_ARCHIVE.DAY_CLOSE_EVENT_CAFE dce on SI.CURRENT_EVENT_ID = dce.EVENT_ID AND SI.STOCK_TYPE = "MONTHLY" AND dce.STATUS = 'CLOSED'
    inner join KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IMR ON IMR.UNIT_ID=dce.UNIT_ID and IMR.PRODUCT_ID=SI.PRODUCT_ID AND IMR.FOUND_MONTHLY = "Y"
WHERE
        dce.GENERATION_TIME  > IMR.OPENING_GENERATION_TIME AND dce.GENERATION_TIME <= IMR.CLOSING_GENERATION_TIME
	group by dce.UNIT_ID,SI.PRODUCT_ID
    ) X
 SET
     IM.VARIANCE_QUANTITY = X.VARIANCE_QUANTITY,
     IM.VARIANCE_PRICE= X.VARIANCE_PRICE,
     IM.VARIANCE_TAX= X.VARIANCE_TAX,
     IM.VARIANCE_VALUE_WITHOUT_TAX= X.VARIANCE_VALUE_WITHOUT_TAX,
     IM.VARIANCE_VALUE_WITH_TAX= X.VARIANCE_VALUE_WITH_TAX
 WHERE
     X.PRODUCT_ID = IM.PRODUCT_ID
         AND X.UNIT_ID = IM.UNIT_ID;



DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.STOCK_TEMP_DATA ;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.STOCK_TEMP_DATA
SELECT * FROM KETTLE_SCM_ARCHIVE.STOCK_INVENTORY_CAFE WHERE 1=0;


INSERT INTO KETTLE_SCM_ARCHIVE.STOCK_TEMP_DATA
(
    SELECT DISTINCT SI.* FROM
		KETTLE_SCM_ARCHIVE.STOCK_INVENTORY_CAFE SI
    inner join KETTLE_SCM_ARCHIVE.DAY_CLOSE_EVENT_CAFE dce on SI.CURRENT_EVENT_ID = dce.EVENT_ID AND SI.STOCK_TYPE = "DAILY" AND dce.STATUS = 'CLOSED'
    inner join KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IMR ON IMR.UNIT_ID=dce.UNIT_ID and IMR.PRODUCT_ID=SI.PRODUCT_ID
    GROUP BY SI.CURRENT_EVENT_ID,SI.PRODUCT_ID
    );

 UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IM,
    (
    SELECT
    SI.PRODUCT_ID,
    dce.UNIT_ID,
    AVG(SI.VARIANCE_PRICE) VARIANCE_PRICE,
    SUM(SI.VARIANCE) VARIANCE_QUANTITY,
    ROUND(((MAX(SI.TAX_PERCENTAGE)/100) * COALESCE(SUM(SI.VARIANCE),0) * COALESCE(SUM(SI.VARIANCE_PRICE),0)),6)
            VARIANCE_TAX,
	SUM(SI.VARIANCE) * AVG(SI.VARIANCE_PRICE) VARIANCE_VALUE_WITHOUT_TAX,
	(SUM(SI.VARIANCE) * AVG(SI.VARIANCE_PRICE)) + (ROUND(((MAX(SI.TAX_PERCENTAGE)/100) * COALESCE(SUM(SI.VARIANCE),0) * COALESCE(AVG(SI.VARIANCE_PRICE),0) ),6))  VARIANCE_VALUE_WITH_TAX
FROM
    KETTLE_SCM_ARCHIVE.STOCK_TEMP_DATA SI
    inner join KETTLE_SCM_ARCHIVE.DAY_CLOSE_EVENT_CAFE dce on SI.CURRENT_EVENT_ID = dce.EVENT_ID AND SI.STOCK_TYPE = "DAILY" AND dce.STATUS = 'CLOSED'
    inner join KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IMR ON IMR.UNIT_ID=dce.UNIT_ID and IMR.PRODUCT_ID=SI.PRODUCT_ID AND IMR.FOUND_MONTHLY <> "Y"
WHERE
        dce.GENERATION_TIME  > IMR.OPENING_GENERATION_TIME AND dce.GENERATION_TIME <= IMR.CLOSING_GENERATION_TIME
	group by dce.UNIT_ID,SI.PRODUCT_ID
    ) X
 SET
     IM.VARIANCE_QUANTITY = X.VARIANCE_QUANTITY,
     IM.VARIANCE_PRICE= X.VARIANCE_PRICE,
     IM.VARIANCE_TAX= X.VARIANCE_TAX,
     IM.VARIANCE_VALUE_WITHOUT_TAX= X.VARIANCE_VALUE_WITHOUT_TAX,
     IM.VARIANCE_VALUE_WITH_TAX= X.VARIANCE_VALUE_WITH_TAX
 WHERE
     X.PRODUCT_ID = IM.PRODUCT_ID
         AND X.UNIT_ID = IM.UNIT_ID;





 -- SETTING THE EXPECTED CLOSING STOCK
 UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP IM,
    (
    SELECT
    PRODUCT_ID,
    UNIT_ID,
    (coalesce(OPENING_STOCK_QUANTITY, 0) + coalesce(GOOD_RECEIVED_QUANTITY, 0) + coalesce(VENDOR_GOOD_RECEIVED_QUANTITY, 0) - coalesce(TRANSFERRED_QUANTITY, 0) - coalesce(WASTAGE_QUANTITY, 0) - coalesce(CONSUMPTION_QUANTITY, 0)) AS EXPECTED_CLOSING_STOCK,
     coalesce(OPENING_STOCK_QUANTITY, 0) + coalesce(GOOD_RECEIVED_QUANTITY, 0) + coalesce(VENDOR_GOOD_RECEIVED_QUANTITY, 0) - coalesce(TRANSFERRED_QUANTITY, 0) - coalesce(WASTAGE_QUANTITY, 0) - coalesce(CONSUMPTION_QUANTITY, 0) - coalesce(VARIANCE_QUANTITY, 0) EXPECTED_CLOSING_STOCK_AFTER_VARIANCE
FROM
    KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP
    ) X
 SET
     IM.EXPECTED_CLOSING_STOCK = X.EXPECTED_CLOSING_STOCK,
     IM.EXPECTED_CLOSING_STOCK_AFTER_VARIANCE = X.EXPECTED_CLOSING_STOCK_AFTER_VARIANCE
 WHERE
     X.PRODUCT_ID = IM.PRODUCT_ID
         AND X.UNIT_ID = IM.UNIT_ID;

INSERT INTO KETTLE_SCM_ARCHIVE.CAFE_INVENTORY_MOVEMENT_REPORT_DUMP
SELECT * FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_REPORT_DUMP;


    END LOOP;

    CLOSE cursor_units;
END $$

DELIMITER $$;

call KETTLE_SCM_ARCHIVE.CAFE_INVENTORY_MOVEMENT_PROCEDURE();
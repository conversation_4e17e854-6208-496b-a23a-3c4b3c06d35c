-- queries related to stock out percentage calculations

CREATE TABLE KETTLE_STOCK_OUT_PERCENTAGE_DATA (
    ID INT AUTO_INCREMENT PRIMARY KEY,
    UNIT_ID INT NOT NULL,
    BUSINESS_DATE DATE NOT NULL,
    BRAND_ID INT,
    INVENTORY_TRACK_LEVEL VARCHAR(255),
    PRODUCT_COUNT INT,
    CAFE_OPENING TIME,
    CAFE_CLOSING TIME,
    CAFE_OPERATIONAL VARCHAR(10),
    PRODUCT_OPERATIONAL_TIME_IN_MIN INT,
    TOTAL_OPERATION_TIME_IN_MIN INT,
    TOTAL_DOWN_TIME_IN_MIN INT,
    STOCK_OUT_PERCENTAGE DECIMAL(19, 4)
);

ALTER TABLE KETTLE_STOCK_OUT_DATE_WISE_DATA
ADD COLUMN BRAND_ID INT,
ADD COLUMN INVENTORY_TRACK_LEVEL VARCHAR(255),
ADD COLUMN TOTAL_STOCK_OUT_TIME BIGINT;

ALTER TABLE SCHEDULER_STATUS_DATA
ADD COLUMN SCHEDULER_TYPE VARCHAR(255),
ADD COLUMN MESSAGE TEXT;

CREATE TABLE KETTLE_PRODUCT_DATA_CLONE (
    ID INT NOT NULL AUTO_INCREMENT,
    PRODUCT_ID INT NOT NULL,
    BRAND_ID INT,
    INVENTORY_TRACK_LEVEL VARCHAR(255),
    PRIMARY KEY (ID)
);

CREATE TABLE KETTLE_UNIT_DETAIL_DATA_CLONE (
    ID INT AUTO_INCREMENT PRIMARY KEY,
    UNIT_ID INT NOT NULL,
    CAFE_OPENING TIME,
    CAFE_CLOSING TIME,
    CAFE_OPERATIONAL VARCHAR(10),
    BRAND_ID INT,
    INVENTORY_LEVEL VARCHAR(255),
    TOTAL_PRODUCTS INT
);
-- monk dayClosefunction
CREATE TABLE KETTLE_SCM_STAGE.MONK_DAY_CLOSE_EVENT_STATUS (
    MONK_DAY_CLOSE_EVENT_STATUS_ID BIGINT AUTO_INCREMENT PRIMARY KEY,
    UNIT_ID INT NOT NULL,
    BUSINESS_DATE DATE NOT NULL,
    EVENT_TYPE VARCHAR(50) NOT NULL,
    EVENT_STATUS VARCHAR(50) NOT NULL,
    KETTLE_DAY_CLOSE INT,
    SUMO_DAY_CLOSE INT,
    UPDATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT fk_kettle_day_close FOREIGN KEY (KETTLE_DAY_CLOSE)
        REFERENCES KETTLE_SCM_STAGE.DAY_CLOSE_EVENT(EVENT_ID),
    CONSTRAINT fk_sumo_day_close FOREIGN KEY (SUMO_DAY_CLOSE)
        REFERENCES KETTLE_SCM_STAGE.DAY_CLOSE_EVENT(EVENT_ID)
);
CREATE TABLE KETTLE_SCM_STAGE.MONK_STATUS_DAY_CLOSE (
    MONK_STATUS_DAY_CLOSE_ID BIGINT AUTO_INCREMENT PRIMARY KEY,
    EVENT_STATUS_ID BIGINT NOT NULL,
    MONK_NAME VARCHAR(100) NOT NULL,
    MONK_STATUS VARCHAR(50) NOT NULL,
    UPDATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT fk_event_status FOREIGN KEY (EVENT_STATUS_ID)
        REFERENCES MONK_DAY_CLOSE_EVENT_STATUS(MONK_DAY_CLOSE_EVENT_STATUS_ID)
);
CREATE TABLE IF NOT EXISTS KETTLE_SCM_STAGE.MONK_STATUS_DAY_CLOSE_HISTORY (
    `MONK_STATUS_DAY_CLOSE_HISTORY_ID` BIGINT NOT NULL AUTO_INCREMENT,
    `CREATED_AT` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `MONK_STATUS_DAY_CLOSE_ID` BIGINT NOT NULL,
    `MONK_STATUS` VARCHAR(50) NOT NULL,
    `COMMENT` VARCHAR(500),
    PRIMARY KEY (`MONK_STATUS_DAY_CLOSE_HISTORY_ID`),
    INDEX `idx_monk_status_day_close_id` (`MONK_STATUS_DAY_CLOSE_ID`),
    INDEX `idx_created_at` (`CREATED_AT`),
    CONSTRAINT `fk_monk_status_history_monk_status`
        FOREIGN KEY (`MONK_STATUS_DAY_CLOSE_ID`)
        REFERENCES `MONK_STATUS_DAY_CLOSE` (`MONK_STATUS_DAY_CLOSE_ID`)
        ON DELETE CASCADE ON UPDATE CASCADE
);

ALTER TABLE KETTLE_SCM.GATEPASS_DATA
ADD COLUMN APPROVAL_REQUESTED_TO INT NULL DEFAULT NULL AFTER IS_ASSET_GATE_PASS;

INSERT INTO `KETTLE_MASTER`.`ROLE_BRAND_MAPPING` (`ROLE_ID`, `BRAND_ID`, `STATUS`) VALUES ('246', '1', 'ACTIVE');
INSERT INTO `KETTLE_MASTER`.`ROLE_BRAND_MAPPING` (`ROLE_ID`, `BRAND_ID`, `STATUS`) VALUES ('246', '3', 'ACTIVE');
INSERT INTO `KETTLE_MASTER`.`ROLE_BRAND_MAPPING` (`ROLE_ID`, `BRAND_ID`, `STATUS`) VALUES ('246', '6', 'ACTIVE');

INSERT INTO KETTLE_MASTER.USER_ROLE_DATA (ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS, APPLICATION_ID)
VALUES ('GatePass Approver', 'Approve Gatepass', 'ACTIVE', '7');
SELECT * FROM KETTLE_MASTER.USER_ROLE_DATA ORDER BY 1 DESC;
INSERT INTO KETTLE_MASTER.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('GP_APP', '7', 'SUBMENU', 'SHOW', 'SUMO -> Gatepass Management ->  Approve GatePass', 'ACTIVE');

INSERT INTO KETTLE_MASTER.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
	VALUES((SELECT ROLE_ID FROM KETTLE_MASTER.USER_ROLE_DATA WHERE ROLE_NAME = 'GatePass Approver'),
    (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER.ACTION_DETAIL WHERE ACTION_CODE = 'GP_APP'),
    'ACTIVE', '140199', '2024-12-12 12:00:00');


ALTER TABLE `KETTLE_SCM_STAGE`.`GATEPASS_STATUS_DETAIL`
CHANGE COLUMN `REASON_TEXT` `REASON_TEXT` VARCHAR(150) NULL DEFAULT NULL ;

ALTER TABLE `KETTLE_SCM_STAGE`.`GATEPASS_STATUS_DETAIL`
CHANGE COLUMN `FROM_STATUS` `FROM_STATUS` VARCHAR(25) NOT NULL ,
CHANGE COLUMN `TO_STATUS` `TO_STATUS` VARCHAR(25) NOT NULL ,
CHANGE COLUMN `TRANSITION_STATUS` `TRANSITION_STATUS` VARCHAR(25) NOT NULL ;

ALTER TABLE `KETTLE_SCM_STAGE`.`GATEPASS_DATA`
CHANGE COLUMN `STATUS` `STATUS` VARCHAR(25) NOT NULL ,
CHANGE COLUMN `RETURN_STATUS` `RETURN_STATUS` VARCHAR(25) NULL DEFAULT NULL ;

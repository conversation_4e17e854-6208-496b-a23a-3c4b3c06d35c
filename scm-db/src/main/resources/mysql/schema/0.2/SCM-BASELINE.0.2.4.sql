UPDATE KETTLE_SCM_DEV.DAY_CLOSE_EVENT set CLOSURE_EVENT_FREQUENCY = "ALL"
where CLOSURE_EVENT_TYPE = "OPENING" and STATUS = "CLOSED";

ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER ADD COLUMN CHILD_REQUEST_ORDER_ID INT NULL;

UPDATE KETTLE_SCM_DEV.PRODUCT_DEFINITION set STOCK_KEEPING_FREQUENCY= "FIXED_ASSETS" where CATEGORY_ID=3;

UPDATE KETTLE_SCM_DEV.PRODUCT_DEFINITION set STOCK_KEEPING_FREQUENCY= "MONTHLY" where STOCK_KEEPING_FREQUENCY = "WEEKLY";
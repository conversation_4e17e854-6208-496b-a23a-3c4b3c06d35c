DROP TABLE IF EXISTS KETTLE_SCM_DEV.PRICE_UPDATE_ENTRY;
CREATE TABLE KETTLE_SCM_DEV.PRICE_UPDATE_ENTRY (
    ENTRY_ID INTEGER NOT NULL AUTO_INCREMENT,
    EVENT_ID INTEGER NOT NULL,
    KEY_TYPE VARCHAR(20) DEFAULT NULL,
    KEY_ID INTEGER NOT NULL,
    KEY_NAME VARCHAR(250) NOT NULL,
    UOM VARCHAR(100) NOT NULL,
    UNIT_PRICE DECIMAL(10 , 2 ) NULL,
    UPDATED_UNIT_PRICE DECIMAL(20,6) NULL,
    EDITED_UNIT_PRICE DECIMAL(20,6) NULL,
    APPROVED_UNIT_PRICE DECIMAL(20,6) NULL,
    ENTRY_STATUS VARCHAR(10) NULL,
    HAS_ERROR VARCHAR(1) NULL,
    PRIMARY KEY (ENTRY_ID)
);

CREATE INDEX EVENT_ID_PRICE_UPDATE_ENTRY  ON KETTLE_SCM_DEV.PRICE_UPDATE_ENTRY(EVENT_ID) USING BTREE;
CREATE INDEX KEY_TYPE_PRICE_UPDATE_ENTRY  ON KETTLE_SCM_DEV.PRICE_UPDATE_ENTRY(KEY_TYPE) USING BTREE;


DROP TABLE IF EXISTS KETTLE_SCM_DEV.PRICE_UPDATE_EVENT;
CREATE TABLE KETTLE_SCM_DEV.PRICE_UPDATE_EVENT (
    EVENT_ID INT NOT NULL AUTO_INCREMENT,
    CREATED_BY INTEGER NULL,
    CREATED_BY_NAME VARCHAR(100) NULL,
    CREATION_TIME TIMESTAMP NOT NULL,
    FINALIZATION_TIME TIMESTAMP,
    FINALIZED_BY INTEGER NULL,
    FINALIZED_BY_NAME VARCHAR(100) NULL,
    EVENT_TYPE VARCHAR(20),
    EVENT_ACTION_TYPE VARCHAR(20),
    EVENT_STATUS VARCHAR(10),
    NO_OF_RECORDS INTEGER NULL,
    NO_OF_ERRORS INTEGER NULL,
    DATA_FILE_PATH VARCHAR(250) NULL,
    PRIMARY KEY (EVENT_ID)
);

CREATE INDEX EVENT_STATUS_PRICE_UPDATE_EVENT  ON KETTLE_SCM_DEV.PRICE_UPDATE_EVENT(EVENT_STATUS) USING BTREE;
CREATE INDEX EVENT_TYPE_PRICE_UPDATE_EVENT  ON KETTLE_SCM_DEV.PRICE_UPDATE_EVENT(EVENT_TYPE) USING BTREE;


DROP TABLE IF EXISTS KETTLE_SCM_DEV.PRICE_UPDATE_DRILLDOWN;
CREATE TABLE KETTLE_SCM_DEV.PRICE_UPDATE_DRILLDOWN (
    DRILLDOWN_ID INTEGER NOT NULL AUTO_INCREMENT,
    ENTRY_ID INTEGER NOT NULL,
    EVENT_ID INTEGER NOT NULL,
    DRILLDOWN_CATEGORY VARCHAR(30) NOT NULL,
    DRILLDOWN_TYPE VARCHAR(30) NOT NULL,
    KEY_ID INTEGER NOT NULL,
    KEY_NAME VARCHAR(250) NOT NULL,
    KEY_TYPE VARCHAR(100) DEFAULT NULL,
    UOM VARCHAR(100) NOT NULL,
    UNIT_PRICE DECIMAL(20,6) NULL,
    QUANTITY DECIMAL(20,6) NULL,
    COST DECIMAL(20,6) NULL,
    HAS_ERROR VARCHAR(1) NULL,
    PRIMARY KEY (DRILLDOWN_ID)
);

CREATE INDEX EVENT_ID_PRICE_UPDATE_DRILLDOWN  ON KETTLE_SCM_DEV.PRICE_UPDATE_DRILLDOWN(EVENT_ID) USING BTREE;

DROP TABLE IF EXISTS KETTLE_SCM_DEV.PRICE_UPDATE_ERROR;
CREATE TABLE KETTLE_SCM_DEV.PRICE_UPDATE_ERROR (
    ERROR_ID INTEGER NOT NULL AUTO_INCREMENT,
    ENTRY_TYPE VARCHAR(10) NOT NULL,
    ENTRY_ID INTEGER NOT NULL,
    EVENT_ID INTEGER NOT NULL,
    ERROR_MESSAGE VARCHAR(1000) NOT NULL,
    PRIMARY KEY (ERROR_ID)
);

CREATE INDEX EVENT_ID_PRICE_UPDATE_ERROR  ON KETTLE_SCM_DEV.PRICE_UPDATE_ERROR(EVENT_ID) USING BTREE;

DROP PROCEDURE IF EXISTS KETTLE_SCM_DEV.PRODUCT_PRICE_CHANGE_CALCULATE;
DELIMITER $$
CREATE PROCEDURE KETTLE_SCM_DEV.PRODUCT_PRICE_CHANGE_CALCULATE(
IN EVENT_ID INTEGER)
proc_label : BEGIN

DELETE FROM KETTLE_SCM_DEV.PRICE_UPDATE_ENTRY where EVENT_ID = EVENT_ID and KEY_TYPE <> 'SKU';
DELETE FROM KETTLE_SCM_DEV.PRICE_UPDATE_ERROR 
WHERE
    EVENT_ID = EVENT_ID;

INSERT INTO KETTLE_SCM_DEV.PRICE_UPDATE_ENTRY(EVENT_ID, KEY_TYPE, KEY_ID, KEY_NAME, UOM, UNIT_PRICE, UPDATED_UNIT_PRICE, EDITED_UNIT_PRICE, ENTRY_STATUS)
SELECT 
    EVENT_ID, 
    'PRODUCT',
    a.PRODUCT_ID,
    a.PRODUCT_NAME,
    a.UNIT_OF_MEASURE,
    a.UNIT_PRICE,
    coalesce(SUM(a.TOTAL_COST) / SUM(a.RECEIVED_QUANTITY),0) CALCULATED_UNIT_PRICE,
    coalesce(SUM(a.TOTAL_COST) / SUM(a.RECEIVED_QUANTITY),0) CALCULATED_UNIT_PRICE,
    'INITIATED'
FROM
    (SELECT 
        pd.PRODUCT_ID PRODUCT_ID,
            pd.PRODUCT_NAME,
            pd.UNIT_OF_MEASURE UNIT_OF_MEASURE,
            pd.UNIT_PRICE,
            sku.SKU_ID,
            sku.EDITED_UNIT_PRICE * qty.RECEIVED_QUANTITY TOTAL_COST,
            qty.RECEIVED_QUANTITY
    FROM
        KETTLE_SCM_DEV.PRODUCT_DEFINITION pd, (SELECT 
        sku.SKU_ID,
            sku.UNIT_OF_MEASURE UNIT_OF_MEASURE,
            sku.LINKED_PRODUCT_ID LINKED_PRODUCT_ID,
            CASE
                WHEN price.SKU_ID IS NULL THEN sku.NEGOTIATED_UNIT_PRICE
                ELSE price.EDITED_UNIT_PRICE
            END EDITED_UNIT_PRICE
    FROM
        KETTLE_SCM_DEV.SKU_DEFINITION sku
    LEFT OUTER JOIN (SELECT 
        pud.KEY_ID SKU_ID,
            pud.EDITED_UNIT_PRICE EDITED_UNIT_PRICE
    FROM
        KETTLE_SCM_DEV.PRICE_UPDATE_EVENT pue, KETTLE_SCM_DEV.PRICE_UPDATE_ENTRY pud
    WHERE
        pue.EVENT_ID = pud.EVENT_ID
            AND pud.KEY_TYPE = 'SKU'
            AND pue.EVENT_ID = EVENT_ID
            AND pue.EVENT_STATUS = 'INITIATED') price ON sku.SKU_ID = price.SKU_ID) sku, (SELECT 
        gri.SKU_ID SKU_ID,
            gri.UNIT_OF_MEASURE UNIT_OF_MEASURE,
            SUM(RECEIVED_QUANTITY) RECEIVED_QUANTITY
    FROM
        KETTLE_SCM_DEV.GOODS_RECEIVED gr, KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM gri, KETTLE_SCM_DEV.UNIT_DETAIL tu, KETTLE_SCM_DEV.UNIT_CATEGORY tuc, KETTLE_SCM_DEV.UNIT_DETAIL ru, KETTLE_SCM_DEV.UNIT_CATEGORY ruc
    WHERE
        gr.GENERATION_UNIT_ID = tu.UNIT_ID
            AND tu.CATEGORY_ID = tuc.CATEGORY_ID
            AND gr.GENERATED_FOR_UNIT_ID = ru.UNIT_ID
            AND ru.CATEGORY_ID = ruc.CATEGORY_ID
            AND gr.GOODS_RECEIVED_ID = gri.GOODS_RECEIVED_ID
            AND gr.GOODS_RECEIVED_STATUS = 'SETTLED'
            AND ((gr.GENERATED_FOR_UNIT_ID = gr.GENERATION_UNIT_ID)
            OR (tuc.CATEGORY_CODE IN ('KITCHEN' , 'WAREHOUSE')
            AND ruc.CATEGORY_CODE NOT IN ('KITCHEN' , 'WAREHOUSE')))
            AND gr.GENERATION_TIME > '2016-01-01 00:00:00'
            AND gri.SKU_ID IN (SELECT 
                SKU_ID
            FROM
                KETTLE_SCM_DEV.SKU_DEFINITION
            WHERE
                LINKED_PRODUCT_ID IN (SELECT 
                        LINKED_PRODUCT_ID
                    FROM
                        KETTLE_SCM_DEV.SKU_DEFINITION
                    WHERE
                        SKU_ID IN (SELECT 
                                pud.KEY_ID
                            FROM
                                KETTLE_SCM_DEV.PRICE_UPDATE_EVENT pue, KETTLE_SCM_DEV.PRICE_UPDATE_ENTRY pud
                            WHERE
                                pue.EVENT_ID = pud.EVENT_ID
                                    AND pud.KEY_TYPE = 'SKU'
                                    AND pue.EVENT_ID = EVENT_ID
                                    AND pue.EVENT_STATUS = 'INITIATED')))
    GROUP BY gri.SKU_ID , gri.UNIT_OF_MEASURE) qty
    WHERE
        sku.SKU_ID = qty.SKU_ID
            AND qty.RECEIVED_QUANTITY > 0
            AND sku.LINKED_PRODUCT_ID = pd.PRODUCT_ID
            AND sku.UNIT_OF_MEASURE = pd.UNIT_OF_MEASURE
            AND sku.UNIT_OF_MEASURE = qty.UNIT_OF_MEASURE) a
GROUP BY a.PRODUCT_ID , a.PRODUCT_NAME, a.UNIT_OF_MEASURE;

INSERT INTO KETTLE_SCM_DEV.PRICE_UPDATE_DRILLDOWN (ENTRY_ID , EVENT_ID ,DRILLDOWN_CATEGORY , DRILLDOWN_TYPE,KEY_TYPE,KEY_ID,KEY_NAME,UOM,UNIT_PRICE,QUANTITY, COST)
select pue.ENTRY_ID,EVENT_ID,'SKU_WEIGHTED_MEAN','SKU','SKU',a.SKU_ID, a.SKU_NAME,a.UNIT_OF_MEASURE,a.EDITED_UNIT_PRICE,a .RECEIVED_QUANTITY ,a.TOTAL_COST from KETTLE_SCM_DEV.PRICE_UPDATE_ENTRY pue,
(SELECT 
        pd.PRODUCT_ID PRODUCT_ID,
            sku.SKU_ID,
            sku.SKU_NAME,
            sku.UNIT_OF_MEASURE,
            sku.EDITED_UNIT_PRICE EDITED_UNIT_PRICE,
            sku.EDITED_UNIT_PRICE * qty.RECEIVED_QUANTITY TOTAL_COST,
            qty.RECEIVED_QUANTITY
    FROM
        KETTLE_SCM_DEV.PRODUCT_DEFINITION pd, (SELECT 
        sku.SKU_ID,
        sku.SKU_NAME,
        
            sku.UNIT_OF_MEASURE UNIT_OF_MEASURE,
            sku.LINKED_PRODUCT_ID LINKED_PRODUCT_ID,
            CASE
                WHEN price.SKU_ID IS NULL THEN sku.NEGOTIATED_UNIT_PRICE
                ELSE price.EDITED_UNIT_PRICE
            END EDITED_UNIT_PRICE
    FROM
        KETTLE_SCM_DEV.SKU_DEFINITION sku
    LEFT OUTER JOIN (SELECT 
        pud.KEY_ID SKU_ID,
            pud.EDITED_UNIT_PRICE EDITED_UNIT_PRICE
    FROM
        KETTLE_SCM_DEV.PRICE_UPDATE_EVENT pue, KETTLE_SCM_DEV.PRICE_UPDATE_ENTRY pud
    WHERE
        pue.EVENT_ID = pud.EVENT_ID
            AND pud.KEY_TYPE = 'SKU'
            AND pue.EVENT_ID = EVENT_ID
            AND pue.EVENT_STATUS = 'INITIATED') price ON sku.SKU_ID = price.SKU_ID) sku, (SELECT 
        gri.SKU_ID SKU_ID,
            gri.UNIT_OF_MEASURE UNIT_OF_MEASURE,
            SUM(RECEIVED_QUANTITY) RECEIVED_QUANTITY
    FROM
        KETTLE_SCM_DEV.GOODS_RECEIVED gr, KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM gri, KETTLE_SCM_DEV.UNIT_DETAIL tu, KETTLE_SCM_DEV.UNIT_CATEGORY tuc, KETTLE_SCM_DEV.UNIT_DETAIL ru, KETTLE_SCM_DEV.UNIT_CATEGORY ruc
    WHERE
        gr.GENERATION_UNIT_ID = tu.UNIT_ID
            AND tu.CATEGORY_ID = tuc.CATEGORY_ID
            AND gr.GENERATED_FOR_UNIT_ID = ru.UNIT_ID
            AND ru.CATEGORY_ID = ruc.CATEGORY_ID
            AND gr.GOODS_RECEIVED_ID = gri.GOODS_RECEIVED_ID
            AND gr.GOODS_RECEIVED_STATUS = 'SETTLED'
            AND ((gr.GENERATED_FOR_UNIT_ID = gr.GENERATION_UNIT_ID)
            OR (tuc.CATEGORY_CODE IN ('KITCHEN' , 'WAREHOUSE')
            AND ruc.CATEGORY_CODE NOT IN ('KITCHEN' , 'WAREHOUSE')))
            AND gr.GENERATION_TIME > '2016-01-01 00:00:00'
            AND gri.SKU_ID IN (SELECT 
                SKU_ID
            FROM
                KETTLE_SCM_DEV.SKU_DEFINITION
            WHERE
                LINKED_PRODUCT_ID IN (SELECT 
                        LINKED_PRODUCT_ID
                    FROM
                        KETTLE_SCM_DEV.SKU_DEFINITION
                    WHERE
                        SKU_ID IN (SELECT 
                                pud.KEY_ID
                            FROM
                                KETTLE_SCM_DEV.PRICE_UPDATE_EVENT pue, KETTLE_SCM_DEV.PRICE_UPDATE_ENTRY pud
                            WHERE
                                pue.EVENT_ID = pud.EVENT_ID
                                    AND pud.KEY_TYPE = 'SKU'
                                    AND pue.EVENT_ID = EVENT_ID
                                    AND pue.EVENT_STATUS = 'INITIATED')))
    GROUP BY gri.SKU_ID , gri.UNIT_OF_MEASURE) qty
    WHERE
        sku.SKU_ID = qty.SKU_ID
            AND qty.RECEIVED_QUANTITY > 0
            AND sku.LINKED_PRODUCT_ID = pd.PRODUCT_ID
            AND sku.UNIT_OF_MEASURE = pd.UNIT_OF_MEASURE
            AND sku.UNIT_OF_MEASURE = qty.UNIT_OF_MEASURE
            ) a
            where pue.KEY_ID = a.PRODUCT_ID
            and pue.KEY_TYPE = 'PRODUCT'
            and pue.EVENT_ID = EVENT_ID;
            
UPDATE KETTLE_SCM_DEV.PRICE_UPDATE_ENTRY pue 
SET 
    HAS_ERROR = 'Y'
WHERE
    pue.EVENT_ID = EVENT_ID
        AND pue.KEY_TYPE IN ('SKU' , 'PRODUCT')
        AND EDITED_UNIT_PRICE IS NULL
        OR EDITED_UNIT_PRICE <= 0.0;

UPDATE KETTLE_SCM_DEV.PRICE_UPDATE_DRILLDOWN pue 
SET 
    HAS_ERROR = 'Y'
WHERE
    pue.EVENT_ID = EVENT_ID
        AND UNIT_PRICE IS NULL
        OR UNIT_PRICE <= 0
        OR QUANTITY IS NULL
        OR QUANTITY <= 0
        OR COST IS NULL
        OR COST <= 0;

INSERT INTO KETTLE_SCM_DEV.PRICE_UPDATE_ERROR (ENTRY_TYPE,ENTRY_ID,EVENT_ID ,ERROR_MESSAGE )
select 'DRILLDOWN',pue.DRILLDOWN_ID,pue.EVENT_ID,'INVALID_SKU_COST' from KETTLE_SCM_DEV.PRICE_UPDATE_DRILLDOWN pue where pue.EVENT_ID = EVENT_ID
and COST is null or COST <= 0
AND HAS_ERROR = 'Y'
AND DRILLDOWN_TYPE = 'SKU';

INSERT INTO KETTLE_SCM_DEV.PRICE_UPDATE_ERROR (ENTRY_TYPE,ENTRY_ID,EVENT_ID ,ERROR_MESSAGE )
select 'DRILLDOWN',pue.DRILLDOWN_ID,pue.EVENT_ID,'INVALID_SKU_PRICE' from KETTLE_SCM_DEV.PRICE_UPDATE_DRILLDOWN pue where pue.EVENT_ID = EVENT_ID
and UNIT_PRICE is null or UNIT_PRICE <= 0
AND HAS_ERROR = 'Y'
AND DRILLDOWN_TYPE = 'SKU';

INSERT INTO KETTLE_SCM_DEV.PRICE_UPDATE_ERROR (ENTRY_TYPE,ENTRY_ID,EVENT_ID ,ERROR_MESSAGE )
select 'DRILLDOWN',pue.DRILLDOWN_ID,pue.EVENT_ID,'INVALID_SKU_QUANTITY' from KETTLE_SCM_DEV.PRICE_UPDATE_DRILLDOWN pue where pue.EVENT_ID = EVENT_ID
and QUANTITY is null or QUANTITY <= 0
AND HAS_ERROR = 'Y'
AND DRILLDOWN_TYPE = 'SKU';

INSERT INTO KETTLE_SCM_DEV.PRICE_UPDATE_ERROR (ENTRY_TYPE,ENTRY_ID,EVENT_ID ,ERROR_MESSAGE )
select 'ENTRY',pue.ENTRY_ID,pue.EVENT_ID, case when pue.KEY_TYPE = 'PRODUCT' then 'ZERO_PRICE_PRODUCT' else 'ZERO_PRICE_SKU' end 
from KETTLE_SCM_DEV.PRICE_UPDATE_ENTRY pue where pue.EVENT_ID = EVENT_ID
and pue.KEY_TYPE IN( 'SKU', 'PRODUCT')
AND HAS_ERROR = 'Y';

UPDATE KETTLE_SCM_DEV.PRICE_UPDATE_EVENT pud 
SET 
    NO_OF_ERRORS = (SELECT 
            COUNT(*)
        FROM
            PRICE_UPDATE_ERROR pue
        WHERE
            pue.EVENT_ID = EVENT_ID)
WHERE
    pud.EVENT_ID = EVENT_ID
;

END$$
DELIMITER ;


INSERT INTO `kettle_master_dev`.`access_control_list_data` (`ACL_MODULE`, `ACL_MODULE_DESCRIPTION`, `ACL_STATUS`, `APPLICATION_NAME`) VALUES ('scm-service.sku-price-management.view.*', 'SKU Manager', 'ACTIVE', 'SCM_SERVICE');
INSERT INTO `kettle_master_dev`.`access_control_list_data` (`ACL_MODULE`, `ACL_MODULE_DESCRIPTION`, `ACL_STATUS`, `APPLICATION_NAME`) VALUES ('scm-service.sku-price-management.approve.*', 'SKU Admin', 'ACTIVE', 'SCM_SERVICE');

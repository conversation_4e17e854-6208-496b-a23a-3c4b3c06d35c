UPDATE `KETTLE_SCM_DEV`.`COMPANY_BANK_MAPPING` SET `BANK_CODE`='KOTAK_V2', `CLIENT_CODE`='DKCTPL' WHERE `COMPANY_BANK_MAPPING_ID`='2';

CREATE INDEX VENDOR_GOODS_RECEIVED_DATA_GOODS_RECEIVED_STATUS ON KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA(GOODS_RECEIVED_STATUS) USING BTREE;
CREATE INDEX VENDOR_GOODS_RECEIVED_DATA_UPDATED_AT ON KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA(UPDATED_AT) USING BTREE;
CREATE INDEX VENDOR_GOODS_RECEIVED_DATA_CREATED_AT ON KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA(CREATED_AT) USING BTREE;
CREATE INDEX VENDOR_GOODS_RECEIVED_DATA_DELIVERY_UNIT_ID ON KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA(DELIVERY_UNIT_ID) USING BTREE;
CREATE INDEX VENDOR_GOODS_RECEIVED_DATA_VENDOR_ID ON KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA(VENDOR_ID) USING BTREE;
CREATE INDEX VENDOR_GOODS_RECEIVED_DATA_DISPATCH_ID ON KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA(DISPATCH_ID) USING BTREE;
CREATE INDEX VENDOR_GOODS_RECEIVED_DATA_DOCUMENT_NUMBER ON KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA(DOCUMENT_NUMBER) USING BTREE;
CREATE INDEX VENDOR_GOODS_RECEIVED_DATA_GR_DOCUMENT_ID ON KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA(GR_DOCUMENT_ID) USING BTREE;
CREATE INDEX VENDOR_GOODS_RECEIVED_DATA_PAYMENT_STATUS ON KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA(PAYMENT_STATUS) USING BTREE;
CREATE INDEX VENDOR_GOODS_RECEIVED_DATA_COMPANY_ID ON KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA(COMPANY_ID) USING BTREE;
CREATE INDEX VENDOR_GOODS_RECEIVED_DATA_TO_BE_PAID ON KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA(TO_BE_PAID) USING BTREE;
CREATE INDEX VENDOR_GOODS_RECEIVED_DATA_DOCUMENT_UPLOADED ON KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA(DOCUMENT_UPLOADED) USING BTREE;


INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('VGFCR', '7', 'ACTION', 'UPDATE', 'SuMo -> Vendor Receivings -> VGFCR-> FORCE CREATE', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`) VALUES ('1', (select ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VGFCR'), 'ACTIVE', '120502', '2019-04-11 11:20:27');

ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER
ADD COLUMN VENDOR_ID INTEGER NULL,
ADD COLUMN NOTIFICATION_TIME TIMESTAMP NULL,
ADD COLUMN IS_NOTIFIED VARCHAR(1) NULL,
ADD COLUMN NOTIFICATION_TYPES VARCHAR(20) NULL;

ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER
ADD COLUMN VENDOR_NAME VARCHAR(100) NULL;

CREATE INDEX REQUEST_ORDER_VENDOR_ID ON KETTLE_SCM_DEV.REQUEST_ORDER(VENDOR_ID) USING BTREE;
CREATE INDEX REQUEST_ORDER_NOTIFICATION_TIME ON KETTLE_SCM_DEV.REQUEST_ORDER(NOTIFICATION_TIME) USING BTREE;
CREATE INDEX REQUEST_ORDER_IS_NOTIFIED ON KETTLE_SCM_DEV.REQUEST_ORDER(IS_NOTIFIED) USING BTREE;
CREATE INDEX REQUEST_ORDER_NOTIFICATION_TYPES ON KETTLE_SCM_DEV.REQUEST_ORDER(NOTIFICATION_TYPES) USING BTREE;

ALTER TABLE KETTLE_SCM_DEV.UNIT_VENDOR_MAPPING
ADD COLUMN FULFILLMENT_LEAD_DAYS INTEGER NULL;


ALTER TABLE KETTLE_SCM_DEV.UNIT_VENDOR_MAPPING MODIFY NOTIFICATION_TIME VARCHAR(45);

UPDATE `KETTLE_MASTER_DEV`.`ACTION_DETAIL` SET `ACTION_TYPE`='ACTION' WHERE `ACTION_DETAIL_ID`='74';
UPDATE `KETTLE_MASTER_DEV`.`ACTION_DETAIL` SET `ACTION_TYPE`='ACTION' WHERE `ACTION_DETAIL_ID`='75';

ALTER TABLE KETTLE_SCM_DEV.UNIT_VENDOR_MAPPING
ADD COLUMN DISPATCH_LOCATION_ID INTEGER NULL;

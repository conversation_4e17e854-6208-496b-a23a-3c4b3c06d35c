CREATE TABLE KETTLE_SCM_DEV.LIST_DETAIL (
    LIST_DETAIL_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    TYPE VARCHAR(100),
    CODE VARCHAR(100),
    NAME VARCHAR(100),
    DESCRIPTION VARCHAR(200),
    STATUS VARCHAR(50),
    ALIAS VARCHAR(50)
);

CREATE TABLE KETTLE_SCM_DEV.LIST_TYPE (
    LIST_TYPE_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    LIST_DETAIL_ID INTEGER,
    CODE VARCHAR(100),
    NAME VARCHAR(100),
    DESCRIPTION VARCHAR(200),
    STATUS VARCHAR(50),
    ALIAS VARCHAR(50),
    FOREIGN KEY (`LIST_DETAIL_ID`)
    REFERENCES `LIST_DETAIL` (`LIST_DETAIL_ID`)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);

CREATE TABLE KETTLE_SCM_DEV.LIST_DATA (
    LIST_DATA_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    LIST_TYPE_ID INTEGER,
    CODE VARCHAR(100),
    NAME VARCHAR(100),
    DESCRIPTION VARCHAR(200),
    STATUS VARCHAR(50),
    ALIAS VARCHAR(50),
	FOREIGN KEY (`LIST_TYPE_ID`)
    REFERENCES `LIST_TYPE` (`LIST_TYPE_ID`)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
);

CREATE TABLE KETTLE_SCM_DEV.COSTELEMENT_VENDOR_MAPPING (
    COSTELEMENT_VENDOR_MAPPING_ID INTEGER(11) NOT NULL AUTO_INCREMENT,
    COSTELEMENT_ID INTEGER(11) NOT NULL,
    VENDOR_ID INTEGER(11) NOT NULL,
    MAPPING_STATUS VARCHAR(15) NOT NULL,
    CREATED_AT TIMESTAMP NULL,
    CREATED_BY VARCHAR(50) NULL,
    UPDATED_AT TIMESTAMP NULL,
    UPDATED_BY VARCHAR(50) NULL,
    PRIMARY KEY (COSTELEMENT_VENDOR_MAPPING_ID),
    UNIQUE KEY COSTELEMENT_VENDOR_MAPPING_KEY (VENDOR_ID , COSTELEMENT_ID)
);

CREATE TABLE KETTLE_SCM_DEV.COSTELEMENT_COSTCENTER_MAPPING (
    COSTELEMENT_COSTCENTER_MAPPING_ID INTEGER(11) NOT NULL AUTO_INCREMENT,
    COSTELEMENT_ID INTEGER(11) NOT NULL,
    COSTCENTER_ID INTEGER(11) NOT NULL,
    MAPPING_STATUS VARCHAR(15) NOT NULL,
    CREATED_AT TIMESTAMP NULL,
    CREATED_BY VARCHAR(50) NULL,
    UPDATED_AT TIMESTAMP NULL,
    UPDATED_BY VARCHAR(50) NULL,
    PRIMARY KEY (COSTELEMENT_COSTCENTER_MAPPING_ID),
    UNIQUE KEY COSTELEMENT_COSTCENTER_MAPPING_KEY (COSTCENTER_ID , COSTELEMENT_ID)
);

CREATE TABLE KETTLE_SCM_DEV.VENDOR_COSTCENTER_COSTELEMENT_MAPPING (
    VENDOR_COSTCENTER_COSTELEMENT_MAPPING_ID INTEGER(11) NOT NULL AUTO_INCREMENT,
    COSTELEMENT_ID INTEGER(11) NOT NULL,
    COSTCENTER_ID INTEGER(11) NOT NULL,
    VENDOR_ID INTEGER(11) NOT NULL,
    PRICE DECIMAL(10,2) NOT NULL,
    MAPPING_STATUS VARCHAR(15) NOT NULL,
    CREATED_AT TIMESTAMP NULL,
    CREATED_BY VARCHAR(50) NULL,
    UPDATED_AT TIMESTAMP NULL,
    UPDATED_BY VARCHAR(50) NULL,
    PRIMARY KEY (VENDOR_COSTCENTER_COSTELEMENT_MAPPING_ID),
    UNIQUE KEY VENDOR_COSTCENTER_COSTELEMENT_MAPPING_KEY (COSTCENTER_ID , COSTELEMENT_ID,VENDOR_ID)
);


 ALTER TABLE KETTLE_SCM_DEV.COST_ELEMENT_DATA ADD COLUMN CATEGORY_ID INTEGER, 
  ADD CONSTRAINT `category`
  FOREIGN KEY (`CATEGORY_ID`)
  REFERENCES `KETTLE_SCM_DEV`.`LIST_DETAIL` (`LIST_DETAIL_ID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;
  
 ALTER TABLE KETTLE_SCM_DEV.COST_ELEMENT_DATA ADD COLUMN DEPARTMENT_ID INTEGER,
  ADD CONSTRAINT `department`
  FOREIGN KEY (`DEPARTMENT_ID`)
  REFERENCES `KETTLE_SCM_DEV`.`LIST_DETAIL` (`LIST_DETAIL_ID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;
  
 ALTER TABLE KETTLE_SCM_DEV.COST_ELEMENT_DATA ADD COLUMN DIVISION_ID INTEGER,
  ADD CONSTRAINT `division`
  FOREIGN KEY (`DIVISION_ID`)
  REFERENCES `KETTLE_SCM_DEV`.`LIST_DETAIL` (`LIST_DETAIL_ID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;
  
 ALTER TABLE KETTLE_SCM_DEV.COST_ELEMENT_DATA ADD COLUMN SUB_CATEGORY INTEGER,
  ADD CONSTRAINT `subcategory`
  FOREIGN KEY (`SUB_CATEGORY`)
  REFERENCES `KETTLE_SCM_DEV`.`LIST_TYPE` (`LIST_TYPE_ID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;
  
 ALTER TABLE KETTLE_SCM_DEV.COST_ELEMENT_DATA ADD COLUMN SUB_SUB_CATEGORY INTEGER,
  ADD CONSTRAINT `subsubcategory`
  FOREIGN KEY (`SUB_SUB_CATEGORY`)
  REFERENCES `KETTLE_SCM_DEV`.`LIST_DATA` (`LIST_DATA_ID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;
  
  ALTER TABLE KETTLE_SCM_DEV.COST_ELEMENT_DATA ADD COLUMN CAPEX VARCHAR(50) NOT NULL;
  
ALTER TABLE `KETTLE_SCM_DEV`.`COST_ELEMENT_DATA` 
ADD COLUMN `UOM` VARCHAR(50) NULL DEFAULT NULL;



ALTER TABLE `KETTLE_SCM_DEV`.`SERVICE_ORDER` 
ADD COLUMN `TYPE` VARCHAR(45) NULL DEFAULT NULL ;

ALTER TABLE `KETTLE_SCM_DEV`.`SERVICE_ORDER_ITEM` 
ADD COLUMN `TYPE` VARCHAR(45) NULL DEFAULT NULL ,
ADD COLUMN `BUSINESS_COST_CENTER_ID` INT(11) NULL DEFAULT NULL ,
ADD COLUMN `BUSINESS_COST_CENTER_NAME` VARCHAR(100) NULL DEFAULT NULL ;

ALTER TABLE `KETTLE_SCM_DEV`.`SERVICE_RECEIVED_ITEM` 
ADD COLUMN `PENDING_INVOICE_QUANTITY` DECIMAL(20,6) NULL DEFAULT NULL ,
ADD COLUMN `INVOICE_QUANTITY` DECIMAL(20,6) NULL DEFAULT NULL  ;


ALTER TABLE `KETTLE_SCM_DEV`.`PAYMENT_INVOICE_ITEM` 
ADD COLUMN `SERVICE_RECEIVED_ID` INT(11) NULL DEFAULT NULL ,
ADD COLUMN `SERVICE_RECEIVED_ITEM_ID` INT(11) NULL DEFAULT NULL ;


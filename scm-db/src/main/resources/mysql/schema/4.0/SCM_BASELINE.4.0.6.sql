DROP TABLE KETTLE_SCM_DEV.COST_DETAIL_DATA;
CREATE TABLE KETTLE_SCM_DEV.COST_DETAIL_DATA(
COST_DETAIL_DATA_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
<PERSON>EY_ID INTEGER NOT NULL,
KEY_TYPE VARCHAR(25) NOT NULL,
UNIT_ID INTEGER NOT NULL,
PRICE DECIMAL(16,6) NOT NULL,
QUANTITY DECIMAL(16,6) NOT NULL,
UNIT_OF_MEASURE VARCHAR(10) NOT NULL,
IS_LATEST VARCHAR(1) NOT NULL,
LAST_UPDATE_TMSTMP TIMESTAMP NOT NULL);

DROP TABLE KETTLE_SCM_DEV.COST_DETAIL_AUDIT_DATA;
CREATE TABLE KETTLE_SCM_DEV.COST_DETAIL_AUDIT_DATA(
COST_DETAIL_DATA_AUDIT_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
COST_DETAIL_DATA_ID INTEGER NOT NULL,
<PERSON><PERSON><PERSON>_ID INTEGER NOT NULL,
KEY_TYPE VARCHAR(25) NOT NULL,
UNIT_ID INTEGER NOT NULL,
PRICE DECIMAL(16,6) NOT NULL,
QUANTITY DECIMAL(16,6) NOT NULL,
UNIT_OF_MEASURE VARCHAR(10) NOT NULL,
TRANSACTTION_TYPE VARCHAR(25) NULL,
ADD_TIME TIMESTAMP NOT NULL
);

CREATE TABLE KETTLE_SCM_DEV.COST_DETAIL_AUDIT_DRILLDOWN_DATA(
COST_DETAIL_DATA_AUDIT_DRILLDOWN_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
COST_DETAIL_DATA_AUDIT_ID INTEGER NOT NULL,
KEY_ID INTEGER NOT NULL,
KEY_TYPE VARCHAR(25) NOT NULL,
PRICE DECIMAL(16,6) NOT NULL,
QUANTITY DECIMAL(16,6) NOT NULL,
UNIT_OF_MEASURE VARCHAR(10) NOT NULL,
ADD_TIME TIMESTAMP NOT NULL
);

ALTER TABLE KETTLE_SCM_DEV.COST_DETAIL_AUDIT_DATA
ADD COLUMN IS_CANCELLATION VARCHAR(1) NOT NULL DEFAULT 'N';
ALTER TABLE KETTLE_SCM_DEV.COST_DETAIL_AUDIT_DRILLDOWN_DATA
ADD COLUMN IS_CANCELLATION VARCHAR(1) NOT NULL DEFAULT 'N';

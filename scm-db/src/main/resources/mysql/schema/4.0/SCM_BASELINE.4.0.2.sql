CREATE TABLE COST_DETAIL_DATA(
COST_DETA<PERSON>_DATA_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
KEY_ID INTEGER NOT NULL,
KEY_TYPE VARCHAR(25) NOT NULL,
UNIT_ID INTEGER NOT NULL,
COST DECIMAL(16,6) NOT NULL,
QUANTITY DECIMAL(16,6) NOT NULL,
TRIGGER_TYPE VARCHAR(25) NULL,
LAST_UPDATE_TMSTMP TIMESTAMP NOT NULL);

CREATE TABLE COST_DETAIL_AUDIT_DATA(
COST_DETAIL_DATA_AUDIT_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
COST_DETAIL_DATA_ID INTEGER NOT NULL,
OLD_PRICE DECIMAL(16,6) NOT NULL,
NEW_PRICE DECIMAL(16,6) NOT NULL,
FROM_TIME TIMESTAMP NOT NULL,
TO_TIME TIMESTAMP NOT NULL,
TRIGGER_TYPE VARCHAR(25) NULL,
TRIG<PERSON>R_ID INTEGER NOT NULL
);

#already executed in production environment
alter table KETTLE_SCM_DEV .PURCHASE_ORDER_ITEM_DETAIL
modify column PACKAGING_QUANTITY DECIMAL(16,4) NOT NULL;


UPDATE INTO KETTLE_MASTER_DEV.PRE_AUTHENTICATED_API set API='scm-service.stock-management.kettle-wastage-event'
WHERE API = 'scm-service.stock-management.wastage-event';

CREATE INDEX inventory_unit_id ON STOCK_INVENTORY (UNIT_ID) USING BTREE;
CREATE INDEX frequency_type ON DAY_CLOSE_EVENT (CLOSURE_EVENT_FREQUENCY) USING BTREE;
CREATE INDEX closure_event_type ON DAY_CLOSE_EVENT (CLOSURE_EVENT_TYPE) USING BTREE;
CREATE INDEX INVENTORY_BUSINESS_DATE ON STOCK_INVENTORY (BUSINESS_DATE) USING BTREE;
CREATE INDEX DCPV_PRODUCT_ID ON DAY_CLOSE_PRODUCT_VALUES (PRODUCT_ID) USING BTREE;
CREATE INDEX DCPV_UNIT_ID ON DAY_CLOSE_PRODUCT_VALUES (UNIT_ID) USING BTREE;
CREATE INDEX PRODUCT_ID_KEY ON PRODUCT_PACKAGING_MAPPING (PRODUCT_ID) USING BTREE;
CREATE INDEX SKU_ID_KEY ON SKU_PACKAGING_MAPPING (SKU_ID) USING BTREE;
CREATE INDEX SKU_ID_KEY ON STOCK_ENTRY (SKU_ID) USING BTREE;
CREATE INDEX SKU_ID_KEY ON INVENTORY_DRILLDOWN (SKU_ID) USING BTREE;

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL
(ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES (''VGRCL'', ''7'', ''ACTION'', ''CANCEL'', ''SuMo -> Vendor Receivings -> VGRCL-> CANCEL'', ''ACTIVE'');

insert into KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)
select 1,ACTION_DETAIL_ID,''ACTIVE',100000,'2017-07-01 00:00:00' from KETTLE_MASTER_DEV.ACTION_DETAIL where ACTION_CODE = 'VGRCL';
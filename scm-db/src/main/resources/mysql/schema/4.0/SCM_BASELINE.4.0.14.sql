ALTER TABLE KETTLE_SCM_DEV.COST_DETAIL_AUDIT_DATA ADD COLUMN OLD_PRICE DECIMAL(16,6) NULL;

CREATE TABLE KETTLE_SCM_DEV.`VENDOR_COMPANY_DEBIT_MAPPING` (
  `DEBIT_MAPPING_ID` int(11) NOT NULL AUTO_INCREMENT,
  `VENDOR_ID` int(11) NOT NULL,
  `COMPANY_ID` int(11) NOT NULL,
   `COMPANY_NAME` varchar(200) NOT NULL,
  `DEBIT_BALANCE` DECIMAL(16,6) NOT NULL DEFAULT 0,
  `LAST_UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`DEBIT_MAPPING_ID`),
  UNIQUE KEY `VENDOR_DEBIT` (`VENDOR_ID`,`COMPANY_ID`),
  CONSTRAINT `VENDOR_COMPANY_DEBIT_MAPPING_ibfk_1` FOREIGN KEY (`VENDOR_ID`) REFERENCES `KETTLE_SCM_DEV`.VENDOR_DETAIL_DATA (`VENDOR_ID`) ON UPDATE CASCADE
) ;


INSERT INTO VENDOR_COMPANY_DEBIT_MAPPING (VENDOR_ID, COMPANY_ID, COMPANY_NAME, DEBIT_BALANCE, LAST_UPDATE_TIME)
SELECT VENDOR_ID,1000,'Sunshine Teahouse Private Limited', DEBIT_BALANCE, current_timestamp() FROM VENDOR_ACCOUNT_DETAILS;

CREATE TABLE `KETTLE_SCM_DEV`.`COMPANY_BANK_MAPPING`  (
  `COMPANY_BANK_MAPPING_ID` INT(11) NOT NULL AUTO_INCREMENT,
  `COMPANY_ID` INT(11) NOT NULL,
  `BANK_CODE` VARCHAR(100) NOT NULL,
  `BANK_NAME` VARCHAR(150) NOT NULL,
  `BANK_ACCOUNT_NO` VARCHAR(75) NOT NULL,
  `BANK_IFSC_CODE` VARCHAR(45) NOT NULL,
  `BENIFICIARY_NAME` VARCHAR(100) NOT NULL,
  `CLIENT_CODE` VARCHAR(45) NULL,
  PRIMARY KEY (`COMPANY_BANK_MAPPING_ID`),
  UNIQUE INDEX `BANK_ACCOUNT_NO_UNIQUE` (`BANK_ACCOUNT_NO` ASC));
 
ALTER TABLE KETTLE_SCM_DEV.PAYMENT_CALENDAR ADD COLUMN COMPANY_ID INT(11) NOT NULL;

UPDATE KETTLE_SCM_DEV.PAYMENT_CALENDAR SET COMPANY_ID =1000;

INSERT INTO KETTLE_SCM_DEV.PAYMENT_CALENDAR (CYCLE_NAME, PAYMENT_DATE,PR_CREATION_DATE,INVOICE_DATE,CYCLE_TAG,COMPANY_ID) 
(SELECT CYCLE_NAME,PAYMENT_DATE,PR_CREATION_DATE,INVOICE_DATE,CYCLE_TAG,1001 FROM KETTLE_SCM_DEV.PAYMENT_CALENDAR where COMPANY_ID = 1000);

ALTER TABLE KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA ADD COLUMN COMPANY_ID INT(11) NOT NULL;

-- Add Company to Request Order
ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER 
ADD COLUMN REQUEST_COMPANY_ID INTEGER NOT NULL AFTER REQUEST_UNIT_ID,
ADD COLUMN FULFILLMENT_COMPANY_ID INTEGER NOT NULL AFTER FULFILLMENT_UNIT_ID;


UPDATE KETTLE_SCM_DEV.REQUEST_ORDER tod INNER JOIN KETTLE_SCM_DEV.UNIT_DETAIL ud1 ON tod.REQUEST_UNIT_ID = ud1.UNIT_ID
INNER JOIN KETTLE_SCM_DEV.UNIT_DETAIL ud2 ON tod.FULFILLMENT_UNIT_ID = ud2.UNIT_ID
SET tod.REQUEST_COMPANY_ID = ud1.COMPANY_ID, tod.FULFILLMENT_COMPANY_ID = ud2.COMPANY_ID;


-- Add Company to Purchase Order
ALTER TABLE KETTLE_SCM_DEV.PURCHASE_ORDER ADD COLUMN COMPANY_ID INTEGER NOT NULL;

UPDATE KETTLE_SCM_DEV.PURCHASE_ORDER po INNER JOIN KETTLE_SCM_DEV.UNIT_DETAIL ud ON po.DELIVERY_LOCATION_ID = ud.UNIT_ID
SET po.COMPANY_ID = ud.COMPANY_ID;


-- Add Company to Transfer Order
ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER 
ADD COLUMN SOURCE_COMPANY_ID INTEGER NOT NULL AFTER GENERATION_UNIT_ID,
ADD COLUMN RECEIVING_COMPANY_ID INTEGER NOT NULL AFTER GENERATED_FOR_UNIT_ID;


UPDATE KETTLE_SCM_DEV.TRANSFER_ORDER tod INNER JOIN KETTLE_SCM_DEV.UNIT_DETAIL ud1 ON tod.GENERATION_UNIT_ID = ud1.UNIT_ID
INNER JOIN KETTLE_SCM_DEV.UNIT_DETAIL ud2 ON tod.GENERATED_FOR_UNIT_ID = ud2.UNIT_ID
SET tod.SOURCE_COMPANY_ID = ud1.COMPANY_ID, tod.RECEIVING_COMPANY_ID = ud2.COMPANY_ID;


-- Add Company to Goods Received
ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED 
ADD COLUMN SOURCE_COMPANY_ID INTEGER NOT NULL AFTER GENERATION_UNIT_ID,
ADD COLUMN RECEIVING_COMPANY_ID INTEGER NOT NULL AFTER GENERATED_FOR_UNIT_ID;

UPDATE KETTLE_SCM_DEV.GOODS_RECEIVED tod INNER JOIN KETTLE_SCM_DEV.UNIT_DETAIL ud1 ON tod.GENERATION_UNIT_ID = ud1.UNIT_ID
INNER JOIN KETTLE_SCM_DEV.UNIT_DETAIL ud2 ON tod.GENERATED_FOR_UNIT_ID = ud2.UNIT_ID
SET tod.SOURCE_COMPANY_ID = ud1.COMPANY_ID, tod.RECEIVING_COMPANY_ID = ud2.COMPANY_ID;


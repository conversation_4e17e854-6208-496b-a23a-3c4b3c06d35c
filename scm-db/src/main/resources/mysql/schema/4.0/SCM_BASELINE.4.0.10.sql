CREATE TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST(
    PAYMENT_REQUEST_ID INT(11) PRIMARY KEY AUTO_INCREMENT,
    PAYMENT_REQUEST_TYPE VARCHAR(200) NOT NULL,
    VENDOR_ID INT,
    CREATED_BY INT NOT NULL,
    CREATION_TIME TIMESTAMP NULL,
    CURRENT_STATUS VARCHAR(50) NOT NULL,
    LAST_UPDATED TIMESTAMP NOT NULL,
    PROPOSED_PAYMENT_DATE TIMESTAMP NOT NULL,
    PROPOSED_PAYMENT_CYCLE VARCHAR(100)
);

CREATE TABLE KETTLE_SCM_DEV.PAYMENT_INVOICE(
    PAYMENT_INVOICE_ID INT(11) PRIMARY KEY AUTO_INCREMENT,
    INVOICE_NUMBER VARCHAR(255),
    INVOICE_DOCUMENT_HANDLE VARCHAR(255),
    PAYMENT_REQUEST_ID INT(11) NOT NULL,
    PAYMENT_AMOUNT DECIMAL(16,6) NOT NULL,
    CALCULATED_AMOUNT DECIMAL(16,6) NOT NULL,
    INVOICE_AMOUNT DECIMAL(16,6) NOT NULL,
    FOREIGN KEY (PAYMENT_REQUEST_ID) REFERENCES PAYMENT_REQUEST(PAYMENT_REQUEST_ID)
);

CREATE TABLE KETTLE_SCM_DEV.PAYMENT_INVOICE_ITEM(
    PAYMENT_INVOICE_ITEM_ID INT(11) PRIMARY KEY AUTO_INCREMENT,
    SKU_ID INT(11) NOT NULL,
    SKU_NAME VARCHAR(255) NOT NULL,
    UOM VARCHAR(10) NOT NULL,
    QUANTITY DECIMAL(16,6) NOT NULL,
    PAYMENT_INVOICE_ID INT(11) NOT NULL,
    HSN VARCHAR(255) NOT NULL,
    PACKAGING_ID INT(11) NOT NULL,
    PACKAGING_NAME VARCHAR(255) NOT NULL,
    CONVERSION_RATIO DECIMAL(16,6) NOT NULL,
    TOTAL_AMOUNT DECIMAL(16,6) NOT NULL,
    TOTAL_TAX DECIMAL(16,6) NOT NULL,
    TOTAL_PRICE DECIMAL(16,6) NOT NULL,
    UNIT_PRICE DECIMAL(16,6) NOT NULL,
    FOREIGN KEY (PAYMENT_INVOICE_ID) REFERENCES PAYMENT_INVOICE(PAYMENT_INVOICE_ID)
);

CREATE TABLE KETTLE_SCM_DEV.PAYMENT_INVOICE_ITEM_TAX(
    PAYMENT_INVOICE_ITEM_TAX_ID INT(11) PRIMARY KEY AUTO_INCREMENT,
    PR_INVOICE_ITEM_ID INT(11) NOT NULL,
    TAX_TYPE VARCHAR(255) NOT NULL,
    TAX_PERCENTAGE DECIMAL(16,6) NOT NULL,
    TAX_VALUE DECIMAL(16,6) NOT NULL,
    FOREIGN KEY (PR_INVOICE_ITEM_ID) REFERENCES PAYMENT_INVOICE_ITEM (PAYMENT_INVOICE_ITEM_ID)
);

CREATE TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST_ITEM_MAPPING (
    PAYMENT_REQUEST_ITEM_MAPPING_ID INT(11) PRIMARY KEY AUTO_INCREMENT,
    PAYMENT_REQUEST_ID INT(11) NOT NULL,
    PAYMENT_REQUEST_TYPE VARCHAR(255) NOT NULL,
    PAYMENT_REQUEST_ITEM_ID INT(11) NOT NULL
);

CREATE TABLE KETTLE_SCM_DEV.PAYMENT_DEVIATION (
    PAYMENT_DEVIATION_ID INT(11) PRIMARY KEY AUTO_INCREMENT,
    DEVIATION_CODE VARCHAR(50) NOT NULL,
    DEVIATION_TYPE VARCHAR(255) NOT NULL,
    DEVIATION_LEVEL VARCHAR(255) NOT NULL,
    DEVIATION_DETAIL VARCHAR(255) NOT NULL
);

CREATE TABLE KETTLE_SCM_DEV.INVOICE_DEVIATION_MAPPING (
    INVOICE_DEVIATION_MAPPING_ID INT(11) PRIMARY KEY AUTO_INCREMENT,
    PAYMENT_DEVIATION_ID INT(11) NOT NULL,
    CURRENT_STATUS VARCHAR(255) NOT NULL,
    ACTION_REMARK VARCHAR(255) NOT NULL,
    DEVIATION_ITEM_ID INT(11) NOT NULL,
    DEVIATION_ITEM_TYPE VARCHAR(255) NOT NULL,
    ACCEPTED_BY INT(11) NOT NULL,
    REJECTED_BY INT(11) NOT NULL,
    REMOVED_BY INT(11) NOT NULL,
    CREATED_BY INT(11) NOT NULL,
    ACTION_TIME TIMESTAMP,
    FOREIGN KEY (PAYMENT_DEVIATION_ID) REFERENCES PAYMENT_DEVIATION (PAYMENT_DEVIATION_ID)
);

CREATE TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST_LOG (
    PAYMENT_REQUEST_LOG_ID INT(11) PRIMARY KEY AUTO_INCREMENT,
    LOG_DATA VARCHAR(255) NOT NULL,
    UPDATE_TIME TIMESTAMP NOT NULL,
    PAYMENT_REQUEST_ID INT(11) NOT NULL
);

CREATE TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST_STATUS_LOG (
    PAYMENT_REQUEST_STATUS_LOG_ID INT(11) PRIMARY KEY AUTO_INCREMENT,
    FROM_STATUS VARCHAR(255) NOT NULL,
    TO_STATUS VARCHAR(255) NOT NULL,
    UPDATED_BY INT(11) NOT NULL,
    UPDATE_TIME TIMESTAMP,
    PAYMENT_REQUEST_ID INT(11) NOT NULL
);

ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST ADD COLUMN PROPOSED_AMOUNT DECIMAL(16,6) NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST ADD COLUMN PAID_AMOUNT DECIMAL(16,6) NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST ADD COLUMN AMOUNTS_MATCH VARCHAR(1) NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST ADD COLUMN IS_BLOCKED VARCHAR(1) NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST ADD COLUMN BLOCKED_BY INT(11) NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST ADD COLUMN REQUESTING_UNIT INT(11) NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST ADD COLUMN GR_DOC_TYPE VARCHAR(50);
ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST ADD COLUMN DEVIATION_COUNT INT(11) NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST ADD COLUMN INVOICE_NUMBER VARCHAR(255);
ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST ADD COLUMN PAYMENT_DETAIL_ID INT(11);

ALTER TABLE KETTLE_SCM_DEV.PAYMENT_INVOICE_ITEM ADD COLUMN PACKAGING_PRICE DECIMAL(16,6);

ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST_STATUS_LOG ADD COLUMN REMARKS VARCHAR(255);

CREATE TABLE KETTLE_SCM_DEV.PR_PAYMENT_DETAIL(
    PAYMENT_DETAIL_ID INT(11) PRIMARY KEY AUTO_INCREMENT,
    VENDOR_ID INT(11) NOT NULL,
    VENDOR_NAME VARCHAR(255) NOT NULL,
    BENEFICIARY_IFSC_CODE VARCHAR(100) NOT NULL,
    BENEFICIARY_ACCOUNT_NUMBER VARCHAR(100) NOT NULL,
    DEBIT_ACCOUNT_NUMBER VARCHAR(100),
    DEBIT_BANK_NAME VARCHAR(255),
    PAYMENT_TYPE VARCHAR(100) NOT NULL,
    PAID_AMOUNT DECIMAL(16,6) NOT NULL,
    PAYMENT_DATE TIMESTAMP,
    PAYMENT_REMARKS VARCHAR(255)
);

ALTER TABLE KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA ADD COLUMN PAYMENT_STATUS VARCHAR(100);
ALTER TABLE KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA ADD COLUMN PAYMENT_REQUEST_ID INT(11);

CREATE TABLE KETTLE_SCM_DEV.PAYMENT_CALENDAR(
    PAYMENT_CALENDAR_ID INT(11) PRIMARY KEY AUTO_INCREMENT,
    CYCLE_NAME VARCHAR(100) NOT NULL,
    PAYMENT_DATE TIMESTAMP,
    PR_CREATION_DATE TIMESTAMP NOT NULL,
    INVOICE_DATE TIMESTAMP NOT NULL
);

CREATE TABLE KETTLE_SCM_DEV.DEBIT_NOTE_DETAIL(
    DEBIT_NOTE_DETAIL_ID INT(11) PRIMARY KEY AUTO_INCREMENT,
    INVOICE_NUMBER VARCHAR(255) NOT NULL,
    AMOUNT DECIMAL(16,6) NOT NULL,
    TOTAL_TAX DECIMAL(16,6) NOT NULL,
    TOTAL_AMOUNT DECIMAL(16,6) NOT NULL,
    CREDIT_NOTE_RECEIVED VARCHAR(1) NOT NULL DEFAULT "N",
    PAYMENT_REQUEST_ID INT(11) NOT NULL,
    CREDIT_NOTE_RECEIVING_TIME TIMESTAMP NULL,
    GENERATION_TIME TIMESTAMP NULL
);

INSERT INTO KETTLE_MASTER_DEV.REF_LOOKUP_TYPE (RTL_GROUP, RTL_CODE, RTL_NAME, STATUS) VALUES ('PR_TYPE', 'PaymentRequestType', 'Payment Request Type', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.REF_LOOKUP (RTL_ID, RL_CODE, RL_NAME, RL_SHORT_CODE, RL_STATUS) VALUES
((SELECT RTL_ID FROM REF_LOOKUP_TYPE WHERE RTL_GROUP = 'PR_TYPE'), 'GOODS_RECEIVED', 'Goods Received', 'GR', 'ACTIVE');

ALTER TABLE KETTLE_SCM_DEV.PAYMENT_INVOICE ADD COLUMN EXTRA_CHARGES DECIMAL(16,6) NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST ADD COLUMN REMARKS VARCHAR(255);

INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_DEVIATION` (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL`) VALUES ('001', 'REJECTION', 'INVOICE', 'Our GSTIN no missing');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_DEVIATION` (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL`) VALUES ('002', 'REJECTION', 'INVOICE', 'Supplier GSTIN no missing');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_DEVIATION` (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL`) VALUES ('003', 'REJECTION', 'INVOICE', 'Supplier Despatched details or address not mentioned');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_DEVIATION` (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL`) VALUES ('004', 'REJECTION', 'INVOICE', 'Our Correct Name & Address not mentioned');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_DEVIATION` (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL`) VALUES ('005', 'REJECTION', 'INVOICE', 'Invoice heading not mentioned i.e. Tax Invoice or Bill of Supply ');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_DEVIATION` (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL`) VALUES ('006', 'REJECTION', 'INVOICE', 'Invoice no is missing');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_DEVIATION` (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL`) VALUES ('007', 'REJECTION', 'INVOICE', 'Invoice date is missing');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_DEVIATION` (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL`) VALUES ('008', 'REJECTION', 'INVOICE', 'Wrong tax calculated');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_DEVIATION` (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL`) VALUES ('009', 'REJECTION', 'INVOICE', 'Description not mentioned');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_DEVIATION` (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL`) VALUES ('010', 'REJECTION', 'INVOICE', 'Invoice calculation is wrong');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_DEVIATION` (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL`) VALUES ('011', 'REJECTION', 'INVOICE', 'Tax type wrongly charges');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_DEVIATION` (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL`) VALUES ('012', 'REJECTION', 'INVOICE', 'Wrong rate of Tax mentioned');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_DEVIATION` (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL`) VALUES ('013', 'REJECTION', 'INVOICE', 'VAT/CST/Service Tax charges');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_DEVIATION` (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL`) VALUES ('014', 'REJECTION', 'INVOICE', 'Cutting/Overwriting on Invoice');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_DEVIATION` (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL`) VALUES ('015', 'REJECTION', 'INVOICE', 'Invoice not signed');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_DEVIATION` (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL`) VALUES ('016', 'REJECTION', 'INVOICE', 'Tax rate difference in GRN & Invoice');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_DEVIATION` (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL`) VALUES ('017', 'REJECTION', 'INVOICE', 'Hardcopy not received');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_DEVIATION` (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL`) VALUES ('018', 'REJECTION', 'INVOICE', 'Billing address and GR address are different on state level');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_DEVIATION` (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL`) VALUES ('019', 'DEVIATION', 'INVOICE', 'GR Less on invoice');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_DEVIATION` (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL`) VALUES ('020', 'DEVIATION', 'INVOICE', 'Late PO');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_DEVIATION` (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL`) VALUES ('021', 'DEVIATION', 'INVOICE_ITEM', 'UOM mismatch');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_DEVIATION` (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL`) VALUES ('022', 'DEVIATION', 'INVOICE_ITEM', 'Tax mismatch');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_DEVIATION` (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL`) VALUES ('023', 'DEVIATION', 'INVOICE', 'Other charges mismatch');


ALTER TABLE KETTLE_SCM_DEV.INVOICE_DEVIATION_MAPPING ADD COLUMN DEVIATION_REMARK VARCHAR(255);

ALTER TABLE KETTLE_SCM_DEV.PAYMENT_INVOICE MODIFY COLUMN INVOICE_DOCUMENT_HANDLE INT(11);

ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST CHANGE COLUMN `BLOCKED_BY` `BLOCKED_BY` INT(11) NULL ;
ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST CHANGE COLUMN `PROPOSED_PAYMENT_DATE` `PROPOSED_PAYMENT_DATE` TIMESTAMP NULL ;
ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST CHANGE COLUMN `PROPOSED_PAYMENT_DATE` `PROPOSED_PAYMENT_DATE` TIMESTAMP NULL ;

ALTER TABLE KETTLE_SCM_DEV.INVOICE_DEVIATION_MAPPING
CHANGE COLUMN `ACTION_REMARK` `ACTION_REMARK` VARCHAR(255) NULL ,
CHANGE COLUMN `ACCEPTED_BY` `ACCEPTED_BY` INT(11) NULL ,
CHANGE COLUMN `REJECTED_BY` `REJECTED_BY` INT(11) NULL ,
CHANGE COLUMN `REMOVED_BY` `REMOVED_BY` INT(11) NULL ,
CHANGE COLUMN `ACTION_TIME` `ACTION_TIME` TIMESTAMP NULL;

ALTER TABLE KETTLE_SCM_DEV.INVOICE_DEVIATION_MAPPING ADD COLUMN CREATION_TIME TIMESTAMP NULL;
ALTER TABLE KETTLE_SCM_DEV.PAYMENT_INVOICE ADD COLUMN INVOICE_DATE TIMESTAMP NULL;
ALTER TABLE KETTLE_SCM_DEV.DEBIT_NOTE_DETAIL ADD COLUMN GENERATED_BY INT(11) NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.DEBIT_NOTE_DETAIL ADD COLUMN LAST_UPDATED_BY INT(11) NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.DEBIT_NOTE_DETAIL ADD COLUMN UPDATE_TIME TIMESTAMP NULL;


ALTER TABLE KETTLE_SCM_DEV.VENDOR_ACCOUNT_DETAILS ADD COLUMN DEBIT_BALANCE DECIMAL(16,6) NOT NULL DEFAULT 0;
ALTER TABLE KETTLE_SCM_DEV.VENDOR_ACCOUNT_DETAILS ADD COLUMN PAYMENT_BLOCKED VARCHAR(1) NOT NULL DEFAULT 'N';

ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST_ITEM_MAPPING ADD COLUMN LINKED_PAYMENT_REQUEST_ID INT(11);
ALTER TABLE KETTLE_SCM_DEV.PAYMENT_CALENDAR ADD COLUMN CYCLE_TAG INT(11);

ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST ADD COLUMN PAYMENT_CYCLE INT(11) NULL;
ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST DROP COLUMN PROPOSED_PAYMENT_DATE;
ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST DROP COLUMN PROPOSED_PAYMENT_CYCLE;
ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST ADD FOREIGN KEY (PAYMENT_CYCLE) REFERENCES PAYMENT_CALENDAR(PAYMENT_CALENDAR_ID);

ALTER TABLE KETTLE_SCM_DEV.PR_PAYMENT_DETAIL ADD COLUMN PROPOSED_AMOUNT DECIMAL(16,6) NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.PR_PAYMENT_DETAIL ADD COLUMN CREATED_BY INT(11) NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.PR_PAYMENT_DETAIL ADD COLUMN UTR_NUMBER VARCHAR(255) NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.PR_PAYMENT_DETAIL ADD COLUMN ACTUAL_DATE TIMESTAMP NULL;

ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST ADD COLUMN PAID_ADHOC VARCHAR(1) NOT NULL DEFAULT 'N';

ALTER TABLE KETTLE_SCM_DEV.PR_PAYMENT_DETAIL MODIFY COLUMN PAYMENT_TYPE VARCHAR(100) NULL;

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('PRCNL', '7', 'ACTION', 'CANCEL', 'SUMO -> MANAGE PAYMENTS -> SEARCH PAYMENT REQUEST -> CANCEL', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('PRAPR', '7', 'ACTION', 'UPDATE', 'SUMO -> MANAGE PAYMENTS -> SEARCH PAYMENT REQUEST -> APPROVE', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('PRSTL', '7', 'ACTION', 'UPDATE', 'SUMO -> MANAGE PAYMENTS -> SEARCH PAYMENT REQUEST -> SETTLE', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('PRACK', '7', 'ACTION', 'UPDATE', 'SUMO -> MANAGE PAYMENTS -> SEARCH PAYMENT REQUEST -> ACKNOWLEDGE', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('PRADP', '7', 'ACTION', 'UPDATE', 'SUMO -> MANAGE PAYMENTS -> SEARCH PAYMENT REQUEST -> PAY ADHOC', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('PRBLK', '7', 'ACTION', 'UPDATE', 'SUMO -> MANAGE PAYMENTS -> SEARCH PAYMENT REQUEST -> BLOCK', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('PRUBL', '7', 'ACTION', 'UPDATE', 'SUMO -> MANAGE PAYMENTS -> SEARCH PAYMENT REQUEST -> UN BLOCK', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('PRSPP', '7', 'ACTION', 'UPDATE', 'SUMO -> MANAGE PAYMENTS -> SEARCH PAYMENT REQUEST -> START PAYMENT PROCESS', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('VNBLKP', '7', 'ACTION', 'UPDATE', 'SUMO -> VENDOR MANGEMENT -> VIEW -> BLOCK PAYMENTS', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('VNUBLP', '7', 'ACTION', 'UPDATE', 'SUMO -> VENDOR MANGEMENT -> VIEW -> UN BLOCK PAYMENTS', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('VNDDB', '7', 'ACTION', 'VIEW', 'SUMO -> VENDOR MANGEMENT -> DEBIT BALANCE -> DOWNLOAD DEBIT BALANCE', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('VNUDB', '7', 'ACTION', 'UPDATE', 'SUMO -> VENDOR MANGEMENT -> DEBIT BALANCE -> UPLOAD DEBIT BALANCE', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('DNSTL', '7', 'ACTION', 'UPDATE', 'SUMO -> MANAGE PAYMENTS -> DEBIT NOTE -> SETTLE', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('PRCRT', '7', 'SUBMENU', 'VIEW', 'SUMO -> MANAGE PAYMENTS -> CREATE PAYMENT REQUEST', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('PRSTLP', '7', 'SUBMENU', 'VIEW', 'SUMO -> MANAGE PAYMENTS -> SETTLE PAYMENTS', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('PRUPI', '7', 'ACTION', 'UPDATE', 'SUMO -> MANAGE PAYMENTS -> SEARCH PAYMENT REQUEST -> UPDATE INVOICE', 'ACTIVE');


INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING(ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY, LAST_UPDATE_TIME)
VALUES (1, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRCNL'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(1, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRAPR'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(1, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRSTL'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(1, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRACK'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(1, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRADP'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(1, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRBLK'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(1, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRUBL'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(1, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRSPP'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(1, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VNBLKP'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(1, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VNUBLP'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(1, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VNDDB'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(1, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VNUDB'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(1, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'DNSTL'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(1, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRCRT'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(1, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRSTLP'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(1, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRUPI'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(2, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRCNL'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(2, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRCRT'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(2, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRUPI'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(3, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRCNL'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(3, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRCRT'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(3, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRUPI'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(5, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRCNL'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(5, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRCRT'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(5, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRUPI'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(6, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRAPR'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(6, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRSTL'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(6, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRACK'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(6, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRADP'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(6, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRBLK'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(6, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRUBL'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(6, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRSPP'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(6, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VNBLKP'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(6, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VNUBLP'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(6, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VNDDB'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(6, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VNUDB'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(6, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'DNSTL'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(6, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRSTLP'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(7, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRAPR'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(7, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRACK'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(7, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRBLK'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(7, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRUBL'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(7, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRSPP'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(7, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VNBLKP'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(7, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VNUBLP'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(7, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VNDDB'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(7, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VNUDB'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(7, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'DNSTL'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(7, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRSTLP'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(8, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRAPR'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(8, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRACK'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(8, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRBLK'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(8, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRUBL'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(8, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRSPP'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(8, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VNBLKP'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(8, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VNUBLP'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(8, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VNDDB'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(8, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VNUDB'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(8, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'DNSTL'),'ACTIVE', 120103, '2017-10-17 17:56:00'),
(8, (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRSTLP'),'ACTIVE', 120103, '2017-10-17 17:56:00');



INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_CALENDAR` (`CYCLE_NAME`, `PAYMENT_DATE`, `PR_CREATION_DATE`, `INVOICE_DATE`, `CYCLE_TAG`) VALUES ('October 2017-1', '2017-10-12 00:00:00', '2017-09-03 00:00:00', '2017-10-01 00:00:00', '1');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_CALENDAR` (`CYCLE_NAME`, `PAYMENT_DATE`, `PR_CREATION_DATE`, `INVOICE_DATE`, `CYCLE_TAG`) VALUES ('October 2017-2', '2017-10-25 00:00:00', '2017-09-17 00:00:00', '2017-10-15 00:00:00', '2');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_CALENDAR` (`CYCLE_NAME`, `PAYMENT_DATE`, `PR_CREATION_DATE`, `INVOICE_DATE`, `CYCLE_TAG`) VALUES ('November 2017-1', '2017-11-12 00:00:00', '2017-10-03 00:00:00', '2017-11-01 00:00:00', '1');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_CALENDAR` (`CYCLE_NAME`, `PAYMENT_DATE`, `PR_CREATION_DATE`, `INVOICE_DATE`, `CYCLE_TAG`) VALUES ('November 2017-2', '2017-11-25 00:00:00', '2017-10-17 00:00:00', '2017-11-15 00:00:00', '2');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_CALENDAR` (`CYCLE_NAME`, `PAYMENT_DATE`, `PR_CREATION_DATE`, `INVOICE_DATE`, `CYCLE_TAG`) VALUES ('December 2017-1', '2017-12-12 00:00:00', '2017-11-03 00:00:00', '2017-12-01 00:00:00', '1');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_CALENDAR` (`CYCLE_NAME`, `PAYMENT_DATE`, `PR_CREATION_DATE`, `INVOICE_DATE`, `CYCLE_TAG`) VALUES ('December 2017-2', '2017-12-25 00:00:00', '2017-11-17 00:00:00', '2017-12-15 00:00:00', '2');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_CALENDAR` (`CYCLE_NAME`, `PAYMENT_DATE`, `PR_CREATION_DATE`, `INVOICE_DATE`, `CYCLE_TAG`) VALUES ('January 2018-1', '2018-01-12 00:00:00', '2017-12-03 00:00:00', '2018-01-01 00:00:00', '1');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_CALENDAR` (`CYCLE_NAME`, `PAYMENT_DATE`, `PR_CREATION_DATE`, `INVOICE_DATE`, `CYCLE_TAG`) VALUES ('January 2018-2', '2018-01-25 00:00:00', '2017-12-17 00:00:00', '2018-01-15 00:00:00', '2');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_CALENDAR` (`CYCLE_NAME`, `PAYMENT_DATE`, `PR_CREATION_DATE`, `INVOICE_DATE`, `CYCLE_TAG`) VALUES ('February 2018-1', '2018-02-12 00:00:00', '2018-01-03 00:00:00', '2018-02-01 00:00:00', '1');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_CALENDAR` (`CYCLE_NAME`, `PAYMENT_DATE`, `PR_CREATION_DATE`, `INVOICE_DATE`, `CYCLE_TAG`) VALUES ('February 2018-2', '2018-02-25 00:00:00', '2018-01-17 00:00:00', '2018-02-15 00:00:00', '2');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_CALENDAR` (`CYCLE_NAME`, `PAYMENT_DATE`, `PR_CREATION_DATE`, `INVOICE_DATE`, `CYCLE_TAG`) VALUES ('March 2018-1', '2018-03-12 00:00:00', '2018-02-03 00:00:00', '2018-03-01 00:00:00', '1');
INSERT INTO `KETTLE_SCM_DEV`.`PAYMENT_CALENDAR` (`CYCLE_NAME`, `PAYMENT_DATE`, `PR_CREATION_DATE`, `INVOICE_DATE`, `CYCLE_TAG`) VALUES ('March 2018-2', '2018-03-25 00:00:00', '2018-02-17 00:00:00', '2018-03-15 00:00:00', '2');


ALTER TABLE `KETTLE_SCM_DEV`.`PAYMENT_CALENDAR`
CHANGE COLUMN `PAYMENT_DATE` `PAYMENT_DATE` TIMESTAMP NULL ,
CHANGE COLUMN `PR_CREATION_DATE` `PR_CREATION_DATE` TIMESTAMP NULL ,
CHANGE COLUMN `INVOICE_DATE` `INVOICE_DATE` TIMESTAMP NULL ;

ALTER TABLE `KETTLE_SCM_DEV`.`PR_PAYMENT_DETAIL`
CHANGE COLUMN `PAYMENT_DATE` `PAYMENT_DATE` TIMESTAMP NULL ;

INSERT INTO KETTLE_SCM_DEV.PAYMENT_DEVIATION (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL` ) VALUES ('027', 'REJECTION', 'INVOICE', 'Bill is in the name of DKC');
INSERT INTO KETTLE_SCM_DEV.PAYMENT_DEVIATION (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL` ) VALUES ('028', 'REJECTION', 'INVOICE', 'Late Service Order raised');
INSERT INTO KETTLE_SCM_DEV.PAYMENT_DEVIATION (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL` ) VALUES ('029', 'REJECTION', 'INVOICE', 'Late Service Receiving Done.');
INSERT INTO KETTLE_SCM_DEV.PAYMENT_DEVIATION (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL` ) VALUES ('030', 'REJECTION', 'INVOICE', 'Same cost allocation / taken in each cafe for expense. (eg. logistics, marketing)');
INSERT INTO KETTLE_SCM_DEV.PAYMENT_DEVIATION (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL` ) VALUES ('031', 'REJECTION', 'INVOICE', 'HSN taken instead of SAC code (Exceptional case) eg. Showaarch, Garnishing Enterprises & A.tosh');
INSERT INTO KETTLE_SCM_DEV.PAYMENT_DEVIATION (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL` ) VALUES ('032', 'REJECTION', 'INVOICE', 'Others - required if need to add any instructions, justifications and descriptions while rejecting vouchers');
INSERT INTO KETTLE_SCM_DEV.PAYMENT_DEVIATION (`DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL` ) VALUES ('033', 'REJECTION', 'INVOICE', 'Detailed cost centre required -like bifurcation of cost centre in marketing other as well bills. exp month mentioned in bill');

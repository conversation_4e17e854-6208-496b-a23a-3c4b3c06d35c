DROP TABLE if exists KETTLE_SCM_DEV.WASTAGE_EVENT;
CREATE TABLE KETTLE_SCM_DEV.WASTAGE_EVENT (
  WASTAGE_ID INT NOT NULL AUTO_INCREMENT,
  BUSINESS_DATE DATE  NOT NULL,
  GE<PERSON>RATION_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  STATUS VARCHAR(45) NOT NULL DEFAULT 'IN_ACTIVE',
  PRODUCT_ID INT NOT NULL,
  QUANTITY DECIMAL(10,2) NOT NULL,
  UNIT_ID INT NOT NULL,
    PRIMARY KEY (WASTAGE_ID),
     FOREIGN KEY (PRODUCT_ID) REFERENCES KETTLE_SCM_DEV.PRODUCT_DEFINITION (PRODUCT_ID));
    
DROP TABLE if exists KETTLE_SCM_DEV.DAY_CLOSE_EVENT;
CREATE TABLE KETTLE_SCM_DEV.DAY_CLOSE_EVENT (
  EVENT_ID INT NOT NULL AUTO_INCREMENT,
  BUSINESS_DATE DATE  NOT NULL,
  <PERSON><PERSON><PERSON><PERSON><PERSON>_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  STATUS VARCHAR(45) NOT NULL DEFAULT 'INITIATED',
  DAY_CLOSURE_ID INT NOT NULL,
  UNIT_ID INT NOT NULL,
    PRIMARY KEY (EVENT_ID) );

  
  DROP TABLE if exists KETTLE_SCM_DEV.DAY_CLOSE_EVENT_RANGE;
  CREATE TABLE KETTLE_SCM_DEV.DAY_CLOSE_EVENT_RANGE (
  EVENT_RANGE_ID INT NOT NULL AUTO_INCREMENT ,
  EVENT_ID INT NOT NULL ,
  TYPE VARCHAR(45) NOT NULL ,
  START_ID INT NOT NULL ,
  END_ID INT NOT NULL ,
	  PRIMARY KEY (EVENT_RANGE_ID),
	  FOREIGN KEY (EVENT_ID) REFERENCES KETTLE_SCM_DEV.DAY_CLOSE_EVENT (EVENT_ID));
  
  
  
  DROP TABLE if exists KETTLE_SCM_DEV.DAY_CLOSE_PRODUCT_VALUES;
  CREATE TABLE KETTLE_SCM_DEV.DAY_CLOSE_PRODUCT_VALUES (
  CALCULATED_PRODUCT_ID INT NOT NULL AUTO_INCREMENT ,
  PRODUCT_ID INT NOT NULL ,
  UNIT_ID INT NOT NULL ,
  BUSINESS_DATE DATE  NOT NULL ,
  CONSUMPTION DECIMAL(10,2) NOT NULL DEFAULT 0 ,
  TRANSFER_OUT DECIMAL(10,2) NOT NULL DEFAULT 0 ,
  WASTAGE DECIMAL(10,2) NOT NULL DEFAULT 0 ,
  RECEIVED DECIMAL(10,2) NOT NULL DEFAULT 0 ,
  UOM VARCHAR(10) NOT NULL ,
  EVENT_ID INT NOT NULL ,
    PRIMARY KEY (CALCULATED_PRODUCT_ID)  ,
    FOREIGN KEY (EVENT_ID)
    REFERENCES KETTLE_SCM_DEV.DAY_CLOSE_EVENT (EVENT_ID));
    
	
DROP TABLE if exists KETTLE_SCM_DEV.STOCK_INVENTORY; 
CREATE TABLE KETTLE_SCM_DEV.STOCK_INVENTORY (
  STOCKING_ID INT NOT NULL AUTO_INCREMENT ,
  EVENT_TYPE VARCHAR(30) NOT NULL ,
  PRODUCT_ID INT NOT NULL ,
  UNIT_ID INT NOT NULL ,
  BUSINESS_DATE DATE  NULL ,
  GENERATION_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  GENERATED_BY INT NULL ,
  OPENING_STOCK DECIMAL(10,2) NOT NULL DEFAULT 0 ,
  EXPECTED_CLOSING_VALUE DECIMAL(10,2) NULL DEFAULT 0 ,
  CLOSING_STOCK DECIMAL(10,2) NULL DEFAULT 0 ,
  VARIANCE DECIMAL(10,2) NULL DEFAULT 0 ,
  UOM VARCHAR(30) NOT NULL ,
  COMMENT VARCHAR(200) NULL ,
  STATUS VARCHAR(45) NULL ,
  LAST_EVENT_ID INT,
  CURRENT_EVENT_ID INT,
  PRIMARY KEY (STOCKING_ID));

  
ALTER TABLE KETTLE_SCM_DEV.DAY_CLOSE_EVENT
ADD COLUMN CLOSURE_EVENT_TYPE VARCHAR(45) NOT NULL COMMENT '' AFTER UNIT_ID;

ALTER TABLE KETTLE_SCM_DEV.WASTAGE_EVENT 
ADD COLUMN COMMENT VARCHAR(450) NULL COMMENT '' AFTER UNIT_ID;

ALTER TABLE KETTLE_SCM_DEV.SKU_DEFINITION ADD COLUMN TORQUS_ID VARCHAR(200) NULL;

ALTER TABLE KETTLE_SCM_DEV.DAY_CLOSE_PRODUCT_VALUES
CHANGE COLUMN CONSUMPTION CONSUMPTION DECIMAL(10,2) NULL DEFAULT '0.00',
CHANGE COLUMN TRANSFER_OUT TRANSFER_OUT DECIMAL(10,2) NULL DEFAULT '0.00',
CHANGE COLUMN WASTAGE WASTAGE DECIMAL(10,2) NULL DEFAULT '0.00',
CHANGE COLUMN RECEIVED RECEIVED DECIMAL(10,2) NULL DEFAULT '0.00';

ALTER TABLE KETTLE_SCM_DEV.DAY_CLOSE_EVENT
CHANGE COLUMN DAY_CLOSURE_ID DAY_CLOSURE_ID INT(11) NULL;

ALTER TABLE KETTLE_SCM_DEV.DAY_CLOSE_EVENT
ADD COLUMN CLOSURE_EVENT_FREQUENCY VARCHAR (45) NOT NULL;
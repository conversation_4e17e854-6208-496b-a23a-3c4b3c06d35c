DROP TABLE IF EXISTS `KETTLE_SCM_ARCHIVE`.`INVENTORY_MOVEMENT_WH_REPORT_DUMP`;
-- `<PERSON><PERSON><PERSON>EN_WH_INVENTORY_MOVEMENT_REPORT_DUMP`
-- `INVENTORY_MOVEMENT_WH_REPORT_DUMP`
CREATE TABLE  `KETTLE_SCM_ARCHIVE`.`INVENTORY_MOVEMENT_WH_REPORT_DUMP` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `BUSINESS_DATE` date DEFAULT NULL,
  `UNIT_ID` int(11) DEFAULT NULL,
  `SKU_ID` int(11) DEFAULT NULL,
  `SKU_NAME` VARCHAR(500) DEFAULT NULL,
  `TAX_RATE` decimal(30,9) DEFAULT '0.000000',
  `MISSING_OPENING_EVENT` varchar(1) DEFAULT NULL,
  `MISSING_CLOSING_EVENT` varchar(1) DEFAULT NULL,
  `OPENING_DAY_CLOSE_EVENT_ID` int(11) DEFAULT NULL,
  `OPENING_GENERATION_TIME` datetime DEFAULT NULL,
  `OPENING_STOCK_QUANTITY` decimal(30,9) DEFAULT '0.000000',
  `OPENING_STOCK_PRICE` decimal(30,9) DEFAULT '0.000000',
  `OPENING_STOCK_TAX` decimal(30,9) DEFAULT '0.000000',
  `OPENING_STOCK_VALUE_WITHOUT_TAX` decimal(30,9) DEFAULT '0.000000',
  `OPENING_STOCK_VALUE_WITH_TAX` decimal(30,9) DEFAULT '0.000000',

  `CLOSING_DAY_CLOSE_EVENT_ID` int(11) DEFAULT NULL,
  `CLOSING_GENERATION_TIME` datetime DEFAULT NULL,
  `CLOSING_STOCK_QUANTITY` decimal(30,9) DEFAULT '0.000000',
  `EXPECTED_CLOSING_VALUE_INVENTORY_DRILLDOWN` decimal(30,9) DEFAULT '0.000000',
  `EXPECTED_CLOSING_STOCK` decimal(30,9) DEFAULT '0.000000',
  `EXPECTED_CLOSING_STOCK_AFTER_VARIANCE` decimal(30,9) DEFAULT '0.000000',
  `CLOSING_STOCK_PRICE` decimal(30,9) DEFAULT '0.000000',
  `CLOSING_STOCK_TAX` decimal(30,9) DEFAULT '0.000000',
  `CLOSING_STOCK_VALUE_WITHOUT_TAX` decimal(30,9) DEFAULT '0.000000',
  `CLOSING_STOCK_VALUE_WITH_TAX` decimal(30,9) DEFAULT '0.000000',

  `VARIANCE_QUANTITY` decimal(30,9) DEFAULT '0.000000',
  `VARIANCE_PRICE` decimal(30,9) DEFAULT '0.000000',
  `VARIANCE_TAX` decimal(30,9) DEFAULT '0.000000',
  `VARIANCE_VALUE_WITHOUT_TAX` decimal(30,9) DEFAULT '0.000000',
  `VARIANCE_VALUE_WITH_TAX` decimal(30,9) DEFAULT '0.000000',

  `TRANSFERRED_QUANTITY` decimal(30,9) DEFAULT '0.000000',
  `TRANSFERRED_PRICE` decimal(30,9) DEFAULT '0.000000',
  `TRANSFERRED_TAX` decimal(30,9) DEFAULT '0.000000',
  `TRANSFERRED_VALUE_WITHOUT_TAX` decimal(30,9) DEFAULT '0.000000',
  `TRANSFERRED_VALUE_WITH_TAX` decimal(30,9) DEFAULT '0.000000',

  `GOOD_RECEIVED_QUANTITY` decimal(30,9) DEFAULT '0.000000',
  `GOOD_RECEIVED_PRICE` decimal(30,9) DEFAULT '0.000000',
  `GOOD_RECEIVED_TAX` decimal(30,9) DEFAULT '0.000000',
  `GOOD_RECEIVED_VALUE_WITHOUT_TAX` decimal(30,9) DEFAULT '0.000000',
  `GOOD_RECEIVED_VALUE_WITH_TAX` decimal(30,9) DEFAULT '0.000000',

  `WASTAGE_QUANTITY` decimal(30,9) DEFAULT '0.000000',
  `WASTAGE_PRICE` decimal(30,9) DEFAULT '0.000000',
  `WASTAGE_TAX` decimal(30,9) DEFAULT '0.000000',
  `WASTAGE_VALUE_WITHOUT_TAX` decimal(30,9) DEFAULT '0.000000',
  `WASTAGE_VALUE_WITH_TAX` decimal(30,9) DEFAULT '0.000000',

  `VENDOR_GOOD_RECEIVED_QUANTITY` decimal(30,9) DEFAULT '0.000000',
  `VENDOR_GOOD_RECEIVED_PRICE` decimal(30,9) DEFAULT '0.000000',
  `VENDOR_GOOD_RECEIVED_TAX` decimal(30,9) DEFAULT '0.000000',
  `VENDOR_GOOD_RECEIVED_VALUE_WITHOUT_TAX` decimal(30,9) DEFAULT '0.000000',
  `VENDOR_GOOD_RECEIVED_VALUE_WITH_TAX` decimal(30,9) DEFAULT '0.000000',

  `B2B_SALE_QUANTITY` decimal(30,9) DEFAULT '0.000000',
  `B2B_SALE_PRICE` decimal(30,9) DEFAULT '0.000000',
  `B2B_SALE_TAX` decimal(30,9) DEFAULT '0.000000',
  `B2B_SALE_VALUE_WITHOUT_TAX` decimal(30,9) DEFAULT '0.000000',
  `B2B_SALE_VALUE_WITH_TAX` decimal(30,9) DEFAULT '0.000000',

  `B2B_RETURN_QUANTITY` decimal(30,9) DEFAULT '0.000000',
  `B2B_RETURN_PRICE` decimal(30,9) DEFAULT '0.000000',
  `B2B_RETURN_TAX` decimal(30,9) DEFAULT '0.000000',
  `B2B_RETURN_VALUE_WITHOUT_TAX` decimal(30,9) DEFAULT '0.000000',
  `B2B_RETURN_VALUE_WITH_TAX` decimal(30,9) DEFAULT '0.000000',

  `ECOM_QUANTITY` decimal(30,9) DEFAULT '0.000000',
  `ECOM_PRICE` decimal(30,9) DEFAULT '0.000000',
  `ECOM_TAX` decimal(30,9) DEFAULT '0.000000',
  `ECOM_VALUE_WITHOUT_TAX` decimal(30,9) DEFAULT '0.000000',
  `ECOM_VALUE_WITH_TAX` decimal(30,9) DEFAULT '0.000000',

  `SCRAP_QUANTITY` decimal(30,9) DEFAULT '0.000000',
  `SCRAP_PRICE` decimal(30,9) DEFAULT '0.000000',
  `SCRAP_TAX` decimal(30,9) DEFAULT '0.000000',
  `SCRAP_VALUE_WITHOUT_TAX` decimal(30,9) DEFAULT '0.000000',
  `SCRAP_VALUE_WITH_TAX` decimal(30,9) DEFAULT '0.000000',

  `RETURN_TO_VENDOR_QUANTITY` decimal(30,9) DEFAULT '0.000000',
  `RETURN_TO_VENDOR_PRICE` decimal(30,9) DEFAULT '0.000000',
  `RETURN_TO_VENDOR_TAX` decimal(30,9) DEFAULT '0.000000',
  `RETURN_TO_VENDOR_VALUE_WITHOUT_TAX` decimal(30,9) DEFAULT '0.000000',
  `RETURN_TO_VENDOR_VALUE_WITH_TAX` decimal(30,9) DEFAULT '0.000000',

  `GATEPASS_QUANTITY` decimal(30,9) DEFAULT '0.000000',
  `GATEPASS_PRICE` decimal(30,9) DEFAULT '0.000000',
  `GATEPASS_TAX` decimal(30,9) DEFAULT '0.000000',
  `GATEPASS_VALUE_WITHOUT_TAX` decimal(30,9) DEFAULT '0.000000',
  `GATEPASS_VALUE_WITH_TAX` decimal(30,9) DEFAULT '0.000000',

  `GATEPASS_RETURN_QUANTITY` decimal(30,9) DEFAULT '0.000000',
  `GATEPASS_RETURN_PRICE` decimal(30,9) DEFAULT '0.000000',
  `GATEPASS_RETURN_TAX` decimal(30,9) DEFAULT '0.000000',
  `GATEPASS_RETURN_VALUE_WITHOUT_TAX` decimal(30,9) DEFAULT '0.000000',
  `GATEPASS_RETURN_VALUE_WITH_TAX` decimal(30,9) DEFAULT '0.000000',

  `PRODUCTION_BOOKING_QUANTITY` decimal(30,9) DEFAULT '0.000000',
  `PRODUCTION_BOOKING_PRICE` decimal(30,9) DEFAULT '0.000000',
  `PRODUCTION_BOOKING_TAX` decimal(30,9) DEFAULT '0.000000',
  `PRODUCTION_BOOKING_VALUE_WITHOUT_TAX` decimal(30,9) DEFAULT '0.000000',
  `PRODUCTION_BOOKING_VALUE_WITH_TAX` decimal(30,9) DEFAULT '0.000000',

  `REVERSE_PRODUCTION_BOOKING_QUANTITY` decimal(30,9) DEFAULT '0.000000',
  `REVERSE_PRODUCTION_BOOKING_PRICE` decimal(30,9) DEFAULT '0.000000',
  `REVERSE_PRODUCTION_BOOKING_TAX` decimal(30,9) DEFAULT '0.000000',
  `REVERSE_PRODUCTION_BOOKING_VALUE_WITHOUT_TAX` decimal(30,9) DEFAULT '0.000000',
  `REVERSE_PRODUCTION_BOOKING_VALUE_WITH_TAX` decimal(30,9) DEFAULT '0.000000',

  `BOOKING_CONSUMPTION_QUANTITY` decimal(30,9) DEFAULT '0.000000',
  `BOOKING_CONSUMPTION_PRICE` decimal(30,9) DEFAULT '0.000000',
  `BOOKING_CONSUMPTION_TAX` decimal(30,9) DEFAULT '0.000000',
  `BOOKING_CONSUMPTION_VALUE_WITHOUT_TAX` decimal(30,9) DEFAULT '0.000000',
  `BOOKING_CONSUMPTION_VALUE_WITH_TAX` decimal(30,9) DEFAULT '0.000000',

  `REVERSE_BOOKING_CONSUMPTION_QUANTITY` decimal(30,9) DEFAULT '0.000000',
  `REVERSE_BOOKING_CONSUMPTION_PRICE` decimal(30,9) DEFAULT '0.000000',
  `REVERSE_BOOKING_CONSUMPTION_TAX` decimal(30,9) DEFAULT '0.000000',
  `REVERSE_BOOKING_CONSUMPTION_VALUE_WITHOUT_TAX` decimal(30,9) DEFAULT '0.000000',
  `REVERSE_BOOKING_CONSUMPTION_VALUE_WITH_TAX` decimal(30,9) DEFAULT '0.000000',

  PRIMARY KEY (`ID`),
  KEY `idx_INVENTORY_MOVEMENT_REPORT_BUSINESS_DATE` (`BUSINESS_DATE`),
  KEY `idx_INVENTORY_MOVEMENT_REPORT_UNIT_ID` (`UNIT_ID`),
  KEY `idx_INVENTORY_MOVEMENT_REPORT_DAY_CLOSE_EVENT_ID_O` (`OPENING_DAY_CLOSE_EVENT_ID`),
  KEY `idx_INVENTORY_MOVEMENT_REPORT_DAY_CLOSE_EVENT_ID_C` (`CLOSING_DAY_CLOSE_EVENT_ID`),
  KEY `idx_INVENTORY_MOVEMENT_REPORT_SKU_ID` (`SKU_ID`)
);


-- STRORED PROCEDURE FOR REPORT OF OTHER THAN CAFES (WH/KIT)

DROP PROCEDURE IF EXISTS `KETTLE_SCM_ARCHIVE`.`KITCHEN_WH_INVENTORY_MOVEMENT_REPORT_DUMP_PROCEDURE`;
DELIMITER $$
CREATE DEFINER=`rptusr`@`%` PROCEDURE `KETTLE_SCM_ARCHIVE`.`KITCHEN_WH_INVENTORY_MOVEMENT_REPORT_DUMP_PROCEDURE`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE CURRENT_UNIT_ID INT;
    DECLARE cursor_units CURSOR FOR
        SELECT UNIT_ID
        FROM KETTLE_MASTER_DUMP.UNIT_DETAIL UD
        WHERE UD.UNIT_CATEGORY NOT IN ("CAFE");

    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    OPEN cursor_units;

    read_loop: LOOP
        FETCH cursor_units INTO CURRENT_UNIT_ID;
        IF done THEN
            LEAVE read_loop;
        END IF;

DELETE FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP;


INSERT INTO KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP (UNIT_ID, SKU_ID,SKU_NAME)
SELECT CURRENT_UNIT_ID,SD.SKU_ID,SD.SKU_NAME
FROM KETTLE_SCM_DUMP.SKU_DEFINITION SD
INNER JOIN KETTLE_SCM_DUMP.PRODUCT_DEFINITION PD ON SD.LINKED_PRODUCT_ID = PD.PRODUCT_ID AND PD.CATEGORY_ID <> 3;


-- GETTING THE OPENING EVENT
UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IMR ,
(SELECT B.* FROM
		(SELECT DT.* FROM (
			SELECT
				DCE.UNIT_ID,
                SI.SKU_ID,
                MAX(SI.CLOSURE_EVENT_ID) CURRENT_EVENT_ID,
                MAX(DCE.GENERATION_TIME) GENERATION_TIME,
                MAX(DCE.BUSINESS_DATE) BUSINESS_DATE
			FROM
				KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM INNER JOIN
				KETTLE_SCM_DUMP.INVENTORY_DRILLDOWN SI ON IM.SKU_ID = SI.SKU_ID INNER JOIN
                KETTLE_SCM_DUMP.DAY_CLOSE_EVENT DCE ON SI.CLOSURE_EVENT_ID = DCE.EVENT_ID AND DCE.UNIT_ID = CURRENT_UNIT_ID and DCE.STATUS = 'CLOSED'
			and DCE.BUSINESS_DATE < '2023-04-01' AND DCE.BUSINESS_DATE > '2023-02-28'
			GROUP BY DCE.UNIT_ID , SI.SKU_ID) DT) B) A
	SET
		IMR.OPENING_DAY_CLOSE_EVENT_ID = A.CURRENT_EVENT_ID,
		IMR.OPENING_GENERATION_TIME= A.GENERATION_TIME,
        IMR.BUSINESS_DATE = A.BUSINESS_DATE
	WHERE
		IMR.SKU_ID = A.SKU_ID AND
		IMR.UNIT_ID = A.UNIT_ID ;

  -- UPDATING THE CLOSING EVENT ID
  UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IMR ,
(SELECT B.* FROM
		(SELECT DT.* FROM (
			SELECT
				DCE.UNIT_ID,
                SI.SKU_ID,
                MAX(SI.CLOSURE_EVENT_ID) CURRENT_EVENT_ID,
                MAX(DCE.GENERATION_TIME) GENERATION_TIME,
                MAX(DCE.BUSINESS_DATE) BUSINESS_DATE
			FROM
				KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM INNER JOIN
				KETTLE_SCM_DUMP.INVENTORY_DRILLDOWN SI ON IM.SKU_ID = SI.SKU_ID INNER JOIN
                KETTLE_SCM_DUMP.DAY_CLOSE_EVENT DCE ON SI.CLOSURE_EVENT_ID = DCE.EVENT_ID AND DCE.UNIT_ID = CURRENT_UNIT_ID and DCE.STATUS = 'CLOSED'
			AND DCE.BUSINESS_DATE <= '2023-04-30' AND DCE.BUSINESS_DATE > '2023-04-01'
			GROUP BY DCE.UNIT_ID , SI.SKU_ID) DT) B) A
	SET
		IMR.CLOSING_DAY_CLOSE_EVENT_ID = A.CURRENT_EVENT_ID,
		IMR.CLOSING_GENERATION_TIME= A.GENERATION_TIME
	WHERE
		IMR.SKU_ID = A.SKU_ID AND
		IMR.UNIT_ID = A.UNIT_ID;

-- SETTING THE OPENING STOCK OF THE OPENING EVNET THAT IS CLOSING OF OPENING

-- DUMPING DATA OF ALL TABLES

-- CREATING DUMP OF DATA OF GR DATA
DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.GOODS_RECEIVED_WH;

CREATE TEMPORARY TABLE `KETTLE_SCM_ARCHIVE`.`GOODS_RECEIVED_WH` (
  `GOODS_RECEIVED_ID` int(11) NOT NULL AUTO_INCREMENT,
  `GENERATION_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `GENERATION_UNIT_ID` int(11) NOT NULL,
  `SOURCE_COMPANY_ID` int(11) NOT NULL,
  `GENERATED_FOR_UNIT_ID` int(11) NOT NULL,
  `RECEIVING_COMPANY_ID` int(11) NOT NULL,
  `GENERATED_BY` int(11) NOT NULL,
  `GOODS_RECEIVED_STATUS` varchar(30) NOT NULL,
  `COMMENT` varchar(1000) DEFAULT NULL,
  `REQUEST_ORDER_ID` int(11) DEFAULT NULL,
  `TRANSFER_ORDER_ID` int(11) DEFAULT NULL,
  `LAST_UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `PURCHASE_ORDER_ID` int(11) DEFAULT NULL,
  `RECEIVED_BY` int(11) DEFAULT NULL,
  `TOTAL_AMOUNT` decimal(30,9) DEFAULT NULL,
  `CANCELLED_BY` int(11) DEFAULT NULL,
  `IS_AUTO_GENERATED` varchar(1) DEFAULT 'N',
  `PARENT_GR` int(11) DEFAULT NULL,
  `CLOSURE_EVENT_ID` int(11) DEFAULT NULL,
  `IS_REJECTED_GR` varchar(1) DEFAULT 'N',
  `REJECT_GR_COMMENT` varchar(1000) DEFAULT NULL,
  `PARENT_GR_COMMENT` varchar(1000) DEFAULT NULL,
  `TO_TYPE` varchar(255) DEFAULT NULL,
  `INVOICE_ID` int(11) DEFAULT NULL,
  `POR_IMAGES_DOC_IDS` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`GOODS_RECEIVED_ID`)
);

-- CREATE TABLE COZ DEFAULT VALUE
INSERT INTO KETTLE_SCM_ARCHIVE.GOODS_RECEIVED_WH
SELECT * FROM KETTLE_SCM_DUMP.GOODS_RECEIVED WHERE LAST_UPDATE_TIME >= (select min(OPENING_GENERATION_TIME) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP) AND  LAST_UPDATE_TIME <= (SElect MAX(CLOSING_GENERATION_TIME) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP)
AND GENERATED_FOR_UNIT_ID IN (SELECT DISTINCT UNIT_ID FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP);

-- CREATING GR ITEM DATA
DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.GOODS_RECEIVED_ITEM_WH ;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.GOODS_RECEIVED_ITEM_WH
SELECT ID.* FROM KETTLE_SCM_DUMP.GOODS_RECEIVED_ITEM ID WHERE GOODS_RECEIVED_ID IN (select DISTINCT GOODS_RECEIVED_ID FROM KETTLE_SCM_ARCHIVE.GOODS_RECEIVED_WH);


-- TO DATA
-- CREATING DUMP OF DATA OF GR DATA
DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.TRANSFER_ORDER_WH;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.TRANSFER_ORDER_WH
SELECT * FROM KETTLE_SCM_DUMP.TRANSFER_ORDER WHERE GENERATION_TIME >= (select min(OPENING_GENERATION_TIME) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP) AND  GENERATION_TIME <= (SElect MAX(CLOSING_GENERATION_TIME) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP)
AND GENERATION_UNIT_ID IN (SELECT DISTINCT UNIT_ID FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP);

-- CREATING GR ITEM DATA
DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.TRANSFER_ORDER_ITEM_WH ;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.TRANSFER_ORDER_ITEM_WH
SELECT ID.* FROM KETTLE_SCM_DUMP.TRANSFER_ORDER_ITEM ID WHERE TRANSFER_ORDER_ID IN (select distinct TRANSFER_ORDER_ID FROM KETTLE_SCM_ARCHIVE.TRANSFER_ORDER_WH);


-- VENDOR GR
DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.VENDOR_GOODS_RECEIVED_DATA_WH;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.VENDOR_GOODS_RECEIVED_DATA_WH
SELECT * FROM KETTLE_SCM_DUMP.VENDOR_GOODS_RECEIVED_DATA WHERE CREATED_AT >= (select min(OPENING_GENERATION_TIME) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP) AND  CREATED_AT <= (SElect MAX(CLOSING_GENERATION_TIME) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP) AND
DELIVERY_UNIT_ID IN (SELECT DISTINCT UNIT_ID FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP);

-- CREATING GR ITEM DATA
DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.VENDOR_GR_ITEM_DETAIL_WH ;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.VENDOR_GR_ITEM_DETAIL_WH
SELECT ID.* FROM KETTLE_SCM_DUMP.VENDOR_GR_ITEM_DETAIL ID WHERE VENDOR_GR_ID IN (select DISTINCT GOODS_RECEIVED_ID FROM KETTLE_SCM_ARCHIVE.VENDOR_GOODS_RECEIVED_DATA_WH);


-- wastage

DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.WASTAGE_EVENT_WH;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.WASTAGE_EVENT_WH
SELECT * FROM KETTLE_SCM_DUMP.WASTAGE_EVENT WHERE GENERATION_TIME >= (select min(OPENING_GENERATION_TIME) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP) AND  GENERATION_TIME <= (SElect MAX(CLOSING_GENERATION_TIME) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP) AND
UNIT_ID IN (SELECT DISTINCT UNIT_ID FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP);

-- CREATING WASTAGE ITEM DATA
DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.WASTAGE_ITEM_DATA_WH ;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.WASTAGE_ITEM_DATA_WH
SELECT ID.* FROM KETTLE_SCM_DUMP.WASTAGE_ITEM_DATA ID WHERE WASTAGE_ID IN (select DISTINCT WASTAGE_ID FROM KETTLE_SCM_ARCHIVE.WASTAGE_EVENT_WH);

-- GATEPASS OUT -> TRANSFER
-- CREATING GATEPASS DATA DUMP
DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.GATEPASS_DATA_WH;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.GATEPASS_DATA_WH
SELECT * FROM KETTLE_SCM_DUMP.GATEPASS_DATA WHERE CREATED_AT >= (select min(OPENING_GENERATION_TIME) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP) AND  CREATED_AT <= (SElect MAX(CLOSING_GENERATION_TIME) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP) AND
SENDING_UNIT IN (SELECT DISTINCT UNIT_ID FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP);

-- CREATING GATEPASS ITEM DATA DUMP
DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.GATEPASS_ITEM_DATA_WH ;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.GATEPASS_ITEM_DATA_WH
SELECT ID.* FROM KETTLE_SCM_DUMP.GATEPASS_ITEM_DATA ID WHERE GATEPASS_ID IN (select DISTINCT GATEPASS_ID FROM KETTLE_SCM_ARCHIVE.GATEPASS_DATA_WH);


-- B2B SALES AND B2B RETURNS, ECOM, SCRAP,AND RETURN TO VENDOR
-- CREATING B2B DATA DUMP
DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.SALES_PERFORMA_INVOICE_WH;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.SALES_PERFORMA_INVOICE_WH
SELECT * FROM KETTLE_SCM_DUMP.SALES_PERFORMA_INVOICE WHERE CREATED_AT >= (select min(OPENING_GENERATION_TIME) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP) AND  CREATED_AT <= (SElect MAX(CLOSING_GENERATION_TIME) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP) AND
SENDING_UNIT_ID IN (SELECT DISTINCT UNIT_ID FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP);

-- CREATING B2B ITEM DATA DUMP
DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.SALES_PERFORMA_INVOICE_ITEM_WH ;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.SALES_PERFORMA_INVOICE_ITEM_WH
SELECT ID.* FROM KETTLE_SCM_DUMP.SALES_PERFORMA_INVOICE_ITEM ID WHERE INVOICE_ID IN (select DISTINCT INVOICE_ID FROM KETTLE_SCM_ARCHIVE.SALES_PERFORMA_INVOICE_WH);


-- DOING FOR PRODUCTION BOOKING
DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.PRODUCTION_BOOKING ;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.PRODUCTION_BOOKING
SELECT * FROM KETTLE_SCM_DUMP.PRODUCTION_BOOKING WHERE GENERATION_TIME >= (select min(OPENING_GENERATION_TIME) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP) AND  GENERATION_TIME <= (SElect MAX(CLOSING_GENERATION_TIME) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP)
 AND UNIT_ID IN (SELECT DISTINCT UNIT_ID FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP);


DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.BOOKING_CONSUMPTION;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.BOOKING_CONSUMPTION
SELECT * FROM KETTLE_SCM_DUMP.BOOKING_CONSUMPTION WHERE PRODUCTION_BOOK_ID IN (select DISTINCT BOOKING_ID FROM KETTLE_SCM_ARCHIVE.PRODUCTION_BOOKING);

DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.DAY_CLOSE_EVENT_WH ;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.DAY_CLOSE_EVENT_WH
SELECT * FROM KETTLE_SCM_DUMP.DAY_CLOSE_EVENT WHERE EVENT_ID >= (select min(OPENING_DAY_CLOSE_EVENT_ID) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP) AND  EVENT_ID <= (SElect MAX(CLOSING_DAY_CLOSE_EVENT_ID) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP)
AND UNIT_ID IN (SELECT DISTINCT UNIT_ID FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP);


DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.INVENTORY_DRILLDOWN;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.INVENTORY_DRILLDOWN
SELECT ID.* FROM KETTLE_SCM_DUMP.INVENTORY_DRILLDOWN ID WHERE CLOSURE_EVENT_ID IN (select DISTINCT EVENT_ID FROM KETTLE_SCM_ARCHIVE.DAY_CLOSE_EVENT_WH);


-- SAME LIKE CALCULATING THE REVERSE BOOKINGS

DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.REVERSE_PRODUCTION_BOOKING ;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.REVERSE_PRODUCTION_BOOKING
SELECT * FROM KETTLE_SCM_DUMP.REVERSE_PRODUCTION_BOOKING WHERE GENERATION_TIME >= (select min(OPENING_GENERATION_TIME) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP) AND  GENERATION_TIME <= (SElect MAX(CLOSING_GENERATION_TIME) FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP)
 AND UNIT_ID IN (SELECT DISTINCT UNIT_ID FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP);


DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.REVERSE_BOOKING_CONSUMPTION;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.REVERSE_BOOKING_CONSUMPTION
SELECT * FROM KETTLE_SCM_DUMP.REVERSE_BOOKING_CONSUMPTION WHERE PRODUCTION_BOOK_ID IN (select DISTINCT BOOKING_ID FROM KETTLE_SCM_ARCHIVE.REVERSE_PRODUCTION_BOOKING);


-- DELETING DATA WHERE NO MOVEMENT INVOLVED
-- TODO CHANGE QUERY -> NEED TO DELETE SKU_ID'S OR NOT


DROP TEMPORARY TABLE IF EXISTS KETTLE_SCM_ARCHIVE.UNIQUE_SKU_IDS;
CREATE TEMPORARY TABLE KETTLE_SCM_ARCHIVE.UNIQUE_SKU_IDS (
SKU_ID INT);

INSERT INTO KETTLE_SCM_ARCHIVE.UNIQUE_SKU_IDS
select distinct SKU_ID from (
SELECT distinct SKU_ID FROM KETTLE_SCM_ARCHIVE.INVENTORY_DRILLDOWN
UNION ALL
(
	SELECT distinct GRI.SKU_ID FROM
    KETTLE_SCM_ARCHIVE.GOODS_RECEIVED_WH GR
        INNER JOIN
            KETTLE_SCM_ARCHIVE.GOODS_RECEIVED_ITEM_WH GRI ON GRI.GOODS_RECEIVED_ID = GR.GOODS_RECEIVED_ID AND GR.GOODS_RECEIVED_STATUS = "SETTLED"
)
UNION ALL
(
	SELECT distinct TOI.SKU_ID FROM
        KETTLE_SCM_ARCHIVE.TRANSFER_ORDER_WH TR
    INNER JOIN KETTLE_SCM_ARCHIVE.TRANSFER_ORDER_ITEM_WH TOI ON TOI.TRANSFER_ORDER_ID = TR.TRANSFER_ORDER_ID AND
		TR.TRANSFER_ORDER_STATUS IN ('CREATED','SETTLED','TRANSFERRED') AND TR.TO_TYPE = "REGULAR_TRANSFER"
)

UNION ALL
(
SELECT distinct VGRI.SKU_ID FROM
	KETTLE_SCM_ARCHIVE.VENDOR_GOODS_RECEIVED_DATA_WH VGR
		INNER JOIN KETTLE_SCM_ARCHIVE.VENDOR_GR_ITEM_DETAIL_WH VGRI ON VGR.GOODS_RECEIVED_ID = VGRI.VENDOR_GR_ID AND GOODS_RECEIVED_STATUS IN ("CREATED", "INITIATED")
)

UNION ALL
(
SELECT distinct WID.SKU_ID
FROM
		KETTLE_SCM_ARCHIVE.WASTAGE_EVENT_WH WE
	INNER JOIN
		KETTLE_SCM_ARCHIVE.WASTAGE_ITEM_DATA_WH WID ON WE.WASTAGE_ID=WID.WASTAGE_ID AND WE.STATUS = "SETTLED"
)) a;

INSERT INTO KETTLE_SCM_ARCHIVE.UNIQUE_SKU_IDS
SELECT distinct dce.SKU_ID FROM
KETTLE_SCM_ARCHIVE.GATEPASS_DATA_WH SI
inner join KETTLE_SCM_ARCHIVE.GATEPASS_ITEM_DATA_WH dce on SI.GATEPASS_ID = dce.GATEPASS_ID AND SI.STATUS <> "CANCELLED" AND dce.TRANSACTION_TYPE = "TRANSFER";

INSERT INTO KETTLE_SCM_ARCHIVE.UNIQUE_SKU_IDS
SELECT distinct dce.SKU_ID FROM
KETTLE_SCM_ARCHIVE.GATEPASS_DATA_WH SI
inner join KETTLE_SCM_ARCHIVE.GATEPASS_ITEM_DATA_WH dce on SI.GATEPASS_ID = dce.GATEPASS_ID AND SI.STATUS <> "CANCELLED" AND dce.TRANSACTION_TYPE = "RETURN";

INSERT INTO KETTLE_SCM_ARCHIVE.UNIQUE_SKU_IDS
SELECT distinct SPII.SKU_ID FROM
KETTLE_SCM_ARCHIVE.SALES_PERFORMA_INVOICE_WH SPI
 inner join KETTLE_SCM_ARCHIVE.SALES_PERFORMA_INVOICE_ITEM_WH SPII on SPI.INVOICE_ID = SPII.INVOICE_ID AND SPI.TYPE="B2B_SALES"
 AND SPI.INVOICE_STATUS IN ("CLOSED", "DELIVERED", "CORRECTION_APPROVAL", "DELIVERED_WITH_CORRECTION");

 INSERT INTO KETTLE_SCM_ARCHIVE.UNIQUE_SKU_IDS
SELECT distinct SPII.SKU_ID FROM
KETTLE_SCM_ARCHIVE.SALES_PERFORMA_INVOICE_WH SPI
inner join KETTLE_SCM_ARCHIVE.SALES_PERFORMA_INVOICE_ITEM_WH SPII on SPI.INVOICE_ID = SPII.INVOICE_ID
AND SPI.TYPE="B2B_RETURN" AND SPI.INVOICE_STATUS IN ("CLOSED", "DELIVERED", "CORRECTION_APPROVAL", "DELIVERED_WITH_CORRECTION");

INSERT INTO KETTLE_SCM_ARCHIVE.UNIQUE_SKU_IDS
SELECT distinct SPII.SKU_ID FROM
	KETTLE_SCM_ARCHIVE.SALES_PERFORMA_INVOICE_WH SPI
	 inner join KETTLE_SCM_ARCHIVE.SALES_PERFORMA_INVOICE_ITEM_WH SPII on SPI.INVOICE_ID = SPII.INVOICE_ID
     AND SPI.TYPE="ECOM" AND SPI.INVOICE_STATUS IN ("CLOSED", "DELIVERED", "CORRECTION_APPROVAL", "DELIVERED_WITH_CORRECTION");

INSERT INTO KETTLE_SCM_ARCHIVE.UNIQUE_SKU_IDS

SELECT distinct SPII.SKU_ID FROM
	KETTLE_SCM_ARCHIVE.SALES_PERFORMA_INVOICE_WH SPI
	 inner join KETTLE_SCM_ARCHIVE.SALES_PERFORMA_INVOICE_ITEM_WH SPII on SPI.INVOICE_ID = SPII.INVOICE_ID AND SPI.TYPE="RETURN_TO_VENDOR"
     AND SPI.INVOICE_STATUS IN ("CLOSED", "DELIVERED", "CORRECTION_APPROVAL", "DELIVERED_WITH_CORRECTION");

INSERT INTO KETTLE_SCM_ARCHIVE.UNIQUE_SKU_IDS
SELECT distinct SPII.SKU_ID FROM
	KETTLE_SCM_ARCHIVE.SALES_PERFORMA_INVOICE_WH SPI
	 inner join KETTLE_SCM_ARCHIVE.SALES_PERFORMA_INVOICE_ITEM_WH SPII on SPI.INVOICE_ID = SPII.INVOICE_ID AND SPI.TYPE="SCRAP"
     AND SPI.INVOICE_STATUS IN ("CLOSED", "DELIVERED", "CORRECTION_APPROVAL", "DELIVERED_WITH_CORRECTION");

INSERT INTO KETTLE_SCM_ARCHIVE.UNIQUE_SKU_IDS
SELECT distinct PB.SKU_ID FROM KETTLE_SCM_ARCHIVE.PRODUCTION_BOOKING PB WHERE PB.BOOKING_STATUS="CREATED";

INSERT INTO KETTLE_SCM_ARCHIVE.UNIQUE_SKU_IDS
SELECT distinct PB.SKU_ID FROM KETTLE_SCM_ARCHIVE.REVERSE_PRODUCTION_BOOKING PB WHERE PB.BOOKING_STATUS="CREATED";



INSERT INTO KETTLE_SCM_ARCHIVE.UNIQUE_SKU_IDS
SELECT distinct BC.SKU_ID FROM KETTLE_SCM_ARCHIVE.PRODUCTION_BOOKING PB
inner join KETTLE_SCM_ARCHIVE.BOOKING_CONSUMPTION BC on BC.PRODUCTION_BOOK_ID = PB.BOOKING_ID AND PB.BOOKING_STATUS="CREATED";



INSERT INTO KETTLE_SCM_ARCHIVE.UNIQUE_SKU_IDS
SELECT distinct BC.SKU_ID FROM KETTLE_SCM_ARCHIVE.REVERSE_PRODUCTION_BOOKING PB
inner join KETTLE_SCM_ARCHIVE.REVERSE_BOOKING_CONSUMPTION BC on BC.PRODUCTION_BOOK_ID = PB.BOOKING_ID AND PB.BOOKING_STATUS="CREATED";


DELETE FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP WHERE SKU_ID NOT IN (
	SELECT distinct SKU_ID FROM KETTLE_SCM_ARCHIVE.UNIQUE_SKU_IDS
);


UPDATE
	KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IMR ,
	(SELECT B.* FROM
		(SELECT DT.* FROM (
			SELECT
				IM.UNIT_ID,
                ID.SKU_ID,
                ID.CLOSURE_EVENT_ID,
                ID.ACTUAL_CLOSING,
                ID.PRICE
			FROM
				KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM
					INNER JOIN
				KETTLE_SCM_DUMP.INVENTORY_DRILLDOWN ID
                ON ID.CLOSURE_EVENT_ID = IM.OPENING_DAY_CLOSE_EVENT_ID AND ID.SKU_ID=IM.SKU_ID
                group by IM.UNIT_ID, ID.SKU_ID
                ) DT) B) A
	SET
		IMR.OPENING_STOCK_QUANTITY = ROUND(A.ACTUAL_CLOSING,9),
        IMR.OPENING_STOCK_PRICE = ROUND(A.PRICE,9),
        IMR.OPENING_STOCK_TAX = 0,
        IMR.OPENING_STOCK_VALUE_WITHOUT_TAX = ROUND((A.ACTUAL_CLOSING * A.PRICE),9),
        IMR.OPENING_STOCK_VALUE_WITH_TAX = ROUND((A.ACTUAL_CLOSING * A.PRICE),9)
	WHERE
		IMR.SKU_ID = A.SKU_ID
        AND IMR.UNIT_ID = A.UNIT_ID
        AND IMR.OPENING_DAY_CLOSE_EVENT_ID = A.CLOSURE_EVENT_ID;

-- UPDATING THE CLOSING STOCK FROM THE CLOSING EVENT

UPDATE
	KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IMR ,
	(SELECT B.* FROM
		(SELECT DT.* FROM (
			SELECT
				IM.UNIT_ID,
                ID.SKU_ID,
                ID.CLOSURE_EVENT_ID,
                ID.ACTUAL_CLOSING,
                ID.EXPECTED_CLOSING,
                ID.PRICE,
                ROUND((coalesce(ID.ACTUAL_CLOSING,0) * coalesce(ID.PRICE,0)),9)
			FROM
				KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM
					INNER JOIN
				KETTLE_SCM_DUMP.INVENTORY_DRILLDOWN ID
                ON ID.CLOSURE_EVENT_ID = IM.CLOSING_DAY_CLOSE_EVENT_ID AND ID.SKU_ID=IM.SKU_ID
                group by IM.UNIT_ID, ID.SKU_ID
                ) DT) B) A
	SET
		IMR.EXPECTED_CLOSING_VALUE_INVENTORY_DRILLDOWN = ROUND(A.EXPECTED_CLOSING,9),
        IMR.CLOSING_STOCK_QUANTITY = ROUND(A.ACTUAL_CLOSING,9),
        IMR.CLOSING_STOCK_PRICE = ROUND(A.PRICE,9),
        IMR.CLOSING_STOCK_TAX = 0,
        IMR.CLOSING_STOCK_VALUE_WITHOUT_TAX = ROUND((coalesce(A.ACTUAL_CLOSING,0) * coalesce(A.PRICE,0)),9),
        IMR.CLOSING_STOCK_VALUE_WITH_TAX = ROUND((coalesce(A.ACTUAL_CLOSING,0) * coalesce(A.PRICE,0)),9)
	WHERE
		IMR.SKU_ID = A.SKU_ID
        AND IMR.UNIT_ID = A.UNIT_ID
        AND IMR.CLOSING_DAY_CLOSE_EVENT_ID = A.CLOSURE_EVENT_ID;

  UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM,
    (
    SELECT
    UNIT_ID,
    min(OPENING_DAY_CLOSE_EVENT_ID) MIN_OPENING_EVENT,
    min(OPENING_GENERATION_TIME) MIN_OPENING_GENERATION_TIME
    FROM
    KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP
    GROUP BY UNIT_ID
    ) X
 SET
     IM.OPENING_DAY_CLOSE_EVENT_ID = X.MIN_OPENING_EVENT,
     IM.OPENING_STOCK_QUANTITY = 0,
     IM.OPENING_GENERATION_TIME = X.MIN_OPENING_GENERATION_TIME,
     IM.MISSING_OPENING_EVENT = "Y"
 WHERE
	X.UNIT_ID = IM.UNIT_ID AND IM.OPENING_DAY_CLOSE_EVENT_ID IS NULL;

 -- NOW SETTING THE OPENING EVENT AS MINIMUM EVENT FOUND FOR THAT UNIT AND CALCULATING FROM THERE AND SETTING OPENING AS 0


    UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM,
    (
    SELECT
    UNIT_ID,
    MAX(CLOSING_DAY_CLOSE_EVENT_ID) MAX_CLOSING_DAY_CLOSE_EVENT_ID,
    MAX(CLOSING_GENERATION_TIME) MAX_CLOSING_GENERATION_TIME
    FROM
    KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP
    GROUP BY UNIT_ID
    ) X
 SET
     IM.CLOSING_DAY_CLOSE_EVENT_ID = X.MAX_CLOSING_DAY_CLOSE_EVENT_ID,
     IM.CLOSING_GENERATION_TIME = X.MAX_CLOSING_GENERATION_TIME,
     IM.CLOSING_STOCK_QUANTITY = 0,
     IM.MISSING_CLOSING_EVENT = "Y"
 WHERE
	X.UNIT_ID = IM.UNIT_ID AND IM.CLOSING_DAY_CLOSE_EVENT_ID IS NULL;

-- SETTING THE TAX RATE(IGST)

UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM,
    (SELECT
        SKU_ID, COALESCE(IGST_TAX_RATE, 0) IGST_TAX_RATE
    FROM
        KETTLE_SCM_DUMP.SKU_DEFINITION SD
    INNER JOIN KETTLE_SCM_DUMP.PRODUCT_DEFINITION PD ON SD.LINKED_PRODUCT_ID = PD.PRODUCT_ID
    INNER JOIN KETTLE_MASTER_DUMP.TAX_CATEGORY_DATA TCD ON PD.TAX_CATEGORY_CODE = TCD.CATEGORY_CODE
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL UD ON UD.UNIT_ID = CURRENT_UNIT_ID
    INNER JOIN KETTLE_MASTER_DUMP.LOCATION_DETAIL LD ON UD.LOCATION_DETAIL_ID = LD.LOCATION_ID
    INNER JOIN KETTLE_MASTER_DUMP.CATEGORY_TAX_DATA CTD ON CTD.CATEGORY_ID = TCD.TAX_CATEGORY_DATA_ID
        AND CTD.STATE_DETAIL_ID = LD.STATE_DETAIL_ID
    GROUP BY SKU_ID) X
SET
    IM.TAX_RATE = X.IGST_TAX_RATE
WHERE
    X.SKU_ID = IM.SKU_ID;

-- GR DATA
UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM,
    (select a.*
    from (
        select
            GR.GENERATED_FOR_UNIT_ID,
            GRI.SKU_ID,
            AVG(GRI.UNIT_PRICE) GOOD_RECEIVED_PRICE,
            SUM(GRI.RECEIVED_QUANTITY) GOOD_RECEIVED_QUANTITY,
            SUM(GRI.RECEIVED_QUANTITY * GRI.UNIT_PRICE) GOOD_RECEIVED_VALUE_WITHOUT_TAX,
            SUM((GRI.RECEIVED_QUANTITY * GRI.UNIT_PRICE) + coalesce(GRI.TAX_AMOUNT, 0)) GOOD_RECEIVED_VALUE_WITH_TAX,
            SUM(GRI.TAX_AMOUNT) GOOD_RECEIVED_TAX
        from
            KETTLE_SCM_ARCHIVE.GOODS_RECEIVED_WH GR
        INNER JOIN
            KETTLE_SCM_ARCHIVE.GOODS_RECEIVED_ITEM_WH GRI ON GRI.GOODS_RECEIVED_ID = GR.GOODS_RECEIVED_ID AND GR.GOODS_RECEIVED_STATUS = "SETTLED"
        INNER JOIN
            KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM ON GR.GENERATED_FOR_UNIT_ID=IM.UNIT_ID and IM.SKU_ID=GRI.SKU_ID
        where
			LAST_UPDATE_TIME >IM.OPENING_GENERATION_TIME AND LAST_UPDATE_TIME<= IM.CLOSING_GENERATION_TIME
        GROUP BY GR.GENERATED_FOR_UNIT_ID , GRI.SKU_ID) a) X
SET
    IM.GOOD_RECEIVED_QUANTITY = ROUND(X.GOOD_RECEIVED_QUANTITY,9),
    IM.GOOD_RECEIVED_PRICE = ROUND(X.GOOD_RECEIVED_PRICE,9),
    IM.GOOD_RECEIVED_TAX = ROUND(X.GOOD_RECEIVED_TAX,9),
    IM.GOOD_RECEIVED_VALUE_WITHOUT_TAX = ROUND(X.GOOD_RECEIVED_VALUE_WITHOUT_TAX,9),
    IM.GOOD_RECEIVED_VALUE_WITH_TAX = ROUND(X.GOOD_RECEIVED_VALUE_WITH_TAX,9)
WHERE
    X.SKU_ID = IM.SKU_ID
        AND X.GENERATED_FOR_UNIT_ID = IM.UNIT_ID;

-- TO DATA

UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM,
    (SELECT
        a.*
    FROM
        (SELECT
        TR.GENERATION_UNIT_ID,
            TOI.SKU_ID,
            AVG(TOI.UNIT_PRICE) TRANSFERRED_PRICE,
            SUM(TOI.TRANSFERRED_QUANTITY) TRANSFERRED_QUANTITY,
            SUM((TOI.TRANSFERRED_QUANTITY * TOI.UNIT_PRICE)) TRANSFERRED_VALUE_WITHOUT_TAX,
            SUM((TOI.TRANSFERRED_QUANTITY * TOI.UNIT_PRICE) + coalesce(TOI.TOTAL_TAX,0)) TRANSFERRED_VALUE_WITH_TAX,
            SUM(TOI.TOTAL_TAX) TRANSFERRED_TAX
    FROM
        KETTLE_SCM_ARCHIVE.TRANSFER_ORDER_WH TR
    INNER JOIN KETTLE_SCM_ARCHIVE.TRANSFER_ORDER_ITEM_WH TOI ON TOI.TRANSFER_ORDER_ID = TR.TRANSFER_ORDER_ID AND
		TR.TRANSFER_ORDER_STATUS IN ('CREATED','SETTLED','TRANSFERRED') AND TR.TO_TYPE = "REGULAR_TRANSFER"
    INNER JOIN KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM ON TR.GENERATION_UNIT_ID = IM.UNIT_ID
        AND IM.SKU_ID = TOI.SKU_ID
    WHERE
        TR.GENERATION_TIME > IM.OPENING_GENERATION_TIME AND TR.GENERATION_TIME <= IM.CLOSING_GENERATION_TIME
    GROUP BY TR.GENERATION_UNIT_ID , TOI.SKU_ID) a) X
SET
    IM.TRANSFERRED_QUANTITY = ROUND(X.TRANSFERRED_QUANTITY,9),
    IM.TRANSFERRED_PRICE = ROUND(X.TRANSFERRED_PRICE,9),
    IM.TRANSFERRED_TAX = ROUND(X.TRANSFERRED_TAX,9),
    IM.TRANSFERRED_VALUE_WITHOUT_TAX = ROUND(X.TRANSFERRED_VALUE_WITHOUT_TAX,9),
    IM.TRANSFERRED_VALUE_WITH_TAX = ROUND(X.TRANSFERRED_VALUE_WITH_TAX,9)
WHERE
X.SKU_ID = IM.SKU_ID
AND X.GENERATION_UNIT_ID = IM.UNIT_ID;


-- VENDOR GR
UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM,
    (select a.*
    from (
		SELECT
			VGR.DELIVERY_UNIT_ID,
            VGRI.SKU_ID,
            AVG(VGRI.UNIT_PRICE) VENDOR_GOOD_RECEIVED_PRICE,
            SUM(VGRI.RECEIVED_QUANTITY) VENDOR_GOOD_RECEIVED_QUANTITY,
            SUM(VGRI.TOTAL_TAX) VENDOR_GOOD_RECEIVED_TAX,
            SUM(VGRI.TOTAL_PRICE) VENDOR_GOOD_RECEIVED_VALUE_WITHOUT_TAX,
            SUM(VGRI.TOTAL_AMOUNT) VENDOR_GOOD_RECEIVED_VALUE_WITH_TAX
		FROM KETTLE_SCM_ARCHIVE.VENDOR_GOODS_RECEIVED_DATA_WH VGR
		INNER JOIN KETTLE_SCM_ARCHIVE.VENDOR_GR_ITEM_DETAIL_WH VGRI ON VGR.GOODS_RECEIVED_ID = VGRI.VENDOR_GR_ID AND GOODS_RECEIVED_STATUS IN ("CREATED", "INITIATED")
        INNER JOIN
            KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM ON VGR.DELIVERY_UNIT_ID=IM.UNIT_ID and IM.SKU_ID=VGRI.SKU_ID
        where
        VGR.CREATED_AT > IM.OPENING_GENERATION_TIME AND VGR.CREATED_AT<= IM.CLOSING_GENERATION_TIME
        GROUP BY VGR.DELIVERY_UNIT_ID , VGRI.SKU_ID
        ) a) X
SET
    IM.VENDOR_GOOD_RECEIVED_QUANTITY = ROUND(X.VENDOR_GOOD_RECEIVED_QUANTITY,9),
    IM.VENDOR_GOOD_RECEIVED_PRICE = ROUND(X.VENDOR_GOOD_RECEIVED_PRICE,9),
    IM.VENDOR_GOOD_RECEIVED_VALUE_WITHOUT_TAX = ROUND(X.VENDOR_GOOD_RECEIVED_VALUE_WITHOUT_TAX,9),
    IM.VENDOR_GOOD_RECEIVED_VALUE_WITH_TAX = ROUND(X.VENDOR_GOOD_RECEIVED_VALUE_WITH_TAX,9),
    IM.VENDOR_GOOD_RECEIVED_TAX = ROUND(X.VENDOR_GOOD_RECEIVED_TAX,9)
WHERE
    X.SKU_ID = IM.SKU_ID
        AND X.DELIVERY_UNIT_ID = IM.UNIT_ID;


-- wastage
UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM,
    (SELECT
		WID.SKU_ID,
        WE.UNIT_ID,
		AVG(WID.PRICE) WASTAGE_PRICE,
		SUM(WID.QUANTITY) WASTAGE_QUANTITY,
        SUM(WID.QUANTITY*PRICE)  WASTAGE_VALUE_WITHOUT_TAX,
        SUM(WID.QUANTITY*PRICE + coalesce(WID.TAX,0))  WASTAGE_VALUE_WITH_TAX,
        SUM(WID.TAX)  WASTAGE_TAX
    FROM
		KETTLE_SCM_ARCHIVE.WASTAGE_EVENT_WH WE
	INNER JOIN
		KETTLE_SCM_ARCHIVE.WASTAGE_ITEM_DATA_WH WID ON WE.WASTAGE_ID=WID.WASTAGE_ID AND WE.STATUS = "SETTLED"
	INNER JOIN
		KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM ON IM.UNIT_ID=WE.UNIT_ID and IM.SKU_ID=WID.SKU_ID
	where
        WE.GENERATION_TIME >IM.OPENING_GENERATION_TIME AND WE.GENERATION_TIME  <= IM.CLOSING_GENERATION_TIME
	GROUP BY WE.UNIT_ID, WID.SKU_ID) X
SET
    IM.WASTAGE_QUANTITY = ROUND(X.WASTAGE_QUANTITY,9),
    IM.WASTAGE_PRICE = ROUND(X.WASTAGE_PRICE,9),
    IM.WASTAGE_VALUE_WITHOUT_TAX = ROUND(X.WASTAGE_VALUE_WITHOUT_TAX,9),
    IM.WASTAGE_VALUE_WITH_TAX = ROUND(X.WASTAGE_VALUE_WITH_TAX,9),
    IM.WASTAGE_TAX = ROUND(X.WASTAGE_TAX,9)
WHERE
    X.SKU_ID = IM.SKU_ID
        AND X.UNIT_ID = IM.UNIT_ID;



-- GATEPASS OUT -> TRANSFER

UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM,
    (
    SELECT
    dce.SKU_ID,
    SI.SENDING_UNIT UNIT_ID,
    AVG(dce.PRICE) GATEPASS_PRICE,
    SUM(dce.QUANTITY) GATEPASS_QUANTITY,
    SUM(dce.TAX) GATEPASS_TAX,
    SUM(dce.COST) GATEPASS_VALUE_WITHOUT_TAX,
    SUM(dce.AMOUNT) GATEPASS_VALUE_WITH_TAX
FROM
    KETTLE_SCM_ARCHIVE.GATEPASS_DATA_WH SI
    inner join KETTLE_SCM_ARCHIVE.GATEPASS_ITEM_DATA_WH dce on SI.GATEPASS_ID = dce.GATEPASS_ID AND SI.STATUS <> "CANCELLED" AND dce.TRANSACTION_TYPE = "TRANSFER"
    inner join KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IMR ON IMR.UNIT_ID=SI.SENDING_UNIT and IMR.SKU_ID=dce.SKU_ID
WHERE
	SI.CREATED_AT  > IMR.OPENING_GENERATION_TIME AND  SI.CREATED_AT <= IMR.CLOSING_GENERATION_TIME
	group by SI.SENDING_UNIT,dce.SKU_ID
    ) X
 SET
     IM.GATEPASS_PRICE = ROUND(X.GATEPASS_PRICE,9),
     IM.GATEPASS_QUANTITY= ROUND(X.GATEPASS_QUANTITY,9),
     IM.GATEPASS_TAX= ROUND(X.GATEPASS_TAX,9),
     IM.GATEPASS_VALUE_WITHOUT_TAX= ROUND(X.GATEPASS_VALUE_WITHOUT_TAX,9),
     IM.GATEPASS_VALUE_WITH_TAX= ROUND(X.GATEPASS_VALUE_WITH_TAX,9)
 WHERE
     X.SKU_ID = IM.SKU_ID
	 AND X.UNIT_ID = IM.UNIT_ID;



-- GATAPASS IN -> RETURN


UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM,
    (
    SELECT
    dce.SKU_ID,
    SI.SENDING_UNIT UNIT_ID,
    AVG(dce.PRICE) GATEPASS_RETURN_PRICE,
    SUM(dce.QUANTITY) GATEPASS_RETURN_QUANTITY,
    SUM(dce.TAX) GATEPASS_RETURN_TAX,
    SUM(dce.COST) GATEPASS_RETURN_VALUE_WITHOUT_TAX,
    SUM(dce.AMOUNT) GATEPASS_RETURN_VALUE_WITH_TAX
FROM
    KETTLE_SCM_ARCHIVE.GATEPASS_DATA_WH SI
    inner join KETTLE_SCM_ARCHIVE.GATEPASS_ITEM_DATA_WH dce on SI.GATEPASS_ID = dce.GATEPASS_ID AND SI.STATUS <> "CANCELLED" AND dce.TRANSACTION_TYPE = "RETURN"
    inner join KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IMR ON IMR.UNIT_ID=SI.SENDING_UNIT and IMR.SKU_ID=dce.SKU_ID
WHERE
	SI.CREATED_AT  > IMR.OPENING_GENERATION_TIME AND  SI.CREATED_AT <= IMR.CLOSING_GENERATION_TIME
	group by SI.SENDING_UNIT,dce.SKU_ID
    ) X
 SET
     IM.GATEPASS_RETURN_PRICE = ROUND(X.GATEPASS_RETURN_PRICE,9),
     IM.GATEPASS_RETURN_QUANTITY= ROUND(X.GATEPASS_RETURN_QUANTITY,9),
     IM.GATEPASS_RETURN_TAX= ROUND(X.GATEPASS_RETURN_TAX,9),
     IM.GATEPASS_RETURN_VALUE_WITHOUT_TAX= ROUND(X.GATEPASS_RETURN_VALUE_WITHOUT_TAX,9),
     IM.GATEPASS_RETURN_VALUE_WITH_TAX= ROUND(X.GATEPASS_RETURN_VALUE_WITH_TAX,9)
 WHERE
     X.SKU_ID = IM.SKU_ID
	 AND X.UNIT_ID = IM.UNIT_ID;

-- B2B SALES AND B2B RETURNS, ECOM, SCRAP,AND RETURN TO VENDOR

UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM,
    (select
			SPII.SKU_ID,
			SPI.SENDING_UNIT_ID,
            AVG(SPII.SELLING_PRICE) B2B_SALE_PRICE,
			SUM(SPII.QTY) B2B_SALE_QUANTITY,
			SUM(SPII.TOTAL_TAX) B2B_SALE_TAX,
			SUM(SPII.SELLING_AMOUNT) B2B_SALE_VALUE_WITHOUT_TAX,
			SUM(SPII.TOTAL_TAX) + SUM(SPII.SELLING_AMOUNT) B2B_SALE_VALUE_WITH_TAX
         from KETTLE_SCM_ARCHIVE.SALES_PERFORMA_INVOICE_WH SPI
         inner join KETTLE_SCM_ARCHIVE.SALES_PERFORMA_INVOICE_ITEM_WH SPII on SPI.INVOICE_ID = SPII.INVOICE_ID AND SPI.TYPE="B2B_SALES" AND SPI.INVOICE_STATUS IN ("CLOSED", "DELIVERED", "CORRECTION_APPROVAL", "DELIVERED_WITH_CORRECTION")
         INNER JOIN
            KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IMR ON SPI.SENDING_UNIT_ID=IMR.UNIT_ID and IMR.SKU_ID=SPII.SKU_ID
         where SPI.CREATED_AT  > IMR.OPENING_GENERATION_TIME AND  SPI.CREATED_AT <= IMR.CLOSING_GENERATION_TIME
          group by SPI.SENDING_UNIT_ID, SPII.SKU_ID
    ) X
 SET
     IM.B2B_SALE_QUANTITY = ROUND(X.B2B_SALE_QUANTITY,9),
     IM.B2B_SALE_PRICE = ROUND(X.B2B_SALE_PRICE,9),
     IM.B2B_SALE_TAX = ROUND(X.B2B_SALE_TAX,9),
     IM.B2B_SALE_VALUE_WITHOUT_TAX = ROUND(X.B2B_SALE_VALUE_WITHOUT_TAX,9),
     IM.B2B_SALE_VALUE_WITH_TAX = ROUND(X.B2B_SALE_VALUE_WITH_TAX,9)
 WHERE
     X.SKU_ID = IM.SKU_ID
         AND X.SENDING_UNIT_ID = IM.UNIT_ID;

-- B2B RETURN
UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM,
    (select
			SPII.SKU_ID,
			SPI.SENDING_UNIT_ID,
			AVG(SPII.SELLING_PRICE) B2B_RETURN_PRICE,
			SUM(SPII.QTY) B2B_RETURN_QUANTITY,
			SUM(SPII.TOTAL_TAX) B2B_RETURN_TAX,
			SUM(SPII.SELLING_AMOUNT) B2B_RETURN_VALUE_WITHOUT_TAX,
			SUM(SPII.TOTAL_TAX) + SUM(SPII.SELLING_AMOUNT) B2B_RETURN_VALUE_WITH_TAX
         from KETTLE_SCM_ARCHIVE.SALES_PERFORMA_INVOICE_WH SPI
         inner join KETTLE_SCM_ARCHIVE.SALES_PERFORMA_INVOICE_ITEM_WH SPII on SPI.INVOICE_ID = SPII.INVOICE_ID AND SPI.TYPE="B2B_RETURN" AND SPI.INVOICE_STATUS IN ("CLOSED", "DELIVERED", "CORRECTION_APPROVAL", "DELIVERED_WITH_CORRECTION")
         INNER JOIN
            KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IMR ON SPI.SENDING_UNIT_ID=IMR.UNIT_ID and IMR.SKU_ID=SPII.SKU_ID
         where SPI.CREATED_AT  > IMR.OPENING_GENERATION_TIME AND  SPI.CREATED_AT <= IMR.CLOSING_GENERATION_TIME
          group by SPI.SENDING_UNIT_ID, SPII.SKU_ID
    ) X
 SET
     IM.B2B_RETURN_QUANTITY = ROUND(X.B2B_RETURN_QUANTITY,9),
     IM.B2B_RETURN_PRICE = ROUND(X.B2B_RETURN_PRICE,9),
     IM.B2B_RETURN_TAX = ROUND(X.B2B_RETURN_TAX,9),
     IM.B2B_RETURN_VALUE_WITHOUT_TAX = ROUND(X.B2B_RETURN_VALUE_WITHOUT_TAX,9),
     IM.B2B_RETURN_VALUE_WITH_TAX = ROUND(X.B2B_RETURN_VALUE_WITH_TAX,9)
 WHERE
     X.SKU_ID = IM.SKU_ID
         AND X.SENDING_UNIT_ID = IM.UNIT_ID;


-- ECOM
UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM,
    (select
			SPII.SKU_ID,
			SPI.SENDING_UNIT_ID,
			AVG(SPII.SELLING_PRICE) ECOM_PRICE,
			SUM(SPII.QTY) ECOM_QUANTITY,
			SUM(SPII.TOTAL_TAX) ECOM_TAX,
			SUM(SPII.SELLING_AMOUNT) ECOM_VALUE_WITHOUT_TAX,
			SUM(SPII.TOTAL_TAX) + SUM(SPII.SELLING_AMOUNT) ECOM_VALUE_WITH_TAX
         from KETTLE_SCM_ARCHIVE.SALES_PERFORMA_INVOICE_WH SPI
         inner join KETTLE_SCM_ARCHIVE.SALES_PERFORMA_INVOICE_ITEM_WH SPII on SPI.INVOICE_ID = SPII.INVOICE_ID AND SPI.TYPE="ECOM" AND SPI.INVOICE_STATUS IN ("CLOSED", "DELIVERED", "CORRECTION_APPROVAL", "DELIVERED_WITH_CORRECTION")
         INNER JOIN
            KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IMR ON SPI.SENDING_UNIT_ID=IMR.UNIT_ID and IMR.SKU_ID=SPII.SKU_ID
         where SPI.CREATED_AT  > IMR.OPENING_GENERATION_TIME AND  SPI.CREATED_AT <= IMR.CLOSING_GENERATION_TIME
          group by SPI.SENDING_UNIT_ID, SPII.SKU_ID
    ) X
 SET
     IM.ECOM_PRICE = ROUND(X.ECOM_PRICE,9),
     IM.ECOM_QUANTITY = ROUND(X.ECOM_QUANTITY,9),
     IM.ECOM_TAX = ROUND(X.ECOM_TAX,9),
     IM.ECOM_VALUE_WITHOUT_TAX = ROUND(X.ECOM_VALUE_WITHOUT_TAX,9),
     IM.ECOM_VALUE_WITH_TAX = ROUND(X.ECOM_VALUE_WITH_TAX,9)
 WHERE
     X.SKU_ID = IM.SKU_ID
	 AND X.SENDING_UNIT_ID = IM.UNIT_ID;


-- RETURN_TO_VENDOR
UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM,
    (select
			SPII.SKU_ID,
			SPI.SENDING_UNIT_ID,
			AVG(SPII.SELLING_PRICE) RETURN_TO_VENDOR_PRICE,
			SUM(SPII.QTY) RETURN_TO_VENDOR_QUANTITY,
			SUM(SPII.TOTAL_TAX) RETURN_TO_VENDOR_TAX,
			SUM(SPII.SELLING_AMOUNT) RETURN_TO_VENDOR_VALUE_WITHOUT_TAX,
			SUM(SPII.TOTAL_TAX) + SUM(SPII.SELLING_AMOUNT) RETURN_TO_VENDOR_VALUE_WITH_TAX
         from KETTLE_SCM_ARCHIVE.SALES_PERFORMA_INVOICE_WH SPI
         inner join KETTLE_SCM_ARCHIVE.SALES_PERFORMA_INVOICE_ITEM_WH SPII on SPI.INVOICE_ID = SPII.INVOICE_ID AND SPI.TYPE="RETURN_TO_VENDOR" AND SPI.INVOICE_STATUS IN ("CLOSED", "DELIVERED", "CORRECTION_APPROVAL", "DELIVERED_WITH_CORRECTION")
         INNER JOIN
            KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IMR ON SPI.SENDING_UNIT_ID=IMR.UNIT_ID and IMR.SKU_ID=SPII.SKU_ID
         where SPI.CREATED_AT  > IMR.OPENING_GENERATION_TIME AND  SPI.CREATED_AT <= IMR.CLOSING_GENERATION_TIME
          group by SPI.SENDING_UNIT_ID, SPII.SKU_ID
    ) X
 SET
     IM.RETURN_TO_VENDOR_PRICE = ROUND(X.RETURN_TO_VENDOR_PRICE,9),
     IM.RETURN_TO_VENDOR_QUANTITY = ROUND(X.RETURN_TO_VENDOR_QUANTITY,9),
     IM.RETURN_TO_VENDOR_TAX = ROUND(X.RETURN_TO_VENDOR_TAX,9),
     IM.RETURN_TO_VENDOR_VALUE_WITHOUT_TAX = ROUND(X.RETURN_TO_VENDOR_VALUE_WITHOUT_TAX,9),
     IM.RETURN_TO_VENDOR_VALUE_WITH_TAX = ROUND(X.RETURN_TO_VENDOR_VALUE_WITH_TAX,9)
 WHERE
     X.SKU_ID = IM.SKU_ID
	 AND X.SENDING_UNIT_ID = IM.UNIT_ID;


-- SCRAP
UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM,
    (select
			SPII.SKU_ID,
			SPI.SENDING_UNIT_ID,
			AVG(SPII.SELLING_PRICE) SCRAP_PRICE,
			SUM(SPII.QTY) SCRAP_QUANTITY,
			SUM(SPII.TOTAL_TAX) SCRAP_TAX,
			SUM(SPII.SELLING_AMOUNT) SCRAP_VALUE_WITHOUT_TAX,
			SUM(SPII.TOTAL_TAX) + SUM(SPII.SELLING_AMOUNT) SCRAP_VALUE_WITH_TAX
         from KETTLE_SCM_ARCHIVE.SALES_PERFORMA_INVOICE_WH SPI
         inner join KETTLE_SCM_ARCHIVE.SALES_PERFORMA_INVOICE_ITEM_WH SPII on SPI.INVOICE_ID = SPII.INVOICE_ID AND SPI.TYPE="SCRAP" AND SPI.INVOICE_STATUS IN ("CLOSED", "DELIVERED", "CORRECTION_APPROVAL", "DELIVERED_WITH_CORRECTION")
         INNER JOIN
            KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IMR ON SPI.SENDING_UNIT_ID=IMR.UNIT_ID and IMR.SKU_ID=SPII.SKU_ID
         where SPI.CREATED_AT  > IMR.OPENING_GENERATION_TIME AND  SPI.CREATED_AT <= IMR.CLOSING_GENERATION_TIME
          group by SPI.SENDING_UNIT_ID, SPII.SKU_ID
    ) X
 SET
     IM.SCRAP_PRICE = ROUND(X.SCRAP_PRICE,9),
     IM.SCRAP_QUANTITY = ROUND(X.SCRAP_QUANTITY,9),
     IM.SCRAP_TAX = ROUND(X.SCRAP_TAX,9),
     IM.SCRAP_VALUE_WITHOUT_TAX = ROUND(X.SCRAP_VALUE_WITHOUT_TAX,9),
     IM.SCRAP_VALUE_WITH_TAX = ROUND(X.SCRAP_VALUE_WITH_TAX,9)
 WHERE
     X.SKU_ID = IM.SKU_ID
	 AND X.SENDING_UNIT_ID = IM.UNIT_ID;



-- DOING FOR PRODUCTION BOOKING

-- FIRST SETTING THE BOOKING QUANTITY IN TIME FRAME
UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM,
    (select
			PB.SKU_ID,
			PB.UNIT_ID,
            AVG(PB.UNIT_PRICE) PRODUCTION_BOOKING_PRICE,
			SUM(PB.QUANTITY) PRODUCTION_BOOKING_QUANTITY,
			SUM(PB.TOTAL_COST) PRODUCTION_BOOKING_VALUE_WITHOUT_TAX
         from KETTLE_SCM_ARCHIVE.PRODUCTION_BOOKING PB
         INNER JOIN
            KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IMR ON PB.UNIT_ID=IMR.UNIT_ID and PB.SKU_ID=IMR.SKU_ID AND PB.BOOKING_STATUS="CREATED"
         where PB.GENERATION_TIME  > IMR.OPENING_GENERATION_TIME AND  PB.GENERATION_TIME <= IMR.CLOSING_GENERATION_TIME
          group by PB.UNIT_ID, PB.SKU_ID
    ) X
 SET
     IM.PRODUCTION_BOOKING_PRICE = ROUND(X.PRODUCTION_BOOKING_PRICE,9),
     IM.PRODUCTION_BOOKING_QUANTITY = ROUND(X.PRODUCTION_BOOKING_QUANTITY,9),
     IM.PRODUCTION_BOOKING_VALUE_WITHOUT_TAX = ROUND(X.PRODUCTION_BOOKING_VALUE_WITHOUT_TAX,9),
     IM.PRODUCTION_BOOKING_VALUE_WITH_TAX = ROUND(X.PRODUCTION_BOOKING_VALUE_WITHOUT_TAX,9),
     IM.PRODUCTION_BOOKING_TAX = 0
 WHERE
     X.SKU_ID = IM.SKU_ID
         AND X.UNIT_ID = IM.UNIT_ID;

UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM,
    (select
			BC.SKU_ID,
			PB.UNIT_ID,
            AVG(BC.UNIT_PRICE) BOOKING_CONSUMPTION_PRICE,
			SUM(BC.CALCULATED_QUANTITY) BOOKING_CONSUMPTION_QUANTITY,
			SUM(BC.TOTAL_COST) BOOKING_CONSUMPTION_VALUE_WITHOUT_TAX
         from KETTLE_SCM_ARCHIVE.PRODUCTION_BOOKING PB
         inner join KETTLE_SCM_ARCHIVE.BOOKING_CONSUMPTION BC on BC.PRODUCTION_BOOK_ID = PB.BOOKING_ID AND PB.BOOKING_STATUS="CREATED"
         INNER JOIN
            KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IMR ON PB.UNIT_ID=IMR.UNIT_ID and BC.SKU_ID=IMR.SKU_ID
         where PB.GENERATION_TIME  > IMR.OPENING_GENERATION_TIME AND  PB.GENERATION_TIME <= IMR.CLOSING_GENERATION_TIME
          group by PB.UNIT_ID, BC.SKU_ID
    ) X
 SET
     IM.BOOKING_CONSUMPTION_QUANTITY = ROUND(X.BOOKING_CONSUMPTION_QUANTITY,9),
     IM.BOOKING_CONSUMPTION_PRICE = ROUND(X.BOOKING_CONSUMPTION_PRICE,9),
     IM.BOOKING_CONSUMPTION_VALUE_WITHOUT_TAX = ROUND(X.BOOKING_CONSUMPTION_VALUE_WITHOUT_TAX,9),
     IM.BOOKING_CONSUMPTION_VALUE_WITH_TAX = ROUND(X.BOOKING_CONSUMPTION_VALUE_WITHOUT_TAX,9),
     IM.BOOKING_CONSUMPTION_TAX = 0
 WHERE
     X.SKU_ID = IM.SKU_ID
         AND X.UNIT_ID = IM.UNIT_ID;


-- SAME LIKE CALCULATING THE REVERSE BOOKINGS
-- FIRST SETTING THE BOOKING QUANTITY IN TIME FRAME
UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM,
    (select
			PB.SKU_ID,
			PB.UNIT_ID,
			AVG(PB.UNIT_PRICE) REVERSE_PRODUCTION_BOOKING_PRICE,
			SUM(PB.QUANTITY) REVERSE_PRODUCTION_BOOKING_QUANTITY,
			SUM(PB.TOTAL_COST) REVERSE_PRODUCTION_BOOKING_VALUE_WITHOUT_TAX
         from KETTLE_SCM_ARCHIVE.REVERSE_PRODUCTION_BOOKING PB
         INNER JOIN
            KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IMR ON PB.UNIT_ID=IMR.UNIT_ID and PB.SKU_ID=IMR.SKU_ID AND PB.BOOKING_STATUS="CREATED"
         where PB.GENERATION_TIME  > IMR.OPENING_GENERATION_TIME AND  PB.GENERATION_TIME <= IMR.CLOSING_GENERATION_TIME
          group by PB.UNIT_ID, PB.SKU_ID
    ) X
 SET
     IM.REVERSE_PRODUCTION_BOOKING_PRICE = ROUND(X.REVERSE_PRODUCTION_BOOKING_PRICE,9),
     IM.REVERSE_PRODUCTION_BOOKING_QUANTITY = ROUND(X.REVERSE_PRODUCTION_BOOKING_QUANTITY,9),
     IM.REVERSE_PRODUCTION_BOOKING_VALUE_WITHOUT_TAX = ROUND(X.REVERSE_PRODUCTION_BOOKING_VALUE_WITHOUT_TAX,9),
     IM.REVERSE_PRODUCTION_BOOKING_VALUE_WITH_TAX = ROUND(X.REVERSE_PRODUCTION_BOOKING_VALUE_WITHOUT_TAX,9),
     IM.REVERSE_PRODUCTION_BOOKING_TAX = 0
 WHERE
     X.SKU_ID = IM.SKU_ID
         AND X.UNIT_ID = IM.UNIT_ID;

UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM,
    (select
			BC.SKU_ID,
			PB.UNIT_ID,
			AVG(BC.UNIT_PRICE) REVERSE_BOOKING_CONSUMPTION_PRICE,
			SUM(BC.CALCULATED_QUANTITY) REVERSE_BOOKING_CONSUMPTION_QUANTITY,
			SUM(BC.TOTAL_COST) REVERSE_BOOKING_CONSUMPTION_VALUE_WITHOUT_TAX
         from KETTLE_SCM_ARCHIVE.REVERSE_PRODUCTION_BOOKING PB
         inner join KETTLE_SCM_ARCHIVE.REVERSE_BOOKING_CONSUMPTION BC on BC.PRODUCTION_BOOK_ID = PB.BOOKING_ID AND PB.BOOKING_STATUS="CREATED"
         INNER JOIN
            KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IMR ON PB.UNIT_ID=IMR.UNIT_ID and BC.SKU_ID=IMR.SKU_ID
         where PB.GENERATION_TIME  > IMR.OPENING_GENERATION_TIME AND  PB.GENERATION_TIME <= IMR.CLOSING_GENERATION_TIME
          group by PB.UNIT_ID, BC.SKU_ID
    ) X
 SET
     IM.REVERSE_BOOKING_CONSUMPTION_QUANTITY = ROUND(X.REVERSE_BOOKING_CONSUMPTION_QUANTITY,9),
     IM.REVERSE_BOOKING_CONSUMPTION_PRICE = ROUND(X.REVERSE_BOOKING_CONSUMPTION_PRICE,9),
     IM.REVERSE_BOOKING_CONSUMPTION_VALUE_WITHOUT_TAX = ROUND(X.REVERSE_BOOKING_CONSUMPTION_VALUE_WITHOUT_TAX,9),
     IM.REVERSE_BOOKING_CONSUMPTION_VALUE_WITH_TAX = ROUND(X.REVERSE_BOOKING_CONSUMPTION_VALUE_WITHOUT_TAX,9),
     IM.REVERSE_BOOKING_CONSUMPTION_TAX = 0
 WHERE
     X.SKU_ID = IM.SKU_ID
         AND X.UNIT_ID = IM.UNIT_ID;

-- SETTING THE VARIANCE
UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM,
    (
    SELECT
    SI.SKU_ID,
    dce.UNIT_ID,
    AVG(SI.PRICE) VARIANCE_PRICE,
    SUM(SI.VARIANCE) VARIANCE_QUANTITY,
    SUM(SI.VARIANCE_COST) VARIANCE_VALUE_WITHOUT_TAX
FROM
    KETTLE_SCM_ARCHIVE.INVENTORY_DRILLDOWN SI
    inner join KETTLE_SCM_ARCHIVE.DAY_CLOSE_EVENT_WH dce on SI.CLOSURE_EVENT_ID = dce.EVENT_ID AND dce.STATUS = 'CLOSED'
    inner join KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IMR ON IMR.UNIT_ID=dce.UNIT_ID and IMR.SKU_ID=SI.SKU_ID
 where dce.GENERATION_TIME > IMR.OPENING_GENERATION_TIME AND dce.GENERATION_TIME <= IMR.CLOSING_GENERATION_TIME
	group by dce.UNIT_ID,SI.SKU_ID
    ) X
 SET
     IM.VARIANCE_PRICE = ROUND(X.VARIANCE_PRICE,9),
     IM.VARIANCE_QUANTITY= ROUND(X.VARIANCE_QUANTITY,9),
     IM.VARIANCE_VALUE_WITHOUT_TAX= ROUND(X.VARIANCE_VALUE_WITHOUT_TAX,9),
     IM.VARIANCE_VALUE_WITH_TAX= ROUND(X.VARIANCE_VALUE_WITHOUT_TAX,9),
     IM.VARIANCE_TAX = 0
 WHERE
     X.SKU_ID = IM.SKU_ID
	 AND X.UNIT_ID = IM.UNIT_ID;


UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM
SET
    IM.OPENING_STOCK_TAX = (CASE
        WHEN TAX_RATE IS NULL OR TAX_RATE = 0 THEN 0
        ELSE ROUND(((TAX_RATE / 100) * (COALESCE(OPENING_STOCK_QUANTITY, 0) * COALESCE(OPENING_STOCK_PRICE, 0))),
                6)
    END),
    IM.CLOSING_STOCK_TAX = (CASE
        WHEN TAX_RATE IS NULL OR TAX_RATE = 0 THEN 0
        ELSE ROUND(((TAX_RATE / 100) * (COALESCE(CLOSING_STOCK_QUANTITY, 0) * COALESCE(CLOSING_STOCK_PRICE, 0))),
                6)
    END),
    IM.PRODUCTION_BOOKING_TAX = (CASE
        WHEN TAX_RATE IS NULL OR TAX_RATE = 0 THEN 0
        ELSE ROUND(((TAX_RATE / 100) * (COALESCE(PRODUCTION_BOOKING_QUANTITY, 0) * COALESCE(PRODUCTION_BOOKING_PRICE, 0))),
                6)
    END),
    IM.REVERSE_PRODUCTION_BOOKING_TAX = (CASE
        WHEN TAX_RATE IS NULL OR TAX_RATE = 0 THEN 0
        ELSE ROUND(((TAX_RATE / 100) * (COALESCE(REVERSE_PRODUCTION_BOOKING_QUANTITY, 0) * COALESCE(REVERSE_PRODUCTION_BOOKING_PRICE, 0))),
                6)
    END),
    IM.BOOKING_CONSUMPTION_TAX = (CASE
        WHEN TAX_RATE IS NULL OR TAX_RATE = 0 THEN 0
        ELSE ROUND(((TAX_RATE / 100) * (COALESCE(BOOKING_CONSUMPTION_QUANTITY, 0) * COALESCE(BOOKING_CONSUMPTION_PRICE, 0))),
                6)
    END),
    IM.REVERSE_BOOKING_CONSUMPTION_TAX = (CASE
        WHEN TAX_RATE IS NULL OR TAX_RATE = 0 THEN 0
        ELSE ROUND(((TAX_RATE / 100) * (COALESCE(REVERSE_BOOKING_CONSUMPTION_QUANTITY, 0) * COALESCE(REVERSE_BOOKING_CONSUMPTION_TAX, 0))),
                6)
    END);


-- NOW SETTING TAX VALUES WITH THE TAX STORED

UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM
SET IM.OPENING_STOCK_VALUE_WITH_TAX = ROUND((coalesce(OPENING_STOCK_TAX,0) + coalesce(OPENING_STOCK_VALUE_WITHOUT_TAX,0)),9),
 IM.CLOSING_STOCK_VALUE_WITH_TAX = ROUND((coalesce(CLOSING_STOCK_TAX,0) + coalesce(CLOSING_STOCK_VALUE_WITHOUT_TAX,0)),9),
 IM.PRODUCTION_BOOKING_VALUE_WITH_TAX = ROUND((coalesce(PRODUCTION_BOOKING_TAX,0) + coalesce(PRODUCTION_BOOKING_VALUE_WITHOUT_TAX,0)),9),
 IM.REVERSE_PRODUCTION_BOOKING_VALUE_WITH_TAX = ROUND((coalesce(REVERSE_PRODUCTION_BOOKING_TAX,0) + coalesce(REVERSE_PRODUCTION_BOOKING_VALUE_WITHOUT_TAX,0)),9),
 IM.BOOKING_CONSUMPTION_VALUE_WITH_TAX = ROUND((coalesce(BOOKING_CONSUMPTION_TAX,0) + coalesce(BOOKING_CONSUMPTION_VALUE_WITHOUT_TAX,0)),9),
 IM.REVERSE_BOOKING_CONSUMPTION_VALUE_WITH_TAX = ROUND((coalesce(REVERSE_BOOKING_CONSUMPTION_TAX,0) + coalesce(REVERSE_BOOKING_CONSUMPTION_TAX,0)),9);


 -- SETTING THE EXPECTED CLOSING STOCK


UPDATE KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP IM,
    (SELECT
        SKU_ID,
            UNIT_ID,
            (COALESCE(OPENING_STOCK_QUANTITY, 0) + COALESCE(GOOD_RECEIVED_QUANTITY, 0) + COALESCE(VENDOR_GOOD_RECEIVED_QUANTITY, 0)
            + COALESCE(PRODUCTION_BOOKING_QUANTITY, 0) + COALESCE(GATEPASS_RETURN_QUANTITY, 0) + COALESCE(B2B_RETURN_QUANTITY, 0)
            - COALESCE(TRANSFERRED_QUANTITY, 0) - COALESCE(WASTAGE_QUANTITY, 0) - COALESCE(BOOKING_CONSUMPTION_QUANTITY, 0)
            - COALESCE(REVERSE_PRODUCTION_BOOKING_QUANTITY, 0) - COALESCE(GATEPASS_QUANTITY, 0) + COALESCE(REVERSE_BOOKING_CONSUMPTION_QUANTITY, 0)
            - COALESCE(VARIANCE_QUANTITY, 0) - COALESCE(B2B_SALE_QUANTITY, 0) - COALESCE(ECOM_QUANTITY, 0) - COALESCE(SCRAP_QUANTITY, 0) - COALESCE(RETURN_TO_VENDOR_QUANTITY, 0)) EXPECTED_CLOSING_AFTER_CALCULATION
    FROM
        KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP) X
SET
    IM.EXPECTED_CLOSING_STOCK_AFTER_VARIANCE = X.EXPECTED_CLOSING_AFTER_CALCULATION
WHERE
    X.SKU_ID = IM.SKU_ID
        AND X.UNIT_ID = IM.UNIT_ID;

INSERT INTO KETTLE_SCM_ARCHIVE.KITCHEN_WH_INVENTORY_MOVEMENT_REPORT_DUMP
SELECT * FROM KETTLE_SCM_ARCHIVE.INVENTORY_MOVEMENT_WH_REPORT_DUMP;

    END LOOP;

    CLOSE cursor_units;
END $$

DELIMITER $$;

CALL KETTLE_SCM_ARCHIVE.KITCHEN_WH_INVENTORY_MOVEMENT_REPORT_DUMP_PROCEDURE();
ALTER TABLE KETTLE_SCM_DEV.SERVICE_ORDER
ADD COLUMN ACCOUNTED_FOR_IN_PNL VARCHAR(10) NULL;

ALTER TABLE KETTLE_SCM_DEV.SERVICE_RECEIVED_DATA
ADD COLUMN BUSINESS_DATE DATE NULL;


UPDATE `KETTLE_SCM_DEV`.`LIST_TYPE` SET `BUDGET_CATEGORY` = 'INSURANCE_CGL' WHERE (`LIST_TYPE_ID` = '29');
UPDATE `KETTLE_SCM_DEV`.`LIST_TYPE` SET `BUDGET_CATEGORY` = 'CORPORATE_MARKETING_ATL_DIGITAL' WHERE (`LIST_TYPE_ID` = '42');
UPDATE `KETTLE_SCM_DEV`.`LIST_TYPE` SET `BUDGET_CATEGORY` = 'CORPORATE_MARKETING_AD_OFFLINE' WHERE (`LIST_TYPE_ID` = '43');
UPDATE `KETTLE_SCM_DEV`.`LIST_TYPE` SET `BUDGET_CATEGORY` = 'CORPORATE_MARKETING_AD_OFFLINE' WHERE (`LIST_TYPE_ID` = '48');
UPDATE `KETTLE_SCM_DEV`.`LIST_TYPE` SET `BUDGET_CATEGORY` = 'CORPORATE_MARKETING_ADV_ONLINE' WHERE (`LIST_TYPE_ID` = '49');
UPDATE `KETTLE_SCM_DEV`.`LIST_TYPE` SET `BUDGET_CATEGORY` = 'CORPORATE_MARKETING_ATL_PRINT_AD' WHERE (`LIST_TYPE_ID` = '53');
UPDATE `KETTLE_SCM_DEV`.`LIST_TYPE` SET `BUDGET_CATEGORY` = 'AIR_CONDITIONER_AMC' WHERE (`LIST_TYPE_ID` = '61');
UPDATE `KETTLE_SCM_DEV`.`LIST_TYPE` SET `BUDGET_CATEGORY` = 'COMMUNICATION_ILL' WHERE (`LIST_TYPE_ID` = '73');
UPDATE `KETTLE_SCM_DEV`.`LIST_TYPE` SET `BUDGET_CATEGORY` = 'COMMUNICATION_INTERNET' WHERE (`LIST_TYPE_ID` = '74');
UPDATE `KETTLE_SCM_DEV`.`LIST_TYPE` SET `BUDGET_CATEGORY` = 'COMMUNICATION_TELEPHONE' WHERE (`LIST_TYPE_ID` = '75');
UPDATE `KETTLE_SCM_DEV`.`LIST_TYPE` SET `BUDGET_CATEGORY` = 'CORPORATE_MARKETING_AD_OFFLINE' WHERE (`LIST_TYPE_ID` = '80');
UPDATE `KETTLE_SCM_DEV`.`LIST_TYPE` SET `BUDGET_CATEGORY` = 'LOGISTIC_INTERSTATE_ROAD' WHERE (`LIST_TYPE_ID` = '82');
UPDATE `KETTLE_SCM_DEV`.`LIST_TYPE` SET `BUDGET_CATEGORY` = 'OTHERS_MAINTENANCE' WHERE (`LIST_TYPE_ID` = '86');
UPDATE `KETTLE_SCM_DEV`.`LIST_TYPE` SET `BUDGET_CATEGORY` = 'CAPITAL_IMPROVEMENT_EXPENSES' WHERE (`LIST_TYPE_ID` = '97');
UPDATE `KETTLE_SCM_DEV`.`LIST_TYPE` SET `BUDGET_CATEGORY` = 'STAFF_WELFARE_EXPENSES' WHERE (`LIST_TYPE_ID` = '99');
UPDATE `KETTLE_SCM_DEV`.`LIST_TYPE` SET `BUDGET_CATEGORY` = 'LICENSE_EXPENSES' WHERE (`LIST_TYPE_ID` = '102');

UPDATE `KETTLE_SCM_DEV`.`LIST_TYPE` SET `STATUS` = 'IN_ACTIVE'  WHERE `BUDGET_CATEGORY` ='NONE';



UPDATE KETTLE_SCM_DEV.SERVICE_RECEIVED_ITEM sri
INNER JOIN KETTLE_SCM_DEV.COST_ELEMENT_DATA ced ON sri.COST_ELEMENT_ID=ced.COST_ELEMENT_ID
INNER JOIN KETTLE_SCM_DEV.LIST_TYPE ld ON ced.SUB_CATEGORY=ld.LIST_TYPE_ID
SET sri.BUDGET_CATEGORY=ld.BUDGET_CATEGORY;

UPDATE KETTLE_SCM_DEV.SERVICE_RECEIVED_DATA
SET BUSINESS_DATE=	(CASE
    WHEN HOUR(CREATED_AT) <= 5 THEN SUBDATE(DATE(CREATED_AT), 1)
    ELSE DATE(CREATED_AT)
END);

INSERT INTO `KETTLE_MASTER_DEV`.`PRE_AUTHENTICATED_API` (`API`, `STATUS`) VALUES ('kettle-service.pos-metadata.unit-kettleDayClose', 'ACTIVE');
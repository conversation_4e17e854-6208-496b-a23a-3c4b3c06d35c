ALTER TABLE KETTLE_SCM_DEV.LIST_TYPE
    ADD COLUMN BUDGET_CATEGORY VARCHAR(50);

ALTER TABLE KETTLE_SCM_DEV.ESTIMATE_QUERY_REQUEST
    ADD COLUMN ORDERING_TYPE VARCHAR(100);
ALTER TABLE KETTLE_SCM_DEV.ESTIMATE_QUERY_REQUEST
    ADD COLUMN SUGGESTED_SALE DECIMAL(10, 2);
ALTER TABLE KETTLE_SCM_DEV.ESTIMATE_QUERY_REQUEST
    ADD COLUMN GENERATION_DATE DATE;


USE `KETTLE_SCM_DEV`;
DROP procedure IF EXISTS `PROC_ESTIMATE_REQUEST_DATA`;

DELIMITER $$
USE `KETTLE_SCM_DEV`$$
CREATE PROCEDURE `PROC_ESTIMATE_REQUEST_DATA`(IN_BRAND_ID INTEGER, IN_UNIT_ID INTEGER, IN_TARGET_DATE DATE,
                                              IN_TARGET_SALE INTEGER, IN_CALCULATION_DATES VARCHAR (500),
                                              IN_CATEGORY_BUFFER INTEGER, IN_BUFFER INTEGER,
                                              IN_PRODUCT_IDS varchar (5000), IN IN_REQUEST_ID INTEGER)
BEGIN
DECLARE TOTAL_NET_SALES INT DEFAULT 0;
DECLARE EXTRA_TARGET_SALES INT DEFAULT 0;
DECLARE UPDATED_QUANTITY INT DEFAULT 0;
DECLARE UPDATED_SALES INT DEFAULT 0;
DECLARE PRICE INT DEFAULT 0;
DECLARE UNIQUE_IDENTIFIER INT DEFAULT 0;
DECLARE COUNTER INT DEFAULT 0;
DECLARE MAX_COUNTER INT DEFAULT 0;
DECLARE INCREMENT INT DEFAULT 0;

INSERT INTO KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA(REQUEST_ID,
                                                 BUSINESS_DATE,
                                                 DAY_OF_WEEK,
                                                 UNIT_ID,
                                                 BRAND_ID,
                                                 PRODUCT_ID,
                                                 DIMENSION,
                                                 AVG_UPT,
                                                 NET_SALES,
                                                 AVG_PRICE,
                                                 TOTAL_SALE,
                                                 SUGGESTED_QUANTITY,
                                                 CATEGORY_BUFFER_SUGGESTED_QUANTITY,
                                                 ROUND_SUGST_QUANTITY,
                                                 SUGGESTED_SALES,
                                                 DATA_TYPE,
                                                 QUANTITY_INCREMENT)
SELECT IN_REQUEST_ID,IN_TARGET_DATE,DAYOFWEEK(IN_TARGET_DATE),m.UNIT_ID,m.BRAND_ID,
       m.PRODUCT_ID,
       m.DIMENSION,
       m.AVG_UPT,
       SUM(m.NET_SALES),
       m.AVG_PRICE,
       IN_TARGET_SALE,
       m.SGST_QTY,
       NULL,
       NULL,
       NULL,
       'CALCULATED',
       0
FROM (
         SELECT DC.BRAND_ID,
                DC.BUSINESS_DATE,
                DC.UNIT_ID,
                DC.PRODUCT_ID,
                DC.NET_SALES,
                SUM(DC.QUANTITY) AS TOTAL_QUANTITY,
                DC.DIMENSION,
                SUM(DC.NET_SALES) / SUM(DC.QUANTITY) AS AVG_PRICE,
                IN_TARGET_SALE * AVG(DC.UPT) / 1000  AS SGST_QTY,
                AVG(DC.UPT) AS AVG_UPT,
                'CALCULATED' DATA_TYPE
         FROM KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA DC,
              (SELECT upm.UNIT_ID,
                      upm.PRODUCT_ID,
                      rl.RL_CODE DIMENSION
               FROM KETTLE_MASTER_DEV.UNIT_PRODUCT_MAPPING upm,
                    KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING upp,
                    KETTLE_MASTER_DEV.REF_LOOKUP rl
               WHERE upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
                 AND upp.DIMENSION_CODE = rl.RL_ID
                 AND upm.UNIT_ID = IN_UNIT_ID
                 AND upm.PRODUCT_STATUS = 'ACTIVE'
                 AND upp.PRICING_STATUS = 'ACTIVE') AP
         WHERE FIND_IN_SET(DC.BUSINESS_DATE, IN_CALCULATION_DATES) > 0
           AND DC.UNIT_ID = IN_UNIT_ID
           AND DC.BRAND_ID = IN_BRAND_ID
           AND DC.UNIT_ID = IN_UNIT_ID
           AND AP.PRODUCT_ID = DC.PRODUCT_ID
           and AP.DIMENSION = DC.DIMENSION
         GROUP BY DC.PRODUCT_ID, DC.DIMENSION, DC.BRAND_ID) m
GROUP BY m.PRODUCT_ID, m.DIMENSION, m.BRAND_ID;

UPDATE KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA ER
SET ER.CATEGORY_BUFFER_SUGGESTED_QUANTITY = ER.SUGGESTED_QUANTITY + (IN_CATEGORY_BUFFER * ER.SUGGESTED_QUANTITY) / 100,
    ER.CATEGORY_BUFFER_APPLIED= 'Y'
WHERE FIND_IN_SET(ER.PRODUCT_ID,
                  IN_PRODUCT_IDS) > 0
  AND UNIT_ID = IN_UNIT_ID
  AND BUSINESS_DATE = IN_TARGET_DATE
  AND ER.REQUEST_ID = IN_REQUEST_ID
  AND ER.BRAND_ID = IN_BRAND_ID;

UPDATE KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA ER
SET ER.CATEGORY_BUFFER_SUGGESTED_QUANTITY = ER.SUGGESTED_QUANTITY,
    ER.CATEGORY_BUFFER_APPLIED= 'N'
WHERE ER.UNIT_ID = IN_UNIT_ID
  AND BUSINESS_DATE = IN_TARGET_DATE
  AND ER.REQUEST_ID = IN_REQUEST_ID
  AND ER.CATEGORY_BUFFER_APPLIED IS NULL
  AND ER.BRAND_ID = IN_BRAND_ID;

UPDATE KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA ER
SET ER.ROUND_SUGST_QUANTITY = CEILING(ER.CATEGORY_BUFFER_SUGGESTED_QUANTITY),
    ER.SUGGESTED_SALES = ER.AVG_PRICE * CEILING(ER.CATEGORY_BUFFER_SUGGESTED_QUANTITY)
WHERE ER.UNIT_ID = IN_UNIT_ID
  AND ER.BUSINESS_DATE = IN_TARGET_DATE
  AND ER.REQUEST_ID = IN_REQUEST_ID
  AND ER.BRAND_ID = IN_BRAND_ID;

SELECT SUM(SUGGESTED_SALES)
INTO TOTAL_NET_SALES
FROM KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA ER
WHERE ER.UNIT_ID = IN_UNIT_ID
  AND ER.BUSINESS_DATE = IN_TARGET_DATE
  AND ER.REQUEST_ID = IN_REQUEST_ID
  AND ER.BRAND_ID = IN_BRAND_ID;

SET EXTRA_TARGET_SALES=IN_TARGET_SALE+ (IN_BUFFER* IN_TARGET_SALE)/100;

UPDATE KETTLE_SCM_DEV.ESTIMATE_QUERY_REQUEST
SET SUGGESTED_SALE=EXTRA_TARGET_SALES
WHERE UNIT_ID = IN_UNIT_ID
  AND BRAND_ID = IN_BRAND_ID
  AND STATUS = 'ACTIVE'
  AND TARGET_DATE = IN_TARGET_DATE;

SELECT COUNT(*)
INTO MAX_COUNTER
FROM KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA ER
WHERE ER.UNIT_ID = IN_UNIT_ID
  AND ER.BUSINESS_DATE = IN_TARGET_DATE
  AND ER.REQUEST_ID = IN_REQUEST_ID
  AND ER.BRAND_ID = IN_BRAND_ID;

IF IN_BUFFER>0 THEN
        WHILE TOTAL_NET_SALES < EXTRA_TARGET_SALES DO

SELECT ER.ROUND_SUGST_QUANTITY, ER.SUGGESTED_SALES, ER.AVG_PRICE, ER.ID, ER.QUANTITY_INCREMENT
INTO UPDATED_QUANTITY,UPDATED_SALES,PRICE,UNIQUE_IDENTIFIER,INCREMENT
FROM KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA ER
WHERE ER.UNIT_ID = IN_UNIT_ID
  AND ER.BUSINESS_DATE = IN_TARGET_DATE
  AND ER.REQUEST_ID = IN_REQUEST_ID
  AND ER.BRAND_ID = IN_BRAND_ID
ORDER BY ER.ROUND_SUGST_QUANTITY DESC LIMIT COUNTER,1;
SET COUNTER=COUNTER+1;
SET UPDATED_QUANTITY= UPDATED_QUANTITY+1;
SET UPDATED_SALES= UPDATED_SALES+PRICE;
SET TOTAL_NET_SALES= TOTAL_NET_SALES+PRICE;

UPDATE KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA ER
SET ER.QUANTITY_INCREMENT= INCREMENT + 1,
    ER.ROUND_SUGST_QUANTITY=UPDATED_QUANTITY,
    ER.SUGGESTED_SALES=UPDATED_SALES
WHERE ER.ID = UNIQUE_IDENTIFIER;

IF COUNTER=MAX_COUNTER THEN SET COUNTER=0;
END IF;
END WHILE;
END IF;

SELECT A.PRODUCT_ID,
       A.DIMENSION,
       A.CATEGORY_ID,
       A.CATEGORY,
       COALESCE(B.ROUND_SUGST_QUANTITY, 0) QUANTITY,
       A.UNIT_ID,
       COALESCE(B.SUGGESTED_SALES, 0)      SUGGESTED_SALES
FROM (
         SELECT UPM.UNIT_ID,
                UPM.PRODUCT_ID,
                RL.RL_CODE   DIMENSION,
                RTL.RTL_NAME CATEGORY,
                RTL.RTL_ID   CATEGORY_ID
         FROM KETTLE_MASTER_DEV.UNIT_PRODUCT_MAPPING UPM,
              KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING UPP,
              KETTLE_MASTER_DEV.REF_LOOKUP RL,
              KETTLE_MASTER_DEV.PRODUCT_DETAIL PD,
              KETTLE_MASTER_DEV.REF_LOOKUP_TYPE RTL

         WHERE UPM.UNIT_PROD_REF_ID = UPP.UNIT_PROD_REF_ID
           AND UPP.DIMENSION_CODE = RL.RL_ID
           AND UPM.PRODUCT_ID = PD.PRODUCT_ID
           AND PD.PRODUCT_TYPE = RTL.RTL_ID
           AND UPM.UNIT_ID = IN_UNIT_ID
           AND UPM.PRODUCT_STATUS = 'ACTIVE'
           AND UPP.PRICING_STATUS = 'ACTIVE') A
         LEFT OUTER JOIN (SELECT *
                          FROM KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA ER
                          WHERE ER.UNIT_ID = IN_UNIT_ID
                            AND ER.REQUEST_ID = IN_REQUEST_ID) B
                         ON A.UNIT_ID = B.UNIT_ID
                             AND A.PRODUCT_ID = B.PRODUCT_ID
                             AND B.BRAND_ID = IN_BRAND_ID
                             AND A.DIMENSION = B.DIMENSION
ORDER BY A.CATEGORY, A.PRODUCT_ID;
END$$
DELIMITER ;


USE `KETTLE_SCM_DEV`;
DROP procedure IF EXISTS `ESTIMATE_QUERY_REQUEST_PROC`;

DELIMITER $$
USE `KETTLE_SCM_DEV`$$
CREATE PROCEDURE `ESTIMATE_QUERY_REQUEST_PROC`(IN_BRAND_ID INTEGER, IN_UNIT_ID INTEGER, IN_TARGET_DATE DATE,
                                               IN_TARGET_SALE INTEGER, IN_CALCULATION_DATES VARCHAR (500),
                                               IN_ORDERING_TYPE VARCHAR (500), IN_GENERATION_DATE DATE)
BEGIN
UPDATE KETTLE_SCM_DEV.ESTIMATE_QUERY_REQUEST SET STATUS = 'IN_ACTIVE'
WHERE UNIT_ID = IN_UNIT_ID  AND BRAND_ID = IN_BRAND_ID
  AND TARGET_DATE = IN_TARGET_DATE;

INSERT INTO KETTLE_SCM_DEV.ESTIMATE_QUERY_REQUEST (UNIT_ID,DAY_OF_WEEK,TARGET_DATE,BRAND_ID,
                                                   TARGET_SALE,CALCULATION_DATES,STATUS,ORDERING_TYPE,GENERATION_DATE)
VALUES (IN_UNIT_ID, DAYOFWEEK(IN_TARGET_DATE), IN_TARGET_DATE, IN_BRAND_ID, IN_TARGET_SALE, IN_CALCULATION_DATES,
        'ACTIVE', IN_ORDERING_TYPE, IN_GENERATION_DATE);

SELECT ID FROM KETTLE_SCM_DEV.ESTIMATE_QUERY_REQUEST EQ
WHERE EQ.UNIT_ID = IN_UNIT_ID
  AND EQ.BRAND_ID = IN_BRAND_ID
  AND EQ.TARGET_DATE = IN_TARGET_DATE
  AND EQ.STATUS = 'ACTIVE';

END$$

DELIMITER ;


  CREATE TABLE KETTLE_SCM_DEV.VENDOR_EDITED_DETAIL_DATA (
   VENDOR_EDITED_DATA_ID INT(11) PRIMARY KEY AUTO_INCREMENT,
   VENDOR_ID INT(11) NOT NULL ,
   EDITED_TYPE VARCHAR(255),
   FIELD_NAME VARCHAR(255),
   UPDATED_STATUS VARCHAR(255)
   );
ALTER TABLE KETTLE_SCM_DEV.UNIT_SKU_MAPPING ADD COLUMN INVENTORY_LIST_ID INT NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.VENDOR_DETAIL_DATA ADD COLUMN TDS_DOCUMENT INTEGER NULL;
ALTER TABLE KETTLE_SCM_DEV.VENDOR_DETAIL_DATA ADD COLUMN TDS_RETURN_STATUS VARCHAR(1) ;


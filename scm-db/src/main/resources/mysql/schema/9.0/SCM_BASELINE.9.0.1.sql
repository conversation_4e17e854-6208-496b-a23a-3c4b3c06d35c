ALTER TABLE KETTLE_SCM_DEV.LIST_DETAIL
ADD COLUMN IS_ACCOUNTABLE VARCHAR(1),
ADD COLUMN BUDGET_CATEGORY VARCHAR(50);

UPDATE KETTLE_SCM_DEV.LIST_DETAIl
SET IS_ACCOUNTABLE = 'N';


ALTER TABLE KETTLE_SCM_DEV.SERVICE_RECEIVED_ITEM
ADD COLUMN BUDGET_CATEGORY VARCHAR(50);

ALTER TABLE KETTLE_SCM_DEV.WASTAGE_ITEM_DATA
ADD COLUMN TAX_TYPE VARCHAR(50),
ADD COLUMN TAX_PERCENTAGE DECIMAL(10,2),
ADD COLUMN TAX DECIMAL(10,2);

ALTER TABLE KETTLE_SCM_DEV.STOCK_INVENTORY
ADD COLUMN TAX_TYPE VARCHAR(50),
ADD COLUMN TAX_PERCENTAGE DECIMAL(10,2),
ADD COLUMN TAX_VARIANCE DECIMAL(10,2);




ALTER TABLE KETTLE_SCM_DEV.DAY_CLOSE_PRODUCT_VALUES
ADD COLUMN TAXABLE_CONSUMPTION DECIMAL(16,6),
ADD COLUMN TAXABLE_AMOUNT DECIMAL(16,6),
ADD COLUMN TAXABLE_PERCENTAGE DECIMAL(16,6),
ADD COLUMN TAX_AMOUNT DECIMAL(16,6);

ALTER TABLE `KETTLE_SCM_DEV`.`DAY_CLOSE_PRODUCT_VALUES`
CHANGE COLUMN `TAXABLE_PERCENTAGE` `TAX_PERCENTAGE` DECIMAL(16,6) NULL DEFAULT NULL ;

ALTER TABLE KETTLE_SCM_DEV.PRODUCT_CONSUMPTION_DRILLDOWN
ADD COLUMN TAXABLE_CONSUMPTION DECIMAL(16,6);

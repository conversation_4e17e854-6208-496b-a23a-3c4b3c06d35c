ALTER TABLE KETTLE_SCM_UAT.ITEM_TAX_DETAIL_DATA MODIFY COLUMN PO_ITEM_ID INTEGER NULL;

ALTER TABLE KETTLE_SCM_UAT.ITEM_TAX_DETAIL_DATA
ADD CONSTRAINT ITEM_TAX_DETAIL_DATA_IBFK_2
  FOREIGN KEY (GR_ITEM_ID)
  REFERENCES KETTLE_SCM_UAT.VENDOR_GR_ITEM_DETAIL (GOODS_RECEIVED_ITEM_ID);
  
  CREATE TABLE KETTLE_SCM_UAT.WRITE_OFF_EVENT_LOG (
    WRITE_OFF_LOG_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
    CLOSURE_EVENT_LOG_ID INTEGER NOT NULL,
    SKU_ID INTEGER NOT NULL,
    SKU_PRICE DECIMAL (16,6) NOT NULL,
    EXPECTED_VALUE DECIMAL (16,6) NOT NULL,
    CORRECTED_VALUE DECIMAL (16,6) NOT NULL,
    CONSTRAINT
      FOREIGN KEY (CLOSURE_EVENT_LOG_ID)
      REFERENCES DAY_CLOSE_EVENT_LOG (EVENT_LOG_ID)
);

UPDATE KETTLE_SCM_UAT.TRANSFER_ORDER SET CLOSURE_EVENT_ID=-1 where GENERATION_TIME <= "2017-06-30";
UPDATE KETTLE_SCM_UAT.GOODS_RECEIVED SET CLOSURE_EVENT_ID=-1 where GENERATION_TIME <= "2017-06-30";
UPDATE KETTLE_SCM_UAT.VENDOR_GOODS_RECEIVED_DATA SET CLOSURE_EVENT_ID=-1 where CREATED_AT <= "2017-06-30";
UPDATE KETTLE_SCM_UAT.WASTAGE_EVENT SET CLOSURE_EVENT_ID=-1 where BUSINESS_DATE <= "2017-06-30";

ALTER TABLE KETTLE_SCM_UAT.VENDOR_GOODS_RECEIVED_DATA ADD COLUMN GR_DOCUMENT_ID INT;

CREATE TABLE KETTLE_SCM_UAT.TRANSFER_ORDER_TAX_DETAIL(
ORDER_TAX_DETAIL_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
ORDER_ID INTEGER NOT NULL,
TAX_CODE VARCHAR(20) NOT NULL,
TAX_TYPE VARCHAR(20) NOT NULL,
TAX_PERCENTAGE DECIMAL(10,2) NULL,
TOTAL_AMOUNT DECIMAL(10,2) NULL,
TAXABLE_AMOUNT DECIMAL(10,2) NULL,
TOTAL_TAX DECIMAL (10,2) NOT NULL
);

CREATE TABLE KETTLE_SCM_UAT.TRANSFER_ORDER_ITEM_TAX_DETAIL(
ORDER_ITEM_TAX_DETAIL_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
ORDER_ITEM_ID INTEGER NOT NULL,
TAX_CODE VARCHAR(20) NOT NULL,
TAX_TYPE VARCHAR(20) NOT NULL,
TOTAL_AMOUNT DECIMAL(10,2) NULL,
TAXABLE_AMOUNT DECIMAL(10,2) NULL,
TAX_PERCENTAGE DECIMAL (10,2) NOT NULL,
TOTAL_TAX DECIMAL (10,2) NOT NULL
);


ALTER TABLE KETTLE_SCM_UAT.TRANSFER_ORDER
ADD COLUMN TOTAL_TAX DECIMAL(10,2) NULL;
ALTER TABLE KETTLE_SCM_UAT.TRANSFER_ORDER
ADD COLUMN GENERATED_INVOICE_ID VARCHAR(40) NULL;
ALTER TABLE KETTLE_SCM_UAT.TRANSFER_ORDER
ADD COLUMN TRANSFER_TYPE VARCHAR(20) NULL;

ALTER TABLE KETTLE_SCM_UAT.TRANSFER_ORDER_ITEM
ADD COLUMN TOTAL_TAX DECIMAL(10,2) NULL;
ALTER TABLE KETTLE_SCM_UAT.TRANSFER_ORDER_ITEM
ADD COLUMN TAX_CODE VARCHAR(40) NULL;
ALTER TABLE KETTLE_SCM_UAT.TRANSFER_ORDER_ITEM
ADD COLUMN ORDER_ITEM_INVOICE_ID INTEGER NULL;
CREATE TABLE KETTLE_SCM_UAT.TRANSFER_ORDER_ITEM_INVOICE
(ORDER_ITEM_INVOICE_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
ORDER_ID INTEGER NOT NULL,
STATE_ID INTEGER NOT NULl,
STATE_INVOICE_ID INTEGER NOT NULl,
TOTAL_AMOUNT DECIMAL(10,2) NOT NULl,
TAXABLE_AMOUNT DECIMAL(10,2) NOT NULl,
TOTAL_TAX DECIMAL(10,2) NOT NULl,
TAX_CATEGORY VARCHAR(40) NOT NULl
);


CREATE INDEX TRANSFER_ORDER_ITEM_INVOICE_STATE_ID ON KETTLE_SCM_UAT.TRANSFER_ORDER_ITEM_INVOICE(STATE_ID);

CREATE TABLE KETTLE_SCM_UAT.TRANSFER_ORDER_ITEM_INVOICE_TAX_DETAIL
(ORDER_ITEM_INVOICE_TAX_DETAIL_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
ORDER_ITEM_INVOICE_ID INTEGER NOT NULl,
TAX_PERCENTAGE DECIMAL(10,2) NOT NULl,
TOTAL_AMOUNT DECIMAL(10,2) NOT NULl,
TAXABLE_AMOUNT DECIMAL(10,2) NOT NULl,
TOTAL_TAX DECIMAL(10,2) NOT NULl,
TAX_CODE VARCHAR(20) NOT NULl,
TAX_TYPE VARCHAR(20) NOT NULl
);

CREATE INDEX TRANSFER_ORDER_ITEM_INVOICE_TAX_DETAIL_ORDER_ITEM_INVOICE_ID ON KETTLE_SCM_UAT.TRANSFER_ORDER_ITEM_INVOICE_TAX_DETAIL(ORDER_ITEM_INVOICE_ID);

CREATE TABLE KETTLE_SCM_UAT.STATE_SEQUENCE_ID(
SEQUENCE_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
STATE_ID INTEGER NOT NULL,
ID_TYPE VARCHAR(30) NOT NULL,
NEXT_VALUE INTEGER NOT NULL
);

CREATE INDEX STATE_SEQUENCE_ID_STATE_ID ON KETTLE_SCM_UAT.STATE_SEQUENCE_ID(STATE_ID) USING BTREE;
CREATE INDEX STATE_SEQUENCE_ID_ID_TYPE ON KETTLE_SCM_UAT.STATE_SEQUENCE_ID(ID_TYPE) USING BTREE;

ALTER TABLE KETTLE_SCM_UAT.VENDOR_GOODS_RECEIVED_DATA ADD COLUMN DOCUMENT_DATE DATE NULL;
ALTER TABLE KETTLE_SCM_UAT.VENDOR_GOODS_RECEIVED_DATA ADD COLUMN AMOUNT_MATCHED VARCHAR (1) NULL;

#ADD UNIQUE CONSTRAINT TO SKU_PACKAGING_PRICE
ALTER TABLE KETTLE_SCM_UAT.SKU_PRICE_DATA ADD constraint PACKAGING_PRICE_UNIQUE UNIQUE (SKU_ID, SKU_PACKAGING_ID, VENDOR_ID,DISPATCH_LOCATION,DELIVERY_LOCATION);

update KETTLE_SCM_UAT.GOODS_RECEIVED set PURCHASE_ORDER_ID = null where PURCHASE_ORDER_ID is not null;
CREATE TABLE KETTLE_SCM_UAT.VENDOR_GR_PO_ITEM_MAPPING_DATA(
 MAPPING_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
 PACKAGING_QUANTITY DECIMAL(16,6),
 PURCHASE_ORDER_ITEM_ID INT NOT NULL,
 VENDOR_GOODS_RECEIVED_ITEM_ID INT NOT NULL,
 CONSTRAINT
		FOREIGN KEY (VENDOR_GOODS_RECEIVED_ITEM_ID)
		REFERENCES VENDOR_GR_ITEM_DETAIL (GOODS_RECEIVED_ITEM_ID),
 CONSTRAINT
		FOREIGN KEY (PURCHASE_ORDER_ITEM_ID)
		REFERENCES PURCHASE_ORDER_ITEM_DETAIL (PURCHASE_ORDER_ITEM_ID)
);


UPDATE KETTLE_SCM_UAT.GOODS_RECEIVED SET GOODS_RECEIVED_STATUS = "SETTLED", CLOSURE_EVENT_ID=-1
where GENERATION_TIME <= "2017-07-01 00:00:00";

UPDATE KETTLE_SCM_UAT.TRANSFER_ORDER SET TRANSFER_ORDER_STATUS = "SETTLED", CLOSURE_EVENT_ID=-1
where GENERATION_TIME <= "2017-07-01 00:00:00";


update KETTLE_SCM.SKU_DEFINITION set INVENTORY_LIST_ID = 1 where
INVENTORY_LIST_ID is null;

UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='0' WHERE PRODUCT_ID=100124;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='0' WHERE PRODUCT_ID=100156;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='0' WHERE PRODUCT_ID=100669;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='0' WHERE PRODUCT_ID=100745;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='39232990' WHERE PRODUCT_ID=100913;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='16021000' WHERE PRODUCT_ID=100914;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='16021000' WHERE PRODUCT_ID=100915;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='96032900' WHERE PRODUCT_ID=100916;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='96032900' WHERE PRODUCT_ID=100917;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='39241090' WHERE PRODUCT_ID=100918;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='96032900' WHERE PRODUCT_ID=100919;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='40151100' WHERE PRODUCT_ID=100920;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='40151100' WHERE PRODUCT_ID=100921;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='40151100' WHERE PRODUCT_ID=100922;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='39241090' WHERE PRODUCT_ID=100923;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='39241090' WHERE PRODUCT_ID=100924;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='96032900' WHERE PRODUCT_ID=100925;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='96032900' WHERE PRODUCT_ID=100926;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='96032900' WHERE PRODUCT_ID=100927;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='39162011' WHERE PRODUCT_ID=100928;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='85171919' WHERE PRODUCT_ID=100929;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='34011942' WHERE PRODUCT_ID=100930;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='34011942' WHERE PRODUCT_ID=100931;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='34011942' WHERE PRODUCT_ID=100932;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='34011942' WHERE PRODUCT_ID=100933;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='34011942' WHERE PRODUCT_ID=100934;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='34011942' WHERE PRODUCT_ID=100935;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='49119990' WHERE PRODUCT_ID=100936;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='49119990' WHERE PRODUCT_ID=100937;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='96032900' WHERE PRODUCT_ID=100938;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='82159900' WHERE PRODUCT_ID=100939;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='82159900' WHERE PRODUCT_ID=100940;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='82159900' WHERE PRODUCT_ID=100941;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='21069099' WHERE PRODUCT_ID=100942;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='73239110' WHERE PRODUCT_ID=100943;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='39161090' WHERE PRODUCT_ID=100944;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='21069040' WHERE PRODUCT_ID=100945;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='4062000'  WHERE PRODUCT_ID=100946;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='16021000' WHERE PRODUCT_ID=100947;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='07101000' WHERE PRODUCT_ID=100948;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='82159900' WHERE PRODUCT_ID=100949;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='21039090' WHERE PRODUCT_ID=100950;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='21069040' WHERE PRODUCT_ID=100951;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='4062000'  WHERE PRODUCT_ID=100952;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='07101000' WHERE PRODUCT_ID=100953;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='21069040' WHERE PRODUCT_ID=100955;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='09109990' WHERE PRODUCT_ID=101139;
UPDATE KETTLE_SCM.PRODUCT_DEFINITION SET TAX_CATEGORY_CODE='09109990' WHERE PRODUCT_ID=101140;


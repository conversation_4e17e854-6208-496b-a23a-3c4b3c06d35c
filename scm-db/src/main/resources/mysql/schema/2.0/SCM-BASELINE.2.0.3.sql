DROP TABLE IF EXISTS KETTLE_SCM_DEV.PURCHASE_ORDER;
CREATE TABLE KETTLE_SCM_DEV.PURCHASE_ORDER (
  PURCHASE_ORDER_ID int(11) NOT NULL AUTO_INCREMENT,
  GENERATION_TIME timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  DELIVERY_LOCATION_ID int(11) NOT NULL,
  DISPATCH_LOCATION_ID int(11) NOT NULL,
  GENERATED_FOR_VENDOR_ID int(11) NOT NULL,
  PO_INVOICE_ID int(11) NULL,
  GENERATED_BY int(11) NOT NULL,
  APPROVED_BY int(11) NULL,
  VENDOR_NOTIFIED VARCHAR(1) NULL,
  FORCE_CLOSED VARCHAR(1) NULL,
  CREATION_TYPE VARCHAR(15) NOT NULL,
  LAST_UPDATED_BY int(11) NULL,
  B<PERSON>L_AMOUNT decimal(16,6) DEFAULT NULL,
  TOTAL_TAXES decimal(16,6) DEFAULT NULL,
  PAID_AMOUNT decimal(16,6) DEFAULT NULL,
  ORDER_RECEIPT_NUMBER varchar(255) DEFAULT NULL,
  PURCHASE_ORDER_STATUS varchar(30) NOT NULL,
  COMMENT varchar(1000) DEFAULT NULL,
  INITIATION_TIME timestamp NULL,
  LAST_UPDATE_TIME timestamp NULL,
  FULFILLMENT_DATE timestamp NULL,
  PRIMARY KEY (PURCHASE_ORDER_ID),
  CONSTRAINT
    FOREIGN KEY (PO_INVOICE_ID)
    REFERENCES KETTLE_SCM_DEV.DOCUMENT_DETAIL_DATA (DOCUMENT_ID)
);


DROP TABLE IF EXISTS KETTLE_SCM_DEV.PURCHASE_ORDER_ITEM_DETAIL;
CREATE TABLE KETTLE_SCM_DEV.PURCHASE_ORDER_ITEM_DETAIL (
  PURCHASE_ORDER_ITEM_ID INT NOT NULL AUTO_INCREMENT,
  TOTAL_COST DECIMAL(10,4) NOT NULL,
  PURCHASE_ORDER_ID INT NOT NULL,
  AMOUNT_PAID DECIMAL(10,4) NOT NULL,
  SKU_ID INT NOT NULL,
  HSN_CODE VARCHAR(75) NOT NULL,
  PACKAGING_CONVERSION_RATIO DECIMAL(10,4) NULL,
  SKU_NAME VARCHAR(150) NOT NULL,
  REQUESTED_QUANTITY DECIMAL(10,4) NOT NULL,
  REQUESTED_ABSOLUTE_QUANTITY DECIMAL(10,4) NOT NULL,
  TRANSFERRED_QUANTITY DECIMAL(10,4) NULL,
  RECEIVED_QUANTITY DECIMAL(10,4) NULL,
  UNIT_OF_MEASURE VARCHAR(45) NOT NULL,
  UNIT_PRICE DECIMAL(10,4) NOT NULL,
  NEGOTIATED_UNIT_PRICE DECIMAL(10,4) NULL,
  PACKAGING_ID INT NULL,
  PACKAGING_NAME VARCHAR(45) NULL,
  PACKAGING_QUANTITY INT NULL,
  EXEMPT_ITEM VARCHAR(1) NOT NULL,
  IGST DECIMAL(10,2) NULL,
  IGST_VALUE DECIMAL(10,2) NULL,
  SGST DECIMAL(10,2) NULL,
  SGST_VALUE DECIMAL(10,2) NULL,
  CGST DECIMAL(10,2) NULL,
  CGST_VALUE DECIMAL(10,2) NULL,
  OTHER_TAXES DECIMAL(10,4) NULL,
  TOTAL_TAX_VALUE DECIMAL(10,4) NULL,
  PRIMARY KEY (PURCHASE_ORDER_ITEM_ID),
  UNIQUE INDEX PURCHASE_ORDER_ITEM_ID_UNIQUE (PURCHASE_ORDER_ITEM_ID ASC),
  INDEX PO_IFBK_idx (PURCHASE_ORDER_ID ASC),
  CONSTRAINT
    FOREIGN KEY (PURCHASE_ORDER_ID)
    REFERENCES KETTLE_SCM_DEV.PURCHASE_ORDER (PURCHASE_ORDER_ID)
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.ITEM_TAX_DETAIL_DATA;
CREATE TABLE KETTLE_SCM_DEV.ITEM_TAX_DETAIL_DATA (
  ITEM_TAX_DETAIL_ID int(11) NOT NULL AUTO_INCREMENT,
  PO_ITEM_ID int(11) NOT NULL,
  TAX_TYPE VARCHAR(15) NOT NULL,
  TAX_PERCENTAGE decimal(16,6) DEFAULT NULL,
  TAX_VALUE decimal(16,6) DEFAULT NULL,
  PRIMARY KEY (ITEM_TAX_DETAIL_ID),
  CONSTRAINT
    FOREIGN KEY (PO_ITEM_ID)
    REFERENCES KETTLE_SCM_DEV.PURCHASE_ORDER_ITEM_DETAIL (PURCHASE_ORDER_ITEM_ID)
);


DROP TABLE IF EXISTS KETTLE_SCM_DEV.PURCHASE_ORDER_NOTIFICATION;
CREATE TABLE KETTLE_SCM_DEV.PURCHASE_ORDER_NOTIFICATION (
  NOTIFICATION_ID int(11) NOT NULL AUTO_INCREMENT,
  CONTACT VARCHAR(100) NULL,
  MESSAGE VARCHAR(150) NULL,
  SERVICE_CLIENT VARCHAR(150) DEFAULT NULL,
  NOTIFICATION_CARRIER_TYPE VARCHAR (15) DEFAULT NULL,
  NOTIFICATION_TYPE VARCHAR (15) NULL,
  NOTIFICATION_SENT VARCHAR (15) NULL,
  NOTIFICATION_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PURCHASE_ORDER_ID INT NULL,
  PRIMARY KEY (NOTIFICATION_ID),
  CONSTRAINT
    FOREIGN KEY (PURCHASE_ORDER_ID)
    REFERENCES KETTLE_SCM_DEV.PURCHASE_ORDER (PURCHASE_ORDER_ID)
);


DROP TABLE IF EXISTS KETTLE_SCM_DEV.PURCHASE_ORDER_STATUS_EVENT;
CREATE TABLE KETTLE_SCM_DEV.PURCHASE_ORDER_STATUS_EVENT (
  PURCHASE_ORDER_STATUS_EVENT_ID int(11) NOT NULL AUTO_INCREMENT,
  FROM_STATUS VARCHAR(15) NULL,
  TO_STATUS VARCHAR(15) NULL,
  TRANSITION_STATUS VARCHAR(15) DEFAULT NULL,
  UPDATED_BY INT(11),
  UPDATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PURCHASE_ORDER_ID INT NULL,
  PRIMARY KEY (PURCHASE_ORDER_STATUS_EVENT_ID)
);

ALTER TABLE KETTLE_SCM_DEV.PRODUCT_DEFINITION ADD COLUMN TAX_CATEGORY_CODE VARCHAR(50);
ALTER TABLE KETTLE_SCM_DEV.VENDOR_DISPATCH_LOCATIONS ADD COLUMN CONTACT_EMAIL VARCHAR(100) NOT NULL;


DROP TABLE IF EXISTS KETTLE_SCM_DEV.SCM_TRANSACTION_EVENT_DRILLDOWN;
CREATE TABLE KETTLE_SCM_DEV.SCM_TRANSACTION_EVENT_DRILLDOWN (
    TRANSACTION_EVENT_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
    EVENT_TYPE VARCHAR(15) NOT NULL,
    SKU_ID INTEGER NOT NULL,
    PRICE DECIMAL(16,6) NOT NULL,
    QUANTITY DECIMAL(16,6) NOT NULL,
    COST DECIMAL(16,6) NOT NULL,
    CLOSURE_EVENT_ID INTEGER NOT NULL,
    CONSTRAINT
      FOREIGN KEY (CLOSURE_EVENT_ID)
      REFERENCES DAY_CLOSE_EVENT (EVENT_ID)
);

CREATE INDEX index_drilldown_event_id ON KETTLE_SCM_DEV.SCM_TRANSACTION_EVENT_DRILLDOWN (CLOSURE_EVENT_ID) USING BTREE;

DROP TABLE IF EXISTS KETTLE_SCM_DEV.DAY_CLOSURE_TXN_EVENT_MAPPING;
CREATE TABLE KETTLE_SCM_DEV.DAY_CLOSURE_TXN_EVENT_MAPPING (
    EVENT_MAPPING_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
    CLOSURE_ID INTEGER NOT NULL,
    TRANSACTION_EVENT_ID INTEGER NOT NULL,
    EVENT_TYPE VARCHAR(15) NOT NULL,
    IS_EXTERNAL VARCHAR(1) NOT NULL,
    CONSTRAINT
      FOREIGN KEY (CLOSURE_ID)
      REFERENCES DAY_CLOSE_EVENT (EVENT_ID)
);


DROP TABLE IF EXISTS KETTLE_SCM_DEV.DAY_CLOSE_EVENT_LOG;
CREATE TABLE KETTLE_SCM_DEV.DAY_CLOSE_EVENT_LOG (
    EVENT_LOG_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
    CLOSURE_EVENT_ID INTEGER NOT NULL,
    EVENT_TYPE VARCHAR(30) NULL,
    EVENT_STATUS VARCHAR(15) NULL,
    CREATED_BY INTEGER NULL,
    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT
      FOREIGN KEY (CLOSURE_EVENT_ID)
      REFERENCES DAY_CLOSE_EVENT (EVENT_ID)
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.WRITE_OFF_EVENT_LOG;
CREATE TABLE KETTLE_SCM_DEV.WRITE_OFF_EVENT_LOG (
    WRITE_OFF_LOG_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
    CLOSURE_EVENT_LOG_ID INTEGER NOT NULL,
    SKU_ID INTEGER NOT NULL,
    SKU_PRICE DECIMAL (16,6) NOT NULL,
    EXPECTED_VALUE DECIMAL (16,6) NOT NULL,
    CORRECTED_VALUE DECIMAL (16,6) NOT NULL,
    CONSTRAINT
      FOREIGN KEY (CLOSURE_EVENT_LOG_ID)
      REFERENCES DAY_CLOSE_EVENT_LOG (EVENT_LOG_ID)
);



CREATE INDEX index_closure_txns_mapping_id ON KETTLE_SCM_DEV.DAY_CLOSURE_TXN_EVENT_MAPPING (CLOSURE_ID) USING BTREE;

DROP TABLE IF EXISTS KETTLE_SCM_DEV.INVENTORY_DRILLDOWN;
CREATE TABLE KETTLE_SCM_DEV.INVENTORY_DRILLDOWN (
    INVENTORY_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
    PRODUCT_ID INTEGER NULL,
    SKU_ID INTEGER NOT NULL,
    UOM VARCHAR(30) NOT NULL,
    PRICE DECIMAL(16,6) NOT NULL,
    OPENING DECIMAL(16,6) NOT NULL,
    TRANSFERRED DECIMAL(16,6),
    RECEIVED DECIMAL(16,6),
    CONSUMPTION DECIMAL (16,6),
    BOOKED DECIMAL(16,6),
    WASTED DECIMAL(16,6),
    EXPECTED_CLOSING DECIMAL(16,6) NULL,
    ACTUAL_CLOSING DECIMAL(16,6) NULL,
    VARIANCE DECIMAL(16,6) NULL,
    VARIANCE_COST DECIMAL(16,6) NULL,
    CLOSURE_EVENT_ID INTEGER NOT NULL,
    CONSTRAINT
      FOREIGN KEY (CLOSURE_EVENT_ID)
      REFERENCES DAY_CLOSE_EVENT (EVENT_ID)
);

CREATE INDEX index_inventory_drilldown_id ON KETTLE_SCM_DEV.INVENTORY_DRILLDOWN (CLOSURE_EVENT_ID) USING BTREE;


DROP TABLE IF EXISTS KETTLE_SCM_DEV.INVENTORY_LISTS;
CREATE TABLE KETTLE_SCM_DEV.INVENTORY_LISTS (
    LIST_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
    LIST_NAME VARCHAR (50),
    LIST_CODE VARCHAR (50),
    LIST_STATUS VARCHAR (50),
    LIST_CREATED_BY INTEGER,
    LIST_CREATED_AT TIMESTAMP default CURRENT_TIMESTAMP
);


#16 dummy lists created for testing purposes
INSERT INTO KETTLE_SCM_DEV.INVENTORY_LISTS (LIST_NAME, LIST_CODE, LIST_STATUS, LIST_CREATED_BY)
VALUES
('No List', '0', 'ACTIVE', '120056'),
('List 1', '1', 'ACTIVE', '120056'),
('List 2', '2', 'ACTIVE', '120056'),
('List 3', '3', 'ACTIVE', '120056'),
('List 4', '4', 'ACTIVE', '120056'),
('List 5', '5', 'ACTIVE', '120056'),
('List 6', '6', 'ACTIVE', '120056'),
('List 7', '7', 'ACTIVE', '120056'),
('List 8', '8', 'ACTIVE', '120056'),
('List 9', '9', 'ACTIVE', '120056'),
('List 10', '10', 'ACTIVE', '120056'),
('List 11', '11', 'ACTIVE', '120056'),
('List 12', '12', 'ACTIVE', '120056'),
('List 13', '13', 'ACTIVE', '120056'),
('List 14', '14', 'ACTIVE', '120056'),
('All Lists', '15', 'ACTIVE', '120056');

ALTER TABLE KETTLE_SCM_DEV.SKU_DEFINITION ADD COLUMN INVENTORY_LIST_ID INTEGER;
ALTER TABLE KETTLE_SCM_DEV.STOCK_ENTRY ADD COLUMN SKU_ID INTEGER;
ALTER TABLE KETTLE_SCM_DEV.STOCK_ENTRY ADD COLUMN SKU_PRICE DECIMAL(16,6);
ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER ADD COLUMN CLOSURE_EVENT_ID INTEGER;
ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED ADD COLUMN CLOSURE_EVENT_ID INTEGER;
ALTER TABLE KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA ADD COLUMN CLOSURE_EVENT_ID INTEGER;
ALTER TABLE KETTLE_SCM_DEV.WASTAGE_EVENT ADD COLUMN CLOSURE_EVENT_ID INTEGER;


ALTER TABLE KETTLE_SCM_DEV.DAY_CLOSE_EVENT ADD COLUMN CREATED_BY INTEGER;
ALTER TABLE KETTLE_SCM_DEV.DAY_CLOSE_EVENT ADD COLUMN UPDATED_BY INTEGER;
ALTER TABLE KETTLE_SCM_DEV.DAY_CLOSE_EVENT MODIFY COLUMN GENERATION_TIME TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE KETTLE_SCM_DEV.DAY_CLOSE_EVENT ADD COLUMN UPDATED_AT TIMESTAMP NULL;

ALTER TABLE KETTLE_SCM_DEV.UNIT_DETAIL ADD COLUMN CLOSURE_FROM_TIME INTEGER;
ALTER TABLE KETTLE_SCM_DEV.UNIT_DETAIL ADD COLUMN CLOSURE_TO_TIME INTEGER;

#Testing purposes for Kitchen Delhi and Warehouse Delhi
UPDATE KETTLE_SCM_DEV.UNIT_DETAIL SET CLOSURE_TO_TIME='13', CLOSURE_FROM_TIME='11' WHERE UNIT_ID='22001';
UPDATE KETTLE_SCM_DEV.UNIT_DETAIL SET CLOSURE_TO_TIME='13', CLOSURE_FROM_TIME='11' WHERE UNIT_ID='24001';



#Scripts to be executed before closing can be done properly on production environment for Warehouse and Kitchen
UPDATE KETTLE_SCM.GOODS_RECEIVED SET CLOSURE_EVENT_ID=-1 where GENERATION_TIME <= "2017-07-01 00:00:00";
UPDATE KETTLE_SCM.TRANSFER_ORDER SET CLOSURE_EVENT_ID=-1 where GENERATION_TIME <= "2017-07-01 00:00:00";
UPDATE KETTLE_SCM.WASTAGE_EVENT SET CLOSURE_EVENT_ID=-1 where BUSINESS_DATE <= "2017-06-30";

ALTER TABLE KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA ADD COLUMN GR_DOCUMENT_ID INT;


DROP TABLE IF EXISTS KETTLE_SCM_DEV.TRANSFER_ORDER_TAX_DETAIL;
CREATE TABLE KETTLE_SCM_DEV.TRANSFER_ORDER_TAX_DETAIL(
ORDER_TAX_DETAIL_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
ORDER_ID INTEGER NOT NULL,
TAX_CODE VARCHAR(20) NOT NULL,
TAX_TYPE VARCHAR(20) NOT NULL,
TAX_PERCENTAGE DECIMAL(10,2) NULL,
TOTAL_AMOUNT DECIMAL(10,2) NULL,
TAXABLE_AMOUNT DECIMAL(10,2) NULL,
TOTAL_TAX DECIMAL (10,2) NOT NULL
);
DROP TABLE IF EXISTS  KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM_TAX_DETAIL;
CREATE TABLE KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM_TAX_DETAIL(
ORDER_ITEM_TAX_DETAIL_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
ORDER_ITEM_ID INTEGER NOT NULL,
TAX_CODE VARCHAR(20) NOT NULL,
TAX_TYPE VARCHAR(20) NOT NULL,
TOTAL_AMOUNT DECIMAL(10,2) NULL,
TAXABLE_AMOUNT DECIMAL(10,2) NULL,
TAX_PERCENTAGE DECIMAL (10,2) NOT NULL,
TOTAL_TAX DECIMAL (10,2) NOT NULL
);


ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER
ADD COLUMN TOTAL_TAX DECIMAL(10,2) NULL;
ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER
ADD COLUMN GENERATED_INVOICE_ID VARCHAR(40) NULL;
ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER
ADD COLUMN TRANSFER_TYPE VARCHAR(20) NULL;

ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM
ADD COLUMN TOTAL_TAX DECIMAL(10,2) NULL;
ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM
ADD COLUMN TAX_CODE VARCHAR(40) NULL;
ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM
ADD COLUMN ORDER_ITEM_INVOICE_ID INTEGER NULL;

DROP TABLE IF EXISTS KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM_INVOICE;
CREATE TABLE KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM_INVOICE
(ORDER_ITEM_INVOICE_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
ORDER_ID INTEGER NOT NULL,
STATE_ID INTEGER NOT NULl,
STATE_INVOICE_ID INTEGER NOT NULl,
TOTAL_AMOUNT DECIMAL(10,2) NOT NULl,
TAXABLE_AMOUNT DECIMAL(10,2) NOT NULl,
TOTAL_TAX DECIMAL(10,2) NOT NULl,
TAX_CATEGORY VARCHAR(40) NOT NULl
);

CREATE INDEX TRANSFER_ORDER_ITEM_INVOICE_STATE_ID ON KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM_INVOICE(STATE_ID);

CREATE TABLE KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM_INVOICE_TAX_DETAIL
(ORDER_ITEM_INVOICE_TAX_DETAIL_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
ORDER_ITEM_INVOICE_ID INTEGER NOT NULl,
TAX_PERCENTAGE DECIMAL(10,2) NOT NULl,
TOTAL_AMOUNT DECIMAL(10,2) NOT NULl,
TAXABLE_AMOUNT DECIMAL(10,2) NOT NULl,
TOTAL_TAX DECIMAL(10,2) NOT NULl,
TAX_CODE VARCHAR(20) NOT NULl,
TAX_TYPE VARCHAR(20) NOT NULl
);

CREATE INDEX TRANSFER_ORDER_ITEM_INVOICE_TAX_DETAIL_ORDER_ITEM_INVOICE_ID ON KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM_INVOICE_TAX_DETAIL(ORDER_ITEM_INVOICE_ID);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.STATE_SEQUENCE_ID;
CREATE TABLE STATE_SEQUENCE_ID(
SEQUENCE_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
STATE_ID INTEGER NOT NULL,
ID_TYPE VARCHAR(30) NOT NULL,
NEXT_VALUE INTEGER NOT NULL
);

CREATE INDEX STATE_SEQUENCE_ID_STATE_ID ON KETTLE_SCM_DEV.STATE_SEQUENCE_ID(STATE_ID) USING BTREE;
CREATE INDEX STATE_SEQUENCE_ID_ID_TYPE ON KETTLE_SCM_DEV.STATE_SEQUENCE_ID(ID_TYPE) USING BTREE;

ALTER TABLE KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA ADD COLUMN DOCUMENT_DATE DATE NULL;
ALTER TABLE KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA ADD COLUMN AMOUNT_MATCHED VARCHAR (1) NULL;

#ADD UNIQUE CONSTRAINT TO SKU_PACKAGING_PRICE
ALTER TABLE KETTLE_SCM_DEV.SKU_PRICE_DATA ADD constraint PACKAGING_PRICE_UNIQUE UNIQUE (SKU_ID, SKU_PACKAGING_ID, VENDOR_ID,DISPATCH_LOCATION,DELIVERY_LOCATION);

INSERT INTO KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA (ACL_ID, ACL_MODULE, ACL_MODULE_DESCRIPTION, ACL_STATUS, APPLICATION_NAME)
 VALUES ('129', 'scm-service.purchase-order-management.approve-po', 'Approve Purchase Order', 'ACTIVE', 'SCM_SERVICE');

INSERT INTO KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA (ACL_ID, ACL_MODULE, ACL_MODULE_DESCRIPTION, ACL_STATUS, APPLICATION_NAME)
 VALUES ('130', 'scm-service.purchase-order-management.create-po', 'Create Purchase Order', 'ACTIVE', 'SCM_SERVICE');

INSERT INTO KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA (ACL_ID, ACL_MODULE, ACL_MODULE_DESCRIPTION, ACL_STATUS, APPLICATION_NAME)
 VALUES ('131', 'scm-service.purchase-order-management.*', 'Create Purchase Order', 'ACTIVE', 'SCM_SERVICE');

update KETTLE_SCM_DEV.GOODS_RECEIVED set PURCHASE_ORDER_ID = null where PURCHASE_ORDER_ID is not null;

DROP TABLE IF EXISTS KETTLE_SCM_DEV.VENDOR_GR_PO_ITEM_MAPPING_DATA;
CREATE TABLE KETTLE_SCM_DEV.VENDOR_GR_PO_ITEM_MAPPING_DATA(
 MAPPING_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
 PACKAGING_QUANTITY DECIMAL(16,6),
 PURCHASE_ORDER_ITEM_ID INT NOT NULL,
 VENDOR_GOODS_RECEIVED_ITEM_ID INT NOT NULL,
 CONSTRAINT
		FOREIGN KEY (VENDOR_GOODS_RECEIVED_ITEM_ID)
		REFERENCES VENDOR_GR_ITEM_DETAIL (GOODS_RECEIVED_ITEM_ID),
 CONSTRAINT
		FOREIGN KEY (PURCHASE_ORDER_ITEM_ID)
		REFERENCES PURCHASE_ORDER_ITEM_DETAIL (PURCHASE_ORDER_ITEM_ID)
);

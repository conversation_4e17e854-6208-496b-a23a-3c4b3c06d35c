ALTER TABLE KETTLE_SCM_DEV.ADDRESS_DETAIL_DATA ADD COLUMN STATE_CODE VARCHAR(10) NULL;
ALTER TABLE KETTLE_SCM_DEV.DOCUMENT_DETAIL_DATA ADD COLUMN DOCUMENT_URL VARCHAR(500);


DROP TABLE IF EXISTS KETTLE_SCM_DEV.PRODUCTION_PLAN_EVENT;
CREATE TABLE KETTLE_SCM_DEV.PRODUCTION_PLAN_EVENT (
    PLAN_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
    UNIT_ID INTEGER NOT NULL,
    FULFILLMENT_DATE DATETIME NOT NULL,
    GENERATED_BY INTEGER NOT NULL,
    GENERATION_TIME DATETIME NOT NULL,
    LAST_UPDATED_BY INTEGER NOT NULL,
    LAST_UPDATE_TIME DATETIME NOT NULL,
    PLAN_STATUS VARCHAR(10)
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.PLAN_ORDER_MAPPING;
CREATE TABLE KETTLE_SCM_DEV.PLAN_ORDER_MAPPING (
    PLAN_ORDER_MAP_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
    PLAN_EVENT_ID INTEGER NOT NULL,
    REQUEST_ORDER_ID INTEGER NOT NULL
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.PLAN_ORDER_ITEM;
CREATE TABLE KETTLE_SCM_DEV.PLAN_ORDER_ITEM (
    PLAN_ITEM_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
    PRODUCT_ID INTEGER NOT NULL,
    PRODUCT_NAME VARCHAR(100) NOT NULL,
    REQUESTED_QUANTITY DECIMAL(20,10) NOT NULL,
    AVAILABLE_QUANTITY DECIMAL(20,10) NOT NULL,
    UNIT_OF_MEASURE VARCHAR(10) NOT NULL,
    UNIT_PRICE DECIMAL(20,10),
    CALCULATED_AMOUNT DECIMAL(20,10),
    CATEGORY VARCHAR(20) NOT NULL,
    ITEM_TYPE VARCHAR(20) NOT NULL,
    PLAN_EVENT_ID INTEGER NOT NULL
);


DROP TABLE IF EXISTS KETTLE_SCM_DEV.PRODUCTION_BOOKING;
CREATE TABLE KETTLE_SCM_DEV.PRODUCTION_BOOKING (
    BOOKING_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    PRODUCT_ID INTEGER NOT NULL,
    PRODUCT_NAME VARCHAR(100) NOT NULL,
    SKU_ID INTEGER NOT NULL,
    UOM VARCHAR(50) NOT NULL,
    QUANTITY DECIMAL(20 , 10 ) NOT NULL,
    UNIT_ID INTEGER NOT NULL,
    UNIT_PRICE DECIMAL(20 , 10 ),
    TOTAL_COST DECIMAL(20 , 10 ),
    GENERATION_TIME DATETIME NOT NULL,
    CANCELLATION_TIME DATETIME NULL,
    CLOSURE_TIME DATETIME NULL,
    CLOSURE_EVENT_ID INTEGER NULL,
    GENERATED_BY INTEGER NOT NULL,
    CANCELLED_BY INTEGER NULL,
    BOOKING_STATUS VARCHAR(20) NOT NULL
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.BOOKING_CONSUMPTION;
CREATE TABLE KETTLE_SCM_DEV.BOOKING_CONSUMPTION (
    BOOKING_CONSUMPTION_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    SKU_ID INTEGER NOT NULL,
    SKU_NAME VARCHAR(100) NOT NULL,
    UOM VARCHAR(50) NOT NULL,
    CALCULATED_QUANTITY DECIMAL(20 , 10 ) NOT NULL,
    UNIT_PRICE DECIMAL(20 , 10 ) NOT NULL,
    TOTAL_COST DECIMAL(20 , 10 ) NOT NULL,
    PRODUCTION_BOOK_ID INTEGER NOT NULL
);

CREATE TABLE `USER_PRODUCT_CREATION_REQUEST` (
  `PRODUCT_CREATION_ID` int NOT NULL AUTO_INCREMENT,
  `PRODUCT_NAME` varchar(255) NOT NULL,
  `PRODUCT_DESCRIPTION` varchar(1000) DEFAULT NULL,
  `CATEGORY_ID` int NOT NULL,
  `SUPPORTS_LOOSE_ORDERING` varchar(1) DEFAULT NULL,
  `CREATION_DATE` timestamp NULL DEFAULT NULL,
  `CREATED_BY` int NOT NULL,
  `HAS_INNER` varchar(1) DEFAULT NULL,
  `HAS_CASE` varchar(1) DEFAULT NULL,
  `STOCK_KEEPING_FREQUENCY` varchar(15) NOT NULL,
  `PRODUCT_CODE` varchar(30) DEFAULT NULL,
  `SHELF_LIFE_IN_DAYS` int NOT NULL,
  `PRODUCT_STATUS` varchar(45) NOT NULL,
  `UNIT_OF_MEASURE` varchar(15) NOT NULL,
  `PARTICIPATES_IN_RECIPE` varchar(1) NOT NULL DEFAULT 'N',
  `PARTICIPATES_IN_CAFE_RECIPE` varchar(1) NOT NULL DEFAULT 'N',
  `VARIANT_LEVEL_ORDERING` varchar(1) NOT NULL DEFAULT 'N',
  `PRODUCT_IMAGE` varchar(255) DEFAULT NULL,
  `SUB_CATEGORY_ID` int DEFAULT NULL,
  `SUPPORTS_SPECIALIZED_ORDERING` varchar(1) NOT NULL DEFAULT 'N',
  `UNIT_PRICE` decimal(16,6) DEFAULT NULL,
  `NEGOTIATED_UNIT_PRICE` decimal(16,6) DEFAULT NULL,
  `TAX_CATEGORY_CODE` varchar(50) DEFAULT NULL,
  `FULFILLMENT_TYPE` varchar(30) NOT NULL,
  `DEFAULT_FULFILLMENT_TYPE` varchar(30) DEFAULT NULL,
  `AVAILABLE_AT_CAFE` varchar(1) NOT NULL DEFAULT 'Y',
  `AVAILABLE_FOR_CAFE_INVENTORY` varchar(1) DEFAULT 'Y',
  `ASSET_ORDERING` varchar(1) DEFAULT NULL,
  `VARIANCE_TYPE` varchar(30) DEFAULT NULL,
  `KITCHEN_VARIANCE_TYPE` varchar(30) DEFAULT NULL,
  `AUTO_PRODUCTION` varchar(1) NOT NULL DEFAULT 'N',
  `PARTICIPATES_IN_PNL` varchar(1) NOT NULL DEFAULT 'Y',
  `IS_BULK_GR_ALLOWED` varchar(1) DEFAULT NULL,
  `PROFILE_ID` int DEFAULT NULL,
  `DIVISION_ID` int DEFAULT NULL,
  `DEPARTMENT_ID` int DEFAULT NULL,
  `CLASSIFICATION_ID` int DEFAULT NULL,
  `SUB_CLASSIFICATION_ID` int DEFAULT NULL,
  `RECIPE_REQUIRED` varchar(1) NOT NULL DEFAULT 'N',
  `PRODUCT_TYPE` varchar(100) NOT NULL,
  `INTER_CAFE_TRANSFER` varchar(1) NOT NULL DEFAULT 'Y',
  `HOD_ID` int DEFAULT NULL,
  PRIMARY KEY (`PRODUCT_CREATION_ID`),
);

CREATE TABLE `USER_PRODUCT_CREATION_REQUEST_LOGS` (
  `PRODUCT_CREATION_LOG_ID` int NOT NULL AUTO_INCREMENT,
  `FROM_STATUS` varchar(45) DEFAULT NULL,
  `TO_STATUS` varchar(45) NOT NULL,
  `UPDATED_BY` int NOT NULL,
  `UPDATED_AT` datetime NOT NULL,
  `PRODUCT_CREATION_ID` int NOT NULL,
  PRIMARY KEY (`PRODUCT_CREATION_LOG_ID`)
);

ALTER TABLE PRODUCT_DEFINITION
ADD COLUMN `PRODUCT_CREATION_ID` INT NULL DEFAULT NULL AFTER `INTER_CAFE_TRANSFER`;


ALTER TABLE `KETTLE_SCM_STAGE`.`VENDOR_REGISTRATION_REQUEST_DETAIL`
ADD COLUMN `DOCUMENT_ID` INT NULL DEFAULT NULL AFTER `PAN_CARD`;

ALTER TABLE `KETTLE_SCM_STAGE`.`VENDOR_DETAIL_DATA`
ADD COLUMN `DOCUMENT_ID` INT NULL DEFAULT NULL AFTER `CAN_CREATE_CONTRACT`;


ALTER TABLE `kettle_scm_stage`.`SKU_DEFINITION`
DROP INDEX `SKU_NAME_UNIQUE` ;


CREATE TABLE `kettle_scm_stage`.`SKU_CREATION_REQUEST_LOGS` (
  `SKU_CREATION_LOG_ID` INT NOT NULL AUTO_INCREMENT,
  `SKU_ID` INT NOT NULL,
  `UPDATED_BY` INT NOT NULL,
  `UPDATED_AT` TIMESTAMP NULL,
  `FROM_STATUS` VARCHAR(45) NULL,
  `TO_STATUS` VARCHAR(45) NULL,
  PRIMARY KEY (`SKU_CREATION_LOG_ID`)
);

ALTER TABLE `KETTLE_SCM_STAGE`.`PRODUCT_DEFINITION`
ADD COLUMN `BRAND_ID` INT NULL DEFAULT NULL AFTER `CATEGORY_LEVEL`,
ADD COLUMN `COMPANY_ID` INT NULL DEFAULT NULL AFTER `BRAND_ID`;

ALTER TABLE `KETTLE_SCM_STAGE`.`USER_PRODUCT_CREATION_REQUEST`
ADD COLUMN `BRAND_ID` INT NULL DEFAULT NULL AFTER `HOD_ID`,
ADD COLUMN `COMPANY_ID` INT NULL DEFAULT NULL AFTER `BRAND_ID`;


ALTER TABLE KETTLE_SCM_STAGE.PRODUCT_DEFINITION
ADD COLUMN APPROVAL_DOCUMENT_ID INT NULL DEFAULT NULL AFTER COMPANY_ID;

ALTER TABLE KETTLE_SCM_STAGE.USER_PRODUCT_CREATION_REQUEST
ADD COLUMN APPROVAL_DOCUMENT_ID INT NULL DEFAULT NULL AFTER COMPANY_ID;

ALTER TABLE KETTLE_SCM_STAGE.SKU_DEFINITION
ADD COLUMN APPROVAL_DOC_ID INT NULL DEFAULT NULL;

--Vendor Contract changes for Work Order
ALTER TABLE `KETTLE_SCM_STAGE`.`BYPASS_VENDOR_CONTRACT`
CHANGE COLUMN `BYPASS_CONTRACT_ID` `CONTRACT_ID` INT NOT NULL AUTO_INCREMENT ,
CHANGE COLUMN `VENDOR_ID` `VENDOR_ID` INT NOT NULL , RENAME TO  `KETTLE_SCM_STAGE`.`VENDOR_CONTRACT_DATA` ;

ALTER TABLE `KETTLE_SCM_STAGE`.`BYPASS_VENDOR_CONTRACT_ITEM`
CHANGE COLUMN `BYPASS_CONTRACT_ID` `CONTRACT_ID` INT NOT NULL ;

ALTER TABLE `KETTLE_SCM_STAGE`.`BYPASS_VENDOR_CONTRACT_STATUS_LOG`
CHANGE COLUMN `BYPASS_CONTRACT_ID` `CONTRACT_ID` INT NOT NULL ;

ALTER TABLE `KETTLE_SCM_STAGE`.`BYPASS_VENDOR_CONTRACT_ITEM`
CHANGE COLUMN `BYPASS_CONTRACT_ITEM_ID` `CONTRACT_ITEM_ID` INT NOT NULL AUTO_INCREMENT , RENAME TO  `KETTLE_SCM_STAGE`.`VENDOR_CONTRACT_ITEM_DATA` ;

ALTER TABLE `KETTLE_SCM_STAGE`.`BYPASS_VENDOR_CONTRACT_ITEM_UNIT`
CHANGE COLUMN `BYPASS_CONTRACT_ITEM_UNIT_ID` `CONTRACT_ITEM_UNIT_ID` INT NOT NULL AUTO_INCREMENT ,
CHANGE COLUMN `BYPASS_CONTRACT_ITEM_ID` `CONTRACT_ITEM_ID` INT NOT NULL , RENAME TO  `KETTLE_SCM_STAGE`.`VENDOR_CONTRACT_ITEM_UNIT` ;

ALTER TABLE `KETTLE_SCM_STAGE`.`BYPASS_VENDOR_CONTRACT_STATUS_LOG`
ADD COLUMN `LOG_TYPE` VARCHAR(45) NULL AFTER `LOG_ID`,
CHANGE COLUMN `CONTRACT_ID` `LOG_TYPE_ID` INT NOT NULL AFTER `LOG_TYPE`,
CHANGE COLUMN `BYPASS_VENDOR_CONTRACT_STATUS_LOG_ID` `LOG_ID` INT NOT NULL AUTO_INCREMENT ,
CHANGE COLUMN `FROM_STATUS` `FROM` VARCHAR(45) NULL DEFAULT NULL ,
CHANGE COLUMN `TO_STATUS` `TO` VARCHAR(45) NULL DEFAULT NULL , RENAME TO  `KETTLE_SCM_STAGE`.`VENDOR_CONTRACT_LOGS` ;

ALTER TABLE `KETTLE_SCM_STAGE`.`VENDOR_CONTRACT_INFO`
RENAME TO  `KETTLE_SCM_STAGE`.`VENDOR_CONTRACT_INFO_OLD` ;

ALTER TABLE `KETTLE_SCM_STAGE`.`VENDOR_CONTRACT_ITEM`
RENAME TO  `KETTLE_SCM_STAGE`.`VENDOR_CONTRACT_ITEM_OLD` ;

ALTER TABLE `KETTLE_SCM_STAGE`.`VENDOR_CONTRACT_LOGS`
ADD COLUMN `LOG_MESSAGE` VARCHAR(255) NULL AFTER `UPDATED_TIME`;

ALTER TABLE `KETTLE_SCM_STAGE`.`VENDOR_CONTRACT_ITEM_DATA`
CHANGE COLUMN `CONTRACT_ID` `WORK_ORDER_ID` INT NOT NULL ;

CREATE TABLE `KETTLE_SCM_STAGE`.`WORK_ORDER_DATA` (
  `WORK_ORDER_ID` INT NOT NULL AUTO_INCREMENT,
  `CONTRACT_ID` INT NOT NULL,
  `GENERATED_WORK_ORDER_NUMBER` VARCHAR(45) NULL,
  `WORK_ORDER_TYPE` VARCHAR(45) NULL,
  `START_DATE` DATE NULL,
  `END_DATE` DATE NULL,
  `WORK_ORDER_DOC_ID` INT NULL,
  `WORK_ORDER_STATUS` VARCHAR(45) NULL,
  `APPROVAL_REQUEST_FROM` INT NULL,
  `IS_BYPASSED` VARCHAR(2) NULL,
  PRIMARY KEY (`WORK_ORDER_ID`),
  UNIQUE INDEX `WORK_ORDER_ID_UNIQUE` (`WORK_ORDER_ID` ASC) VISIBLE);

ALTER TABLE `KETTLE_SCM_STAGE`.`VENDOR_CONTRACT_ITEM_DATA`
CHANGE COLUMN `WORK_ORDER_ID` `CONTRACT_ID` INT NOT NULL ;

ALTER TABLE `KETTLE_SCM_STAGE`.`WORK_ORDER_DATA`
ADD COLUMN `VENDOR_APPROVAL_MAIL_TRIGGERED_TIME` DATE NULL DEFAULT NULL AFTER `IS_BYPASSED`;

ALTER TABLE `KETTLE_SCM_STAGE`.`VENDOR_CONTRACT_ITEM_DATA`
ADD COLUMN `WORK_ORDER_ID` INT NULL DEFAULT NULL AFTER `IS_NEW_ITEM`;

ALTER TABLE `KETTLE_SCM_STAGE`.`VENDOR_CONTRACT_ITEM_DATA`
CHANGE COLUMN `WORK_ORDER_ID` `WORK_ORDER_ID` INT NULL DEFAULT NULL AFTER `CONTRACT_ID`;

ALTER TABLE `KETTLE_SCM_STAGE`.`WORK_ORDER_DATA`
ADD COLUMN `CREATED_BY` INT NULL DEFAULT NULL AFTER `END_DATE`,
ADD COLUMN `CREATED_AT` DATE NULL DEFAULT NULL AFTER `VENDOR_APPROVAL_MAIL_TRIGGERED_TIME`;

ALTER TABLE `KETTLE_SCM_STAGE`.`VENDOR_CONTRACT_LOGS`
CHANGE COLUMN `FROM` `FROM_STATE` VARCHAR(45) NULL DEFAULT NULL ,
CHANGE COLUMN `TO` `TO_STATE` VARCHAR(45) NULL DEFAULT NULL ;

ALTER TABLE `KETTLE_SCM_STAGE`.`VENDOR_CONTRACT_ITEM_DATA`
CHANGE COLUMN `CONTRACT_ID` `CONTRACT_ID` INT NULL DEFAULT NULL ;

ALTER TABLE `KETTLE_SCM_STAGE`.`VENDOR_CONTRACT_LOGS`
CHANGE COLUMN `FROM` `FROM_STATE` VARCHAR(45) NULL DEFAULT NULL ,
CHANGE COLUMN `TO` `TO_STATE` VARCHAR(45) NULL DEFAULT NULL ;

CREATE TABLE `KETTLE_SCM_STAGE`.`WORK_ORDER_APPROVAL_DETAIL_DATA` (
  `WORK_ORDER_ID` INT NOT NULL,
  `WORK_ORDER_DOC_ID` INT NULL,
  `AUTH_IP_ADDRESS` VARCHAR(45) NULL,
  `VENDOR_IP_ADDRESS` VARCHAR(45) NULL,
  `VENDOR_USER_NAME` VARCHAR(45) NULL,
  `VENDOR_USER_DESIGNATION` VARCHAR(45) NULL,
  `VENDOR_SIGNED_DOCUMENT_ID` INT NULL,
  `AUTH_SIGNED_DOCUMENT_ID` INT NULL,
  `VENDOR_DIGITAL_SIGN_ID` INT NULL,
  `TEMPLATE_ID` INT NULL,
  `VENDOR_APPROVAL_MAIL_TRIGGERED_TIME` DATE NULL,
  PRIMARY KEY (`WORK_ORDER_ID`),
  UNIQUE INDEX `WORK_ORDER_ID_UNIQUE` (`WORK_ORDER_ID` ASC) VISIBLE);

ALTER TABLE `KETTLE_SCM_STAGE`.`WORK_ORDER_DATA`
DROP COLUMN `VENDOR_APPROVAL_MAIL_TRIGGERED_TIME`,
DROP COLUMN `WORK_ORDER_DOC_ID`,
CHANGE COLUMN `GENERATED_WORK_ORDER_NUMBER` `GENERATED_WORK_ORDER_NUMBER` VARCHAR(45) NULL DEFAULT NULL AFTER `WORK_ORDER_ID`;

ALTER TABLE `KETTLE_SCM_STAGE`.`VENDOR_CONTRACT_ITEM_DATA`
ADD COLUMN `SKU_PRICE_STATE` VARCHAR(45) NULL DEFAULT NULL AFTER `IS_NEW_ITEM`;

ALTER TABLE `KETTLE_SCM_STAGE`.`WORK_ORDER_APPROVAL_DETAIL_DATA`
ADD COLUMN `AUTH_DIGITAL_SIGN_ID` INT NULL DEFAULT NULL AFTER `VENDOR_APPROVAL_MAIL_TRIGGERED_TIME`, RENAME TO  `KETTLE_SCM_STAGE`.`WORK_ORDER_APPROVAL_METADATA` ;

ALTER TABLE `KETTLE_SCM_STAGE`.`WORK_ORDER_DATA`
CHANGE COLUMN `CREATED_AT` `CREATED_AT` TIMESTAMP NULL DEFAULT NULL ;

ALTER TABLE `KETTLE_SCM_STAGE`.`WORK_ORDER_APPROVAL_METADATA`
ADD COLUMN `UNSIGNED_DOCUMENT_ID` INT NULL DEFAULT NULL AFTER `WORK_ORDER_DOC_ID`;

ALTER TABLE `KETTLE_SCM_STAGE`.`SKU_PRICE_DATA`
CHANGE COLUMN `DISPATCH_LOCATION` `DISPATCH_LOCATION` VARCHAR(50) NULL DEFAULT NULL ,
CHANGE COLUMN `DELIVERY_LOCATION` `DELIVERY_LOCATION` VARCHAR(50) NULL ;

INSERT INTO `KETTLE_MASTER_STAGE`.`PRE_AUTHENTICATED_API` (`API`, `STATUS`) VALUES ('scm-service.vendor-contract-management.vendor-action-by-mail', 'ACTIVE');

ALTER TABLE `KETTLE_SCM_STAGE`.`WORK_ORDER_APPROVAL_METADATA`
CHANGE COLUMN `VENDOR_APPROVAL_MAIL_TRIGGERED_TIME` `VENDOR_APPROVAL_MAIL_TRIGGERED_TIME` TIMESTAMP NULL DEFAULT NULL ;

-- customer mapping ACL in Vendor Price Management
INSERT INTO KETTLE_MASTER_STAGE.USER_ROLE_DATA (ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS, APPLICATION_ID)
VALUES ('SUMO customer mapping', 'SUMO customer mapping', 'ACTIVE', '7');

INSERT INTO KETTLE_MASTER_STAGE.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('SCM_CUS_MAP', '7', 'ACTION', 'SHOW', 'Sumo -> Vendor Price Management ->  Vendor To Sku Price Mapping', 'ACTIVE');

INSERT INTO KETTLE_MASTER_STAGE.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
    VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_STAGE.USER_ROLE_DATA WHERE ROLE_NAME = 'SUMO customer mapping'),
    (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_STAGE.ACTION_DETAIL WHERE ACTION_CODE = 'SCM_CUS_MAP'), 'ACTIVE', '140199', '2025-06-17 12:00:00');

-- customer mapping approver ACL in Vendor Price Management
INSERT INTO KETTLE_MASTER_STAGE.USER_ROLE_DATA (ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS, APPLICATION_ID)
VALUES ('SUMO customer mapping approver', 'SUMO customer mapping approver', 'ACTIVE', '7');

INSERT INTO KETTLE_MASTER_STAGE.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('SCM_CUS_MAP_APP', '7', 'ACTION', 'SHOW', 'Sumo -> Vendor Price Management ->  Vendor To Sku Price Mapping', 'ACTIVE');

INSERT INTO KETTLE_MASTER_STAGE.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
    VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_STAGE.USER_ROLE_DATA WHERE ROLE_NAME = 'SUMO customer mapping approver'),
    (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_STAGE.ACTION_DETAIL WHERE ACTION_CODE = 'SCM_CUS_MAP_APP'), 'ACTIVE', '140199', '2025-06-17 12:00:00');


-- vendor mapping ACL in Vendor Price Management
INSERT INTO KETTLE_MASTER_STAGE.USER_ROLE_DATA (ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS, APPLICATION_ID)
VALUES ('SUMO vendor mapping', 'SUMO vendor mapping', 'ACTIVE', '7');

INSERT INTO KETTLE_MASTER_STAGE.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('SCM_VEN_MAP', '7', 'ACTION', 'SHOW', 'Sumo -> Vendor Price Management ->  Vendor To Sku Price Mapping', 'ACTIVE');

INSERT INTO KETTLE_MASTER_STAGE.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
    VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_STAGE.USER_ROLE_DATA WHERE ROLE_NAME = 'SUMO vendor mapping'),
    (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_STAGE.ACTION_DETAIL WHERE ACTION_CODE = 'SCM_VEN_MAP'), 'ACTIVE', '140199', '2025-06-17 12:00:00');

-- vendor mapping approver ACL in Vendor Price Management
INSERT INTO KETTLE_MASTER_STAGE.USER_ROLE_DATA (ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS, APPLICATION_ID)
VALUES ('SUMO vendor mapping approver', 'SUMO vendor mapping approver', 'ACTIVE', '7');

INSERT INTO KETTLE_MASTER_STAGE.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('SCM_VEN_MAP_APP', '7', 'ACTION', 'SHOW', 'Sumo -> Vendor Price Management ->  Vendor To Sku Price Mapping', 'ACTIVE');

INSERT INTO KETTLE_MASTER_STAGE.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
    VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_STAGE.USER_ROLE_DATA WHERE ROLE_NAME = 'SUMO vendor mapping approver'),
    (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_STAGE.ACTION_DETAIL WHERE ACTION_CODE = 'SCM_VEN_MAP_APP'), 'ACTIVE', '140199', '2025-06-17 12:00:00');

-- NEW Product approver ACL in Sumo User request
INSERT INTO KETTLE_MASTER_STAGE.USER_ROLE_DATA (ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS, APPLICATION_ID)
VALUES ('SUMO NEW Product approver', 'SUMO NEW Product approver', 'ACTIVE', '7');

INSERT INTO KETTLE_MASTER_STAGE.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('SCM_PRO_APP', '7', 'ACTION', 'SHOW', 'Sumo -> Sumo User request ->  New Product Request', 'ACTIVE');

INSERT INTO KETTLE_MASTER_STAGE.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
    VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_STAGE.USER_ROLE_DATA WHERE ROLE_NAME = 'SUMO NEW Product approver'),
    (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_STAGE.ACTION_DETAIL WHERE ACTION_CODE = 'SCM_PRO_APP'), 'ACTIVE', '140199', '2025-06-17 12:00:00');

-- ADD MAPPINGS FOR ABOVE ACL's
SELECT * FROM KETTLE_MASTER_STAGE.ROLE_BRAND_MAPPING rbm ORDER BY 1 DESC;

ALTER TABLE KETTLE_SCM_STAGE.VENDOR_CONTRACT_LOGS
RENAME TO COMMON_LOGS_DATA;

UPDATE KETTLE_SCM.VENDOR_CONTRACT_DATA
SET STATUS = 'PARTIALLY_REJECTED'
WHERE STATUS = 'PARTIAL_REJECTED';


UPDATE KETTLE_SCM.WORK_ORDER_DATA
SET WORK_ORDER_STATUS = 'PARTIALLY_REJECTED'
WHERE WORK_ORDER_STATUS = 'PARTIAL_REJECTED';


UPDATE KETTLE_SCM.VENDOR_CONTRACT_LOGS
SET
    FROM_STATE = CASE
                    WHEN FROM_STATE = 'PARTIAL_REJECTED' THEN 'PARTIALLY_REJECTED'
                    ELSE FROM_STATE
                END,
    TO_STATE = CASE
                  WHEN TO_STATE = 'PARTIAL_REJECTED' THEN 'PARTIALLY_REJECTED'
                  ELSE TO_STATE
              END
WHERE FROM_STATE = 'PARTIAL_REJECTED' OR TO_STATE = 'PARTIAL_REJECTED';

CREATE TABLE REGION_PRODUCT_PACKAGING_MAPPING_DATA (
    MAPPING_ID INT AUTO_INCREMENT PRIMARY KEY,
    REGION VARCHAR(255) NOT NULL,
    PRODUCT_ID INT NOT NULL,
    PACKAGING_ID INT NOT NULL,
    MAPPING_STATUS VARCHAR(255),
    CREATED_BY INT,
    CREATION_DATE TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);



ALTER TABLE `KETTLE_SCM_STAGE`.`PURCHASE_ORDER_ITEM_DETAIL`
ADD COLUMN `DEPARTMENT_ID` INT NULL DEFAULT NULL AFTER `TYPE`;

ALTER TABLE `KETTLE_SCM_STAGE`.`VENDOR_GR_ITEM_DETAIL`
ADD COLUMN `DEPARTMENT_ID` INT NULL DEFAULT NULL AFTER `EXPIRY_DATE`;

-- Set DEPARTMENT_ID = 38 for REGULAR orders
UPDATE PURCHASE_ORDER_ITEM_DETAIL poi
JOIN PURCHASE_ORDER po ON poi.PURCHASE_ORDER_ID = po.PURCHASE_ORDER_ID
SET poi.DEPARTMENT_ID = 38
WHERE po.ORDER_TYPE = 'REGULAR_ORDER';

-- Set DEPARTMENT_ID = 37 for FIXED_ASSET_ORDER orders
UPDATE PURCHASE_ORDER_ITEM_DETAIL poi
JOIN PURCHASE_ORDER po ON poi.PURCHASE_ORDER_ID = po.PURCHASE_ORDER_ID
SET poi.DEPARTMENT_ID = 37
WHERE po.ORDER_TYPE = 'FIXED_ASSET_ORDER';

-- 1. REGULAR  →  department_id = 38
UPDATE VENDOR_GR_ITEM_DETAIL vgri
JOIN VENDOR_GOODS_RECEIVED_DATA vgr
      ON vgr.GOODS_RECEIVED_ID = vgri.VENDOR_GR_ID
SET    vgri.DEPARTMENT_ID = 38
WHERE  vgr.VENDOR_GR_TYPE = 'REGULAR_ORDER';

-- 2. FIXED_ASSET_ORDER  →  department_id = 37
UPDATE VENDOR_GR_ITEM_DETAIL vgri
JOIN VENDOR_GOODS_RECEIVED_DATA vgr
      ON vgr.GOODS_RECEIVED_ID = vgri.VENDOR_GR_ID
SET    vgri.DEPARTMENT_ID = 37
WHERE  vgr.VENDOR_GR_TYPE = 'FIXED_ASSET_ORDER';


Set department Id nre_consumables
UPDATE KETTLE_SCM_STAGE.PRODUCT_DEFINITION
SET DEPARTMENT_ID = 38
WHERE CATEGORY_ID = 2
  AND SUB_CATEGORY_ID IN (10, 11, 12, 13, 14, 15, 21, 22, 23, 24, 25, 26, 27, 30)
  AND PRODUCT_STATUS = 'ACTIVE';

set department id fa_equipment
UPDATE KETTLE_SCM_STAGE.PRODUCT_DEFINITION
SET DEPARTMENT_ID = 37
WHERE CATEGORY_ID = 3
  AND SUB_CATEGORY_ID IN (16, 17, 18, 28)
  AND PRODUCT_STATUS = 'ACTIVE';


SET DEPARTMENT ID AS FA_FURNITURE
UPDATE KETTLE_SCM_STAGE.PRODUCT_DEFINITION
SET DEPARTMENT_ID = 36
WHERE CATEGORY_ID = 3
  AND SUB_CATEGORY_ID IN (19)
  AND PRODUCT_STATUS = 'ACTIVE';


SET DEPARTMENT ID AS FA_HVAC
UPDATE KETTLE_SCM_STAGE.PRODUCT_DEFINITION
SET DEPARTMENT_ID = 35
WHERE CATEGORY_ID = 3
  AND SUB_CATEGORY_ID IN (29)
  AND PRODUCT_STATUS = 'ACTIVE';

UPDATE KETTLE_SCM_DEV.TRANSFER_ORDER SET TRANSFER_TYPE="INVOICE" where TRANSFER_TYPE="INTER_STATE";
UPDATE KETTLE_SCM_DEV.TRANSFER_ORDER SET TRANSFER_TYPE="TRANSFER" where TRANSFER_TYPE="INTERNAL";

INSERT INTO KETTLE_SCM_DEV.VEHICLE_DATA (REGISTRATION_NUMBER, VEHICLE_NAME, MOD<PERSON>, MAKE, TRANSPORT_MODE, VEHICLE_STATUS, IS_MULTI_DISPATCH)
VALUES ('DL01EE1487', 'EMERGENCY', 'TRUCK', 'EICHER', 'ROAD', 'ACTIVE', 'N');

ALTER TABLE KETTLE_SCM_DEV.VEHICLE_DISPATCH_DATA ADD COLUMN VEHICLE_NUMBER VARCHAR(30);

UPDATE KETTLE_SCM_DEV.VEHICLE_DISPATCH_DATA vdd
INNER JOIN KETTLE_SCM_DEV.VEHICLE_DATA vd ON vd.VEHICLE_ID = vdd.VEHICLE_ID
SET vdd.VEHICLE_NUMBER = vd.REGISTRATION_NUMBER;

ALTER TABLE KETTLE_SCM_DEV.SERVICE_ORDER ADD COLUMN SO_INVOICE_ID INTEGER;

CREATE TABLE KETTLE_SCM_DEV.SERVICE_ORDER_NOTIFICATION (
NOTIFICATION_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
CONTACT VARCHAR(100), 
MESSAGE VARCHAR(150), 
SERVICE_CLIENT VARCHAR(150), 
NOTIFICATION_CARRIER_TYPE  VARCHAR(15),  
NOTIFICATION_TYPE  VARCHAR(15), 
NOTIFICATION_SENT  VARCHAR(15), 
NOTIFICATION_TIME TIMESTAMP NULL, 
SERVICE_ORDER_ID  INTEGER
);

CREATE INDEX SERVICE_ORDER_ID_SERVICE_ORDER_NOTIFICATION ON KETTLE_SCM_DEV.SERVICE_ORDER_NOTIFICATION(SERVICE_ORDER_ID) USING BTREE;

CREATE TABLE KETTLE_SCM_DEV.MONK_WASTAGE_DETAIL_DATA (
MONK_WASTAGE_DETAIL_DATA_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
WASTAGE_ITEM_ID INTEGER,
TASK_ID INTEGER,
ORDER_ID INTEGER,
QUANTITY DECIMAL (16,6),
MONK_EVENT VARCHAR(150),
CHAI_MONK VARCHAR(100)
);

ALTER TABLE KETTLE_SCM_DEV.SERVICE_RECEIVED_DATA ADD COLUMN DELIVERY_STATE_ID INTEGER;
set sql_safe_updates=0;
UPDATE KETTLE_SCM_DEV.SERVICE_RECEIVED_DATA r
INNER JOIN KETTLE_MASTER_DEV.LOCATION_DETAIL s on r.DELIVERY_LOCATION_ID = s.LOCATION_ID
set r.DELIVERY_STATE_ID = s.STATE_DETAIL_ID where r.DELIVERY_STATE_ID is null;
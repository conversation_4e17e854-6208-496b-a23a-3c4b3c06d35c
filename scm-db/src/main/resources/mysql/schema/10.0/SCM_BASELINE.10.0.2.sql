DROP TABLE IF EXISTS KETTLE_SCM_DEV.SERVICE_RECEIVED_ITEM_DRILLDOWN;

CREATE TABLE KETTLE_SCM_DEV.SERVICE_RECEIVED_ITEM_DRILLDOWN (
                                                   `SERVICE_RECEIVED_ITEM_DRILLDOWN_ID` int(11) NOT NULL AUTO_INCREMENT,
                                                   `SERVICE_RECEIVED_ITEM_ID` int(11) NOT NULL,
                                                   `DESCRIPTION` varchar(1000) DEFAULT NULL,
                                                   `WIDTH` decimal(16,6) DEFAULT NULL,
                                                   `HEIGHT` decimal(16,6) DEFAULT NULL,
                                                   `LENGTH` decimal(16,6) DEFAULT NULL,
                                                   `NOS` int(11) DEFAULT NULL,
                                                   `RECEIVED_QUANTITY` decimal(16,6) DEFAULT NULL,
                                                   `UOM` varchar(20) DEFAULT NULL ,
                                                   PRIMARY KEY (`SERVICE_RECEIVED_ITEM_DRILLDOWN_ID`)

);

CREATE INDEX SERVICE_RECEIVED_ITEM_DRILLDOWN_ID ON KETTLE_SCM_DEV.SERVICE_RECEIVED_ITEM_DRILLDOWN(SERVICE_RECEIVED_ITEM_DRILLDOWN_ID) USING BTREE;
CREATE INDEX SERVICE_RECEIVED_ITEM_DRILLDOWN_ITEM_ID ON KETTLE_SCM_DEV.SERVICE_RECEIVED_ITEM_DRILLDOWN(SERVICE_RECEIVED_ITEM_ID) USING BTREE;

ALTER TABLE KETTLE_SCM_DEV.SERVICE_RECEIVED_ITEM_DRILLDOWN CHANGE COLUMN `UOM` `SOURCE_UOM` VARCHAR(20) NULL DEFAULT NULL ;


ALTER TABLE KETTLE_SCM_DEV.SERVICE_RECEIVED_DATA ADD COLUMN `TYPE` VARCHAR(20) NULL;

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('SRAPPRV', '12', 'ACTION', 'APPROVE', 'SERVICE RECEIVING -> APPROVE', 'ACTIVE');


INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'SCM_APPROVER_L1'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'SRAPPRV'), 'ACTIVE', '120063', '2022-05-23 12:00:00');




ALTER TABLE KETTLE_SCM_DEV.DAY_CLOSE_EVENT ADD COLUMN IS_ORDERING_SUCCESS VARCHAR(1) NULL;

CREATE TABLE `KETTLE_SCM_DEV`.`UNIT_ORDER_SCHEDULE` (
    `ORDER_SCHEDULE_ID` INT(11) NOT NULL AUTO_INCREMENT,
    `UNIT_BRAND_ID` INT(11) NOT NULL,
    `ORDERING_DAY` VARCHAR(10) NOT NULL,
    `IS_FUNCTIONAL` VARCHAR(1) NOT NULL,
    `IS_ORDERING_DAY` VARCHAR(1) NOT NULL,
    `ORDERING_DAYS` INT(11) NULL,
    PRIMARY KEY (`ORDER_SCHEDULE_ID`),
    CONSTRAINT
    FOREIGN KEY (`UNIT_BRAND_ID`)
    REFERENCES KETTLE_SCM_DEV.REGULAR_ORDER_UNIT_BRAND_DATA (`UNIT_BRAND_ID`)
);

CREATE TABLE `KETTLE_SCM_DEV`.`REGULAR_ORDER_UNIT_BRAND_DATA` (
      `UNIT_BRAND_ID` INT(11) NOT NULL AUTO_INCREMENT,
      `UNIT_ID` INT(11) NOT NULL,
      `BRAND_ID` INT(11) NOT NULL,
      `IS_FUNCTIONAL` VARCHAR(1) NOT NULL,
      `IS_MANUAL` VARCHAR(1) NOT NULL DEFAULT "Y",
      `CREATED_BY` INT(11) NOT NULL,
      `CREATED_AT` TIMESTAMP NOT NULL,
      `LAST_UPDATED_BY` INT(11) NULL,
      `LAST_UPDATED_TIME` TIMESTAMP NULL,
      PRIMARY KEY (`UNIT_BRAND_ID`)
);


CREATE TABLE `KETTLE_SCM_DEV`.`REGULAR_ORDERING_EVENTS` (
    `EVENT_ID` INT(11) NOT NULL AUTO_INCREMENT,
    `DAY_CLOSE_ID` INT(11) NOT NULL,
    `UNIT_ID` INT(11) NOT NULL,
    `BRAND` VARCHAR(100) NOT NULL,
    `FULFILMENT_DATE` TIMESTAMP NULL,
    `ORDERING_DAYS` INT(11) NOT NULL,
    `STATUS` VARCHAR(20) NOT NULL,
    `REFERENCE_ORDER_ID` INT(11) NULL,
    `CREATED_AT` TIMESTAMP NULL,
    `LAST_UPDATED_TIME` TIMESTAMP NULL,
    PRIMARY KEY (`EVENT_ID`),
    CONSTRAINT
    FOREIGN KEY (`DAY_CLOSE_ID`)
    REFERENCES KETTLE_SCM_DEV.DAY_CLOSE_EVENT (`EVENT_ID`),
    CONSTRAINT
    FOREIGN KEY (`REFERENCE_ORDER_ID`)
    REFERENCES KETTLE_SCM_DEV.REFERENCE_ORDER (`REFERENCE_ORDER_ID`)
);

CREATE INDEX CAFE_ID_REGULAR_ORDERING_FORECAST_DATA ON KETTLE_SCM_DEV.REGULAR_ORDERING_FORECAST_DATA(CAFE_ID) USING BTREE;

ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER_ITEM ADD COLUMN REASON VARCHAR(100) NULL;
ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER_ITEM ADD COLUMN COMMENT VARCHAR(100) NULL;

ALTER TABLE KETTLE_SCM_DEV.BULK_TRANSFER_EVENT ADD COLUMN TYPE VARCHAR(45) NULL ;

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('Bulk StandAlone TO', 'Access to see Bulk StandAlone Tab', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('bst', '7', 'SUBMENU', 'SHOW', 'SuMo ->Transfers -> Bulk StandAlone TO', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Bulk StandAlone TO'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'bst'), 'ACTIVE', '120063', '2022-09-15 12:00:00');


INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('Cafe To Cafe GNT StandAlone TO', 'Access to see Cafe To Cafe Gnt CheckBox', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('GNTCTC', '7', 'ACTION', 'VIEW', 'SuMo ->Transfers -> StandAlone TO -> Only GNT', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Cafe To Cafe GNT StandAlone TO'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'GNTCTC'), 'ACTIVE', '120063', '2022-09-21 12:00:00');

CREATE INDEX DATE_REGULAR_ORDERING_FORECAST_DATA ON KETTLE_SCM_DEV.REGULAR_ORDERING_FORECAST_DATA(DATE) USING BTREE;

ALTER TABLE `KETTLE_SCM_DEV`.`REGULAR_ORDER_UNIT_BRAND_DATA`
ADD COLUMN `CAPPING_STOCK_MULTIPLIER` VARCHAR(5) NULL AFTER `UNIT_ID`;

UPDATE `KETTLE_SCM_DEV`.`REGULAR_ORDER_UNIT_BRAND_DATA` SET CAPPING_STOCK_MULTIPLIER = "20";

ALTER TABLE `KETTLE_SCM_DEV`.`REGULAR_ORDERING_FORECAST_DATA`
ADD COLUMN `SAFETY_STOCK` DECIMAL(16,6) NULL DEFAULT NULL AFTER `ACTUAL`;

ALTER TABLE `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION`
ADD COLUMN `PARTICIPATES_IN_CAFE_RECIPE` VARCHAR(1) NOT NULL DEFAULT 'N' AFTER `PARTICIPATES_IN_RECIPE`;

ALTER TABLE `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION`
ADD COLUMN `KITCHEN_VARIANCE_TYPE` VARCHAR(30) NULL DEFAULT NULL AFTER `VARIANCE_TYPE`;

UPDATE `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` SET PARTICIPATES_IN_CAFE_RECIPE = PARTICIPATES_IN_RECIPE;
UPDATE `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` SET KITCHEN_VARIANCE_TYPE = VARIANCE_TYPE;

INSERT INTO KETTLE_MASTER_DEV.PARTNER_PERMISSION_MAPPING (`PARTNER_ID`, `PERMISSION`, `ACL_ID`, `PPM_STATUS`) VALUES ('4', '1111', '43', 'ACTIVE');


INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('SUMO ADMIN', 'SUMO Admins', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VRMENU', '7', 'SUBMENU', 'SHOW', 'SuMo ->Vendor Management -> Vendor Request', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'SUMO ADMIN'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VRMENU'), 'ACTIVE', '120063', '2022-10-03 12:00:00');


INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'FINANCE_EXECUTIVE'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VRMENU'), 'ACTIVE', '120063', '2022-11-03 12:00:00');


INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('mnktrnsfr', '7', 'SUBMENU', 'SHOW', 'SuMo ->Transfer -> monk-recipe-converter', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'SUMO ADMIN'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'mnktrnsfr'), 'ACTIVE', '120063', '2022-10-07 12:00:00');



ALTER TABLE KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION
    ADD COLUMN `SHELF_LIFE_IN_DAYS` INT(11) NULL ,
ADD COLUMN `SHELF_LIFE_RANGE` VARCHAR(45) NULL ;

ALTER TABLE `KETTLE_SCM_DEV`.`REGULAR_ORDERING_FORECAST_DATA`
ADD COLUMN `REFERENCE_ORDER_ID` INT(11) NULL DEFAULT NULL AFTER `SAFETY_STOCK`;

CREATE TABLE `KETTLE_SCM_DEV`.`MULTI_PACKAGING_ADJUSTMENTS` (
`ITEM_PACKAGING_ID` INT(11) NOT NULL AUTO_INCREMENT,
`PACKAGING_ID` INT(11) NOT NULL,
`IS_CHECKED` VARCHAR(1) NOT NULL,
`QUANTITY` DECIMAL(16,6) NOT NULL,
`REQUEST_ORDER_ITEM_ID` INT(11) NOT NULL,
PRIMARY KEY (`ITEM_PACKAGING_ID`));

CREATE INDEX REFERENCE_ORDER_ID_REGULAR_ORDERING_FORECAST_DATA ON KETTLE_SCM_DEV.REGULAR_ORDERING_FORECAST_DATA(REFERENCE_ORDER_ID) USING BTREE;
CREATE INDEX REFRESH_DATE_REGULAR_ORDERING_FORECAST_DATA ON KETTLE_SCM_DEV.REGULAR_ORDERING_FORECAST_DATA(REFRESH_DATE) USING BTREE;

CREATE TABLE KETTLE_SCM_DEV.MAINTENANCE_WH_ZONE_MAPPING (
                                                              `MAPPING_ID` INT NOT NULL AUTO_INCREMENT,
                                                              `ZONE` VARCHAR(45) NOT NULL,
                                                              `UNIT_ID` INT(11) NOT NULL,
                                                              `MAPPING_STATUS` VARCHAR(45) NOT NULL,
                                                              PRIMARY KEY (`MAPPING_ID`));

CREATE INDEX ZONE_INDEX ON KETTLE_SCM_DEV.MAINTANCE_WH_ZONE_MAPPING(ZONE) USING BTREE;

INSERT INTO `KETTLE_SCM_DEV`.`MAINTENANCE_WH_ZONE_MAPPING` (`ZONE`, `UNIT_ID` ,`MAPPING_STATUS`) VALUES ('NORTH', '26521','ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`MAINTENANCE_WH_ZONE_MAPPING` (`ZONE`, `UNIT_ID`, `MAPPING_STATUS`) VALUES ('SOUTH', '26531','ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`MAINTENANCE_WH_ZONE_MAPPING` (`ZONE`, `UNIT_ID`, `MAPPING_STATUS`) VALUES ('WEST', '26530','ACTIVE');

ALTER TABLE `KETTLE_SCM_DEV`.`REGULAR_ORDER_UNIT_BRAND_DATA`
CHANGE COLUMN `CREATED_AT` `CREATED_AT` TIMESTAMP NULL DEFAULT NULL ;

UPDATE `KETTLE_SCM_DEV`.`REGULAR_ORDER_UNIT_BRAND_DATA` SET CAPPING_STOCK_MULTIPLIER="10" WHERE BRAND_ID = 1;

ALTER TABLE `KETTLE_SCM_DEV`.`MULTI_PACKAGING_ADJUSTMENTS`
ADD COLUMN `LAST_UPDATED_BY` VARCHAR(100) NULL DEFAULT NULL AFTER `REQUEST_ORDER_ITEM_ID`,
ADD COLUMN `LAST_UPDATED_TIME` TIMESTAMP NULL DEFAULT NULL AFTER `LAST_UPDATED_BY`;

ALTER TABLE `KETTLE_SCM_DEV`.`REQUEST_ORDER`
ADD COLUMN `ALTERNATE_F9_ORDER` VARCHAR(1) NULL DEFAULT "N" AFTER `ASSET_ORDER`;
ALTER TABLE KETTLE_SCM_DEV.UNIT_SKU_MAPPING
    ADD COLUMN `TAX_CODE` VARCHAR(50) NULL ;

ALTER TABLE KETTLE_SCM_DEV.SKU_DEFINITION
    ADD COLUMN TAX_CATEGORY_CODE VARCHAR(50) NULL ;

update KETTLE_SCM_DEV.SKU_DEFINITION   set TAX_CATEGORY_CODE = (select TAX_CATEGORY_CODE FROM KETTLE_SCM_DEV.PRODUCT_DEFINITION WHERE PRODUCT_ID = LINKED_PRODUCT_ID );

CREATE TABLE KETTLE_SCM_DEV.SKU_PACKAGING_TAX_MAPPING (
                                                              `SKU_PACKAGING_TAX_MAPPING_ID` INT NOT NULL AUTO_INCREMENT,
                                                              `UNIT_ID` INT(11) NOT NULL,
                                                              `SKU_ID` INT(11) NOT NULL,
                                                              `PACKAGING_ID` INT(11) NOT NULL,
                                                              `MAPPING_STATUS` VARCHAR(45) NOT NULL,
                                                              `TAX_CODE` VARCHAR(50)  NULL,
                                                              `CREATED_AT` TIMESTAMP NULL,
                                                              `CREATED_BY` INT(11) NULL,
                                                              `UPDATED_AT` TIMESTAMP NULL,
                                                              `UPDATED_BY` INT(11) NULL,

                                                               PRIMARY KEY (`SKU_PACKAGING_TAX_MAPPING_ID`));

CREATE INDEX UNIT_ID_SKU_PACKAGING_TAX_MAPPING ON KETTLE_SCM_DEV.SKU_PACKAGING_TAX_MAPPING(UNIT_ID) USING BTREE;
CREATE INDEX SKU_ID_SKU_PACKAGING_TAX_MAPPING ON KETTLE_SCM_DEV.SKU_PACKAGING_TAX_MAPPING(SKU_ID) USING BTREE;







INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('Cancel Fountain9 RO', 'Access to Cancel Fountain9 RO', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('CF9RO', '7', 'ACTION', 'VIEW', 'SuMo -> Manage Transactions -> Request Order management -> RO -> CANCEL', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Cancel Fountain9 RO'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'CF9RO'), 'ACTIVE', '120063', '2022-12-02 12:00:00');

ALTER TABLE KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA
    ADD COLUMN `COMMENT` VARCHAR(1000) NULL ;

CREATE TABLE KETTLE_SCM_DEV.AUDIT_LOG_DATA (
                                                   `AUDIT_LOG_DATA_ID` INT(11) NOT NULL,
                                                   `KEY_ID` INT(11) NOT NULL,
                                                   `KEY_TYPE` VARCHAR(45) NOT NULL,
                                                   `CHANGE_TYPE` VARCHAR(45) NULL,
                                                   `CREATED_ON` TIMESTAMP NULL,
                                                   `UPDATED_ON` TIMESTAMP NULL,
                                                   `CHANGED_BY` INT(11) NULL,
                                                   `VERSION` INT(11) NULL,
                                                   `OBJECT_ID` INT(11) NOT NULL,
                                                   PRIMARY KEY (`AUDIT_LOG_DATA_ID`));

CREATE TABLE KETTLE_SCM_DEV.PRODUCT_DEFINITION_LOGS (
                                           `LOG_ID` int(11) NOT NULL AUTO_INCREMENT,
                                           `PRODUCT_ID` int(11) NOT NULL,
                                           `PRODUCT_NAME` varchar(255) NOT NULL,
                                           `PRODUCT_DESCRIPTION` varchar(1000) DEFAULT NULL,
                                           `CATEGORY_ID` int(11) NOT NULL,
                                           `SUPPORTS_LOOSE_ORDERING` varchar(1) NOT NULL,
                                           `CREATION_DATE` timestamp NULL DEFAULT NULL,
                                           `CREATED_BY` int(11) NOT NULL,
                                           `HAS_INNER` varchar(1) NOT NULL,
                                           `HAS_CASE` varchar(1) NOT NULL,
                                           `STOCK_KEEPING_FREQUENCY` varchar(15) NOT NULL,
                                           `PRODUCT_CODE` varchar(30) DEFAULT NULL,
                                           `SHELF_LIFE_IN_DAYS` int(11) NOT NULL,
                                           `PRODUCT_STATUS` varchar(15) NOT NULL,
                                           `UNIT_OF_MEASURE` varchar(15) NOT NULL,
                                           `PARTICIPATES_IN_RECIPE` varchar(1) NOT NULL DEFAULT 'N',
                                           `PARTICIPATES_IN_CAFE_RECIPE` varchar(1) NOT NULL DEFAULT 'N',
                                           `VARIANT_LEVEL_ORDERING` varchar(1) NOT NULL DEFAULT 'N',
                                           `PRODUCT_IMAGE` varchar(255) DEFAULT NULL,
                                           `SUB_CATEGORY_ID` int(11) DEFAULT NULL,
                                           `SUPPORTS_SPECIALIZED_ORDERING` varchar(1) NOT NULL DEFAULT 'N',
                                           `UNIT_PRICE` decimal(16,6) DEFAULT NULL,
                                           `NEGOTIATED_UNIT_PRICE` decimal(16,6) DEFAULT NULL,
                                           `TAX_CATEGORY_CODE` varchar(50) DEFAULT NULL,
                                           `FULFILLMENT_TYPE` varchar(30) NOT NULL,
                                           `DEFAULT_FULFILLMENT_TYPE` varchar(30) DEFAULT NULL,
                                           `AVAILABLE_AT_CAFE` varchar(1) NOT NULL DEFAULT 'Y',
                                           `AVAILABLE_FOR_CAFE_INVENTORY` varchar(1) DEFAULT 'Y',
                                           `ASSET_ORDERING` varchar(1) DEFAULT NULL,
                                           `VARIANCE_TYPE` varchar(30) DEFAULT NULL,
                                           `KITCHEN_VARIANCE_TYPE` varchar(30) DEFAULT NULL,
                                           `AUTO_PRODUCTION` varchar(1) NOT NULL DEFAULT 'N',
                                           `PARTICIPATES_IN_PNL` varchar(1) NOT NULL DEFAULT 'Y',
                                           `IS_BULK_GR_ALLOWED` varchar(1) DEFAULT NULL,
                                           `PROFILE_ID` int(11) DEFAULT NULL,
                                           `DIVISION_ID` int(11) DEFAULT NULL,
                                           `DEPARTMENT_ID` int(11) DEFAULT NULL,
                                           `CLASSIFICATION_ID` int(11) DEFAULT NULL,
                                           `SUB_CLASSIFICATION_ID` int(11) DEFAULT NULL,
                                           `RECIPE_REQUIRED` varchar(1) NOT NULL DEFAULT 'N',
                                           `INTER_CAFE_TRANSFER` varchar(1) NOT NULL DEFAULT 'Y',
                                           PRIMARY KEY (`LOG_ID`)
) ;



CREATE TABLE `KETTLE_SCM_DEV`.`REGULAR_ORDERING_SCM_FORECAST_DATA` (
`SCM_FORECAST_ID` INT(20) NOT NULL AUTO_INCREMENT,
`DATE` TIMESTAMP NULL DEFAULT NULL,
`CAFE_ID` VARCHAR(100) NULL DEFAULT NULL,
`SKU_ID` INT(11) NULL DEFAULT NULL,
`SKU_NAME` VARCHAR(255) NULL DEFAULT NULL,
`ACTUAL` DECIMAL(16,6) NULL DEFAULT NULL,
`PREDICTED_LPI` DECIMAL(16,6) NULL DEFAULT NULL,
`PREDICTED_BASELINE` DECIMAL(16,6) NULL DEFAULT NULL,
`PREDICTED_UPI` DECIMAL(16,6) NULL DEFAULT NULL,
`PREDICTED_FINAL` DECIMAL(16,6) NULL DEFAULT NULL,
`PRICE` DECIMAL(16,6) NULL DEFAULT NULL,
`ACTUAL_VALUE` DECIMAL(16,6) NULL DEFAULT NULL,
`PREDICTED_BASELINE_VALUE` DECIMAL(16,6) NULL DEFAULT NULL,
`VALUE` DECIMAL(16,6) NULL DEFAULT NULL,
`SAFETY_STOCK` DECIMAL(16,6) NULL DEFAULT NULL,
`REFRESH_DATE` TIMESTAMP NULL DEFAULT NULL,
`REFERENCE_ORDER_ID` INT(11) NULL DEFAULT NULL,
`SUGGESTIONS_REQUESTED_BY` VARCHAR(100) NULL DEFAULT NULL,
PRIMARY KEY (`SCM_FORECAST_ID`));

ALTER TABLE `KETTLE_SCM_DEV`.`REFERENCE_ORDER_SCM_ITEM`
ADD COLUMN `PREDICTED_QUANTITY` DECIMAL(10,2) NULL AFTER `SUGGESTED_QUANTITY`;

ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER
    ADD COLUMN `TYPE` VARCHAR(45) NULL ;

ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER
    ADD COLUMN `TYPE` VARCHAR(45) NULL ;

ALTER TABLE `KETTLE_SCM_DEV`.`PAYMENT_REQUEST`
ADD COLUMN `EXTRA_CHARGES_TYPE` VARCHAR(20) NULL DEFAULT NULL AFTER `VENDOR_PAYMENT_DATE`;

ALTER TABLE `KETTLE_SCM_DEV`.`REFERENCE_ORDER_SCM_ITEM`
ADD COLUMN `REASON` VARCHAR(45) NULL DEFAULT NULL AFTER `SUGGESTED_QUANTITY`;

CREATE INDEX  IDX_REQUEST_ORDER_TYPE ON KETTLE_SCM_DEV.REQUEST_ORDER(TYPE) USING BTREE;

CREATE TABLE KETTLE_SCM_DEV.SCM_DAY_CLOSE_CHECK_EXCLUSION_LIST (
                                                                       `SCM_DAY_CLOSE_CHECK_EXCLUSION_LIST_ID` INT NOT NULL AUTO_INCREMENT,
                                                                       `UNIT_ID` INT(11) NOT NULL,
                                                                       `STATUS` VARCHAR(45) NOT NULL,
                                                                       PRIMARY KEY (`SCM_DAY_CLOSE_CHECK_EXCLUSION_LIST_ID`));

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('dsptchjson', '7', 'ACTION', 'SHOW', 'SuMo ->dispatch -> search-dispatch', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'SUMO ADMIN'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'dsptchjson'), 'ACTIVE', '120063', '2022-01-16 12:00:00');


ALTER TABLE `KETTLE_SCM_DEV`.`REGULAR_ORDERING_FORECAST_DATA`
ADD COLUMN `NON_FUNCTIONAL` VARCHAR(1) NULL DEFAULT NULL AFTER `REFERENCE_ORDER_ID`;

CREATE TABLE `KETTLE_SCM_DEV`.`RO_SCM_ITEM_EXPIRY_DRILLDOWN` (
`SCM_ITEM_EXPIRY_ID` int(11) NOT NULL AUTO_INCREMENT,
`SCM_ITEM_ID` int(11) NOT NULL,
`STOCK_TYPE` varchar(45) NOT NULL,
`EXPIRY_DATE` timestamp NULL DEFAULT NULL,
`QUANTITY` decimal(16,6) NOT NULL,
PRIMARY KEY (`SCM_ITEM_EXPIRY_ID`)
);

ALTER TABLE `KETTLE_SCM_DEV`.`PAYMENT_REQUEST`
ADD COLUMN `EXTRA_CHARGES_SGST` DECIMAL(16,6) NULL DEFAULT NULL AFTER `EXTRA_CHARGES_TYPE`,
ADD COLUMN `EXTRA_CHARGES_CGST` DECIMAL(16,6) NULL DEFAULT NULL AFTER `EXTRA_CHARGES_SGST`,
ADD COLUMN `EXTRA_CHARGES_IGST` DECIMAL(16,6) NULL DEFAULT NULL AFTER `EXTRA_CHARGES_CGST`,
ADD COLUMN `EXTRA_CHARGES_WITHOUT_TAX` DECIMAL(16,6) NULL DEFAULT NULL AFTER `EXTRA_CHARGES_IGST`;

ALTER TABLE `KETTLE_SCM_DEV`.`REQUEST_ORDER`
ADD COLUMN `BULK_ORDER` VARCHAR(1) NULL DEFAULT 'N' AFTER `TYPE`;
CREATE INDEX UNIT_ID_REGULAR_ORDER_UNIT_BRAND_DATA ON KETTLE_SCM_DEV.REGULAR_ORDER_UNIT_BRAND_DATA(UNIT_ID) USING BTREE;

ALTER TABLE `KETTLE_SCM_DEV`.`REFERENCE_ORDER_SCM_ITEM`
ADD COLUMN `REASON` VARCHAR(45) NULL DEFAULT NULL AFTER `SUGGESTED_QUANTITY`;

ALTER TABLE `KETTLE_SCM_DEV`.`REGULAR_ORDERING_SCM_FORECAST_DATA`
ADD COLUMN `NON_FUNCTIONAL` VARCHAR(1) NULL DEFAULT NULL AFTER `SUGGESTIONS_REQUESTED_BY`;

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('fatrnsfr', '7', 'ACTION', 'UPDATE', 'SuMo -> access to bypass standAlone Asset Transfers Checks', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'SUMO ADMIN'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'fatrnsfr'), 'ACTIVE', '120063', '2023-02-11 12:00:00');


INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('fatrnsfr', '7', 'ACTION', 'UPDATE', 'SuMo -> access to bypass standAlone Asset Transfers Checks', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'SUMO ADMIN'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'fatrnsfr'), 'ACTIVE', '120063', '2023-02-11 12:00:00');






CREATE TABLE KETTLE_SCM_DEV.SPECIALIZED_ORDER_INVOICE (
                                                          SPECIALIZED_ORDER_INVOICE_ID INT NOT NULL AUTO_INCREMENT,
                                                          VENDOR_ID INT(11) NULL,
                                                          UNIT_ID INT(11) NULL,
                                                          GENERATION_TIME DATETIME NULL,
                                                          IS_PR_RAISED VARCHAR(1) NULL,
                                                          PR_ID INT(11) NULL ,
                                                          INVOICE_URL VARCHAR(400) NULL,
                                                          INVOICE_ID VARCHAR(200) NULL,
                                                          DOCUMENT_ID INT(11) NULL,
                                                          PRIMARY KEY (SPECIALIZED_ORDER_INVOICE_ID));

ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED
    ADD COLUMN INVOICE_ID INT(11) NULL ;

ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER_ITEM
    ADD COLUMN PACKAGING_ID INT(11) NULL ;

CREATE TABLE KETTLE_SCM_DEV.INVOICE_EXCESS_QUANTITY (
                                                        INVOICE_EXCESS_QUANTITY_ID INT NOT NULL AUTO_INCREMENT,
                                                        INVOICE_ID INT(11) NOT NULL,
                                                        UNIT_ID INT(11) NOT NULL,
                                                        SKU_ID INT(11) NOT NULL,
                                                        SKU_NAME VARCHAR(100) NOT NULL,
                                                        EXCESS_QTY DECIMAL(10,6) NOT NULL,
                                                        UNIT_PRICE DECIMAL(10,6) NOT NULL,
                                                        PRIMARY KEY (INVOICE_EXCESS_QUANTITY_ID));


ALTER TABLE `KETTLE_SCM_DEV`.`REGULAR_ORDERING_SCM_FORECAST_DATA`
ADD COLUMN `NON_FUNCTIONAL` VARCHAR(1) NULL DEFAULT NULL AFTER `SUGGESTIONS_REQUESTED_BY`;

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('fatrnsfr', '7', 'ACTION', 'UPDATE', 'SuMo -> access to bypass standAlone Asset Transfers Checks', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'SUMO ADMIN'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'fatrnsfr'), 'ACTIVE', '120063', '2023-02-11 12:00:00');

ALTER TABLE KETTLE_SCM_DEV.`STOCK_EVENT_DEFINITION`
    ADD COLUMN `SUB_TYPE` VARCHAR(100) NULL ;

ALTER TABLE kettle_scm_dev.`STOCK_EVENT_ASSET_MAPPING`
    ADD COLUMN `MANUAL_CHECKED` VARCHAR(1) NULL ;

CREATE TABLE KETTLE_SCM_DEV.`NON_SCANNABLE_ASSET_PRODUCTS` (
                                                                 `NON_SCANNABLE_ASSET_PRODUCTS_ID` INT NOT NULL AUTO_INCREMENT,
                                                                 `PRODUCT_ID` INT(11) NOT NULL,
                                                                 `MAPPING_STATUS` VARCHAR(45) NOT NULL,
                                                                 PRIMARY KEY (`NON_SCANNABLE_ASSET_PRODUCTS_ID`));
ALTER TABLE `KETTLE_SCM_DEV`.`SKU_DEFINITION`
ADD COLUMN `VO_DISCONTINUED_FROM` TIMESTAMP NULL DEFAULT NULL AFTER `TAX_CATEGORY_CODE`,
ADD COLUMN `RO_DISCONTINUED_FROM` TIMESTAMP NULL DEFAULT NULL AFTER `VO_DISCONTINUED_FROM`;

ALTER TABLE `KETTLE_SCM_DEV`.`UNIT_SKU_MAPPING`
ADD COLUMN `VO_DISCONTINUED_FROM` TIMESTAMP NULL DEFAULT NULL AFTER `TAX_CODE`,
ADD COLUMN `RO_DISCONTINUED_FROM` TIMESTAMP NULL DEFAULT NULL AFTER `VO_DISCONTINUED_FROM`;

ALTER TABLE `kettle_scm_dev`.`vehicle_dispatch_data`
ADD COLUMN `E_INVOICE_DONE` VARCHAR(45) NULL AFTER `VEHICLE_NUMBER`;

CREATE TABLE `kettle_scm_dev`.`transfer_order_e_invoice` (
                                                             `TRANSFER_ORDER_E_INVOICE_ID` INT NOT NULL AUTO_INCREMENT,
                                                             `TRANSFER_ORDER_ID` INT(11) NULL,
                                                             `IRN_NO` VARCHAR(1000) NULL,
                                                             `ACK_NO` VARCHAR(1000) NULL,
                                                             `BAR_CODE_ID` INT(11) NULL,
                                                             `SIGNED_QR_CODE` VARCHAR(1000) NULL,
                                                             `INVOICE_URL` VARCHAR(1000) NULL,
                                                             PRIMARY KEY (`TRANSFER_ORDER_E_INVOICE_ID`));

ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER
    ADD COLUMN `E_INVOICE_GENERATED` VARCHAR(1) NULL AFTER `REVERSE_TO_GR_ID`;




CREATE TABLE `KETTLE_SCM_DEV`.`ADVANCE_PAYMENT_DATA` (
`ADVANCE_PAYMENT_ID` int(11) NOT NULL AUTO_INCREMENT,
`PR_ID` int(11) NOT NULL,
`ADVANCE_TYPE` varchar(45) NOT NULL,
`ADVANCE_STATUS` varchar(20) NOT NULL,
`VENDOR_ID` int(11) NOT NULL,
`PR_AMOUNT` decimal(16,6) NOT NULL,
`AVAILABLE_AMOUNT` decimal(16,6) NOT NULL,
`BLOCKED_AMOUNT` decimal(16,6) NOT NULL,
`PURCHASE_ORDER_ID` int(11) DEFAULT NULL,
`SERVICE_ORDER_ID` int(11) DEFAULT NULL,
`LAST_PO_SO_STATUS` varchar(100) DEFAULT NULL,
`CREATED_BY` varchar(200) NOT NULL,
`CREATED_AT` timestamp NULL DEFAULT NULL,
`REJECTED_FOR` varchar(100) DEFAULT NULL,
`ADJUSTED_PO_SO` int(11) DEFAULT NULL,
`REFUND_DATE` timestamp NULL DEFAULT NULL,
`PARENT_ADVANCE_ID` int(11) DEFAULT NULL,
`REFUND_INITIATED_BY` int(11) DEFAULT NULL,
`REFUND_RECEIVED_DATE` timestamp NULL DEFAULT NULL,
`LAST_UPDATED_BY` int(11) DEFAULT NULL,
`LAST_UPDATED_DATE` timestamp NULL DEFAULT NULL,
`CHILD_ADVANCE_ID` int(11) DEFAULT NULL,
`MAX_SETTLEMENT_TIME` timestamp NULL DEFAULT NULL,
PRIMARY KEY (`ADVANCE_PAYMENT_ID`)
);


CREATE TABLE `KETTLE_SCM_DEV`.`ADVANCE_PAYMENT_AUDIT_LOG` (
`ADVANCE_PAYMENT_AUDIT_LOG_ID` int(11) NOT NULL AUTO_INCREMENT,
`AMOUNT` decimal(16,6) NOT NULL,
`FROM_AMOUNT` decimal(16,6) NOT NULL,
`TO_AMOUNT` decimal(16,6) NOT NULL,
`PR_ID` int(11) NOT NULL,
`ADVANCE_PAYMENT_ID` int(11) NOT NULL,
`STATUS` varchar(45) NOT NULL,
`LOGGED_BY` varchar(200) NOT NULL,
`LOGGED_AT` timestamp NULL DEFAULT NULL,
PRIMARY KEY (`ADVANCE_PAYMENT_AUDIT_LOG_ID`),
UNIQUE KEY `ADVANCE_PAYMENT_AUDIT_LOG_ID_UNIQUE` (`ADVANCE_PAYMENT_AUDIT_LOG_ID`)
);

ALTER TABLE `KETTLE_SCM_DEV`.`PAYMENT_REQUEST`
ADD COLUMN `DUPLICATE_PAID_AMOUNT` decimal(16,6) DEFAULT NULL AFTER `PAID_AMOUNT`,
ADD COLUMN `ADVANCE_PAYMENT_ID` INT(11) DEFAULT NULL AFTER `DUPLICATE_PAID_AMOUNT`,
ADD COLUMN `ADVANCE_AMOUNT` decimal(16,6) DEFAULT NULL AFTER `ADVANCE_PAYMENT_ID`;

ALTER TABLE `KETTLE_SCM_DEV`.`PURCHASE_ORDER`
ADD COLUMN `ADVANCE_PAYMENT_ID` INT(11) NULL AFTER `TYPE`;

ALTER TABLE `KETTLE_SCM_DEV`.`SERVICE_ORDER`
ADD COLUMN `ADVANCE_PAYMENT_ID` INT(11) NULL AFTER `UPLOADED_DOCUMENT_ID`;

CREATE TABLE `KETTLE_SCM_DEV`.`ADVANCE_PAYMENT_STATUS_LOGS` (
`ADVANCE_PAYMENT_STATUS_LOG_ID` int(11) NOT NULL AUTO_INCREMENT,
`ADVANCE_PAYMENT_ID` int(11) NOT NULL,
`FROM_STATUS` varchar(100) NOT NULL,
`TO_STATUS` varchar(100) NOT NULL,
`LOGGED_BY` int(11) NOT NULL,
`LOGGED_AT` timestamp NULL DEFAULT NULL,
PRIMARY KEY (`ADVANCE_PAYMENT_STATUS_LOG_ID`)
);

ALTER TABLE `KETTLE_SCM_DEV`.`VENDOR_DETAIL_DATA`
ADD COLUMN `VENDOR_BLOCKED` varchar(1) DEFAULT NULL AFTER `TDS_DOCUMENT`,
ADD COLUMN `BLOCKED_REASON` VARCHAR(700) DEFAULT NULL AFTER `VENDOR_BLOCKED`,
ADD COLUMN `UN_BLOCKED_TILL_DATE` TIMESTAMP DEFAULT NULL AFTER `BLOCKED_REASON`;

ALTER TABLE `KETTLE_SCM_DEV`.`DEBIT_NOTE_DETAIL`
ADD COLUMN `ADVANCE_AMOUNT` DECIMAL(16,6) NULL DEFAULT NULL AFTER `UPLOAD_DOC_ID`,
ADD COLUMN `ADVANCE_PAYMENT_ID` INT(11) NULL DEFAULT NULL AFTER `ADVANCE_AMOUNT`;

INSERT INTO `KETTLE_MASTER`.`REF_LOOKUP` (`RL_ID`, `RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`)
VALUES ('3882', '39', 'ADVANCE_PAYMENT', 'Advance Payment', 'ADP', 'ACTIVE');

ALTER TABLE `KETTLE_SCM_DEV`.`DEBIT_NOTE_DETAIL`
ADD COLUMN `DEBIT_NOTE_STATUS` VARCHAR(45) NULL DEFAULT NULL AFTER 'DEBIT_NOTE_DETAIL_ID';






ALTER TABLE KETTLE_SCM_DEV.`STOCK_EVENT_DEFINITION`
    ADD COLUMN `SUB_TYPE` VARCHAR(100) NULL ;

ALTER TABLE kettle_scm_dev.`STOCK_EVENT_ASSET_MAPPING`
    ADD COLUMN `MANUAL_CHECKED` VARCHAR(1) NULL ;

CREATE TABLE KETTLE_SCM_DEV.`NON_SCANNABLE_ASSET_PRODUCTS` (
                                                                 `NON_SCANNABLE_ASSET_PRODUCTS_ID` INT NOT NULL AUTO_INCREMENT,
                                                                 `PRODUCT_ID` INT(11) NOT NULL,
                                                                 `MAPPING_STATUS` VARCHAR(45) NOT NULL,
                                                                 PRIMARY KEY (`NON_SCANNABLE_ASSET_PRODUCTS_ID`));


CREATE TABLE `kettle_scm_dev`.`outward_register` (
                                                     `SERIAL_NUMBER` INT NOT NULL,
                                                     `DATE` DATETIME NOT NULL,
                                                     `CHALLAN_NUMBER` INT NOT NULL,
                                                     `TIME` DATETIME NOT NULL,
                                                     `NAME_ADDRESS_OF_BUYER` VARCHAR(45) NOT NULL,
                                                     `DETAILS_OF_ARTICLE` VARCHAR(45) NOT NULL,
                                                     `QUANTITY` INT NOT NULL,
                                                     `AMOUNT` INT NOT NULL,
                                                     `NAME_OF_DELIVERER` VARCHAR(45) NOT NULL,
                                                     `SIGN_OF_DELIVERER` VARCHAR(45) NOT NULL,
                                                     `VEHICLE_NUMBER_TYPE` VARCHAR(45) NOT NULL,
                                                     `SIGNATURE_OF_SECURITY` VARCHAR(45) NOT NULL,
                                                     `REMARKS` VARCHAR(45) NOT NULL,
                                                     `INVOICE_ID` INT(11) NULL,
                                                     `BUISNESS_TYPE` VARCHAR(45) NULL,
                                                     `ID` INT(11) NOT NULL,
                                                     UNIQUE INDEX `TIME_UNIQUE` (`TIME` ASC),
                                                     UNIQUE INDEX `SERIAL_NUMBER_UNIQUE` (`SERIAL_NUMBER` ASC),
                                                     PRIMARY KEY (`ID`));


CREATE TABLE `KETTLE_SCM_DEV`.`OUTWARD_REGISTER` (
                                    `ID` int NOT NULL AUTO_INCREMENT,
                                    `DATE_TIME` timestamp NULL DEFAULT NULL,
                                    `CHALLAN_NUMBER` varchar(50) NOT NULL,
                                    `ADDRESS_OF_BUYER` varchar(200) NOT NULL,
                                    `DETAILS_OF_ARTICLE` varchar(200) NOT NULL,
                                    `QUANTITY` decimal(16,6) NOT NULL,
                                    `AMOUNT` decimal(16,6) NOT NULL,
                                    `NAME_OF_DELIVERER` varchar(200) NOT NULL,
                                    `VEHICLE_NUMBER_TYPE` varchar(100) NOT NULL,
                                    `SIGNATURE_OF_SECURITY` varchar(100) NOT NULL,
                                    `REMARKS` varchar(200) DEFAULT NULL,
                                    `INVOICE_ID` int DEFAULT NULL,
                                    `BUSINESS_TYPE` varchar(45) DEFAULT NULL,
                                    `UNIT_ID` int NOT NULL,
                                    `SUBMISSION_DATE_TIME` timestamp NULL DEFAULT NULL,
                                    PRIMARY KEY (`ID`),
                                    UNIQUE KEY `INVOICE_ID_UNIQUE` (`INVOICE_ID`)
) ;



ALTER TABLE `KETTLE_SCM_DEV`.`PAYMENT_REQUEST`
ADD COLUMN `EXTRA_CHARGES_SGST` DECIMAL(16,6) NULL DEFAULT NULL AFTER `EXTRA_CHARGES_TYPE`,
ADD COLUMN `EXTRA_CHARGES_CGST` DECIMAL(16,6) NULL DEFAULT NULL AFTER `EXTRA_CHARGES_SGST`,
ADD COLUMN `EXTRA_CHARGES_IGST` DECIMAL(16,6) NULL DEFAULT NULL AFTER `EXTRA_CHARGES_CGST`,
ADD COLUMN `EXTRA_CHARGES_WITHOUT_TAX` DECIMAL(16,6) NULL DEFAULT NULL AFTER `EXTRA_CHARGES_IGST`;


ALTER TABLE `KETTLE_SCM_DEV`.`REQUEST_ORDER`
ADD COLUMN `BULK_ORDER` VARCHAR(1) NULL DEFAULT 'N' AFTER `TYPE`;

CREATE TABLE KETTLE_SCM_DEV.SPECIALIZED_ORDER_INVOICE (
                                                          SPECIALIZED_ORDER_INVOICE_ID INT NOT NULL AUTO_INCREMENT,
                                                          VENDOR_ID INT(11) NULL,
                                                          UNIT_ID INT(11) NULL,
                                                          GENERATION_TIME DATETIME NULL,
                                                          IS_PR_RAISED VARCHAR(1) NULL,
                                                          PR_ID INT(11) NULL ,
                                                          INVOICE_URL VARCHAR(400) NULL,
                                                          INVOICE_ID VARCHAR(200) NULL,
                                                          DOCUMENT_ID INT(11) NULL,
                                                          PRIMARY KEY (SPECIALIZED_ORDER_INVOICE_ID));

ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED
    ADD COLUMN INVOICE_ID INT(11) NULL ;

ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER_ITEM
    ADD COLUMN PACKAGING_ID INT(11) NULL ;

CREATE TABLE KETTLE_SCM_DEV.INVOICE_EXCESS_QUANTITY (
                                                        INVOICE_EXCESS_QUANTITY_ID INT NOT NULL AUTO_INCREMENT,
                                                        INVOICE_ID INT(11) NOT NULL,
                                                        UNIT_ID INT(11) NOT NULL,
                                                        SKU_ID INT(11) NOT NULL,
                                                        SKU_NAME VARCHAR(100) NOT NULL,
                                                        EXCESS_QTY DECIMAL(10,6) NOT NULL,
                                                        UNIT_PRICE DECIMAL(10,6) NOT NULL,
                                                        PRIMARY KEY (INVOICE_EXCESS_QUANTITY_ID));


ALTER TABLE `KETTLE_SCM_DEV`.`REGULAR_ORDERING_SCM_FORECAST_DATA`
ADD COLUMN `NON_FUNCTIONAL` VARCHAR(1) NULL DEFAULT NULL AFTER `SUGGESTIONS_REQUESTED_BY`;


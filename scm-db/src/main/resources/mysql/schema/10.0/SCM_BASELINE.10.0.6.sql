CREATE TABLE KETTLE_SCM_DEV.VENDOR_TDS_CERTIFICATE_SENT_DATA (
  ID int(11) NOT NULL AUTO_INCREMENT,
  VENDOR_NAME varchar(45) NOT NULL,
  QUARTE<PERSON> varchar(45) DEFAULT NULL,
  FINANCIAL_YEAR varchar(45) DEFAULT NULL,
  VENDOR_EMAIL_ADDRESS varchar(45) NOT NULL,
  PAN_NUMBER varchar(45) NOT NULL,
  UPLOAD_PATH varchar(500) DEFAULT NULL,
  SEND_TIME timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`)
);

CREATE INDEX PAN_NUMBER ON KETTLE_SCM_DEV.VENDOR_TDS_CERTIFICATE_SENT_DATA(PAN_NUMBER) USING BTREE;

CREATE INDEX VENDOR_GOODS_RECEIVED_DATA_PAYMENT_REQUEST_ID ON KETTLE_SCM_DUMP.VENDOR_GOODS_RECEIVED_DATA(PAYMENT_REQUEST_ID) USING BTREE;
CREATE INDEX SERVICE_ORDER_TO_SERVICE_RECEIVED_MAPPING_SERVICE_ORDER_ID ON KETTLE_SCM_DUMP.SERVICE_ORDER_TO_SERVICE_RECEIVED_MAPPING(SERVICE_ORDER_ID) USING BTREE;
CREATE INDEX SERVICE_ORDER_TO_SERVICE_RECEIVED_MAPPING_SERVICE_RECEIVED_ID ON KETTLE_SCM_DUMP.SERVICE_ORDER_TO_SERVICE_RECEIVED_MAPPING(SERVICE_RECEIVED_ID) USING BTREE;

CREATE INDEX REQUEST_ORDER_ITEM_VENDOR_ID ON KETTLE_SCM_DUMP.REQUEST_ORDER_ITEM(VENDOR_ID) USING BTREE;
-- # SET sql_mode = '';    Required for below queries otherwise giving "Error Code : 1067 Invalid default value for ..."
CREATE INDEX REQUEST_ORDER_GENERATED_BY ON KETTLE_SCM_DUMP.REQUEST_ORDER(GENERATED_BY) USING BTREE;
CREATE INDEX TRANSFER_ORDER_GENERATED_BY ON KETTLE_SCM_DUMP.TRANSFER_ORDER(GENERATED_BY) USING BTREE;
CREATE INDEX GOODS_RECEIVED_GENERATED_BY ON KETTLE_SCM_DUMP.GOODS_RECEIVED(GENERATED_BY) USING BTREE;


CREATE INDEX UNIT_OPERATION_DETAIL_UNIT_ID ON KETTLE_DEV.UNIT_OPERATION_DETAIL(UNIT_ID) USING BTREE;
CREATE INDEX UNIT_OPERATION_DETAIL_BRAND_ID ON KETTLE_DEV.UNIT_OPERATION_DETAIL(BRAND_ID) USING BTREE;
CREATE INDEX UNIT_OPERATION_DETAIL_FIRST_ORDER_DATE ON KETTLE_DEV.UNIT_OPERATION_DETAIL(FIRST_ORDER_DATE) USING BTREE;
CREATE INDEX UNIT_OPERATION_DETAIL_LAST_ORDER_DATE ON KETTLE_DEV.UNIT_OPERATION_DETAIL(LAST_ORDER_DATE) USING BTREE;
CREATE INDEX UNIT_OPERATION_DETAIL_HANDOVER_DATE ON KETTLE_DEV.UNIT_OPERATION_DETAIL(HANDOVER_DATE) USING BTREE;


CREATE INDEX TEMP_UNIT_OPERATION_DETAIL_UNIT_ID ON KETTLE_DEV.TEMP_UNIT_OPERATION_DETAIL(UNIT_ID) USING BTREE;
CREATE INDEX TEMP_UNIT_OPERATION_DETAIL_BRAND_ID ON KETTLE_DEV.TEMP_UNIT_OPERATION_DETAIL(BRAND_ID) USING BTREE;
CREATE INDEX TEMP_UNIT_OPERATION_DETAIL_FIRST_ORDER_DATE ON KETTLE_DEV.TEMP_UNIT_OPERATION_DETAIL(FIRST_ORDER_DATE) USING BTREE;
CREATE INDEX TEMP_UNIT_OPERATION_DETAIL_LAST_ORDER_DATE ON KETTLE_DEV.TEMP_UNIT_OPERATION_DETAIL(LAST_ORDER_DATE) USING BTREE;
CREATE INDEX TEMP_UNIT_OPERATION_DETAIL_HANDOVER_DATE ON KETTLE_DEV.TEMP_UNIT_OPERATION_DETAIL(HANDOVER_DATE) USING BTREE;

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL(APPLICATION_ID,ACTION_TYPE,ACTION_CATEGORY,ACTION_CODE,ACTION_DESCRIPTION,ACTION_STATUS) VALUES
(7,'SUBMENU','SHOW','SVMMTS','SuMo -> Vendor Management -> Mail TDS Certificate-> SHOW','ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA
(ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS, APPLICATION_ID)
VALUES ('Mail TDS Certificate', 'Access to Mail TDS Certificate To Vendor', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Mail TDS Certificate'),
(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'SVMMTS'), 'ACTIVE', '120063', '2022-07-25 00:00:00');
ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER
    ADD COLUMN `BULK_TRANSFER_EVENT_ID` INT(11) NULL ;


CREATE TABLE KETTLE_SCM_DEV.BULK_TRANSFER_EVENT (
                                                        `BULK_TRANSFER_EVENT_ID` INT(11) NOT NULL AUTO_INCREMENT,
                                                        `RO_COUNT` INT(11) NULL,
                                                        `TO_COUNT` INT(11) NULL,
                                                        `GENERATED_BY` INT(11) NULL,
                                                        `SOURCE_COMPANY_ID` INT(11) NULL,
                                                        `GENERATION_UNIT_ID` INT(11) NULL,
                                                        `INITIATION_TIME` TIMESTAMP NULL,
                                                        `COMPLETION_TIME` TIMESTAMP NULL,
                                                        `STATUS` VARCHAR(30) NULL,
                                                        PRIMARY KEY (`BULK_TRANSFER_EVENT_ID`));

ALTER TABLE KETTLE_SCM_DEV.WASTAGE_ITEM_DATA ADD COLUMN PRODUCTION_ID INT(11) NULL;

ALTER TABLE KETTLE_SCM_DEV.BULK_TRANSFER_EVENT
    ADD COLUMN `IS_INVOICE_SET` VARCHAR(1) NULL DEFAULT 'Y';

CREATE TABLE `KETTLE_SCM_DEV`.`WASTAGE_LIMIT_LOOKUP` (
`WASTAGE_LIMIT_ID` INT(11) NOT NULL AUTO_INCREMENT,
`DESIGNATION_ID` INT(6) NOT NULL,
`MAXIMUM_LIMIT` INT(7) DEFAULT NULL,
PRIMARY KEY (`WASTAGE_LIMIT_ID`),
UNIQUE (`DESIGNATION_ID`));

CREATE TABLE `KETTLE_SCM_DEV`.`TRANSFER_ORDER_ASSET_CORRECTION` (
`ASSET_CORRECTION_ID` INT(11) NOT NULL AUTO_INCREMENT,
`TRANSFER_ORDER_ID` INT(11) NOT NULL,
`TRANSFER_ORDER_ITEM_ID` INT(11) NOT NULL,
`ASSET_ID` INT(11) NOT NULL,
`PREVIOUS_ASSET_TAG` VARCHAR(40) NOT NULL,
`UPDATED_ASSET_TAG` VARCHAR(40) NOT NULL,
`STATUS` VARCHAR(40) NOT NULL,
`UPDATED_BY` VARCHAR(100) NOT NULL,
`LAST_UPDATED_TIME` TIMESTAMP NOT NULL,
PRIMARY KEY (`ASSET_CORRECTION_ID`));

CREATE INDEX TRANSFER_ORDER_ASSET_CORRECTION_TRANSFER_ORDER_ITEM_ID ON KETTLE_SCM_DEV.TRANSFER_ORDER_ASSET_CORRECTION(TRANSFER_ORDER_ITEM_ID) USING BTREE;

ALTER TABLE KETTLE_SCM_DEV.WASTAGE_ITEM_DATA ADD COLUMN ENTERED_COMMENT VARCHAR(1000) NULL;

ALTER TABLE KETTLE_SCM_DEV.DEBIT_NOTE_DETAIL ADD COLUMN UPLOAD_DOC_ID INTEGER;

CREATE INDEX BUSINESS_DATE ON KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA(BUSINESS_DATE) USING BTREE;
CREATE INDEX UNIT_ID ON KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA(UNIT_ID) USING BTREE;
CREATE INDEX BRAND_ID ON KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA(BRAND_ID) USING BTREE;
CREATE INDEX AVG_UPT ON KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA(AVG_UPT) USING BTREE;
CREATE INDEX AVG_PRICE ON KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA(AVG_PRICE) USING BTREE;
CREATE INDEX SUGGESTED_QUANTITY ON KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA(SUGGESTED_QUANTITY) USING BTREE;
CREATE INDEX CATEGORY_BUFFER_SUGGESTED_QUANTITY ON KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA(CATEGORY_BUFFER_SUGGESTED_QUANTITY) USING BTREE;
CREATE INDEX ROUND_SUGST_QUANTITY ON KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA(ROUND_SUGST_QUANTITY) USING BTREE;
CREATE INDEX SUGGESTED_SALES ON KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA(SUGGESTED_SALES) USING BTREE;
CREATE INDEX CATEGORY_BUFFER_APPLIED ON KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA(CATEGORY_BUFFER_APPLIED) USING BTREE;
CREATE INDEX QUANTITY_INCREMENT ON KETTLE_SCM_DEV.ESTIMATE_REQUEST_DATA(QUANTITY_INCREMENT) USING BTREE;



INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('Custom Reports', 'Access to see Custom Reports', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('CSR', '7', 'SUBMENU', 'SHOW', 'SuMo -> Admin Reports -> Custom Reports', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Custom Reports'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'CSR'), 'ACTIVE', '120063', '2022-07-22 12:00:00');

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('Asset Transfers', 'Access to see Asset Transfers', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('ATC', '7', 'SUBMENU', 'SHOW', 'SuMo -> Asset Management -> Asset Transfers', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Asset Transfers'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ATC'), 'ACTIVE', '120063', '2022-07-22 12:00:00');

INSERT INTO `KETTLE_SCM_DEV`.`WASTAGE_LIMIT_LOOKUP` (`WASTAGE_LIMIT_ID`, `DESIGNATION_ID`, `MAXIMUM_LIMIT`) VALUES ('1', '1501', '5000');
INSERT INTO `KETTLE_SCM_DEV`.`WASTAGE_LIMIT_LOOKUP` (`WASTAGE_LIMIT_ID`, `DESIGNATION_ID`, `MAXIMUM_LIMIT`) VALUES ('2', '1502', '15000');
INSERT INTO `KETTLE_SCM_DEV`.`WASTAGE_LIMIT_LOOKUP` (`WASTAGE_LIMIT_ID`, `DESIGNATION_ID`, `MAXIMUM_LIMIT`) VALUES ('3', '1503', '5000');
INSERT INTO `KETTLE_SCM_DEV`.`WASTAGE_LIMIT_LOOKUP` (`WASTAGE_LIMIT_ID`, `DESIGNATION_ID`, `MAXIMUM_LIMIT`) VALUES ('4', '1504', '5000');
INSERT INTO `KETTLE_SCM_DEV`.`WASTAGE_LIMIT_LOOKUP` (`WASTAGE_LIMIT_ID`, `DESIGNATION_ID`, `MAXIMUM_LIMIT`) VALUES ('5', '1505', '5000');
INSERT INTO `KETTLE_SCM_DEV`.`WASTAGE_LIMIT_LOOKUP` (`WASTAGE_LIMIT_ID`, `DESIGNATION_ID`, `MAXIMUM_LIMIT`) VALUES ('6', '1506', '5000');
INSERT INTO `KETTLE_SCM_DEV`.`WASTAGE_LIMIT_LOOKUP` (`WASTAGE_LIMIT_ID`, `DESIGNATION_ID`, `MAXIMUM_LIMIT`) VALUES ('7', '1507', '5000');
INSERT INTO `KETTLE_SCM_DEV`.`WASTAGE_LIMIT_LOOKUP` (`WASTAGE_LIMIT_ID`, `DESIGNATION_ID`, `MAXIMUM_LIMIT`) VALUES ('8', '1508', '5000');
INSERT INTO `KETTLE_SCM_DEV`.`WASTAGE_LIMIT_LOOKUP` (`WASTAGE_LIMIT_ID`, `DESIGNATION_ID`, `MAXIMUM_LIMIT`) VALUES ('9', '1509', '5000');
INSERT INTO `KETTLE_SCM_DEV`.`WASTAGE_LIMIT_LOOKUP` (`WASTAGE_LIMIT_ID`, `DESIGNATION_ID`, `MAXIMUM_LIMIT`) VALUES ('10', '1516', '5000');
INSERT INTO `KETTLE_SCM_DEV`.`WASTAGE_LIMIT_LOOKUP` (`WASTAGE_LIMIT_ID`, `DESIGNATION_ID`, `MAXIMUM_LIMIT`) VALUES ('11', '1517', '10000');
INSERT INTO `KETTLE_SCM_DEV`.`WASTAGE_LIMIT_LOOKUP` (`WASTAGE_LIMIT_ID`, `DESIGNATION_ID`, `MAXIMUM_LIMIT`) VALUES ('12', '1502', '25000');

ALTER TABLE KETTLE_SCM_DEV.MONK_WASTAGE_DETAIL_DATA 
    ADD COLUMN MILK_PRODUCT_ID INT(11) NULL,
    ADD COLUMN UNIT_ID INT(11) NULL,
    ADD COLUMN IS_CLUBBED VARCHAR(1) NULL,
    ADD COLUMN CLUBBED_WITH_TASK INT(11) NULL,
    ADD COLUMN IS_SPLIT VARCHAR(1) NULL,
    ADD COLUMN LINKED_TASK_ID INT(11) NULL,
    ADD COLUMN IS_MANUAL_TASK VARCHAR(1) NULL,
    ADD COLUMN IS_PROCESSED VARCHAR(20) NULL,
    ADD COLUMN LOG_ADD_TIME TIMESTAMP DEFAULT NULL,
    ADD COLUMN LOG_ADD_TIME_AT_SERVER TIMESTAMP DEFAULT NULL,
    ADD COLUMN ORIGINAL_QUANTITY DECIMAL(16,6) DEFAULT NULL;

CREATE INDEX IS_PROCESSED_MONK_WASTAGE_DETAIL_DATA ON KETTLE_SCM.MONK_WASTAGE_DETAIL_DATA(IS_PROCESSED) USING BTREE;

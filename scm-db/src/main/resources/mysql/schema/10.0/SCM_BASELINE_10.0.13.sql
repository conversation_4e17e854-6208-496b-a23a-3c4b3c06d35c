CREATE TABLE KETTLE_SCM.RIDER_INFO_DATA
(
    RIDER_INFO_DATA_ID    INT AUTO_INCREMENT PRIMARY KEY NOT NULL,
    EMPLOYEE_ID      INT                NOT NULL,
    VEHICLE_ID       INT                NOT NULL,
    UNIT_ID       INT                NOT NULL,
    CREATION_TIME    TIMESTAMP          NULL,
    RIDER_STATUS     VARCHAR(30)       NOT NULL,
    LAST_UPDATE_TIME TIMESTAMP        NULL
);

ALTER TABLE KETTLE_SCM.UNIT_DETAIL ADD COLUMN HUB_SPOKE_TYPE VARCHAR(15) DEFAULT NULL;

CREATE TABLE KETTLE_SCM.STOCK_REDISTRIBUTION_ROUTE_INFO_DATA
(
    STOCK_REDISTRIBUTION_ROUTE_INFO_DATA_ID INT AUTO_INCREMENT PRIMARY KEY NOT NULL,
    ROUTE_NAME                              VARCHAR(255)       NOT NULL,
    ROUTE_STATUS                            VARCHAR(20)       NOT NULL,
    CREATION_TIME                           TIMESTAMP           NULL,
    LAST_UPDATE_TIME                        TIMESTAMP        NULL
);

CREATE TABLE KETTLE_SCM.STOCK_REDISTRIBUTION_ROUTE_UNITS_DATA
(
    STOCK_REDISTRIBUTION_ROUTE_UNIT_DATA_ID INT AUTO_INCREMENT PRIMARY KEY NOT NULL,
    STOCK_REDISTRIBUTION_ROUTE_INFO_DATA_ID INT                NOT NULL,
    UNIT_ID INT                NOT NULL
);

CREATE TABLE KETTLE_SCM.RIDER_ROUTE_PLAN_DATA
(
    RIDER_ROUTE_PLAN_DATA_ID INT AUTO_INCREMENT PRIMARY KEY NOT NULL,
    RIDER_ID                 INT                NOT NULL,
    ROUTE_ID                 INT                NOT NULL,
    RIDER_ROUTE_PLAN_STATUS  VARCHAR(30)       NOT NULL,
    RIDER_ROUTE_TYPE  VARCHAR(30)       NOT NULL,
    CREATION_TIME            TIMESTAMP           NULL,
    RIDE_START_TIME          TIMESTAMP        NULL,
    RIDE_END_TIME            TIMESTAMP        NULL
);

CREATE TABLE RIDER_ROUTE_PLAN_STEP_DATA
(
    RIDER_ROUTE_PLAN_STEP_DATA_ID INT AUTO_INCREMENT PRIMARY KEY NOT NULL,
    RIDER_ROUTE_PLAN_DATA_ID      INT                NOT NULL,
    ROUTE_STEP_STATUS             VARCHAR(30)       NOT NULL,
    CURRENT_STORE                 INT                NOT NULL,
    NEXT_STORE                    INT                NOT NULL,
    DROP_STATUS             VARCHAR(30)       NOT NULL,
    DROP_TIME            TIMESTAMP          DEFAULT NULL,
    DROP_TEMPERATURE            DECIMAL(5,2)         DEFAULT NULL,
    PICKUP_STATUS             VARCHAR(30)       NOT NULL,
    PICKUP_TIME            TIMESTAMP          DEFAULT NULL,
    PICKUP_TEMPERATURE            DECIMAL(5,2)         DEFAULT NULL,
    ROUTE_STEP                    INT                NOT NULL,
    PICKED_UP_TO_ID               INT                NULL,
    DROP_OFF_TO_ID                INT                NULL,
    RIDER_REACHED_TIME            TIMESTAMP          NULL,
    RIDER_LEAVE_TIME            TIMESTAMP          DEFAULT NULL,
);


CREATE TABLE KETTLE_SCM.RIDER_ROUTE_PLAN_ITEM_DATA
(
    RIDER_ROUTE_PLAN_ITEM_DATA_ID INT AUTO_INCREMENT PRIMARY KEY NOT NULL,
    RIDER_ROUTE_PLAN_STEP_DATA_ID INT                NOT NULL,
    RIDER_ACTION                  VARCHAR(10)       NOT NULL,
    PRODUCT_ID                  INT                NOT NULL,
    PACKAGING_ID                  INT                NOT NULL,
    PROPOSED_QUANTITY             DECIMAL(16,6)           NOT NULL,
    FINAL_QUANTITY                DECIMAL(16,6)            NULL,
    STOCK_TYPE                    VARCHAR(15)       NOT NULL,
    COMMENT                       TEXT       NULL
);


INSERT INTO `KETTLE_SCM_STAGE`.`RIDER_INFO` (`RIDER_INFO_ID`, `EMPLOYEE_ID`, `VEHICLE_ID`, `CREATION_TIME`, `RIDER_STATUS`) VALUES ('1', '140199', '1', '2024-12-06 00:00:00', 'ACTIVE');
INSERT INTO `KETTLE_SCM_STAGE`.`RIDER_ROUTE_PLAN_DATA` (`RIDER_ROUTE_PLAN_DATA_ID`, `RIDER_ID`, `ROUTE_ID`, `RIDER_ROUTE_PLAN_STATUS`, `CREATION_TIME`) VALUES ('1', '1', '1', 'CREATED', '2024-12-06 00:00:00');
INSERT INTO `KETTLE_SCM_STAGE`.`RIDER_ROUTE_PLAN_STEP_DATA` (`RIDER_ROUTE_PLAN_STEP_DATA_ID`, `RIDER_ROUTE_PLAN_DATA_ID`, `ROUTE_STEP_STATUS`, `CURRENT_STORE`, `NEXT_STORE`, `TOTAL_PICK_UP_QUANTITY`, `TOTAL_PICK_UP_PRODUCTS`, `TOTAL_DROP_OFF_QUANTITY`, `TOTAL_DROP_OFF_PRODUCTS`, `ROUTE_STEP`) VALUES ('1', '1', 'CREATED', '26422', '10000', '5', '3', '7', '3', '1');
INSERT INTO `KETTLE_SCM_STAGE`.`RIDER_ROUTE_PLAN_STEP_DATA` (`RIDER_ROUTE_PLAN_DATA_ID`, `ROUTE_STEP_STATUS`, `CURRENT_STORE`, `NEXT_STORE`, `TOTAL_PICK_UP_QUANTITY`, `TOTAL_PICK_UP_PRODUCTS`, `TOTAL_DROP_OFF_QUANTITY`, `TOTAL_DROP_OFF_PRODUCTS`, `ROUTE_STEP`) VALUES ('1', 'CREATED', '10000', '10001', '1', '1', '2', '1', '2');
INSERT INTO `KETTLE_SCM_STAGE`.`RIDER_ROUTE_PLAN_ITEM_DATA` (`RIDER_ROUTE_PLAN_STEP_DATA_ID`, `RIDER_ACTION`, `PRODUCT_ID`, `PACKAGING_ID`, `PROPOSED_QUANTITY`, `STOCK_TYPE`) VALUES ('1', 'PICKUP', '100324', '1', '5', 'FRESH');
INSERT INTO `KETTLE_SCM_STAGE`.`RIDER_ROUTE_PLAN_ITEM_DATA` (`RIDER_ROUTE_PLAN_STEP_DATA_ID`, `RIDER_ACTION`, `PRODUCT_ID`, `PACKAGING_ID`, `PROPOSED_QUANTITY`, `STOCK_TYPE`) VALUES ('1', 'DROP', '100325', '3', '7', 'EXPIRING');
INSERT INTO `KETTLE_SCM_STAGE`.`RIDER_ROUTE_PLAN_ITEM_DATA` (`RIDER_ROUTE_PLAN_ITEM_DATA_ID`, `RIDER_ROUTE_PLAN_STEP_DATA_ID`, `RIDER_ACTION`, `PRODUCT_ID`, `PACKAGING_ID`, `PROPOSED_QUANTITY`, `STOCK_TYPE`) VALUES ('3', '2', 'PICKUP', '100324', '1', '1', 'EXPIRING');
INSERT INTO `KETTLE_SCM_STAGE`.`RIDER_ROUTE_PLAN_ITEM_DATA` (`RIDER_ROUTE_PLAN_ITEM_DATA_ID`, `RIDER_ROUTE_PLAN_STEP_DATA_ID`, `RIDER_ACTION`, `PRODUCT_ID`, `PACKAGING_ID`, `PROPOSED_QUANTITY`, `STOCK_TYPE`) VALUES ('4', '2', 'DROP', '100325', '3', '2', 'FRESH');
INSERT INTO `KETTLE_SCM_STAGE`.`STOCK_REDISTRIBUTION_ROUTE_INFO_DATA` (`ROUTE_NAME`, `ROUTE_STATUS`, `CREATION_TIME`) VALUES ('Shannus Test', 'ACTIVE', '2024-12-06 00:00:00');
INSERT INTO `KETTLE_SCM_STAGE`.`STOCK_REDISTRIBUTION_ROUTE_UNITS_DATA` (`STOCK_REDISTRIBUTION_ROUTE_INFO_DATA_ID`, `UNIT_ID`, `UNIT_STATUS`) VALUES ('1', '26422', 'ACTIVE');
INSERT INTO `KETTLE_SCM_STAGE`.`STOCK_REDISTRIBUTION_ROUTE_UNITS_DATA` (`STOCK_REDISTRIBUTION_ROUTE_INFO_DATA_ID`, `UNIT_ID`, `UNIT_STATUS`) VALUES ('1', '10000', 'ACTIVE');
INSERT INTO `KETTLE_SCM_STAGE`.`STOCK_REDISTRIBUTION_ROUTE_UNITS_DATA` (`STOCK_REDISTRIBUTION_ROUTE_INFO_DATA_ID`, `UNIT_ID`, `UNIT_STATUS`) VALUES ('1', '10001', 'ACTIVE');
UPDATE `KETTLE_SCM_STAGE`.`UNIT_DETAIL` SET `HUB_SPOKE_TYPE` = 'KITCHEN' WHERE (`UNIT_ID` = '26422');
UPDATE `KETTLE_SCM_STAGE`.`UNIT_DETAIL` SET `HUB_SPOKE_TYPE` = 'HUB' WHERE (`UNIT_ID` = '10000');
UPDATE `KETTLE_SCM_STAGE`.`UNIT_DETAIL` SET `HUB_SPOKE_TYPE` = 'CAFE' WHERE (`UNIT_ID` = '10001');

ALTER TABLE KETTLE_SCM_STAGE.CAPEX_AUDIT_DETAIL ADD VERSION_CREATION_DATE DATETIME;


ALTER TABLE `KETTLE_SCM`.`REFERENCE_ORDER_SCM_ITEM`
ADD COLUMN `EXPIRY_USAGE_LOGS` TEXT NULL;

CREATE TABLE `KETTLE_SCM`.DAY_WISE_ORDER_DATA
(
    DAY_WISE_ORDER_DATA_ID          INT AUTO_INCREMENT NOT NULL PRIMARY KEY,
    RO_ORDERING_DAYS_ID             INT                NULL,
    AVERAGE_CONSUMPTION_FOR_THE_DAY DECIMAL            NULL,
    SUGGESTED_QUANTITY_TO_ORDER     DECIMAL            NULL,
    ORDER_DATE                      DATETIME           NULL,
    CONSUMPTION_DATA                TEXT               NULL
);

ALTER TABLE `KETTLE_SCM_DEV`.`RO_SCM_ITEM_EXPIRY_DRILLDOWN`
ADD COLUMN `FULFILMENT_DATE` TIMESTAMP NULL;


CREATE TABLE `SALES_FORECASTING_INPUT_DATA` (
  `SALES_FORECASTING_INPUT_DATA_ID` int NOT NULL AUTO_INCREMENT,
  `DATE_OF_ORDERING` date DEFAULT NULL,
  `PRODUCT_ID` int DEFAULT NULL,
  `UNIT_ID_PRODUCT_ID_DIMENSION_IDENTIFIER` varchar(50) DEFAULT NULL,
  `VEG_NON_VEG` varchar(10) DEFAULT NULL,
  `PRODUCT_NAME` varchar(150) DEFAULT NULL,
  `PRODUCT_CATEGORY` varchar(50) DEFAULT NULL,
  `PRODUCT_SUB_CATEGORY` varchar(50) DEFAULT NULL,
  `PRODUCT_PREPARATION_TIME` decimal(16,6) DEFAULT NULL,
  `PRODUCT_INCIDENCE` decimal(16,6) DEFAULT NULL,
  `NEW_CUSTOMER_INTREST` decimal(16,6) DEFAULT NULL,
  `OLD_CUSTOMER_INTREST` decimal(16,6) DEFAULT NULL,
  `UNIT_ID` int DEFAULT NULL,
  `UNIT_NAME` varchar(100) DEFAULT NULL,
  `UNIT_CITY` varchar(50) DEFAULT NULL,
  `UNIT_CATEGORY` varchar(50) DEFAULT NULL,
  `UNITS_UNDER_2_KMS` int DEFAULT NULL,
  `UNITS_UNDER_5_KMS` int DEFAULT NULL,
  `UNITS_UNDER_10_KMS` int DEFAULT NULL,
  `UNIT_LIVE_DATE` date DEFAULT NULL,
  `IS_UNIT_CLOSED` varchar(1) DEFAULT NULL,
  `OPERATIONAL_HOURS` int DEFAULT NULL,
  `OPERATIONAL_MINUTES` int DEFAULT NULL,
  `UNIT_STATE` varchar(100) DEFAULT NULL,
  `TOTAL_SALE_QUANTITY` int DEFAULT NULL,
  `CANCELLED_ORDER_QUANTITY` int DEFAULT NULL,
  `DINE_IN_SALE_QUANTITY` int DEFAULT NULL,
  `COD_SALE_QUANTITY` int DEFAULT NULL,
  `ZOMATO_SALE_QUANTITY` int DEFAULT NULL,
  `SWIGGY_SALE_QUANTITY` int DEFAULT NULL,
  `MAGICPIN_SALE_QUANTITY` int DEFAULT NULL,
  `DINE_IN_AVERAGE_PRICE` decimal(16,6) DEFAULT NULL,
  `ZOMATO_AVERAGE_PRICE` decimal(16,6) DEFAULT NULL,
  `SWIGGY_AVERAGE_PRICE` decimal(16,6) DEFAULT NULL,
  `MAGICPIN_AVERAGE_PRICE` decimal(16,6) DEFAULT NULL,
  `BF_DINE_IN_SALES` int DEFAULT NULL,
  `BF_COD_SALES` int DEFAULT NULL,
  `BF_ZOMATO_SALES` int DEFAULT NULL,
  `BF_SWIGGY_SALES` int DEFAULT NULL,
  `BF_MAGIC_PIN_SALES` int DEFAULT NULL,
  `LUNCH_DINE_IN_SALES` int DEFAULT NULL,
  `LUNCH_COD_SALES` int DEFAULT NULL,
  `LUNCH_ZOMATO_SALES` int DEFAULT NULL,
  `LUNCH_SWIGGY_SALES` int DEFAULT NULL,
  `LUNCH_MAGIC_PIN_SALES` int DEFAULT NULL,
  `EVENING_DINE_IN_SALES` int DEFAULT NULL,
  `EVENING_COD_SALES` int DEFAULT NULL,
  `EVENING_ZOMATO_SALES` int DEFAULT NULL,
  `EVENING_SWIGGY_SALES` int DEFAULT NULL,
  `EVENING_MAGIC_PIN_SALES` int DEFAULT NULL,
  `DINNER_DINE_IN_SALES` int DEFAULT NULL,
  `DINNER_COD_SALES` int DEFAULT NULL,
  `DINNER_ZOMATO_SALES` int DEFAULT NULL,
  `DINNER_SWIGGY_SALES` int DEFAULT NULL,
  `DINNER_MAGIC_PIN_SALES` int DEFAULT NULL,
  `P_DINNER_DINE_IN_SALES` int DEFAULT NULL,
  `P_DINNER_COD_SALES` int DEFAULT NULL,
  `P_DINNER_ZOMATO_SALES` int DEFAULT NULL,
  `P_DINNER_SWIGGY_SALES` int DEFAULT NULL,
  `P_DINNER_MAGIC_PIN_SALES` int DEFAULT NULL,
  `Z_OVERNIGHT_DINE_IN_SALES` int DEFAULT NULL,
  `Z_OVERNIGHT_COD_SALES` int DEFAULT NULL,
  `Z_OVERNIGHT_ZOMATO_SALES` int DEFAULT NULL,
  `Z_OVERNIGHT_SWIGGY_SALES` int DEFAULT NULL,
  `Z_OVERNIGHT_MAGIC_PIN_SALES` int DEFAULT NULL,
  `BIG_DAY_EVENT` varchar(2) DEFAULT NULL,
  `NEW_CUSTOMER_COUNT` int DEFAULT NULL,
  `OLD_CUSTOMER_COUNT` int DEFAULT NULL,
  `DAY_OF_WEEK` int DEFAULT NULL,
  `WEEK_NUMBER` int DEFAULT NULL,
  `WEEK_PART` varchar(10) DEFAULT NULL,
  `DAY_NAME` varchar(20) DEFAULT NULL,
  `HUMIDITY` decimal(16,6) DEFAULT NULL,
  `WINDSPEED` decimal(16,6) DEFAULT NULL,
  `PRECIPITATION` decimal(16,6) DEFAULT NULL,
  `IRRADIANCE` decimal(16,6) DEFAULT NULL,
  `TEMPERATURE_MAX` decimal(16,6) DEFAULT NULL,
  `TEMPERATURE_MIN` decimal(16,6) DEFAULT NULL,
  `NUMBER_OF_STOCK_OUT` int DEFAULT NULL,
  `STOCK_OUT_TOTAL_DURATION` int DEFAULT NULL,
  `TOTAL_WASTGE_PERCENTAGE` decimal(16,6) DEFAULT NULL,
  `DIMENSION` varchar(100) DEFAULT NULL,
  `SUM_OF_ALL_SALES` decimal(16,6) DEFAULT NULL,
  PRIMARY KEY (`SALES_FORECASTING_INPUT_DATA_ID`)
) ENGINE=InnoDB;

CREATE TABLE `KETTLE_SCM_STAGE`.`SUGGESTIVE_ORDERING_STRATEGY_METADATA` (
  `ID` INT NOT NULL AUTO_INCREMENT,
  `UNIT_ID` INT NULL,
  `STRATEGY_TYPE` VARCHAR(45) NULL,
  `STATUS` VARCHAR(45) NULL,
  `NO_OF_WEEKS` INT NULL,
  `SAFETY_WEEKS` INT NULL,
  PRIMARY KEY (`ID`));

  INSERT INTO  KETTLE_SCM_STAGE.SUGGESTIVE_ORDERING_STRATEGY_METADATA (
      UNIT_ID, STRATEGY_TYPE,STATUS,NO_OF_WEEKS,SAFETY_WEEKS
  ) VALUES (26277,
      'GM','ACTIVE', 4, 2
  );

CREATE TABLE `KETTLE_SCM_STAGE`.`STRATEGY_MEAN_METADATA` (
  `STRATEGY_MEAN_ID` INT NOT NULL AUTO_INCREMENT,
  `SUGGESTIVE_ORDERING_STRATEGY_ID` INT NOT NULL,
  `MEAN_TYPE` VARCHAR(45) NULL,
  `START_WEEK` INT NULL,
  `END_WEEK` INT NULL,
  `PERCENTAGE` DECIMAL NULL,
  `STATUS` VARCHAR(45) NULL,
  PRIMARY KEY (`STRATEGY_MEAN_ID`));

  INSERT INTO KETTLE_SCM_STAGE.STRATEGY_MEAN_METADATA (
      SUGGESTIVE_ORDERING_STRATEGY_ID, MEAN_TYPE,
      START_WEEK, END_WEEK, PERCENTAGE, STATUS
  ) VALUES
  (1, 'GEOMETRIC', 1, 4, 30, 'ACTIVE'),
  (1, 'GEOMETRIC', 1, 3, 30, 'ACTIVE'),
  (1, 'GEOMETRIC', 1, 2, 40, 'ACTIVE');

CREATE TABLE MENU_SCM_KEY_DATA (
  KEY_ID int NOT NULL AUTO_INCREMENT,
  REFERENCE_ORDER_KEY_ITEM_ID int DEFAULT NULL,
  REFERENCE_ORDER_KEY_ITEM_TYPE varchar(100) DEFAULT NULL,
  DATE date DEFAULT NULL,
  DATE_TYPE varchar(50) DEFAULT NULL,
  QUANTITY decimal(19,4) DEFAULT NULL,
  SUGGESTED_QUANTITY decimal(19,4) DEFAULT NULL,
  PRIMARY KEY (KEY_ID)
) ENGINE=InnoDB

ALTER TABLE KETTLE_SCM_STAGE.MENU_SCM_KEY_DATA
ADD COLUMN ORDERING_PERCENTAGE DECIMAL(19, 4) default null;

CREATE TABLE KETTLE_SCM_STAGE.UOM_CONVERSION_MAPPING_DATA (
    UOM_CONVERSION_MAPPING_ID INT AUTO_INCREMENT PRIMARY KEY,
    FROM_UOM VARCHAR(50) NOT NULL,
    TO_UOM VARCHAR(50) NOT NULL,
    CONVERSION_RATIO DECIMAL(10, 4) NOT NULL,
    PRODUCT_ID INT NOT NULL UNIQUE,
    CONSTRAINT FK_UOM_PRODUCT FOREIGN KEY (PRODUCT_ID)
        REFERENCES KETTLE_SCM_STAGE.PRODUCT_DEFINITION(PRODUCT_ID)
        ON DELETE CASCADE
);

ALTER TABLE KETTLE_SCM_STAGE.MONK_WASTAGE_DETAIL_DATA ADD (ERROR_CODE INT NULL, REMAKE_REASON VARCHAR(50) NULL , EXPECTED_MILK_QUANTITY DECIMAL(16,6) NULL, RECIPE_STRING VARCHAR(50) NULL);


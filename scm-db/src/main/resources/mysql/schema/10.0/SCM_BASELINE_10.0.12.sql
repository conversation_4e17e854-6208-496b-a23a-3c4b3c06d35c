CREATE TABLE KETTLE_SCM_STAGE.VENDOR_CONTRACT_INFO (
  VENDOR_CONTRACT_ID INT AUTO_INCREMENT NOT NULL,
  VENDOR_ID INT NOT NULL,
  START_DATE DATE NOT NULL,
  <PERSON><PERSON>_DATE DATE,
  UNSIGNED_DOCUMENT_ID INT,
  VENDOR_SIGNED_DOCUMENT_ID INT,
  AUTH_SIGNED_DOCUMENT_ID INT ,
  RECORD_STATUS VARCHAR(255) NOT NULL,
  TEMPLATE_ID INT ,
  CREATED_BY VARCHAR(255) NOT NULL,
  CONTRACT_REQUESTED_BY VARCHAR(255) NOT NULL,
  CREATION_TIME DATETIME NOT NULL,
  IS_MAIL_TRIGGERED VARCHAR(255) NULL,
  OTP_VERIFIED VARCHAR(255) NULL,
  VENDOR_IP_ADDRESS VARCHAR(255) NULL,
  AUTH_IP_ADDRESS VARCHAR(255) NULL,
  VENDOR_DIGITAL_SIGN_ID INT NULL,
  AUTH_DIGITAL_SIGN_ID INT NULL,
  MAIL_TIME DATETIME NULL,
  CONSTRAINT PK_VENDOR_CONTRACT_INFO PRIMARY KEY (VENDOR_CONTRACT_ID)
);

CREATE TABLE KETTLE_SCM_STAGE.PAGE_REQUEST_DETAIL
(
    REQUEST_ID       INT AUTO_INCREMENT
        PRIMARY KEY,
    EVENT_ID         INT          NOT NULL,
    EVENT_TYPE       VARCHAR(255) NOT NULL,
    CREATED_BY       VARCHAR(255) NOT NULL,
    RECORD_STATUS    VARCHAR(255) NOT NULL,
    REGISTRATION_UTL VARCHAR(255) NOT NULL,
    AUTH_KEY         VARCHAR(255) NOT NULL,
    REQUEST_DATE     DATE         NULL
);

CREATE TABLE KETTLE_SCM_STAGE.VENDOR_CONTRACT_ITEM
(
    CONTRACT_ITEM_ID   INT AUTO_INCREMENT
        PRIMARY KEY,
    VENDOR_CONTRACT_ID INT          NOT NULL,
    VENDOR_ID          INT          NOT NULL,
    SKU_ID             INT          NOT NULL,
    SKU_PACKAGING_ID   INT          NOT NULL,
    SKU_PRICE_DATE_ID  INT          NOT NULL,
    DISPATCH_LOCATION  VARCHAR(255) NOT NULL,
    DELIVERY_LOCATION  VARCHAR(255) NOT NULL,
    CURRENT_PRICE      DECIMAL      NULL,
    NEGOTIATED_PRICE   DECIMAL      NULL,
    START_DATE         DATE         ,
    END_DATE           DATE         ,
    CREATED_BY         VARCHAR(255) NOT NULL,
    CREATION_TIME      DATETIME     NOT NULL,
    TAX_CODE      VARCHAR(200)     NOT NULL,
    TAX_PERCENTAGE     DECIMAL     NOT NULL
);

ALTER TABLE KETTLE_SCM_STAGE.SKU_PRICE_DATA ADD COLUMN END_DATE DATE ,
ADD IS_PRICE_CHANGE_REQUESTED VARCHAR(2) DEFAULT 'N';

ALTER TABLE KETTLE_SCM_STAGE.SKU_PRICE_HISTORY ADD COLUMN END_DATE DATE;
ALTER TABLE KETTLE_SCM_STAGE.SKU_PRICE_HISTORY ADD COLUMN CONTRACT_ID INTEGER;

ALTER TABLE KETTLE_SCM_STAGE.SKU_PRICE_DATA MODIFY COLUMN START_DATE DATE;
ALTER TABLE KETTLE_SCM_STAGE.SKU_PRICE_HISTORY MODIFY COLUMN START_DATE DATE;


INSERT INTO KETTLE_MASTER_STAGE.ACCESS_CONTROL_LIST_DATA (ACL_MODULE, ACL_MODULE_DESCRIPTION, ACL_STATUS, APPLICATION_NAME)
VALUES ('scm-service.vendor-contract-management.*', 'Vendor Management Resource for Contract Services', 'ACTIVE',
        'SCM_SERVICE');
INSERT INTO KETTLE_MASTER_STAGE.PARTNER_PERMISSION_MAPPING (PARTNER_ID , PERMISSION, ACL_ID, PPM_STATUS)
VALUES( 6,1111, (SELECT ACL_ID FROM KETTLE_MASTER_STAGE.ACCESS_CONTROL_LIST_DATA  WHERE ACL_MODULE ='scm-service.vendor-contract-management.*') ,'ACTIVE');



-- VCM
INSERT INTO `KETTLE_MASTER_STAGE`.`USER_ROLE_DATA`(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('SUMO - Vendor Contract Management', 'Vendor Contract Management', 'ACTIVE', '7');
INSERT INTO `KETTLE_MASTER_STAGE`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`,
                                                 `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VCM', '7', 'MENU', 'SHOW', 'SuMo ->Vendor Contract Management', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_STAGE`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`,
                                                       `LAST_UPDATE_TIME`)
VALUES ((SELECT ROLE_ID FROM KETTLE_MASTER_STAGE.USER_ROLE_DATA WHERE ROLE_NAME = 'SUMO - Vendor Contract Management'),
        (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_STAGE.ACTION_DETAIL WHERE ACTION_CODE = 'VCM'), 'ACTIVE', '124984',
        '2023-09-03 23:59:59');
-- VCMSP
INSERT INTO `KETTLE_MASTER_STAGE`.`USER_ROLE_DATA`(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('SUMO - Vendor Contract Management Stage Price', 'Vendor Contract Management Stage Price', 'ACTIVE', '7');
INSERT INTO `KETTLE_MASTER_STAGE`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`,
                                                 `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VCMSP', '7', 'SUBMENU', 'SHOW', 'SuMo ->Vendor Contract Management -> Vendor to SKU Price', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_STAGE`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`,
                                                       `LAST_UPDATE_TIME`)
VALUES ((SELECT ROLE_ID FROM KETTLE_MASTER_STAGE.USER_ROLE_DATA WHERE ROLE_NAME = 'SUMO - Vendor Contract Management Stage Price'),
        (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_STAGE.ACTION_DETAIL WHERE ACTION_CODE = 'VCMSP'), 'ACTIVE', '124984',
        '2023-09-03 23:59:59');
-- VCMRR
INSERT INTO `KETTLE_MASTER_STAGE`.`USER_ROLE_DATA`(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('SUMO - Vendor Contract Management Raise Request', 'Vendor Contract Management Raise Request', 'ACTIVE', '7');
INSERT INTO `KETTLE_MASTER_STAGE`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`,
                                                 `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VCMRR', '7', 'SUBMENU', 'SHOW', 'SuMo ->Vendor Contract Management -> Vendor to SKU Price', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_STAGE`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`,
                                                       `LAST_UPDATE_TIME`)
VALUES ((SELECT ROLE_ID FROM KETTLE_MASTER_STAGE.USER_ROLE_DATA WHERE ROLE_NAME = 'SUMO - Vendor Contract Management Raise Request'),
        (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_STAGE.ACTION_DETAIL WHERE ACTION_CODE = 'VCMRR'), 'ACTIVE', '124984',
        '2023-09-03 23:59:59');


-- VCMRP
INSERT INTO `KETTLE_MASTER_STAGE`.`USER_ROLE_DATA`(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('SUMO - Vendor Contract Management Price Preview', 'Vendor Contract Management Price Preview', 'ACTIVE', '7');
INSERT INTO `KETTLE_MASTER_STAGE`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`,
                                                 `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VCMRP', '7', 'SUBMENU', 'SHOW', 'SuMo ->Vendor Contract Management -> Vendor to SKU Price Preview', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_STAGE`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`,
                                                       `LAST_UPDATE_TIME`)
VALUES ((SELECT ROLE_ID FROM KETTLE_MASTER_STAGE.USER_ROLE_DATA WHERE ROLE_NAME = 'SUMO - Vendor Contract Management Price Preview'),
        (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_STAGE.ACTION_DETAIL WHERE ACTION_CODE = 'VCMRP'), 'ACTIVE', '124984',
        '2023-09-03 23:59:59');

-- VCMVC
INSERT INTO `KETTLE_MASTER_STAGE`.`USER_ROLE_DATA`(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('SUMO - Vendor Contract Management View Contracts', 'Vendor Contract Management View Contracts', 'ACTIVE', '7');
INSERT INTO `KETTLE_MASTER_STAGE`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`,
                                                 `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VCMVC', '7', 'MENU', 'SHOW', 'SuMo ->Vendor Contract Management -> View Contracts', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_STAGE`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`,
                                                       `LAST_UPDATE_TIME`)
VALUES ((SELECT ROLE_ID FROM KETTLE_MASTER_STAGE.USER_ROLE_DATA WHERE ROLE_NAME = 'SUMO - Vendor Contract Management View Contracts'),
        (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_STAGE.ACTION_DETAIL WHERE ACTION_CODE = 'VCMVC'), 'ACTIVE', '124984',
        '2023-09-03 23:59:59');

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('PREVIEW_PRICE', 'Vendor Contract Preview Price', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VCMPP', '7', 'ACTION', 'VIEW', 'SuMo -> Vendor Contarct Management -> Vendor To Sku Request', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'PREVIEW_PRICE'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VCMPP'), 'ACTIVE', '120063', '2023-09-05 12:00:00');


INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('VENDOR_CONTRACT_MAIL_VENDOR', 'Vendor Contract Mail Vendor', 'ACTIVE', '7');
INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA` (`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('VARIANCE_EDIT_SUBMISSION', 'Variance Edit Submission', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VEDS', '7', 'ACTION', 'VIEW', 'SuMo -> Update -> Variance Edit', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'VARIANCE_EDIT_SUBMISSION'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VEDS'), 'ACTIVE', '120063', '2023-09-05 12:00:00');



INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VCMMV', '7', 'ACTION', 'VIEW', 'SuMo -> Vendor Contarct Management -> View Contracts', 'ACTIVE');

ALTER TABLE `KETTLE_SCM_DEV`.`SKU_DEFINITION`
    ADD COLUMN `IS_BRANDED` VARCHAR(1) NULL DEFAULT NULL AFTER `DISCONTINUED_SKU`,
ADD COLUMN `BRAND` VARCHAR(255) NULL DEFAULT NULL AFTER `IS_BRANDED`;
INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'VENDOR_CONTRACT_MAIL_VENDOR'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VCMMV'), 'ACTIVE', '120063', '2023-09-05 12:00:00');


ALTER TABLE KETTLE_SCM_DEV.VENDOR_CONTRACT_INFO ADD COLUMN VENDOR_USER_NAME VARCHAR(100);
ALTER TABLE KETTLE_SCM_DEV.VENDOR_CONTRACT_INFO ADD COLUMN VENDOR_USER_DESIGNATION VARCHAR(100);
ALTER TABLE KETTLE_SCM_DEV.VENDOR_CONTRACT_ITEM ADD COLUMN IS_NEW VARCHAR(100);
ALTER TABLE KETTLE_SCM_DEV.VENDOR_DETAIL_DATA ADD COLUMN BY_PASS_CONTRACT VARCHAR(1) DEFAULT 'N';

CREATE TABLE `KETTLE_SCM_DEV`.`LINKED_ADVANCE_FOR_PAYMENT`
(
    `LINKED_PARENT_ADVANCE_ID` INT(11) NOT NULL AUTO_INCREMENT,
    `ADVANCE_PAYMENT_ID`       INT(11) NOT NULL,
    `PAYMENT_REQUEST_ID`       INT(11) NOT NULL,
    PRIMARY KEY (`LINKED_PARENT_ADVANCE_ID`),
    UNIQUE KEY `LINKED_PARENT_ADVANCE_ID_UNIQUE` (`LINKED_PARENT_ADVANCE_ID`)
);

ALTER TABLE `KETTLE_SCM_DEV`.`GATEPASS_ITEM_ASSET_MAPPING`
    ADD COLUMN `RETURN_GATEPASS_ITEM_ID` INT(11) ;

ALTER TABLE `KETTLE_SCM_DEV`.`SALES_PERFORMA_INVOICE`
    ADD COLUMN `CREDIT_NOTE_DOC_ID` INT(11) ,
    ADD COLUMN `DEBIT_NOTE_DOC_ID` INT(11) ,
    ADD COLUMN `CREDIT_NOTE_DOC_URL` VARCHAR(500) ,
    ADD COLUMN `DEBIT_NOTE_DOC_URL` VARCHAR(500) ;


ALTER TABLE `KETTLE_SCM_DEV`.`STOCK_INVENTORY`
ADD COLUMN `ORIGINAL_EXPECTED_CLOSING` DECIMAL(16,6) NULL DEFAULT NULL AFTER `ORIGINAL_CLOSING_STOCK`,
ADD COLUMN `VARIANCE_TILL_NOW` DECIMAL(16,6) NULL DEFAULT NULL AFTER `ORIGINAL_EXPECTED_CLOSING`,
ADD COLUMN `EXTRA_VARIANCE` DECIMAL(16,6) NULL DEFAULT NULL AFTER `VARIANCE_TILL_NOW`;


CREATE INDEX WASTAGE_EVENT_GENERATION_TIME ON KETTLE_SCM_DUMP.WASTAGE_EVENT(GENERATION_TIME) USING BTREE;

CREATE INDEX STOCK_INVENTORY_PRODUCT_ID ON KETTLE_SCM_DUMP.STOCK_INVENTORY(PRODUCT_ID) USING BTREE;

CREATE TABLE `KETTLE_SCM_DEV`.`CONSUMABLE_STOCK_STATE` (
  `CONSUMABLE_STOCK_STATE_ID` INT NOT NULL AUTO_INCREMENT,
  `UNIT_ID` INT(11) NULL,
  `PRODUCT_ID` INT(11) NULL,
  `SKU_ID` INT(11) NULL,
  `BAD_STOCK` DECIMAL(16,6) NULL,
  `GOOD_STOCK` DECIMAL(16,6) NULL,
  PRIMARY KEY (`CONSUMABLE_STOCK_STATE_ID`));


CREATE TABLE KETTLE_SCM_DEV.BUSINESS_COST_CENTER_MAPPING_DATA (
  MAPPING_ID INT AUTO_INCREMENT NOT NULL,
   COST_CENTER_ID INT NULL,
   BUSINESS_COST_CENTER_ID INT NULL,
   MAPPING_STATUS VARCHAR(255) NULL,
   CONSTRAINT PK_BUSINESS_COST_CENTER_MAPPING_DATA PRIMARY KEY (MAPPING_ID)
);


ALTER TABLE `KETTLE_SCM`.`SALES_PERFORMA_INVOICE_ITEM`
    CHANGE COLUMN `PKG_QTY` `PKG_QTY` DECIMAL(15,6) NOT NULL ,
    CHANGE COLUMN `CONVERSION_RATIO` `CONVERSION_RATIO` DECIMAL(15,6) NOT NULL ,
    CHANGE COLUMN `QTY` `QTY` DECIMAL(15,6) NOT NULL ,
    CHANGE COLUMN `CURRENT_PRICE` `CURRENT_PRICE` DECIMAL(15,6) NULL DEFAULT NULL ,
    CHANGE COLUMN `MAPPED_PRICE` `MAPPED_PRICE` DECIMAL(15,6) NOT NULL ,
    CHANGE COLUMN `SELLING_PRICE` `SELLING_PRICE` DECIMAL(15,6) NOT NULL ,
    CHANGE COLUMN `CURRENT_AMOUNT` `CURRENT_AMOUNT` DECIMAL(15,6) NULL DEFAULT NULL ,
    CHANGE COLUMN `MAPPED_AMOUNT` `MAPPED_AMOUNT` DECIMAL(15,6) NOT NULL ,
    CHANGE COLUMN `SELLING_AMOUNT` `SELLING_AMOUNT` DECIMAL(15,6) NOT NULL ,
    CHANGE COLUMN `TOTAL_TAX` `TOTAL_TAX` DECIMAL(15,6) NOT NULL ;

CREATE INDEX WASTAGE_EVENT_GENERATION_TIME ON KETTLE_SCM_DUMP.WASTAGE_EVENT(GENERATION_TIME) USING BTREE;

CREATE INDEX STOCK_INVENTORY_PRODUCT_ID ON KETTLE_SCM_DUMP.STOCK_INVENTORY(PRODUCT_ID) USING BTREE;

CREATE TABLE `KETTLE_SCM_DEV`.`STOCK_TAKE_SUMO_DAY_CLOSE_EVENT`
(
    `STOCK_TAKE_SUMO_DAY_CLOSE_EVENT_ID` INT(11) NOT NULL AUTO_INCREMENT,
    `UNIT_ID`                            INT(11) NOT NULL,
    `DAY_CLOSE_EVENT`                    INT(11) NOT NULL,
    `STOCK_TAKE_TYPE`                    VARCHAR(45)  NOT NULL,
    `EVENT_STATUS`                       VARCHAR(45)  NOT NULL,
    `EVENT_CREATED_BY`                   INT(11) NOT NULL,
    `EVENT_CREATED_AT`                   TIMESTAMP NULL DEFAULT NULL,
    `DEVICE_INFO`                        VARCHAR(200) NOT NULL,
    `LAST_UPDATED_TIME`                  TIMESTAMP NULL DEFAULT NULL,
    PRIMARY KEY (`STOCK_TAKE_SUMO_DAY_CLOSE_EVENT_ID`),
    UNIQUE KEY `STOCK_TAKE_SUMO_DAY_CLOSE_EVENT_ID_UNIQUE` (`STOCK_TAKE_SUMO_DAY_CLOSE_EVENT_ID`)
);


CREATE TABLE `KETTLE_SCM_DEV`.`STOCK_TAKE_SUMO_DAY_CLOSE_PRODUCTS`
(
    `DAY_CLOSE_PRODUCT_ITEM_ID`          INT(11) NOT NULL AUTO_INCREMENT,
    `STOCK_TAKE_SUMO_DAY_CLOSE_EVENT_ID` INT(11) NOT NULL,
    `PRODUCT_ID`                         INT(11) DEFAULT NULL,
    `UPDATED_TIME`                       TIMESTAMP NULL DEFAULT NULL,
    PRIMARY KEY (`DAY_CLOSE_PRODUCT_ITEM_ID`)
);

CREATE TABLE `KETTLE_SCM_DEV`.`DAY_CLOSE_PRODUCT_PACKAGING_MAPPINGS`
(
    `PRODUCT_PACKAGING_MAPPING_ID` INT(11) NOT NULL AUTO_INCREMENT,
    `DAY_CLOSE_PRODUCT_ITEM_ID`    INT(11) NOT NULL,
    `PACKAGING_ID`                 INT(11) DEFAULT NULL,
    `QUANTITY`                     DECIMAL(10, 0) DEFAULT NULL,
    PRIMARY KEY (`PRODUCT_PACKAGING_MAPPING_ID`)
);

ALTER TABLE `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION`
ADD COLUMN `PRODUCT_TYPE` VARCHAR(100) NOT NULL AFTER `RECIPE_REQUIRED`;

ALTER TABLE `KETTLE_SCM_DEV`.`SALES_PERFORMA_INVOICE`
ADD COLUMN `REFERENCE_INVOICE_NUMBER` INT(11) ;

ALTER TABLE `KETTLE_SCM_DEV`.`STOCK_TAKE_SUMO_DAY_CLOSE_EVENT`
CHANGE COLUMN `DAY_CLOSE_EVENT` `KETTLE_DAY_CLOSE_EVENT` INT(11) NULL DEFAULT NULL;

ALTER TABLE `KETTLE_SCM_DEV`.`STOCK_TAKE_SUMO_DAY_CLOSE_EVENT`
ADD COLUMN `SUMO_DAY_CLOSE_EVENT` INT NULL DEFAULT NULL AFTER `KETTLE_DAY_CLOSE_EVENT`;

ALTER TABLE `KETTLE_SCM_DEV`.`STOCK_TAKE_SUMO_DAY_CLOSE_EVENT`
ADD COLUMN `EVENT_SUBMITTED_AT` TIMESTAMP NULL DEFAULT NULL AFTER `EVENT_CREATED_AT`;

ALTER TABLE `KETTLE_SCM_DEV`.`TRANSFER_ORDER`
    ADD COLUMN `PARTIAL_INVOICE_IRN` VARCHAR(255) ;


INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('Approve Lost Asset', 'Access to approve Lost Asset', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('SCMALA', '7', 'SUBMENU', 'SHOW', 'SuMo -> Approvals ->Lost Asset', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Approve Lost Asset'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'SCMALA'), 'ACTIVE', '140019', '2023-12-11 12:00:00');


CREATE TABLE `KETTLE_SCM`.`SALES_PERFORMA_INVOICE_CORRECTED` (
  `ID` INT(11) NOT NULL AUTO_INCREMENT,
  `INVOICE_ID` INT(45) DEFAULT NULL,
  `INVOICE_STATUS` VARCHAR(50) DEFAULT NULL,
  `TYPE` VARCHAR(45) DEFAULT NULL,
  `DOC_ID` VARCHAR(50) DEFAULT NULL,
  `CREDIT_NOTE_DOC_ID` INT(11) DEFAULT NULL,
  `CREDIT_NOTE_DOC_URL` VARCHAR(500) DEFAULT NULL,
  `GENERATED_CREDIT_NOTE_ID` VARCHAR(50) DEFAULT NULL,
  `DEBIT_NOTE_DOC_ID` INT(11) DEFAULT NULL,
  `DEBIT_NOTE_DOC_URL` VARCHAR(500) DEFAULT NULL,
  `GENERATED_DEBIT_NOTE_ID` VARCHAR(50) DEFAULT NULL,
  `IRN_NO` VARCHAR(500) DEFAULT NULL,
  `UPLOADED_ACK_NO` VARCHAR(50) DEFAULT NULL,
  `UPLOADED_EWAY_NO` VARCHAR(50) DEFAULT NULL,
  `BARCODE_ID` INT(11) DEFAULT NULL,
  `SIGNED_QR_CODE` VARCHAR(5000) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=INNODB AUTO_INCREMENT=47 DEFAULT CHARSET=LATIN1;

CREATE TABLE `KETTLE_SCM`.`SALES_PERFORMA_INVOICE_ITEM_CORRECTED` (
  `ITEM_ID` INT(11) NOT NULL AUTO_INCREMENT,
  `ID` INT(11) NOT NULL,
  `SKU_ID` INT(11) DEFAULT NULL,
  `SKU_NAME` VARCHAR(500) DEFAULT NULL,
  `PRICE` DECIMAL(15,6) DEFAULT NULL,
  `REVISED_PRICE` DECIMAL(15,6) DEFAULT NULL,
  `CORRECTED_PRICE` DECIMAL(15,6) DEFAULT NULL,
  `PKG_QTY` DECIMAL(15,6) DEFAULT NULL,
  `REVISED_PKG_QTY` DECIMAL(15,6) DEFAULT NULL,
  `CORRECTED_PKG_QTY` DECIMAL(15,4) DEFAULT NULL,
  `AMOUNT` DECIMAL(15,6) DEFAULT NULL,
  `REVISED_AMOUNT` DECIMAL(15,6) DEFAULT NULL,
  `TAX` DECIMAL(15,6) DEFAULT NULL,
  `REVISED_TAX` DECIMAL(15,6) DEFAULT NULL,
  PRIMARY KEY (`ITEM_ID`)
) ENGINE=INNODB AUTO_INCREMENT=69 DEFAULT CHARSET=LATIN1;


ALTER TABLE `KETTLE_SCM`.`SALES_PERFORMA_INVOICE`
    ADD COLUMN `CREDIT_NOTE_DOC_ID` INT(11) ,
    ADD COLUMN `GENERATED_CREDIT_NOTE_ID` VARCHAR(100),
    ADD COLUMN `CREDIT_NOTE_DOC_URL` VARCHAR(500) ,
    ADD COLUMN `DEBIT_NOTE_DOC_ID` INT(11) ,
    ADD COLUMN `GENERATED_DEBIT_NOTE_ID` VARCHAR(100),
    ADD COLUMN `DEBIT_NOTE_DOC_URL` VARCHAR(500) ;


ALTER TABLE `KETTLE_SCM`.`SALES_PERFORMA_INVOICE_CREDIT_DEBIT_NOTE`
CHANGE COLUMN `INVOICE_ID` `INVOICE_ID` VARCHAR(1000) NULL ;

DELETE FROM KETTLE_MASTER.ROLE_ACTION_MAPPING where
ACTION_DETAIL_ID in  (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER.ACTION_DETAIL where ACTION_CODE = 'VICNA')
and ROLE_ID in (SELECT ROLE_ID FROM KETTLE_MASTER.USER_ROLE_DATA  where ROLE_NAME = 'VENDOR INVOICE CREDIT NOTE APPROVER') ;
DELETE FROM KETTLE_MASTER.EMPLOYEE_ROLE_MAPPING where ROLE_ID in ( SELECT ROLE_ID FROM KETTLE_MASTER.USER_ROLE_DATA  where ROLE_NAME = 'VENDOR INVOICE CREDIT NOTE APPROVER')  ;

DELETE FROM KETTLE_MASTER.ROLE_ACTION_MAPPING where
ACTION_DETAIL_ID in  (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER.ACTION_DETAIL where ACTION_CODE = 'VICDA')
and ROLE_ID in (SELECT ROLE_ID FROM KETTLE_MASTER.USER_ROLE_DATA  where ROLE_NAME = 'VENDOR INVOICE CORRECTION DETAILS APPROVER') ;
DELETE FROM KETTLE_MASTER.EMPLOYEE_ROLE_MAPPING where ROLE_ID in ( SELECT ROLE_ID FROM KETTLE_MASTER.USER_ROLE_DATA  where ROLE_NAME = 'VENDOR INVOICE CORRECTION DETAILS APPROVER')  ;

INSERT INTO `KETTLE_MASTER`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('VENDOR INVOICE CREDIT NOTE APPROVER L1', 'Approve vendor invoice credit note level 1 ', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VICNPL1', '7', 'ACTION', 'VIEW', 'SUMO -> VENDOR INVOICING -> VIEW CREDIT NOTE -> PENDING APPROVAL L1 -> SHOW', 'ACTIVE');

INSERT INTO `KETTLE_MASTER`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER.USER_ROLE_DATA WHERE ROLE_NAME = 'VENDOR INVOICE CREDIT NOTE APPROVER L1'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER.ACTION_DETAIL WHERE ACTION_CODE = 'VICNPL1'),
       'ACTIVE', '120063', '2022-07-28 12:00:00');

INSERT INTO `KETTLE_MASTER`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('VENDOR INVOICE CREDIT NOTE APPROVER L2', 'Approve vendor invoice credit note level 2 ', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VICNPL2', '7', 'ACTION', 'VIEW', 'SUMO -> VENDOR INVOICING -> VIEW CREDIT NOTE -> PENDING APPROVAL L2 -> SHOW', 'ACTIVE');

INSERT INTO `KETTLE_MASTER`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER.USER_ROLE_DATA WHERE ROLE_NAME = 'VENDOR INVOICE CREDIT NOTE APPROVER L2'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER.ACTION_DETAIL WHERE ACTION_CODE = 'VICNPL2'),
       'ACTIVE', '120063', '2022-07-28 12:00:00');


INSERT INTO `KETTLE_MASTER`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('VENDOR INVOICE CORRECTION DETAILS APPROVER L1', 'Approve vendor invoice correction details level 1', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VICDL1', '7', 'ACTION', 'VIEW', 'SUMO -> VENDOR INVOICING -> VIEW CORRECTION DETAIL -> CORRECTION APPROVAL L1 -> SHOW', 'ACTIVE');

INSERT INTO `KETTLE_MASTER`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER.USER_ROLE_DATA WHERE ROLE_NAME = 'VENDOR INVOICE CORRECTION DETAILS APPROVER L1'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER.ACTION_DETAIL WHERE ACTION_CODE = 'VICDL1'),
       'ACTIVE', '120063', '2022-07-28 12:00:00');

INSERT INTO `KETTLE_MASTER`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('VENDOR INVOICE CORRECTION DETAILS APPROVER L2', 'Approve vendor invoice correction details level 2', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VICDL2', '7', 'ACTION', 'VIEW', 'SUMO -> VENDOR INVOICING -> VIEW CORRECTION DETAIL -> CORRECTION APPROVAL L2 -> SHOW', 'ACTIVE');

INSERT INTO `KETTLE_MASTER`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER.USER_ROLE_DATA WHERE ROLE_NAME = 'VENDOR INVOICE CORRECTION DETAILS APPROVER L2'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER.ACTION_DETAIL WHERE ACTION_CODE = 'VICDL2'),
       'ACTIVE', '120063', '2022-07-28 12:00:00');



CREATE INDEX WASTAGE_EVENT_GENERATION_TIME ON KETTLE_SCM_DUMP.WASTAGE_EVENT(GENERATION_TIME) USING BTREE;

CREATE INDEX STOCK_INVENTORY_PRODUCT_ID ON KETTLE_SCM_DUMP.STOCK_INVENTORY(PRODUCT_ID) USING BTREE;

CREATE TABLE `KETTLE_SCM_DEV`.`STOCK_TAKE_SUMO_DAY_CLOSE_EVENT`
(
    `STOCK_TAKE_SUMO_DAY_CLOSE_EVENT_ID` INT(11) NOT NULL AUTO_INCREMENT,
    `UNIT_ID`                            INT(11) NOT NULL,
    `DAY_CLOSE_EVENT`                    INT(11) NOT NULL,
    `STOCK_TAKE_TYPE`                    VARCHAR(45)  NOT NULL,
    `EVENT_STATUS`                       VARCHAR(45)  NOT NULL,
    `EVENT_CREATED_BY`                   INT(11) NOT NULL,
    `EVENT_CREATED_AT`                   TIMESTAMP NULL DEFAULT NULL,
    `DEVICE_INFO`                        VARCHAR(200) NOT NULL,
    `LAST_UPDATED_TIME`                  TIMESTAMP NULL DEFAULT NULL,
    PRIMARY KEY (`STOCK_TAKE_SUMO_DAY_CLOSE_EVENT_ID`),
    UNIQUE KEY `STOCK_TAKE_SUMO_DAY_CLOSE_EVENT_ID_UNIQUE` (`STOCK_TAKE_SUMO_DAY_CLOSE_EVENT_ID`)
);


CREATE TABLE `KETTLE_SCM_DEV`.`STOCK_TAKE_SUMO_DAY_CLOSE_PRODUCTS`
(
    `DAY_CLOSE_PRODUCT_ITEM_ID`          INT(11) NOT NULL AUTO_INCREMENT,
    `STOCK_TAKE_SUMO_DAY_CLOSE_EVENT_ID` INT(11) NOT NULL,
    `PRODUCT_ID`                         INT(11) DEFAULT NULL,
    `UPDATED_TIME`                       TIMESTAMP NULL DEFAULT NULL,
    PRIMARY KEY (`DAY_CLOSE_PRODUCT_ITEM_ID`)
);

CREATE TABLE `KETTLE_SCM_DEV`.`DAY_CLOSE_PRODUCT_PACKAGING_MAPPINGS`
(
    `PRODUCT_PACKAGING_MAPPING_ID` INT(11) NOT NULL AUTO_INCREMENT,
    `DAY_CLOSE_PRODUCT_ITEM_ID`    INT(11) NOT NULL,
    `PACKAGING_ID`                 INT(11) DEFAULT NULL,
    `QUANTITY`                     DECIMAL(10, 0) DEFAULT NULL,
    PRIMARY KEY (`PRODUCT_PACKAGING_MAPPING_ID`)
);

ALTER TABLE `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION`
ADD COLUMN `PRODUCT_TYPE` VARCHAR(100) NOT NULL AFTER `RECIPE_REQUIRED`;

ALTER TABLE `KETTLE_SCM_DEV`.`SALES_PERFORMA_INVOICE`
ADD COLUMN `REFERENCE_INVOICE_NUMBER` INT(11) ;

ALTER TABLE `KETTLE_SCM_DEV`.`VENDOR_COMPANY_DETAIL`
ADD COLUMN `PAN_STATUS` VARCHAR(100) NULL DEFAULT NULL AFTER `PAN`;





INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('SCMALA', '7', 'SUBMENU', 'SHOW', 'SuMo -> Approvals ->Lost Asset', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Approve Lost Asset'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'SCMALA'), 'ACTIVE', '140019', '2023-12-11 12:00:00');

ALTER TABLE `KETTLE_SCM_DEV`.`COST_ELEMENT_DATA`
ADD COLUMN `LOWER_PRICE_RANGE` DECIMAL(10,2) NULL DEFAULT 0 ,
ADD COLUMN `UPPER_PRICE_RANGE` DECIMAL(10,2) NULL DEFAULT 0 ;


ALTER TABLE `KETTLE_SCM_DEV`.`VENDOR_CONTRACT_SO_INFO`
ADD COLUMN `LAST_UPDATE_TIME` DATETIME NULL AFTER `VENDOR_LOCATION`;

ALTER TABLE `KETTLE_SCM_DEV`.`COST_ELEMENT_DATA`
ADD COLUMN `LOWER_PRICE_RANGE` DECIMAL(10,2) NULL DEFAULT 0 ,
ADD COLUMN `UPPER_PRICE_RANGE` DECIMAL(10,2) NULL DEFAULT 0 ;


ALTER TABLE `KETTLE_SCM_STAGE`.`VENDOR_CONTRACT_SO_INFO`
ADD COLUMN `VENDOR_NAME` VARCHAR(255) NULL AFTER `LAST_UPDATE_TIME`,
ADD COLUMN `VENDOR_DESIGNATION` VARCHAR(255) NULL AFTER `VENDOR_NAME`;

ALTER TABLE `KETTLE_SCM_STAGE`.`PAYMENT_REQUEST`
ADD COLUMN `IS_SO_CONTRACT_BREACH` VARCHAR(1) NULL AFTER `CARD_PAYMENT_COMMENT`,
ADD COLUMN `SO_CONTRACT_BREACH_APPROVAL_DOC_ID` INT(11) NULL AFTER `IS_SO_CONTRACT_BREACH`;


CREATE TABLE KETTLE_SCM_DEV.BUSINESS_COST_CENTER_MAPPING_DATA (
  MAPPING_ID INT AUTO_INCREMENT NOT NULL,
   COST_CENTER_ID INT NULL,
   BUSINESS_COST_CENTER_ID INT NULL,
   MAPPING_STATUS VARCHAR(255) NULL,
   CONSTRAINT PK_BUSINESS_COST_CENTER_MAPPING_DATA PRIMARY KEY (MAPPING_ID)
);
ALTER TABLE `KETTLE_SCM_DEV`.`SERVICE_RECEIVED_DATA`
CHANGE COLUMN `SERVICE_PROOF_DOCUMENT_IDS` `SERVICE_PROOF_DOCUMENT_IDS` VARCHAR(200) NULL ;

CREATE INDEX HOLIDAYS_LIST_DATA_HOLIDAY_DATE ON KETTLE_SCM_DEV.HOLIDAYS_LIST_DATA(HOLIDAY_DATE) USING BTREE;

ALTER TABLE `KETTLE_SCM_DEV`.`INVENTORY_DRILLDOWN`
ADD COLUMN `AVERAGE_PRICE` DECIMAL(16,6) NULL DEFAULT NULL AFTER `PRICE`;

ALTER TABLE `KETTLE_SCM_DEV`.`STOCK_INVENTORY`
ADD COLUMN `AVERAGE_PRICE` DECIMAL(16,6) NULL DEFAULT NULL AFTER `ORIGINAL_EXPECTED_CLOSING`;

CREATE TABLE `KETTLE_SCM_DEV`.`DAY_CLOSE_COST_DETAIL_DATA_CAFE_DUMP` (
`CAFE_DUMP_DETAIL_ID` int(11) NOT NULL AUTO_INCREMENT,
`COST_DETAIL_DATA_ID` int(11),
`PRODUCT_ID` int(11) NOT NULL,
`UNIT_ID` int(11) NOT NULL,
`PRICE` decimal(16,6) NOT NULL,
`QUANTITY` decimal(16,6) NOT NULL,
`UNIT_OF_MEASURE` varchar(10) NOT NULL,
`IS_LATEST` varchar(1) NOT NULL,
`LAST_UPDATE_TIME` timestamp NULL DEFAULT NULL,
`EXPIRY_DATE` datetime DEFAULT NULL,
`CREATION_REASON` varchar(20) DEFAULT NULL,
`CREATION_ITEM_ID` int(20) DEFAULT NULL,
`DAY_CLOSE_EVENT_ID` int(11) NOT NULL,
PRIMARY KEY (`CAFE_DUMP_DETAIL_ID`)
);

CREATE TABLE `KETTLE_SCM_DEV`.`DAY_CLOSE_COST_DETAIL_DATA_WH_DUMP` (
`WH_DUMP_DETAIL_ID` int(11) NOT NULL AUTO_INCREMENT,
`COST_DETAIL_DATA_ID` int(11),
`SKU_ID` int(11) NOT NULL,
`UNIT_ID` int(11) NOT NULL,
`PRICE` decimal(16,6) NOT NULL,
`QUANTITY` decimal(16,6) NOT NULL,
`UNIT_OF_MEASURE` varchar(10) NOT NULL,
`IS_LATEST` varchar(1) NOT NULL,
`LAST_UPDATE_TIME` timestamp NULL DEFAULT NULL,
`EXPIRY_DATE` datetime DEFAULT NULL,
`CREATION_REASON` varchar(20) DEFAULT NULL,
`CREATION_ITEM_ID` int(20) DEFAULT NULL,
`DAY_CLOSE_EVENT_ID` int(11) NOT NULL,
PRIMARY KEY (`WH_DUMP_DETAIL_ID`)
);

CREATE TABLE `PAYMENT_REQUEST_METADATA` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `PAYMENT_REQUEST_ID` int(11) DEFAULT NULL,
  `IS_RCM` varchar(255) DEFAULT NULL,
  `GST_RATE` double DEFAULT NULL,
  `IS_GST_AVAILED` varchar(255) DEFAULT NULL,
  `TDS_LEDGER` bigint(20) DEFAULT NULL,
  `LDC_VENDOR_RATE_ID` bigint(20) DEFAULT NULL,
  `RECIPIENT_STATE_ID` bigint(20) DEFAULT NULL,
  `SUPPLIER_STATE_ID` bigint(20) DEFAULT NULL,
  `TDS_PERCENTAGE` double DEFAULT NULL,
  `LOGGED_MESSAGES` longtext,
  PRIMARY KEY (`ID`),
  KEY `FK_PAYMENT_REQUEST_METADATA_ON_LDC_VENDOR_RATE` (`LDC_VENDOR_RATE_ID`),
  KEY `FK_PAYMENT_REQUEST_METADATA_ON_PAYMENT_REQUEST` (`PAYMENT_REQUEST_ID`),
  KEY `FK_PAYMENT_REQUEST_METADATA_ON_RECIPIENT_STATE` (`RECIPIENT_STATE_ID`),
  KEY `FK_PAYMENT_REQUEST_METADATA_ON_SUPPLIER_STATE` (`SUPPLIER_STATE_ID`),
  KEY `FK_PAYMENT_REQUEST_METADATA_ON_TDS_LEDGER_RATE` (`TDS_LEDGER`),
  CONSTRAINT `FK_PAYMENT_REQUEST_METADATA_ON_LDC_VENDOR_RATE` FOREIGN KEY (`LDC_VENDOR_RATE_ID`) REFERENCES `LDC_VENDOR_DATA` (`ID`),
  CONSTRAINT `FK_PAYMENT_REQUEST_METADATA_ON_PAYMENT_REQUEST` FOREIGN KEY (`PAYMENT_REQUEST_ID`) REFERENCES `PAYMENT_REQUEST` (`PAYMENT_REQUEST_ID`),
  CONSTRAINT `FK_PAYMENT_REQUEST_METADATA_ON_RECIPIENT_STATE` FOREIGN KEY (`RECIPIENT_STATE_ID`) REFERENCES `GST_OF_STPL` (`ID`),
  CONSTRAINT `FK_PAYMENT_REQUEST_METADATA_ON_SUPPLIER_STATE` FOREIGN KEY (`SUPPLIER_STATE_ID`) REFERENCES `GST_STATE_METADATA` (`ID`),
  CONSTRAINT `FK_PAYMENT_REQUEST_METADATA_ON_TDS_LEDGER_RATE` FOREIGN KEY (`TDS_LEDGER`) REFERENCES `TDS_LEDGER_RATE` (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=latin1;

ALTER TABLE `KETTLE_SCM_STAGE`.`VENDOR_COMPANY_DETAIL`
ADD COLUMN `PAN_STATUS_UPDATED_AT` DATETIME NULL DEFAULT NULL AFTER `MSME_EXPIRE_DATE`;

ALTER TABLE `KETTLE_SCM_STAGE`.`VENDOR_ACCOUNT_DETAILS`
ADD COLUMN `SECTION_206_UPDATED_AT` DATETIME NULL DEFAULT NULL AFTER `SECTION_206`;

ALTER TABLE `KETTLE_SCM_DEV`.`STOCK_INVENTORY`
ADD COLUMN `EXPECTED_EXPIRY_WASTAGE` DECIMAL(16,6) NULL DEFAULT NULL AFTER `VARIANCE_PRICE`,
ADD COLUMN `EXPECTED_EXPIRY_WASTAGE_BY_NOW` DECIMAL(16,6) NULL DEFAULT NULL AFTER `EXPECTED_EXPIRY_WASTAGE`,
ADD COLUMN `EXPECTED_EXPIRY_WASTAGE_AFTER_CONSUMPTION` DECIMAL(16,6) NULL DEFAULT NULL AFTER `EXPECTED_EXPIRY_WASTAGE_BY_NOW`,
ADD COLUMN `ACTUAL_EXPIRY_WASTAGE` DECIMAL(16,6) NULL DEFAULT NULL AFTER `EXPECTED_EXPIRY_WASTAGE_AFTER_CONSUMPTION`,
ADD COLUMN `DEVIATION_OF_EXPIRY_WASTAGE` DECIMAL(16,6) NULL DEFAULT NULL AFTER `ACTUAL_EXPIRY_WASTAGE`,
ADD COLUMN `NEXT_DAY_EXPECTED_EXPIRY_WASTAGE` DECIMAL(16,6) NULL DEFAULT NULL AFTER `DEVIATION_OF_EXPIRY_WASTAGE`,
ADD COLUMN `NEXT_DAY_EXPECTED_EXPIRY_WASTAGE_BY_NOW` DECIMAL(16,6) NULL DEFAULT NULL AFTER `NEXT_DAY_EXPECTED_EXPIRY_WASTAGE`;

ALTER TABLE `KETTLE_SCM_DEV`.`REFERENCE_ORDER`
ADD COLUMN `BRAND` VARCHAR(100) NULL DEFAULT NULL AFTER `REF_ORDER_SOURCE`;


CREATE TABLE KETTLE_SCM_STAGE.TDS_LEDGER_RATE (
CREATE TABLE  KETTLE_SCM_STAGE.TDS_LEDGER_RATE (
  ID BIGINT AUTO_INCREMENT NOT NULL,
   LEDGER_NAME VARCHAR(255) NULL,
   RATE DOUBLE NULL,
   SECTION VARCHAR(255) NULL,
   STATUS VARCHAR(255) NULL,
   CONSTRAINT PK_TDS_LEDGER_RATE PRIMARY KEY (ID)
);

CREATE TABLE KETTLE_SCM_STAGE.GST_STATE_METADATA (
  ID BIGINT AUTO_INCREMENT NOT NULL,
   GST_STATE_CODE INT NULL,
   STATE_NAME VARCHAR(255) NULL,
   ALPHA_CODE VARCHAR(255) NULL,
   CONSTRAINT pk_gst_state_metadata PRIMARY KEY (id)
);
CREATE TABLE KETTLE_SCM_STAGE.GST_OF_STPL(
  ID BIGINT AUTO_INCREMENT NOT NULL,
   ALPHA_CODE VARCHAR(255) NULL,
   NAME VARCHAR(255) NULL,
   GSTIN VARCHAR(255) NULL,
   CONSTRAINT PK_GST_OF_STPL PRIMARY KEY (id)
);

ALTER TABLE GST_OF_STPL ADD CONSTRAINT UC_GST_OF_STPL_GSTIN UNIQUE (GSTIN);

ALTER TABLE `KETTLE_SCM_STAGE`.`VENDOR_DETAIL_DATA`
ADD COLUMN `IS_CC_VENDOR` VARCHAR(1)  DEFAULT 'N',
ADD COLUMN `IS_ECOM_PARTY` VARCHAR(1)  DEFAULT 'N';

ALTER TABLE `KETTLE_SCM_DEV`.`SERVICE_RECEIVED_DATA`
CHANGE COLUMN `SERVICE_PROOF_DOCUMENT_IDS` `SERVICE_PROOF_DOCUMENT_IDS` VARCHAR(200) NULL ;

CREATE INDEX HOLIDAYS_LIST_DATA_HOLIDAY_DATE ON KETTLE_SCM_DEV.HOLIDAYS_LIST_DATA(HOLIDAY_DATE) USING BTREE;

CREATE TABLE KETTLE_SCM_DEV.LDC_VENDOR_DATA (
  ID BIGINT AUTO_INCREMENT NOT NULL,
   LDC_LIMIT DOUBLE NOT NULL,
   LDC_TENURE_FROM DATETIME NOT NULL,
   LDC_TENURE_TO DATETIME NOT NULL,
   LDC_TDS_RATE DOUBLE NOT NULL,
   LDC_TDS_SECTION VARCHAR(255) NOT NULL,
   LDC_CERTIFICATE_NO VARCHAR(255) NOT NULL,
   VENDOR_ID INT NOT NULL,
   REMAINING_LIMIT DOUBLE NOT NULL,
   STATUS VARCHAR(255) NOT NULL,
   CREATED_BY INT NOT NULL,
   CREATED_AT DATETIME NOT NULL,
   UPDATED_BY INT NULL,
   UPDATED_AT DATETIME NULL,
   CONSTRAINT PK_LDC_VENDOR_DATA PRIMARY KEY (ID)
);

ALTER TABLE LDC_VENDOR_DATA ADD CONSTRAINT FK_LDC_VENDOR_DATA_ON_VENDOR FOREIGN KEY (VENDOR_ID) REFERENCES VENDOR_DETAIL_DATA (VENDOR_ID);

CREATE TABLE `PAYMENT_REQUEST_METADATA` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `PAYMENT_REQUEST_ID` int(11) DEFAULT NULL,
  `IS_RCM` varchar(255) DEFAULT NULL,
  `GST_RATE` double DEFAULT NULL,
  `IS_GST_AVAILED` varchar(255) DEFAULT NULL,
  `TDS_LEDGER` bigint(20) DEFAULT NULL,
  `LDC_VENDOR_RATE_ID` bigint(20) DEFAULT NULL,
  `RECIPIENT_STATE_ID` bigint(20) DEFAULT NULL,
  `SUPPLIER_STATE_ID` bigint(20) DEFAULT NULL,
  `TDS_PERCENTAGE` double DEFAULT NULL,
  `LOGGED_MESSAGES` longtext,
  PRIMARY KEY (`ID`),
  KEY `FK_PAYMENT_REQUEST_METADATA_ON_LDC_VENDOR_RATE` (`LDC_VENDOR_RATE_ID`),
  KEY `FK_PAYMENT_REQUEST_METADATA_ON_PAYMENT_REQUEST` (`PAYMENT_REQUEST_ID`),
  KEY `FK_PAYMENT_REQUEST_METADATA_ON_RECIPIENT_STATE` (`RECIPIENT_STATE_ID`),
  KEY `FK_PAYMENT_REQUEST_METADATA_ON_SUPPLIER_STATE` (`SUPPLIER_STATE_ID`),
  KEY `FK_PAYMENT_REQUEST_METADATA_ON_TDS_LEDGER_RATE` (`TDS_LEDGER`),
  CONSTRAINT `FK_PAYMENT_REQUEST_METADATA_ON_LDC_VENDOR_RATE` FOREIGN KEY (`LDC_VENDOR_RATE_ID`) REFERENCES `LDC_VENDOR_DATA` (`ID`),
  CONSTRAINT `FK_PAYMENT_REQUEST_METADATA_ON_PAYMENT_REQUEST` FOREIGN KEY (`PAYMENT_REQUEST_ID`) REFERENCES `PAYMENT_REQUEST` (`PAYMENT_REQUEST_ID`),
  CONSTRAINT `FK_PAYMENT_REQUEST_METADATA_ON_RECIPIENT_STATE` FOREIGN KEY (`RECIPIENT_STATE_ID`) REFERENCES `GST_OF_STPL` (`ID`),
  CONSTRAINT `FK_PAYMENT_REQUEST_METADATA_ON_SUPPLIER_STATE` FOREIGN KEY (`SUPPLIER_STATE_ID`) REFERENCES `GST_STATE_METADATA` (`ID`),
  CONSTRAINT `FK_PAYMENT_REQUEST_METADATA_ON_TDS_LEDGER_RATE` FOREIGN KEY (`TDS_LEDGER`) REFERENCES `TDS_LEDGER_RATE` (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=latin1;

ALTER TABLE `KETTLE_SCM_STAGE`.`VENDOR_COMPANY_DETAIL`
ADD COLUMN `PAN_STATUS_UPDATED_AT` DATETIME NULL DEFAULT NULL AFTER `MSME_EXPIRE_DATE`;

ALTER TABLE `KETTLE_SCM_STAGE`.`VENDOR_ACCOUNT_DETAILS`
ADD COLUMN `SECTION_206_UPDATED_AT` DATETIME NULL DEFAULT NULL AFTER `SECTION_206`;


CREATE TABLE KETTLE_SCM.SCHEDULER_STATUS_DATA (
  ID BIGINT AUTO_INCREMENT NOT NULL,
   SCHEDULER_KEY VARCHAR(255) NULL,
   STATUS VARCHAR(255) NULL,
   TIMESTAMP DATETIME NULL,
   CONSTRAINT PK_SCHEDULER_STATUS_DATA PRIMARY KEY (ID)
);

CREATE TABLE KETTLE_SCM.EMAIL_NOTIFICATION_STATUS(
  ID BIGINT AUTO_INCREMENT NOT NULL,
   SCHEDULED_TIME DATETIME NULL,
   SCHEDULER_STATUS VARCHAR(255) NULL,
   NOTIFICATION_KEY_TYPE VARCHAR(255) NULL,
   MESSAGE_ID VARCHAR(255) NULL,
   METADATA LONGTEXT NULL,
   CONSTRAINT PK_EMAIL_NOTIFICATION_STATUS PRIMARY KEY (ID)
);
ALTER TABLE EMAIL_NOTIFICATION_STATUS ADD CONSTRAINT UC_EMAIL_NOTIFICATION_STATUS_MESSAGE UNIQUE (MESSAGE_ID);

ALTER TABLE KETTLE_SCM_STAGE.DAY_CLOSE_EVENT ADD COLUMN IS_AUTO_DAY_CLOSE VARCHAR(1) DEFAULT "N";

INSERT INTO `KETTLE_MASTER_STAGE`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('K WH Auto Day Close', 'Access to see K WH Auto Day Close', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_STAGE`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('KWHADC', '7', 'ACTION', 'SHOW', 'SuMo -> WAREHOUSE/KITCEHN -> DAYCLOSE', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_STAGE`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_STAGE.USER_ROLE_DATA WHERE ROLE_NAME = 'K WH Auto Day Close'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'KWHADC'), 'ACTIVE', '120063', '2024-04-10 12:00:00');

CREATE TABLE KETTLE_SCM_STAGE.EXPIRY_DATE_CORRECTION_DATA
(
    EXPIRY_DATE_CORRECTION_ID   INT AUTO_INCREMENT NOT NULL,
    EXPIRY_DATE_CORRECTION_TYPE VARCHAR(255)       NOT NULL,
    UNIT_ID                     INT                NOT NULL,
    TRANSACTION_ID              INT                NOT NULL,
    KEY_ID                      INT                NOT NULL,
    KEY_TYPE                    VARCHAR(255)       NOT NULL,
    ORIGINAL_EXPIRY_DATE        TIMESTAMP           NULL,
    UPDATED_EXPIRY_DATE         TIMESTAMP           NULL,
    CREATED_BY                  INT                NULL,
    CREATED_AT                  TIMESTAMP           NULL,
    CONSTRAINT PK_EXPIRY_DATE_CORRECTION_DATA PRIMARY KEY (EXPIRY_DATE_CORRECTION_ID)
);

CREATE TABLE KETTLE_SCM_STAGE.VARIANCE_EXPIRY_DRILL_DOWN_DATA
(
    VARIANCE_EXPIRY_DRILL_DOWN_DATA_ID INT AUTO_INCREMENT NOT NULL,
    DAY_CLOSE_EVENT_ID                 INT                NULL,
    KEY_ID                             INT                NOT NULL,
    KEY_TYPE                           VARCHAR(255)       NOT NULL,
    EXPIRY_DATE                        TIMESTAMP           NULL,
    CREATED_BY                         INT                NULL,
    CREATED_AT                         TIMESTAMP           NULL,
    CONSTRAINT PK_VARIANCE_EXPIRY_DRILL_DOWN_DATA PRIMARY KEY (VARIANCE_EXPIRY_DRILL_DOWN_DATA_ID)
);

CREATE TABLE `KETTLE_SCM`.`OBSOLETE_COST_DETAIL_DATA_CAFE`
(
    `COST_DETAIL_DATA_ID` int NOT NULL AUTO_INCREMENT,
    `KEY_ID`              int NOT NULL,
    `KEY_TYPE`            varchar(25)    NOT NULL,
    `UNIT_ID`             int(11) NOT NULL,
    `PRICE`               decimal(16, 6) NOT NULL,
    `QUANTITY`            decimal(16, 6) NOT NULL,
    `UNIT_OF_MEASURE`     varchar(10)    NOT NULL,
    `IS_LATEST`           varchar(1)     NOT NULL,
    `LAST_UPDATE_TMSTMP`  timestamp NULL DEFAULT NULL,
    `EXPIRY_DATE`         datetime    DEFAULT NULL,
    `CREATION_REASON`     varchar(20) DEFAULT NULL,
    `CREATION_ITEM_ID`    int(20) DEFAULT NULL,
    `DELETED_TIME`        timestamp NULL DEFAULT NULL,
    `DAY_CLOSE_EVENT_ID`   int NULL,
    PRIMARY KEY (`COST_DETAIL_DATA_ID`)
);

CREATE TABLE `KETTLE_SCM`.`OBSOLETE_COST_DETAIL_DATA_WH`
(
    `COST_DETAIL_DATA_ID` int NOT NULL AUTO_INCREMENT,
    `KEY_ID`              int NOT NULL,
    `KEY_TYPE`            varchar(25)    NOT NULL,
    `UNIT_ID`             int(11) NOT NULL,
    `PRICE`               decimal(16, 6) NOT NULL,
    `QUANTITY`            decimal(16, 6) NOT NULL,
    `UNIT_OF_MEASURE`     varchar(10)    NOT NULL,
    `IS_LATEST`           varchar(1)     NOT NULL,
    `LAST_UPDATE_TMSTMP`  timestamp NULL DEFAULT NULL,
    `EXPIRY_DATE`         datetime    DEFAULT NULL,
    `CREATION_REASON`     varchar(20) DEFAULT NULL,
    `CREATION_ITEM_ID`    int(20) DEFAULT NULL,
    `DELETED_TIME`        timestamp NULL DEFAULT NULL,
    `DAY_CLOSE_EVENT_ID`   int NULL,
    PRIMARY KEY (`COST_DETAIL_DATA_ID`)
);


CREATE TABLE `BYPASS_VENDOR_CONTRACT` (
  `BYPASS_CONTRACT_ID` int(11) NOT NULL AUTO_INCREMENT,
  `VENDOR_ID` int(11) DEFAULT NULL,
  `STATUS` varchar(255) DEFAULT NULL,
  `CREATED_AT` datetime DEFAULT NULL,
  `CREATED_BY` int(11) DEFAULT NULL,
  `DOCUMENT_ID` int(11) DEFAULT NULL,
  `START_DATE` date DEFAULT NULL,
  `END_DATE` date DEFAULT NULL,
  `APPROVAL_REQUEST_FROM` varchar(255) DEFAULT NULL,
  `VENDOR_USER_NAME` varchar(100) DEFAULT NULL,
  `VENDOR_USER_DESIGNATION` varchar(100) DEFAULT NULL,
  `VENDOR_IP_ADDRESS` varchar(45) DEFAULT NULL,
  `AUTH_IP_ADDRESS` varchar(45) DEFAULT NULL,
  `UNSIGNED_DOCUMENT_ID` int(11) DEFAULT NULL,
  `VENDOR_SIGNED_DOCUMENT_ID` int(11) DEFAULT NULL,
  `AUTH_SIGNED_DOCUMENT_ID` int(11) DEFAULT NULL,
  `VENDOR_DIGITAL_SIGN_ID` int(11) DEFAULT NULL,
  `AUTH_DIGITAL_SIGN_ID` int(11) DEFAULT NULL,
  `OTP_VERIFIED` varchar(5) DEFAULT NULL,
  `IS_BYPASSED` varchar(5) DEFAULT NULL,
  `IS_MAIL_TRIGGERED` varchar(5) DEFAULT NULL,
  `MAIL_TIME` datetime DEFAULT NULL,
  `TEMPLATE_ID` int(11) DEFAULT NULL,
  PRIMARY KEY (`BYPASS_CONTRACT_ID`)
);

-- DUMP QUERY FROM VENDOR_CONTRACT_INFO ---> BYPASS_VENDOR_CONTRACT
INSERT INTO bypass_vendor_contract(BYPASS_CONTRACT_ID, VENDOR_ID, STATUS, CREATED_AT, START_DATE, END_DATE, APPROVAL_REQUEST_FROM, VENDOR_USER_NAME, VENDOR_USER_DESIGNATION,
          VENDOR_IP_ADDRESS, AUTH_IP_ADDRESS, UNSIGNED_DOCUMENT_ID, VENDOR_SIGNED_DOCUMENT_ID,
          AUTH_SIGNED_DOCUMENT_ID, VENDOR_DIGITAL_SIGN_ID, AUTH_DIGITAL_SIGN_ID, OTP_VERIFIED,
          IS_MAIL_TRIGGERED, MAIL_TIME, TEMPLATE_ID, CREATED_BY)
    SELECT VENDOR_CONTRACT_ID, VENDOR_ID, RECORD_STATUS, CREATION_TIME, START_DATE, END_DATE,
            CONTRACT_REQUESTED_BY, VENDOR_USER_NAME, VENDOR_USER_DESIGNATION, VENDOR_IP_ADDRESS, AUTH_IP_ADDRESS,
            UNSIGNED_DOCUMENT_ID, VENDOR_SIGNED_DOCUMENT_ID, AUTH_SIGNED_DOCUMENT_ID, VENDOR_DIGITAL_SIGN_ID,
            AUTH_DIGITAL_SIGN_ID, OTP_VERIFIED, IS_MAIL_TRIGGERED, MAIL_TIME, TEMPLATE_ID, SUBSTRING(CREATED_BY, -7, 6)
    FROM vendor_contract_info
    WHERE RECORD_STATUS = 'APPLIED';


CREATE TABLE `BYPASS_VENDOR_CONTRACT_ITEM` (
  `BYPASS_CONTRACT_ITEM_ID` int(11) NOT NULL AUTO_INCREMENT,
  `BYPASS_CONTRACT_ID` int(11) NOT NULL,
  `VENDOR_ID` int(11) DEFAULT NULL,
  `SKU_ID` int(11) DEFAULT NULL,
  `SKU_PACKAGING_ID` int(11) DEFAULT NULL,
  `DISPATCH_LOCATION` varchar(255) DEFAULT NULL,
  `DISPATCH_LOCATION_ID` int(11) DEFAULT NULL,
  `DELIVERY_LOCATION` varchar(255) DEFAULT NULL,
  `DELIVERY_LOCATION_ID` int(11) DEFAULT NULL,
  `CURRENT_PRICE` decimal(16,6) DEFAULT NULL,
  `UPDATED_PRICE` decimal(16,6) DEFAULT NULL,
  `STATUS` varchar(255) DEFAULT NULL,
  `PREVIOUS_REJECTION_REASON` varchar(255) DEFAULT NULL,
  `CURRENT_REJECTION_REASON` varchar(255) DEFAULT NULL,
  `SKU_PRICE_DATA_ID` int(11) DEFAULT NULL,
  `SELECTED_REJECTION_REASON` varchar(255) DEFAULT NULL,
  `START_DATE` date DEFAULT NULL,
  `END_DATE` date DEFAULT NULL,
  `TAX_CODE` varchar(255) DEFAULT NULL,
  `TAX_PERCENTAGE` decimal(10,0) DEFAULT NULL,
  `IS_NEW` varchar(5) DEFAULT NULL,
  `IS_NEW_ITEM` varchar(5) DEFAULT NULL,
  PRIMARY KEY (`BYPASS_CONTRACT_ITEM_ID`)
);

-- DUMP QUERY FROM VENDOR_CONTRACT_ITEM ---> BYPASS_VENDOR_CONTRACT_ITEM
INSERT INTO bypass_vendor_contract_item(
        BYPASS_CONTRACT_ITEM_ID, BYPASS_CONTRACT_ID, VENDOR_ID, SKU_ID, SKU_PACKAGING_ID, DISPATCH_LOCATION,
        DELIVERY_LOCATION, CURRENT_PRICE, UPDATED_PRICE,
        SKU_PRICE_DATA_ID, TAX_CODE, TAX_PERCENTAGE, IS_NEW_ITEM, IS_NEW, STATUS)
    SELECT CONTRACT_ITEM_ID, VENDOR_CONTRACT_ID, VENDOR_ID, SKU_ID, SKU_PACKAGING_ID,
            DISPATCH_LOCATION, DELIVERY_LOCATION, CURRENT_PRICE, NEGOTIATED_PRICE, SKU_PRICE_DATE_ID,
            TAX_CODE, TAX_PERCENTAGE, IS_NEW_ITEM, IS_NEW, 'APPROVED'
    FROM vendor_contract_item
    WHERE VENDOR_CONTRACT_ID IN ( SELECT BYPASS_CONTRACT_ID FROM bypass_vendor_contract WHERE STATUS = 'APPLIED' );


CREATE TABLE `BYPASS_VENDOR_CONTRACT_ITEM_UNIT` (
  `BYPASS_CONTRACT_ITEM_UNIT_ID` int(11) NOT NULL AUTO_INCREMENT,
  `BYPASS_CONTRACT_ITEM_ID` int(11) NOT NULL,
  `UNIT_ID` int(11) NOT NULL,
  `STATUS` varchar(45) NOT NULL,
  PRIMARY KEY (`BYPASS_CONTRACT_ITEM_UNIT_ID`)
);

CREATE TABLE `BYPASS_VENDOR_CONTRACT_STATUS_LOG` (
  `BYPASS_VENDOR_CONTRACT_STATUS_LOG_ID` int(11) NOT NULL AUTO_INCREMENT,
  `FROM_STATUS` varchar(45) DEFAULT NULL,
  `TO_STATUS` varchar(45) DEFAULT NULL,
  `UPDATED_BY` int(11) NOT NULL,
  `UPDATED_TIME` datetime NOT NULL,
  `BYPASS_CONTRACT_ID` int(11) NOT NULL,
  PRIMARY KEY (`BYPASS_VENDOR_CONTRACT_STATUS_LOG_ID`)
);

-- ALTER TABLE KETTLE_SCM_STAGE.SKU_PRICE_DATA CHANGE COLUMN SKU_PRICE SKU_PRICE DECIMAL(19,6) NOT NULL ;
-- ALTER TABLE KETTLE_SCM_STAGE.SKU_PRICE_HISTORY CHANGE COLUMN CURRENT_PRICE CURRENT_PRICE DECIMAL(19,6) NULL DEFAULT NULL ;


ALTER TABLE `KETTLE_SCM_STAGE`.`VENDOR_DETAIL_DATA` ADD COLUMN `CAN_CREATE_CONTRACT` VARCHAR(1) NULL DEFAULT 'Y' AFTER `IS_ECOM_PARTY`;

ALTER TABLE KETTLE_SCM_STAGE.`TRANSFER_ORDER` ADD COLUMN ROW_VERSION INT DEFAULT NULL;
ALTER TABLE KETTLE_SCM_STAGE.`REVERSE_PRODUCTION_BOOKING` ADD COLUMN ROW_VERSION INT DEFAULT NULL;
ALTER TABLE KETTLE_SCM_STAGE.`PRODUCTION_BOOKING` ADD COLUMN ROW_VERSION INT DEFAULT NULL;
ALTER TABLE KETTLE_SCM_STAGE.`GATEPASS_ITEM_DATA` ADD COLUMN ROW_VERSION INT DEFAULT NULL;
ALTER TABLE KETTLE_SCM_STAGE.`SALES_PERFORMA_INVOICE` ADD COLUMN ROW_VERSION INT DEFAULT NULL;
ALTER TABLE KETTLE_SCM_STAGE.`VENDOR_GOODS_RECEIVED_DATA` ADD COLUMN ROW_VERSION INT DEFAULT NULL;
ALTER TABLE KETTLE_SCM_STAGE.`GOODS_RECEIVED` ADD COLUMN ROW_VERSION INT DEFAULT NULL;
ALTER TABLE KETTLE_SCM_STAGE.`WASTAGE_EVENT` ADD COLUMN ROW_VERSION INT DEFAULT NULL;

ALTER TABLE KETTLE_SCM.VENDOR_REGISTRATION_REQUEST_DETAIL ADD COLUMN PAN_CARD VARCHAR(10) DEFAULT NULL;

INSERT INTO `KETTLE_MASTER_STAGE`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('SUMO New Product Creation', 'SUMO new Product Creation By User', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_STAGE`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('SURNPR', '7', 'SUBMENU', 'SHOW', 'SUMO -> Sumo User Request ->  New Product Request', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_STAGE`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_STAGE.USER_ROLE_DATA WHERE ROLE_NAME = 'SUMO New Product Creation'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_STAGE.ACTION_DETAIL WHERE ACTION_CODE = 'SURNPR'),
       'ACTIVE', '140199', '2024-11-30 12:00:00');


-- INSERT INTO `KETTLE_MASTER_STAGE`.`USER_ROLE_DATA`
-- (`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
-- VALUES ('SUMO User Requests', 'SUMO User Requests', 'ACTIVE', '7');
--
-- INSERT INTO `KETTLE_MASTER_STAGE`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
-- VALUES ('SUR', '7', 'MENU', 'SHOW', 'SUMO -> Sumo User Request', 'ACTIVE');
--
-- INSERT INTO `KETTLE_MASTER_STAGE`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
-- VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_STAGE.USER_ROLE_DATA WHERE ROLE_NAME = 'SUMO User Requests'),
--        (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_STAGE.ACTION_DETAIL WHERE ACTION_CODE = 'SUR'),
--        'ACTIVE', '140199', '2024-11-30 12:00:00');


INSERT INTO `KETTLE_MASTER_STAGE`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('SUMO New Vendor Registration Request', 'SUMO New Vendor Registration Request', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_STAGE`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('SURNVRR', '7', 'SUBMENU', 'SHOW', 'SUMO -> Sumo User Request -> Vendor Registration Request', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_STAGE`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_STAGE.USER_ROLE_DATA WHERE ROLE_NAME = 'SUMO New Vendor Registration Request'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_STAGE.ACTION_DETAIL WHERE ACTION_CODE = 'SURNVRR'),
       'ACTIVE', '140199', '2024-11-30 12:00:00');

INSERT INTO `KETTLE_MASTER_STAGE`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('SUMO New Product Request Finance Approval', 'SUMO New Product Request Finance Approval', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_STAGE`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('SURNPRFA', '7', 'ACTION', 'SHOW', 'SUMO -> Sumo User Request -> Product Dashboard', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_STAGE`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_STAGE.USER_ROLE_DATA WHERE ROLE_NAME = 'SUMO New Product Request Finance Approval'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_STAGE.ACTION_DETAIL WHERE ACTION_CODE = 'SURNPRFA'),
       'ACTIVE', '140199', '2024-11-30 12:00:00');

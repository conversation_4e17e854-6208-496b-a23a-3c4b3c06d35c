CREATE TABLE KETTLE_SCM_DEV.REGION_FULFILLMENT_MAPPING (
                                                           REGION_FULFILLMENT_MAPPING_ID int(11) NOT NULL AUTO_INCREMENT,
                                                           TYP<PERSON> varchar(45) DEFAULT NULL,
                                                           <PERSON>IT_ID int(11) DEFAULT NULL,
                                                           R<PERSON><PERSON> varchar(200) DEFAULT NULL,
                                                           MAPPING_STATUS varchar(45) DEFAULT NULL,
                                                           PRIMARY KEY (REGION_FULFILLMENT_MAPPING_ID)
);

CREATE INDEX REGION_IDX ON KETTLE_SCM_DEV.REGION_FULFILLMENT_MAPPING(REGION) USING BTREE;



INSERT INTO KETTLE_SCM_DEV.REGION_FULFILLMENT_MAPPING (`TYPE`,`UNIT_ID`,`REGION`,`MAPPING_STATUS`) VALUES ('WAREHOUSE',26185,'NCR','ACTIVE');
INSERT INTO KETTLE_SCM_DEV.REGION_FULFILLMENT_MAPPING (`TYPE`,`UNIT_ID`,`REGION`,`MAPPING_STATUS`) VALUES ('<PERSON><PERSON>CHEN',26130,'NCR','ACTIVE');
INSERT INTO KETTLE_SCM_DEV.REGION_FULFILLMENT_MAPPING (`TYPE`,`UNIT_ID`,`REGION`,`MAPPING_STATUS`) VALUES ('WAREHOUSE',26185,'NCR_EDU','ACTIVE');
INSERT INTO KETTLE_SCM_DEV.REGION_FULFILLMENT_MAPPING (`TYPE`,`UNIT_ID`,`REGION`,`MAPPING_STATUS`) VALUES ('KITCHEN',26130,'NCR_EDU','ACTIVE');
INSERT INTO KETTLE_SCM_DEV.REGION_FULFILLMENT_MAPPING (`TYPE`,`UNIT_ID`,`REGION`,`MAPPING_STATUS`) VALUES ('WAREHOUSE',26327,'MUMBAI','ACTIVE');
INSERT INTO KETTLE_SCM_DEV.REGION_FULFILLMENT_MAPPING (`TYPE`,`UNIT_ID`,`REGION`,`MAPPING_STATUS`) VALUES ('KITCHEN',24002,'MUMBAI','ACTIVE');
INSERT INTO KETTLE_SCM_DEV.REGION_FULFILLMENT_MAPPING (`TYPE`,`UNIT_ID`,`REGION`,`MAPPING_STATUS`) VALUES ('WAREHOUSE',26185,'CHANDIGARH','ACTIVE');
INSERT INTO KETTLE_SCM_DEV.REGION_FULFILLMENT_MAPPING (`TYPE`,`UNIT_ID`,`REGION`,`MAPPING_STATUS`) VALUES ('KITCHEN',26130,'CHANDIGARH','ACTIVE');
INSERT INTO KETTLE_SCM_DEV.REGION_FULFILLMENT_MAPPING (`TYPE`,`UNIT_ID`,`REGION`,`MAPPING_STATUS`) VALUES ('WAREHOUSE',26282,'BANGALORE','ACTIVE');
INSERT INTO KETTLE_SCM_DEV.REGION_FULFILLMENT_MAPPING (`TYPE`,`UNIT_ID`,`REGION`,`MAPPING_STATUS`) VALUES ('KITCHEN',26100,'BANGALORE','ACTIVE');
INSERT INTO KETTLE_SCM_DEV.REGION_FULFILLMENT_MAPPING (`TYPE`,`UNIT_ID`,`REGION`,`MAPPING_STATUS`) VALUES ('WAREHOUSE',26327,'PUNE','ACTIVE');
INSERT INTO KETTLE_SCM_DEV.REGION_FULFILLMENT_MAPPING (`TYPE`,`UNIT_ID`,`REGION`,`MAPPING_STATUS`) VALUES ('KITCHEN',24002,'PUNE','ACTIVE');
INSERT INTO KETTLE_SCM_DEV.REGION_FULFILLMENT_MAPPING (`TYPE`,`UNIT_ID`,`REGION`,`MAPPING_STATUS`) VALUES ('WAREHOUSE',26282,'CHENNAI','ACTIVE');
INSERT INTO KETTLE_SCM_DEV.REGION_FULFILLMENT_MAPPING (`TYPE`,`UNIT_ID`,`REGION`,`MAPPING_STATUS`) VALUES ('KITCHEN',26100,'CHENNAI','ACTIVE');
INSERT INTO KETTLE_SCM_DEV.REGION_FULFILLMENT_MAPPING (`TYPE`,`UNIT_ID`,`REGION`,`MAPPING_STATUS`) VALUES ('WAREHOUSE',26496,'HYDERABAD','ACTIVE');
INSERT INTO KETTLE_SCM_DEV.REGION_FULFILLMENT_MAPPING (`TYPE`,`UNIT_ID`,`REGION`,`MAPPING_STATUS`) VALUES ('KITCHEN',26495,'HYDERABAD','ACTIVE');


ALTER TABLE KETTLE_SCM_DEV.VENDOR_DISPATCH_LOCATIONS
    ADD COLUMN LOCATION_TYPE VARCHAR(100) NULL DEFAULT "DELIVERY_BILLING" ;

ALTER TABLE KETTLE_SCM_DEV.SALES_PERFORMA_INVOICE
    ADD COLUMN BILLING_LOCATION_ID INT(11) NULL;

ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION
    ADD COLUMN IS_IN_TRANSIT VARCHAR(1) NULL;


CREATE TABLE KETTLE_SCM_DEV.EMPLOYEE_BUSINESS_COST_CENTER_MAPPING (
  MAPPING_ID INT AUTO_INCREMENT NOT NULL,
   EMPLOYEE_ID INT NULL,
   BUSINESS_COST_CENTER_ID INT NULL,
   MAPPING_STATUS VARCHAR(255) NULL,
   CONSTRAINT PK_EMPLOYEE_BUSINESS_COST_CENTER_MAPPING PRIMARY KEY (MAPPING_ID)
);
ALTER TABLE KETTLE_SCM_DEV.EMPLOYEE_BUSINESS_COST_CENTER_MAPPING ADD UNIQUE COST_CENTER_ID_EMPLOYEE_ID_EMPLOYEE_COST_CENTER_MAPPING(EMPLOYEE_ID, BUSINESS_COST_CENTER_ID);

ALTER TABLE KETTLE_SCM_DEV.SERVICE_RECEIVED_DATA ADD COLUMN SERVICE_PROOF_DOCUMENT_IDS VARCHAR(200) NOT NULL;




ALTER TABLE KETTLE_SCM_DEV.DAY_CLOSE_EVENT ADD COLUMN SUB_CATEGORIES VARCHAR(200) NULL ;

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('auddc', '7', 'ACTION', 'VIEW', 'SuMo -> Audit Day Close', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'AUDITOR'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'auddc'),
       'ACTIVE', '120063', '2022-07-28 12:00:00');

ALTER TABLE KETTLE_SCM_DEV.DAY_CLOSE_EVENT
    CHANGE COLUMN CATEGORIES SUB_CATEGORIES VARCHAR(200) NULL DEFAULT NULL ;


ALTER TABLE KETTLE_SCM_DEV.STOCK_INVENTORY ADD COLUMN CATEGORY_ID INT(11) NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.STOCK_INVENTORY ADD COLUMN SUB_CATEGORY_ID INT(11) NOT NULL;

ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM ADD COLUMN CATEGORY_ID INT(11) NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM ADD COLUMN SUB_CATEGORY_ID INT(11) NOT NULL;

ALTER TABLE KETTLE_SCM_DEV.ASSET_DEPRECIATION_MAPPING ADD COLUMN SUB_CATEGORY_ID INT(11) NOT NULL;

UPDATE KETTLE_SCM_DEV.STOCK_INVENTORY SI SET SI.CATEGORY_ID = (SELECT CATEGORY_ID FROM KETTLE_SCM_DEV.PRODUCT_DEFINITION PD WHERE SI.PRODUCT_ID = PD.PRODUCT_ID),
 SI.SUB_CATEGORY_ID = (SELECT SUB_CATEGORY_ID FROM KETTLE_SCM_DEV.PRODUCT_DEFINITION PD WHERE SI.PRODUCT_ID = PD.PRODUCT_ID);

UPDATE KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM GR SET GR.CATEGORY_ID = (SELECT CATEGORY_ID FROM KETTLE_SCM_DEV.PRODUCT_DEFINITION PD WHERE PD.PRODUCT_ID =
(SELECT LINKED_PRODUCT_ID FROM KETTLE_SCM_DEV.SKU_DEFINITION SD WHERE SD.SKU_ID = GR.SKU_ID)),
 GR.SUB_CATEGORY_ID = (SELECT SUB_CATEGORY_ID FROM KETTLE_SCM_DEV.PRODUCT_DEFINITION PD WHERE PD.PRODUCT_ID =
(SELECT LINKED_PRODUCT_ID FROM KETTLE_SCM_DEV.SKU_DEFINITION SD WHERE SD.SKU_ID = GR.SKU_ID));

UPDATE KETTLE_SCM_DEV.ASSET_DEPRECIATION_MAPPING ADM SET ADM.SUB_CATEGORY_ID = (SELECT SUB_CATEGORY_ID FROM KETTLE_SCM_DEV.PRODUCT_DEFINITION PD WHERE PD.PRODUCT_ID =
(SELECT PRODUCT_ID FROM KETTLE_SCM_DEV.ASSET_DEFINITION AD WHERE AD.ASSET_ID = ADM.ASSET_ID));

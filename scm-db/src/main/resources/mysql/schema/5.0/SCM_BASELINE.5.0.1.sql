ALTER TABLE KETTLE_SCM_DEV.VEHICLE_DATA
ADD COLUMN IS_MULTI_DISPATCH VARCHAR(1) DEFAULT 'N';

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('CREWDIS', '7', 'SUBMENU', 'VIEW', 'SuMo-> Dispatchmanager -> Create dispatch -> SHOW', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_DETAIL_ID`, `ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES (NULL, 'SREWDIS', '7', '<PERSON><PERSON>BMENU', 'VIEW', 'SuMo-> Dispatchmanager -> Search dispatch -> SHOW', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_DETAIL_ID`, `ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES (NULL, 'UPEWF', '7', 'ACTION', 'VIEW', 'SuMo-> Dispatchmanager -> Search dispatch -> Upload EWAY file', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID,ACTION_DETAIL_ID, MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)
VALUES
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'SCM_HEAD'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'CREWDIS'),'ACTIVE',120103,'2018-04-13 00:00:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'DISPATCH MANAGER'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'CREWDIS'),'ACTIVE',120103,'2018-04-13 00:00:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'SCM_HEAD'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'SREWDIS'),'ACTIVE',120103,'2018-04-13 00:00:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'DISPATCH MANAGER'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'SREWDIS'),'ACTIVE',120103,'2018-04-13 00:00:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'FINANCE_HEAD'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'SREWDIS'),'ACTIVE',120103,'2018-04-13 00:00:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'FINANCE_MANAGER'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'SREWDIS'),'ACTIVE',120103,'2018-04-13 00:00:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'FINANCE_EXECUTIVE'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'SREWDIS'),'ACTIVE',120103,'2018-04-13 00:00:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'SCM_HEAD'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'UPEWF'),'ACTIVE',120103,'2018-04-13 00:00:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'FINANCE_HEAD'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'UPEWF'),'ACTIVE',120103,'2018-04-13 00:00:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'FINANCE_MANAGER'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'UPEWF'),'ACTIVE',120103,'2018-04-13 00:00:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'FINANCE_EXECUTIVE'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'UPEWF'),'ACTIVE',120103,'2018-04-13 00:00:00');



ALTER TABLE KETTLE_SCM_DEV.COST_DETAIL_DATA MODIFY COLUMN EXPIRY_DATE DATETIME;
ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM_DRILLDOWN MODIFY COLUMN EXPIRY_DATE DATETIME;

ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM_DRILLDOWN MODIFY ADD_TIME DATETIME NULL;
ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM_DRILLDOWN ADD COLUMN REJECTION DECIMAL(16,6);
ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM_DRILLDOWN MODIFY EXPIRY_DATE DATETIME;


ALTER TABLE KETTLE_SCM_DEV.PRODUCTION_BOOKING MODIFY EXPIRY_DATE DATETIME;
ALTER TABLE KETTLE_SCM_DEV.BOOKING_CONSUMPTION_ITEM_DRILLDOWN  MODIFY COLUMN EXPIRY_DATE DATETIME NULL;

ALTER TABLE KETTLE_SCM_DEV.WASTAGE_ITEM_DRILLDOWN  MODIFY COLUMN EXPIRY_DATE DATETIME NULL;
ALTER TABLE KETTLE_SCM_DEV.COST_DETAIL_AUDIT_DATA  MODIFY COLUMN EXPIRY_DATE DATETIME NULL;
ALTER TABLE KETTLE_SCM_DEV.COST_DETAIL_AUDIT_DRILLDOWN_DATA  MODIFY COLUMN EXPIRY_DATE DATETIME NULL;

CREATE INDEX STOCK_TYPE_STOCK_INVENTORY ON KETTLE_SCM_DEV.STOCK_INVENTORY(STOCK_TYPE) USING BTREE;
CREATE INDEX EVENT_TYPE_STOCK_INVENTORY ON KETTLE_SCM_DEV.STOCK_INVENTORY(EVENT_TYPE) USING BTREE;
CREATE INDEX STATUS_STOCK_INVENTORY ON KETTLE_SCM_DEV.STOCK_INVENTORY(STATUS) USING BTREE;
CREATE INDEX CURRENT_EVENT_ID_STOCK_INVENTORY ON KETTLE_SCM_DEV.STOCK_INVENTORY(CURRENT_EVENT_ID) USING BTREE;

CREATE INDEX UOM_DAY_CLOSE_PRODUCT_VALUES ON KETTLE_SCM_DEV.DAY_CLOSE_PRODUCT_VALUES(UOM) USING BTREE;

CREATE INDEX UOM_PRODUCT_CONSUMPTION_DRILLDOWN ON KETTLE_SCM_DEV.PRODUCT_CONSUMPTION_DRILLDOWN(UOM) USING BTREE;
CREATE INDEX CONSUMPTION_TYPE_PRODUCT_CONSUMPTION_DRILLDOWN ON KETTLE_SCM_DEV.PRODUCT_CONSUMPTION_DRILLDOWN(CONSUMPTION_TYPE) USING BTREE;
CREATE INDEX EVENT_ID_PRODUCT_CONSUMPTION_DRILLDOWN ON KETTLE_SCM_DEV.PRODUCT_CONSUMPTION_DRILLDOWN(EVENT_ID) USING BTREE;
CREATE INDEX UNIT_ID_PRODUCT_CONSUMPTION_DRILLDOWN ON KETTLE_SCM_DEV.PRODUCT_CONSUMPTION_DRILLDOWN(UNIT_ID) USING BTREE;
CREATE INDEX PRODUCT_ID_PRODUCT_CONSUMPTION_DRILLDOWN ON KETTLE_SCM_DEV.PRODUCT_CONSUMPTION_DRILLDOWN(PRODUCT_ID) USING BTREE;


CREATE INDEX UNIT_ID_WASTAGE_EVENT ON KETTLE_SCM_DEV.WASTAGE_EVENT(UNIT_ID) USING BTREE;
CREATE INDEX BUSINESS_WASTAGE_EVENT ON KETTLE_SCM_DEV.WASTAGE_EVENT(BUSINESS_DATE) USING BTREE;
CREATE INDEX STATUS_WASTAGE_EVENT ON KETTLE_SCM_DEV.WASTAGE_EVENT(STATUS) USING BTREE;

CREATE INDEX WASTAGE_ID_WASTAGE_ITEM_DATA ON KETTLE_SCM_DEV.WASTAGE_ITEM_DATA(WASTAGE_ID) USING BTREE;

CREATE INDEX UNIT_ID_PRODUCTION_PLAN_EVENT ON KETTLE_SCM_DEV.PRODUCTION_PLAN_EVENT(UNIT_ID) USING BTREE;
CREATE INDEX GENERATION_TIME_PRODUCTION_PLAN_EVENT ON KETTLE_SCM_DEV.PRODUCTION_PLAN_EVENT(GENERATION_TIME) USING BTREE;
CREATE INDEX PLAN_STATUS_PRODUCTION_PLAN_EVENT ON KETTLE_SCM_DEV.PRODUCTION_PLAN_EVENT(PLAN_STATUS) USING BTREE;
CREATE INDEX FULFILLMENT_DATE_PRODUCTION_PLAN_EVENT ON KETTLE_SCM_DEV.PRODUCTION_PLAN_EVENT(FULFILLMENT_DATE) USING BTREE;

CREATE INDEX PLAN_EVENT_ID_PLAN_ORDER_MAPPING ON KETTLE_SCM_DEV.PLAN_ORDER_MAPPING(PLAN_EVENT_ID) USING BTREE;
CREATE INDEX REQUEST_ORDER_ID_PLAN_ORDER_MAPPING ON KETTLE_SCM_DEV.PLAN_ORDER_MAPPING(REQUEST_ORDER_ID) USING BTREE;

CREATE INDEX PLAN_EVENT_ID_PLAN_ORDER_ITEM ON KETTLE_SCM_DEV.PLAN_ORDER_ITEM(PLAN_EVENT_ID) USING BTREE;

ALTER TABLE KETTLE_SCM_DEV.PRODUCTION_BOOKING MODIFY COLUMN UNIT_PRICE DECIMAL(20,6);
ALTER TABLE KETTLE_SCM_DEV.PRODUCTION_BOOKING MODIFY COLUMN TOTAL_COST DECIMAL(20,6);

DELETE FROM COST_DETAIL_DATA WHERE UNIT_ID IN (26031,
26032);

DELETE FROM COST_DETAIL_DATA WHERE UNIT_ID IN (26040,
26041);


UPDATE COST_DETAIL_DATA cd INNER JOIN PRODUCT_DEFINITION pd ON cd.KEY_ID = pd.PRODUCT_ID AND cd.KEY_TYPE = 'PRODUCT'
INNER JOIN UNIT_DETAIL ud on cd.UNIT_ID = ud.UNIT_ID 
SET cd.EXPIRY_DATE = DATE_ADD(DATE(DATE_ADD(cd.LAST_UPDATE_TMSTMP, INTERVAL pd.SHELF_LIFE_IN_DAYS DAY)), INTERVAL 1 HOUR)
WHERE ud.CATEGORY_ID NOT IN (3,4)  AND  pd.SHELF_LIFE_IN_DAYS > -1;


UPDATE COST_DETAIL_DATA cd INNER JOIN PRODUCT_DEFINITION pd ON cd.KEY_ID = pd.PRODUCT_ID AND cd.KEY_TYPE = 'PRODUCT'
INNER JOIN UNIT_DETAIL ud on cd.UNIT_ID = ud.UNIT_ID 
SET cd.EXPIRY_DATE = '9999-12-31 01:00:00'
WHERE ud.CATEGORY_ID NOT IN (3,4)  AND  pd.SHELF_LIFE_IN_DAYS <= -1;


UPDATE COST_DETAIL_DATA cd 
INNER JOIN SKU_DEFINITION sd ON cd.KEY_ID = sd.SKU_ID  INNER JOIN PRODUCT_DEFINITION pd ON sd.LINKED_PRODUCT_ID = pd.PRODUCT_ID
INNER JOIN UNIT_DETAIL ud on cd.UNIT_ID = ud.UNIT_ID 
SET cd.EXPIRY_DATE = DATE_ADD(DATE(DATE_ADD(cd.LAST_UPDATE_TMSTMP, INTERVAL pd.SHELF_LIFE_IN_DAYS DAY)), INTERVAL 1 HOUR)
WHERE ud.CATEGORY_ID IN (3,4)  AND  pd.SHELF_LIFE_IN_DAYS > -1;


UPDATE COST_DETAIL_DATA cd 
INNER JOIN SKU_DEFINITION sd ON cd.KEY_ID = sd.SKU_ID  INNER JOIN PRODUCT_DEFINITION pd ON sd.LINKED_PRODUCT_ID = pd.PRODUCT_ID
INNER JOIN UNIT_DETAIL ud on cd.UNIT_ID = ud.UNIT_ID 
SET cd.EXPIRY_DATE = '9999-12-31 01:00:00'
WHERE ud.CATEGORY_ID IN (3,4)  AND  pd.SHELF_LIFE_IN_DAYS <= -1;


UPDATE COST_DETAIL_DATA cd INNER JOIN PRODUCT_DEFINITION pd ON cd.KEY_ID = pd.PRODUCT_ID AND cd.KEY_TYPE = 'PRODUCT'
INNER JOIN UNIT_DETAIL ud on cd.UNIT_ID = ud.UNIT_ID 
SET cd.EXPIRY_DATE = '9999-12-31 01:00:00'
WHERE ud.UNIT_ID =  26001 AND  pd.SHELF_LIFE_IN_DAYS <= -1;


UPDATE COST_DETAIL_DATA cd INNER JOIN PRODUCT_DEFINITION pd ON cd.KEY_ID = pd.PRODUCT_ID AND cd.KEY_TYPE = 'PRODUCT'
INNER JOIN UNIT_DETAIL ud on cd.UNIT_ID = ud.UNIT_ID 
SET cd.EXPIRY_DATE = DATE_ADD(DATE(DATE_ADD(cd.LAST_UPDATE_TMSTMP, INTERVAL pd.SHELF_LIFE_IN_DAYS DAY)), INTERVAL 1 HOUR)
WHERE ud.UNIT_ID =  26001 AND  pd.SHELF_LIFE_IN_DAYS >= -1;


UPDATE GOODS_RECEIVED SET GOODS_RECEIVED_STATUS = 'SETTLED',
RECEIVED_BY = 120056  WHERE 
GOODS_RECEIVED_STATUS NOT IN ('SETTLED', 'CANCELLED') 
AND 
GENERATED_FOR_UNIT_ID IN (26001,26003,
26004,10008,26002,12022,10004,26046,26042,26020,12001,12004);


INSERT INTO GOODS_RECEIVED_ITEM_DRILLDOWN (GOODS_RECEIVED_ITEM_ID, PRICE, QUANTITY, EXPIRY_DATE, ADD_TIME)
SELECT 
    gri.GOODS_RECEIVED_ITEM_ID,
    gri.UNIT_PRICE,
    gri.TRANSFERRED_QUANTITY,
    DATE_ADD(DATE(DATE_ADD(gr.GENERATION_TIME,
                INTERVAL pd.SHELF_LIFE_IN_DAYS DAY)),
        INTERVAL 1 HOUR),
	gr.GENERATION_TIME
FROM
    GOODS_RECEIVED gr
        INNER JOIN
    GOODS_RECEIVED_ITEM gri ON gr.GOODS_RECEIVED_ID = gri.GOODS_RECEIVED_ID
    INNER JOIN 
    SKU_DEFINITION sd ON gri.SKU_ID = sd.SKU_ID
    INNER JOIN 
    PRODUCT_DEFINITION pd ON pd.PRODUCT_ID = sd.LINKED_PRODUCT_ID
WHERE  pd.SHELF_LIFE_IN_DAYS >= -1;


INSERT INTO GOODS_RECEIVED_ITEM_DRILLDOWN (GOODS_RECEIVED_ITEM_ID, PRICE, QUANTITY, EXPIRY_DATE, ADD_TIME)
SELECT 
    gri.GOODS_RECEIVED_ITEM_ID,
    gri.UNIT_PRICE,
    gri.TRANSFERRED_QUANTITY,
    '9999-12-31 01:00:00',
	gr.GENERATION_TIME
FROM
    GOODS_RECEIVED gr
        INNER JOIN
    GOODS_RECEIVED_ITEM gri ON gr.GOODS_RECEIVED_ID = gri.GOODS_RECEIVED_ID
    INNER JOIN 
    SKU_DEFINITION sd ON gri.SKU_ID = sd.SKU_ID
    INNER JOIN 
    PRODUCT_DEFINITION pd ON pd.PRODUCT_ID = sd.LINKED_PRODUCT_ID
WHERE  pd.SHELF_LIFE_IN_DAYS <= -1;



INSERT INTO TRANSFER_ORDER_ITEM_DRILLDOWN (TRANSFER_ORDER_ITEM_ID, PRICE, QUANTITY, EXPIRY_DATE, ADD_TIME)
SELECT 
    toi.TRANSFER_ORDER_ITEM_ID,
    toi.UNIT_PRICE,
    toi.TRANSFERRED_QUANTITY,
    DATE_ADD(DATE(DATE_ADD(t.GENERATION_TIME,
                INTERVAL pd.SHELF_LIFE_IN_DAYS DAY)),
        INTERVAL 1 HOUR),
	t.GENERATION_TIME
FROM
    TRANSFER_ORDER t
        INNER JOIN
    TRANSFER_ORDER_ITEM toi ON t.TRANSFER_ORDER_ID = toi.TRANSFER_ORDER_ID
    INNER JOIN 
    SKU_DEFINITION sd ON toi.SKU_ID = sd.SKU_ID
    INNER JOIN 
    PRODUCT_DEFINITION pd ON pd.PRODUCT_ID = sd.LINKED_PRODUCT_ID
WHERE  pd.SHELF_LIFE_IN_DAYS >= -1;


INSERT INTO TRANSFER_ORDER_ITEM_DRILLDOWN (TRANSFER_ORDER_ITEM_ID, PRICE, QUANTITY, EXPIRY_DATE, ADD_TIME)
SELECT 
    toi.TRANSFER_ORDER_ITEM_ID,
    toi.UNIT_PRICE,
    toi.TRANSFERRED_QUANTITY,
    '9999-12-31 01:00:00',
	t.GENERATION_TIME
FROM
    TRANSFER_ORDER t
        INNER JOIN
    TRANSFER_ORDER_ITEM toi ON t.TRANSFER_ORDER_ID = toi.TRANSFER_ORDER_ID
    INNER JOIN 
    SKU_DEFINITION sd ON toi.SKU_ID = sd.SKU_ID
    INNER JOIN 
    PRODUCT_DEFINITION pd ON pd.PRODUCT_ID = sd.LINKED_PRODUCT_ID
WHERE  pd.SHELF_LIFE_IN_DAYS <= -1;

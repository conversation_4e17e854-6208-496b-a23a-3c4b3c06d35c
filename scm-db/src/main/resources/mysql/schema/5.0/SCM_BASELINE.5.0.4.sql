CREATE TABLE KETTLE_SCM_DEV.PURCHASE_PROFILE(
PROFILE_ID INT NOT NULL AUTO_INCREMENT,
ROLE_ID INT NOT NULL,
ROLE_NAME VARCHAR(50) NOT NULL,
SUB_CATEGORY INT NOT NULL,
PRIMARY KEY (PROFILE_ID)
);

CREATE INDEX PURCHASE_PROFILE_ROLE_INDEX ON KETTLE_SCM_DEV.PURCHASE_PROFILE(ROLE_ID) USING BTREE;


UPDATE KETTLE_MASTER_DEV.USER_ROLE_DATA SET ROLE_NAME='SCM_PURCHASER', ROLE_DESCRIPTION='SCM_PURCHASER' WHERE ROLE_ID='5';
INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA (ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS) VALUES ('MARKETING_PURCHASER', 'MARKETING_PURCHASER', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA (ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS) VALUES ('PROJECTS_PURCHASER', 'PROJECTS_PURCHASER', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA (ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS) VALUES ('SCM_APPROVER_L1', 'SCM_APPROVER_L1', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA (ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS) VALUES ('SCM_APPROVER_L2', 'SCM_APPROVER_L2', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA (ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS) VALUES ('MARKETING_APPROVER_L1', 'MARKETING_APPROVER_L1', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA (ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS) VALUES ('MARKETING_APPROVER_L2', 'MARKETING_APPROVER_L2', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA (ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS) VALUES ('PROJECTS_APPROVER_L1', 'PROJECTS_APPROVER_L1', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA (ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS) VALUES ('PROJECTS_APPROVER_L2', 'PROJECTS_APPROVER_L2', 'ACTIVE');



INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME="SCM_APPROVER_L1"),ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,current_timestamp()
from KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING where ROLE_ID = 5;

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME="SCM_APPROVER_L2"),ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,current_timestamp()
from KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING where ROLE_ID = 5;

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME="MARKETING_PURCHASER"),ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,current_timestamp()
from KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING where ROLE_ID = 5;

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME="MARKETING_APPROVER_L1"),ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,current_timestamp()
from KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING where ROLE_ID = 5;

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME="MARKETING_APPROVER_L2"),ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,current_timestamp()
from KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING where ROLE_ID = 5;

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME="PROJECTS_PURCHASER"),ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,current_timestamp()
from KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING where ROLE_ID = 5;

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME="PROJECTS_APPROVER_L1"),ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,current_timestamp()
from KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING where ROLE_ID = 5;

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME="PROJECTS_APPROVER_L2"),ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,current_timestamp()
from KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING where ROLE_ID = 5;




INSERT INTO KETTLE_SCM_DEV.PURCHASE_PROFILE(ROLE_ID,ROLE_NAME,SUB_CATEGORY)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME="SCM_PURCHASER"),"SCM_PURCHASER",SUB_CATEGORY_ID
FROM KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION;

INSERT INTO KETTLE_SCM_DEV.PURCHASE_PROFILE(ROLE_ID,ROLE_NAME,SUB_CATEGORY)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME="SCM_APPROVER_L1"),"SCM_APPROVER_L1",SUB_CATEGORY_ID
FROM KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION;


INSERT INTO KETTLE_SCM_DEV.PURCHASE_PROFILE(ROLE_ID,ROLE_NAME,SUB_CATEGORY)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME="SCM_APPROVER_L2"),"SCM_APPROVER_L2",SUB_CATEGORY_ID
FROM KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION;


INSERT INTO KETTLE_SCM_DEV.PURCHASE_PROFILE(ROLE_ID,ROLE_NAME,SUB_CATEGORY)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME="MARKETING_PURCHASER"),"MARKETING_PURCHASER",SUB_CATEGORY_ID
FROM KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION WHERE SUB_CATEGORY_CODE="MARKETING";

INSERT INTO KETTLE_SCM_DEV.PURCHASE_PROFILE(ROLE_ID,ROLE_NAME,SUB_CATEGORY)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME="MARKETING_APPROVER_L1"),"MARKETING_APPROVER_L1",SUB_CATEGORY_ID
FROM KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION WHERE SUB_CATEGORY_CODE="MARKETING";

INSERT INTO KETTLE_SCM_DEV.PURCHASE_PROFILE(ROLE_ID,ROLE_NAME,SUB_CATEGORY)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME="MARKETING_APPROVER_L2"),"MARKETING_APPROVER_L2",SUB_CATEGORY_ID
FROM KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION WHERE SUB_CATEGORY_CODE="MARKETING";

INSERT INTO KETTLE_SCM_DEV.PURCHASE_PROFILE(ROLE_ID,ROLE_NAME,SUB_CATEGORY)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME="PROJECTS_PURCHASER"),"PROJECTS_PURCHASER",SUB_CATEGORY_ID
FROM KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION WHERE LINKED_CATEGORY_ID = 3;

INSERT INTO KETTLE_SCM_DEV.PURCHASE_PROFILE(ROLE_ID,ROLE_NAME,SUB_CATEGORY)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME="PROJECTS_APPROVER_L1"),"PROJECTS_APPROVER_L1",SUB_CATEGORY_ID
FROM KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION WHERE LINKED_CATEGORY_ID = 3;

INSERT INTO KETTLE_SCM_DEV.PURCHASE_PROFILE(ROLE_ID,ROLE_NAME,SUB_CATEGORY)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME="PROJECTS_APPROVER_L2"),"PROJECTS_APPROVER_L2",SUB_CATEGORY_ID
FROM KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION WHERE LINKED_CATEGORY_ID = 3;

INSERT INTO KETTLE_SCM_DEV.PURCHASE_PROFILE(ROLE_ID,ROLE_NAME,SUB_CATEGORY)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME="PROJECTS_PURCHASER"),"PROJECTS_PURCHASER",SUB_CATEGORY_ID
FROM KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION WHERE SUB_CATEGORY_CODE="EQUIPMENT";

INSERT INTO KETTLE_SCM_DEV.PURCHASE_PROFILE(ROLE_ID,ROLE_NAME,SUB_CATEGORY)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME="PROJECTS_APPROVER_L1"),"PROJECTS_APPROVER_L1",SUB_CATEGORY_ID
FROM KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION WHERE SUB_CATEGORY_CODE="EQUIPMENT";

INSERT INTO KETTLE_SCM_DEV.PURCHASE_PROFILE(ROLE_ID,ROLE_NAME,SUB_CATEGORY)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME="PROJECTS_APPROVER_L2"),"PROJECTS_APPROVER_L2",SUB_CATEGORY_ID
FROM KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION WHERE SUB_CATEGORY_CODE="EQUIPMENT";


alter table KETTLE_SCM_DEV.UNIT_DETAIL add column UNIT_REGION VARCHAR(30);
UPDATE KETTLE_SCM_DEV.UNIT_DETAIL u1
INNER JOIN KETTLE_MASTER_DEV.UNIT_DETAIL u2 on u1.UNIT_ID = u2.UNIT_ID
set u1.UNIT_REGION = u2.UNIT_REGION;


INSERT INTO KETTLE_SCM.PACKAGING_DEFINITION (PACKAGING_TYPE, PACKAGING_CODE, PACKAGING_NAME, PACKAGING_STATUS, CONVERSION_RATIO, UNIT_OF_MEASURE)
 VALUES ('LOOSE', 'M', 'METER', 'ACTIVE', '1.000000', 'M');
INSERT INTO KETTLE_SCM.PACKAGING_DEFINITION (PACKAGING_TYPE, PACKAGING_CODE, PACKAGING_NAME, PACKAGING_STATUS, CONVERSION_RATIO, UNIT_OF_MEASURE)
 VALUES ('LOOSE', 'SQ_FT', 'SQUARE FEET', 'ACTIVE', '1.000000', 'SQ_FT');
